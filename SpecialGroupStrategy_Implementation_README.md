# SpecialGroupStrategy 增删改查接口实现

## 概述

本次实现为 `SpecialGroupStrategy` 实体类创建了完整的增删改查接口，仿照现有的 `PriorityPushRule` 相关接口的设计模式。

## 实现的文件列表

### 1. API 层
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/api/SpecialGroupStrategyApi.java`
  - 定义了特殊人群策略的API接口

### 2. 请求/响应类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/CreateRqt.java`
  - 创建特殊人群策略请求类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/UpdateRqt.java`
  - 更新特殊人群策略请求类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/DetailRqt.java`
  - 查询策略详情请求类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/ListRqt.java`
  - 分页查询策略列表请求类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/EnableRqt.java`
  - 启用/禁用策略请求类
- `master-order-push-service-api/src/main/java/com/wanshifu/master/order/push/domain/rqt/specialGroupStrategy/DeleteRqt.java`
  - 删除策略请求类

### 3. 服务层
- `master-order-push-service-web/src/main/java/com/wanshifu/master/order/push/service/SpecialGroupStrategyService.java`
  - 特殊人群策略服务接口
- `master-order-push-service-web/src/main/java/com/wanshifu/master/order/push/service/impl/SpecialGroupStrategyServiceImpl.java`
  - 特殊人群策略服务实现类

### 4. 数据访问层
- `master-order-push-service-web/src/main/java/com/wanshifu/master/order/push/repository/SpecialGroupStrategyRepository.java`
  - 特殊人群策略数据访问层
- `master-order-push-service-web/src/main/java/com/wanshifu/master/order/push/mapper/SpecialGroupStrategyMapper.java`
  - MyBatis Mapper接口
- `master-order-push-service-web/src/main/resources/mappers/SpecialGroupStrategyMapper.xml`
  - MyBatis XML映射文件

### 5. 控制器层
- `master-order-push-service-web/src/main/java/com/wanshifu/controller/SpecialGroupStrategyController.java`
  - 特殊人群策略控制器

### 6. 测试类
- `master-order-push-service-web/src/test/java/com/wanshifu/master/order/push/service/impl/SpecialGroupStrategyServiceImplTest.java`
  - 单元测试类

## 功能特性

### 1. 创建策略 (POST /specialGroupStrategy/create)
- 支持创建新的特殊人群策略
- 包含策略名称重复性校验
- 支持飞书通知功能

### 2. 更新策略 (POST /specialGroupStrategy/update)
- 支持更新现有策略的所有字段
- 包含策略名称重复性校验
- 支持飞书通知功能

### 3. 查询策略详情 (POST /specialGroupStrategy/detail)
- 根据策略ID查询详细信息

### 4. 分页查询策略列表 (POST /specialGroupStrategy/list)
- 支持多条件查询：策略名称、城市ID、服务ID、创建时间范围、策略状态
- 支持分页功能
- 按更新时间倒序排列

### 5. 启用/禁用策略 (POST /specialGroupStrategy/enable)
- 支持启用或禁用策略
- 支持飞书通知功能

### 6. 删除策略 (POST /specialGroupStrategy/delete)
- 软删除功能，不物理删除数据
- 支持飞书通知功能

## 技术特点

1. **参数验证**: 使用 JSR-303 注解进行参数验证
2. **分页支持**: 继承 Pager 类，支持标准分页功能
3. **软删除**: 使用 is_delete 字段标记删除状态
4. **审计功能**: 记录创建人、更新人、创建时间、更新时间
5. **飞书通知**: 集成现有的飞书通知功能
6. **单元测试**: 提供完整的单元测试覆盖

## API 接口说明

### 请求路径前缀
```
/specialGroupStrategy
```

### 接口列表
| 方法 | 路径 | 功能 | 请求体 |
|------|------|------|--------|
| POST | /create | 创建策略 | CreateRqt |
| POST | /update | 更新策略 | UpdateRqt |
| POST | /detail | 查询详情 | DetailRqt |
| POST | /list | 分页查询 | ListRqt |
| POST | /enable | 启用/禁用 | EnableRqt |
| POST | /delete | 删除策略 | DeleteRqt |

## 数据库字段映射

实现完全基于现有的 `special_group_strategy` 表结构，包含以下主要字段：
- strategy_id: 策略ID（主键）
- strategy_name: 策略名称
- strategy_desc: 策略描述
- serve_ids: 服务ID集合
- region_level: 区域级别
- city_ids: 城市ID集合
- push_groups: 调度人群
- push_groups_expression: 调度人群表达式
- serve_model: 下单模式
- delay_minutes: 延迟推送时间
- filter_groups: 过滤人群
- filter_groups_expression: 过滤人群表达式
- strategy_status: 策略状态
- is_delete: 删除标记
- create_time: 创建时间
- create_account_id: 创建人ID
- update_time: 更新时间
- update_account_id: 更新人ID

## 使用示例

### 创建策略
```json
{
  "strategyName": "特殊人群策略1",
  "strategyDesc": "针对特定人群的推送策略",
  "serveIds": "1,2,3",
  "regionLevel": "city",
  "cityIds": "1,2,3",
  "pushGroups": "group1,group2",
  "pushGroupsExpression": "expression",
  "serveModel": 1,
  "delayMinutes": 30,
  "filterGroups": "filter1,filter2",
  "filterGroupsExpression": "filter_expression",
  "createAccountId": 1001
}
```

### 查询列表
```json
{
  "strategyName": "特殊",
  "cityId": 1,
  "strategyStatus": 1,
  "pageNum": 1,
  "pageSize": 20
}
```

## 注意事项

1. 所有接口都使用 POST 方法，保持与现有接口的一致性
2. 请求体使用 JSON 格式
3. 分页查询默认按更新时间倒序排列
4. 删除操作为软删除，不会物理删除数据
5. 策略名称在系统中必须唯一
6. 所有修改操作都会记录操作人信息
