-- SpecialGroupStrategy 表创建语句
-- 特殊人群策略表

CREATE TABLE `special_group_strategy` (
  `strategy_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '策略ID，主键',
  `strategy_name` varchar(100) NOT NULL COMMENT '策略名称',
  `strategy_desc` varchar(500) DEFAULT NULL COMMENT '策略描述',
  `serve_ids` text COMMENT '服务ID集合，逗号分隔',
  `region_level` varchar(50) DEFAULT NULL COMMENT '区域级别',
  `city_ids` text COMMENT '城市ID集合，逗号分隔',
  `push_groups` varchar(500) DEFAULT NULL  COMMENT '调度人群配置',
  `serve_models` varchar(100) DEFAULT NULL COMMENT '下单模式，多个用逗号分割，2:报价招标，4:一口价，5:预付款',
  `delay_minutes` int(11) DEFAULT NULL COMMENT '延迟推送下一优先级的时间（分钟）',
  `filter_groups` varchar(500) DEFAULT NULL  COMMENT '过滤人群配置',
  `strategy_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '策略状态：0-禁用，1-启用',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_account_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_account_id` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`strategy_id`),
  KEY `idx_strategy_name` (`strategy_name`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_create_account_id` (`create_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊人群策略表';

-- 创建复合索引以优化查询性能
CREATE INDEX `idx_city_serve` ON `special_group_strategy` (`city_ids`(100), `serve_ids`(100));

-- 插入示例数据（可选）
INSERT INTO `special_group_strategy` (
  `strategy_name`, 
  `strategy_desc`, 
  `serve_ids`, 
  `region_level`, 
  `city_ids`, 
  `push_groups`,
  `serve_models`,
  `delay_minutes`, 
  `filter_groups`,
  `strategy_status`, 
  `create_account_id`
) VALUES (
  '示例特殊人群策略', 
  '这是一个示例的特殊人群策略配置', 
  '1,2,3', 
  'city', 
  '1,2,3', 
  '{"groups": ["group1", "group2"]}', 
  'group1 AND group2', 
  1, 
  30, 
  '{"filters": ["filter1", "filter2"]}', 
  'filter1 OR filter2', 
  0, 
  1001
);

