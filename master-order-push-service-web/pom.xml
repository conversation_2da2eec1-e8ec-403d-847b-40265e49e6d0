<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>master-order-push-service</artifactId>
        <version>1.0.46-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>

    <artifactId>master-order-push-service-web</artifactId>
    <version>1.0.46-SNAPSHOT</version>

    <dependencies>

<!--        <dependency>-->
<!--            <groupId>com.lmax</groupId>-->
<!--            <artifactId>disruptor</artifactId>-->
<!--            <version>3.3.6</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.elasticsearch</groupId>-->
<!--            <artifactId>elasticsearch</artifactId>-->
<!--            <version>6.8.23</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>slf4j-log4j12</artifactId>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->




        <!-- es -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.3.2</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-push-service-api</artifactId>
            <version>1.0.44</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-information-service-api</artifactId>
            <version>1.1.18</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-training-service-api</artifactId>
            <version>1.0.22</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-offline-service-api</artifactId>
            <version>1.0.6</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-lang</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-push-service-api</artifactId>
            <version>1.0.46-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>wshifu-framework-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.wanshifu</groupId>-->
<!--            <artifactId>wshifu-framework-redis-spring-boot-starter</artifactId>-->
<!--            <version>${wanshfiu-framework-version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-test</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.4.0</version>
        </dependency>


        <!--H2-->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-consume-dispatcher</artifactId>
            <version>1.0.18</version>
        </dependency>



        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-zipkin-spring-boot-starter</artifactId>
            <version>1.0.43</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>enterprise-master-service-api</artifactId>
            <version>1.5.25</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>wshifu-framework-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--地址服务 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-address-service-api</artifactId>
            <version>1.0.37</version>
        </dependency>


        <!--订单配置-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-config-service-api</artifactId>
            <version>2.0.131</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.wanshifu</groupId>-->
<!--            <artifactId>enterprise-inner-api-api</artifactId>-->
<!--            <version>1.4.7</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.wanshifu</groupId>-->
<!--                    <artifactId>wshifu-framework-all-spring-boot-starter</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-service-config-api</artifactId>
            <version>1.0.15</version>
        </dependency>


        <!-- 订单中台-订单报价服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-offer-service-api</artifactId>
            <version>1.93</version>
        </dependency>


        <!--专属师傅招募-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-exclusive-service-api</artifactId>
            <version>1.0.22</version>
        </dependency>


        <!--师傅活动-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-activity-service-api</artifactId>
            <version> 1.0.61</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-recruit-service-api</artifactId>
            <version>1.0.29</version>
        </dependency>





        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-activity-service-api</artifactId>
            <version>1.0.61</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-supply-demand-service-api</artifactId>
            <version>1.0.0</version>
        </dependency>



        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-manage-config-service-api</artifactId>
            <version>1.0.20</version>
        </dependency>


        <!-- 经纬度算距离-->
        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-order-service-api</artifactId>
            <version>1.207-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.8.1</version>
        </dependency>

        <dependency>
        <groupId>com.wanshifu</groupId>
        <artifactId>enterprise-order-service-api</artifactId>
        <version>1.10.73</version>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>fee-center-service-api</artifactId>
            <version>1.0.50</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliyun.openservices</groupId>
                    <artifactId>ons-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>0.10.2.2</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>org.slf4j</groupId>-->
<!--            <artifactId>slf4j-log4j12</artifactId>-->
<!--            <version>1.7.6</version>-->
<!--        </dependency>-->
        


    </dependencies>

    <!--多环境部署配置-->
    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <deploy.env>test</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy.env>prod</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy.env>dev</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy.env>local</deploy.env>
            </properties>
        </profile>
    </profiles>

    <build>
        <filters>
            <filter>src/main/resources/config/application-${deploy.env}.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <finalName>master-order-push-service</finalName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>