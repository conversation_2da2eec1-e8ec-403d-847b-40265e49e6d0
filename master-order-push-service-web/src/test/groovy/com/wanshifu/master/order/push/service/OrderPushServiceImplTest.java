//package com.wanshifu.master.order.push.service;
//
//import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
//import com.google.common.collect.Sets;
//import com.wanshifu.MasterOrderPushServiceApplication;
//import com.wanshifu.master.order.push.domain.rqt.InviteOrderPushRqt;
//import junit.framework.TestCase;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.HashSet;
//import java.util.Set;
//
//
//@RunWith(SpringRunner.class)
//@ContextConfiguration()
//@SpringBootTest(classes = MasterOrderPushServiceApplication.class)
//@MapperScan("com.wanshifu.master.order.push.mapper")
//@EnableApolloConfig
//public class OrderPushServiceImplTest{
//
//    @Resource
//    private OrderPushService orderPushService;
//
//    @Test
//    public void testInviteOrderPush() {
////        final OrderPushServiceImpl service = (OrderPushServiceImpl) (OrderPushServiceImpl)orderPushService;
////        final Set<String> disinterestMaster = service.getDisinterestMaster(113112L, new HashSet<String>() {{
////            add("113112");
////        }});
////
////        System.out.println(disinterestMaster+"===============================================");
//    }
//}