//package com.wanshifu.master.order.push.operator;
//
//import com.ql.util.express.DefaultContext;
//import com.ql.util.express.ExpressRunner;
//import com.wanshifu.master.order.push.function.AllMatch;
//import com.wanshifu.master.order.push.function.AnyMatch;
//import junit.framework.TestCase;
//import org.assertj.core.util.Lists;
//import org.junit.Test;
//
//import java.util.HashSet;
//import java.util.Set;
//
//public class OperatorContainsAnyTest extends TestCase {
//    @Test
//    public void test1() throws Exception {
//        ExpressRunner runner = new ExpressRunner(true,false);
//        runner.setShortCircuit(true);
//        DefaultContext<String, Object> params = new DefaultContext<>();
//        long begain = System.currentTimeMillis();
//        System.out.println("begain = " + begain);
//        //String exp15 = " serve1 containsAny [100060009,100060008,100060007,100060006,100060005,100060004,100060003,100060002,100060001,100050004,100050003,100050002,100050001,100050000,100040008,100040007,100040006,100040005,100040004,100040003,100040002,100040001,100040000,100022211,100022112,100022111,100022102,100022101,100022092,100022091,100022087,100022086,100022085,100022084,100022083,100022082,100022081,100022080,100022029,100011035,100011033,100011031,100011029,100011027,100011025,100011023,100011021,100011019,100011017,100011015,100011013,100011011,100011009,100011007,100011005,100011003,100011001,100010151,100010150,100010129,100010128,100010127,100010126,100010125,100010124,100010123,100010122,100010121,100010120,100010108,100010107,100010106,100010105,100010104,100010103,100010102,100010101,100010100,80020201,70031310,60031310,45503077,45502874,45501762,45501592,45501452,45501189,45501137,45500987,45500802,45500553,45500318,45500248,40031310,34003112,34003111,34003110,33003112,33003111,33003110,30031310,20031310,18003616,18003615,18003614,18003613,18003612,18003611,18003122,18003121,18003113,18003112,18003111,18003108,18003107,18003106,18003104,18003103,18003101,16003114,16003108,16003101,15003616,15003615,15003614,15003613,15003612,15003611,15003122,15003121,15003113,15003112,15003111,15003108,15003107,15003106,15003104,15003103,15003101,14901398,14901397,14901396,14901395,14901394,14901393,14901391,14901348,14901347,14901346,14901343,14901342,14901341,14901322,14901321,14003616,14003615,14003614,14003613,14003612,14003611,14003122,14003121,14003113,14003112,14003111,14003108,14003107,14003106,14003104,14003103,14003101,13003616,13003615,13003614,13003613,13003612,13003611,13003122,13003121,13003113,13003112,13003111,13003108,13003107,13003106,13003104,13003103,13003101,10031310,7503111,7463144,7453144,7443144,7433144,7423144,7413144,7403144,7403111,7393144,7383144,7373144,7363144,7353144,7343144,7333144,7323144,7313144,7303144,7303111,7293144,7283144,7273144,7263144,7253144,7243144,7233144,7223144,7203144,7203111,7193144,7183144,7173144,7163144,7153144,7143144,7133144,7123144,7113639,7113144,7103639,7103144,7103132,7103111,7003810,7003710,7003510,7003411,7003410,7003145,7003144,7003143,7003142,7003141,7003139,7003137,7003136,7003135,7003134,7003133,7003131,7003117,7003116,7003115,7003114,7003113,7003112,7003111,7003109,7003107,7003106,7003105,7003104,7003103,7003102,7003101,6503111,6463144,6453144,6443144,6433144,6423144,6413144,6403144,6403111,6393144,6383144,6373144,6363144,6353144,6343144,6333144,6323144,6313144,6303144,6303111,6293144,6283144,6273144,6263144,6253144,6243144,6233144,6223144,6203144,6203111,6193144,6183144,6173144,6163144,6153144,6143144,6133144,6123144,6113639,6113144,6103639,6103144,6103132,6103111,6003810,6003710,6003510,6003411,6003410,6003145,6003144,6003143,6003142,6003141,6003139,6003137,6003136,6003135,6003134,6003133,6003131,6003117,6003116,6003115,6003114,6003113,6003112,6003111,6003109,6003107,6003106,6003105,6003104,6003103,6003102,6003101,5503111,5463144,5453144,5443144,5433144,5423144,5413144,5403144,5403111,5393144,5383144,5373144,5363144,5353144,5343144,5333144,5323144,5313144,5303144,5303111,5293144,5283144,5273144,5263144,5253144,5243144,5233144,5223144,5203144,5203111,5193144,5183144,5173144,5163144,5153144,5143144,5133144,5123144,5113639,5113144,5103639,5103144,5103132,5103111,5003810,5003710,5003510,5003411,5003410,5003145,5003144,5003143,5003142,5003141,5003139,5003137,5003136,5003135,5003134,5003133,5003131,5003117,5003116,5003115,5003114,5003113,5003112,5003111,5003109,5003107,5003106,5003105,5003104,5003103,5003102,5003101,4900398,4900397,4900396,4900395,4900394,4900393,4900392,4900391,4900348,4900347,4900346,4900343,4900342,4900341,4900322,4900321,4801314,4801312,4800315,4800314,4800313,4800312,4800311,4701314,4701312,4700341,4700315,4700314,4700313,4700312,4700311,4601311,4600341,4600316,4600315,4600314,4600313,4600312,4600311,4503111,4501311,4500341,4500316,4500315,4500314,4500313,4500312,4500311,4463144,4453144,4443144,4433144,4423144,4413144,4403144,4403111,4400341,4393144,4383144,4373144,4363144,4353144,4343144,4333144,4323144,4313144,4303144,4303111,4300341,4293144,4283144,4273144,4263144,4253144,4243144,4233144,4223144,4203144,4203111,4200341,4193144,4183144,4173144,4163144,4153144,4143144,4133144,4123144,4113639,4113144,4103639,4103144,4103132,4103111,4100341,4003810,4003710,4003510,4003411,4003410,4003145,4003144,4003143,4003142,4003141,4003139,4003137,4003136,4003135,4003134,4003133,4003131,4003117,4003116,4003115,4003114,4003113,4003112,4003111,4003109,4003107,4003106,4003105,4003104,4003103,4003102,4003101,3800375,3800374,3800373,3800372,3800371,3800365,3800364,3800363,3800362,3800361,3800359,3800358,3800357,3800356,3800355,3800354,3800353,3800352,3800351,3800344,3800343,3800342,3800341,3800337,3800336,3800335,3800334,3800333,3800332,3800331,3800327,3800326,3800325,3800324,3800323,3800322,3800321,3800315,3800314,3800313,3800312,3800311,3700375,3700374,3700373,3700372,3700371,3700365,3700364,3700363,3700362,3700361,3700359,3700358,3700357,3700356,3700355,3700354,3700353,3700352,3700351,3700344,3700343,3700342,3700341,3700337,3700336,3700335,3700334,3700333,3700332,3700331,3700327,3700326,3700325,3700324,3700323,3700322,3700321,3700315,3700314,3700313,3700312,3700311,3600375,3600374,3600373,3600372,3600371,3600365,3600364,3600363,3600362,3600361,3600359,3600358,3600357,3600356,3600355,3600354,3600353,3600352,3600351,3600344,3600343,3600342,3600341,3600337,3600336,3600335,3600334,3600333,3600332,3600331,3600327,3600326,3600325,3600324,3600323,3600322,3600321,3600315,3600314,3600313,3600312,3600311,3503111,3500375,3500374,3500373,3500372,3500371,3500365,3500364,3500363,3500362,3500361,3500359,3500358,3500357,3500356,3500355,3500354,3500353,3500352,3500351,3500344,3500343,3500342,3500341,3500337,3500336,3500335,3500334,3500333,3500332,3500331,3500327,3500326,3500325,3500324,3500323,3500322,3500321,3500315,3500314,3500313,3500312,3500311,3463144,3453144,3443144,3433144,3423144,3413144,3403144,3403111,3400322,3400321,3400319,3400318,3400317,3400316,3400315,3400313,3400311,3393144,3383144,3373144,3363144,3353144,3343144,3333144,3323144,3313144,3303144,3303111,3300322,3300321,3300319,3300318,3300317,3300316,3300315,3300313,3300311,3293144,3283144,3273144,3263144,3253144,3243144,3233144,3223144,3203144,3203111,3193144,3183144,3173144,3163144,3153144,3143144,3133144,3123144,3113639,3113144,3103639,3103144,3103132,3103111,3003810,3003710,3003510,3003411,3003410,3003145,3003144,3003143,3003142,3003141,3003139,3003137,3003136,3003135,3003134,3003133,3003131,3003117,3003116,3003115,3003114,3003113,3003112,3003111,3003109,3003107,3003106,3003105,3003104,3003103,3003102,3003101,2800210,2700341,2700210,2600341,2600210,2503111,2500341,2500210,2463144,2453144,2443144,2433144,2423144,2413144,2403144,2403111,2400341,2393144,2383144,2373144,2363144,2353144,2343144,2333144,2323144,2313144,2303144,2303111,2300341,2293144,2283144,2273144,2263144,2253144,2243144,2233144,2223144,2203144,2203111,2200341,2193144,2183144,2173144,2163144,2153144,2143144,2133144,2123144,2113639,2113144,2103639,2103144,2103132,2103111,2100341,2003810,2003710,2003510,2003411,2003410,2003145,2003144,2003143,2003142,2003141,2003139,2003137,2003136,2003135,2003134,2003133,2003131,2003117,2003116,2003115,2003114,2003113,2003112,2003111,2003109,2003107,2003106,2003105,2003104,2003103,2003102,2003101,1800521,1800421,1800400,1800399,1800398,1800397,1800396,1800395,1800394,1800393,1800391,1800386,1800385,1800384,1800383,1800382,1800381,1800376,1800375,1800374,1800371,1800369,1800365,1800363,1800348,1800347,1800346,1800345,1800344,1800343,1800342,1800341,1800337,1800336,1800335,1800334,1800333,1800332,1800331,1800329,1800328,1800327,1800326,1800322,1800321,1800314,1800312,1800311,1800212,1800211,1800210,1601327,1601322,1600521,1600421,1600406,1600405,1600404,1600403,1600402,1600401,1600400,1600399,1600398,1600397,1600396,1600395,1600394,1600393,1600391,1600377,1600375,1600371,1600348,1600347,1600346,1600345,1600344,1600343,1600342,1600341,1600329,1600327,1600322,1600321,1600314,1600312,1600311,1600210,1503111,1501327,1501322,1500521,1500502,1500501,1500500,1500421,1500401,1500400,1500399,1500398,1500397,1500396,1500395,1500394,1500393,1500391,1500386,1500385,1500384,1500383,1500382,1500381,1500376,1500375,1500374,1500371,1500369,1500365,1500363,1500349,1500348,1500347,1500346,1500345,1500344,1500343,1500342,1500341,1500337,1500336,1500335,1500334,1500333,1500332,1500331,1500329,1500328,1500327,1500326,1500322,1500321,1500314,1500312,1500311,1500212,1500211,1500210,1490129,1490124,1490122,1463144,1453144,1443144,1433144,1424006,1424005,1424004,1424003,1424002,1424001,1423801,1423601,1423501,1423403,1423402,1423401,1423208,1423207,1423206,1423205,1423204,1423203,1423202,1423201,1423144,1423106,1423105,1423104,1423103,1423102,1423101,1423006,1423005,1423004,1423003,1423002,1423001,1421803,1421802,1413144,1403144,1403111,1400521,1400421,1400400,1400399,1400398,1400397,1400396,1400395,1400394,1400393,1400391,1400386,1400385,1400384,1400383,1400382,1400381,1400376,1400375,1400374,1400371,1400369,1400365,1400363,1400348,1400347,1400346,1400345,1400344,1400343,1400342,1400341,1400337,1400336,1400335,1400334,1400333,1400332,1400331,1400329,1400328,1400327,1400326,1400322,1400321,1400314,1400312,1400311,1400212,1400211,1400210,1393144,1383144,1373144,1363144,1353144,1343144,1333144,1323144,1313144,1303144,1303111,1300521,1300421,1300400,1300399,1300398,1300397,1300396,1300395,1300394,1300393,1300391,1300386,1300385,1300384,1300383,1300382,1300381,1300376,1300375,1300374,1300371,1300369,1300365,1300363,1300348,1300347,1300346,1300345,1300344,1300343,1300342,1300341,1300337,1300336,1300335,1300334,1300333,1300332,1300331,1300329,1300328,1300327,1300326,1300322,1300321,1300314,1300312,1300311,1300212,1300211,1300210,1293144,1283144,1273144,1263144,1253144,1243144,1233144,1223144,1203144,1203111,1200373,1200372,1200371,1200364,1200363,1193144,1183144,1173144,1163144,1161144,1153144,1143144,1133144,1123144,1113639,1103639,1103144,1103132,1103111,1100384,1100383,1100382,1100381,1100373,1100372,1100371,1100368,1100367,1100366,1100365,1100364,1100363,1100362,1100361,1100346,1100345,1100344,1100343,1100342,1100341,1100314,1100313,1100312,1100311,1003810,1003710,1003510,1003411,1003410,1003145,1003144,1003143,1003142,1003141,1003139,1003137,1003136,1003135,1003134,1003133,1003131,1003117,1003116,1003115,1003114,1003113,1003112,1003111,1003109,1003107,1003106,1003105,1003104,1003103,1003102,1003101,1000638,1000637,1000636,1000635,1000634,1000633,1000632,1000631,1000630,1000629,1000628,1000627,1000626,1000625,1000624,1000623,1000622,1000621,1000620,1000619,1000618,1000617,1000616,1000615,1000614,1000613,1000612,1000611,1000610,1000609,1000608,1000607,1000606,1000605,1000603,1000602,1000472,1000471,1000470,1000469,1000468,1000467,1000465,1000464,1000463,1000462,1000461,1000384,1000383,1000382,1000381,1000373,1000372,1000371,1000368,1000367,1000366,1000365,1000364,1000363,1000362,1000361,1000346,1000345,1000344,1000343,1000342,1000341,1000314,1000313,1000312,1000311,900373,900372,900371,900364,900363,800373,800372,800371,800364,800363,740389,730389,730371,730214,720391,720389,720341,720214,710392,710391,710389,710214,700502,700501,700398,700397,700396,700394,700393,700392,700391,700389,700388,700387,700386,700385,700384,700383,700382,700381,700379,700378,700377,700376,700375,700374,700373,700372,700371,700363,700362,700361,700359,700358,700357,700356,700355,700354,700353,700352,700351,700349,700348,700347,700346,700345,700344,700343,700342,700341,700312,700311,700214,700213,700211,700210,640389,630389,630371,630214,620391,620389,620341,620214,620010,620009,620008,620007,620006,620005,620004,620003,620002,620001,610392,610391,610389,610214,600502,600501,600398,600397,600396,600394,600393,600392,600391,600389,600388,600387,600386,600385,600384,600383,600382,600381,600379,600378,600377,600376,600375,600374,600373,600372,600371,600363,600362,600361,600359,600358,600357,600356,600355,600354,600353,600352,600351,600349,600348,600347,600346,600345,600344,600343,600342,600341,600312,600311,600214,600213,600211,600210,550105,550104,550103,550102,550101,550001,540389,530389,530371,530214,520389,520341,520214,510392,510391,510389,510214,500502,500501,500398,500397,500396,500394,500393,500392,500391,500389,500388,500387,500386,500385,500384,500383,500382,500381,500379,500378,500377,500376,500375,500374,500373,500372,500371,500363,500362,500361,500359,500358,500357,500356,500355,500354,500353,500352,500351,500349,500348,500347,500346,500345,500344,500343,500342,500341,500312,500311,500214,500213,500211,500210,490029,490024,490022,480021,470021,460023,460022,460021,456106,456105,456104,456103,456102,456101,456001,455106,455105,455104,455103,455102,455101,455001,451105,451104,451103,451102,451101,451001,450023,450022,450021,440389,440027,440026,440025,440024,440023,440022,440021,430389,430371,430229,430214,430027,430026,430025,430024,430023,430022,430021,426202,426201,426102,426101,426008,426007,426006,426005,426004,426003,426002,426001,425212,425211,425202,425201,425112,425111,425102,425101,425018,425017,425016,425015,425014,425013,425012,425011,425008,425007,425006,425005,425004,425003,425002,425001,422926,422925,422924,422923,422922,422921,422920,422919,422918,422917,422916,422915,422914,422913,422912,422911,422910,422909,422908,422907,422906,422905,422904,422903,422902,422901,422801,422601,422501,422403,422402,422401,422208,422207,422206,422205,422204,422203,422202,422201,422106,422105,422104,422103,422102,422101,422010,422009,422008,422007,422006,422005,422004,422003,422002,422001,421908,421907,421906,421905,421904,421903,421902,421901,421810,421809,421807,421806,421805,421804,421803,421802,421801,421601,421501,421403,421402,421401,421208,421207,421206,421205,421204,421203,421202,421201,421106,421105,421104,421103,421102,421101,421010,421009,421008,421007,421006,421005,421004,421003,421002,421001,420391,420389,420341,420229,420214,420027,420026,420025,420024,420023,420022,420021,410392,410391,410389,410229,410214,410027,410026,410025,410024,410023,410022,410021,400502,400501,400398,400397,400396,400394,400393,400392,400391,400389,400388,400387,400386,400385,400384,400383,400382,400381,400379,400378,400377,400376,400375,400374,400373,400372,400371,400363,400362,400361,400359,400358,400357,400356,400355,400354,400353,400352,400351,400349,400348,400347,400346,400345,400344,400343,400342,400341,400312,400311,400257,400256,400255,400254,400253,400252,400251,400250,400249,400248,400247,400246,400245,400244,400243,400242,400241,400240,400239,400238,400237,400236,400235,400234,400233,400232,400231,400230,400229,400228,400227,400226,400225,400224,400223,400222,400221,400220,400219,400218,400217,400216,400214,400213,400211,400210,400025,400024,400023,400022,390322,390222,390123,390122,390025,390024,390023,390022,380027,380026,380025,380024,380023,380022,380021,370027,370026,370025,370024,370023,370022,370021,360027,360026,360025,360024,360023,360022,360021,350027,350026,350025,350024,350023,350022,350021,340389,340022,340021,330389,330371,330214,330022,330021,320391,320389,320341,320214,320031,320030,320029,320028,320027,320026,320025,320024,320023,320022,320021,310392,310391,310389,310214,310031,310030,310029,310028,310027,310026,310025,310024,310023,310022,310021,300502,300501,300398,300397,300396,300394,300393,300392,300391,300389,300388,300387,300386,300385,300384,300383,300382,300381,300379,300378,300377,300376,300375,300374,300373,300372,300371,300363,300362,300361,300359,300358,300357,300356]";
//        String exp16 = " serveL1 containsAny [100060009,100060008] and serveL2 containsAny [100060009,100060008] and serveL3 containsAny [100060009,100060008]";
////        params.put("master_group", Lists.newArrayList(100));
////        params.put("master_level",1);
//        // params.put("serve",Lists.newArrayList(1,2,3,4));
//
////        String exp17 = "appoint_type in [3]  and ( serveLv1 containsAny [11,12] or serveLv2 containsAny [21,22] )";
////        String exp18 = "appoint_type in [3]  and ( serveLv1 notContainsAny [11,12] and serveLv2 notContainsAny [21,22] )";
////        String exp19 = " if(!(test == 1)) return 4; if(a == 12) then {return 1;}   else if(a == 13)then{return 2;}    else {return 3;}";
//        String exp20= "( a==1 ? c==1 : true ) and (b == 2 )";
//        String exp21= "anyMatch(bus_and_serve_lv2_last_30d_refund_all_ratio,'<=',30)";
////          String exp21= "anyMatch(bus_and_serve_lv2_last_30d_refund_all_ratio,'<=',30)";
//        String exp22= "lv2_serve_ids containsAny [40021,40024]";
//
////          params.put("appoint_type",3);
////          params.put("serveLv1",Lists.newArrayList(11));
////          params.put("serveLv2",Lists.newArrayList(55));
////          params.put("test",2);
////          params.put("a",1);
////          params.put("b",2);
////          params.put("c",2);
////          params.put("bus_and_serve_lv2_last_30d_refund_all_ratio",Lists.newArrayList(50,40));
//        params.put("lv2_serve_ids", Lists.newArrayList(40021,40024));
//
//        runner.addOperator("contain", new OperatorContain());
//        runner.addOperator("notContain", new OperatorNotContain());
//        runner.addOperator("containsAny", new OperatorContainsAny());
//        runner.addOperator("notContainsAny", new OperatorNotContainsAny());
//
//        runner.addFunction("anyMatch", new AnyMatch());
//        runner.addFunction("allMatch", new AllMatch());
//
//        Object execute = runner.execute(exp22, params, null, true, false);
//        long end = System.currentTimeMillis();
//        System.out.println(execute);
//        // System.out.println("end = " + end);
//        //System.out.println(end-begain);
//    }
//
//
//}