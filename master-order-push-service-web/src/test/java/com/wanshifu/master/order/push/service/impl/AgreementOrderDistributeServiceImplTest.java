package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * AgreementOrderDistributeServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>06/24/2025</pre>
 */
@SpringBootTest()
@RunWith(SpringRunner.class)
public class AgreementOrderDistributeServiceImplTest {

    @Resource
    private AgreementOrderDistributeServiceImpl agreementOrderDistributeServiceImpl;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: orderDistribute(OrderPushedResultNotice orderPushedResultNotice)
     */
    @Test
    public void testOrderDistribute() throws Exception {
        String paramString = "{\"businessLineId\":1,\"firstPush\":1,\"globalOrderTraceId\":***********,\"hasPrice\":false,\"masterInfoList\":[{\"accordingTechnologyPushFlag\":1,\"autoPrice\":22,\"masterId\":**********,\"recruitId\":\"840\",\"tagName\":\"new_contract\"}],\"masterSourceType\":\"tob\",\"orderBaseComposite\":{\"orderAddItemServiceInfoList\":[],\"orderBase\":{\"accountId\":**********,\"accountType\":\"user\",\"bindingTechnologyIds\":\"1026|1033|1054|1057|1065|1086|1090|1099|1120|1122|1125|1127|1131|1133|1134|1141|2100|2101|2102|2107|2199\",\"businessLineId\":1,\"categoryId\":4,\"createTime\":*************,\"exclusiveFlag\":0,\"fourthDivisionId\":*********,\"globalOrderTraceId\":***********,\"isTmallIkeaFlag\":0,\"orderCreateTime\":*************,\"orderFrom\":\"site\",\"orderId\":***********,\"orderLabel\":\"\",\"orderModifyTime\":*************,\"orderNo\":\"P61712730906\",\"orderOperationVersion\":0,\"orderServeVersion\":1,\"orderStatus\":\"trading\",\"pushNumber\":0,\"recruitTagName\":\"\",\"regionId\":0,\"riskControlStatus\":\"check_normal\",\"serveIds\":\"1500321\",\"serveLevel1Ids\":\"15\",\"serveType\":4,\"serveTypeId\":15,\"serviceIds\":\"1500321\",\"subAccountId\":0,\"thirdDivisionId\":440306,\"updateTime\":*************},\"orderExtraData\":{\"buyerAddress\":\"泰华梧桐岛\",\"buyerAddressLatitude\":22.6056100,\"buyerAddressLongitude\":113.8383030,\"buyerGender\":1,\"buyerHouseNumber\":\"\",\"buyerName\":\"郑某某\",\"buyerNote\":\"轻拿轻放\",\"buyerPhone\":\"***********\",\"buyerVoiceData\":\"\",\"clearanceNo\":\"\",\"clearanceType\":\"\",\"contactName\":\"de\",\"contactPhone\":\"***********\",\"createTime\":*************,\"demolishGoods\":\"\",\"demolishType\":\"nouse\",\"destinationAddress\":\"\",\"destinationContactName\":\"\",\"destinationContactPhone\":\"\",\"destinationDivisionId\":1,\"destinationFloorNum\":0,\"destinationGender\":1,\"destinationHasLift\":0,\"destinationHouseNumber\":\"\",\"destinationLatitude\":0E-7,\"destinationLongitude\":0E-7,\"emergencyContactName\":\"\",\"emergencyContactPhone\":\"\",\"emergencyOrderFlag\":0,\"expectDoorInDate\":\"\",\"expectOfferMaxPrice\":21.00,\"expectOfferMinPrice\":21.00,\"floorNum\":0,\"generalField\":\"\",\"hasLift\":0,\"isExemptInformationServeFee\":0,\"isParts\":0,\"isSendBack\":0,\"isSendBackOld\":0,\"jsonGeneralField\":\"\",\"lessTransportCapacityFlag\":0,\"mapType\":\"\",\"measureWay\":\"\",\"needConfirmFixOnWall\":0,\"needConstructConfirm\":1,\"onTimeOrderFlag\":0,\"orderExtraId\":5357362,\"orderId\":***********,\"orderPushExcludeMasterIds\":\"\",\"personalizedReceiptFlag\":0,\"platformServiceFeeToAccount\":0.00,\"projectManagerName\":\"\",\"sendBackOldGoods\":\"\",\"snType\":0,\"timerFlag\":0,\"updateTime\":*************,\"warrantyType\":0},\"orderFileRelaList\":[],\"orderGrab\":{\"accountId\":**********,\"accountType\":\"user\",\"appointType\":2,\"areaId\":0,\"cityId\":0,\"confirmServeStatus\":0,\"createTime\":*************,\"endDate\":*************,\"fourthDivisionId\":*********,\"grabId\":********,\"hireMasterId\":0,\"isDelete\":0,\"offerNumber\":0,\"orderId\":***********,\"orderPayStatus\":0,\"processVersion\":0,\"requireCondition\":0,\"secondDivisionId\":440300,\"thirdDivisionId\":440306,\"updateTime\":*************},\"orderInitFee\":{\"createTime\":*************,\"definiteServeFee\":0.00,\"initAdvanceFee\":0.00,\"initFeeId\":5556475,\"initLogisticsFee\":0.00,\"orderId\":***********,\"updateTime\":*************},\"orderLogisticsInfo\":{\"arriveCustomerHomeStatus\":1,\"arriveTime\":*************,\"createTime\":*************,\"isArrived\":0,\"logisticsCompanyName\":\"\",\"logisticsId\":5211911,\"logisticsNo\":\"\",\"logisticsToCustomerDistance\":0,\"operateArrivedAccountType\":\"user\",\"operatePickupAddressAccountType\":\"\",\"orderId\":***********,\"packNumber\":0,\"pickupAddress\":\"\",\"pickupPhone\":\"\",\"updateTime\":*************},\"orderPartsList\":[],\"orderRateAward\":{\"applyRefundStatus\":0,\"createTime\":*************,\"customerAwardFee\":20.00,\"maxUploadTime\":10,\"orderId\":***********,\"orderRateAwardId\":3595389,\"rateAwardFee\":10.00,\"rateAwardRemark\":\"\",\"rateType\":\"image\",\"updateTime\":*************}},\"orderId\":***********,\"pushDivisionLevel\":4,\"pushMasterIds\":[**********],\"pushMode\":\"agreement_master\",\"pushScenarioType\":\"SMART_PUSH\",\"recruitTagName\":\"\",\"teamMasterOrderPush\":0}";
        OrderPushedResultNotice orderPushedResultNotice = JSONUtil.toBean(paramString, OrderPushedResultNotice.class);
        agreementOrderDistributeServiceImpl.orderDistribute(orderPushedResultNotice);
//TODO: Test goes here... 
    }

    /**
     * Method: afterPropertiesSet()
     */
    @Test
    public void testAfterPropertiesSet() throws Exception {
//TODO: Test goes here... 
    }


} 
