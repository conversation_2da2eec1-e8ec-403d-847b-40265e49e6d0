package com.wanshifu.master.order.push.service.impl;

import cn.hutool.json.JSONUtil;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * AgreementOrderDistributeResultNoticeServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>06/24/2025</pre>
 */
@SpringBootTest()
@RunWith(SpringRunner.class)
public class AgreementOrderDistributeResultNoticeServiceImplTest {

    @Resource
    private AgreementOrderDistributeResultNoticeServiceImpl agreementOrderDistributeResultNoticeServiceImpl;

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage)
     */
    @Test
    public void testDistributeResultNotice() throws Exception {
        String paramString = "{\"appointType\":4,\"failMasterList\":[6098176290],\"appointDetailType\":400700,\"orderId\":61712734214,\"successMasterList\":[5246634710],\"extraId\":4701,\"optionTime\":1750756549490,\"masterFailVos\":[]}";
        OrderDistributeResultMessage orderDistributeResultMessage = JSONUtil.toBean(paramString, OrderDistributeResultMessage.class);
        agreementOrderDistributeResultNoticeServiceImpl.distributeResultNotice(orderDistributeResultMessage);
//TODO: Test goes here... 
    }

    /**
     * Method: afterPropertiesSet()
     */
    @Test
    public void testAfterPropertiesSet() throws Exception {
//TODO: Test goes here... 
    }


    /**
     * Method: updateAgreementMasterMatch(Long orderId, Long masterId, Long recruitId, Integer isDistributeSucc, String distributeFailReason)
     */
    @Test
    public void testUpdateAgreementMasterMatch() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = AgreementOrderDistributeResultNoticeServiceImpl.getClass().getMethod("updateAgreementMasterMatch", Long.class, Long.class, Long.class, Integer.class, String.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: updateAgreementMasterPush(String today, Long masterId, Long recruitId)
     */
    @Test
    public void testUpdateAgreementMasterPush() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = AgreementOrderDistributeResultNoticeServiceImpl.getClass().getMethod("updateAgreementMasterPush", String.class, Long.class, Long.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: getCompensateDistributeList(String masterSourceType, Integer businessLineId, Long categoryId, Integer appointType, Integer hasPrice, Integer hasCooperationUser)
     */
    @Test
    public void testGetCompensateDistributeList() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = AgreementOrderDistributeResultNoticeServiceImpl.getClass().getMethod("getCompensateDistributeList", String.class, Integer.class, Long.class, Integer.class, Integer.class, Integer.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

    /**
     * Method: getMasterSourceType(Integer businessLineId, Long secondDivisionId)
     */
    @Test
    public void testGetMasterSourceType() throws Exception {
//TODO: Test goes here... 
/* 
try { 
   Method method = AgreementOrderDistributeResultNoticeServiceImpl.getClass().getMethod("getMasterSourceType", Integer.class, Long.class); 
   method.setAccessible(true); 
   method.invoke(<Object>, <Parameters>); 
} catch(NoSuchMethodException e) { 
} catch(IllegalAccessException e) { 
} catch(InvocationTargetException e) { 
} 
*/
    }

} 
