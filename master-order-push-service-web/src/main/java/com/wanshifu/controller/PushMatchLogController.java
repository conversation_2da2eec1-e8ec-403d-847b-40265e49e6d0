package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushMatchLogApi;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.service.PushMatchLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/25 18:20
 */
@RestController
@RequestMapping("/pushMatchLog")
public class PushMatchLogController implements PushMatchLogApi {

    @Resource
    private PushMatchLogService pushMatchLogService;


    @PostMapping("/list")
    @Override
    @Deprecated
    public SimplePageInfo<PushMatchLogDto> pushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.pushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 平台协议派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("userAgreementList")
    @Override
    public SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.userAgreementPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 总包直接指派日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("enterpriseAppointList")
    @Override
    public SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.enterpriseAppointPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 合作经营派单日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @Override
    @PostMapping("cooperationBusinessList")
    public SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.cooperationBusinessPushMatchLogList(pushMatchLogRqt);
    }

    /**
     * 样板城市派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("newModelCityList")
    @Override
    public SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.newModelCityPushMatchLogList(pushMatchLogRqt);
    }


    /**
     * 平台协议派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("fullTimeMatchLogList")
    @Override
    public SimplePageInfo<OrderFullTimeMasterMatchLogDto> fullTimeMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt) {
        return pushMatchLogService.orderFullMasterMatchLogList(pushMatchLogRqt);
    }

}
