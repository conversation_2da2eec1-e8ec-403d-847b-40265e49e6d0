package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PermissionSetApi;
import com.wanshifu.master.order.push.domain.po.Permission;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;
import com.wanshifu.master.order.push.service.PermissionSetService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("permissionSet")
@Deprecated
public class PermissionSetController implements PermissionSetApi {

    @Resource
    private PermissionSetService permissionSetService;

    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddPermissionSetRqt rqt) {
        return permissionSetService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdatePermissionSetRqt rqt) {
        return permissionSetService.update(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<PermissionSet> list(@Valid @RequestBody GetPermissionSetListRqt rqt) {
        return permissionSetService.list(rqt);
    }


    @Override
    @PostMapping(value = "detail")
    public PermissionSet detail(@Valid @RequestBody GetPermissionSetDetailRqt rqt) {
        return permissionSetService.detail(rqt);
    }


    @Override
    @PostMapping(value = "menuList")
    public List<Permission> menuList(@Valid @RequestBody GetMenuListRqt rqt) {
        return permissionSetService.menuList(rqt);
    }


    @Override
    @PostMapping(value = "permissionList")
    public List<Permission> permissionList(@Valid @RequestBody GetPermissionListRqt rqt) {
        return permissionSetService.permissionList(rqt);
    }

    @Override
    @PostMapping(value = "allPermissionList")
    public List<Permission> allPermissionList(){
        return permissionSetService.allPermissionList();
    }


    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeletePermissionSetRqt rqt) {
        return permissionSetService.delete(rqt);
    }



}
