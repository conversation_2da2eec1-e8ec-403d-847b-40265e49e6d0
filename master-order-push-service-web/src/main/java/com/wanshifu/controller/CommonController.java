package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.CommonApi;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import com.wanshifu.master.order.push.domain.rqt.common.*;
import com.wanshifu.master.order.push.service.CommonService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/common")
@Validated
public class CommonController implements CommonApi {

    @Resource
    private CommonService commonService;


    /**
     * 查询师傅指标
     *
     * @return
     */
    @Override
    @PostMapping("/masterQuota")
    public SimplePageInfo<MasterQuota> masterQuota(@RequestBody @Valid MasterQuotaRqt rqt) {
        return commonService.masterQuota(rqt);
    }


    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @Override
    @PostMapping("/masterItemList")
    public List<ScoreItem> masterItemList(@RequestBody @Valid MasterItemRqt rqt) {
        return commonService.masterItemList(rqt);
    }


    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @Override
    @PostMapping("/getMasterQuotaValue")
    public List<MasterQuotaValue> getMasterQuotaValue(@RequestBody @Valid GetMasterQuotaValueRqt rqt) {
        return commonService.getMasterQuotaValue(rqt);
    }


    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @Override
    @PostMapping("/getScoreItemValue")
    public List<ScoreItemValue> getScoreItemValue(@RequestBody @Valid GetScoreItemValueRqt rqt) {
        return commonService.getScoreItemValue(rqt);
    }


    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @Override
    @PostMapping("/getScoreItemByCodes")
    public List<ScoreItem> getScoreItemByCodes(@RequestBody @Valid GetScoreItemByCodesRqt rqt) {
        return commonService.getScoreItemByCodes(rqt);
    }


    @Override
    @PostMapping("/getMasterQuotaByCodes")
    public List<MasterQuota> getMasterQuotaByCodes(@RequestBody @Valid GetMasterQuotaByCodesRqt rqt) {
        return commonService.getMasterQuotaByCodes(rqt);
    }


}
