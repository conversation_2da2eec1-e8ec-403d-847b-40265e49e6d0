package com.wanshifu.controller;


import com.wanshifu.master.order.push.domain.po.OrderPushMaster;
import com.wanshifu.master.order.push.domain.rqt.orderPushMaster.GetOrderPushMasterRqt;
import com.wanshifu.master.order.push.service.OrderPushMasterService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 推单明细Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/order/master")
public class OrderPushMasterController {

    @Resource
    private OrderPushMasterService orderPushMasterService;


    /**
     * 根据订单id查询推单明细列表
     * @param getOrderPushMasterRqt
     * @return
     */
    @PostMapping("getOrderPushMasterList")
    public List<OrderPushMaster> getOrderPushMasterList(@Valid @RequestBody GetOrderPushMasterRqt getOrderPushMasterRqt) {
        return orderPushMasterService.getOrderPushMasterList(getOrderPushMasterRqt.getOrderId());
    }


    /**
     * 清除过期的推单明细数据
     * @return
     */
    @PostMapping("deleteExpiredOrderPushMasterList")
    public Integer deleteExpiredOrderPushMasterList() {
        return orderPushMasterService.deleteExpiredOrderPushMasterList();
    }


}
