package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.LongTailStrategyGroupApi;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;
import com.wanshifu.master.order.push.service.LongTailStrategyGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/longTailStrategyGroup")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyGroupController implements LongTailStrategyGroupApi {

    private final LongTailStrategyGroupService longTailStrategyGroupService;
    /**
     * 长尾策略列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<LongTailStrategyGroup> list(@RequestBody @Valid ListRqt rqt) {
        return longTailStrategyGroupService.list(rqt);
    }

    /**
     * 创建长尾策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return longTailStrategyGroupService.create(rqt);
    }

    /**
     * 修改长尾策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return longTailStrategyGroupService.update(rqt);
    }

    /**
     * 启用/禁用策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int updateStatus(@RequestBody @Valid EnableRqt rqt) {
        return longTailStrategyGroupService.updateStatus(rqt);
    }

    /**
     * 删除策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return longTailStrategyGroupService.delete(rqt);
    }

    /**
     * 召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    public LongTailStrategyGroup detail(@RequestBody @Valid DetailRqt rqt) {
        return longTailStrategyGroupService.detail(rqt);
    }
}
