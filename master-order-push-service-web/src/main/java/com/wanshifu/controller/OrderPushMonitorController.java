package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderPushMonitorApi;
import com.wanshifu.master.order.push.domain.po.PushOrderList;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.service.OrderNotPushService;
import com.wanshifu.master.order.push.service.OrderPushMonitorService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述 : 推单监控
 *
 * <AUTHOR> cheng<PERSON>@wshifu.com
 * @date : 2023-07-11 15:31
 */
@RestController
@RequestMapping("/orderPushMonitor")
@Validated
public class OrderPushMonitorController implements OrderPushMonitorApi {

    @Resource
    private OrderNotPushService orderNotPushService;


    /**
     * 未推单明细列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/noPushedOrderList")
    public SimplePageInfo<PushOrderList> noPushedMasterOrderList(@RequestBody @Valid NoPushedMasterOrderListRqt rqt) {
        return orderNotPushService.noPushedMasterOrderList(rqt);
    }




    @Override
    @PostMapping("/filterData")
    public List<GetFilterDataResp> filterData(@Valid @RequestBody GetFilterDataRqt rqt) {
        return orderNotPushService.filterData(rqt);
    }
}