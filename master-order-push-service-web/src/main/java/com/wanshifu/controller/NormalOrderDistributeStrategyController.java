package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.NormalOrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.service.NormalOrderDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("normalOrderDistributeStrategy")
public class NormalOrderDistributeStrategyController implements NormalOrderDistributeStrategyApi {

    @Resource
    private NormalOrderDistributeStrategyService normalOrderDistributeStrategyService;

    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return normalOrderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return normalOrderDistributeStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public NormalOrderDistributeStrategy detail(@Valid @RequestBody DetailRqt rqt) {
        return normalOrderDistributeStrategyService.detail(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
       return normalOrderDistributeStrategyService.enable(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<NormalOrderDistributeStrategy> list(@Valid @RequestBody ListRqt rqt){
        return normalOrderDistributeStrategyService.list(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return normalOrderDistributeStrategyService.delete(rqt);
    }

}
