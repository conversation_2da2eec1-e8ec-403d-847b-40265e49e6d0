package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.LongTailStrategyApi;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;
import com.wanshifu.master.order.push.service.LongTailStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/longTailStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyController implements LongTailStrategyApi {

    private final LongTailStrategyService longTailStrategyService;
    /**
     * 长尾策略列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<LongTailStrategy> list(@RequestBody @Valid ListRqt rqt) {
        return longTailStrategyService.list(rqt);
    }

    /**
     * 创建长尾策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return longTailStrategyService.create(rqt);
    }

    /**
     * 修改长尾策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return longTailStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int updateStatus(@RequestBody @Valid EnableRqt rqt) {
        return longTailStrategyService.updateStatus(rqt);
    }

    /**
     * 删除策略
     *
     * @param rqt
     * @return
     */
    @Override
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return longTailStrategyService.delete(rqt);
    }

    /**
     * 召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    public LongTailStrategy detail(@RequestBody @Valid DetailRqt rqt) {
        return longTailStrategyService.detail(rqt);
    }
}
