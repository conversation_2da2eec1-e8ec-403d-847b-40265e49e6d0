package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.BaseSelectStrategyApi;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;
import com.wanshifu.master.order.push.service.BaseSelectStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  初筛策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/baseSelectStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BaseSelectStrategyController implements BaseSelectStrategyApi {

    private final BaseSelectStrategyService baseSelectStrategyService;


    /**
     * 初筛策略列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<BaseSelectStrategy> list(@RequestBody @Valid ListRqt rqt) {
        return baseSelectStrategyService.list(rqt);
    }

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return baseSelectStrategyService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return baseSelectStrategyService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/detail")
    public BaseSelectStrategy detail(@RequestBody @Valid DetailRqt rqt) {
        return baseSelectStrategyService.detail(rqt);
    }

    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return baseSelectStrategyService.enable(rqt);
    }


    /**
     * 删除初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return baseSelectStrategyService.delete(rqt);
    }


    @Override
    @PostMapping("/selectBySnapshotIdList")
    public List<BaseSelectStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt){
        return baseSelectStrategyService.selectBySnapshotIdList(rqt.getSnapshotIdList());
    }
}