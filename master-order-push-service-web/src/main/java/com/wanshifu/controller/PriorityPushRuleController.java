package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PriorityPushRuleApi;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.*;
import com.wanshifu.master.order.push.service.PriorityPushRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.PriorityOrdered;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/priorityPushRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PriorityPushRuleController implements PriorityPushRuleApi {

    @Resource
    private PriorityPushRuleService priorityPushRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return priorityPushRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return priorityPushRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/detail")
    public PriorityPushRule detail(@RequestBody @Valid DetailRqt rqt) {
        return priorityPushRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<PriorityPushRule> list(@RequestBody @Valid ListRqt rqt) {
        return priorityPushRuleService.list(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt) {
        return priorityPushRuleService.enable(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return priorityPushRuleService.delete(rqt);
    }




}
