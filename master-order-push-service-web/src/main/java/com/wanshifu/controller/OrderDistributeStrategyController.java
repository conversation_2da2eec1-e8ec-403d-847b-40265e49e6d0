package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;
import com.wanshifu.master.order.push.service.OrderDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *  智能接单调度
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderDistributeStrategy")
public class OrderDistributeStrategyController implements OrderDistributeStrategyApi {

    @Resource
    private OrderDistributeStrategyService orderDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderDistributeStrategyRqt rqt) {
        return orderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderDistributeStrategyRqt rqt) {
        return orderDistributeStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableOrderDistributeStrategyRqt rqt) {
        return orderDistributeStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteOrderDistributeStrategyRqt rqt) {
        return orderDistributeStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public GetOrderDistributeStrategyDetailResp detail(@Valid @RequestBody OrderDistributeStrategyDetailRqt rqt) {
        return orderDistributeStrategyService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderDistributeStrategy> list(@Valid @RequestBody GetOrderDistributeStrategyListRqt rqt){
        return orderDistributeStrategyService.list(rqt);
    }


}
