package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderMatchRouteApi;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.service.OrderMatchRouteService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRoute")
public class OrderMatchRouteController implements OrderMatchRouteApi {

    @Resource
    private OrderMatchRouteService orderMatchRouteService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderMatchRouteRqt rqt) {
        return orderMatchRouteService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderMatchRouteRqt rqt) {
        return orderMatchRouteService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderMatchRoute detail(@Valid @RequestBody OrderMatchRouteDetailRqt rqt) {
        return orderMatchRouteService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderMatchRoute> list(@Valid @RequestBody OrderMatchRouteListRqt rqt){
        return orderMatchRouteService.list(rqt);
    }


}
