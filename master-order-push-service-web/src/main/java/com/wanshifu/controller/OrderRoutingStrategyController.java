package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderRoutingStrategyApi;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;
import com.wanshifu.master.order.push.service.OrderRoutingStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderRoutingStrategy")
public class OrderRoutingStrategyController implements OrderRoutingStrategyApi {

    @Resource
    private OrderRoutingStrategyService orderRoutingStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return orderRoutingStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return orderRoutingStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderRoutingStrategy detail(@Valid @RequestBody DetailRqt rqt) {
        return orderRoutingStrategyService.detail(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
       return orderRoutingStrategyService.enable(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderRoutingStrategy> list(@Valid @RequestBody ListRqt rqt){
        return orderRoutingStrategyService.list(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return orderRoutingStrategyService.delete(rqt);
    }

}
