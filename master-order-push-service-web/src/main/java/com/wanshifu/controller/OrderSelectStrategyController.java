package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderSelectStrategyApi;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;
import com.wanshifu.master.order.push.service.OrderSelectStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 15:27
 */
@RestController
@RequestMapping("orderSelectStrategy")
public class OrderSelectStrategyController implements OrderSelectStrategyApi {


    @Resource
    private OrderSelectStrategyService orderSelectStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteOrderSelectStrategyRqt rqt) {
        return orderSelectStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderSelectStrategy detail(@Valid @RequestBody OrderSelectStrategyDetailRqt rqt) {
        return orderSelectStrategyService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderSelectStrategy> list(@Valid @RequestBody GetOrderSelectStrategyListRqt rqt){
        return orderSelectStrategyService.list(rqt);
    }


    @Override
    @PostMapping("/quotaList")
    public SimplePageInfo<MasterQuotaResp> quotaList(@Valid @RequestBody MasterQuotaListRqt rqt) {
        return orderSelectStrategyService.masterQuota(rqt);
    }


}
