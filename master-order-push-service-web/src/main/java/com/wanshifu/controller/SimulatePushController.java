package com.wanshifu.controller;


import com.wanshifu.master.order.push.api.SimulatePushApi;
import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import com.wanshifu.master.order.push.domain.resp.simulatePush.GetSimulatePushResp;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.*;
import com.wanshifu.master.order.push.service.SimulatePushService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/simulatePush")
@Validated
public class SimulatePushController implements SimulatePushApi {

    @Resource
    private SimulatePushService simulatePushService;

    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/getStrategyCombinationSimulate")
    public List<StrategyCombinationSimulate> getStrategyCombinationSimulate(@RequestBody @Valid GetStrategyCombinationSimulateRqt rqt) {
        return simulatePushService.getStrategyCombinationSimulate(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/getCombinationLastSimulate")
    public StrategyCombinationSimulate getCombinationLastSimulate(@RequestBody @Valid GetCombinationLastSimulateRqt rqt) {
        return simulatePushService.getCombinationLastSimulate(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/addCombinationSimulate")
    public Long addCombinationSimulate(@RequestBody @Valid AddCombinationSimulateRqt rqt) {
        return simulatePushService.addCombinationSimulate(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/updateOrderNum")
    public Integer updateOrderNum(@RequestBody @Valid UpdateOrderNumRqt rqt) {
        return simulatePushService.updateOrderNum(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/updateSimulateFinish")
    public Integer updateSimulateFinish(@RequestBody @Valid UpdateSimulateFinishRqt rqt) {
        return simulatePushService.updateSimulateFinish(rqt);
    }



    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/getSimulatePush")
    public GetSimulatePushResp getSimulatePush(@RequestBody @Valid GetSimulatePushRqt rqt) {
        return simulatePushService.getSimulatePush(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/addSimulatedOrderNum")
    public Integer addSimulatedOrderNum(@RequestBody @Valid AddSimulateOrderNumRqt rqt) {
        return simulatePushService.addSimulatedOrderNum(rqt);
    }


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/addSimulatePush")
    public Long addSimulatePush(@RequestBody @Valid AddSimulatePushRqt rqt) {
        return simulatePushService.addSimulatePush(rqt);
    }



    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/batchAddSimulatePushDetail")
    public Integer batchAddSimulatePushDetail(@RequestBody @Valid BatchAddSimulatePushDetailRqt rqt) {
        return simulatePushService.batchAddSimulatePushDetail(rqt);
    }

}
