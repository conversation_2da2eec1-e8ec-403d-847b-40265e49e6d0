package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderMatchRouteApi;
import com.wanshifu.master.order.push.api.OrderMatchRoutingApi;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.CreateRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.ListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.UpdateRqt;
import com.wanshifu.master.order.push.service.OrderMatchRouteService;
import com.wanshifu.master.order.push.service.OrderMatchRoutingService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRouting")
public class OrderMatchRoutingController implements OrderMatchRoutingApi {

    @Resource
    private OrderMatchRoutingService orderMatchRoutingService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return orderMatchRoutingService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return orderMatchRoutingService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderMatchRouting detail(@Valid @RequestBody DetailRqt rqt) {
        return orderMatchRoutingService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderMatchRouting> list(@Valid @RequestBody ListRqt rqt){
        return orderMatchRoutingService.list(rqt);
    }


}
