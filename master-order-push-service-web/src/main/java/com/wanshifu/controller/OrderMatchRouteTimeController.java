package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderMatchRouteTimeApi;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.service.OrderMatchRouteTimeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderMatchRouteTime")
public class OrderMatchRouteTimeController implements OrderMatchRouteTimeApi {

    @Resource
    private OrderMatchRouteTimeService orderMatchRouteTimeService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderMatchRouteTimeRqt rqt) {
        return orderMatchRouteTimeService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderMatchRouteTimeRqt rqt) {
        return orderMatchRouteTimeService.update(rqt);
    }






    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderMatchRouteTime detail(@Valid @RequestBody OrderMatchRouteTimeDetailRqt rqt) {
        return orderMatchRouteTimeService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderMatchRouteTime> list(@Valid @RequestBody OrderMatchRouteTimeListRqt rqt){
        return orderMatchRouteTimeService.list(rqt);
    }


}
