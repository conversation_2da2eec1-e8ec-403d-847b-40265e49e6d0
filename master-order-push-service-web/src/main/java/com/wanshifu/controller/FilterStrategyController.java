package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.FilterStrategyApi;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.SnapshotRqt;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;
import com.wanshifu.master.order.push.service.FilterStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  召回策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/filterStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FilterStrategyController implements FilterStrategyApi {

    private final FilterStrategyService filterStrategyService;

    /**
     * 创建召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return filterStrategyService.create(rqt);
    }


    /**
     * 修改召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return filterStrategyService.update(rqt);
    }

    /**
     * 召回策略详情
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/detail")
    public FilterStrategy detail(@RequestBody @Valid DetailRqt rqt) {
        return filterStrategyService.detail(rqt);
    }

    /**
     * 召回策略列表
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<FilterStrategy> list(@RequestBody @Valid ListRqt rqt) {
        return filterStrategyService.list(rqt);
    }


    /**
     * 启用/禁用召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return filterStrategyService.enable(rqt);
    }

    /**
     * 删除召回策略
     *
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return filterStrategyService.delete(rqt);
    }


    @Override
    @PostMapping("/selectBySnapshotIdList")
    public List<FilterStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt){
        return filterStrategyService.selectBySnapshotIdList(rqt.getSnapshotIdList());
    }}