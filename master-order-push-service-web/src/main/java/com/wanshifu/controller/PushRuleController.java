package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushRuleApi;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;
import com.wanshifu.master.order.push.service.PushRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/pushRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushRuleController implements PushRuleApi {

    @Resource
    private PushRuleService pushRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    @Override
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return pushRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    @Override
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return pushRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    @Override
    public PushRule detail(@RequestBody @Valid DetailRqt rqt) {
        return pushRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    @Override
    public SimplePageInfo<PushRule> list(@RequestBody @Valid ListRqt rqt) {
        return pushRuleService.list(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    @Override
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return pushRuleService.delete(rqt);
    }




}
