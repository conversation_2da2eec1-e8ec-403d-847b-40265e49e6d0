package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.RoleApi;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.domain.resp.role.RoleDetailResp;
import com.wanshifu.master.order.push.domain.resp.role.RoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;
import com.wanshifu.master.order.push.service.RoleService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("role")
public class RoleController implements RoleApi {

    @Resource
    private RoleService roleService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddRoleRqt rqt) {
        return roleService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRoleRqt rqt) {
        return roleService.update(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<RoleListResp> list(@Valid @RequestBody GetRoleListRqt rqt) {
        return roleService.list(rqt);
    }


    @Override
    @PostMapping(value = "detail")
    public RoleDetailResp detail(@Valid @RequestBody GetRoleDetailRqt rqt) {
        return roleService.detail(rqt);
    }


    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteRoleRqt rqt) {
        return roleService.delete(rqt);
    }

    @Override
    @PostMapping(value = "addAccount")
    public Integer addAccount(@Valid @RequestBody AddAccountRqt rqt) {
        return roleService.addAccount(rqt);
    }

    @Override
    @PostMapping(value = "batchRoleList")
    public List<Role> batchRoleList(@Valid @RequestBody BatchRoleListRqt rqt){
        return roleService.batchRoleList(rqt);
    }

}
