package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushLimitRuleApi;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.*;
import com.wanshifu.master.order.push.service.PushLimitRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/pushLimitRule")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushLimitRuleController implements PushLimitRuleApi {

    @Resource
    private PushLimitRuleService pushLimitRuleService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    @Override
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return pushLimitRuleService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    @Override
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return pushLimitRuleService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    @Override
    public PushLimitRule detail(@RequestBody @Valid DetailRqt rqt) {
        return pushLimitRuleService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    @Override
    public SimplePageInfo<PushLimitRule> list(@RequestBody @Valid ListRqt rqt) {
        return pushLimitRuleService.list(rqt);
    }


        /**
         * 删除补偿调度策略
         * @param rqt
         * @return
         */
        @PostMapping("/enable")
        @Override
        public Integer enable(@RequestBody @Valid EnableRqt rqt) {
            return pushLimitRuleService.enable(rqt);
        }



    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    @Override
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return pushLimitRuleService.delete(rqt);
    }




}
