package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.CompensateDistributeApi;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;
import com.wanshifu.master.order.push.service.CompensateDistributeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 补偿调度
 */
@RestController
@RequestMapping("/compensateDistribute")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CompensateDistributeController implements CompensateDistributeApi {

    @Resource
    private CompensateDistributeService compensateDistributeService;

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    @Override
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return compensateDistributeService.create(rqt);
    }

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    @Override
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return compensateDistributeService.update(rqt);
    }

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    @Override
    public CompensateDistribute detail(@RequestBody @Valid DetailRqt rqt) {
        return compensateDistributeService.detail(rqt);
    }


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    @Override
    public SimplePageInfo<CompensateDistribute> list(@RequestBody @Valid ListRqt rqt) {
        return compensateDistributeService.list(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    @Override
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return compensateDistributeService.delete(rqt);
    }


    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/match")
    @Override
    public List<CompensateDistribute> match(@RequestBody @Valid MatchRqt rqt) {
        return compensateDistributeService.match(rqt);
    }



}
