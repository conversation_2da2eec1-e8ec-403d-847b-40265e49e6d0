package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.SortingStrategyApi;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.po.SortingStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.SnapshotRqt;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;
import com.wanshifu.master.order.push.service.SortingStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  精排策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/sortingStrategy")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SortingStrategyController implements SortingStrategyApi {

    private final SortingStrategyService sortingStrategyService;




    /**
     * 启用/禁用精排策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return sortingStrategyService.enable(rqt);
    }

    /**
     * 精排策略列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<SortingStrategy> list(@RequestBody @Valid ListRqt rqt) {
        return sortingStrategyService.list(rqt);
    }

    /**
     * 删除精排策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return sortingStrategyService.delete(rqt);
    }


    /**
     * 创建精排策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create/v2")
    public int createV2(@RequestBody @Valid CreateV2Rqt rqt) {
        return sortingStrategyService.createV2(rqt);
    }


    /**
     * 修改精排策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/modify/v2")
    public int updateV2(@RequestBody @Valid UpdateV2Rqt rqt) {
        return sortingStrategyService.updateV2(rqt);
    }

    /**
     * 精排策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/detail/v2")
    public SortingStrategy detailV2(@RequestBody @Valid DetailRqt rqt) {
        return sortingStrategyService.detailV2(rqt);
    }

    @Override
    @PostMapping("/selectBySnapshotIdList")
    public List<SortingStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt){
        return sortingStrategyService.selectBySnapshotIdList(rqt.getSnapshotIdList());
    }


}