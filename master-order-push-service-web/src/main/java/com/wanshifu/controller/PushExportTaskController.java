package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.PushExportTaskApi;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.CreatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.ListPushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.UpdatePushExportTaskRqt;
import com.wanshifu.master.order.push.service.PushExportTaskService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 21:40
 */
@RestController
@RequestMapping("/pushExportTask")
public class PushExportTaskController implements PushExportTaskApi {

    @Resource
    private PushExportTaskService pushExportTaskService;

    @Override
    @PostMapping("/create")
    public Long createPushExportTask(@Valid @RequestBody CreatePushExportTaskRqt createPushExportTaskRqt) {
        return pushExportTaskService.createPushExportTask(createPushExportTaskRqt);
    }

    @Override
    @PostMapping("/update")
    public void updatePushExportTask(@Valid @RequestBody UpdatePushExportTaskRqt updatePushExportTaskRqt) {
        pushExportTaskService.updatePushExportTask(updatePushExportTaskRqt);
    }

    @Override
    @PostMapping("/list")
    public SimplePageInfo<PushExportTask> listPushExportTask(@Valid @RequestBody ListPushExportTaskRqt listPushExportTaskRqt) {
        return pushExportTaskService.listPushExportTask(listPushExportTaskRqt);
    }
}
