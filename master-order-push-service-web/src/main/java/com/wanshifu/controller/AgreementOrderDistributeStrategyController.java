package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.AgreementOrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.agreementOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.service.AgreementOrderDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("agreementOrderDistributeStrategy")
public class AgreementOrderDistributeStrategyController implements AgreementOrderDistributeStrategyApi {

    @Resource
    private AgreementOrderDistributeStrategyService agreementOrderDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return agreementOrderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return agreementOrderDistributeStrategyService.update(rqt);
    }



    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public AgreementOrderDistributeStrategy detail(@Valid @RequestBody DetailRqt rqt) {
        return agreementOrderDistributeStrategyService.detail(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt){
       return agreementOrderDistributeStrategyService.enable(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<AgreementOrderDistributeStrategy> list(@Valid @RequestBody ListRqt rqt){
        return agreementOrderDistributeStrategyService.list(rqt);
    }


    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return agreementOrderDistributeStrategyService.delete(rqt);
    }

}
