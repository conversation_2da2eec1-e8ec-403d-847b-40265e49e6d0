package com.wanshifu.controller;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.StrategyCombinationApi;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.resp.strategyCombination.DetailResp;
import com.wanshifu.master.order.push.domain.rqt.strategyCombination.*;
import com.wanshifu.master.order.push.service.StrategyCombinationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 描述 :  策略组合.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:31
 */
@RestController
@RequestMapping("/strategyCombination")
@Validated
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StrategyCombinationController implements StrategyCombinationApi {

    private final StrategyCombinationService strategyCombinationService;

    /**
     * 创建组合策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return strategyCombinationService.create(rqt);
    }

    /**
     * 修改组合策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/modify")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return strategyCombinationService.update(rqt);
    }

    /**
     * 组合策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/combinationDetail")
    public DetailResp combinationDetail(@RequestBody @Valid DetailRqt rqt) {
        return strategyCombinationService.combinationDetail(rqt);
    }

    /**
     * 启用/禁用策略组合
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return strategyCombinationService.enable(rqt);
    }

    /**
     * 组合策略列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<StrategyCombination> list(@RequestBody @Valid ListRqt rqt) {
        return strategyCombinationService.list(rqt);
    }


    /**
     * 删除组合策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return strategyCombinationService.delete(rqt);
    }


    @Override
    @PostMapping("/detail")
    public StrategyCombination detail(@RequestBody @Valid DetailRqt rqt) {
        return strategyCombinationService.detail(rqt);
    }
}