package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.NewMasterOrderDistributeStrategyApi;
import com.wanshifu.master.order.push.api.OrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;
import com.wanshifu.master.order.push.service.NewMasterOrderDistributeStrategyService;
import com.wanshifu.master.order.push.service.OrderDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *  智能接单调度
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("newMasterOrderDistributeStrategy")
public class NewMasterOrderDistributeStrategyController implements NewMasterOrderDistributeStrategyApi {

    @Resource
    private NewMasterOrderDistributeStrategyService newMasterOrderDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateRqt rqt) {
        return newMasterOrderDistributeStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateRqt rqt) {
        return newMasterOrderDistributeStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableRqt rqt) {
        return newMasterOrderDistributeStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteRqt rqt) {
        return newMasterOrderDistributeStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public DetailResp detail(@Valid @RequestBody DetailRqt rqt) {
        return newMasterOrderDistributeStrategyService.detail(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<NewMasterOrderDistributeStrategy> list(@Valid @RequestBody ListRqt rqt){
        return newMasterOrderDistributeStrategyService.list(rqt);
    }


}
