package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.ExclusiveOrderSchedulerApi;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;
import com.wanshifu.master.order.push.service.ExclusiveOrderSchedulerService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/exclusiveOrderScheduler")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExclusiveOrderSchedulerController implements ExclusiveOrderSchedulerApi {

    private final ExclusiveOrderSchedulerService exclusiveOrderSchedulerService;


    /**
     * 列表
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<ExclusiveOrderScheduler> list(@RequestBody @Valid ListRqt rqt) {
        return exclusiveOrderSchedulerService.list(rqt);
    }

    /**
     * 创建
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return exclusiveOrderSchedulerService.create(rqt);
    }

    /**
     * 修改
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return exclusiveOrderSchedulerService.update(rqt);
    }

    /**
     * 详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/detail")
    public ExclusiveOrderScheduler detail(@RequestBody @Valid DetailRqt rqt) {
        return exclusiveOrderSchedulerService.detail(rqt);
    }

    /**
     * 启用/禁用
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/enable")
    public int enable(@RequestBody @Valid EnableRqt rqt) {
        return exclusiveOrderSchedulerService.enable(rqt);
    }


    /**
     * 删除
     * @param rqt
     * @return
     */
    @Override
    @PostMapping("/delete")
    public int delete(@RequestBody @Valid DeleteRqt rqt) {
        return exclusiveOrderSchedulerService.delete(rqt);
    }


}