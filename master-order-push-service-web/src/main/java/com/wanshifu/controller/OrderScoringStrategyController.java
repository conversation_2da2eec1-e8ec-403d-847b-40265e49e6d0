package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderScoringStrategyApi;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;
import com.wanshifu.master.order.push.service.OrderScoringStrategyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 18:00
 */
@RestController
@RequestMapping("orderScoringStrategy")
public class OrderScoringStrategyController implements OrderScoringStrategyApi {


    @Resource
    private OrderScoringStrategyService orderScoringStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "create")
    public Integer create(@Valid @RequestBody CreateOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.create(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.enable(rqt);
    }


    /**
     * 删除策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteOrderScoringStrategyRqt rqt) {
        return orderScoringStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public OrderScoringStrategy detail(@Valid @RequestBody OrderScoringStrategyDetailRqt rqt) {
        return orderScoringStrategyService.detail(rqt);
    }

    /**
     * 主键idList查询
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "selectAvailableStrategyByIdList")
    public List<OrderScoringStrategy> selectAvailableStrategyByIdList(@Valid @RequestBody OrderScoringStrategyByIdListRqt rqt) {
        return orderScoringStrategyService.selectAvailableStrategyByIdList(rqt);
    }



    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderScoringStrategy> list(@Valid @RequestBody GetOrderScoringStrategyListRqt rqt){
        return orderScoringStrategyService.list(rqt);
    }


    @Override
    @GetMapping("/scoringItemList")
    public List<OrderScoringItemListResp> scoringItemList(@RequestParam(value = "itemName", required = false) String itemName) {
        return orderScoringStrategyService.orderScoreItemList(itemName);
    }
}
