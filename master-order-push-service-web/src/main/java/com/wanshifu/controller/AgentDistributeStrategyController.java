package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.AgentDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.resp.agent.TobGroupAgentInfoResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import com.wanshifu.master.order.push.service.AgentDistributeStrategyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 20:22
 */
@RestController
@RequestMapping("agentDistributeStrategy")
public class AgentDistributeStrategyController implements AgentDistributeStrategyApi {

    @Resource
    private AgentDistributeStrategyService agentDistributeStrategyService;


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "add")
    public Integer add(@Valid @RequestBody AddAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.add(rqt);
    }


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "update")
    public Integer update(@Valid @RequestBody UpdateAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.update(rqt);
    }

    /**
     * 启用/禁用策略
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "enable")
    public Integer enable(@Valid @RequestBody EnableAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.enable(rqt);
    }


    @Override
    @PostMapping(value = "delete")
    public Integer delete(@Valid @RequestBody DeleteAgentDistributeStrategyRqt rqt) {
        return agentDistributeStrategyService.delete(rqt);
    }


    /**
     * 策略详情
     * @param rqt
     * @return
     */
    @Override
    @PostMapping(value = "detail")
    public AgentDistributeDetailResp detail(@Valid @RequestBody AgentDistributeStrategyDetailRqt rqt) {
        return agentDistributeStrategyService.detail(rqt);
    }


    @Override
    @PostMapping(value = "getAgentList")
    public SimplePageInfo<AgentInfo> getAgentList(@Valid @RequestBody GetAgentListRqt rqt){
        return agentDistributeStrategyService.getAgentList(rqt);
    }

    @Override
    @PostMapping(value = "getTobGroupAgentList")
    public SimplePageInfo<TobGroupAgentInfoResp> getTobGroupAgentList(@Valid @RequestBody TobGroupAgentInfoRqt rqt){
        return agentDistributeStrategyService.getTobGroupAgentList(rqt);
    }


    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<AgentDistributeStrategy> list(@Valid @RequestBody GetAgentDistributeStrategyListRqt rqt){
        return agentDistributeStrategyService.list(rqt);
    }


    @PostMapping(value = "flushAgentStrategyData")
    public void flushAgentStrategyData() {
        agentDistributeStrategyService.flushAgentStrategyData();
    }

    @Override
    @PostMapping(value = "getAgentById")
    public AgentInfo getAgentById(@Valid @RequestBody Long agentId) {
        return agentDistributeStrategyService.getAgentById(agentId);
    }

    @Override
    @PostMapping(value = "getTobGroupAgentById")
    public TobGroupAgentInfoResp getTobGroupAgentById(@Valid @RequestBody Long agentId) {
        return agentDistributeStrategyService.getTobGroupAgentById(agentId);
    }
}
