package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.OrderDynamicRoundsPushApi;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;
import com.wanshifu.master.order.push.service.OrderDynamicRoundsPushService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Title：
 *
 * @Auther ZengQingLong
 * @Date 2021/3/11
 */

@RestController
@RequestMapping("orderDynamicRoundsPush")
public class OrderDynamicRoundsPushController implements OrderDynamicRoundsPushApi {

    @Resource
    private OrderDynamicRoundsPushService dynamicRoundsPushCountService;


    @Override
    @PostMapping(value = "list")
    public SimplePageInfo<OrderDynamicRoundsPush> list(@Valid @RequestBody ListRqt rqt){
        return dynamicRoundsPushCountService.list(rqt);
    }


}
