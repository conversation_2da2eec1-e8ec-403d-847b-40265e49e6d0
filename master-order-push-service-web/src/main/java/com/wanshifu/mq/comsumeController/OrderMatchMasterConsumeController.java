package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.google.common.collect.Lists;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.message.DynamicAutoReceiveMessage;
import com.wanshifu.master.order.push.domain.message.PortPushMessage;
import com.wanshifu.master.order.push.domain.message.LongTailBatchPushMessage;
import com.wanshifu.master.order.push.domain.rqt.*;
import com.wanshifu.master.order.push.service.DynamicRoundsPushService;
import com.wanshifu.master.order.push.service.LongTailPushService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.master.order.push.service.OrderPushService;
import com.wanshifu.master.order.push.service.impl.NormalOrderAgreementAutoGrabService;
import com.wanshifu.master.order.push.service.impl.NormalOrderDistributeServiceImpl;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
@Topic("${wanshifu.rocketMQ.order-match-master-topic}")
@Slf4j
public class OrderMatchMasterConsumeController {


    private final String ORDER_MATCH_MASTER = "order_match_master";

    private final String MATCH_ORDER_PACKAGE_MASTER = "match_order_package_master";

    private final String ORDER_REFUND = "order_refund";

    private final String AGENT_PUSH = "agent_push";

    private final String SPECIAL_GROUP = "special_group";


    @Resource
    private OrderMatchMasterService orderMatchMasterService;

    @Resource
    private DynamicRoundsPushService dynamicRoundsPushService;

    @Resource
    private LongTailPushService longTailPushService;

    @Resource
    private OrderPushService orderPushService;


    @Resource
    private NormalOrderDistributeServiceImpl orderDistributeServiceImpl;

    @Resource
    private NormalOrderAgreementAutoGrabService normalOrderAgreementAutoGrabService;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;



    /**
     * 推单匹配
     *
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_MATCH_MASTER}, desc = "推单匹配")
    public int orderMatchMaster(@Validated @MessageBody OrderMatchMasterRqt rqt, Message message) {
        try{
            log.info("receive orderPush message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.match(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"推单匹配师傅失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_MATCH_MASTER, JSON.toJSONString(rqt), e));
            log.error(String.format("orderMatchMaster error,OrderMatchMasterRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }

    /**
     * 代理商定向推送后、首次查看后多少分钟无人接单消费逻辑
     *
     * @param rqt
     * @return
     */
    @Tag(value = {AGENT_PUSH}, desc = "代理商定向推送后、首次查看后多少分钟无人接单消费逻辑")
    public int agentPush(@Validated @MessageBody AgentPushRqt rqt, Message message) {
        try{
            log.info("receive agent_push message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.agentPush(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"代理商定向推送后、首次查看后多少分钟无人接单消费逻辑处理失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_MATCH_MASTER, JSON.toJSONString(rqt), e));
            log.error(String.format("agentPush error,AgentPushRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }



    /**
     * 总包直接雇佣匹配订单包师傅
     *
     * @param rqt
     * @return
     */
    @Tag(value = {MATCH_ORDER_PACKAGE_MASTER}, desc = "总包直接雇佣匹配订单包师傅")
    public int matchOrderPackageMaster(@Validated @MessageBody OrderPushRqt rqt, Message message) {
        try{
            log.info("receive matchOrderPackageMaster message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.matchOrderPackageMaster(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"总包直接雇佣匹配订单包师傅失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MATCH_ORDER_PACKAGE_MASTER, JSON.toJSONString(rqt), e));
            log.error(String.format("matchOrderPackageMaster error,OrderPushRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }


    /**
     * 订单退款同步
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_REFUND}, desc = "订单退款同步")
    public int orderRefundSync(@Validated @MessageBody RefundSyncRqt rqt, Message message) {
        log.info("receive orderRefundSync message :{}", JSON.toJSONString(rqt));
        orderMatchMasterService.orderRefundSync(rqt);
        return 1;
    }


    public static final String EXCLUSIVE_ORDER_SCHEDULED_TASK = "exclusive_order_scheduled_task";
    /**
     * 专属订单转单(推单)计划任务
     * @param rqt
     * @return
     */
    @Tag(value = {EXCLUSIVE_ORDER_SCHEDULED_TASK}, desc = "专属订单转单-推单计划任务")
    public int exclusiveOrderScheduledTask(@Validated @MessageBody OrderMatchMasterRqt rqt, Message message) {
        log.info("receive exclusiveOrderScheduledTask message :{}", JSON.toJSONString(rqt));
        orderMatchMasterService.notAppointOrderScheduledTask(rqt);
        return 1;
    }


    public static final String NOT_APPOINT_ORDER_SCHEDULED_TASK = "not_appoint_order_scheduled_task";
    /**
     * 未指派订单-转单计划任务
     * @param rqt
     * @return
     */
    @Tag(value = {NOT_APPOINT_ORDER_SCHEDULED_TASK}, desc = "未指派订单转单-推单计划任务")
    public int notAppointOrderScheduledTask(@Validated @MessageBody OrderMatchMasterRqt rqt, Message message) {
        log.info("receive notAppointOrderScheduledTask message :{}", JSON.toJSONString(rqt));
        orderMatchMasterService.notAppointOrderScheduledTask(rqt);
        return 1;
    }


    private final String  ORDER_MATCH_ROUTE = "order_match_route";
    /**
     * 订单匹配路由
     * @param routeMessage
     * @return
     */
    @Tag(value = {ORDER_MATCH_ROUTE}, desc = "订单匹配路由")
    public int orderMatchRoute(@Validated @MessageBody OrderMatchRouteMessage routeMessage, Message message) {
        log.info("receive orderMatchRoute message :{}", JSON.toJSONString(routeMessage));
        orderMatchMasterService.orderMatchRoute(routeMessage);
        return 1;
    }



    private final String  COMPENSATE_DISTRIUBTE = "compensate_distribute";
    /**
     * 订单补偿调度
     * @param distributeMessage
     * @return
     */
    @Tag(value = {COMPENSATE_DISTRIUBTE}, desc = "订单补偿调度")
    public int compensateDistribute(@Validated @MessageBody CompensateDistributeMessage distributeMessage, Message message) {
        try{
            log.info("receive compensateDistribute message :{}", JSON.toJSONString(distributeMessage));
            orderMatchMasterService.compensateDistribute(distributeMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"订单补偿调度失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    COMPENSATE_DISTRIUBTE, JSON.toJSONString(distributeMessage), e));
            log.error(String.format("compensateDistribute error,CompensateDistributeMessage:%s,{}",JSON.toJSONString(distributeMessage)),e);
            throw e;
        }
    }


    /**
     * 动态分轮轮动推送
     *
     * @param rqt
     * @return
     */
    private final String WHEEL_ROUNDS_PUSH = "wheel_rounds_push";
    @Tag(value = {WHEEL_ROUNDS_PUSH}, desc = "动态分轮轮动推送")
    public int wheelRoundsPush(@Validated @MessageBody WheelRoundsPushMessage rqt, Message message) {
        try{
            log.info("receive orderPush message :{}", JSON.toJSONString(rqt));
            dynamicRoundsPushService.wheelRoundsPush(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"动态分轮轮动推送失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    COMPENSATE_DISTRIUBTE, JSON.toJSONString(rqt), e));
            log.error(String.format("wheelRoundsPush error,WheelRoundsPushMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }


    /**
     * 重新推单
     *
     * @param rqt
     * @return
     */
    private final String AFRESH_PUSH = "afresh_push";
    @Tag(value = {AFRESH_PUSH}, desc = "重新推单")
    public int afreshPush(@Validated @MessageBody OrderAfreshPushRqt rqt , Message message) {
        try{
            log.info("receive orderPush message :{}", JSON.toJSONString(rqt));
            orderPushService.afreshPush(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"重新推单失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    AFRESH_PUSH, JSON.toJSONString(rqt), e));
            log.error(String.format("afreshPush error,OrderAfreshPushRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }


    /**
     * 长尾单分批推送
     *
     * @param rqt
     * @return
     */
    private final String LONG_TAIL_BATCH_PUSH = "long_tail_batch_push";
    @Tag(value = {LONG_TAIL_BATCH_PUSH}, desc = "长尾单分批推送")
    public int longTailBatchPush(@Validated @MessageBody LongTailBatchPushMessage rqt , Message message) {
        try{
            log.info("receive orderPush message :{}", JSON.toJSONString(rqt));
            longTailPushService.longTailBatchPush(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"长尾单分批推送失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    LONG_TAIL_BATCH_PUSH, JSON.toJSONString(rqt), e));
            log.error(String.format("longTailBatchPush error,LongTailBatchPushMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }


    /**
     * C端订单端口推送
     *
     * @param rqt
     * @return
     */
    private final String PORT_PUSH = "port_push";
    @Tag(value = {PORT_PUSH}, desc = "订单端口推单")
    public int portPush(@Validated @MessageBody PortPushMessage rqt , Message message) {
        try{
            log.info("receive portPush message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.portPush(rqt);
            return 1;
        }catch(Exception e){
            log.error(String.format("portPush error ,PortPushMessage:%s",JSON.toJSONString(rqt)),e);
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"订单端口推单失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    PORT_PUSH, JSON.toJSONString(rqt), e));
            log.error(String.format("portPush error,PortPushMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }
    }



    /**
     * 师傅自动接单（动态自动接单）
     *
     * @param rqt
     * @return
     */
    private final String DYNAMIC_AUTO_RECEIVE = "dynamic_auto_receive";
    @Tag(value = {DYNAMIC_AUTO_RECEIVE}, desc = "师傅自动接单（动态自动接单）")
    public int dynamicAutoReceive(@Validated @MessageBody DynamicAutoReceiveMessage rqt , Message message) {
        try{
            log.info("receive dynamicAutoReceive message :{}", JSON.toJSONString(rqt));
            orderDistributeServiceImpl.dynamicAutoReceive(rqt.getOrderId());
            return 1;
        }catch(Exception e){
            log.error(String.format("dynamicAutoReceive error ,DynamicAutoReceiveMessage:%s",JSON.toJSONString(rqt)),e);
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"师傅自动接单（动态自动接单）失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    DYNAMIC_AUTO_RECEIVE, JSON.toJSONString(rqt), e));
            throw e;
        }
    }



    /**
     * 协议师傅兜底报价
     *
     * @param compensateDistributeMessage
     * @return
     */
    private final String NORMAL_ORDER_DISTRIBUTE = "normal_order_distribute";
    @Tag(value = {NORMAL_ORDER_DISTRIBUTE}, desc = "普通订单调度")
    public int normalOrderDistribute(@Validated @MessageBody CompensateDistributeMessage compensateDistributeMessage, Message message) {
        try{
            normalOrderAgreementAutoGrabService.agreementAutoReceive(compensateDistributeMessage);
        }catch(Exception e){
            log.error(String.format("协议师傅兜底报价,masterOrderId:%d",compensateDistributeMessage.getOrderId()),e);
        }
        return 0;
    }

    /**
     * 家庭协议推单多少分钟后推普通师傅
     *
     * @param familyAgreementRePushNormalRqt
     * @return
     */
    private final String FAMILY_AGREEMENT_RE_PUSH_NORMAL = "family_agreement_re_push_normal";
    @Tag(value = {FAMILY_AGREEMENT_RE_PUSH_NORMAL}, desc = "家庭协议推单多少分钟后推普通师傅")
    public int familyAgreementRePushNormal(@Validated @MessageBody FamilyAgreementRePushNormalRqt familyAgreementRePushNormalRqt, Message message) {
        try{
            log.info("receive familyAgreementRePushNormal message :{}", JSON.toJSONString(familyAgreementRePushNormalRqt));
            orderPushService.familyAgreementRePushNormal(familyAgreementRePushNormalRqt);
        }catch(Exception e){
            log.error(String.format("家庭协议推单多少分钟后推普通师傅,masterOrderId:%d", familyAgreementRePushNormalRqt.getMasterOrderId()), e);
        }
        return 0;
    }



    /**
     * 协议师傅兜底报价
     *
     * @param compensateDistributeMessage
     * @return
     */

    private final static String PUSH_GOLD_MEDAL_MASTER = "push_gold_medal_master";
    @Tag(value = {PUSH_GOLD_MEDAL_MASTER}, desc = "推送金牌维修师傅")
    public int pushGoldMedalMaster(@Validated @MessageBody PushGoldMedalMasterMessage pushGoldMedalMasterMessage, Message message) {
        try{
            orderMatchMasterService.pushGoldMedalMaster(pushGoldMedalMasterMessage);
        }catch(Exception e){
            log.error(String.format("pushGoldMedalMaster error ,PushGoldMedalMasterMessage:%s",JSON.toJSONString(pushGoldMedalMasterMessage)),e);
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"订单无人报价推送金牌维修师傅失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    PUSH_GOLD_MEDAL_MASTER, JSON.toJSONString(pushGoldMedalMasterMessage), e));
            throw e;
        }
        return 0;
    }

    /**
     * 特殊人群匹配
     *
     * @param rqt 订单匹配请求体
     * @return
     */
    @Tag(value = {SPECIAL_GROUP}, desc = "特殊人群匹配")
    public int specialGroupMatchMaster(@Validated @MessageBody OrderMatchMasterRqt rqt) {
        try {
            log.info("receive special group message :{}", JSON.toJSONString(rqt));
            return orderMatchMasterService.specialGroupPush(rqt);
        } catch (Exception e) {
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl, feiShuRobotNotifySign, "推单匹配师傅失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    SPECIAL_GROUP, JSON.toJSONString(rqt), e));
            log.error(String.format("orderMatchMaster error,OrderMatchMasterRqt:%s,{}", JSON.toJSONString(rqt)), e);
            throw e;
        }
    }

}
