package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.AgentDirectAppointDto;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterAutoGrabMessage;
import com.wanshifu.master.order.push.domain.dto.CompensateDistributeMessage;
import com.wanshifu.master.order.push.domain.po.DistributeResultNotices;
import com.wanshifu.master.order.push.service.AgentDirectAppointService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.master.order.push.service.impl.NormalOrderAgreementAutoGrabService;
import com.wanshifu.master.order.push.service.impl.NormalOrderDistributeServiceImpl;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderCancelMasterLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单调度结果通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.order-distribute-topic}")
@Slf4j
public class OrderDistributeConsumeController {


    /**
     * 创建订单
     */
    private final String DISTRIBUTE_RESULT_NOTICES = "distribute_result_notices";

    /**
     * 合作商订单调度
     */
    private static final String AGENT_DIRECT_APPOINT = "agent_direct_appoint";



    @Resource
    private OrderMatchMasterService orderMatchMasterService;



    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Autowired
    private AgentDirectAppointService agentDirectAppointService;


    @Autowired
    private NormalOrderDistributeServiceImpl normalOrderDistributeService;

    @Resource
    private NormalOrderAgreementAutoGrabService normalOrderAgreementAutoGrabService;


    /**
     * 调度服务合并开关
     */
    @Value("${order.orderPush.distributeServiceMergeSwitch:on}")
    private String distributeServiceMergeSwitch;

    /**
     * 订单调度结果通知
     *
     * @param rqt
     * @return
     */
    @Tag(value = {DISTRIBUTE_RESULT_NOTICES}, desc = "调度结果通知")
    @Deprecated
    public int distributeResultNotices(@Validated @MessageBody DistributeResultNotices rqt, Message message) {
        try{
            log.info("receive orderDistributeResult message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.distributeResultNotices(rqt);
            return 1;
        }catch(Exception e){
//            feiShuRobotNotifyUtils.sendRichTextNotice("处理订单调度结果异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
//                    DISTRIBUTE_RESULT_NOTICES, JSON.toJSONString(rqt), e));
            log.error(String.format("orderDistributeResult error,PushedResultNotices:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }


    /**
     * 协议师傅动态抢单
     *
     * @param agreementMasterAutoGrabMessage
     * @return
     */
    private final String AGREEMENT_MASTER_AUTO_GRAB  = "agreement_master_auto_grab";
    @Tag(value = {AGREEMENT_MASTER_AUTO_GRAB}, desc = "协议师傅动态抢单")
    public int agreementMasterAutoGrab(@Validated @MessageBody AgreementMasterAutoGrabMessage agreementMasterAutoGrabMessage, Message message) {
        try{
            log.info("receive agreementMasterAutoGrab message :{}", JSON.toJSONString(agreementMasterAutoGrabMessage));
            normalOrderDistributeService.userAgreementMasterAutoGrab(agreementMasterAutoGrabMessage);
        }catch(Exception e){
            log.error(String.format("协议师傅动态抢单失败,masterOrderId:%d",agreementMasterAutoGrabMessage.getOrderId()),e);
        }
        return 0;
    }


    /**
     * 代理商直接指派
     *
     * @param agentDirectAppointRqt
     * @return
     */
    @Tag(value = {AGENT_DIRECT_APPOINT}, desc = "代理商直接指派")
    @PostMapping("agentDirectAppoint")
    @Deprecated
    public int agentDirectAppoint(@Validated @MessageBody @RequestBody AgentDirectAppointDto agentDirectAppointRqt, Message message) {
        return 0;
        /*if ("on".equals(distributeServiceMergeSwitch)) {
            final Long orderId = agentDirectAppointRqt.getOrderId();
            OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
            OrderBase orderBase = orderGrabByIdResp.getOrderBase();

            //代理商取消指派的订单不自动指派
            if (1 == getCancelAppoint(
                    orderBase.getGlobalOrderTraceId(),
                    orderBase.getAccountType(),
                    orderBase.getAccountId()
            )) {
                log.info("orderId:{},代理商取消指派订单不作二次自动指派:[{},{},{}],{}",
                        orderId,
                        orderBase.getGlobalOrderTraceId(),
                        orderBase.getAccountType(),
                        orderBase.getAccountId(),
                        JSON.toJSONString(agentDirectAppointRqt));
                return 0;
            }

            try {
                return agentDirectAppointService.agentDirectAppoint(agentDirectAppointRqt);
            } catch (Exception e) {
                log.error(String.format("代理商直接指派,masterOrderId:%d", agentDirectAppointRqt.getOrderId()), e);
            }
            return 0;
        } else {
            return 0;
        }*/
    }

    /**
     * 订单是否取消指派
     * @param globalOrderId
     * @return
     */
    private int getCancelAppoint(Long globalOrderId,String accountType,Long AccountId){
        final List<OrderCancelMasterLog> orderCancelMasterLogs = appointedModuleResourceApi.selectOrderCancelMasterLog(
                globalOrderId,accountType,AccountId);
        if (orderCancelMasterLogs!=null&&orderCancelMasterLogs.size()!=0) {
            final boolean anyMatch = orderCancelMasterLogs.stream().anyMatch(
                    row ->
                            row.getIsDelete() == 0
                                    && "agent".equals(row.getCurrentTagNames())
            );
            if (anyMatch) {
                return 1;
            }
        }
        return 0;
    }



    /**
     * 协议师傅兜底报价
     *
     * @param compensateDistributeMessage
     * @return
     */
    /*private final String NORMAL_ORDER_DISTRIBUTE = "normal_order_distribute";
    @Tag(value = {NORMAL_ORDER_DISTRIBUTE}, desc = "普通订单调度")
    public int normalOrderDistribute(@Validated @MessageBody CompensateDistributeMessage compensateDistributeMessage, Message message) {
        try{
            normalOrderAgreementAutoGrabService.agreementAutoReceive(compensateDistributeMessage);
        }catch(Exception e){
            log.error(String.format("协议师傅兜底报价,masterOrderId:%d",compensateDistributeMessage.getOrderId()),e);
        }
        return 0;
    }*/




}
