package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.message.*;
import com.wanshifu.master.order.push.service.EnterpriseAppointMasterService;
import com.wanshifu.master.order.push.service.MasterOperationNotifyService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.master.order.push.service.impl.NormalOrderDistributeServiceImpl;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 订单推单通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.order-push-result-topic}")
@Slf4j
public class CommonOrderGeneralConsumeController {


    /**
     * 创建订单
     */
    private final String GET_SUPPORT_MASTER = "getSupportMaster";

    private final String GET_AGREEMENT_MASTER = "getAgreementMaster";




    @Resource
    private OrderMatchMasterService orderMatchMasterService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    @Resource
    private MasterOperationNotifyService masterOperationNotifyService;


    @Resource
    private EnterpriseAppointMasterService enterpriseAppointMasterService;


    @Resource
    private NormalOrderDistributeServiceImpl orderDistributeServiceImpl;
    /**
     * 总包直接指派匹配协议师傅
     *
     * @param rqt
     * @return
     */
    @Tag(value = {GET_AGREEMENT_MASTER}, desc = "总包直接指派匹配协议师傅")
    public int matchAgreementMaster(@Validated @MessageBody EnterpriseAppointMessage rqt, Message message) {
        try{
            log.info("receive matchAgreementMaster message :{}", JSON.toJSONString(rqt));
            enterpriseAppointMasterService.matchAppointMaster(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"总包直接指派匹配协议师傅异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    GET_AGREEMENT_MASTER, JSON.toJSONString(rqt), e));
            log.error(String.format("matchAgreementMaster error,matchAgreementMasterRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }



    /**
     * 总包匹配技能验证师傅
     *
     * @param rqt
     * @return
     */
    @Tag(value = {GET_SUPPORT_MASTER}, desc = "总包匹配技能验证师傅")
    public int matchTechniqueVerifyMaster(@Validated @MessageBody MatchSupportMasterRqt rqt, Message message) {
        try{
            log.info("receive matchTechniqueVerifyMaster message :{}", JSON.toJSONString(rqt));
            enterpriseAppointMasterService.matchTechniqueVerifyMaster(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"总包匹配技能验证师傅", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    GET_SUPPORT_MASTER, JSON.toJSONString(rqt), e));
            log.error(String.format("matchTechniqueVerifyMaster error,matchTechniqueVerifyMasterRqt:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }




    private final String MASTER_RESERVE_CUSTOMER = "master_reserveCustomer";


    @Tag(value = {MASTER_RESERVE_CUSTOMER}, desc = "师傅预约客户")
    public int masterReserveCustomer(@Validated @MessageBody MasterReserveCustomerMessage masterReserveCustomerMessage, Message message) {
        try{
            log.info("receive matchSupportMaster message :{}", JSON.toJSONString(masterReserveCustomerMessage));
            masterOperationNotifyService.masterReserveCustomer(masterReserveCustomerMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理师傅预约客户mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_RESERVE_CUSTOMER, JSON.toJSONString(masterReserveCustomerMessage), e));
            log.error(String.format("masterReserveCustomer error,MasterReserveCustomerMessage:%s,{}",JSON.toJSONString(masterReserveCustomerMessage)),e);
            throw e;
        }

    }


    private final String MASTER_HANG_ORDER = "master_hang_order";

    @Tag(value = {MASTER_HANG_ORDER}, desc = "师傅挂起订单")
    public int masterHangOrder(@Validated @MessageBody MasterHangOrderMessage masterHangOrderMessage, Message message) {
        try{
            log.info("receive masterHangOrder message :{}", JSON.toJSONString(masterHangOrderMessage));
            masterOperationNotifyService.masterHangOrder(masterHangOrderMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理师傅挂起订单mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_HANG_ORDER, JSON.toJSONString(masterHangOrderMessage), e));
            log.error(String.format("masterHangOrder error,MasterHangOrderMessage:%s,{}",JSON.toJSONString(masterHangOrderMessage)),e);
            throw e;
        }

    }


    private final String MASTER_SERVE_FINISH = "master_serveFinish";

    @Tag(value = {MASTER_SERVE_FINISH}, desc = "师傅服务完工-非返货")
    public int masterServeFinish(@Validated @MessageBody MasterServeFinishMessage masterServeFinishMessage, Message message) {
        try{
            log.info("receive masterServeFinish message :{}", JSON.toJSONString(masterServeFinishMessage));
            masterOperationNotifyService.masterServeFinish(masterServeFinishMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理师傅服务完工(非返货)mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_SERVE_FINISH, JSON.toJSONString(masterServeFinishMessage), e));
            log.error(String.format("masterServeFinish error,MasterServeFinishMessage:%s,{}",JSON.toJSONString(masterServeFinishMessage)),e);
            throw e;
        }

    }


    private final String MASTER_SERVE_RETURN_FINISH = "master_serveReturnFinish";

    @Tag(value = {MASTER_SERVE_RETURN_FINISH}, desc = "师傅服务完工-返货")
    public int masterServeReturnFinish(@Validated @MessageBody MasterServeReturnFinishMessage masterServeReturnFinishMessage, Message message) {
        try{
            log.info("receive masterServeReturnFinish message :{}", JSON.toJSONString(masterServeReturnFinishMessage));
            masterOperationNotifyService.masterServeReturnFinish(masterServeReturnFinishMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理师傅服务完工(返货)mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_SERVE_RETURN_FINISH, JSON.toJSONString(masterServeReturnFinishMessage), e));
            log.error(String.format("masterServeReturnFinish error,MasterServeReturnFinishMessage:%s,{}",JSON.toJSONString(masterServeReturnFinishMessage)),e);
            throw e;
        }

    }


    /**
     * 师傅报价
     *
     * @param rqt
     * @returnMasterOfferPriceMessage
     */
    private final String MASTER_OFFER_PRICE = "master_offerPrice";
    @Tag(value = {MASTER_OFFER_PRICE}, desc = "师傅报价")
    public int masterOfferPrice(@Validated @MessageBody MasterOfferPriceMessage rqt, Message message) {
        try{
            log.info("receive masterOfferPrice message :{}", JSON.toJSONString(rqt));
            masterOperationNotifyService.masterOfferPrice(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅报价mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_OFFER_PRICE, JSON.toJSONString(rqt), e));
            log.error(String.format("masterOfferPrice error,MasterOfferPriceMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }


    /**
     * 师傅修改报价
     *
     * @param rqt
     * @return int
     */
    private final String MASTER_MODIFY_OFFER_PRICE = "master_modify_offerPrice";

    @Tag(value = {MASTER_MODIFY_OFFER_PRICE}, desc = "师傅修改报价")
    public int masterModifyOfferPrice(@Validated @MessageBody MasterModifyOfferPriceMessage rqt, Message message) {
        try{
            log.info("receive masterModifyOfferPrice message :{}", JSON.toJSONString(rqt));
            masterOperationNotifyService.masterModifyOfferPrice(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅修改报价mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_MODIFY_OFFER_PRICE, JSON.toJSONString(rqt), e));
            log.error(String.format("masterModifyOfferPrice error,MasterModifyOfferPriceMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }


    /**
     * C端师傅B端APP报价限制
     *
     * @param rqt
     * @returnMasterOfferPriceMessage
     */
    private final String TOC_MASTER_AT_TOB_APP_OFFER_LIMIT = "toc_master_at_tob_app_offer_limit";
    @Tag(value = {TOC_MASTER_AT_TOB_APP_OFFER_LIMIT}, desc = "C端师傅B端APP报价限制")
    public int tocMasterAtTobAppOfferLimit(@Validated @MessageBody TocMasterAtTobAppOfferLimitMessage rqt, Message message) {
        try{
            log.info("receive tocMasterAtTobAppOfferLimit message :{}", JSON.toJSONString(rqt));
            masterOperationNotifyService.tocMasterAtTobAppOfferLimit(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费C端师傅B端APP报价限制mq消息异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_OFFER_PRICE, JSON.toJSONString(rqt), e));
            log.error(String.format("tocMasterAtTobAppOfferLimit error,MasterOfferPriceMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }





}
