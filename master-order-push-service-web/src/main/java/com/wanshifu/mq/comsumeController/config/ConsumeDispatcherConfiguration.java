package com.wanshifu.mq.comsumeController.config;

import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.EnableMQConsumeDispatcher;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.configuration.ConsumeDispatcherConfigurer;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.meta.interceptor.HandlerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@EnableMQConsumeDispatcher(reconsumeCount = "16")
@Configuration
public class ConsumeDispatcherConfiguration implements ConsumeDispatcherConfigurer {


    @Override
    public void addInterceptors(List<HandlerInterceptor> interceptors) {


    }


}
