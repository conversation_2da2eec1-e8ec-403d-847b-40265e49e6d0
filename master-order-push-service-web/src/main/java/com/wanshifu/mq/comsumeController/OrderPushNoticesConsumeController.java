package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.ConfirmAppointMasterMessage;
import com.wanshifu.master.order.push.domain.dto.OrderCloseMessage;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.order.offer.domains.vo.DirectAppointOrderMessage;
import com.wanshifu.order.offer.domains.vo.OrderCanceledAutoGrabMessage;
import com.wanshifu.order.offer.domains.vo.appointed.CancelMasterMessage;
import com.wanshifu.order.offer.domains.vo.publish.ExclusiveOrderTransferMessage;
import com.wanshifu.order.offer.domains.vo.publish.OrderPackageConfirmMessage;
import com.wanshifu.order.offer.domains.vo.push.OrderPushNotices;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 订单推单通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.order-offer-service-common-topic}")
@Slf4j
public class OrderPushNoticesConsumeController {


    /**
     * 创建订单
     */
    private final String ORDER_PUSH_NOTICES = "order_push_notices";

    /**
     * 总包直接指派确认订单包
     */
    private final String ORDER_PACKAGE_CONFIRM = "order_package_confirm";

    /**
     * 修改订单
     */
    private final String ORDER_AFRESH_PUSH_NOTICES = "order_afresh_push_notices";

    /**
     * 订单转单
     */
    private final String EXCLUSIVE_ORDER_TRANSFER = "exclusive_order_transfer";



    /**
     * 取消指派师傅：家庭预付款、企业一口价的取消指派
     */
    private final String ORDER_CANCEL_MASTER = "order_cancel_master";


    /**
     * 订单关单mq
     */
    private final String ORDER_CLOSE = "order_close";



    @Resource
    private OrderMatchMasterService orderMatchMasterService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;


    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;

    /**
     * 订单创建通知
     *
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_PUSH_NOTICES}, desc = "订单创建通知")
    public int orderPushNotices(@Validated @MessageBody OrderPushNotices rqt, Message message) {
        try{
            log.info("receive orderPusNotices message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.orderPushNotices(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"推单异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_PUSH_NOTICES, JSON.toJSONString(rqt), e));
            log.error(String.format("orderPushNotices error,orderPushNoticesMessage:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }


    /**
     * 总包直接雇佣匹配订单包师傅
     *
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_PACKAGE_CONFIRM}, desc = "总包直接雇佣匹配订单包师傅")
    public int orderPackageConfirm(@Validated @MessageBody OrderPackageConfirmMessage rqt, Message message) {
        try{
            log.info("receive OrderPackageConfirmMessage message :{}", JSON.toJSONString(rqt));
            orderMatchMasterService.orderPackageConfirm(rqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"总包确认订单包异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_PACKAGE_CONFIRM, JSON.toJSONString(rqt), e));
            log.error(String.format("orderPackageConfirm error,orderPackageConfirmMessage:%s",JSON.toJSONString(rqt)));
            throw e;
        }

    }


    /**
     * 订单修改通知
     *
     * @param
     * @return
     */
    @Tag(value = {ORDER_AFRESH_PUSH_NOTICES}, desc = "订单重新推单通知")
    public int orderAfreshPushNotices(@Validated @MessageBody OrderPushNotices orderPushNotices, Message message) {
        try{
            log.info("receive orderAfreshPushNotices message :{}", JSON.toJSONString(orderPushNotices));
            orderMatchMasterService.orderPushNotices(orderPushNotices);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费订单重新推单通知mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_AFRESH_PUSH_NOTICES, JSON.toJSONString(orderPushNotices), e));
            log.error(String.format("orderAfreshPushNotices error,orderAfreshPushNotices:%s",JSON.toJSONString(orderPushNotices)));
            throw e;
        }

    }


    /**
     * 订单转单通知
     *
     * @param
     * @return
     */
    @Tag(value = {EXCLUSIVE_ORDER_TRANSFER}, desc = "订单转单通知")
    public int exclusiveOrderTransfer(@Validated @MessageBody ExclusiveOrderTransferMessage exclusiveOrderTransferMessage, Message message) {
        try{
            log.info("receive orderTransfer message :{}", JSON.toJSONString(exclusiveOrderTransferMessage));
            orderMatchMasterService.exclusiveOrderTransfer(exclusiveOrderTransferMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"订单转单异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    EXCLUSIVE_ORDER_TRANSFER, JSON.toJSONString(exclusiveOrderTransferMessage), e));
            log.error(String.format("exclusiveOrderTransfer error,exclusiveOrderTransferMessage:%s",JSON.toJSONString(exclusiveOrderTransferMessage)));
            throw e;
        }
    }



    /**
     * 取消指派师傅
     *
     * @param
     * @return
     */
    @Tag(value = {ORDER_CANCEL_MASTER}, desc = "取消指派师傅")
    public int orderCancelMaster(@Validated @MessageBody CancelMasterMessage cancelMasterMessage, Message message) {
        try{
            log.info("receive orderCancelMaster message :{}", JSON.toJSONString(cancelMasterMessage));
            orderMatchMasterService.orderCancelMaster(cancelMasterMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费取消指派师傅mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_CANCEL_MASTER, JSON.toJSONString(cancelMasterMessage), e));
            log.error(String.format("orderCancelMaster error,cancelMasterMessage:%s",JSON.toJSONString(cancelMasterMessage)));
            throw e;
        }

    }


    /**
     * 订单取消锁定
     */
    private final String ORDER_CANCELED_AUTO_GRAB = "order_canceled_auto_grab";
    @Tag(value = {ORDER_CANCELED_AUTO_GRAB}, desc = "订单取消锁定")
    public int orderCancelAutoGrab(@Validated @MessageBody OrderCanceledAutoGrabMessage orderCanceledAutoGrabMessage, Message message) {
        try{
            log.info("receive orderCancelAutoGrab message :{}", JSON.toJSONString(orderCanceledAutoGrabMessage));
            orderMatchMasterService.orderCancelAutoGrab(orderCanceledAutoGrabMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费订单取消锁定mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_CANCELED_AUTO_GRAB, JSON.toJSONString(orderCanceledAutoGrabMessage), e));
            log.error(String.format("orderCancelAutoGrab error,orderCanceledAutoGrabMessage:%s",JSON.toJSONString(orderCanceledAutoGrabMessage)));
            throw e;
        }

    }




    /**
     * 确认雇佣师傅
     */
    private final String CONFIRM_APPOINT_MASTER = "confirm_appoint_master";
    @Tag(value = {CONFIRM_APPOINT_MASTER}, desc = "确认雇佣师傅")
    public int confirmAppointMaster(@Validated @MessageBody ConfirmAppointMasterMessage confirmAppointMasterMessage, Message message) {
        try{
            log.info("receive confirmAppointMaster message :{}", JSON.toJSONString(confirmAppointMasterMessage));
            orderMatchMasterService.confirmAppointMaster(confirmAppointMasterMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费确认雇佣师傅mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    CONFIRM_APPOINT_MASTER, JSON.toJSONString(confirmAppointMasterMessage), e));
            log.error(String.format("orderCancelAutoGrab error,orderCanceledAutoGrabMessage:%s",JSON.toJSONString(confirmAppointMasterMessage)));
            throw e;
        }

    }


    /**
     * 订单关单
     * @param orderCloseMessage
     * @param message
     * @return
     */
    @Tag(value = {ORDER_CLOSE}, desc = "订单关单")
    public int orderClose(@Validated @MessageBody OrderCloseMessage orderCloseMessage, Message message) {
        try{
            log.info("receive order_close message :{}", JSON.toJSONString(orderCloseMessage));
            orderMatchMasterService.orderClose(orderCloseMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费订单关单mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_CLOSE, JSON.toJSONString(orderCloseMessage), e));
            log.error(String.format("order_close error,orderCloseMessage:%s",JSON.toJSONString(orderCloseMessage)));
            throw e;
        }

    }






}
