package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.message.*;
import com.wanshifu.master.order.push.service.MasterOperationNotifyService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 订单推单通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.master-order-serve-topic}")
@Slf4j
public class MasterOrderServeConsumeController {


    /**
     * 创建订单
     */
    private final String MASTER_AGREE_REFUND = "master_agree_refund";



    @Resource
    private OrderMatchMasterService orderMatchMasterService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    @Resource
    private MasterOperationNotifyService masterOperationNotifyService;

    /**
     * 订单调度结果通知
     *
     * @param masterAgreeRefundMessage
     * @return
     */
    @Tag(value = {MASTER_AGREE_REFUND}, desc = "师傅同意退款")
    public int masterAgreeRefund(@Validated @MessageBody MasterAgreeRefundMessage masterAgreeRefundMessage, Message message) {
        try{
            log.info("receive masterAgreeRefund message :{}", JSON.toJSONString(masterAgreeRefundMessage));
            masterOperationNotifyService.masterAgreeRefund(masterAgreeRefundMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅同意退款mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_AGREE_REFUND, JSON.toJSONString(masterAgreeRefundMessage), e));
            log.error(String.format("masterAgreeRefund error,MasterAgreeRefundMessage:%s,{}",JSON.toJSONString(masterAgreeRefundMessage)),e);
            throw e;
        }

    }






}
