package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.DistributeOrderDistributeService;
import com.wanshifu.master.order.push.service.OrderPushedResultNoticeService;
import com.wanshifu.master.order.push.service.impl.OrderDistributeContext;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 订单推单通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.order-push-service-common-topic}")
@Slf4j
public class OrderPushResultNoticeConsumeController {

    private final String ORDER_PUSHED_RESULT_NOTICE = "order_pushed_result_notice";

    @Resource
    private OrderPushedResultNoticeService orderPushedResultNoticeService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;


    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    /**
     * 推单结果通知
     *
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_PUSHED_RESULT_NOTICE}, desc = "推单结果通知")
    public int orderPushedResultNotice(@Validated @MessageBody OrderPushedResultNotice rqt, Message message) {
        try{
            log.info("receive orderPushedResultNotice message :{}", JSON.toJSONString(rqt));
            return orderPushedResultNoticeService.pushedResultNotice(rqt);
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理推单结果通知异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_PUSHED_RESULT_NOTICE, JSON.toJSONString(rqt), e));
            log.error(String.format("orderPushedResultNotice error,OrderPushedResultNotice:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }



}
