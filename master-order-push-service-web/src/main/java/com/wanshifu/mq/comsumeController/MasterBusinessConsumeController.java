package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.MasterSettleInRqt;
import com.wanshifu.master.order.push.service.MasterOperationNotifyService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.master.order.push.service.impl.MasterOperationNotifyServiceImpl;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

@Component
@Topic("${wanshifu.rocketMQ.master-business-topic}")
@Slf4j
public class MasterBusinessConsumeController {

    @Resource
    private OrderMatchMasterService orderMatchMasterService;

    @Resource
    private MasterOperationNotifyService masterOperationNotifyService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    /**
     * 师傅入驻推单
     * @param masterSettleInRqt
     * @return
     */
    private final String MASTER_SETTLE_IN = "master_settle_in";
    @Tag(value = {MASTER_SETTLE_IN}, desc = "师傅入驻推单")
    public int masterSettleIn(@Validated @MessageBody MasterSettleInRqt masterSettleInRqt, Message message) {
        try{
            log.info("receive masterSettleIn message :{}", JSON.toJSONString(masterSettleInRqt));
            masterOperationNotifyService.masterSettleIn(masterSettleInRqt);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅入驻mq失败", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_SETTLE_IN, JSON.toJSONString(masterSettleInRqt), e));
            log.error(String.format("masterSettleIn error,MasterSettleInRqt:%s,{}",JSON.toJSONString(masterSettleInRqt)),e);
            throw e;
        }
    }



}
