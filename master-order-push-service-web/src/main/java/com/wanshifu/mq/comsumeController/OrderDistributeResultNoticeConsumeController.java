package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.impl.OrderDistributeContext;
import com.wanshifu.master.order.push.service.impl.OrderDistributeResultNoticeContext;
import com.wanshifu.master.order.push.service.impl.OrderDistributeResultNoticeFacade;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;


/**
 * 订单推单通知
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.master-order-common-offer-topic}")
@Slf4j
public class OrderDistributeResultNoticeConsumeController {

    private final String ORDER_DISTRIBUTE_RESULT = "order_distribute_result";

    @Resource
    private OrderDistributeResultNoticeFacade orderDistributeResultNoticeFacade;



    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;



    /**
     * 订单调度结果通知
     *
     * @param rqt
     * @return
     */
    @Tag(value = {ORDER_DISTRIBUTE_RESULT}, desc = "订单调度结果通知")
    public int orderDistributeResultNotice(@Validated @MessageBody OrderDistributeResultMessage rqt, Message message) {
        try{
            log.info("receive orderDistributeResultNotice message :{}", JSON.toJSONString(rqt));
            return orderDistributeResultNoticeFacade.distributeResultNotice(rqt);
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"处理订单调度结果通知异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_DISTRIBUTE_RESULT, JSON.toJSONString(rqt), e));
            log.error(String.format("orderPushedResultNotice error,OrderPushedResultNotice:%s,{}",JSON.toJSONString(rqt)),e);
            throw e;
        }

    }



}
