package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.rqt.*;
import com.wanshifu.master.order.push.repository.OrderPushAbnormalResultRepository;
import com.wanshifu.master.order.push.service.LongTailPushService;
import com.wanshifu.master.order.push.service.OrderPushService;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Random;

/**
 * 推单mq消费controller
 * <AUTHOR>
 */
@Component
@Topic("${wanshifu.rocketMQ.order-push-topic}")
@Slf4j
public class OrderPushConsumeController {

    private static final String INVITE_ORDER_PUSH="invite_order_push";

    private static final String ORDER_PUSH = "order_push";

    private static final String DELAY_PUSH = "delay_push";

    private static final String REPUSH_ORDER = "repush_order";

    private static final String NEARBY_PUSH = "nearby_push";

    private static final String LONG_TAIL_PUSH = "long_tail_push";


    @Value("${wanshifu.rocketMQ.order-push-topic}")
    private String orderPushTopic;


    @Value("${orderPush.abnormalResult.enable}")
    private Boolean orderPushAbnormalResultEnable;

    @Resource
    private OrderPushService orderPushService;

    @Resource
    private LongTailPushService longTailPushService;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private OrderPushAbnormalResultRepository orderPushAbnormalResultRepository;

//
//    /**
//     * 推单（已废弃）
//     *
//     * @param orderPushRqt
//     * @return
//     */
//    @Tag(value = {ORDER_PUSH}, desc = "推单")
//    @Deprecated
//    public int orderPush(@Validated @MessageBody OrderPushRqt orderPushRqt, Message message) {
//        try{
//            log.info("receive orderPush message :{}",JSON.toJSONString(orderPushRqt));
//            orderPushService.orderPush(orderPushRqt);
//            return 1;
//        }catch(Exception e){
//            log.error(String.format("order push error ,orderPushRqt:%s",JSON.toJSONString(orderPushRqt)),e);
//            if(orderPushAbnormalResultEnable){
//                orderPushAbnormalResultRepository.saveAbnormalResult(orderPushRqt.getGlobalOrderId(),message.getMsgID(),
//                        orderPushTopic,ORDER_PUSH, JSON.toJSONString(orderPushRqt));
//            }
//
//        }
//        return 0;
//    }


    /**
     * 固定分轮推单
     *
     * @param delayPushRqt
     * @return
     */
    @Tag(value = {DELAY_PUSH}, desc = "固定分轮推单")
    public void delayPush(@Validated @MessageBody DelayPushRqt delayPushRqt,Message message) {
        try{
            log.info("receive delayPush message :{}",JSON.toJSONString(delayPushRqt));
            pushControllerFacade.delayPush(delayPushRqt);
        }catch(Exception e){
            log.error(String.format("order delayPush error ,delayPushRqt:%s",JSON.toJSONString(delayPushRqt)),e);
            if(orderPushAbnormalResultEnable){
                orderPushAbnormalResultRepository.saveAbnormalResult(delayPushRqt.getGlobalOrderId(),message.getMsgID(),
                        orderPushTopic,DELAY_PUSH, JSON.toJSONString(delayPushRqt));
            }
        }

    }


//    /**
//     * 重新推单（已废弃）
//     *
//     * @param repushOrderRqt
//     * @return
//     */
//    @Tag(value = {REPUSH_ORDER}, desc = "重新推单")
//    @Deprecated
//    public void repushOrder(@Validated @MessageBody RepushOrderRqt repushOrderRqt,Message message) {
//        try{
//            log.info("receive repushOrder message :{}",JSON.toJSONString(repushOrderRqt));
//            orderPushService.repushOrder(repushOrderRqt);
//        }catch(Exception e){
//            log.error(String.format("order repushOrder error ,repushOrderRqt:%s",JSON.toJSONString(repushOrderRqt)),e);
//            if(orderPushAbnormalResultEnable) {
//                orderPushAbnormalResultRepository.saveAbnormalResult(repushOrderRqt.getGlobalOrderId(),message.getMsgID(),
//                        orderPushTopic,REPUSH_ORDER, JSON.toJSONString(repushOrderRqt));
//            }
//
//        }
//
//    }


//    /**
//     * 长尾单（已废弃）
//     * @param nearbyPushRqt
//     * @param message
//     */
//    @Tag(value = {NEARBY_PUSH}, desc = "附近推单")
//    @Deprecated
//    public void nearbyPush(@Validated @MessageBody NearbyPushRqt nearbyPushRqt, Message message) {
//        try{
//            log.info("receive nearbyPushRqt message :{}",JSON.toJSONString(nearbyPushRqt));
////            orderPushService.nearbyPush(nearbyPushRqt);
//        }catch(Exception e){
//            log.error(String.format("order nearbyPush error ,nearbyPushRqt:%s",JSON.toJSONString(nearbyPushRqt)),e);
//            if(orderPushAbnormalResultEnable) {
//                orderPushAbnormalResultRepository.saveAbnormalResult(nearbyPushRqt.getOrderDetailData().getGlobalOrderId(),message.getMsgID(),
//                        orderPushTopic,NEARBY_PUSH, JSON.toJSONString(nearbyPushRqt));
//            }
//
//        }
//
//    }

    /**
     * 长尾单推送
     * @param longTailOrderPushRqt
     * @param message
     */
    @Tag(value = {LONG_TAIL_PUSH}, desc = "长尾单推送")
    public void longTailPush(@Validated @MessageBody LongTailOrderPushRqt longTailOrderPushRqt, Message message) {
        try{
            log.info("receive longTailPush message :{}",JSON.toJSONString(longTailOrderPushRqt));
            longTailPushService.longTailPush(longTailOrderPushRqt);
        }catch(Exception e){
            log.error(String.format("order longTailPush error ,longTailOrderPushRqt:%s",JSON.toJSONString(longTailOrderPushRqt)),e);
            if(orderPushAbnormalResultEnable) {
                orderPushAbnormalResultRepository.saveAbnormalResult(
                        longTailOrderPushRqt.getOrderDetailData().getGlobalOrderId(),message.getMsgID(),
                        orderPushTopic,
                        NEARBY_PUSH,
                        JSON.toJSONString(longTailOrderPushRqt));
            }

        }

    }

    @Tag(value = {INVITE_ORDER_PUSH}, desc = "总包自动邀约推单")
    public void inviteOrderPush(@Validated @MessageBody InviteOrderPushRqt inviteOrderPushRqt, Message message) {
        try{
            log.info("receive inviteOrderPushRqt message :{}",JSON.toJSONString(inviteOrderPushRqt));
            orderPushService.inviteOrderPush(inviteOrderPushRqt);
        }catch(Exception e){
            log.error(String.format("order inviteOrderPush error ,inviteOrderPushRqt:%s",JSON.toJSONString(inviteOrderPushRqt)),e);
            if(orderPushAbnormalResultEnable) {
                orderPushAbnormalResultRepository.saveAbnormalResult(inviteOrderPushRqt.getGlobalOrderTraceId(),message.getMsgID(),
                        orderPushTopic,INVITE_ORDER_PUSH, JSON.toJSONString(inviteOrderPushRqt));
            }

        }

    }

}
