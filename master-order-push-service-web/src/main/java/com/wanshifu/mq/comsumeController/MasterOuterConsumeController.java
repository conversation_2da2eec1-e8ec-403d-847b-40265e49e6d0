package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.dto.MasterSettleInRqt;
import com.wanshifu.master.order.push.domain.message.MasterAddFourthDivisionMessage;
import com.wanshifu.master.order.push.domain.message.MasterAddTechniqueMessage;
import com.wanshifu.master.order.push.domain.message.MasterRestStateMessage;
import com.wanshifu.master.order.push.service.MasterOperationNotifyService;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

@Component
@Topic("${wanshifu.rocketMQ.master-outer-topic}")
@Slf4j
public class MasterOuterConsumeController {

    @Resource
    private MasterOperationNotifyService masterOperationNotifyService;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    /**
     * 师傅更新休息状态
     * @param masterRestStateMessage
     * @return
     */
    private final String MASTER_REST_STATE = "master_rest_state";
    @Tag(value = {MASTER_REST_STATE}, desc = "师傅更新休息状态")
    public int masterRestState(@Validated @MessageBody MasterRestStateMessage masterRestStateMessage, Message message) {
        try{
            log.info("receive masterRestState message :{}", JSON.toJSONString(masterRestStateMessage));
            masterOperationNotifyService.masterRestState(masterRestStateMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅更新休息状态mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    MASTER_REST_STATE, JSON.toJSONString(masterRestStateMessage), e));
            log.error(String.format("masterRestState error,MasterRestStateMessage:%s,{}",JSON.toJSONString(masterRestStateMessage)),e);
            throw e;
        }
    }



    /**
     * 师傅新增技能
     * @param masterAddTechniqueMessage
     * @return
     */

    private final String ADD_TECHNIQUE = "add_technique";

    @Tag(value = {ADD_TECHNIQUE}, desc = "师傅新增技能")
    public int addTechnique(@Validated @MessageBody MasterAddTechniqueMessage masterAddTechniqueMessage, Message message) {
        try{
            log.info("receive addTechnique message :{}", JSON.toJSONString(masterAddTechniqueMessage));
            masterOperationNotifyService.addTechnique(masterAddTechniqueMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅新增技能mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ADD_TECHNIQUE, JSON.toJSONString(masterAddTechniqueMessage), e));
            log.error(String.format("addTechnique error,MasterAddTechniqueMessage:%s,{}",JSON.toJSONString(masterAddTechniqueMessage)),e);
            throw e;
        }
    }


    /**
     * 师傅增加服务区域
     * @param masterAddFourthDivisionMessage
     * @return
     */
    private final String ADD_SERVE_FOURTH_DIVISION = "add_serve_fourth_division";

    @Tag(value = {ADD_SERVE_FOURTH_DIVISION}, desc = "师傅增加服务区域")
    public int addServeFourthDivision(@Validated @MessageBody MasterAddFourthDivisionMessage masterAddFourthDivisionMessage, Message message) {
        try{
            log.info("receive addServeFourthDivision message :{}", JSON.toJSONString(masterAddFourthDivisionMessage));
            masterOperationNotifyService.addServeFourthDivision(masterAddFourthDivisionMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费师傅增加服务区域mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ADD_TECHNIQUE, JSON.toJSONString(masterAddFourthDivisionMessage), e));
            log.error(String.format("addServeFourthDivision error,MasterAddFourthDivisionMessage:%s,{}",JSON.toJSONString(masterAddFourthDivisionMessage)),e);
            throw e;
        }
    }



}
