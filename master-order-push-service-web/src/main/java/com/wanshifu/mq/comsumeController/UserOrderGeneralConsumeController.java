package com.wanshifu.mq.comsumeController;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.master.order.push.domain.message.OrderChangeGrabPushMessage;
import com.wanshifu.master.order.push.service.OrderMatchMasterService;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

@Component
@Topic("${wanshifu.rocketMQ.user-order-general-topic}")
@Slf4j
public class UserOrderGeneralConsumeController {

    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    @Resource
    private OrderMatchMasterService orderMatchMasterService;


    /**
     * 订单改派推送金牌维修师傅
     * @param masterRestStateMessage
     * @return
     */
    private final String ORDER_CHANGE_GRAB_PUSH = "order_change_grab_push";
    @Tag(value = {ORDER_CHANGE_GRAB_PUSH}, desc = "订单改派推送金牌维修师傅")
    public int orderChangeGrabPush(@Validated @MessageBody OrderChangeGrabPushMessage orderChangeGrabPushMessage, Message message) {
        try{
            log.info("receive orderChangeGrabPush message :{}", JSON.toJSONString(orderChangeGrabPushMessage));
            orderMatchMasterService.orderChangeGrabPush(orderChangeGrabPushMessage);
            return 1;
        }catch(Exception e){
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"消费订单改派推送金牌维修师傅mq异常", String.format("【tag】：%s\n\n【msgBody】%s\n\n【错误原因】：%s",
                    ORDER_CHANGE_GRAB_PUSH, JSON.toJSONString(orderChangeGrabPushMessage), e));
            log.error(String.format("orderChangeGrabPush error,OrderChangeGrabPushMessage:%s,{}",JSON.toJSONString(orderChangeGrabPushMessage)),e);
            throw e;
        }
    }


}
