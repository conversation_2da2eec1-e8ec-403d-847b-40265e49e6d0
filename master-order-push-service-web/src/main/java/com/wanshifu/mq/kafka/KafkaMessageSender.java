package com.wanshifu.mq.kafka;

import com.wanshifu.util.ExceptionInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;
/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/6 15:55
 * @Version
 */
@Component
@Slf4j
public class KafkaMessageSender {

    private static final Logger logger = LoggerFactory.getLogger(KafkaMessageSender.class);


    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;

    @Async
    public void sendMessageAsync(String topic,String eventName, String message) {
        String key = UUID.randomUUID().toString();
        ProducerRecord<String, String> kafkaMessage = new ProducerRecord<String, String>(topic, key, message);
        log.info(String.format("kafka sendMessageAsync,message:%s",message));
        kafkaProducer.send(kafkaMessage, (recordMetadata, exception) -> {
            // 如果消息成功写入Kafka/则返回RecordMetaData对象
            if (exception != null) {
                String failMess = ExceptionInfoUtil.getExceptionAllinformation(exception);
                logger.error("kafka send message fail,failMess:[{}],topic:[{}],message:[{}]", failMess,topic,message);
            }else {
                logger.info("kafka send message successful,eventName:[{}],topic:[{}],message:[{}]",eventName,topic,message);
            }
        });
    }
}
