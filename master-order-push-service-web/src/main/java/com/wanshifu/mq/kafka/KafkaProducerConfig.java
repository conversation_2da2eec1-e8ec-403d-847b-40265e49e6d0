package com.wanshifu.mq.kafka;


import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


import java.util.Properties;


/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/8 10:49
 * @Version
 */

@Configuration
public class KafkaProducerConfig {
    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerConfig.class);

    @Value("${kafka.producer.ack}")
    private String ACKS_CONFIG;

    @Value("${kafka.producer.retries}")
    private Integer RETRIES_CONFIG;

    @Value("${kafka.producer.retry.backoff.ms}")
    private Integer RETRY_BACKOFF_MS_CONFIG;

    @Value("${kafka.producer.max.block.ms}")
    private Integer MAX_BLOCK_MS_CONFIG;

    @Value("${kafka.producer.bootstrap.servers}")
    private String BOOTSTRAP_SERVERS_CONFIG;

    @Value("${kafka.producer.serializer.class}")
    private String SERIALIZER_CLASS_CONFIG;

    @Value("${kafka.producer.batch.size}")
    private Integer BATCH_SIZE;

    @Value("${kafka.producer.receive.buffer.bytes}")
    private Integer RECEIVE_BUFFER_BYTES;

    @Value("${kafka.producer.send.buffer.bytes}")
    private Integer SEND_BUFFER_BYTES;

    @Value("${kafka.producer.buffer.memory}")
    private Integer BUFFER_MEMORY;

    @Bean("kafkaProducer")
    public KafkaProducer<String, String> kafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.ACKS_CONFIG, ACKS_CONFIG);
        props.put(ProducerConfig.RETRIES_CONFIG, RETRIES_CONFIG);
        props.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, RETRY_BACKOFF_MS_CONFIG);
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, MAX_BLOCK_MS_CONFIG);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, SERIALIZER_CLASS_CONFIG);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, SERIALIZER_CLASS_CONFIG);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS_CONFIG);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, BATCH_SIZE);
        props.put(ProducerConfig.RECEIVE_BUFFER_CONFIG, RECEIVE_BUFFER_BYTES);
        props.put(ProducerConfig.SEND_BUFFER_CONFIG, SEND_BUFFER_BYTES);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, BUFFER_MEMORY);
        KafkaProducer<String, String> producer = new KafkaProducer<String, String>(props);
        logger.info("buildProducer  ---------------------------- 生产开启构建成功");
        return producer;
    }
}
