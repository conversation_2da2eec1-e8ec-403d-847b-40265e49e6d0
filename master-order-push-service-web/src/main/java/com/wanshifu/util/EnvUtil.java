package com.wanshifu.util;

import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.framework.utils.TransmittableContext;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @history 2023/7/3 新建
 * @since JDK1.8
 */
@Component
public class EnvUtil implements EnvironmentAware {

    private static final String ENABLE_ACCELERATED_FOR_START = "wanshifu.devModel.enableAcceleratedStarted";
    private static Environment environment;

    public static final String PROFILE_DEV = "dev";
    public static final String PROFILE_TEST = "test";
    public static final String PROFILE_RELEASE = "release";
    public static final String PROFILE_PROD = "prod";

    /**
     * 是否开启本地加速启动模式
     * 本地配置 vm参数即可生效
     */
    public static boolean openAccelerated4Start() {
        String enable = System.getProperty(ENABLE_ACCELERATED_FOR_START);
        if (Objects.isNull(enable)) {
            enable = System.getenv().get(ENABLE_ACCELERATED_FOR_START);
        }

        return Objects.equals(enable, "true");
    }



    public static String getIterativeEnvName(){
        String envName = "";
        TransmittableContext.Tenant tenant = TransmittableContext.context.get();
        if (Objects.nonNull(tenant) && StringUtils.isNotEmpty(tenant.getEnvironmentTag())) {
            envName = tenant.getEnvironmentTag();
        }
        return StringUtils.isEmpty(envName) ? getProfileName(): envName;
    }


    public static String getProfileName() {
        if (isReleaseProfile()){
            return PROFILE_RELEASE;
        }
        return getProfile();
    }

    public static String getEnvName(){
        String iterativeEnvName = getIterativeEnvName();
        if (iterativeEnvName.equals("prod")) {
            return "线上环境";
        }
        if (iterativeEnvName.equals(PROFILE_RELEASE)){
            return "灰度环境";
        }
        return iterativeEnvName;
    }


    /**
     * 是否是灰度环境
     * @return true or false
     */
    public static boolean isReleaseProfile() {
        return PROFILE_RELEASE.equals(getSystemProfile());
    }

    /**
     * 是否是生产环境
     * @return true or false
     */
    public static boolean isProdProfile() {
        return PROFILE_PROD.equals(getSystemProfile());
    }

    public static String getProfile() {
        return getProperty("spring.profiles.active");
    }

    public static String getProperty(String key) {
        return environment.getProperty(key);
    }


    public static String getSystemProfile() {
        return getProperty("env_tag");
    }

    public static String getAppName(){
        String property = getProperty("spring.application.name");
        return Objects.nonNull(property) ? property : "";
    }
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

}
