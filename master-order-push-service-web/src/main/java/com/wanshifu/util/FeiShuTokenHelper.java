package com.wanshifu.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/11 17:03
 */
@Component
@Slf4j
public class FeiShuTokenHelper {

    /**
     * 飞书预警机器人-推单系统后台配置规则变动飞书告警群
     */
    public static String FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL;
    @Value("${feiShu-talk.client.push.config.update.notice.url:}")
    public void setFeiShuTalkPushConfigUpdateNoticeUrl(String feiShuTalkPushConfigUpdateNoticeUrl) {
        FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL = feiShuTalkPushConfigUpdateNoticeUrl;
    }
}
