package com.wanshifu.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;

import java.util.*;

/**
 * <AUTHOR>
 */
public class LoCollectionsUtil {


    public static void putJsonArray(JSONObject jsonObject,
                                    String key,String value){
        final JSONArray jsonArray = jsonObject.getJSONArray(key);
        if (jsonArray==null) {
            final JSONArray objects = new JSONArray();
            objects.add(value);
            jsonObject.put(key,objects);
        }else {
            jsonArray.add(value);
        }
    }


    public enum Method {MIN,MAX};
    public static String collectionCompare(Collection col,Method method){
        String result=null;
        for (Object o : col) {
            if (result==null) {
                result=o.toString();
                continue;
            }
            if (method==Method.MAX){
                result=max(result,o).toString();
            }else if(method==Method.MIN){
                result=min(result,o).toString();
            }else {
            }
        }
        return result;
    }


    private static Object min(Object o1,Object o2){
        final String o1string = o1.toString();
        final String o2string = o2.toString();
        try {
            final Double o1Double = Double.valueOf(o1string);
            final Double o2Double = Double.valueOf(o2string);
            if (o1Double>o2Double) {
                return o2Double;
            }else {
                return o1Double;
            }
        }catch (Exception e){
        }
        if (o1string.compareTo(o2string)>1) {
            return o2string;
        }else {
            return o1string;
        }
    }

    private static Object max(Object o1,Object o2){

        final String o1string = o1.toString();
        final String o2string = o2.toString();
        try {
            final Double o1Double = Double.valueOf(o1string);
            final Double o2Double = Double.valueOf(o2string);
            if (o1Double>o2Double) {
                return o1Double;
            }else {
                return o2Double;
            }
        }catch (Exception e){
        }
        if (o1string.compareTo(o2string)>1) {
            return o1string;
        }else {
            return o2string;
        }
    }


    public static void putToMapSet(
            Map<String, Map<String, Object>> multiValueMap,
            String key1,String key2,String value
                                   ){
        if (multiValueMap==null) {
            return;
        }
        Map<String, Object> setHashMap = multiValueMap.get(key1);
        if (setHashMap==null) {
            setHashMap=new HashMap<>(50);
            multiValueMap.put(key1,setHashMap);
            putToSet(setHashMap,key2,value);
        }else {
            putToSet(setHashMap,key2,value);
        }
    }

    public static void putToMapValue(
            DefaultContext<String, DefaultContext<String, Object>> multiValueMap,
            String key1,String key2,Object value
    ){
        if (multiValueMap==null) {
            return;
        }
        DefaultContext<String, Object> currentMap = multiValueMap.get(key1);
        if (currentMap==null) {
            currentMap=new DefaultContext();
            currentMap.put(key2,value);
            multiValueMap.put(key1,currentMap);
        }else {
            currentMap.put(key2,value);
        }
    }

    private static void putToSet(Map<String, Object> setHashMap,String key,String value){
        if (setHashMap==null) {
            return;
        }
        Object strings = setHashMap.get(key);
        if (strings==null) {
            HashSet<String> newSet=new HashSet<>();
            newSet.add(value);
            setHashMap.put(key,newSet);
        }else {
            HashSet<String> newSet=(HashSet<String>)strings;
            newSet.add(value);
        }
    }


    public static <T,P>void putToSet(Map<T, Set<P>> setHashMap,T key,P value){
        if (setHashMap==null) {
            return;
        }
        Set<P> localSet = setHashMap.get(key);
        if (localSet==null) {
            HashSet<P> newSet=new HashSet<>();
            newSet.add(value);
            setHashMap.put(key,newSet);
        }else {
            localSet.add(value);
        }
    }

    public static Collection<String> stringToSet(String source){
        return Arrays.asList(source.split(SymbolConstant.COMMA));
    }

    public static <K, A, B> void putMapToMap(
            Map<K, Map<A, B>> dataCollection,
            K key,
            Map<A, B> otherMap) {
        if (dataCollection == null) {
            return;
        }
        Map<A, B> valueList = dataCollection.get(key);
        if (valueList == null) {
            valueList = new HashMap<A, B>(50);
            valueList.putAll(otherMap);
            dataCollection.put(key, valueList);
        } else {
            valueList.putAll(otherMap);
        }
    }

    public static <K, A, B> void putMapToMap(
            DefaultContext<K, DefaultContext<A, B>> dataCollection,
            K key,
            DefaultContext<A, B> otherMap) {
        if (dataCollection == null) {
            return;
        }
        DefaultContext<A, B> valueList = dataCollection.get(key);
        if (valueList == null) {
            valueList = new DefaultContext<A, B>();
            valueList.putAll(otherMap);
            dataCollection.put(key, valueList);
        } else {
            valueList.putAll(otherMap);
        }
    }

}
