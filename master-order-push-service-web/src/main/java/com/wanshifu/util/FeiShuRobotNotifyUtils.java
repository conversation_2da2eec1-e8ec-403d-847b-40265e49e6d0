package com.wanshifu.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.codec.binary.Base64;
import com.wanshifu.framework.utils.TransmittableContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 飞书机器人通知
 * <br/>
 * 文档：https://open.feishu.cn/document/ukTMukTMukTM/ucTM5YjL3ETO24yNxkjN
 */
@Slf4j
@Component
public class FeiShuRobotNotifyUtils {

//    /**
//     * 飞书群机器人签名
//     */
//    @Value("${fei_shu_robot_notify_sign}")
//    private String feiShuRobotNotifySign;
//
//    /**
//     * 飞书通知群签名
//     */
//    @Value("${fei_shu_robot_notify_url}")
//    private String feiShuRobotNotifyUrl;



//    /**
//     * 飞书群机器人签名
//     */
//    @Value("${buss_fei_shu_robot_notify_sign}")
//    private String bussFeiShuRobotNotifySign;
//
//    /**
//     * 飞书通知群签名
//     */
//    @Value("${buss_fei_shu_robot_notify_url}")
//    private String bussFeiShuRobotNotifyUrl;


    @Value("${env_tag:online}")
    private String env;


    /**
     * 发送富文本通知
     *
     * @param title
     * @param content
     */
    @Async
    public void sendRichTextNotice(String feiShuRobotNotifyUrl,String feiShuRobotNotifySign, String title, String content) {

        //过滤压测流量
        if (Arrays.asList("prod", "release").contains(env) && this.isPressSceneFlow()) {
            return;
        }
        //添加TraceId &环境 & 项目
        String traceId = MDC.get(Span.TRACE_ID_NAME);
        content = content + "\n\n【链路TraceId】：" + traceId + "\n\n【环境】：" + env + "\n\n【项目】：" + "master-order-push-service";
        //组装内容参数
        String postContent = this.buildBodyContentparams(feiShuRobotNotifySign,title, content);
        try {
            //post请求
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, postContent);
            Request request = new Request.Builder()
                    .url(feiShuRobotNotifyUrl)
                    .method("POST", body)
                    .build();
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (IOException e) {
            log.error("=========sendRichTextNoticeError===========", e);
        }
    }

    /**
     * 构建主要内容
     * 示例：{"msg_type":"post","content":{"post":{"zh_cn":{"title":"项目更新通知","content":[[{"tag":"text","text":"项目有更新: "},
     * {"tag":"a","text":"请查看","href":"http://www.example.com/"},{"tag":"at","user_id":"ou_18eac8********17ad4f02e8bbbb"}]]}}}}
     *
     * @param content
     * @param title
     * @return
     */
    private String buildBodyContentparams(String feiShuRobotNotifySign,String title, String content) {
        //subContentVo
        BodyContentVo.ContentVo.PostVo.ZhCnVo.SubContentVo subContentVo = new BodyContentVo.ContentVo.PostVo.ZhCnVo.SubContentVo();
        subContentVo.setText(content);
        subContentVo.setTag("text");
        //zhCnVo
        BodyContentVo.ContentVo.PostVo.ZhCnVo zhCnVo = new BodyContentVo.ContentVo.PostVo.ZhCnVo();
        zhCnVo.setTitle(title);
        List<List<BodyContentVo.ContentVo.PostVo.ZhCnVo.SubContentVo>> subContentVoList = new ArrayList<>();
        subContentVoList.add(Collections.singletonList(subContentVo));
        zhCnVo.setContent(subContentVoList);
        //postVo
        BodyContentVo.ContentVo.PostVo postVo = new BodyContentVo.ContentVo.PostVo();
        postVo.setZh_cn(zhCnVo);
        //contentVo
        BodyContentVo.ContentVo contentVo = new BodyContentVo.ContentVo();
        contentVo.setPost(postVo);
        //组装参数
        long timestamp = System.currentTimeMillis() / 1000L;
        BodyContentVo bodyContentVo = new BodyContentVo();
        bodyContentVo.setMsg_type("post");
        bodyContentVo.setContent(contentVo);
        bodyContentVo.setSign(this.generateSign(feiShuRobotNotifySign,timestamp));
        bodyContentVo.setTimestamp(String.valueOf(timestamp));
        return JSON.toJSONString(bodyContentVo);
    }


    /**
     * 生成签名
     *
     * @param timestamp 时间戳（秒）
     * @returnfeiShuRobotNotifySign
     *
     */
    private String generateSign(String feiShuRobotNotifySign,long timestamp) {
        try {
            //把timestamp+"\n"+密钥当做签名字符串
            String stringToSign = timestamp + "\n" + feiShuRobotNotifySign;

            //使用HmacSHA256算法计算签名
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(new byte[]{});
            return new String(Base64.encodeBase64(signData));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            return null;
        }
    }

    /***
     * 是否为压测流量日志
     * @return true：压测流量
     */
    private boolean isPressSceneFlow() {
        TransmittableContext.Tenant tenant = TransmittableContext.context.get();
        return tenant != null && tenant.isPtScene();
    }

    @Data
    private static class BodyContentVo {
        private String timestamp;
        private String sign;
        private String msg_type;
        private ContentVo content;

        @Data
        private static class ContentVo {
            private PostVo post;

            @Data
            private static class PostVo {
                private ZhCnVo zh_cn;

                @Data
                private static class ZhCnVo {
                    private String title;
                    private List<List<SubContentVo>> content;

                    @Data
                    private static class SubContentVo {
                        private String tag;
                        private String text;
                    }
                }
            }
        }
    }

}
