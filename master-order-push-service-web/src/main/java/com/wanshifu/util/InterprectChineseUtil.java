package com.wanshifu.util;

import com.google.common.collect.Lists;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.enums.TranslateEnum;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 17:24
 */
public class InterprectChineseUtil<T extends Serializable> {

    /**
     * 通过注解将传入的实体翻译成中文的说明 实体中对应注解@TranslateEnum
     * @param t 需要将枚举翻译成中文说明的实体
     */
    public static void reflexEnum(Object  t) {
        Class<?> clas = t.getClass();
        for (Field f : t.getClass().getDeclaredFields()) {
            Object value = null;
            try {
                //对于private的属性，如果不解锁，那么将无法访问，会报错
                f.setAccessible(true);
                //通过反射的字段获取实体中的值
                value = f.get(t);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            //获取注解的信息
            TranslateEnum anno2 = f.getAnnotation(TranslateEnum.class);
            if(anno2!=null&&value!=null) {
                //注解的class
                Class<?> aClass = anno2.enumClass();
                //注解的fieldName
                String fieldName = anno2.fieldName();
                //通过value过去枚举中对应的中文说明
                String enumValues = InterprectChineseUtil.enumExplain(aClass, value.toString());

                try {
                    //通过注解中写的fieldName，给需要赋值的字段赋值
                    Field fv  = null;
                    if(!"".equals(fieldName)) {
                        fv = clas.getDeclaredField(fieldName);
                    }
                    else {
                        fv = f;
                    }
                    //对于private的属性，如果不解锁，那么将无法访问，会报错
                    fv.setAccessible(true);
                    fv.set(t,enumValues);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 通过枚举跟值获取对应的中文说明
     *
     * @param clazz 枚举的class
     * @param code  对应的枚举值,可以为多个值，使用,拼接
     * @return 翻译后值
     */
    public static String enumExplain(Class<?> clazz, String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        try {
            List<Map<String, Object>> list = EnumUtil.toMapList(clazz);
            List<String> enumStr = Lists.newArrayList();
            Arrays.stream(code.split(",")).forEach(it -> {
                Map<String, Object> m = list.stream().filter(map -> map.get("code").toString().equals(it)).findFirst().orElse(null);
                boolean desc = (m == null) ? enumStr.add(it) : enumStr.add(m.get("desc").toString());
            });
            return StringUtils.join(enumStr, ",");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}