package com.wanshifu.util;

import com.wanshifu.framework.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * description: 字符串转换
 * Author: <EMAIL>
 * CreateDate: 2019/7/23 14:27
 * Version: 1.0
 **/
public class StringEnhanceUtils extends StringUtils {

    /**
     * 判断字符串是否为空
     *
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        if (str == null || str.trim().equals("") || str.trim().equalsIgnoreCase("null")
                || str.trim().equalsIgnoreCase("NULL")) {
            return true;
        } else {
            return false;
        }
    }

    /***
     * 将下划线参数转化为驼峰参数
     * @param colName
     * @return
     */
    public static String toFormatCol(String colName) {
        StringBuilder sb = new StringBuilder();
        String[] str = colName.toLowerCase().split("_");
        int i = 0;
        for (String s : str) {
            if (s.length() == 1) {
                s = s.toUpperCase();
            }
            i++;
            if (i == 1) {
                sb.append(s);
                continue;
            }
            if (s.length() > 0) {
                sb.append(s.substring(0, 1).toUpperCase());
                sb.append(s.substring(1));
            }
        }
        return sb.toString();
    }

    /**
     * 过滤emoji 或者 其他非文字类型的字符
     *
     * @param source
     * @return
     */
    public static String filterEmoji(String source) {

        if (StringUtils.isEmpty(source)) {
            return "";
        }
        if (!containsEmoji(source)) {
            return source;//如果不包含，直接返回
        }
        StringBuilder buf = new StringBuilder();
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (isNotEmojiCharacter(codePoint)) {
                buf.append(codePoint);
            }
        }

        return buf.toString().trim();
    }


    /**
     * 检测是否有emoji字符
     *
     * @param source
     * @return 一旦含有就抛出
     */
    public static boolean containsEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为非Emoji字符
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    private static boolean isNotEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) ||
                (codePoint == 0x9) ||
                (codePoint == 0xA) ||
                (codePoint == 0xD) ||
                ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
                ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
    }


    //首字母大写
    public static String captureName(String name) {
        char[] cs = name.toCharArray();
        cs[0] -= 32;
        return String.valueOf(cs);

    }


    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }

    // 完整的判断中文汉字和符号
    public static boolean isChinese(String strName) {
        char[] ch = strName.toCharArray();
        for (int i = 0; i < ch.length; i++) {
            char c = ch[i];
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 字符串截取，中英文混合截取问题，为中文是截取算两个字符，英文数字算一个字符
     *
     * @param str
     * @param start
     * @param length
     * @return
     */
    public static String subMixStr(String str, int start, int length) {

        int currentLength = 0;
        StringBuilder returnStr = new StringBuilder();
        int len = str.length();
        for (int i = start; i < len; i++) {
            char c = str.charAt(i);

            if (Character.isDigit(c)) {
                currentLength++;
                returnStr.append(c);
            }
            if (Character.isLetter(c)) {
                currentLength++;
                returnStr.append(c);
            }

            if (StringUtil.isChinese(c)) {
                length--;
                currentLength++;
                returnStr.append(c);
            }

            if (currentLength >= (length - 1) || currentLength >= len) {
                break;
            }

        }
        return returnStr.toString();
    }

    /**
     * 清除字符串空格
     *
     * @param str
     * @return
     */
    public static String removeBlanks(String str) {
        return str == null || str == "" ? "" : str.replaceAll("\\s", "");
    }

    /**
     * 字符串转list集合
     *
     * @param selectTechnologyIds
     * @return
     */
    public static List<String> strToStringList(String selectTechnologyIds) {
        List<String> list = new ArrayList<>();
        if (StringUtil.isEmpty(selectTechnologyIds)) {
            return list;
        }
        String[] strings = selectTechnologyIds.split(",");
        for (String info : strings) {
            if (StringUtils.isNotEmpty(info)) {
                list.add(info);
            }
        }
        return list;
    }


    /**
     * 字符串 =>BigDecimal 并保留两位小数
     *
     * @param str
     * @param roundingMode
     * @return roundingMode值如下：
     * #ROUND_UP
     * #ROUND_DOWN
     * #ROUND_CEILING
     * #ROUND_FLOOR
     * #ROUND_HALF_UP
     * #ROUND_HALF_DOWN
     * #ROUND_HALF_EVEN
     * #ROUND_UNNECESSARY
     */
    public static BigDecimal strToBigDecimal(String str, int roundingMode) {
        if (StringUtils.isEmpty(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(str).setScale(2, roundingMode);
    }

    /**
     * 获取数字
     *
     * @param str
     * @return
     */
    public static String getNumeric(String str) {
        str = str.trim();
        StringBuilder str2 = new StringBuilder();
        if (!"".equals(str)) {
            for (int i = 0; i < str.length(); i++) {
                if (str.charAt(i) >= 48 && str.charAt(i) <= 57) {
                    str2.append(str.charAt(i));
                }
            }
        }
        return str2.toString();
    }
}
