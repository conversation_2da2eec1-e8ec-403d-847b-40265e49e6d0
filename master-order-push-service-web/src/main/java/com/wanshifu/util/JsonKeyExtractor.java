package com.wanshifu.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.stream.Collectors;

public class JsonKeyExtractor {

    /**
     * 从 JSON 字符串中提取所有顶层对象的 key（去重）
     * 示例输入: [{"3292":"限制推单师傅"},{"3291":"师傅10001"}]
     */
    public static Set<String> extractKeys(String jsonArrayStr) {
        if (jsonArrayStr == null || jsonArrayStr.trim().isEmpty()) {
            return Collections.emptySet();
        }

        JSONArray arr = JSON.parseArray(jsonArrayStr.trim());
        if (arr == null || arr.isEmpty()) {
            return Collections.emptySet();
        }

        return arr.stream()
                .map(obj -> (JSONObject) obj)
                .flatMap(jsonObj -> jsonObj.keySet().stream())
                .collect(Collectors.toSet());
    }

}
