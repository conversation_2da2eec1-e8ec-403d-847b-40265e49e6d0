package com.wanshifu.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtil {

    /**
     * @param bigDecimal
     * @param scale
     * @param roundMode
     * @return
     */
    public static BigDecimal reserveDecimalPlacesByScale(BigDecimal bigDecimal, int scale, RoundingMode roundMode) {
        return bigDecimal.setScale(scale, roundMode);
    }

    /**
     * 保留2位小数
     *
     * @param bigDecimal
     * @return
     */
    public static BigDecimal reserveTowDecimalPlaces(BigDecimal bigDecimal, RoundingMode roundMode) {
        return bigDecimal.setScale(2, roundMode);
    }

    /**
     * 保留2位小数
     *
     * @param number
     * @return
     */
    public static BigDecimal reserveTowDecimalPlaces(String number, RoundingMode roundMode) {
        BigDecimal bigDecimal = new BigDecimal(number);
        return bigDecimal.setScale(2, roundMode);
    }


    /**
     * 判断BigDecimal不为空
     *
     * @param b
     * @return
     */
    public static boolean notEmpty(BigDecimal b) {
        return b != null && !b.equals(BigDecimal.ZERO);
    }

    /**
     * 判断BigDecimal为空
     *
     * @param b
     * @return
     */
    public static boolean isEmpty(BigDecimal b) {
        return b == null || b.equals(BigDecimal.ZERO);
    }

    /**
     * 判断A大于B
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean isGreaterThan(BigDecimal a, BigDecimal b) {
        if (BigDecimalUtil.isEmpty(a)) {
            return false;
        }
        return a.compareTo(b) > 0;
    }

    public static void main(String[] args) {
        BigDecimal a = new BigDecimal(22.6072256);
        BigDecimal b = null;
        System.out.println(notEmpty(a));
        System.out.println(isEmpty(b));


    }

}
