package com.wanshifu.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * description: Set集合转换
 * Author: <EMAIL>
 * CreateDate: 2019/7/23 14:27
 * Version: 1.0
 **/
public class SetUtil {

    /***
     * 整理set集合
     * @param set
     * @return
     */
    public static Set<String> changeSet(Set<Set<Long>> set) {
        Set<String> result = new HashSet<>();
        for (Set<Long> str : set) {
            for (Long info : str) {
                result.add(String.valueOf(info));
            }
        }
        return result;
    }

    /***
     * 整理set集合
     * @param set
     * @return
     */
    public static Set<List<Long>> setToLongSet(Set<Set<Long>> set) {
        Set<List<Long>> result = new HashSet<>();
        for (Set<Long> str : set) {
            result.add(new ArrayList<>(str));
        }
        return result;
    }

    /***
     * set集合转字符串
     * @param set
     * @return
     */
    public static String setToStr(Set<Long> set) {
        if(set==null){
            return "";
        }
        Set<String> result = new HashSet<>();
        for (Long along : set) {
            result.add(String.valueOf(along));
        }
        return String.join(",",result);
    }
}
