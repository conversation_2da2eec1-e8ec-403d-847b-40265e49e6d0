package com.wanshifu.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.HttpBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/11 17:03
 */
@Slf4j
public class FeiShuUtil {


    /**
     * 发送消息
     * 成功报文: {"StatusCode": 0,"StatusMessage": "success"}
     *
     * @param webhookUrl 飞书群 url
     * @param text       消息内容
     * @author: <PERSON>
     * @date: 2022/9/6
     */
    public static boolean sendMsg(String webhookUrl, String text) {
        String res = "";
        String requestData = "";
        try {

            HashMap<String,Object> content = new HashMap();
            content.put("text", text);
            HashMap<String,Object> data = new HashMap();
            data.put("msg_type", "text");
            data.put("content", content);

            requestData = JSON.toJSONString(data);
            res = HttpBuilder.genericPost(webhookUrl)
                    .connectTimeOut(500)
                    .socketTimeout(500)
                    .entity(new StringEntity(requestData, ContentType.APPLICATION_JSON))
                    .build().executeToString();
            JSONObject jsonObject = JSON.parseObject(res);

            String statusCode = jsonObject.getString("StatusCode");
            if(StringUtils.isBlank(statusCode) || !statusCode.equals("0")){
                log.error("飞书预警请求参数: {} , 响应内容: {}, webhookUrl:{}", requestData, res, webhookUrl);
            }
            return true;
        } catch (Exception e) {
            log.error("飞书预警失败: req: {}, res: {} , webhookUrl:{} ", requestData, res, webhookUrl , e);
        }
        return false;
    }
}
