package com.wanshifu.util;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/23 16:24
 * @Version
 */
public class ExceptionInfoUtil {
   /**
    * 将异常信息的StackTrace拼成字符串，用在log打印
    * 
    * @param ex 异常
    * @return 带有换行的字符串
    */
   public static String getExceptionAllinformation(Exception ex) {
      if (ex == null) {
         return null;
      }
      String sOut = ex.getMessage();
      StackTraceElement[] trace = ex.getStackTrace();
      for (StackTraceElement s : trace) {
         sOut += "\tat " + s + "\r\n";
      }
      return sOut;
   }

   /**
    * 将异常信息的StackTrace拼成字符串，用在log打印
    * 
    * @param exceptionInfoList 异常
    * @return 带有换行的字符串
    */
   public static String combineExceptionInfo(List<String> exceptionInfoList) {
      String sOut = "";
      for (int i =0; i < exceptionInfoList.size(); i++) {
         sOut += "#####  Exception-" + (i + 1) + "  #####";
         sOut += "\r\n " + exceptionInfoList.get(i);
      }
      return sOut;
   }
}