package com.wanshifu.util;

import com.wanshifu.framework.utils.StringUtils;
import org.apache.commons.beanutils.ConvertUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * description: 字符串转换
 * Author: <EMAIL>
 * CreateDate: 2019/7/23 14:27
 * Version: 1.0
 **/
public class StringUtil {

    /**
     * 判断字符串是否为空
     *
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        if (str == null || str.trim().equals("") || str.trim().equalsIgnoreCase("null")
                || str.trim().equalsIgnoreCase("NULL")) {
            return true;
        } else {
            return false;
        }
    }

    /***
     * 将下划线参数转化为驼峰参数
     * @param colName
     * @return
     */
    public static String toFormatCol(String colName) {
        StringBuilder sb = new StringBuilder();
        String[] str = colName.toLowerCase().split("_");
        int i = 0;
        for (String s : str) {
            if (s.length() == 1) {
                s = s.toUpperCase();
            }
            i++;
            if (i == 1) {
                sb.append(s);
                continue;
            }
            if (s.length() > 0) {
                sb.append(s.substring(0, 1).toUpperCase());
                sb.append(s.substring(1));
            }
        }
        return sb.toString();
    }

    /**
     * 过滤emoji 或者 其他非文字类型的字符
     *
     * @param source
     * @return
     */
    public static String filterEmoji(String source) {

        if (StringUtils.isEmpty(source)) {
            return "";
        }
        if (!containsEmoji(source)) {
            return source;//如果不包含，直接返回
        }
        StringBuilder buf = new StringBuilder();
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (isNotEmojiCharacter(codePoint)) {
                buf.append(codePoint);
            }
        }

        return buf.toString().trim();
    }


    /**
     * 检测是否有emoji字符
     *
     * @param source
     * @return 一旦含有就抛出
     */
    public static boolean containsEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为非Emoji字符
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    private static boolean isNotEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) ||
                (codePoint == 0x9) ||
                (codePoint == 0xA) ||
                (codePoint == 0xD) ||
                ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
                ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
    }


    //首字母大写
    public static String captureName(String name) {
        char[] cs=name.toCharArray();
        cs[0]-=32;
        return String.valueOf(cs);

    }


    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }

    // 完整的判断中文汉字和符号
    public static boolean isChinese(String strName) {
        char[] ch = strName.toCharArray();
        for (int i = 0; i < ch.length; i++) {
            char c = ch[i];
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 字符串截取，中英文混合截取问题，为中文是截取算两个字符，英文数字算一个字符
     * @param str
     * @param start
     * @param length
     * @return
     */
    public static String subMixStr(String str, int start, int length) {

        int currentLength = 0;
        StringBuilder returnStr = new StringBuilder();
        int len = str.length();
        for (int i = start; i < len; i++) {
            char c = str.charAt(i);

            if(Character.isDigit(c)) {
                currentLength ++;
                returnStr.append(c);
            }
            if(Character.isLetter(c)) {
                currentLength ++;
                returnStr.append(c);
            }

            if(StringUtil.isChinese(c)) {
                length -- ;
                currentLength ++;
                returnStr.append(c);
            }

            if(currentLength >= (length - 1) || currentLength >= len){
                break;
            }

        }
        return returnStr.toString();
    }

    /**
     * 清除字符串空格
     * @param str
     * @return
     */
    public static String removeBlanks(String str){
        return str == null || str == "" ? "" : str.replaceAll("\\s", "");
    }

    /**
     * Id字符串转list
     * @param str
     * @return
     */
    public static List<Long> idStrToListLong(String str){
        // 首先去除空格
        String idsWithNoBlank = str.replaceAll(" +", "");
        // 其次使用分隔符将代码字符分开
        String[] idsNoBlankArray = idsWithNoBlank.split(",");
        //使用beanutils 提供的工具类进行类型转换
        Long[] convert = (Long[]) ConvertUtils.convert(idsNoBlankArray, Long.class);
        // 然后转换成为 list
        return Arrays.asList(convert);
    }

    /**
     * 字符串转list集合
     *
     * @param selectTechnologyIds
     * @return
     */
    public static List<String> strToStringList(String selectTechnologyIds) {
        List<String> list = new ArrayList<>();
        if (StringUtil.isEmpty(selectTechnologyIds)) {
            return list;
        }
        String[] strings = selectTechnologyIds.split(",");
        for (String info : strings) {
            if (StringUtils.isNotEmpty(info)) {
                list.add(info);
            }
        }
        return list;
    }

    /**
     * 字符串转list集合
     *
     * @param selectTechnologyIds
     * @return
     */
    public static List<Long> strToLongList(String selectTechnologyIds) {
        List<Long> list = new ArrayList<>();
        if (StringUtil.isEmpty(selectTechnologyIds)) {
            return list;
        }
        String[] strings = selectTechnologyIds.split(",");
        for (String info : strings) {
            if (StringUtils.isNotEmpty(info)) {
                list.add(Long.valueOf(info));
            }
        }
        return list;
    }

    /**
     * 字符串转list集合
     *
     * @param ids
     * @return
     */
    public static List<String> strToStrList(String ids) {
        List<String> list = new ArrayList<>();
        if (StringUtil.isEmpty(ids)) {
            return list;
        }
        String[] strings = ids.split(",");
        for (String info : strings) {
            if (StringUtils.isNotEmpty(info)) {
                list.add(info);
            }
        }
        return list;
    }

    /**
     * 字符串转list集合
     *
     * @param selectTechnologyIds
     * @return
     */
    public static Set<String> strToStrSet(String selectTechnologyIds) {
        Set<String> list = new HashSet<>();
        if (StringUtil.isEmpty(selectTechnologyIds)) {
            return list;
        }
        String[] strings = selectTechnologyIds.split(",");
        for (String info : strings) {
            if (StringUtils.isNotEmpty(info)) {
                list.add(info);
            }
        }
        return list;
    }

    /**
     * 获取字符串长度（按字数）
     *
     * @param value
     * @return
     */
    public static int strLengthByWordNumber(String value) {
        return value != null ? value.length() : 0;
    }

    /**
     * 获取字符串长度（按utf-8字节数）
     * @param value
     * @return
     */
    public static int strLengthByBytes(String value) {
        return value != null ? value.getBytes(StandardCharsets.UTF_8).length : 0;
    }

}
