package com.wanshifu;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableTransactionManagement
@MapperScan("com.wanshifu.master.order.push.mapper")
@EnableApolloConfig
@EnableFeignClients
@EnableAspectJAutoProxy
public class MasterOrderPushServiceApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(
            SpringApplicationBuilder application) {
        return application.sources(MasterOrderPushServiceApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(MasterOrderPushServiceApplication.class, args);
    }
}
