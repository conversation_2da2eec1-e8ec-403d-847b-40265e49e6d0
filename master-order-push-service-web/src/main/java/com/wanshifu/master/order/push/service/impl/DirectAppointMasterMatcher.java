package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 非智能推单匹配师傅（开发环境）
 * <AUTHOR>
 */
@Slf4j
@Component("direct_appoint_master")
public class DirectAppointMasterMatcher extends AbstractOrderMasterMatcher{

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    public boolean checkPreCondition(OrderDetailData orderDetailData){

        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();

        if(CollectionUtils.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.DIRECT_APPOINT_MASTER.code)){
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(StringUtils.isNotBlank(pushMode) && (!PushMode.DIRECT_APPOINT_MASTER.code.equals(pushMode))){
            return Boolean.FALSE;
        }


        return AccountType.USER .code.equals(orderDetailData.getAccountType()) && AppointType.NORMAL.value.equals(orderDetailData.getAppointType());
    }


    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){
        MatchMasterResult matchMasterResult = new MatchMasterResult(Collections.singleton(String.valueOf(orderDetailData.getPushExtraData().getDirectAppointMasterId())));
        return matchMasterResult;
    }


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){

    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        try{
            if(matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
                return false;
            }
            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp), PushMode.DIRECT_APPOINT_MASTER.code);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
            commonFeature.put(FieldConstant.PUSH_SCENARIO_TYPE,"direct_assignment_push");
            pushControllerFacade.directPush(orderDetailData,orderVersion,matchMasterResult.getMasterIdSet(),commonFeature);
            return true;
        }catch(Exception e){
            log.error(String.format("直接指派推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;
    }


}
