package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.*;

import java.util.List;

/**
 * 推单端口规则
 */
public interface PushPortRuleService {


    /**
     * 创建端口规则
     * @param rqt
     * @return
     */
    Integer create(CreateRqt rqt);


    /**
     * 更新端口规则
     * @param rqt
     * @return
     */
    Integer update(UpdateRqt rqt);


    /**
     * 端口规则详情
     * @param rqt
     * @return
     */
    PushPortRule detail(DetailRqt rqt);


    /**
     * 端口规则详情
     * @param rqt
     * @return
     */
    SimplePageInfo<PushPortRule> list(ListRqt rqt);



    /**
     * 启用/禁用端口规则
     * @param rqt
     * @return
     */
    Integer enable(EnableRqt rqt);


}
