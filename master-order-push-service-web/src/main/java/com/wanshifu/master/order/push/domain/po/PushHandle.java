package com.wanshifu.master.order.push.domain.po;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 推单处理记录表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "push_handle")
public class PushHandle {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 全局订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "order_version")
    private String orderVersion;

    @Column(name = "base_select_master_num")
    private Integer baseSelectMasterNum = 0;

    @Column(name = "direct_push_num")
    private Integer directPushNum = 0;

    @Column(name = "filtered_master_num")
    private Integer filteredMasterNum = 0;

    @Column(name = "filter_message")
    private String filterMessage;

    @Column(name = "score_message")
    private String scoreMessage;

    /**
     * 推送策略
     */
    @Column(name = "push_strategy")
    private String pushStrategy;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;


    public void addDirectPushMasterNum(){
        directPushNum++;
    }


    /**
     * 过滤详情(过滤器ID,过滤掉的师傅)
     */
    @Transient
    private Map<String, ArrayList<String>> filterMessageMap = new HashMap<>();

    /**
     * 评分详情(评分器ID,师傅ID-评分详情)
     */
    @Transient
    private JSONObject scoreMessageObject = new JSONObject();


    /**
     * 添加过滤详情
     */
    public void addFilterMessage(String filterName, String masterId) {
        ArrayList<String> filterInfoRow = filterMessageMap.get(filterName);
        if (filterInfoRow != null) {
            filterInfoRow.add(masterId);
        } else {
            filterInfoRow=new ArrayList<>();
            filterInfoRow.add(masterId);
            filterMessageMap.put(filterName, filterInfoRow);
        }
        // 过滤师傅数加一
        filteredMasterNum++;
    }


    /**
     * 添加评分详情
     */
    public void addScoreMessage(String scoreName, String masterId, BigDecimal score) {
        JSONObject masterScorerInfo = scoreMessageObject.getJSONObject(masterId);
        if (masterScorerInfo != null) {
            masterScorerInfo.put(scoreName, score);
        } else {
            masterScorerInfo=new JSONObject();
            masterScorerInfo.put(scoreName, score);
            scoreMessageObject.put(masterId, masterScorerInfo);
        }
    }

    public PushHandle(Long globalOrderId,String orderVersion,Integer baseSelectMasterNum){
        this.orderId = globalOrderId;
        this.orderVersion = orderVersion;
        this.baseSelectMasterNum = baseSelectMasterNum;
    }

    public PushHandle(){

    }

}
