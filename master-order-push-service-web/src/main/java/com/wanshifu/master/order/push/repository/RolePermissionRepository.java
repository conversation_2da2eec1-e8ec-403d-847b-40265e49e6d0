package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.RolePermission;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.AddPermissionSetRqt;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class RolePermissionRepository extends BaseRepository<RolePermission> {

    public int batchInsertRolePermission(Integer permissionSetId, List<AddPermissionSetRqt.PermissionMenu> permissionMenuList, List<Integer> roleIdList){
        List<RolePermission> rolePermissionList = new ArrayList<>();
        List<Integer> permissionIdList = new ArrayList<>();
        permissionMenuList.forEach(permissionMenu -> {
            permissionIdList.add(permissionMenu.getMenuId());
            if(CollectionUtils.isNotEmpty(permissionMenu.getButtonList())){
                permissionIdList.addAll(permissionMenu.getButtonList());
            }
            if(CollectionUtils.isNotEmpty(permissionMenu.getTabList())){
                permissionIdList.addAll(permissionMenu.getTabList());
            }
        });
        roleIdList.forEach(roleId -> {
            permissionIdList.forEach(permissionId -> {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setPermissionId(permissionId);
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionSetId(permissionSetId);
                rolePermission.setIsDelete(0);
                rolePermission.setCreateTime(new Date());
                rolePermission.setUpdateTime(new Date());
                rolePermissionList.add(rolePermission);
            });
        });
        return this.insertList(rolePermissionList);
    }


    public int deleteByPermissionSetId(Integer permissionSetId){
        RolePermission rolePermission = new RolePermission();
        rolePermission.setPermissionSetId(permissionSetId);
        return this.delete(rolePermission);
    }

    public List<RolePermission> selectByRoleIdList(List<Integer> roleIdList){
        Condition condition = new Condition(RolePermission.class);
        condition.createCriteria()
                .andIn("roleId", roleIdList)
                .andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

}