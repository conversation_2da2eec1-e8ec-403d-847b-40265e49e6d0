package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.EnterpriseLimitUser;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
@Repository
public class EnterpriseLimitUserRepository extends BaseRepository<EnterpriseLimitUser> {

    public List<EnterpriseLimitUser> selectByEnterpriseIdAndUserId(String enterpriseId, String userId){
        EnterpriseLimitUser enterpriseLimitUser = new EnterpriseLimitUser();
        enterpriseLimitUser.setEnterpriseId(enterpriseId);
        enterpriseLimitUser.setUserId(userId);
        enterpriseLimitUser.setIsDelete("0");
        enterpriseLimitUser.setLimitType("2");
        return this.select(enterpriseLimitUser);
    }

}