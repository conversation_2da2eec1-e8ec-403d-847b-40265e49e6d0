package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AutoReceiveMaster {

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 师傅自动接单价格
     */
    private BigDecimal masterPrice;

    /**
     * 师傅评分
     */
    private BigDecimal masterScore;


    public AutoReceiveMaster(){

    }

    public AutoReceiveMaster(Long masterId, BigDecimal masterPrice,BigDecimal masterScore){
        this.masterId = masterId;
        this.masterPrice = masterPrice;
        this.masterScore = masterScore;
    }
}
