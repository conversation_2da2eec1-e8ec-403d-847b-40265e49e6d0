package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;

import java.util.List;

public interface CompensateDistributeService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    CompensateDistribute detail(DetailRqt rqt);


    SimplePageInfo<CompensateDistribute> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    List<CompensateDistribute> match(MatchRqt rqt);

}
