package com.wanshifu.master.order.push.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wanshifu.base.address.api.GdGeoCodeApi;
import com.wanshifu.base.address.domain.gaode.form.GeoElectrobikeDrivingDrivingForm;
import com.wanshifu.base.address.domain.gaode.vo.GeoDirectionDrivingPathsVo;
import com.wanshifu.base.address.domain.gaode.vo.GeoDirectionDrivingVo;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class NavigationalDistanceCalculateService {

    private Cache<String, Object> cache = null;

    @Resource
    private Tools tools;


    @Resource
    private GdGeoCodeApi gdGeoCodeApi;



    @PostConstruct
    public void init() {
        cache = CacheBuilder.newBuilder()
                .initialCapacity(10)
                .maximumSize(1000)
                .expireAfterWrite(10, TimeUnit.MINUTES).build();

    }


    public Double calculate(String origin,String destination) {

        try{
            String key = origin + "_" + destination;
            Double cacheDistance = (Double) cache.getIfPresent(key);
            if (Objects.nonNull(cacheDistance)) {
                return cacheDistance;
            }


            //计算师傅常住地与订单地址导航距离
            GeoElectrobikeDrivingDrivingForm from = new GeoElectrobikeDrivingDrivingForm();
            from.setOrigin(origin);
            from.setDestination(destination);
            GeoDirectionDrivingVo geoDirectionDrivingVo = tools.catchLog(() -> gdGeoCodeApi.electrobikeDriving(from),"gdGeoCodeApi.electrobikeDriving",from);
            if (Objects.nonNull(geoDirectionDrivingVo) && "1".equals(geoDirectionDrivingVo.getStatus()) && CollectionUtils.isNotEmpty(geoDirectionDrivingVo.getRoute()) &&
                    CollectionUtils.isNotEmpty(geoDirectionDrivingVo.getRoute().get(0).getPaths())) {
                List<GeoDirectionDrivingPathsVo> pathList = geoDirectionDrivingVo.getRoute().get(0).getPaths();
                if (CollectionUtils.isNotEmpty(pathList)) {
                    Double distance = Double.valueOf(pathList.get(0).getDistance());
                    for(GeoDirectionDrivingPathsVo pathVo :  pathList){
                        Double currentDistance = Double.valueOf(pathVo.getDistance());
                        if(currentDistance < distance){
                            distance = currentDistance;
                        }
                    }
                    cache.put(key,distance);
                    return distance;
                }
            }
        }catch(Exception e){
            log.error("calculateNavigationDistance error",e);
        }

        return 0d;
    }


}
