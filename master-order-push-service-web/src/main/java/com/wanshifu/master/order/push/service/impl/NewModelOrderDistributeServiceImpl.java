package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterBase;
import com.wanshifu.master.order.push.domain.dto.MasterAutoReceiverRqt;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.repository.OrderDistributeRepository;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 协议订单调度
 */
@Service
public class NewModelOrderDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private RocketMqSendService rocketMqSendService;


    @Resource
    private OrderDistributeRepository orderDistributeRepository;


    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Value("${newModel.move.switch:on}")
    private String newModelMoveSwitch;



    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice){

        if(!"on".equals(newModelMoveSwitch)){
            return 0;
        }

        Long orderId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getOrderId();
        Long globalOrderTraceId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getGlobalOrderTraceId();
        OrderGrab orderGrab = orderPushedResultNotice.getOrderBaseComposite().getOrderGrab();
        String pushMode = orderPushedResultNotice.getPushMode();

        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        rqt.setOrderId(orderId);
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        if(orderGrab.getAppointType() == 4 || orderGrab.getAppointType() == 5){
            rqt.setAppointType(4);
        }
        rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_NEW_MODEL.getCode());
        if("new_model_single".equals(pushMode)){
            rqt.setOfferStrategy("auto_receive_grab");
            rqt.setExtraId("1");
        }else if("afresh_new_model_single".equals(pushMode)){
            rqt.setOfferStrategy("distribute");
            rqt.setExtraId("0");
        }
        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
        masterPrice.setMasterId(orderPushedResultNotice.getMasterInfoList().get(0).getMasterId());
        masterList.add(masterPrice);
        rqt.setMasterList(masterList);
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),1000L);
        return 1;

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeContext.register(PushMode.NEW_MODEL_SINGLE.code, this);
        OrderDistributeContext.register(PushMode.AFRESH_NEW_MODEL_SINGLE.code, this);
    }

}
