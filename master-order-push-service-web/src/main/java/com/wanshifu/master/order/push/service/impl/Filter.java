package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public class Filter {
    String filterId;
    String ruleName;
    String expression;
    String appointGroupExpression;

    public String getAppointGroupExpression() {
        return appointGroupExpression;
    }

    public void setAppointGroupExpression(String appointGroupExpression) {
        this.appointGroupExpression = appointGroupExpression;
    }

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleName() {
        return ruleName;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public static final class FilterBuilder {
        String filterId;
        String ruleName;
        String expression;
        String appointGroupExpression;

        private FilterBuilder() {
        }

        public static FilterBuilder aFilter() {
            return new FilterBuilder();
        }

        public FilterBuilder withFilterId(String filterId) {
            this.filterId = filterId;
            return this;
        }

        public FilterBuilder withRuleName(String ruleName) {
            this.ruleName = ruleName;
            return this;
        }

        public FilterBuilder withExpression(String expression) {
            this.expression = expression;
            return this;
        }

        public FilterBuilder withAppointGroupExpression(String appointGroupExpression) {
            this.appointGroupExpression = appointGroupExpression;
            return this;
        }

        public Filter build() {
            Filter filter = new Filter();
            filter.setFilterId(filterId);
            filter.setRuleName(ruleName);
            filter.setExpression(expression);
            filter.appointGroupExpression = this.appointGroupExpression;
            return filter;
        }
    }
}
