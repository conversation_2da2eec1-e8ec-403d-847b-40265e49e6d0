package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.mapper.AgentDistributeStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:36
 */
@Repository
public class AgentDistributeStrategyRepository extends BaseRepository<AgentDistributeStrategy> {

    @Resource
    private AgentDistributeStrategyMapper agentDistributeStrategyMapper;


    public AgentDistributeStrategy selectByServeId(Long agentId,Long serveId,Long thirdDivisionId){
        return CollectionUtils.getFirstSafety(agentDistributeStrategyMapper.selectByServeId(agentId,serveId,thirdDivisionId));
    }

    public List<AgentDistributeStrategy> selectByAgentIdAndServeIdsAndDivisionId(Long agendId, List<Long> serveIdList, Long thirdDivisionId) {

        return agentDistributeStrategyMapper.selectByAgentIdAndServeIdsAndDivisionId(agendId,serveIdList,thirdDivisionId);
    }

    public List<AgentDistributeStrategy> selectByServeIdsAndDivisionId(List<Long> serveIdList, Long fourthDivisionId) {
        return agentDistributeStrategyMapper.selectByServeIdsAndDivisionId(serveIdList, fourthDivisionId);
    }

    public List<AgentDistributeStrategy> selectList(String strategyName, String agentName, String serveName, Long cityDivisionId, Date createTimeStart, Date createTimeEnd,String masterSourceType){
        return agentDistributeStrategyMapper.selectList(strategyName,agentName,serveName,cityDivisionId,createTimeStart,createTimeEnd, masterSourceType);
    }


    public List<AgentDistributeStrategy> selectByAgentId(Long agentId){
        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setAgentId(agentId);
        agentDistributeStrategy.setIsDelete(0);
        return agentDistributeStrategyMapper.select(agentDistributeStrategy);
    }

    public List<AgentDistributeStrategy> selectByAgentIdAndMasterSourceType(Long agentId, String masterSourceType) {

        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setAgentId(agentId);
        agentDistributeStrategy.setMasterSourceType(masterSourceType);
        agentDistributeStrategy.setIsDelete(0);
        return agentDistributeStrategyMapper.select(agentDistributeStrategy);
    }

    public List<AgentDistributeStrategy> selectByAgentIdList(List<Long> agentIdList){
        Condition condition = new Condition(AgentDistributeStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andIn("agentId", agentIdList).andEqualTo("strategyStatus",1);
        return this.selectByCondition(condition);
    }

    public List<AgentDistributeStrategy> select4FlushData() {
        Condition condition = new Condition(AgentDistributeStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }

}
