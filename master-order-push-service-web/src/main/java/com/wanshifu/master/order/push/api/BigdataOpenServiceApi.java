package com.wanshifu.master.order.push.api;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.domain.api.response.*;
import com.wanshifu.master.order.push.domain.api.rqt.*;
import com.wanshifu.master.order.push.domain.dto.BigDataOpenApiDecoder;
import com.wanshifu.master.order.push.domain.dto.GetMstIndexValueVo;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(value = "bigdata-open-service",
        url = "${wshifu.bigdata-open-service.url}",
        configuration = {DefaultEncoder.class, BigDataOpenApiDecoder.class, BigDataOpenApiDecoder.ApiErrorDecoder.class}
)
public interface BigdataOpenServiceApi {

    /**
     * 【人群-离线】由商家ID(单个商家)查询人群所属的ID
     * <br/>
     * http://dev-api-manage.wanshifu.com:3000/project/1190/interface/api/66302
     *
     * @param rqt
     * @return
     */
    @FeignTimeout(connectTimeoutMillis = 2000, readTimeoutMillis = 1000)
    @PostMapping("/dataApi/getOne/getPersonaGroupIdsByUserId")
    GetPersonaGroupIdsByUserIdResp getPersonaGroupIdsByUserId(@RequestBody GetPersonaGroupIdsByUserIdRqt rqt);


    @FeignTimeout(connectTimeoutMillis = 2000, readTimeoutMillis = 1000)
    @PostMapping("/dataApi/getList/getPersonaGroupIdsByMasterIds")
    List<MasterGroupResp> getPersonaGroupIdsByMasterIds(@RequestBody GetPersonGroupByMasterIdsRqt rqt);



    @FeignTimeout(connectTimeoutMillis = 5000, readTimeoutMillis = 5000)
    @PostMapping("/abTry/getAbTryGroupByOrderId")
    List<GetAbTryGroupByOrderIdResp> getAbTryGroupByOrderId(@RequestBody GetAbTryGroupByOrderIdRqt rqt);


    @PostMapping("/data/table/getMstLast1mServingCntByMstIds")
    List<GetMstLast1mServingCntByMstIdsResp> getMstLast1mServingCntByMstIds(@RequestBody GetMstLast1mServingCntByMstIdsRqt rqt);


    @FeignTimeout(connectTimeoutMillis = 2000, readTimeoutMillis = 1000)
    @PostMapping("/dataApi/getData/getMstOrderCancelCntIn30Min")
    List<GetMstOrderCancelCntIn30MinResp> getMstOrderCancelCntIn30Min(@RequestBody GetMstOrderCancelCntIn30MinRqt rqt);


    /**
     * 推单对接大数据ABTest,按订单分组
     * @param rqt
     * @return
     */
    @PostMapping("/abTry/getTryByObjId")
    @FeignTimeout(connectTimeoutMillis = 5000, readTimeoutMillis = 5000)
    List<OrderPushAbTestResp> getOrderPushAbTest(@RequestBody OrderPushAbTestRqt rqt);


    @PostMapping("/dmp/getMstIndexValue")
    JSONObject getMstIndexValue(@RequestBody GetMstIndexValueVo rqt);






    @FeignTimeout(connectTimeoutMillis = 2000, readTimeoutMillis = 1000)
    @PostMapping("/dataApi/getData/mstCooperativeBusinessOrderStat")
    List<GetMstCooperativeBusinessOrderStatResp> mstCooperativeBusinessOrderStat(@RequestBody GetMstCooperativeBusinessOrderStatRqt rqt);


    @FeignTimeout(connectTimeoutMillis = 5000, readTimeoutMillis = 5000)
    @PostMapping("/dataApi/getData/allTimeOrderMstStat")
    List<AllTimeOrderMstStatResp> allTimeOrderMstStat(@RequestBody AllTimeOrderMstStatRqt rqt);


}
