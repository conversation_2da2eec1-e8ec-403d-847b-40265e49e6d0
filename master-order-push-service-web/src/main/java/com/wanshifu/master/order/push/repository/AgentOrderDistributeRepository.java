package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AgentOrderDistribute;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 18:35
 */
@Repository
public class AgentOrderDistributeRepository extends BaseRepository<AgentOrderDistribute> {

    public Long insertAgentOrderDistribute(Long orderId,Long agentId,String serveIds,Long thirdDivisionId,Integer strategyId,String distributeRule,String distributePriority,String remark){
        AgentOrderDistribute agentOrderDistribute = new AgentOrderDistribute();
        agentOrderDistribute.setOrderId(orderId);
        agentOrderDistribute.setAgentId(agentId);
        agentOrderDistribute.setOrderServeIds(serveIds);
        agentOrderDistribute.setThirdDivisionId(thirdDivisionId);
        agentOrderDistribute.setStrategyId(strategyId);
        agentOrderDistribute.setDistributeRule(distributeRule);
        agentOrderDistribute.setDistributePriority(distributePriority);
        agentOrderDistribute.setDirectAppointFailReason(remark);
        this.insertSelective(agentOrderDistribute);
        return agentOrderDistribute.getDistributeId();
    }


    public int updateDirectAppointFailReason(Long distributeId,String directAppointFailReason){
        AgentOrderDistribute agentOrderDistribute = new AgentOrderDistribute();
        agentOrderDistribute.setDistributeId(distributeId);
        agentOrderDistribute.setDirectAppointFailReason(directAppointFailReason);
        return this.updateByPrimaryKeySelective(agentOrderDistribute);
    }
}
