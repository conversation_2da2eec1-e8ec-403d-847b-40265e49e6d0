//package com.wanshifu.master.order.push.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alicloud.openservices.tablestore.model.BatchGetRowResponse;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.alicloud.openservices.tablestore.model.search.SearchQuery;
//import com.alicloud.openservices.tablestore.model.search.SearchRequest;
//import com.alicloud.openservices.tablestore.model.search.SearchResponse;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.wanshifu.base.address.api.AddressApi;
//import com.wanshifu.base.address.domain.po.Address;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
//import com.wanshifu.master.order.push.api.PackageOrderApi;
//import com.wanshifu.master.order.push.domain.api.response.GetPersonaGroupIdsByUserIdResp;
//import com.wanshifu.master.order.push.domain.api.rqt.GetPersonaGroupIdsByUserIdRqt;
//import com.wanshifu.master.order.push.domain.common.OrderPackageMaster;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
//import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
//import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
//import com.wanshifu.master.order.push.domain.dto.PackageBase;
//import com.wanshifu.master.order.push.domain.enums.PushMode;
//import com.wanshifu.master.order.push.domain.enums.RangeQueryType;
//import com.wanshifu.master.order.push.domain.po.Master;
//import com.wanshifu.master.order.push.domain.resp.orderPackage.PackageOrderOfferPriceResp;
//import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
//import com.wanshifu.master.order.push.domain.rqt.orderPackage.PackageOrderMasterOfferPriceRqt;
//import com.wanshifu.master.order.push.repository.MasterRepository;
//import com.wanshifu.master.order.push.repository.PushDataCenterRepository;
//import com.wanshifu.master.order.push.repository.PushProgressRepository;
//import com.wanshifu.master.order.push.service.*;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
//import com.wanshifu.order.offer.domains.enums.AccountType;
//import com.wanshifu.order.offer.domains.enums.AppointType;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//
//@Slf4j
//@Component("package_order")
//public class OrderPackageMasterMatcher extends AbstractOrderMasterMatcher{
//
//
//    @Resource
//    private TableStoreClient tableStoreClient;
//
//    @Resource
//    private PushDataCenterRepository pushDataCenterRepository;
//
//    @Resource
//    private MasterRepository masterRepository;
//
//    @Resource
//    private OTSMasterSelector otsMasterSelector;
//
//    @Resource
//    private PackageOrderApi packageOrderApi;
//
//    @Resource
//    private AddressApi addressApi;
//
//
//    @Value("${night.push.switch}")
//    private String nightPushSwitch;
//
//    @Value("${night.push.start.time}")
//    private String nightPushStartTime;
//
//
//    @Value("${night.push.end.time}")
//    private String nightPushEndTime;
//
//    @Value("${package.order.forbidden.user}")
//    private String forbiddenUser;
//
//    @Resource
//    private PushControllerFacade pushControllerFacade;
//
//    @Resource
//    private PushQueueService pushQueueService;
//
//    @Resource
//    private PushProgressRepository pushProgressRepository;
//
//    @Resource
//    private BigdataOpenServiceApi bigdataOpenServiceApi;
//
//
//    @Resource
//    private Tools tools;
//
//
//    @Resource
//    private OrderMatchRouteService orderMatchRouteService;
//
//
//    private boolean nightPush(){
//        if("on".equals(nightPushSwitch)){
//            return true;
//        }else{
//            return false;
//        }
//    }
//
//
//    @Override
//    public boolean checkPreCondition(OrderDetailData orderDetailData){
//        return (!"normal".equals(orderDetailData.getPushExtraData().getPushMode()))
//                && orderDetailData.getBusinessLineId() == 1
//                && CollectionUtils.isNotEmpty(orderDetailData.getLv3ServeIdList())
//                && orderDetailData.getLv3ServeIdList().size() == 1
//                && orderDetailData.getAppointType() != 3;
//    }
//
//    @Override
//    public MatchMasterResult match(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){
//
//        try{
//            log.info(String.format("orderPackageMasterMatcher orderDetailData:%s,masterMatchCondition:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(masterMatchCondition)));
//            Long masterOrderId = orderDetailData.getMasterOrderId();
//            String accountType=orderDetailData.getAccountType();
//            String orderUserId = "none";
//            String orderEnterpriseId = "none";
//            Long accountId = orderDetailData.getAccountId();
//            if(AccountType.USER.code.equals(accountType)&&accountId!=null) {
//                orderUserId=String.valueOf(accountId);
//            }else if (AccountType.ENTERPRISE.code.equals(accountType)) {
//                orderUserId=String.valueOf(orderDetailData.getUserId());
//                orderEnterpriseId=String.valueOf(accountId);
//            }
//            //用户黑名单 ***********
//            if (StringUtils.isNotEmpty(forbiddenUser)) {
//                final List<String> forbiddenUserList =
//                        new ArrayList<>(Arrays.asList(forbiddenUser.split(",")));
//                if (forbiddenUserList.contains(orderUserId)) {
//                    log.info("debug::[订单包禁止用户]break package_mode::{},{},orderEnterpriseId:{},:orderUserId{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId,orderUserId);
//                    return null;
//                }
//
//            }
//
//
//
//            //总包
//            if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
//                //非总包直接指派确认订单包,才需要做总包开关判断
//                if (!FieldConstant.CONFIRM_ORDER_PACKAGE.equals(orderDetailData.getPushExtraData().getOperateFlag())) {
//                    if (!this.enterprisePackageSwitch(orderEnterpriseId)) {
//                        log.info("debug::[总包订单包开关]break package_mode::{},{},orderEnterpriseId:{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId);
//                        return null;
//                    }
//                }
//
//                //总包指派黑名单过滤
//                if (this.enterpriseUserLimit(orderEnterpriseId,orderUserId)) {
//                    log.info("debug::[总包指派黑名单]break package_mode::{},{},orderEnterpriseId:{},:orderUserId{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId,orderUserId);
//                    return null;
//                }
//            }
//
//
//            //拆旧服务,nouse不需要拆旧,useall拆整体,usepart拆部件
//            String demolishType = orderDetailData.getDemolishType();
//            //是否需要寄回旧件,1:是,2:否
//            final Integer isSendBackOld = orderDetailData.getIsSendBackOld();
//            if (FieldConstant.USEALL.equals(demolishType)||
//                    FieldConstant.USEPART.equals(demolishType)||
//                    SymbolConstant.ONE.equals(String.valueOf(isSendBackOld))) {
////            logger.info("debug::break package_mode::{},{},{}",masterOrderId,demolishType,isSendBackOld);
//                return null;
//            }
//            //加急单不走订单包-工作项:********-2023-12-11
//            if (orderDetailData.getEmergencyOrderFlag()!=null&&1==orderDetailData.getEmergencyOrderFlag()) {
//                return null;
//            }
//
//
//            final BigDecimal definiteServeFee = orderDetailData.getDefiniteServeFee();
//            final MatchMasterResult matchMasterResult = this.searchOrderPackageMaster(
//                    masterMatchCondition,
//                    orderUserId,orderEnterpriseId,
//                    orderDetailData.getAccountType(),orderDetailData.getAccountId(),
//                    orderDetailData.getExpectDoorInStartTimeString(),
//                    orderDetailData.getExpectDoorInEndTimeString(),
//                    orderDetailData.getAttributeKeys(),
//                    orderDetailData.getAttributeSet(),
//                    orderDetailData.getAppointType(),
//                    orderDetailData.getAttributeValue(),
//                    orderDetailData.getGlobalOrderId(),
//                    definiteServeFee
//            );
//            return matchMasterResult;
//        }catch(Exception e){
//            log.error(String.format("订单匹配订单包师傅失败,orderDetailData:%s",JSON.toJSONString(orderDetailData)),e);
//        }
//        return new MatchMasterResult(new HashSet<>());
//    }
//
//
//    /**
//     * 总包订单包开关
//     * @param enterpriseId
//     * @return
//     */
//
//
//    private static final String RULE="order_package_rule";
//
//    public boolean enterprisePackageSwitch(String enterpriseId){
//        final Row row = tableStoreClient.getRowWithOnePK(
//                "t_enterprise",
//                FieldConstant.ENTERPRISE_ID,
//                enterpriseId);
//
//        final String rule = tableStoreClient.getValue(row, RULE);
//
//        if (rule!=null&&rule.contains("2")) {
//            return false;
//        }
//        return true;
//    }
//
//
//    /**
//     * 总包商家限制过滤
//     * @return
//     */
//    private static final String ENTERPRISE_LIMIT_USER = "t_enterprise_limit_user";
//    private static final String ENTERPRISE_LIMIT_USER_INDEX = "enterprise_limit_user_index_v1";
//    public boolean enterpriseUserLimit(String enterpriseId,String userId){
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQuery=new ArrayList<Query>();
//        setEnterpriseUserLimitQuery(mustQuery,enterpriseId,userId);
//        boolQuery.setMustQueries(mustQuery);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(ENTERPRISE_LIMIT_USER, ENTERPRISE_LIMIT_USER_INDEX, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row!=null) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private static final String LIMIT_TYPE="limit_type";
//    private void setEnterpriseUserLimitQuery(List<Query> mustQuery,String enterpriseId,String userId){
//        mustQuery.add(tableStoreClient.stringTermQuery(FieldConstant.ENTERPRISE_ID,enterpriseId));
//        mustQuery.add(tableStoreClient.stringTermQuery(FieldConstant.USER_ID,userId));
//        mustQuery.add(tableStoreClient.stringTermQuery("is_delete","0"));
//        mustQuery.add(tableStoreClient.stringTermQuery(LIMIT_TYPE,"2"));
//    }
//
//
//    /**
//     * 获取订单包信息
//     * @return
//     */
//    private static final String ORDER_PACKAGE_BASE = "t_order_package_base";
//    private static final String ORDER_PACKAGE_BASE_INDEX = "order_package_base_index_v2";
//    private static final String PACKAGE_CONFIG_ID = "package_config_id";
//    private static final String MAX_PUSH_NUMBER_DAILY="max_push_number_daily";
//    public MatchMasterResult searchOrderPackageMaster(
//            MasterMatchCondition masterCondition,
//            String orderUserId,String orderEnterpriseId,
//            String accountType,Long accountId,
//            String expectDoorInStartTimeString,
//            String expectDoorInEndTimeString,
//            String attributeKey,
//            Set<String> attributeSet,
//            Integer appointType,
//            JSONObject attributeValue,
//            Long getGlobalOrderTraceId,
//            BigDecimal definiteServeFee) {
//
//        MatchMasterResult matchMasterResult = new MatchMasterResult();
//        final HashSet<String> packageConfigIdSet = new HashSet<>();
//        final HashMap<String, PackageBase> packageBaseMap = new HashMap<>();
//        getOrderPackageInfo(
//                orderUserId,
//                orderEnterpriseId,
//                masterCondition,
//                packageConfigIdSet,
//                packageBaseMap,
//                appointType,
//                getGlobalOrderTraceId,
//                definiteServeFee);
//        HashSet<String> masterIds = new HashSet<>();
//        log.info("master_order_id:{}::package_list::debug::accountId:{},result package:{}",
//                masterCondition.getMasterOrderId(),accountId,packageConfigIdSet);
//
//        if (packageConfigIdSet.size()==0) {
//            return matchMasterResult;
//        }
//        /**
//         * 子属性判断 WORKFLOW
//         */
//        lowAttributeFilter(masterCondition.getServeIds(),attributeKey,attributeSet,packageConfigIdSet,packageBaseMap,attributeValue);
//        log.info("master_order_id:{}::package_attr_list::accountId:{},result package:{},attributeKey:{}::AttributeValue:{}," +
//                        "packageBaseMap:{}",
//                masterCondition.getMasterOrderId(),accountId,packageConfigIdSet,attributeKey,attributeValue,packageBaseMap);
//
//        if (packageConfigIdSet.size()==0) {
//            return matchMasterResult;
//        }
//
//        /**
//         * 师傅列表
//         */
//        final HashMap<String, OrderPackageMaster> packageMasterIds
//                = getOrderPackageMasterIds(masterCondition.getMasterOrderId(),masterCondition.getOrderVersion()
//                ,packageBaseMap, packageConfigIdSet, masterIds,
//                masterCondition.getServeIds(),
//                attributeSet);
//        log.info("master_order_id:{}::master_list::package::::accountId:{},masterIds:{},attributeKey{}",
//                masterCondition.getMasterOrderId(),accountId,masterIds,attributeKey);
////		------------------------------------师傅过滤-----------------------------------
//        //四级地址过滤
//        if (masterCondition.getDivisionMatchLevel()==3&&masterIds.size() != 0) {
//            levelThreeDivisionFilter(masterIds,masterCondition);
//        }
//
//        //退款限制
//        if (masterIds.size() != 0) {
//            //TODO
//            filterRefundMaster(masterCondition,orderUserId,masterIds);
//        }else {
//            return matchMasterResult;
//        }
//        log.info("{}::after 退款::package::debug::::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
//        //服务中限制
//        if (masterIds.size() != 0) {
////            reserveMasterFilter(expectDoorInStartTimeString,expectDoorInEndTimeString,masterIds);
//        }
//        log.info("{}::after 服务中::package::debug::::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
//        if (masterIds.size() != 0) {
//            //拉黑过滤
//            this.blackListFilter(masterIds,accountType,accountId,orderUserId);
//        }
//        log.info("{}::after 拉黑::package::debug::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
//        //功能限制过滤
//        if (masterIds.size() != 0) {
//            final Set<String> limitedMasterSet = searchRestrictMasterByIndex(masterIds,
//                    RestrictAction.NO_QUOTATION.getActionString(),
//                    RestrictAction.NO_APPOINT.getActionString());
//            if (limitedMasterSet.size()!=0) {
//                masterIds.removeAll(limitedMasterSet);
//            }
//        }
//        log.info("{}::after 功能限制::package::debug::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
//        //		------------------------------------师傅过滤-----------------------------------
//        String masterId=null;
//        if (masterIds.size()==0) {
//            return matchMasterResult;
//        }else if(masterIds.size()==1) {
//            masterId=masterIds.iterator().next();
//        }else {
//            masterId= minMasterSelect(masterIds,packageMasterIds,masterCondition);
//        }
//
//        if (masterId!=null) {
//            String finalMasterId = masterId;
//            final OrderPackageMaster orderPackageMaster = packageMasterIds.get(finalMasterId);
//            matchMasterResult.putExtraData(FieldConstant.PACKAGE_ID,orderPackageMaster.getBindPackageId());
//            final String bindPackageConfigId = orderPackageMaster.getBindPackageConfigId();
//            matchMasterResult.putExtraData(FieldConstant.PACKAGE_CONFIG_ID,bindPackageConfigId);
//            final PackageBase packageBase = packageBaseMap.get(bindPackageConfigId);
//            matchMasterResult.putExtraData(FieldConstant.PACKAGE_ATTRIBUTE_ID,packageBase.getMatchedAttributeId());
//            matchMasterResult.putExtraData(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE,packageBase.getMatchedAttributeValue());
//            matchMasterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
//            matchMasterResult.putExtraData(FieldConstant.PLATFORM_AUTO_MODE,packageBase.getPlatformAutoMode());
//
//
//            log.info("{}::{},package::finalMasterId:[{}],getBindPackageId:[{}]," +
//                    "[{}]",masterCondition.getMasterOrderId(),accountId,finalMasterId,orderPackageMaster.getBindPackageId(),packageMasterIds);
//
//            matchMasterResult.setMasterIdSet(new HashSet<String>(){{add(finalMasterId);}});
//        }else {
//            return matchMasterResult;
//        }
//        return matchMasterResult;
//
//    }
//
//    private void levelThreeDivisionFilter(Set<String> masterIds,MasterMatchCondition masterMatchCondition){
//        final Set<String> subDivisionIdList = getSubListByDivisionId(masterMatchCondition.getThirdDivisionId());
//        if (CollectionUtils.isEmpty(subDivisionIdList)) {
//            log.info("{},subDivision is empty:{},lv3:{},getDivisionMatchLevel:{}"
//                    ,masterMatchCondition.getMasterOrderId()
//                    ,subDivisionIdList
//                    ,masterMatchCondition.getThirdDivisionId()
//                    ,masterMatchCondition.getDivisionMatchLevel());
//            return;
//        }
//        log.info("{},subDivision:{},lv3:{},getDivisionMatchLevel:{}"
//                ,masterMatchCondition.getMasterOrderId()
//                ,subDivisionIdList
//                ,masterMatchCondition.getThirdDivisionId()
//                ,masterMatchCondition.getDivisionMatchLevel());
//
//        List<Master> masterList = masterRepository.selectByMasterIdSet(masterIds);
//        if(CollectionUtils.isEmpty(masterList)){
//            return ;
//        }
//        final Map<String, Master> masterMap = masterList.stream()
//                .filter(entity -> entity.getMasterId() != null)
//                .collect(Collectors.toMap(Master::getMasterId, Function.identity()));
//
//        final HashSet<String> tmpMasterSet = new HashSet<>(masterIds);
//        //按3级地址扩展时,过滤[不匹配全区域]的师傅
//        for (String currentMasterId : tmpMasterSet) {
//            final String masterLevelFourDivision = masterMap.get(currentMasterId).getCoreFourthDivisionIds();
//            final boolean fullMatch = thirdLevelDivisionFullMatch(subDivisionIdList, masterLevelFourDivision);
//            if (!fullMatch) {
//                masterIds.remove(currentMasterId);
//            }
//        }
//    }
//
//
//    /**
//     * 用户/总包 拉黑过滤
//     */
//    public Set<String> blackListFilter(Set<String> result,String accountType,Long accountId,String orderUserId){
//        Set<String> blackMasterList=new HashSet<>();
//        try {
//            String accountIdString=String.valueOf(accountId);
//            if (FieldConstant.ENTERPRISE.equals(accountType)) {
//                blackMasterList= otsMasterSelector.enterpriseBlackListFilter(
//                        accountIdString,result);
//                //用户ID不为空
//                if (!"none".equals(orderUserId)&&!"0".equals(orderUserId)) {
//                    blackMasterList.addAll(otsMasterSelector.userBlackListFilter(
//                            orderUserId,result
//                    ));
//                }
//            } else if(FieldConstant.USER.equals(accountType)) {
//                blackMasterList=otsMasterSelector.userBlackListFilter(
//                        accountIdString,result
//                );
//            }
//            if (blackMasterList!=null) {
//                result.removeAll(blackMasterList);
//            }
//        } catch (Exception e) {
//            log.warn("blackListFilter failed :: accountId:{},e:{}",accountId,e);
//        }
//        return blackMasterList;
//    }
//
//
//    /**
//     * 限制枚举
//     */
//    private enum RestrictAction {
//        PUSH{
//            @Override
//            public String getActionString(){
//                return "1";
//            };
//        },LOGIN{
//            @Override
//            public String getActionString(){
//                return "2";
//            };
//        },EXCLUSIVE{
//            @Override
//            public String getActionString(){
//                return "14";
//            };
//        },NO_QUOTATION{
//            @Override
//            public String getActionString(){
//                return "3";
//            };
//        },NO_APPOINT{
//            @Override
//            public String getActionString(){
//                return "4";
//            };
//        };
//        public String getActionString(){
//            return null;
//        };
//    };
//
//
//
//    /**
//     * 过滤功能限制师傅
//     * @param RestrictAction 1：禁推单，2：禁登录，3：禁专属
//     * @param orderGoodsLv1Id
//     * @param inputMasterIdsSet
//     * @return
//     */
//    private static final String MASTER_INDEX = "master_index_v1";
//
//
//
//
//    private Set<String> searchRestrictMasterByIndex(Set<String> inputMasterIdsSet,String ... restrictActions) {
//        HashSet<String> masterIds=new HashSet<>();
//        if (restrictActions==null||restrictActions.length==0) {
//            return masterIds;
//        }
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList=new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID, inputMasterIdsSet));
//        setRestrictMasterQuery(mustQueryList,restrictActions);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(FieldConstant.T_MASTER, MASTER_INDEX, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row!=null) {
//                masterIds.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//            }
//        }
//        return masterIds;
//    }
//
//
//    private void setRestrictMasterQuery(List<Query> mustQueryList,String ... restrictActions){
//        mustQueryList.add(tableStoreClient.stringTermsQuery("restrict_action", restrictActions));
//    }
//
//
//
//
//    private void getOrderPackageInfo(String orderUserId,String orderEnterpriseId,
//                                       MasterMatchCondition masterCondition,
//                                       HashSet<String> packageConfigIdSet,
//                                       HashMap<String, PackageBase> packageBaseMap,
//                                       Integer appointType,
//                                       Long globalOrderId,
//                                       BigDecimal definiteServeFee){
//
//        searchOrderPackage(orderUserId,orderEnterpriseId,masterCondition,packageConfigIdSet,packageBaseMap,appointType);
//
//        if (definiteServeFee!=null
//                &&definiteServeFee.compareTo(BigDecimal.ZERO)!=0) {
//            log.info("{},debug definiteServeFee before::{}",masterCondition.getMasterOrderId(),
//                    packageConfigIdSet);
//            checkPackageOrderPrice(definiteServeFee,globalOrderId,packageConfigIdSet);
//            log.info("{},debug definiteServeFee after::{}",masterCondition.getMasterOrderId(),
//                    packageConfigIdSet);
//        }
//    }
//
//
//    /**
//     * 检查订单包价格
//     * @param definiteServeFee
//     * @param globalOrderId
//     * @param packageConfigIdSet
//     */
//    private void checkPackageOrderPrice(
//            BigDecimal definiteServeFee,
//            Long globalOrderId,
//            HashSet<String> packageConfigIdSet
//    ){
//        if (packageConfigIdSet.size()==0) {
//            return;
//        }
//
//        final HashSet<String> before = new HashSet<>();
//        before.addAll(packageConfigIdSet);
//
//        PackageOrderMasterOfferPriceRqt rqt = new PackageOrderMasterOfferPriceRqt();
//        rqt.setGlobalOrderTraceId(globalOrderId);
//        rqt.setPackageIds(String.join(",",packageConfigIdSet));
//        final PackageOrderOfferPriceResp resp = packageOrderApi.getOfferPrice(rqt);
//        Map<String, BigDecimal> orderPackagePriceMap = resp.getOfferPrice();
//        //订单有价格,订单包【无价格】,【价格为0】,【大于订单价格】则过滤
//        for (String packageConfigId : before) {
//            final BigDecimal packagePrice = orderPackagePriceMap.get(packageConfigId);
//            if (packagePrice.compareTo(BigDecimal.ZERO)==0) {
//                packageConfigIdSet.remove(packageConfigId);
//                log.info("{},debug::Zero{},{},{}",globalOrderId,
//                        packageConfigId,packagePrice,definiteServeFee);
//                continue;
//            }else if (packagePrice.compareTo(definiteServeFee) > 0) {
//                packageConfigIdSet.remove(packageConfigId);
//                log.info("{},debug::remove{},{},{}",globalOrderId,
//                        packageConfigId,packagePrice,definiteServeFee);
//            }
//        }
//
//
//    }
//
//
//    private void searchOrderPackage(String orderUserId,String orderEnterpriseId,
//                                          MasterMatchCondition masterCondition,
//                                          HashSet<String> packageConfigIdSet,
//                                          HashMap<String, PackageBase> packageBaseMap,
//                                          Integer appointType){
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList=new ArrayList<Query>();
//        /**
//         * 有效范围(白名单)
//         * 耀辉:用户下给总包的订单 mq 新增一个 userId 用户字段
//         */
//        setOrderPackageSourceQuery(mustQueryList,orderUserId,orderEnterpriseId);
//
//        /**
//         * 人群包过滤
//         */
////        setOrderPackageUserGroupQuery(mustQueryList,orderUserId,orderEnterpriseId);
//
//
//        /**
//         * 服务地区
//         */
//        setOrderPackageServeDivisionQuery(mustQueryList,masterCondition);
//        /**
//         * 进行中
//         */
//        setOrderPackageStatusQuery(mustQueryList,masterCondition,appointType);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(ORDER_PACKAGE_BASE, ORDER_PACKAGE_BASE_INDEX, searchQuery);
//        /**
//         * 添加返回列
//         */
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        columnsToGet.setColumns(Arrays.asList(FieldConstant.CREATE_TIME,MAX_PUSH_NUMBER_DAILY,LV4_DIVISION_IDS,FieldConstant.PLATFORM_AUTO_MODE));
//        searchRequest.setColumnsToGet(columnsToGet);
//
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//
//        p:for (Row row : rows) {
//            if (row!=null) {
//                final String packageConfigId = row.getPrimaryKey()
//                        .getPrimaryKeyColumn(PACKAGE_CONFIG_ID).getValue().asString();
//                packageConfigIdSet.add(packageConfigId);
//                packageBaseMap.put(packageConfigId,PackageBase.PackageBaseBuilder.aPackageBase()
//                        .withPackageConfigId(packageConfigId)
//                        .withMaxPushNumberDaily(tableStoreClient.getLongValue(row,MAX_PUSH_NUMBER_DAILY))
//                        .withCreateTime(tableStoreClient.getLongValue(row,FieldConstant.CREATE_TIME))
//                        .withPlatformAutoMode(tableStoreClient.getLongValue(row,FieldConstant.PLATFORM_AUTO_MODE))
//                        .build());
//            }
//        }
//    }
//
//    /**
//     * 获取地址子集集合
//     * @return
//     */
//    private Set<String> getSubListByDivisionId(Long divisionId){
//        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
//        final Set<String> divisionList =
//                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
//        return divisionList;
//    }
//    /**
//     * 三级地址全匹配
//     */
//    private boolean thirdLevelDivisionFullMatch(
//            Set<String> orderFullFourthDivisions,
//            String packageFourthLevelDivisions){
//        if (StringUtils.isEmpty(packageFourthLevelDivisions)) {
//            return false;
//        }
//        final Set<String> packageFourthDivisionSet
//                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());
//
//        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
//    }
//
//
//    /**
//     * 订单包子属性查询
//     * @param packageConfigIdSet
//     * @param packageBaseMap
//     */
//    private void lowAttributeFilter(String serveIds,String attributeKey,Set<String> attributeSet,
//                                    HashSet<String> packageConfigIdSet,
//                                    HashMap<String, PackageBase> packageBaseMap,
//                                    JSONObject attributeValue){
//        //查询服务子属性列表
//        final HashSet<String> matchedAttributePackage = packageMatch(serveIds,packageConfigIdSet);
//
//        if (attributeKey==null) {
//            packageConfigIdSet.removeAll(matchedAttributePackage);
//        }else {
//            //订单有子属性,服务无子属性,不做属性筛选
//            if (0==matchedAttributePackage.size()) {
//                return;
//            }
//            final HashSet<String> matchedPackage = packageServeMatch(serveIds,attributeSet, packageConfigIdSet,packageBaseMap,attributeValue);
//            packageConfigIdSet.retainAll(matchedPackage);
//            packageConfigIdSet.forEach(packageConfigId -> packageBaseMap.get(packageConfigId).setIsMatchedAttribute(1));
//        }
//    }
//
//
//    //	private static final String SMC_PACKAGE_SERVE_INFO = "smc_package_serve_info";
////	private static final String SMC_PACKAGE_SERVE_INFO_INDEX = "smc_package_serve_info_index";
//    private static final String ORDER_PACKAGE_SERVE = "t_order_package_serve";
//    private static final String ORDER_PACKAGE_SERVE_INDEX = "order_package_serve_index_v1";
//    private HashSet<String> packageServeMatch(String serveIds,Set<String> attributeSet,HashSet<String> packageConfigIdSet
//            ,HashMap<String, PackageBase> packageBaseMap,JSONObject AttributeValue){
//        HashSet<String> matchedPackage=new HashSet<>();
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(PACKAGE_CONFIG_ID,
//                packageConfigIdSet));
//        setPackageServeQuery(serveIds,attributeSet,mustQueryList,AttributeValue);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        //嵌套查询需要指定排序方式
////		searchQuery.setSort(new Sort(Arrays.asList(new FieldSort(PACKAGE_CONFIG_ID, SortOrder.ASC))));
//
//
//        SearchRequest searchRequest = new SearchRequest(ORDER_PACKAGE_SERVE,
//                ORDER_PACKAGE_SERVE_INDEX, searchQuery);
//
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        // 设置为返回指定列。
//        columnsToGet.setColumns(Arrays.asList(FieldConstant.PACKAGE_CONFIG_ID,
//                FieldConstant.ATTRIBUTE_KEY,
//                FieldConstant.ATTRIBUTE_VALUE_MIN,
//                FieldConstant.ATTRIBUTE_VALUE_MAX
//        ));
//        searchRequest.setColumnsToGet(columnsToGet);
//
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        final HashMap<String, HashSet<String>> packageAttributesMap = new HashMap<>();
//        final HashMap<String, Map<String,String>> packageServeDataMap = new HashMap<>();
//        for (Row row : rows) {
//            if (row != null) {
//                final String packageConfigId = tableStoreClient.getValue(row,FieldConstant.PACKAGE_CONFIG_ID);
//                final String packageAttributesId = tableStoreClient.getValue(row,FieldConstant.ATTRIBUTE_KEY);
//
//                final String attributeValueMin = tableStoreClient.getValue(row,FieldConstant.ATTRIBUTE_VALUE_MIN);
//                final String attributeValueMax = tableStoreClient.getValue(row,FieldConstant.ATTRIBUTE_VALUE_MAX);
//                //获取订单包的子属性集合
//                LocalCollectionsUtil.addHashSetValueToGenericMap(packageAttributesMap,packageConfigId,packageAttributesId);
//                packageServeDataMap.put(packageServeDataMapKey(packageConfigId,packageAttributesId),new HashMap<String,String>(){
//                    {
//                        put(FieldConstant.ATTRIBUTE_VALUE_MIN,attributeValueMin);
//                        put(FieldConstant.ATTRIBUTE_VALUE_MAX,attributeValueMax);
//                    }
//                });
//            }
//        }
//
//        for (Map.Entry<String, HashSet<String>> entry : packageAttributesMap.entrySet()) {
//            if (entry.getValue().size()==1) {
//                final String packageConfigId = entry.getKey();
//                matchedPackage.add(packageConfigId);
//                final PackageBase packageBase = packageBaseMap.get(packageConfigId);
//                final HashSet<String> matchedAttributeSet = entry.getValue();
//                for (String matchedAttributeId : matchedAttributeSet) {
//                    packageBase.setMatchedAttributeId(matchedAttributeId);
//                }
//                final Map<String, String> packageAttributeData =
//                        packageServeDataMap.get(packageServeDataMapKey(packageConfigId, packageBase.getMatchedAttributeId()));
//                final JSONObject matchedAttributeValue = new JSONObject();
//
//                final String finalMin = packageAttributeData.get(FieldConstant.ATTRIBUTE_VALUE_MIN);
//                final String finalMax = packageAttributeData.get(FieldConstant.ATTRIBUTE_VALUE_MAX);
//                //记录订单包尺寸匹配数据
//                if (finalMin!=null&&finalMax!=null) {
//                    matchedAttributeValue.put(packageBase.getMatchedAttributeId(),new JSONArray(){{
//                        add(Double.valueOf(finalMin));
//                        add(Double.valueOf(finalMax));
//                    }});
//                    packageBase.setMatchedAttributeValue(matchedAttributeValue);
//                    packageBase.setMatchedAttributeSizeMin(finalMin);
//                    packageBase.setMatchedAttributeSizeMax(finalMax);
//                }
//            }
//        }
//        return matchedPackage;
//    }
//
//    private String packageServeDataMapKey(String packageConfigId,String packageAttributesId){
//        return packageConfigId+"_"+packageAttributesId;
//    }
//
//    /**
//     * 返回包含子属性的订单包
//     * @param packageConfigIdSet
//     * @return
//     */
//    private HashSet<String> packageMatch(String serveIds,HashSet<String> packageConfigIdSet){
//        HashSet<String> matchedPackage=new HashSet<>();
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
////		List<Query> mustNotQueryList=new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(PACKAGE_CONFIG_ID,
//                packageConfigIdSet));
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.SERVE_ID,serveIds));
//        //服务过滤修补
////		setPackageMatchNotQuery(serveIds,mustNotQueryList);
//        boolQuery.setMustQueries(mustQueryList);
////		boolQuery.setMustNotQueries(mustNotQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(ORDER_PACKAGE_SERVE,
//                ORDER_PACKAGE_SERVE_INDEX, searchQuery);
//
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        // 设置为返回指定列。
//        columnsToGet.setColumns(Arrays.asList(FieldConstant.PACKAGE_CONFIG_ID,
//                FieldConstant.ATTRIBUTE_KEY
//        ));
//        searchRequest.setColumnsToGet(columnsToGet);
//
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row != null) {
//                final String packageConfigId = tableStoreClient.getValue(row,FieldConstant.PACKAGE_CONFIG_ID);
//                final String attributeKey = tableStoreClient.getValue(row,FieldConstant.ATTRIBUTE_KEY);
//                if (attributeKey!=null&&!SymbolConstant.EMPTY_STRING.equals(attributeKey)) {
//                    matchedPackage.add(packageConfigId);
//                }
//            }
//        }
//        return matchedPackage;
//    }
//
//
//
//    private static final String ATTRIBUTE_KEY="attribute_key";
//    private void setPackageServeQuery(String serveIds,Set<String> attributeSet,
//                                      List<Query> mustQueryList,
//                                      JSONObject attributeValue){
//        //商品属性 使用attributeKey非空判断
////		mustQueryList.add(OTSUtil.stringTermsQuery(ATTRIBUTE_KEY,
////				attributeSet));
////		-----------------------------------------------------------------
//        //修补package_serve过滤
////		mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.SERVE_ID,serveIds));
////		-----------------------------------------------------------------
//        BoolQuery attributeShouldQuery = new BoolQuery();
//        final ArrayList<Query> attributeShouldArray = new ArrayList<>();
//        if (attributeSet!=null) {
//            for (String attributeId : attributeSet) {
//                BoolQuery attributeBool = new BoolQuery();
//                final ArrayList<Query> attributeMustBoolArray = new ArrayList<>();
//                attributeMustBoolArray.add(tableStoreClient.stringTermQuery(ATTRIBUTE_KEY,attributeId));
//                try {
//                    final Double attributeValueDouble = attributeValue.getDouble(attributeId);
//                    //如果有商品尺寸,则判断尺寸
//                    if (attributeValueDouble!=null) {
//                        tableStoreClient.doubleRangeBetweenQuery(
//                                attributeMustBoolArray,
//                                attributeValueDouble,
//                                FieldConstant.ATTRIBUTE_VALUE_MIN,
//                                FieldConstant.ATTRIBUTE_VALUE_MAX);
//                    }
//                }catch (Exception e){
//                    log.warn("warn - setPackageServeQuery attributeValue:{},{},{}",attributeValue,attributeSet,e);
//                }
//                attributeBool.setMustQueries(attributeMustBoolArray);
//                attributeShouldArray.add(attributeBool);
//            }
//        }
//        attributeShouldQuery.setShouldQueries(attributeShouldArray);
//        attributeShouldQuery.setMinimumShouldMatch(1);
//        mustQueryList.add(attributeShouldQuery);
//    }
//
//    /** 排序
//     *1.批次，创建时间
//     *2.待指派订单
//     *3.核心区域
//     *4.随机
//     */
//    private String minMasterSelect(HashSet<String> masterIds,
//                                   HashMap<String, OrderPackageMaster> packageMasterIds,
//                                   MasterMatchCondition masterCondition){
//        final ArrayList<String> masterSortList = new ArrayList<>(masterIds);
//        /** 排序
//         * 1.批次，创建时间
//         * 2.待指派订单
//         */
//        sortPackageMaster(masterSortList,packageMasterIds);
//        /**
//         * 取最大值相同
//         */
//        final String firstMaster = masterSortList.get(0);
//        final OrderPackageMaster orderPackageMaster = packageMasterIds.get(firstMaster);
//        long maxPackageCreateTime = orderPackageMaster.getBindPackageCreateTime();
//        int maxRemainPushOrderNumber = orderPackageMaster.getRemainPushOrderNumber();
//        final ArrayList<String> moreSortMasterList = new ArrayList<>();
//        for (int i = 1; i < masterSortList.size(); i++) {
//            final String dMasterId = masterSortList.get(i);
//            final OrderPackageMaster dMaster = packageMasterIds.get(dMasterId);
//            if (dMaster.getBindPackageCreateTime()==maxPackageCreateTime
//                    &&dMaster.getRemainPushOrderNumber()==maxRemainPushOrderNumber) {
//                moreSortMasterList.add(dMasterId);
//            }
//        }
//        if (moreSortMasterList.size()==0) {
//            return firstMaster;
//        }else {
//            //核心区域
//            final String thirdDivisionId = String.valueOf(masterCondition.getThirdDivisionId());
//            final Map<String, String> tMasterFeature =
//                    getMasterFeature(moreSortMasterList, FieldConstant.CORE_DIVISION_ID);
//            final String coreMaster = getSortedPackageMasterDivision(thirdDivisionId, moreSortMasterList, tMasterFeature);
//            return coreMaster;
//        }
//    }
//
//
//    private Map<String,String> getMasterFeature(List<String>masterList,String columnName){
//        final HashMap<String, String> resultMap = new HashMap<>();
//        try {
//            final List<BatchGetRowResponse.RowResult> rowResults = tableStoreClient
//                    .batchQueryByList(FieldConstant.T_MASTER
//                            , masterList, FieldConstant.MASTER_ID,
//                            columnName);
//            for (BatchGetRowResponse.RowResult rowResult : rowResults) {
//                final Row row = rowResult.getRow();
//                if (row!=null) {
//                    final String masterId = row.getPrimaryKey()
//                            .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                    final String coreDivisionId = tableStoreClient.getValue(row, columnName);
//                    resultMap.put(masterId,coreDivisionId);
//                }
//            }
//        }catch (Exception e){
//        }
//        return resultMap;
//    }
//
//    /**
//     * 订单包师傅排序
//     * @param masterSortList
//     * @param packageMasterIds
//     */
//    private void sortPackageMaster(ArrayList<String> masterSortList,HashMap<String, OrderPackageMaster> packageMasterIds){
//        Collections.sort(masterSortList, (o1, o2) -> {
//            final OrderPackageMaster groupMaster1 = packageMasterIds.get(o1);
//            final OrderPackageMaster groupMaster2 = packageMasterIds.get(o2);
//            final long bindPackageCreateTime1 = groupMaster1.getBindPackageCreateTime();
//            final long bindPackageCreateTime2 = groupMaster2.getBindPackageCreateTime();
//            if (bindPackageCreateTime1>bindPackageCreateTime1) {
//                return 1;
//            }else if (bindPackageCreateTime1==bindPackageCreateTime2) {
//                final int remainPushOrderNumber1 = groupMaster1.getRemainPushOrderNumber();
//                final int remainPushOrderNumber2 = groupMaster2.getRemainPushOrderNumber();
//                if (remainPushOrderNumber1>remainPushOrderNumber2) {
//                    return -1;
//                }else if (remainPushOrderNumber1==remainPushOrderNumber2) {
//                    return 0;
//                }else {
//                    return 1;
//                }
//            }else {
//                return -1;
//            }
//        });
//    }
//
//    /**
//     * 订单包核心区域排序
//     * @param thirdDivisionId
//     * @param masterSortList
//     * @param tMasterFeature
//     */
//    private String getSortedPackageMasterDivision(String thirdDivisionId,
//                                                  ArrayList<String> masterSortList,
//                                                  Map<String, String> tMasterFeature){
//        final ArrayList<String> coreMasterList = new ArrayList<>();
//        for (String master : masterSortList) {
//            final String coreDivisionId = tMasterFeature.get(master);
//            if (thirdDivisionId.equals(coreDivisionId)) {
//                coreMasterList.add(master);
//            }
//        }
//
//        if (coreMasterList.size()>1) {
//            Collections.shuffle(coreMasterList);
//            return coreMasterList.get(0);
//        }else if (coreMasterList.size()==1) {
//            return coreMasterList.get(0);
//        }else {
//            Collections.shuffle(masterSortList);
//            return masterSortList.get(0);
//        }
//
//    }
//    /**
//     *预约冲突过滤
//     * @return
//     */
//    public static final String ORDER_RESERVE_INFO = "t_order_serve_info";
//    public static final String ORDER_RESERVE_INDEX = "order_serve_info_index_v1";
//    private Set<String> reserveMasterFilter(String startTime,String endTime,HashSet<String> masterIds){
//        if (startTime==null||endTime==null) {
//            return masterIds;
//        }
//        Set<String> removedIds=new HashSet<>();
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID,
//                masterIds));
//        reserveMasterQuery(startTime, endTime,mustQueryList);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(ORDER_RESERVE_INFO,
//                ORDER_RESERVE_INDEX, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        log.info("reserveMasterFilter rows:[{}]",rows.size());
//        for (Row row : rows) {
//            if (row != null) {
//                final String conflictMasterId = row.getPrimaryKey()
//                        .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                masterIds.remove(conflictMasterId);
//                removedIds.add(conflictMasterId);
//            }
//        }
//        return removedIds;
//    }
//
//    private void reserveMasterQuery(String startTime,String endTime,List<Query> mustQueryList){
//        Long startTimestamp= DateFormatterUtil.timeToTimestamp(startTime);
//        Long endTimestamp=DateFormatterUtil.timeToTimestamp(endTime);
//        //状态过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.SERVE_STATUS,
//                FieldConstant.SERVING));
//
//        //时间过滤-start
//        BoolQuery startBool = new BoolQuery();
//        startBool.setMustQueries(Arrays.asList(
//                tableStoreClient.longCompareQuery(FieldConstant.RESERVE_START_TIME,
//                        RangeQueryType.LESS_THAN_OREQUAL,
//                        startTimestamp),
//                tableStoreClient.longCompareQuery(FieldConstant.RESERVE_END_TIME,
//                        RangeQueryType.GREATER_THAN_OREQUAL,
//                        startTimestamp)
//        ));
//        //时间过滤-end
//        BoolQuery endBool = new BoolQuery();
//        endBool.setMustQueries(Arrays.asList(
//                tableStoreClient.longCompareQuery(FieldConstant.RESERVE_START_TIME,
//                        RangeQueryType.LESS_THAN_OREQUAL,
//                        endTimestamp),
//                tableStoreClient.longCompareQuery(FieldConstant.RESERVE_END_TIME,
//                        RangeQueryType.GREATER_THAN_OREQUAL,
//                        endTimestamp)
//        ));
//
//        //时间过滤-must
//        BoolQuery timeBool = new BoolQuery();
//        timeBool.setShouldQueries(Arrays.asList(startBool,endBool));
//        timeBool.setMinimumShouldMatch(1);
//
//        mustQueryList.add(timeBool);
//    }
//
//    /**
//     * 【推单组】订单包支持配置到商品属性级别
//     * 1.订单数量到子属性级别
//     * 3.配置到子属性的订单包只接收配置到子属性级别的订单->筛选订单包时 TODO ↑能不能合并?
//     */
//    public static final String MASTER_ORDER_PACKAGE = "t_master_order_package";
//    private static final String MASTER_ORDER_PACKAGE_INDEX = "master_order_package_index_v1";
//    public static final String MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY = "master_package_order_push_number_daily";
//    private HashMap<String, OrderPackageMaster> getOrderPackageMasterIds(Long masterOrderId, String orderVersion,
//                                                                         HashMap<String, PackageBase> packageBaseMap,
//                                                                         Set<String> packageSet, HashSet<String> masterIds,
//                                                                         String serveIds,
//                                                                         Set<String> attributeSet) {
//
//        HashMap<String, OrderPackageMaster> orderPackageMasterMap=new HashMap<>();
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(PACKAGE_CONFIG_ID,
//                packageSet));
//        setOrderPackageMasterQuery(mustQueryList);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(MASTER_ORDER_PACKAGE,
//                MASTER_ORDER_PACKAGE_INDEX, searchQuery);
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        //设置为返回指定列。
//        columnsToGet.setColumns(Arrays.asList(
//                REMAIN_PUSH_ORDER_NUMBER,
//                MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY, PushRecordTableStoreService.PUSH_COUNT_TAG,
//                FieldConstant.PACKAGE_ID));
//        searchRequest.setColumnsToGet(columnsToGet);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row != null) {
//                final String packageConfigId = row.getPrimaryKey()
//                        .getPrimaryKeyColumn(PACKAGE_CONFIG_ID).getValue().asString();
//                final String masterId = row.getPrimaryKey()
//                        .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                final PackageBase packageBase = packageBaseMap.get(packageConfigId);
//                /**
//                 * 每日推送上限
//                 */
//                final Long maxPushNumberDaily = packageBase.getMaxPushNumberDaily();
//                Long orderNum=getPackageOrderDailyNum(row,orderVersion);
//
//                final Long pushPackageOrderNumToday = (orderNum==null?0:orderNum);
//                if (maxPushNumberDaily!=null&&pushPackageOrderNumToday>=maxPushNumberDaily) {
//                    continue;
//                }
//
//                masterIds.add(masterId);
//                final Long createTime = packageBase.getCreateTime();
//                if (orderPackageMasterMap.containsKey(masterId)) {
//                    final OrderPackageMaster orderPackageMaster = orderPackageMasterMap.get(masterId);
//                    final Long bindPackageCreateTime = orderPackageMaster.getBindPackageCreateTime();
//                    if (bindPackageCreateTime>createTime) {
//                        orderPackageMaster.setBindPackageConfigId(packageConfigId);
//                        orderPackageMaster.setBindPackageCreateTime(createTime);
//                        orderPackageMaster.setBindPackageId(
//                                tableStoreClient.getValue(row,FieldConstant.PACKAGE_ID));
//                    }
//                }else {
//                    orderPackageMasterMap
//                            .put(masterId, OrderPackageMaster.GroupMasterBuilder.aGroupMaster()
//                                    .withMasterId(masterId)
//                                    .withRemainPushOrderNumber(
//                                            Integer.valueOf(tableStoreClient.getValue(row,
//                                                    REMAIN_PUSH_ORDER_NUMBER))
//                                    )
//                                    .withBindPackageId(
//                                            tableStoreClient.getValue(row,FieldConstant.PACKAGE_ID))
//                                    .withBindPackageConfigId(packageConfigId)
//                                    .withBindPackageCreateTime(createTime)
//                                    .build());
//                }
//
//            }
//        }
//        /**
//         * 订单包订单指派数量限制 WORKFLOW
//         */
//        log.info("master_order_id:{}::before appoint limit:{}",masterOrderId,masterIds);
//        if (masterIds.size()!=0) {
//            appointOrderLimit(serveIds,attributeSet,masterIds,packageSet,orderPackageMasterMap,packageBaseMap);
//            log.info("master_order_id:{}::after appoint limit:{}",masterOrderId,masterIds);
//        }
//        return orderPackageMasterMap;
//    }
//
//    /**
//     * 指派上限过滤
//     */
//    private void appointOrderLimit(String serveIds, Set<String> attributeSet, HashSet<String> masterIds,
//                                   Set<String> packageSet, HashMap<String, OrderPackageMaster> orderPackageMasterMap, HashMap<String, PackageBase> packageBaseMap){
//        final HashMap<String, HashSet<String>> packageKeyMaster = new HashMap<>();
//        //获取订单包与师傅的对应关系
//        for (Map.Entry<String, OrderPackageMaster> row : orderPackageMasterMap.entrySet()) {
//            String masterId=row.getKey();
//            final String bindPackageConfigId = row.getValue().getBindPackageConfigId();
//            LocalCollectionsUtil.addHashSetValueToGenericMap(packageKeyMaster,bindPackageConfigId,masterId);
//        }
//        final HashSet<String>  availableMasterIds= new HashSet<>();
//        for (Map.Entry<String, HashSet<String>> entry : packageKeyMaster.entrySet()) {
//            final String bindPackageConfigId = entry.getKey();
//            final HashSet<String> masterIdSet = entry.getValue();
//            final PackageBase packageBase = packageBaseMap.get(bindPackageConfigId);
//            availableMasterIds.addAll(appointOrderLimitOTS(serveIds, attributeSet, masterIdSet,bindPackageConfigId,packageBase));
//        }
//        masterIds.retainAll(availableMasterIds);
//    }
//
//    /**
//     * 查出来可以推的
//     * @param serveIds
//     * @param attributeSet
//     * @param mustQueryList
//     */
//    private void setAppointOrderQuery(PackageBase packageBase,String serveIds,Set<String> attributeSet,List<Query> mustQueryList){
//        //指派数量过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.SERVE_ID,
//                serveIds));
//        if (packageBase.getIsMatchedAttribute() == 1 && attributeSet!=null&&attributeSet.size()>0) {
//            mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.ATTRIBUTE_KEY,
//                    attributeSet));
//        }
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.IS_DELETE,
//                "0"));
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                REMAIN_APPOINT_ORDER_NUMBER,
//                RangeQueryType.GREATER_THAN,
//                0L
//        ));
//        //尺寸
//        final String matchedAttributeSizeMin = packageBase.getMatchedAttributeSizeMin();
//        final String matchedAttributeSizeMax = packageBase.getMatchedAttributeSizeMax();
//        if (matchedAttributeSizeMin!=null&&matchedAttributeSizeMax!=null) {
//            mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.ATTRIBUTE_VALUE_MIN,
//                    matchedAttributeSizeMin));
//            mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.ATTRIBUTE_VALUE_MAX,
//                    matchedAttributeSizeMax));
//        }
//    }
//
//    //	public static final String SMC_PACKAGE_REMAIN_APPOINT = "smc_package_remain_appoint";
////	public static final String SMC_PACKAGE_REMAIN_APPOINT_INDEX = "smc_package_remain_appoint_index";
//    public static final String MASTER_ORDER_PACKAGE_APPOINT = "t_master_order_package_appoint";
//    public static final String MASTER_ORDER_PACKAGE_APPOINT_INDEX = "master_order_package_appoint_index_v1";
//    private Set<String> appointOrderLimitOTS(String serveIds,Set<String> attributeSet,HashSet<String> masterIds,
//                                             String packageConfigId,PackageBase packageBase){
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(PACKAGE_CONFIG_ID,
//                packageConfigId));
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID,
//                masterIds));
//        setAppointOrderQuery(packageBase,serveIds, attributeSet,mustQueryList);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(MASTER_ORDER_PACKAGE_APPOINT,
//                MASTER_ORDER_PACKAGE_APPOINT_INDEX, searchQuery);
//
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        // 设置为返回指定列。
//        columnsToGet.setColumns(Arrays.asList(FieldConstant.MASTER_ID));
//        searchRequest.setColumnsToGet(columnsToGet);
//
//
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        final HashSet<String> availableMasterIds = new HashSet<>();
//        for (Row row : rows) {
//            if (row != null) {
//                log.info("appointOrderLimitOTS final");
//                final String availableMasterId = tableStoreClient.getValue(row,FieldConstant.MASTER_ID);
//                availableMasterIds.add(availableMasterId);
//            }
//        }
//        log.info("appointOrderLimitOTS debug::availableMasterIds{}::packageConfigId:{}" +
//                        ",masterIds:{},serveIds:{},attributeSet:{},packageBase:{}",availableMasterIds,
//                packageConfigId,masterIds,serveIds,attributeSet,packageBase);
//        return availableMasterIds;
//    }
//
//
//
//    private Long getPackageOrderDailyNum(Row row,String orderVersion){
//        Long orderNum=tableStoreClient.getLongValue(row, MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY);
//        try {
//            final String pushCountTag = tableStoreClient.getValue(row, PushRecordTableStoreService.PUSH_COUNT_TAG);
//            final String currentDate = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
//            if (!currentDate.equals(pushCountTag)) {
//                orderNum=0L;
//            }
//        }catch (Exception e){
//            log.error("getPackageOrderDailyNum error");
//            orderNum=0L;
//        }
//        return orderNum;
//    }
//
//    private static final String ORDER_REFUND = "t_order_refund";
//    private static final String ORDER_REFUND_INDEX = "order_refund_index_v1";
//    private Set<String> filterRefundMaster(
//            MasterMatchCondition masterCondition,
//            String orderUserId,
//            HashSet<String> masterIds) {
//        Set<String> removedIds=new HashSet<>();
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList = new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID,
//                masterIds));
//        setOrderRefundQuery(masterCondition,orderUserId,mustQueryList);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(ORDER_REFUND,
//                ORDER_REFUND_INDEX, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while (resp.getNextToken() != null) {
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()) {
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row != null) {
//                final String refundMasterId = row.getPrimaryKey()
//                        .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                masterIds.remove(refundMasterId);
//                removedIds.add(refundMasterId);
//            }
//        }
//        return removedIds;
//    }
//
//
//    private static final String REFUND_TIME="refund_time";
//    private void setOrderRefundQuery(MasterMatchCondition masterCondition,String orderUserId,List<Query> mustQueryList){
//        //用户过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.USER_ID,
//                orderUserId));
//        //服务过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.ORDER_SERVE_TYPE,
//                String.valueOf(masterCondition.getServeType())));
//        //时间过滤
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                REFUND_TIME,
//                RangeQueryType.GREATER_THAN,
////				DateFormatterUtil.getAssignDayTimeStampMils(-2)
//                DateFormatterUtil.getAssignMinutesTimeStampMils(-120)
//        ));
//    }
//
//
//
//
//
//    /*
//     */
//    private static final String HAS_PAUSE_PUSH="has_pause_push";
//    private static final String REMAIN_PUSH_ORDER_NUMBER ="remain_push_order_number";
//    private static final String REMAIN_APPOINT_ORDER_NUMBER ="remain_appoint_order_number";
//    private static final String STATEMENT_STATUS="statement_status";
//    private static final String HAS_RECYCLE="has_recycle";
//    private void setOrderPackageMasterQuery(List<Query> mustQueryList){
////		mustQueryList.add(OTSUtil.longCompareQuery(
////				REMAIN_PUSH_ORDER_NUMBER,
////				RangeQueryType.GREATER_THAN,
////				0L
////		));
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                REMAIN_APPOINT_ORDER_NUMBER,
//                RangeQueryType.GREATER_THAN,
//                0L
//        ));
//        mustQueryList.add(tableStoreClient.stringTermQuery(STATEMENT_STATUS,"pre"));
//        mustQueryList.add(tableStoreClient.stringTermQuery(HAS_PAUSE_PUSH,"0"));
//        mustQueryList.add(tableStoreClient.stringTermQuery(HAS_RECYCLE,"0"));
//    }
//
//
//    /**
//     * account_type-user查询
//     * - platform_special_user_flag
//     * - platform_special_user_ids
//     * account_type-enterprise查询
//     * - enterprise_special_user_flag
//     * - enterprise_special_user_ids
//     * - special_enterprise_ids
//     * @param mustQueryList
//     * @param masterCondition
//     * @param accountType
//     * @param accountId
//     * @param userId
//     */
//    private static final String PLATFORM_SPECIAL_USER_FLAG="platform_special_user_flag";
//    private static final String PLATFORM_SPECIAL_USER_IDS="platform_special_user_ids";
//    private static final String ENTERPRISE_SPECIAL_USER_FLAG="enterprise_special_user_flag";
//    private static final String ENTERPRISE_SPECIAL_USER_IDS="enterprise_special_user_ids";
//    private static final String SPECIAL_ENTERPRISE_IDS="special_enterprise_ids";
//    private static final String PACKAGE_SOURCE="source";
//    private void setOrderPackageSourceQuery(List<Query> mustQueryList
//            ,String orderUserId,String orderEnterpriseId){
//        if (FieldConstant.NONE.equals(orderEnterpriseId)) {
//
//            //用户订单
//            BoolQuery userFlagQuery = new BoolQuery();
//
//            //TODO
//            GetPersonaGroupIdsByUserIdRqt rqt = new GetPersonaGroupIdsByUserIdRqt();
//            rqt.setUserId(Long.valueOf(orderUserId));
//            rqt.setAppId(5);
//            GetPersonaGroupIdsByUserIdResp resp = tools.catchLogThrow(() -> bigdataOpenServiceApi.getPersonaGroupIdsByUserId(rqt));
//
//
//            //匹配订单包绑定人群 TODO
//            if(Objects.nonNull(resp) && StringUtils.isNotBlank(resp.getGroupIds())){
//                String groupIds = resp.getGroupIds();
//                userFlagQuery.setShouldQueries(Arrays.asList(
//                        tableStoreClient.stringTermQuery(PLATFORM_SPECIAL_USER_FLAG,"0"),
//                        tableStoreClient.stringTermQuery(PLATFORM_SPECIAL_USER_IDS, orderUserId),
//                        tableStoreClient.stringTermsQuery("platform_special_user_group_ids",Arrays.asList(groupIds.split(",")))));
//            }else{
//                userFlagQuery.setShouldQueries(Arrays.asList(
//                        tableStoreClient.stringTermQuery(PLATFORM_SPECIAL_USER_FLAG,"0"),
//                        tableStoreClient.stringTermQuery(PLATFORM_SPECIAL_USER_IDS, orderUserId)));
//            }
//
//
//
//            userFlagQuery.setMinimumShouldMatch(1);
//
//            BoolQuery userBool = new BoolQuery();
//            userBool.setMustQueries(Arrays.asList(
//                    tableStoreClient.stringTermQuery(PACKAGE_SOURCE,"platform"),
//                    userFlagQuery
//            ));
//            mustQueryList.add(userBool);
//
//
//
//
//        }else {
//            //总包订单
//            BoolQuery userBool = new BoolQuery();
//            userBool.setShouldQueries(Arrays.asList(
//                    tableStoreClient.stringTermQuery(ENTERPRISE_SPECIAL_USER_FLAG,"0"),
//                    tableStoreClient.stringTermQuery(ENTERPRISE_SPECIAL_USER_IDS, orderUserId)
//            ));
//            userBool.setMinimumShouldMatch(1);
//            BoolQuery enterpriseBool = new BoolQuery();
//            enterpriseBool.setMustQueries(Arrays.asList(
//                    tableStoreClient.stringTermQuery(PACKAGE_SOURCE,"enterprise"),
//                    tableStoreClient.stringTermQuery(SPECIAL_ENTERPRISE_IDS,orderEnterpriseId),
//                    userBool
//            ));
//            mustQueryList.add(enterpriseBool);
//        }
//    }
//
//    /**
//     * 人群包过滤
//     * @param mustQueryList
//     * @param orderUserId
//     * @param orderEnterpriseId
//     */
//    private void setOrderPackageUserGroupQuery(List<Query> mustQueryList
//            ,String orderUserId,String orderEnterpriseId) {
//
//        if (FieldConstant.NONE.equals(orderEnterpriseId)) {
//
//                        //用户人群
////            final String groupIds = pushDataCenterRepository.getUserBaseFeature(
////                    Long.valueOf(orderUserId), "group_ids");
//
//            //TODO
//            GetPersonaGroupIdsByUserIdRqt rqt = new GetPersonaGroupIdsByUserIdRqt();
//            rqt.setUserId(Long.valueOf(orderUserId));
//            rqt.setAppId(5);
//            GetPersonaGroupIdsByUserIdResp resp = tools.catchLogThrow(() -> bigdataOpenServiceApi.getPersonaGroupIdsByUserId(rqt));
//
//            //匹配订单包绑定人群 TODO
//            if(Objects.nonNull(resp) && StringUtils.isNotBlank(resp.getGroupIds())){
//                String groupIds = resp.getGroupIds();
//                BoolQuery groupQuery = new BoolQuery();
//                groupQuery.setShouldQueries(Arrays.asList(
//                        tableStoreClient.stringTermsQuery("platform_special_user_group_ids",Arrays.asList(groupIds.split(","))),
//                        tableStoreClient.stringTermQuery("platform_special_user_group_ids","0")
//                ));
//                groupQuery.setMinimumShouldMatch(1);
//
//                mustQueryList.add(groupQuery);
//            }else{
//                mustQueryList.add(tableStoreClient.stringTermQuery("platform_special_user_group_ids","0"));
//            }
//
//
//        }
//
//
//
//    }
//
//    /**
//     * 服务,地区 过滤
//     * @param masterCondition
//     */
//    private static final String SERVE_IDS="serve_ids";
//    private static final String LV3_DIVISION_IDS="lv3_division_ids";
//    private static final String LV4_DIVISION_IDS="lv4_division_ids";
//    private void setOrderPackageServeDivisionQuery(List<Query> mustQueryList,MasterMatchCondition masterCondition) {
//        /**
//         * 服务表结构改造,不再支持分词索引同步
//         */
//        mustQueryList.add(tableStoreClient.stringTermQuery(SERVE_IDS,
//                masterCondition.getServeIds()));
////--------------------------------------------
//
//        /**
//         * 		mustQueryList.add(OTSUtil.stringMatchQuery("serve_ids",
//         * 		masterCondition.getServeIds(), QueryOperator.AND));
//         */
//        //由于门,定制/存在特殊处理,且订单包不做区域特殊处理,需再次判断区域处理等级
//        if (masterCondition.getFourthDivisionId()!=null
//                &&!SymbolConstant.ZERO.equals(String.valueOf(masterCondition.getFourthDivisionId()))) {
//            //lv 4
//            mustQueryList.add(tableStoreClient.stringTermQuery(LV4_DIVISION_IDS,
//                    String.valueOf(masterCondition.getFourthDivisionId())));
//            masterCondition.setDivisionMatchLevel(4);
//        }else{
//            //lv 3
//            mustQueryList.add(tableStoreClient.stringTermQuery(LV3_DIVISION_IDS,
//                    String.valueOf(masterCondition.getThirdDivisionId())));
//            masterCondition.setDivisionMatchLevel(3);
//        }
//    }
//
//
//
//    /**
//     * 进行中 过滤
//     * 开始推单时间
//     * 待指派未满
//     * 结算
//     * 订单包未被回收或暂停
//     * 是否可用
//     * 审核
//     * 结算时间
//     * @param masterCondition
//     */
//    private static final String AUDIT_STATUS="audit_status";
//    private static final String ENABLE_STATUS="enable_status";
//    private static final String ORDER_PUSH_START_TIME="order_push_start_time";
//    private static final String SETTLEMENT_TIME="settlement_time";
//    private static final String APPOINT_TYPE="appoint_type";
//    //TODO 指派类型 合作价格
//    private void setOrderPackageStatusQuery(List<Query> mustQueryList,MasterMatchCondition masterCondition,Integer appointType) {
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                ORDER_PUSH_START_TIME,
//                RangeQueryType.LESS_THAN_OREQUAL,
//                DateFormatterUtil.getNowTimeStampMil()
//        ));
//        mustQueryList.add(tableStoreClient.stringTermQuery(AUDIT_STATUS,"pass"));
//        mustQueryList.add(tableStoreClient.stringTermQuery(APPOINT_TYPE,String.valueOf(appointType)));
////		mustQueryList.add(OTSUtil.stringTermQuery(ENABLE_STATUS,"1"));
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                SETTLEMENT_TIME,
//                RangeQueryType.GREATER_THAN_OREQUAL,
//                DateFormatterUtil.getNowTimeStampMil()
//        ));
//
//    }
//
//    @Override
//    protected void afterMatch(MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
//
//    }
//
//
//    @Override
//    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
//        try {
//            if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
//                return false;
//            }
//            // 订单版本号
//            String orderVersion = orderDetailData.getOrderVersion();
//            Long timeStamp = Long.parseLong(orderVersion);
//            String operateFlag = orderDetailData.getPushExtraData().getOperateFlag();
//            String pushType = FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag) ? "order_package_match" : "order_package_push";
//            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), pushType);
//            //TODO 夜间推送
//            if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {
//
//                if(FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag)){
//                    //TODO 总包直接指派匹配订单包
////                    pushQueueService.sendDelayMatchOrderPackageMasterMesasge(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
//                }else{
//                    OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
//                    rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
//                    rqt.setBusinessLineId(orderDetailData.getBusinessLineId());
//                    rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
//                    pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
//                }
//                return true;
//            }
//
//            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
//            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
//            JSONObject commonFeature = new JSONObject();
//            commonFeature.put(FieldConstant.OPERATE_FLAG, orderDetailData.getPushExtraData().getOperateFlag());
//            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
//            //TODO 判断地址级别
//            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
//            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.PACKAGE_ORDER.code);
//            commonFeature.put(FieldConstant.PACKAGE_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_ID));
//            commonFeature.put(FieldConstant.PACKAGE_CONFIG_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_CONFIG_ID));
//            commonFeature.put(FieldConstant.PACKAGE_ATTRIBUTE_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_ATTRIBUTE_ID));
//            commonFeature.put(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE, matchMasterResult.getExtraData().getString(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE));
//            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
//            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
//            pushControllerFacade.packageOrderPush(orderDetailData, orderVersion, timeMark, matchMasterResult.getMasterIdSet(), commonFeature);
//
//
//            Long platformAutoMode = matchMasterResult.getExtraData().getLongValue(FieldConstant.PLATFORM_AUTO_MODE);
//            if(AppointType.OPEN.value.equals(orderDetailData.getAppointType()) && Objects.nonNull(platformAutoMode) && platformAutoMode == 1){
//                OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
//                rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
//                rqt.setBusinessLineId(orderDetailData.getBusinessLineId());
//                rqt.setPushMode("normal");
//                rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
//                pushQueueService.sendDelayPushMessage(1000L,JSON.toJSONString(rqt));
//            }
////            orderMatchRouteService.orderMatchRoute("order_package",orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
//            return true;
//        }catch(Exception e){
//            log.error(String.format("执行代理商推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
//        }
//        return false;
//    }
//
//    public Long getSecondDayTimestamp(){
//        Calendar calendar = Calendar.getInstance();
//        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
//            calendar.add(Calendar.DATE,1);
//        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
//        }
//        calendar.set(Calendar.HOUR_OF_DAY,8);
//        calendar.set(Calendar.MINUTE,0);
//        return calendar.getTimeInMillis();
//    }
//
//
//
//}
