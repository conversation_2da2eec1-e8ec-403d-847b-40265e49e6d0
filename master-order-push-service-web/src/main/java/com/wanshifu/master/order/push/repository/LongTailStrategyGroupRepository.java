package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply.LongTailStrategyMatcher;
import com.wanshifu.master.order.push.mapper.LongTailStrategyGroupMapper;
import com.wanshifu.master.order.push.mapper.LongTailStrategyMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略Repsitory
 * <AUTHOR>
 */
@Repository
public class LongTailStrategyGroupRepository extends BaseRepository<LongTailStrategyGroup> {

    @Resource
    private LongTailStrategyGroupMapper longTailStrategyGroupMapper;

    @Resource
    private LongTailStrategyMapper longTailStrategyMapper;


    public int insert(
            String longTailStrategyGroupName,
            String longTailStrategyGroupDesc,
            Integer businessLineId,
            String pushType,
            String categoryIds,
            String openCityMode,
            String cityIds,
            String strategyJson,
            String strategyExpression,
            Long updateAccountId,
            Long createAccountId
    ){
        final LongTailStrategyGroup longTailStrategyGroup = new LongTailStrategyGroup();
        longTailStrategyGroup.setLongTailStrategyGroupName(longTailStrategyGroupName);
        longTailStrategyGroup.setLongTailStrategyGroupDesc(longTailStrategyGroupDesc);
        longTailStrategyGroup.setBusinessLineId(businessLineId);
        longTailStrategyGroup.setPushType(pushType);
        longTailStrategyGroup.setCategoryIds(categoryIds);
        longTailStrategyGroup.setOpenCityMode(openCityMode);
        longTailStrategyGroup.setCityIds(cityIds);
        longTailStrategyGroup.setStrategyJson(strategyJson);
        longTailStrategyGroup.setStrategyExpression(strategyExpression);
        longTailStrategyGroup.setUpdateAccountId(updateAccountId);
        longTailStrategyGroup.setCreateAccountId(createAccountId);
        return longTailStrategyGroupMapper.insertSelective(longTailStrategyGroup);
    }

    public int update(
            Long longTailStrategyGroupId,
            String longTailStrategyGroupName,
            String longTailStrategyGroupDesc,
            Integer businessLineId,
            String pushType,
            String categoryIds,
            String openCityMode,
            String cityIds,
            String strategyJson,
            String strategyExpression,
            Long updateAccountId,
            Long createAccountId
    ){
        final LongTailStrategyGroup longTailStrategyGroup = new LongTailStrategyGroup();
        longTailStrategyGroup.setLongTailStrategyGroupId(longTailStrategyGroupId);
        longTailStrategyGroup.setLongTailStrategyGroupName(longTailStrategyGroupName);
        longTailStrategyGroup.setLongTailStrategyGroupDesc(longTailStrategyGroupDesc);
        longTailStrategyGroup.setBusinessLineId(businessLineId);
        longTailStrategyGroup.setPushType(pushType);
        longTailStrategyGroup.setCategoryIds(categoryIds);
        longTailStrategyGroup.setOpenCityMode(openCityMode);
        longTailStrategyGroup.setCityIds(cityIds);
        longTailStrategyGroup.setStrategyJson(strategyJson);
        longTailStrategyGroup.setStrategyExpression(strategyExpression);
        longTailStrategyGroup.setUpdateAccountId(updateAccountId);
        longTailStrategyGroup.setCreateAccountId(createAccountId);
        return longTailStrategyGroupMapper.updateByPrimaryKeySelective(longTailStrategyGroup);
    }

    public int updateStatus(
            Long longTailStrategyGroupId,
            Integer isActive,
            Long updateAccountId
    ){
        final LongTailStrategyGroup longTailStrategyGroup = new LongTailStrategyGroup();
        longTailStrategyGroup.setLongTailStrategyGroupId(longTailStrategyGroupId);
        longTailStrategyGroup.setIsActive(isActive);
        longTailStrategyGroup.setUpdateAccountId(updateAccountId);
        return longTailStrategyGroupMapper.updateByPrimaryKeySelective(longTailStrategyGroup);
    }

    public int delete(
            Long longTailStrategyGroupId
    ){
        final LongTailStrategyGroup longTailStrategyGroup = new LongTailStrategyGroup();
        longTailStrategyGroup.setLongTailStrategyGroupId(longTailStrategyGroupId);
        longTailStrategyGroup.setIsDelete(1);
        return longTailStrategyGroupMapper.updateByPrimaryKeySelective(longTailStrategyGroup);
    }


    public List<LongTailStrategyGroup> selectList(Long businessLineId,
            String longTailStrategyGroupName, Integer isActive,Date createStartTime,Date createEndTime,
                                             List<String> categoryIdList,String cityId
    ) {
        return longTailStrategyGroupMapper.selectList(
                businessLineId,longTailStrategyGroupName, isActive, createStartTime,createEndTime,
                categoryIdList,cityId);
    }

    public LongTailStrategyGroup selectByStrategyId(Long strategyId) {
        LongTailStrategyGroup longTailStrategyGroup = longTailStrategyGroupMapper.selectByPrimaryKey(strategyId);
        Assert.isTrue(longTailStrategyGroup != null && Objects.equals(longTailStrategyGroup.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return longTailStrategyGroup;
    }

    public LongTailStrategyGroup selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Long strategyId) {
        Example example = new Example(LongTailStrategyGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("longTailStrategyGroupName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (strategyId != null) {
            criteria.andNotEqualTo("longTailStrategyGroupId", strategyId);
        }
        return CollectionUtils.getFirstSafety(longTailStrategyGroupMapper.selectByExample(example));
    }


    public LongTailStrategyGroup selectByCityAndCategory(
            String cityIdStr,
            List<String> categoryIdList,
            Integer businessLineId,
            Long longTailStrategyGroupId
    ) {
        return longTailStrategyGroupMapper.selectByCityAndCategory(
                cityIdStr,
                categoryIdList,
                businessLineId,
                longTailStrategyGroupId
        );
    }

    private static final String LONG_TAIL_STRATEGY_ID="longTailStrategyId";


    public List<LongTailStrategyGroup> selectByCityAndCategoryActive(
            String cityIdStr,
            List<String> categoryIdList,
            Integer businessLineId,
            Long longTailStrategyGroupId
    ) {
        final StringJoiner stringJoiner = new StringJoiner(",");
        stringJoiner.add("all");
        stringJoiner.add(cityIdStr);
        categoryIdList.add("0");
        categoryIdList.add("all");
        final List<LongTailStrategyGroup> longTailStrategyGroups = longTailStrategyGroupMapper.selectByCityAndCategoryActive(
                stringJoiner.toString(),
                categoryIdList,
                businessLineId,
                longTailStrategyGroupId
        );


        if (longTailStrategyGroups.size()==0) {
            return null;
        }

        if (longTailStrategyGroups.size()==1) {
            return Collections.singletonList(longTailStrategyGroups.get(0));
        }else {

            List<LongTailStrategyGroup> bonusOrderStrategyGroupList = new ArrayList<>();
            List<LongTailStrategyGroup> nearbyMoreStrategyGroupList = new ArrayList<>();


            longTailStrategyGroups.forEach(longTailStrategyGroup -> {
                final String strategyExpression = longTailStrategyGroup.getStrategyExpression();
                final JSONArray array = JSONArray.parseArray(strategyExpression);

                final HashMap<Long, LongTailStrategyMatcher.LongTailStrategyGroupCondition> conditionMap = new HashMap<>();
                final JSONObject row = array.getJSONObject(0);
                final Long longTailStrategyId = row.getLong(LONG_TAIL_STRATEGY_ID);
                LongTailStrategy longTailStrategy = longTailStrategyMapper.selectByPrimaryKey(longTailStrategyId);
                if("bonus_order".equals(longTailStrategy.getPushType())){
                    bonusOrderStrategyGroupList.add(longTailStrategyGroup);
                }else{
                    nearbyMoreStrategyGroupList.add(longTailStrategyGroup);
                }
            });


            List<LongTailStrategyGroup> resultList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(bonusOrderStrategyGroupList)){
                resultList.add(getLongTailStrategyGroup(bonusOrderStrategyGroupList));
            }

            if(CollectionUtils.isNotEmpty(nearbyMoreStrategyGroupList)){
                resultList.add(getLongTailStrategyGroup(nearbyMoreStrategyGroupList));
            }

            return resultList;


        }
    }

    private LongTailStrategyGroup getLongTailStrategyGroup(List<LongTailStrategyGroup> longTailStrategyGroups){
        /**
         * 长尾单策略匹配优先级规则：
         * 优先级1:具体类目+具体城市
         * 优先级2:具体类目+不限城市
         * 优先级3:不限类目+具体城市
         * 优先级4:不限类目+不限城市
         */
        int maxLevel=0;
        int maxGroupIndex=0;
        for (int i = 0; i < longTailStrategyGroups.size(); i++) {
            final LongTailStrategyGroup longTailStrategyGroup = longTailStrategyGroups.get(i);
            final boolean isCity = longTailStrategyGroup.getOpenCityMode().equals(FieldConstant.CITY);
            final boolean isCategory = !SymbolConstant.ZERO.equals(longTailStrategyGroup.getCategoryIds());
            if (isCity&&isCategory) {
                return longTailStrategyGroups.get(i);
            }
            int level=0;
            if (isCategory) {
                level=level+2;
            }

            if (isCity) {
                level=level+1;
            }

            if (level>maxLevel) {
                maxLevel=level;
                maxGroupIndex=i;
            }
        }
        return longTailStrategyGroups.get(maxGroupIndex);
    }

}