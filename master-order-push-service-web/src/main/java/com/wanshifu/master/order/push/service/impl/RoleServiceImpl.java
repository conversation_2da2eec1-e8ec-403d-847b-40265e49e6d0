package com.wanshifu.master.order.push.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AccountRole;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.domain.resp.role.RoleDetailResp;
import com.wanshifu.master.order.push.domain.resp.role.RoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;
import com.wanshifu.master.order.push.repository.AccountRoleRepository;
import com.wanshifu.master.order.push.repository.PermissionSetRepository;
import com.wanshifu.master.order.push.repository.RoleRepository;
import com.wanshifu.master.order.push.service.RoleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl implements RoleService {

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private AccountRoleRepository accountRoleRepository;

    @Resource
    private PermissionSetRepository permissionSetRepository;

    @Override
    public int add(AddRoleRqt rqt){
        checkRoleName(rqt.getRoleName(),null);
       return roleRepository.insertRole(rqt.getRoleName(),rqt.getRoleDesc(),rqt.getCreateAccountId());
    }

    @Override
    public int update(UpdateRoleRqt rqt){
        checkRoleName(rqt.getRoleName(),rqt.getRoleId());
        return roleRepository.updateRole(rqt.getRoleId(),rqt.getRoleName(),rqt.getRoleDesc(),rqt.getUpdateAccountId());
    }

    @Override
    public SimplePageInfo<RoleListResp> list(GetRoleListRqt rqt){
        Page page = PageHelper.startPage(rqt.getPageNum(),rqt.getPageSize());
        List<Role> roleList = roleRepository.selectList(rqt.getRoleName(),rqt.getCreateTimeStart(),rqt.getCreateTimeEnd());

        Map<Integer, List<AccountRole>> accountRoleMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(roleList)){
            List<Integer> roleIdList = roleList.stream().map(Role::getRoleId).collect(Collectors.toList());
            List<AccountRole> accountRoleList = accountRoleRepository.selectByRoleIdList(roleIdList);
            accountRoleMap = accountRoleList.stream().collect(Collectors.groupingBy(AccountRole::getRoleId));
        }

        Map<Integer, List<AccountRole>> finalAccountRoleMap = accountRoleMap;
        List<RoleListResp> getRoleListRespList = new ArrayList<>();
        roleList.forEach(role -> {
            RoleListResp getRoleListResp = new RoleListResp();
            BeanUtils.copyProperties(role,getRoleListResp);
            getRoleListResp.setRoleUserNum(finalAccountRoleMap.getOrDefault(role.getRoleId(), Collections.emptyList()).size());
            getRoleListRespList.add(getRoleListResp);
        });
        SimplePageInfo<RoleListResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(getRoleListRespList);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional
    public int delete(DeleteRoleRqt rqt){
        Role role = roleRepository.selectByPrimaryKey(rqt.getRoleId());
        Assert.notNull(role,"角色不存在");
        List<PermissionSet> permissionSetList = permissionSetRepository.selectList(null,null,null,Collections.singletonList(rqt.getRoleId()));
        Assert.isTrue(CollectionUtils.isEmpty(permissionSetList),"角色已分配权限，请先删除权限");
        return roleRepository.deleteByPrimaryKey(rqt.getRoleId());
    }

    @Override
    public RoleDetailResp detail(GetRoleDetailRqt rqt){
        RoleDetailResp resp = null;
        Role role = roleRepository.selectByPrimaryKey(rqt.getRoleId());
        if(role != null){
            resp = new RoleDetailResp();
            BeanUtils.copyProperties(role,resp);
            List<AccountRole> accountRoleList = accountRoleRepository.selectByRoleIdList(Collections.singletonList(role.getRoleId()));
            if(CollectionUtils.isNotEmpty(accountRoleList)){
                List<Long> accountIdList = accountRoleList.stream().map(AccountRole::getAccountId).collect(Collectors.toList());
                resp.setAccountIdList(accountIdList);
            }
        }
        return resp;
    }


    @Override
    @Transactional
    public int addAccount(AddAccountRqt rqt){
        accountRoleRepository.deleteByRoleId(rqt.getRoleId());
        return accountRoleRepository.insertAccountRoleList(rqt.getRoleId(),rqt.getAccountIdList());
    }

    private void checkRoleName(String roleName,Integer roleId) {
        Role role = roleRepository.selectByRoleName(roleName, roleId);
        Assert.isNull(role, "已存在该角色");
    }


    @Override
    public List<Role> batchRoleList(BatchRoleListRqt rqt){
        return roleRepository.selectByRoleIdList(rqt.getRoleIdList());
    }

}
