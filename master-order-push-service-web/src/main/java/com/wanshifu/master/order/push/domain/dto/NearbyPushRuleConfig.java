package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 附近推单配置DTO
 * <AUTHOR>
 */
@Data
public class NearbyPushRuleConfig {

    /**
     * 下单模式
     */
    private Integer appointType;

    /**
     * 最佳报价数
     */
    private Integer bestOfferNum;

    /**
     * 附近推单延迟时间
     */
    private Integer delayMinutesTime;


    /**
     * 附近距离
     */
    private BigDecimal nearbyDistance;


}
