package com.wanshifu.master.order.push.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.domain.api.response.OrderPushAbTestResp;
import com.wanshifu.master.order.push.domain.api.rqt.OrderPushAbTestRqt;
import com.wanshifu.master.order.push.domain.common.PushFilterList;
import com.wanshifu.master.order.push.domain.common.PushScorerList;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.dto.OrderPushRule;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.message.PortPushMessage;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.resp.InviteOrderPushResp;
import com.wanshifu.master.order.push.domain.rqt.*;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.impl.OrderDataBuilder;
import com.wanshifu.master.order.push.service.impl.OrderMasterMatcher;
import com.wanshifu.master.order.push.service.impl.OrderMasterMatcherFactory;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.request.offer.GetOfferedPriceListByGlobalOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.offer.GetOfferedPriceListByGlobalOrderIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 推单
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderPushServiceImpl implements OrderPushService {


    @Resource
    private StrategyCombinationRepository strategyCombinationRepository;

    @Resource
    private BaseSelectStrategyRepository baseSelectStrategyRepository;


    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private BaseSelector baseSelector;


    @Resource
    private PushHandler pushHandler;

    @Resource
    private RepushPolicyRepository repushPolicyRepository;

    @Resource
    private MasterPushRepository masterPushRepository;


    @Resource
    private OrderMasterPushRepository  orderMasterPushRepository;

    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private QLExpressHandler qlExpressHandler;

    @Resource
    private RepushPolicySnapshotRepository repushPolicySnapshotRepository;

    @Resource
    private NearbyPushConfigRepository nearbyPushConfigRepository;

    @Resource
    private OrderDataBuilder orderDataBuilder;

    @Resource
    private HBaseClient hBaseClient;

//    @Resource
//    private PushDataCenterRepository pushDataCenterRepository;


    @Value("${extra.contest.offerNumber.push.percent:30}")
    private Integer extraContestOfferNumberPushPercent;



    @Resource
    private FeatureQueryFacade featureQueryFacade;

    @Value("${order.afresh.push.switch:on}")
    private String orderAfreshPushSwitch;


    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;


    @Value("${nearby.push.enable}")
    private boolean nearbyPushEnable;



    @Value("${wanshifu.rocketMQ.order-push-result-topic}")
    private String commonOrderGeneralTopic;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;


    @Resource
    private PushHandleRepository pushHandleRepository;

    @Resource
    private RocketMqSendService rocketMqSendService;


    @Value("${team.master.order.rematch.delayTime:10}")
    private Long teamMasterOrderRematchDelayTime;

    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;

    @Resource
    private MasterOrderRepository masterOrderRepository;


    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;


    @Resource
    private PushRuleRepository pushRuleRepository;


    @Resource
    private NewModelMatchService newModelMatchService;
    
    
    @Resource
    private LongTailPushService longTailPushService;



    @Value("${normal.push.master.size.limit:3000}")
    private int normalPushMasterSizeLimit;

    @Value("${interfereOrderPush.switch:on}")
    private String interfereOrderPushSwitch;


    /**
     * 订单推单
     * @param orderPushRqt
     * @return
     */
    @Override
    public int orderPush(OrderPushRqt orderPushRqt){
        Long timeStamp = System.currentTimeMillis();
        String orderVersion = String.valueOf(timeStamp);
        //获取订单详细数据
        OrderDetailData orderDetailData = new OrderDetailData(orderPushRqt,orderVersion);
        //构建初筛条件
        MasterMatchCondition masterMatchCondition = baseSelector.buildMasterMatchCondition(orderDetailData,orderPushRqt.getPushMode(),orderVersion);
        PushCommonObject pushCommonObject;
        pushCommonObject = new PushCommonObject();
        pushCommonObject.setOrderDetailData(orderDetailData);
        pushCommonObject.setOrderVersion(orderVersion);
        pushCommonObject.setMasterMatchCondition(masterMatchCondition);
        pushCommonObject.setTimestamp(timeStamp);
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        //根据策略推单
        pushByStrategy(pushCommonObject,timeMark,orderPushRqt,null);
        return 1;
    }


    /**
     * 根据策略推单
     * @param pushCommonObject
     * @param timeMark
     * @param orderPushRqt
     * @param repushPolicyRule
     */
    private void pushByStrategy(PushCommonObject pushCommonObject,String timeMark,OrderPushRqt orderPushRqt,RepushPolicyRule repushPolicyRule){
        OrderDetailData orderDetailData = pushCommonObject.getOrderDetailData();



        if(repushPolicyRule != null){
            //重新推单
//            RepushPolicyRule repushPolicyRule = JSON.parseObject(repushPolicySnapshot.getStrategyCombination(),RepushPolicyRule.class);
            StrategyEntityList strategyEntityList = getStrategyEntityList(repushPolicyRule.getStrategyCombination(), repushPolicyRule.getPushRule(), repushPolicyRule.getPushRuleId(), orderPushRqt.getAppointType(), pushCommonObject.getOrderDetailData());
            pushByStrategy(pushCommonObject.getRepushStrategySnapshotId(),orderPushRqt,strategyEntityList,null,pushCommonObject,timeMark,false,false,repushPolicyRule.getIsFilterPushedMaster());
        } else {
            //非重推时匹配策略组合
            String orderFlag = orderDetailData.getOrderFrom();
            if (!StringUtils.equals(orderFlag, "ikea")) {
                orderFlag = "normal";
            }
            StrategyCombination strategyCombination = this.getStrategyCombination(orderFlag, orderDetailData);

            if (strategyCombination != null) {
                log.info(String.format("订单匹配到推单策略组合,globalOrderId:%d,categoryId:%d,cityId:%d,strategyCombinationSnapshotId:%d", orderPushRqt.getGlobalOrderId(),
                        orderPushRqt.getCategoryId(), orderPushRqt.getSecondDivisionId(), strategyCombination.getSnapshotId()));
                PushStrategyRule priorityPushStrategyRule = JSON.parseObject(strategyCombination.getPriorityStrategyCombination(), PushStrategyRule.class);
                PushStrategyRule alternatePushStrategyRule = null;
                if (StringUtils.isNotBlank(strategyCombination.getAlternateStrategyCombination())) {
                    alternatePushStrategyRule = JSON.parseObject(strategyCombination.getAlternateStrategyCombination(), PushStrategyRule.class);
                }
                StrategyEntityList strategyEntityList = getStrategyEntityList(priorityPushStrategyRule.getStrategyList(), priorityPushStrategyRule.getPushRule(), priorityPushStrategyRule.getPushRuleId(), orderDetailData.getAppointType(), pushCommonObject.getOrderDetailData());
                pushByStrategy(strategyCombination.getSnapshotId(), orderPushRqt, strategyEntityList, alternatePushStrategyRule, pushCommonObject, timeMark, true, true, 0);
            } else {
                //未匹配到策略组合则发送mq消息给老推单系统进行推单（兜底策略，防止未配置推送策略组合）
                log.error("订单未匹配到推单策略组合,orderPushRqt:{}", JSON.toJSONString(orderPushRqt));
                feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"推单异常", String.format("【tag】：%s\n\n【orderDetailData】%s\n\n【错误原因】：%s",
                        "order_push_notices", JSON.toJSONString(pushCommonObject.getOrderDetailData()), "订单未匹配到推单策略组合"));
//                pushQueueService.sendSmartPushMsg(JSON.toJSONString(orderPushRqt));

            }
        }
    }

    private Integer getBusinessLineId(OrderDetailData orderDetailData){
        Integer businessLineId = 1;
        String masterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();
        if(orderDetailData.getBusinessLineId() == 1){
            businessLineId = 1;
        }else if(orderDetailData.getBusinessLineId() == 2 && OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom())){
            //家庭小程序订单推单分流新师傅APP
            businessLineId = MasterSourceType.TOB.code.equals(masterSourceType) ? 2 : 999;
        }else{
            businessLineId = orderDetailData.getBusinessLineId();
        }
        return businessLineId;
    }


    private StrategyCombination getStrategyCombination(String orderFlag, OrderDetailData orderDetailData) {

        Integer businessLineId = getBusinessLineId(orderDetailData);

        StrategyCombination strategyCombination = strategyCombinationRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), String.valueOf(orderDetailData.getSecondDivisionId()));

        if (strategyCombination == null) {
            //具体城市+不限类目
            strategyCombination = strategyCombinationRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, "all", String.valueOf(orderDetailData.getSecondDivisionId()));
        }

        if (strategyCombination == null) {
            //全国城市+具体类目
            strategyCombination = strategyCombinationRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), "all");
        }

        if (strategyCombination == null) {
            //全国城市+不限类目
            strategyCombination = strategyCombinationRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, "all", "all");
        }
        return strategyCombination;
    }

    /**
     * 获取推送规则
     * @param pushRuleList
     * @param appointType
     * @return
     */
    private PushConfig getPushConfig(List<OrderPushRule> pushRuleList, Integer appointType){
        PushConfig pushConfig = new PushConfig();
        if(CollectionUtils.isNotEmpty(pushRuleList)){
            OrderPushRule pushRule = pushRuleList.stream().filter(currentPushRule -> currentPushRule.getAppointType().equals(appointType)).findFirst().orElse(null);
            if(pushRule != null){
                Integer firstPushNewMasterNum = 0;
                Integer firstPushOldMasterNum = 0;
                Integer delayPushNewMasterNum = 0;
                Integer delayPushOldMasterNum = 0;
                if(MasterType.MASETR_NEW.getCode().equals(pushRule.getFirstPushMasterFlag())){
                    //首轮推送模式：推送新师傅
                    firstPushNewMasterNum = (new BigDecimal(pushRule.getFirstPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getFirstPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    firstPushOldMasterNum = pushRule.getFirstPushMasterNumPerRound() - firstPushNewMasterNum;
                }else if(MasterType.MASTER_OLD.getCode().equals(pushRule.getFirstPushMasterFlag())){
                    //首轮推送模式：推送老师傅
                    firstPushOldMasterNum = (new BigDecimal(pushRule.getFirstPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getFirstPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    firstPushNewMasterNum = pushRule.getFirstPushMasterNumPerRound() - firstPushOldMasterNum;
                }else{
                    //首轮推送模式：不区分新老师傅
                    firstPushOldMasterNum = pushRule.getFirstPushMasterNumPerRound();
                }

                if(MasterType.MASETR_NEW.getCode().equals(pushRule.getDelayPushMasterFlag())){
                    //非首轮推送模式：推送新师傅
                    delayPushNewMasterNum = (new BigDecimal(pushRule.getDelayPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getDelayPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    delayPushOldMasterNum = pushRule.getDelayPushMasterNumPerRound() - delayPushNewMasterNum;
                }else if(MasterType.MASTER_OLD.getCode().equals(pushRule.getDelayPushMasterFlag())){
                    //非首轮推送模式：推送老师傅
                    delayPushOldMasterNum = (new BigDecimal(pushRule.getDelayPushMasterNumPerRound()).multiply(new BigDecimal(pushRule.getDelayPushMasterPercent()))).divide(new BigDecimal(100)).setScale( 0, BigDecimal.ROUND_HALF_UP ).intValue();
                    delayPushNewMasterNum = pushRule.getDelayPushMasterNumPerRound() - delayPushOldMasterNum;
                }else{
                    //非首轮推送模式：不区分新老师傅
                    delayPushOldMasterNum = pushRule.getDelayPushMasterNumPerRound();
                }

                return PushConfig.builder().setBestOfferNum(pushRule.getBestOfferNum())
                        .setDelayMinutesBetweenRounds(pushRule.getDelayMinutesBetweenRounds())
                        .setFirstPushNewMasterNum(firstPushNewMasterNum)
                        .setFirstPushOldMasterNum(firstPushOldMasterNum)
                        .setDelayPushNewMasterNumPerRound(delayPushNewMasterNum)
                        .setDelayPushOldMasterNumPerRound(delayPushOldMasterNum)
                        .setFirstPushMasterType(pushRule.getFirstPushMasterFlag())
                        .setDelayPushMasterType(pushRule.getDelayPushMasterFlag())
                        .build();
            }
        }
        return pushConfig;
    }


    @Value("${pushRule.percent:5}")
    private Integer pushRulePercent;

    private StrategyEntityList getStrategyEntityList(StrategyIdList strategyIdList, List<OrderPushRule> orderPushRuleList, Integer pushRuleId, Integer appointType, OrderDetailData orderDetailData) {

        BaseSelect baseSelect = getBaseSelect(strategyIdList.getBaseSelectStrategyId());
        PushFilterList pushFilterList = pushHandler.getPushFilterList(strategyIdList.getFilterStrategyId());
        PushScorerList pushScorerList = pushHandler.getPushScorerList(strategyIdList.getSortingStrategyId());
        if (pushRuleId != null) {
            //策略组合新模式
            PushRule pushRule = pushRuleRepository.selectByPrimaryKey(pushRuleId);
            List<PushRuleConfig> pushRuleConfigList = JSON.parseArray(pushRule.getPushRuleList(), PushRuleConfig.class);
            //对接大数abTest
            PushRuleConfig pushRuleConfig = dockingAbTest(pushRuleId, appointType, pushRuleConfigList, orderDetailData);
            if(Objects.isNull(pushRuleConfig)){
                log.error(String.format("推单失败，未匹配到对应交易模式的推单规则，pushRuleId:%d", pushRuleId));
                feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"推单异常", String.format("【订单号】：%s\n\n【错误原因】：%s",
                        orderDetailData.getOrderNo(), "未匹配到对应交易模式的推单规则"));
            }
            if(Objects.nonNull(pushRuleConfig.getFixedRoundsRule())){
                if(orderDetailData.getBusinessLineId() == 2){
                    OrderPushRule orderPushRule = new OrderPushRule();
                    BeanUtils.copyProperties(pushRuleConfig.getFixedRoundsRule(),orderPushRule);
                    orderPushRule.setAppointType(pushRuleConfig.getAppointType());
                    PushConfig pushConfig = getPushConfig(Collections.singletonList(orderPushRule), appointType);
                    log.info("getStrategyEntityList2:" + JSON.toJSONString(pushConfig));
                    return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushConfig);
                }else if(orderDetailData.getBusinessLineId() == 1){
                    Random random = new Random();
                    int randomNum = random.nextInt(pushRulePercent);
                    if(randomNum == 0){
                        OrderPushRule orderPushRule = new OrderPushRule();
                        BeanUtils.copyProperties(pushRuleConfig.getFixedRoundsRule(),orderPushRule);
                        orderPushRule.setAppointType(pushRuleConfig.getAppointType());
                        PushConfig pushConfig = getPushConfig(Collections.singletonList(orderPushRule), appointType);
                        log.info("getStrategyEntityList1:" + JSON.toJSONString(pushConfig));
                        return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushConfig);
                    }else{
                        return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushRuleConfig);
                    }
                }else{
                    return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushRuleConfig);
                }
            }else{
                return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushRuleConfig);
            }
        } else if (CollectionUtils.isNotEmpty(orderPushRuleList)) {
            //策略组合旧模式
            PushConfig pushConfig = getPushConfig(orderPushRuleList, appointType);
            return new StrategyEntityList(baseSelect, pushFilterList, pushScorerList, pushConfig);
        }
        return null;
    }

    private PushRuleConfig dockingAbTest(Integer pushRuleId, Integer appointType,
                                             List<PushRuleConfig> pushRuleConfigList,
                                             OrderDetailData orderDetailData) {
        List<PushRuleConfig> pushRuleConfigFilterAppointType = pushRuleConfigList.stream().filter(config -> config.getAppointType().equals(appointType)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(pushRuleConfigFilterAppointType) && pushRuleConfigFilterAppointType.size() > 1) {
            log.info("------masterOrderPushGetStrategyEntityList dockingABT,orderId:{},cityId:{},pushRuleId:{}", orderDetailData.getMasterOrderId(), orderDetailData.getSecondDivisionId(), pushRuleId);

            //默认开启实验的规则为对照组
            PushRuleConfig originPushRuleConfig = pushRuleConfigFilterAppointType.stream().filter(config -> Objects.nonNull(config.getTestFlag()) && config.getTestFlag() == 1).findFirst().orElse(null);

            //对接大数据
            OrderPushAbTestRqt rqt = new OrderPushAbTestRqt();
            rqt.setCityCode(orderDetailData.getSecondDivisionId().intValue());
            rqt.setPersonaId(4);
            rqt.setTryBelongs("order_distribute");
            rqt.setTryObjId(orderDetailData.getOrderNo());

            List<OrderPushAbTestResp> orderPushAbTestRespList = null;
            try {
                orderPushAbTestRespList = bigdataOpenServiceApi.getOrderPushAbTest(rqt);
            } catch (Exception e) {
                log.error("------masterOrderPushGetStrategyEntityList dockingABT error!orderId:{},params:{}", orderDetailData.getMasterOrderId(), JSONUtil.toJsonStr(rqt), e);
            }
            log.info("-----masterOrderPushGetStrategyEntityList dockingABT finish,参数：{}，返回结果:{}", JSONUtil.toJsonStr(rqt), JSONUtil.toJsonStr(orderPushAbTestRespList));
            if (CollectionUtil.isEmpty(orderPushAbTestRespList)) {
                log.error("-----masterOrderPushGetStrategyEntityList dockingABT 失败或无分组结果!orderId:{},params:{},resp:{}", orderDetailData.getMasterOrderId(), JSONUtil.toJsonStr(rqt), JSONUtil.toJsonStr(orderPushAbTestRespList));
                //对接大数据失败走对照组
                return originPushRuleConfig;
            }

            OrderPushAbTestResp orderPushAbTestResp = orderPushAbTestRespList.get(0);
            //实验组ID
            Integer tryGroupId = orderPushAbTestResp.getTryGroupId();

            PushRuleConfig tryPushRuleConfig = pushRuleConfigFilterAppointType.stream().filter(config -> Objects.nonNull(config.getTestGroupId()) && Objects.equals(config.getTestGroupId(), tryGroupId)).findFirst().orElse(null);
            if (Objects.isNull(tryPushRuleConfig)) {
                //配置错误，大数据返回实验组id不匹配也走对照组
                log.error("-----masterOrderPushGetStrategyEntityList dockingABT 配置错误，大数据返回实验组id不匹配！");
                return originPushRuleConfig;
            } else if ((orderPushAbTestResp.getTryGroupType() == 1 && tryPushRuleConfig.getTestFlag() != 1)
                    || (orderPushAbTestResp.getTryGroupType() == 2 && tryPushRuleConfig.getTestFlag() == 1)) {
                //配置错误，对照组实验组跟大数据映射配置错误也走对照组
                log.error("-----masterOrderPushGetStrategyEntityList dockingABT 对照组实验组跟大数据映射配置错误！");
                return originPushRuleConfig;
            } else {
                return tryPushRuleConfig;
            }

        } else {
            //没有配置开启ABTest，原匹配交易模式逻辑
            return pushRuleConfigList.stream().filter(config -> config.getAppointType().equals(appointType)).findFirst().orElse(null);
        }
    }

    /**
     * 获取初筛规则
     * @param baseSelectStrategyId
     * @return
     */
    private BaseSelect getBaseSelect(Long baseSelectStrategyId){
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyRepository.selectByPrimaryKey(baseSelectStrategyId);
        RangeSelect rangeSelect = JSON.parseObject(baseSelectStrategy.getRangeSelect(),RangeSelect.class);
        TechniqueSelect techniqueSelect = JSON.parseObject(baseSelectStrategy.getTechniqueSelect(),TechniqueSelect.class);
        StatusSelect statusSelect = JSON.parseObject(baseSelectStrategy.getStatusSelect(),StatusSelect.class);
        ServeDataSelect serveDataSelect = JSON.parseObject(baseSelectStrategy.getServeDataSelect(),ServeDataSelect.class);
        return new BaseSelect(baseSelectStrategy.getSnapshotId(),baseSelectStrategy.getStrategyName(),rangeSelect,techniqueSelect,statusSelect,serveDataSelect);
    }

    private Set<String> filterNewModelMaster(Set<String> masterSet,OrderDetailData orderDetailData){
        if(CollectionUtils.isEmpty(masterSet)){
            return masterSet;
        }
        List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
        List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
        masterSet.removeAll(agreementMasterIdList);
        return masterSet;
    }

    /**
     * 过滤已推送的家庭协议师傅
     * @param masterIdSet
     * @param getGlobalOrderTraceId
     * @return
     */
    public Set<String> filterPushedFamilyAgreementMaster(Set<String> masterIdSet, Long getGlobalOrderTraceId) {
        String masterIdStr = hBaseClient.querySingle("order_push", String.valueOf(getGlobalOrderTraceId), PushMode.FAMILY_AGREEMENT_MASTER.getCode());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)) {
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            masterIdSet.removeAll(pushedMasterIdSet);
        }
        return masterIdSet;
    }


    /**
     * 策略推单
     * @param pushStrategySnapshotId 推送策略快照id
     * @param orderPushRqt 推单请求实体
     * @param strategyEntityList 策略实体列表
     * @param alternatePushStrategyRule 备用推荐路由规则
     * @param pushCommonObject 推单公共实体类
     * @param timeMark 时间标记
     * @param isPriorityStrategyPush 是否优先推荐路由
     * @param isToRepush 是否重推
     * @param isFilterPushedMaster 是否过滤已推送师傅
     */
    private void pushByStrategy(Long pushStrategySnapshotId,OrderPushRqt orderPushRqt, StrategyEntityList strategyEntityList, PushStrategyRule alternatePushStrategyRule,
                                PushCommonObject pushCommonObject, String timeMark, boolean isPriorityStrategyPush,boolean isToRepush,Integer isFilterPushedMaster){

        OrderDetailData orderDetailData = pushCommonObject.getOrderDetailData();
        if(!isToRepush){
            orderDetailData.setIsExclusiveTeamMaster(1);
            orderPushRqt.setIsExclusiveTeamMaster(1);
        }
        Long globalOrderId = pushCommonObject.getOrderDetailData().getGlobalOrderId();
        String orderVersion = pushCommonObject.getOrderVersion();
        Long timeStamp = pushCommonObject.getTimestamp();

        BaseSelect baseSelect;
        if(strategyEntityList != null) {
            baseSelect = strategyEntityList.getBaseSelect();
        }else{
            baseSelect = getBaseSelect(alternatePushStrategyRule.getStrategyList().getBaseSelectStrategyId());
        }

        pushCommonObject.setBaseSelect(baseSelect);
        Long beginTime = System.currentTimeMillis();
        List<BaseSelectMaster> baseSelectMasterList = baseSelector.baseSelectByStrategy(pushCommonObject,orderVersion);
        Long endTime = System.currentTimeMillis();
        long baseSelectTakeTime = endTime - beginTime;
        log.info("订单id:{},baseSelect by strategy take times: {} ms", globalOrderId, baseSelectTakeTime);
        if (baseSelectTakeTime > 5000) {
            log.info("订单id:{},baseSelect takes time greater than 5000ms,actual take: {} ms", globalOrderId, baseSelectTakeTime);
        }
        log.info("订单id:{},初筛后师傅:【{}】",globalOrderId,JSONObject.toJSONString(pushCommonObject.getMasterSet()));

        //首次有效师傅推送 0：非首次有效推送，1：首次有效推送
        int firstTimeValidPush = 0;
        PushProgress pushProgress = pushProgressRepository.getFirstTimeValidPushProgress(globalOrderId);
        if (Objects.isNull(pushProgress)) {
            firstTimeValidPush = 1;
        }

        //普通推单首次有效师傅推送，0：非首次有效推送，1：首次有效推送
        int normalFirstTimeValidPush = 0;
        PushProgress pushProgress2 = pushProgressRepository.getNormalFirstTimeValidPushProgress(globalOrderId);
        if (Objects.isNull(pushProgress2)) {
            normalFirstTimeValidPush = 1;
        }


        if (!CollectionUtils.isEmpty(pushCommonObject.getMasterSet())) {
            //过滤ocs推送数量限制
//            ocsPushRestrictFilter(orderPushRqt.getMasterOrderId(),orderVersion,pushCommonObject.getMasterSet(),orderDetailData.getBusinessLineId());
            //过滤不感兴趣重推
            masterDisinterestOrderFilter(orderPushRqt.getMasterOrderId(),pushCommonObject.getMasterSet());

            if(!isToRepush){
                GetOfferedPriceListByGlobalOrderIdRqt rqt = new GetOfferedPriceListByGlobalOrderIdRqt();
                rqt.setGlobalOrderTraceId(orderPushRqt.getGlobalOrderId());
                List<GetOfferedPriceListByGlobalOrderIdResp> respList = offerModuleResourceApi.getOfferedPriceListByGlobalOrderId(rqt);
                if(CollectionUtils.isNotEmpty(respList)){
                    Set<String> offeredMasterList = respList.stream().map(GetOfferedPriceListByGlobalOrderIdResp::getMasterId).map(String::valueOf).collect(Collectors.toSet());
                    pushCommonObject.getMasterSet().removeAll(offeredMasterList);
                }
            }
            if(isFilterPushedMaster != null && isFilterPushedMaster == 1){
                //重推过滤已推送师傅
                PushProgress latestPushProgress = pushProgressRepository.getPushProgress(globalOrderId,pushCommonObject.getLatestOrderVersion());
                //过滤已推送的新师傅
                Set<String> pushedNewMasterList = masterPushRepository.selectByMasterIdList(globalOrderId,latestPushProgress.getOrderVersion(),MasterType.MASETR_NEW.code,latestPushProgress.getNewMasterOffset(),pushCommonObject.getMasterSet());
                pushCommonObject.getMasterSet().removeAll(pushedNewMasterList);
                if(CollectionUtils.isNotEmpty(pushCommonObject.getMasterSet())){
                    //过滤已推送的老师傅
                    Set<String> pushedOldMasterList = masterPushRepository.selectByMasterIdList(globalOrderId,latestPushProgress.getOrderVersion(),MasterType.MASTER_OLD.code, latestPushProgress.getOldMasterOffset(),pushCommonObject.getMasterSet());
                    pushCommonObject.getMasterSet().removeAll(pushedOldMasterList);
                }
            }

            pushCommonObject.setMasterSet(filterNewModelMaster(pushCommonObject.getMasterSet(),orderDetailData));


            pushProgressRepository.insertBasePushProgress(pushCommonObject.getGlobalOrderId(), orderVersion,pushCommonObject.getMasterSet().size(),new Date(timeStamp),"normal_push");
        }else{
            PushStrategySnapshot pushStrategySnapshot = new PushStrategySnapshot(pushStrategySnapshotId,!isToRepush, baseSelect.getSnapshotId(), null,null,isPriorityStrategyPush);
            pushHandleRepository.insertPushHanle(new PushHandle(pushCommonObject.getGlobalOrderId(),orderVersion,0),pushStrategySnapshot);
            pushProgressRepository.insertBasePushZeroProgress(pushCommonObject.getGlobalOrderId(),orderVersion,new Date(timeStamp),"normal_push");
        }


        boolean isEnableAlternativeStrategy = isPriorityStrategyPush && alternatePushStrategyRule != null &&
                checkAlternativeStrategyOpenCondition(pushCommonObject.getMasterSet().size(),alternatePushStrategyRule.getOpenConditionQlExpression().getQlExpression());
        if(isEnableAlternativeStrategy){
            //满足备用路由开启条件，启用备用路由策略进行推单

            pushProgressRepository.updatePushOverMessage(globalOrderId, orderVersion, PushStatus.STOP.code, "ENABLE_ALTERNATIVE_STRATEGY");

            timeStamp = System.currentTimeMillis();
            orderVersion = String.valueOf(timeStamp);
            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushCommonObject.setOrderVersion(orderVersion);
            pushByStrategy(pushStrategySnapshotId,orderPushRqt,null,alternatePushStrategyRule,pushCommonObject,timeMark,false,true,0);
            return;
        }


        if(CollectionUtils.isEmpty(pushCommonObject.getMasterSet())){
            portPushService.matchPushPortRule("normal",orderDetailData,0);
            return ;
        }

        if(MatchSceneCode.FAMILY_AGREEMENT_PUSH_NORMAL.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //家庭协议推单多少分钟后推普通师傅  过滤已推的协议师傅
            pushCommonObject.setMasterSet(filterPushedFamilyAgreementMaster(pushCommonObject.getMasterSet(), orderDetailData.getGlobalOrderId()));
        }


        PushFilterList pushFilterList;
        PushScorerList pushScorerList;
        PushConfig pushConfig = null;
        PushRuleConfig pushRuleConfig = null;
        if(strategyEntityList != null) {
            pushFilterList = strategyEntityList.getPushFilterList();
            pushScorerList = strategyEntityList.getPushScorerList();
            pushConfig = strategyEntityList.getPushConfig();
            pushRuleConfig = strategyEntityList.getPushRuleConfig();
        }else{
            pushFilterList = pushHandler.getPushFilterList(alternatePushStrategyRule.getStrategyList().getFilterStrategyId());
            pushScorerList = pushHandler.getPushScorerList(alternatePushStrategyRule.getStrategyList().getSortingStrategyId());
            Integer pushRuleId = alternatePushStrategyRule.getPushRuleId();
            if(pushRuleId != null && pushRuleId > 0){
                PushRule pushRule = pushRuleRepository.selectByPrimaryKey(pushRuleId);
                List<PushRuleConfig> pushRuleConfigList = JSON.parseArray(pushRule.getPushRuleList(), PushRuleConfig.class);
                pushRuleConfig = this.dockingAbTest(pushRuleId, orderDetailData.getAppointType(), pushRuleConfigList, orderDetailData);
            }

            if(CollectionUtils.isNotEmpty(alternatePushStrategyRule.getPushRule())){
                pushConfig = getPushConfig(alternatePushStrategyRule.getPushRule(),orderDetailData.getAppointType());
            }
        }

        Set<String> orderFeatureSet = new HashSet<>();
        orderFeatureSet.addAll(pushFilterList.getOrderFeatureSet());
        orderFeatureSet.addAll(pushScorerList.getOrderFeatureSet());
        orderFeatureSet.add("user_group");
        orderFeatureSet.add("appoint_user");



        //rowkey = 10000285936_430600_430602_400357
//        pushCommonObject.setMasterSet(Collections.singleton("61295001577"));
//        orderFeatureSet = Collections.singleton("price_sensitive");
        PushFeature pushFeature = featureQueryFacade.getOrderFeatures(pushCommonObject , orderFeatureSet);

        pushFeature.addOrderFeature(FieldConstant.PUSH_MODE, pushCommonObject.getPushMode());


//        Integer teamOrderTag = orderDetailData.getTeamMasterOrderPush();
//        if (teamOrderTag!=null) {
//            pushFeature.addOrderFeature(FieldConstant.TEAM_MASTER_ORDER_PUSH, teamOrderTag);
//        }


        pushHandler.checkCondition(pushFilterList,pushScorerList,pushFeature);


        Set<String> masterFeatureSet = new HashSet<>();
        masterFeatureSet.addAll(pushFilterList.getMasterFeatureSet());
        masterFeatureSet.addAll(pushScorerList.getMasterFeatureSet());
        masterFeatureSet.add("skill_degree");
        masterFeatureSet.add("smc_serve_degree");
        masterFeatureSet.add("is_forced_push");
        masterFeatureSet.add("master_group");
        //2023-11-07::工作项***********::所有订单都需要计算距离,来判断备用路由
        masterFeatureSet.add("order_master_distance");
        masterFeatureSet.add("last_30d_offer_order_cnt");
        masterFeatureSet.add("order_master_serve_division_level");
        masterFeatureSet.add("id_card_number");



//        masterFeatureSet =new HashSet<>();
//        masterFeatureSet.add("last30d_serve_complete_cnt");
//        masterFeatureSet.add("last30d_serve_complete_cnt_rank");

//        pushCommonObject.setMasterSet(Collections.singleton("5232782664"));
        featureQueryFacade.getMasterFeatures(pushFeature,pushCommonObject.getMasterSet(),masterFeatureSet);

        beginTime = System.currentTimeMillis();
        PushStrategySnapshot pushStrategySnapshot = new PushStrategySnapshot(pushStrategySnapshotId,!isToRepush, baseSelect.getSnapshotId(), pushFilterList.getSnapshotId(), pushScorerList.getSnapshotId(),isPriorityStrategyPush);
        List<PushMaster> pushMasterList = pushHandler.handle(pushStrategySnapshot,pushCommonObject,pushFilterList,pushScorerList,pushFeature,timeMark,orderVersion,baseSelectMasterList,isToRepush);
        log.info("pushMasterList:" + JSON.toJSONString(pushMasterList));
        endTime = System.currentTimeMillis();
        log.info("push handle take times: " + (endTime - beginTime) + "ms");


        isEnableAlternativeStrategy = isPriorityStrategyPush && alternatePushStrategyRule != null &&
                checkAlternativeStrategyOpenCondition(
                        getCanPushNumber(
                                pushMasterList,
                                pushFeature.getMasterFeature(),
                                alternatePushStrategyRule
                        ),
                        alternatePushStrategyRule.getOpenConditionQlExpression().getQlExpression()
                );
        if(isEnableAlternativeStrategy){

            pushProgressRepository.updatePushOverMessage(globalOrderId, orderVersion, PushStatus.STOP.code, "ENABLE_ALTERNATIVE_STATEGY");

            timeStamp = System.currentTimeMillis();
            orderVersion = String.valueOf(timeStamp);
            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushCommonObject.setOrderVersion(orderVersion);
            pushByStrategy(pushStrategySnapshotId,orderPushRqt,null,alternatePushStrategyRule,pushCommonObject,timeMark,false,true,0);
            return;
        }

        //大订单标识，去除标识后重推
        if (CollectionUtils.isEmpty(pushMasterList)) {
            log.info("has no master after pushHandle:" + JSON.toJSONString(orderDetailData));
            String teamOrderTag = pushFeature.getOrderFeatureByName(FieldConstant.TEAM_MASTER_ORDER_PUSH);
            if (StringUtils.isNotBlank(teamOrderTag) && Integer.valueOf(teamOrderTag) == 1) {
                log.info("团队师傅推单没有可推师傅，推送普通师傅,orderPushRqt:" + JSON.toJSONString(orderPushRqt));
                rePush(orderPushRqt,orderDetailData.getPushExtraData().getHandoffTag(),orderDetailData.getPushExtraData().getMasterSourceType());
                return;
            }
            portPushService.matchPushPortRule("normal",orderDetailData,0);
            return ;
        }



        JSONObject commonFeature = new JSONObject();
        buildPushFeature(commonFeature, pushCommonObject, pushFeature);
        final Integer maxOfferNumber = orderPushRqt.getMaxOfferNumber();
        if (maxOfferNumber!=null) {
            commonFeature.put(FieldConstant.OCS_ORDER_OFFER_NUM,maxOfferNumber);
        }

        //是否根据距离推送
        commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH,Optional.ofNullable(pushCommonObject.getMasterMatchCondition()).map(MasterMatchCondition::isAccordingDistancePush).orElse(false));

        commonFeature.put(FieldConstant.ORDER_BIND_TECHNIQUES,orderDetailData.getOrderTechniques());
        commonFeature.put(FieldConstant.HAND_OFF_TAG,orderDetailData.getPushExtraData().getHandoffTag());
        commonFeature.put(FieldConstant.ORDER_LNG_LAT,orderDetailData.getOrderLngLat());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
        commonFeature.put(FieldConstant.SECOND_DIVISION_ID,orderDetailData.getSecondDivisionId());



        // 推送 (每轮推送之前进行师傅推送状态校验)--【推送】
        pushCommonObject.setPushConfig(pushConfig);
        pushCommonObject.setPushRuleConfig(pushRuleConfig);


        if(MatchSceneCode.NEW_MODEL_RECOMMEND_MASTER_LIST.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //样板订单推荐列表
            pushMasterList = pushMasterList.stream().filter(pushMaster -> StringUtils.isNotBlank(pushMaster.getMasterSourceType())).collect(Collectors.toList());
            Map<String,List<PushMaster>> pushMasterMap = pushMasterList.stream().collect(Collectors.groupingBy(pushMaster -> pushMaster.getMasterSourceType()));
            List<PushMaster> tobPushMasterList = pushMasterMap.get(MasterSourceType.TOB.code);
            List<PushMaster> tocPushMasterList = pushMasterMap.get(MasterSourceType.TOC.code);

            List<PushMaster> finalPushMasterList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(tocPushMasterList)){
                Collections.sort(tocPushMasterList);
                finalPushMasterList.addAll(tocPushMasterList.subList(0,tocPushMasterList.size() <= 1000 ? tocPushMasterList.size() : 1000));
            }

            if((CollectionUtils.isEmpty(finalPushMasterList) || finalPushMasterList.size() < 1000) && CollectionUtils.isNotEmpty(tobPushMasterList)){
                int count = CollectionUtils.isNotEmpty(finalPushMasterList) ? 1000 - finalPushMasterList.size() : 1000;
                Collections.sort(tobPushMasterList);
                finalPushMasterList.addAll(tobPushMasterList.subList(0,tobPushMasterList.size() <= count ? tobPushMasterList.size() : count));
            }
            commonFeature.put(FieldConstant.PUSH_MODE,"recommend_list");
            pushControllerFacade.directPush(orderDetailData, orderVersion, finalPushMasterList, commonFeature);
            return ;
        }

        if("new_model_transfor_normal".equals(orderDetailData.getPushExtraData().getPushMode())){
            //样板订单推送普通师傅
            commonFeature.put(FieldConstant.PUSH_MODE,"new_model_transfor_normal");
            pushControllerFacade.push(pushFeature, timeMark, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush,normalFirstTimeValidPush,null);
            return ;
        }


        if(MatchSceneCode.COOPERATION_BUSINESS.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //合作经营推单
            commonFeature.put(FieldConstant.PUSH_MODE,"cooperation_business");
//            commonFeature.put(FieldConstant.MATCH_SCENE_CODE,"cooperative_business");
            pushControllerFacade.directPush(orderDetailData, orderVersion, pushMasterList, commonFeature);
            return ;
        }


        if(MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //合作经营推单
            commonFeature.put(FieldConstant.PUSH_MODE,PushMode.NORMAL.code);
            commonFeature.put(FieldConstant.MATCH_SCENE_CODE,MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode());
            Collections.sort(pushMasterList);

            Integer limitCount = ((pushMasterList.size() * extraContestOfferNumberPushPercent) / 100);
            limitCount = limitCount < 1 ? 1 : limitCount;

            pushControllerFacade.directPush(orderDetailData, orderVersion, pushMasterList.subList(0,limitCount), commonFeature);
            return ;
        }


        if(MatchSceneCode.FULL_TIME_MASTER.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //报价人数已满推送全时师傅
            commonFeature.put(FieldConstant.PUSH_MODE,PushMode.FULL_TIME_MASTER.code);
            pushControllerFacade.directPush(orderDetailData, orderVersion, pushMasterList, commonFeature);
            return ;
        }




        if(MatchSceneCode.INTERFERE_ORDER_PUSH.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
            //平台干预订单-推荐师傅
            pushControllerFacade.interfereOrderPush(pushMasterList,orderDetailData.getGlobalOrderId(),orderDetailData.getMasterOrderId());
            return ;
        }



        if (PushMode.NORMAL.code.equals(pushCommonObject.getPushMode()) && pushMasterList.size() > normalPushMasterSizeLimit) {
            log.info("pushMasterSizeLimitByNormal >>> 订单推送技能相关的师傅,人数限制3000！");
            Collections.sort(pushMasterList);
            pushMasterList = pushMasterList.subList(0, normalPushMasterSizeLimit);
        }

        log.info("pushMasterList:" + JSON.toJSONString(pushMasterList));
        //普通推单首次有效师傅推送匹配的可推单师傅数
        Integer normalFirstMatchMasterNum = pushMasterList.size();

        Integer pushNum = pushControllerFacade.push(pushFeature, timeMark, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush,normalFirstTimeValidPush, normalFirstMatchMasterNum);

        if("on".equals(interfereOrderPushSwitch) && (!AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType()))){
            pushControllerFacade.interfereOrderPush(pushMasterList,orderDetailData.getGlobalOrderId(),orderDetailData.getMasterOrderId());
        }


        teamOrderMasterRematch(orderDetailData,commonFeature);

        String teamMasterOrderPush = (String)commonFeature.get(FieldConstant.TEAM_MASTER_ORDER_PUSH);
        boolean isTeamMasterPush = StringUtils.isNotBlank(teamMasterOrderPush) && Integer.valueOf(teamMasterOrderPush) == 1;

        if(isToRepush && (!isTeamMasterPush)){
            Integer businessLineId = getBusinessLineId(orderDetailData);
            //首轮推送后匹配重推机制，满足重推机制则发送重推MQ消息
            this.repushOrder(businessLineId,orderDetailData,pushCommonObject,pushConfig,pushRuleConfig,pushFeature,orderPushRqt,orderVersion);
//            this.nearbyPush(orderDetailData,orderVersion,orderPushRqt.getMaxOfferNumber());
            longTailPushService.matchLongTailStrategy(businessLineId,orderDetailData,orderVersion,orderPushRqt.getMaxOfferNumber());
            portPushService.matchPushPortRule("normal",orderDetailData,pushNum);
        }

    }


    @Resource
    private PortPushService portPushService;



    private static final String ORDER_MASTER_DISTANCE="orderMasterDistance";
    private Integer getCanPushNumber(List<PushMaster> pushMasterList,
                                     DefaultContext<String, DefaultContext<String, Object>> masterFeature,
                                     PushStrategyRule alternatePushStrategyRule){
        final List<PushStrategyRule.OpenConditionItem> collect
                = alternatePushStrategyRule.getOpenCondition().getItemList().stream().filter(
                        row -> row.getItemName().equals(ORDER_MASTER_DISTANCE)
        ).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collect)) {
            final PushStrategyRule.OpenConditionItem openConditionItem = collect.get(0);
            final Integer distance = openConditionItem.getItemValue();
            final Long masterCount = pushMasterList.stream().filter(
                    row -> {
                        final String masterId = row.getMasterId();
                        final DefaultContext<String, Object> masterContext = masterFeature.get(masterId);
                        if (MapUtils.isNotEmpty(masterContext)) {
                            //没有距离的师傅算作大于备用路由配置的距离规则
                            final Object orderMasterDistance = masterContext.get("order_master_distance");
                            if (orderMasterDistance != null) {
                                try {
                                    //当前只支持小于
                                    if (Double.valueOf(orderMasterDistance.toString()) < distance.doubleValue()) {
                                        return true;
                                    }
                                } catch (Exception e) {
                                }
                            }
                        }
                        return false;
                    }
            ).count();

            return masterCount.intValue();
        }else {
            if(CollectionUtils.isNotEmpty(pushMasterList)){
                return pushMasterList.size();
            }else{
                return 0;
            }
        }

    }

    public static void main(String[] args) {
        final ArrayList<String> objects = new ArrayList<String>(){{add("1");add("2");add("3");}};
        final List<String> collect = objects.stream().filter(row -> row.equals("1")).collect(Collectors.toList());
        System.out.println(collect);
    }
    private void teamOrderMasterRematch(OrderDetailData orderDetailData,JSONObject commonFeature){
        try{
            String teamMasterOrderPush = (String)commonFeature.get(FieldConstant.TEAM_MASTER_ORDER_PUSH);
            if(StringUtils.isNotBlank(teamMasterOrderPush) && Integer.valueOf(teamMasterOrderPush) == 1){
                log.info("发送团队师傅推单重新推单mq消息，orderDetailData：" + JSON.toJSONString(orderDetailData));
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(orderDetailData.getMasterOrderId());
                orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                orderMatchMasterRqt.setIsExclusiveTeamMaster(1);
                orderMatchMasterRqt.setBusinessLineId(orderDetailData.getBusinessLineId());
                orderMatchMasterRqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
                orderMatchMasterRqt.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
                pushQueueService.sendDelayPushMessage(teamMasterOrderRematchDelayTime * 60L * 1000L ,JSON.toJSONString(orderMatchMasterRqt));
            }
        }catch(Exception e){
            log.error("teamOrderMasterRematch error",e);
        }

    }

    private final  String VERSION = "v2.0.0";

    private void repushOrder(Integer businessLineId,OrderDetailData orderDetailData, PushCommonObject pushCommonObject, PushConfig pushConfig,
                             PushRuleConfig pushRuleConfig,
                             PushFeature pushFeature, OrderPushRqt orderPushRqt, String orderVersion) {

        String orderFlag = orderDetailData.getOrderFrom();
        if (!StringUtils.equals(orderFlag,"ikea")) {
            orderFlag = "normal";
        }
        RepushPolicy repushPolicy = this.getRepushPolicy(businessLineId,orderFlag, orderDetailData, pushCommonObject);

        if (repushPolicy != null) {

            Integer bestOfferNum = 10;

            if(pushConfig != null){
                bestOfferNum = pushConfig.getBestOfferNum();
            }

            if(pushRuleConfig != null){
                bestOfferNum = pushRuleConfig.getBestOfferNum();
            }

            if(bestOfferNum == null){
                bestOfferNum = 10;
            }

            List<RepushPolicyRule> repushPolicyRuleList = Lists.newArrayList();
            if (VERSION.equals(repushPolicy.getVersion())) {
                repushPolicyRuleList = JSON.parseArray(repushPolicy.getStrategyCombination(), RepushPolicyRule.class);
            } else {
                repushPolicyRuleList.add(JSON.parseObject(repushPolicy.getStrategyCombination(), RepushPolicyRule.class));
            }
            Long snapshotId = repushPolicy.getSnapshotId();
            Integer finalBestOfferNum = bestOfferNum;
            repushPolicyRuleList.forEach(repushPolicyRule -> {
                String conditionExpression = repushPolicyRule.getOpenCondition().getOpenConditionQlExpression().getQlExpression();
                if (StringUtils.isBlank(conditionExpression) || checkRepushCondition(conditionExpression, pushFeature.getOrderFeature())) {
                    Integer delayTimeMinute = repushPolicyRule.getOpenCondition().getOfferIntervalTime().getInterval();
                    Long delayTime =  delayTimeMinute* 60 * 1000L;
                    orderPushRqt.setTeamMasterOrderPush(0);

                    if("on".equals(orderAfreshPushSwitch)){
                        pushQueueService.sendAfreshPushMessage(snapshotId, orderVersion, orderDetailData, finalBestOfferNum, delayTime,delayTimeMinute,
                                orderDetailData.getPushExtraData().getHandoffTag());
                    }else{
                        pushQueueService.sendRepushMessage(snapshotId, orderVersion, orderPushRqt, finalBestOfferNum, delayTime,delayTimeMinute,
                                orderDetailData.getPushExtraData().getHandoffTag(),orderDetailData.getPushExtraData().getMasterSourceType());
                    }

                    log.info("订单满足重推机制，发送重新推单mq消息,globalOrderId:{}", pushCommonObject.getGlobalOrderId());
                }
            });
        }
    }


    private RepushPolicy getRepushPolicy(Integer businessLineId,String orderFlag, OrderDetailData orderDetailData, PushCommonObject pushCommonObject) {

        RepushPolicy repushPolicy = repushPolicyRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), String.valueOf(pushCommonObject.getOrderDetailData().getSecondDivisionId()));

        if (repushPolicy == null) {
            repushPolicy = repushPolicyRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, "all", String.valueOf(pushCommonObject.getOrderDetailData().getSecondDivisionId()));
        }
        if (repushPolicy == null) {
            repushPolicy = repushPolicyRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), "all");
        }
        if (repushPolicy == null) {
            repushPolicy = repushPolicyRepository.selectByCategoryIdAndCityId(orderFlag, businessLineId, "all", "all");
        }
        log.info("重推机制 policySnapshotId【{}】,globalOrderId:{}", Optional.ofNullable(repushPolicy).map(RepushPolicy::getSnapshotId).orElse(null), pushCommonObject.getGlobalOrderId());
        return repushPolicy;
    }



    private void nearbyPush(OrderDetailData orderDetailData,String orderVersion,Integer maxOfferNum){
        if(!nearbyPushEnable){
            return ;
        }
        NearbyPushConfig nearbyPushConfig = nearbyPushConfigRepository.selectByCategoryDivisionId(orderDetailData.getOrderCategoryId(),orderDetailData.getSecondDivisionId());
        if(Objects.nonNull(nearbyPushConfig) && StringUtils.isNotBlank(nearbyPushConfig.getRuleConfig())){
            List<NearbyPushRuleConfig> pushRuleConfigList = JSON.parseArray(nearbyPushConfig.getRuleConfig(),NearbyPushRuleConfig.class);
            if(CollectionUtils.isNotEmpty(pushRuleConfigList)){
                NearbyPushRuleConfig nearbyPushRuleConfig = pushRuleConfigList.stream().filter(ruleConfig -> ruleConfig.getAppointType().equals(orderDetailData.getAppointType())).findFirst().orElse(null);
                if(Objects.nonNull(nearbyPushRuleConfig)){
                    pushQueueService.sendNearbyPushMessage(orderDetailData,orderVersion,nearbyPushRuleConfig,maxOfferNum,nearbyPushRuleConfig.getDelayMinutesTime() * 60 * 1000L);
                    log.info("发送附近推单mq消息,globalOrderId:{}",orderDetailData.getGlobalOrderId());
                }
            }

        }
    }

    public boolean checkRepushCondition(String conditionExpression,DefaultContext<String,Object> orderFeatureContext){
        try{
            return (Boolean)qlExpressHandler.getExpressRunner().execute(conditionExpression, orderFeatureContext,null,true,false);
        }catch(Exception e){
            log.error(String.format("checkRepushCondition error, conditionExpression:%s,orderFeatureContext:%s",conditionExpression,orderFeatureContext.toString()),e);
            return false;
        }

    }


    private static final String CAN_PUSH_NUMBER = "canPushNumber";
    private boolean checkAlternativeStrategyOpenCondition(Integer canPushNumber,String openConditionExpression){
        DefaultContext<String, Object> context = new DefaultContext<>();
        context.put(CAN_PUSH_NUMBER,canPushNumber);
        context.put(ORDER_MASTER_DISTANCE,-1);
        try{
            return (Boolean)qlExpressHandler.getExpressRunner().execute(openConditionExpression, context,null,true,false);
        }catch(Exception e){
            log.error(String.format("checkAlternativeStrategyOpenCondition failed, canPushNumber:%d,openConditionExpression:%s",canPushNumber,openConditionExpression),e);
            return false;
        }


    }




    /**
     * 重发
     * @param orderPushRqt
     * @param
     */
    private void rePush(OrderPushRqt orderPushRqt,String handoffTag,String masterSourceType){
        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(orderPushRqt.getMasterOrderId());
        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
        orderMatchMasterRqt.setIsExclusiveTeamMaster(1);
        orderMatchMasterRqt.setBusinessLineId(orderPushRqt.getBusinessLineId());
        orderMatchMasterRqt.setHandoffTag(handoffTag);
        orderMatchMasterRqt.setMasterSourceType(masterSourceType);
        pushQueueService.sendDelayPushMessage(10L,JSON.toJSONString(orderMatchMasterRqt));

    }

    /**
     * 构建推送特征
     */
    private void buildPushFeature(JSONObject commonFeature,PushCommonObject pushCommonObject,PushFeature feature) {
        commonFeature.put(FieldConstant.IS_DELIVERY_ORDER,
                feature.getOrderFeatureByName(FieldConstant.IS_DELIVERY_ORDER));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, pushCommonObject.getDivisionMatchLevel());
        commonFeature.put(FieldConstant.ACCOUNT_TYPE, feature.getOrderFeatureByName(FieldConstant.ACCOUNT_TYPE));
        commonFeature.put(FieldConstant.CATEGORY_ID, feature.getOrderFeatureByName(FieldConstant.CATEGORY_ID));
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, feature.getOrderFeatureByName(FieldConstant.BUSINESS_LINE_ID));
        commonFeature.put(FieldConstant.PUSH_MODE, pushCommonObject.getPushMode());
        commonFeature.put(FieldConstant.APPOINT_TYPE, feature.getOrderFeatureByName(FieldConstant.APPOINT_TYPE));
        commonFeature.put(FieldConstant.TEAM_MASTER_ORDER_PUSH, feature.getOrderFeatureByName(FieldConstant.TEAM_MASTER_ORDER_PUSH));
    }

    /**
     * 重新推单
     * @param repushOrderRqt
     * @return
     */
    @Override
    public int repushOrder(RepushOrderRqt repushOrderRqt){
        try{

            OrderStatus orderStatus = pushControllerFacade.getOrderStatus(repushOrderRqt.getBestOfferNum(),repushOrderRqt.getGlobalOrderId(),repushOrderRqt.getOrderVersion(),repushOrderRqt.getMaxOfferNumber());
            if(OrderStatusValue.NORMAL != orderStatus.getOrderStatus()){
                return 0;
            }

            RepushPolicySnapshot repushPolicySnapshot = repushPolicySnapshotRepository.selectByPrimaryKey(repushOrderRqt.getRepushPolicySnapshotId());
            RepushPolicyRule repushPolicyRule;
            if (VERSION.equals(repushPolicySnapshot.getVersion())) {
                Integer delayTimeMinute = repushOrderRqt.getDelayTimeMinute();
                repushPolicyRule = JSON.parseArray(repushPolicySnapshot.getStrategyCombination(), RepushPolicyRule.class)
                        .stream().filter(it -> Objects.equals(delayTimeMinute, it.getOpenCondition().getOfferIntervalTime().getInterval()))
                        .findFirst().orElse(null);
            } else {
                repushPolicyRule = JSON.parseObject(repushPolicySnapshot.getStrategyCombination(), RepushPolicyRule.class);
            }

            if (repushPolicyRule == null || orderStatus.getOfferNum() >= repushPolicyRule.getOpenCondition().getOfferIntervalTime().getItemValue()) {
                return 0;
            }
            OrderPushRqt orderPushRqt = new OrderPushRqt();
            BeanUtils.copyProperties(repushOrderRqt,orderPushRqt);

            PushCommonObject pushCommonObject = new PushCommonObject();
            Long timeStamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timeStamp);
            OrderDetailData orderDetailData = new OrderDetailData(orderPushRqt,orderVersion);
            orderDetailData.getPushExtraData().setHandoffTag(repushOrderRqt.getHandoffTag());

            pushCommonObject.setOrderDetailData(orderDetailData);
            pushCommonObject.setOrderVersion(orderVersion);
            pushCommonObject.setTimestamp(timeStamp);
            pushCommonObject.setRepushStrategySnapshotId(repushOrderRqt.getRepushPolicySnapshotId());
            pushCommonObject.setLatestOrderVersion(repushOrderRqt.getOrderVersion());
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushByStrategy(pushCommonObject,timeMark,orderPushRqt,repushPolicyRule);
        }catch(Exception e){
            log.error(String.format("repushOrder error,repushOrderRqt:%s",JSON.toJSONString(repushOrderRqt)),e);
        }
        return 1;
    }

    /**
     * 重新推单
     * @param orderAfreshPushRqt
     * @return
     */
    @Override
    public int afreshPush(OrderAfreshPushRqt orderAfreshPushRqt){
        try{

            OrderDetailData orderDetailData = orderAfreshPushRqt.getOrderDetailData();
            Long globalOrderId = orderDetailData.getGlobalOrderId();
            //TODO maxOfferNumber
            OrderStatus orderStatus = pushControllerFacade.getOrderStatus(orderAfreshPushRqt.getBestOfferNum(),globalOrderId,orderAfreshPushRqt.getOrderVersion(),orderAfreshPushRqt.getMaxOfferNumber());
            if(OrderStatusValue.NORMAL != orderStatus.getOrderStatus()){
                log.info("afreshPush orderStatus:" + JSON.toJSONString(orderStatus));
                return 0;
            }

            RepushPolicySnapshot repushPolicySnapshot = repushPolicySnapshotRepository.selectByPrimaryKey(orderAfreshPushRqt.getRepushPolicySnapshotId());
            RepushPolicyRule repushPolicyRule;
            if (VERSION.equals(repushPolicySnapshot.getVersion())) {
                Integer delayTimeMinute = orderAfreshPushRqt.getDelayTimeMinute();
                repushPolicyRule = JSON.parseArray(repushPolicySnapshot.getStrategyCombination(), RepushPolicyRule.class)
                        .stream().filter(it -> Objects.equals(delayTimeMinute, it.getOpenCondition().getOfferIntervalTime().getInterval()))
                        .findFirst().orElse(null);
            } else {
                repushPolicyRule = JSON.parseObject(repushPolicySnapshot.getStrategyCombination(), RepushPolicyRule.class);
            }

            if (repushPolicyRule == null || orderStatus.getOfferNum() >= repushPolicyRule.getOpenCondition().getOfferIntervalTime().getItemValue()) {

                log.info("afreshPush openCondition no match");

                return 0;
            }

            OrderPushRqt orderPushRqt = new OrderPushRqt();
            BeanUtils.copyProperties(orderAfreshPushRqt.getOrderDetailData(),orderPushRqt);

            PushCommonObject pushCommonObject = new PushCommonObject();
            Long timeStamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timeStamp);
            orderDetailData.getPushExtraData().setHandoffTag(orderAfreshPushRqt.getHandoffTag());
            orderDetailData.getPushExtraData().setAfreshDelayTime(orderAfreshPushRqt.getDelayTimeMinute());
            orderDetailData.getPushExtraData().setMatchSceneCode(MatchSceneCode.AFRESH_PUSH.getCode());



            pushCommonObject.setOrderDetailData(orderDetailData);
            pushCommonObject.setOrderVersion(orderVersion);
            pushCommonObject.setTimestamp(timeStamp);
            pushCommonObject.setRepushStrategySnapshotId(orderAfreshPushRqt.getRepushPolicySnapshotId());
            pushCommonObject.setLatestOrderVersion(orderAfreshPushRqt.getOrderVersion());
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushByStrategy(pushCommonObject,timeMark,orderPushRqt,repushPolicyRule);
        }catch(Exception e){
            log.error(String.format("orderAfreshPush error,orderAfreshPushRqt:%s",JSON.toJSONString(orderAfreshPushRqt)),e);
        }
        return 1;
    }

    @Override
    public void familyAgreementRePushNormal(FamilyAgreementRePushNormalRqt familyAgreementRePushNormalRqt) {


        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(familyAgreementRePushNormalRqt.getMasterOrderId());
        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
        orderMatchMasterRqt.setBusinessLineId(familyAgreementRePushNormalRqt.getBusinessLineId());
        orderMatchMasterRqt.setMasterSourceType(familyAgreementRePushNormalRqt.getMasterSourceType());
        orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.FAMILY_AGREEMENT_PUSH_NORMAL.getCode());

        OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
        if(Objects.isNull(orderDetailData)){
            return;
        }


        MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);

        OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
        orderMasterMatcher.executeMatch(orderDetailData, masterMatchCondition);
    }


    /**
     * 重推同步（修改订单）
     * @param globalOrderId
     * @return
     */
    @Override
    public Integer repushSync(Long globalOrderId){

        PushProgress pushProgress = pushProgressRepository.getOrderLatestPushProgress(globalOrderId);
        if(pushProgress != null && PushStatus.PUSHING.code.equals(pushProgress.getPushStatus())){
            PushProgress updatePushProgress = new PushProgress();
            updatePushProgress.setPushProgressId(pushProgress.getPushProgressId());
            updatePushProgress.setPushStatus(PushStatus.STOP.code);
            updatePushProgress.setCurrentStopReason(OrderStatusValue.MODIFY_AND_REPUSH.toString());
            pushProgressRepository.updateByPrimaryKeySelective(updatePushProgress);
        }
        return 1;
    }

//    /**
//     * 附近更多推单
//     * @param rqt
//     * @return
//     */
//    @Override
//    public int nearbyPush(NearbyPushRqt rqt){
//
//        OrderDetailData orderDetailData = rqt.getOrderDetailData();
//
//        OrderStatus orderStatus = pushControllerFacade.getOrderStatus(rqt.getBestOfferNum(),orderDetailData.getGlobalOrderId(),rqt.getOrderVersion(),rqt.getMaxOfferNum());
//        if(OrderStatusValue.NORMAL != orderStatus.getOrderStatus()){
//            return 0;
//        }
//
//        Long timeStamp = System.currentTimeMillis();
//        String orderVersion = String.valueOf(timeStamp);
//
//        MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
//        masterMatchCondition.setOrderLngLat(orderDetailData.getOrderLngLat());
//        masterMatchCondition.setServeIds(orderDetailData.getLv3ServeIds());
//        masterMatchCondition.setNearbyDistance(rqt.getNearbyDistance());
//        masterMatchCondition.setThirdDivisionId(orderDetailData.getThirdDivisionId());
//        masterMatchCondition.setBusinessLineId(orderDetailData.getBusinessLineId());
//        Set<String> masterSet = baseSelector.nearbyPushBaseSelect(masterMatchCondition,orderDetailData.getAccountType(),orderDetailData.getAccountId(),orderDetailData.getUserId());
//
//
//        if(CollectionUtils.isNotEmpty(masterSet)){
//            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion,masterSet.size(),new Date(timeStamp),"nearby_push");
//
//            JSONObject commonFeature = new JSONObject();
//            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
//            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
//            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
//            commonFeature.put(FieldConstant.IS_NEARBY_PUSH, 1);
//            pushControllerFacade.directPush(orderDetailData,orderVersion,masterSet,commonFeature);
//
//        }else{
//            pushProgressRepository.insertBasePushZeroProgress(orderDetailData.getGlobalOrderId(),orderVersion,new Date(timeStamp),"nearby_push");
//        }
//
//        return 0;
//    }
















    private List<FilterStrategyRuleExpressionDto> checkOpenCondition(List<FilterStrategyRuleExpressionDto> filterStrategyRuleExpressionDtoList, DefaultContext<String,Object> orderFeatureContext){

        return filterStrategyRuleExpressionDtoList.stream().filter(filterStrategyRuleExpressionDto -> {
            String conditionExpression = filterStrategyRuleExpressionDto.getOpenConditionRuleExpression();
            if (StringUtils.isBlank(conditionExpression)) {
                return true;
            }
            boolean checkConditionSucc = false;
            try {
                checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, orderFeatureContext, null, true, false);
            } catch (Exception e) {
                log.error("校验召回策略开启条件失败",e);
            }
            return checkConditionSucc;
        }).collect(Collectors.toList());
    }








    private String getAppointType(OrderGrab orderGrab){
        if(AppointType.OPEN.value.equals(orderGrab.getAppointType())) {
            return "open";
        }else if(AppointType.NORMAL.value.equals(orderGrab.getAppointType())){
            return "normal";
        }else if(AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType())){
            return "definite_price";
        }else if(AppointType.ADVANCE_PAY.value.equals(orderGrab.getAppointType())){
            return "advance_payment";
        }
        return "";
    }

    /**
     * 总包自动邀约推单
     *
     * @param inviteOrderPushRqt
     * @return
     */
    @Override
    public int inviteOrderPush(InviteOrderPushRqt inviteOrderPushRqt) {
        Long globalOrderTraceId = inviteOrderPushRqt.getGlobalOrderTraceId();
        Set<Long> masterIds = inviteOrderPushRqt.getMasterIds();
        Integer businessLineId = inviteOrderPushRqt.getBusinessLineId();

        if (globalOrderTraceId == null || CollectionUtils.isEmpty(masterIds) || businessLineId == null) {
            return 0;
        }
        GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
        getOrderIdRqt.setGlobalOrderTraceId(globalOrderTraceId);
        Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);

        //获取订单时间标记
        String timeMark = DateFormatterUtil.getNow();

        Set<Long> masterIdList = masterPushRepository.selectByOrderIdAndMasterIds(globalOrderTraceId, masterIds, timeMark);
        if (CollectionUtils.isNotEmpty(masterIdList)) {
            //推单
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.HAND_OFF_TAG, "new");
            String masterSourceType = MasterSourceType.TOB.code;
            if (businessLineId == 2) {
                masterSourceType = MasterSourceType.TOC.code;
            }
            pushQueueService.pushByQueue(orderId, StringUtils.join(masterIdList, SymbolConstant.COMMA), masterIdList.size(),
                    "0", "3", String.valueOf(businessLineId), commonFeature, null, null,null,null, masterSourceType,null);
            //回复总包mq
            InviteOrderPushResp inviteOrderPushResp = new InviteOrderPushResp(globalOrderTraceId, masterIdList);
            rocketMqSendService.sendSyncMessage(commonOrderGeneralTopic, "invite_order_push_success", JSON.toJSONString(inviteOrderPushResp));
            return 1;
        }
        return 0;
    }


    /**
     * 师傅不感兴趣订单过滤
     * @param masterOrderId
     * @param masterIdSet
     */
    private void masterDisinterestOrderFilter(Long masterOrderId,Set<String> masterIdSet){
        try{
            final Set<String> disinterestMaster = getDisinterestMasterList(masterOrderId, masterIdSet);
            log.info("{},masterDisinterestOrderFilter:{}",masterOrderId,disinterestMaster);
            masterIdSet.removeAll(disinterestMaster);
        }catch(Exception e){
            log.error("过滤不感兴趣师傅失败",e);
        }

    }



    private Set<String> getDisinterestMasterList(Long masterOrderId, Set<String> masterIdSet){
        Set<String> resultSet = new HashSet<>();
        final List<List<String>> batch = LocalCollectionsUtil.groupByBatchString(masterIdSet, 499);
        for (List<String> batchRequest : batch) {
            List<MasterOrder> masterOrderList = masterOrderRepository.selectByMasterIdAndOrderId(batchRequest,String.valueOf(masterOrderId));
            if(CollectionUtils.isNotEmpty(masterOrderList)){
                resultSet.addAll(masterOrderList.stream().map(MasterOrder::getMasterId).collect(Collectors.toSet()));
            }
        }
        return resultSet;
    }
//    //改为查询MYSQL
//    public static final String T_MASTER_ORDER="t_master_order";
//    public static final String IS_DISINTEREST_ORDER="is_disinterest_order";
//    private Set<String> getDisinterestMaster(Long masterOrderId, Set<String> masterIdSet){
//        final HashSet<String> result = new HashSet<>();
//        try {
//            SingleColumnValueFilter singleColumnValueFilter = new SingleColumnValueFilter(
//                    IS_DISINTEREST_ORDER,
//                    SingleColumnValueFilter.CompareOperator.EQUAL,
//                    ColumnValue.fromString("1")
//            );
//            singleColumnValueFilter.setPassIfMissing(false);
//            final List<BatchGetRowResponse.RowResult> rowResults = tableStoreClient.batchQueryBySetWithFilter(
//                    T_MASTER_ORDER,
//                    singleColumnValueFilter,
//                    FieldConstant.MASTER_ORDER_ID,String.valueOf(masterOrderId),
//                    masterIdSet, FieldConstant.MASTER_ID);
//            for (BatchGetRowResponse.RowResult row : rowResults) {
//                if (row!=null) {
//                    final Row rowRow = row.getRow();
//                    if (rowRow!=null) {
//                        final String masterId = rowRow.getPrimaryKey().getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                        result.add(masterId);
//                    }
//                }
//            }
//
//        }catch (Exception e){
//            log.warn("getDisinterestMaster [{}],{}",masterOrderId,e);
//        }
//        return result;
//    }



    //    private Map<String,Long> getMasterPushCount(String dayTimeString,Set<String> masterIdSet){
//        final Map<String,Long> result = new HashMap<>(50);
//        try {
//            final List<BatchGetRowResponse.RowResult> rowResults = tableStoreClient.batchQueryBySetWithCol(
//                    PushRecordTableStoreService.T_MASTER_PUSH_COUNT,
//                    dayTimeString, masterIdSet, FieldConstant.MASTER_ID);
//            for (BatchGetRowResponse.RowResult row : rowResults) {
//                if (row!=null) {
//                    final Row rowRow = row.getRow();
//                    final Long longValue = tableStoreClient.getLongValue(rowRow, dayTimeString);
//                    if (longValue!=null) {
//                        final String masterId = row.getRow().getPrimaryKey().getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                        result.put(masterId,longValue);
//                    }
//                }
//            }
//
//        }catch (Exception e){
//            log.warn("getMasterPushCount [{}],{}",dayTimeString,e);
//        }
//        return result;
//    }









    @Override
    public Integer dropHistoryPushTable(){
        if (!DateFormatterUtil.isBetweenPeriodTime("00:05","02:00")) {
            return 0;
        }
        CompletableFuture.runAsync(() -> {
            Date date = DateUtils.addDays(new Date(),-15);
            String tableName = "master_push_" + DateFormatterUtil.timeStampToTimed(date.getTime());
            masterPushRepository.dropHistoryPushTable(tableName);
        });


        CompletableFuture.runAsync(() -> {
            Date date = DateUtils.addDays(new Date(),-15);
            String tableName = "order_master_push_" + DateFormatterUtil.timeStampToTimed(date.getTime());
            orderMasterPushRepository.dropHistoryOrderMasterPushTable(tableName);
        });

        return 1;
    }


    @Override
    public Integer push(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition){
        OrderPushRqt orderPushRqt = new OrderPushRqt();
        BeanUtils.copyProperties(orderDetailData,orderPushRqt);
        orderPushRqt.setCategoryId(orderDetailData.getOrderCategoryId());
        String orderVersion = orderDetailData.getOrderVersion();
        Long timeStamp = Long.parseLong(orderVersion);
        //构建初筛条件
        PushCommonObject pushCommonObject;
        pushCommonObject = new PushCommonObject();
        pushCommonObject.setOrderDetailData(orderDetailData);
        pushCommonObject.setOrderVersion(orderVersion);
        pushCommonObject.setMasterMatchCondition(masterMatchCondition);
        pushCommonObject.setTimestamp(timeStamp);
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        //根据策略推单
        pushByStrategy(pushCommonObject,timeMark,orderPushRqt,null);
        return 1;
    }


    @Override
    public Integer batchOrderMatch(BatchOrderMatchRqt batchOrderMatchRqt){
        CompletableFuture.runAsync(() -> {
            batchOrderMatchRqt.getOrderIdList().forEach(orderId -> pushQueueService.sendOrderMatchMessage(orderId));
        });
        return batchOrderMatchRqt.getOrderIdList().size();
    }





}
