package com.wanshifu.master.order.push.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.CompensateDistributeMessage;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.AgreementMasterMatchRepository;
import com.wanshifu.master.order.push.repository.AgreementMasterPushRepository;
import com.wanshifu.master.order.push.repository.CompensateDistributeRepository;
import com.wanshifu.master.order.push.repository.OrderDistributeRepository;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.PortPushService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议订单调度结果通知
 * <AUTHOR>
 * @description
 * @date 2025/2/25 18:36
 */
@Service
@Slf4j
public class AgreementOrderDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {


    @Resource
    private OrderDistributeRepository orderDistributeRepository;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AgreementMasterMatchRepository agreementMasterMatchRepository;


    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;



    @Resource
    private AgreementMasterPushRepository agreementMasterPushRepository;

    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private PortPushService portPushService;

    @Resource
    private PushRecordService pushRecordService;


    @Value("${agreementOrderDistribute.move.switch:on}")
    private String agreementOrderDistributeMoveSwitch;


    /**
     * 家庭app推C端师傅城市开放配置
     */
    @Value("${push.toc.cityList:}")
    private String pushTocCityList;


    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage){

        if(!"on".equals(agreementOrderDistributeMoveSwitch)){
            return 0;
        }
        Long orderId = orderDistributeResultMessage.getOrderId();

        String extraId = orderDistributeResultMessage.getExtraId();
        OrderDistribute orderDistribute = orderDistributeRepository.selectByPrimaryKey(Long.valueOf(extraId));


        List<Long> masterIdList = orderDistributeResultMessage.getSuccessMasterList();

        List<JSONObject> jsonObjectList = orderDistributeResultMessage.getMasterFailVos();
        Map<Long,JSONObject> failMap = new HashMap<>();
        try{
            if(CollectionUtils.isNotEmpty(jsonObjectList)){
                jsonObjectList.forEach(jsonObject -> {
                    failMap.put( jsonObject.getLong("masterId"),jsonObject.getJSONObject("object"));
                });
            }
        }catch(Exception e){
            log.error("parseDistributeRemark error ",e);
        }

        List<DistributeResultNotices.MasterInfo> masterInfoList = JSON.parseArray(orderDistribute.getMasterList(),DistributeResultNotices.MasterInfo.class);
        masterInfoList.forEach(masterInfo -> {
            if(CollectionUtils.isNotEmpty(masterIdList) && masterIdList.contains(masterInfo.getMasterId())){
                masterInfo.setDistributeResult(1);
            }else{
                masterInfo.setDistributeResult(0);
                masterInfo.setDistributeResultRemark(failMap.containsKey(masterInfo.getMasterId()) ? failMap.get(masterInfo.getMasterId()).getString("tipContent") : "");
            }
        });



        Integer distributeResult = 1;


        if(CollectionUtils.isNotEmpty(orderDistributeResultMessage.getSuccessMasterList())){
            distributeResult = 1;
        }else{
            distributeResult = 0;
        }


        if(distributeResult == 0){
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderId);
            rqt.setOrderPushEliminateMasterIds(Collections.singletonList(masterInfoList.get(0).getMasterId()));
            pushQueueService.sendDelayPushMessage(100L,JSON.toJSONString(rqt));
//            orde.match(rqt);

            Set<DistributeResultNotices.MasterInfo> failMasterInfoList = masterInfoList.stream().filter(masterInfo -> masterInfo.getDistributeResult() == 0).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(failMasterInfoList)){
                failMasterInfoList.forEach(masterInfo -> {
                    this.updateAgreementMasterMatch(orderId,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()),masterInfo.getDistributeResult(),masterInfo.getDistributeResultRemark());
                });
            }
            return 0;
        }
        OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderId,null);
        OrderBase orderBase = orderBaseComposite.getOrderBase();
        OrderGrab orderGrab = orderBaseComposite.getOrderGrab();
        Integer businessLineId = orderBase.getBusinessLineId();
        Long secondDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());

        //推单师傅类型
        String masterSourceType = getMasterSourceType(businessLineId, secondDivisionId);
        if (MasterSourceType.TOC.code.equals(masterSourceType)) {
            //C端协议师傅推单后匹配端口规则
            OrderDetailData orderDetailData = new OrderDetailData();
            orderDetailData.setMasterOrderId(orderBase.getOrderId());
            orderDetailData.setSecondDivisionId(secondDivisionId);
            orderDetailData.setLv1ServeIds(orderBase.getServeLevel1Ids());
            orderDetailData.setAppointType(orderGrab.getAppointType());

            int pushNum = 0;
            if(CollectionUtils.isNotEmpty(orderDistributeResultMessage.getSuccessMasterList())){
                pushNum = pushNum + orderDistributeResultMessage.getSuccessMasterList().size();
            }

            if(CollectionUtils.isNotEmpty(orderDistributeResultMessage.getFailMasterList())){
                pushNum = pushNum + orderDistributeResultMessage.getFailMasterList().size();
            }
            portPushService.matchPushPortRule("agreement_master",orderDetailData,pushNum);
        }


        List<DistributeResultNotices.MasterInfo> successMasterInfoList = masterInfoList.stream().filter(masterInfo -> masterInfo.getDistributeResult() == 1).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(successMasterInfoList)){
            return 0;
        }

        String recruitId = successMasterInfoList.get(0).getRecruitId();
        Long masterId = successMasterInfoList.get(0).getMasterId();

        //抢单成功，更新协议师傅当日抢单量指标
        if (AppointDetailType.AUTO_GRAB_AGREEMENT.getCode().equals(orderDistributeResultMessage.getAppointDetailType())) {
            //协议师傅自动抢单
            log.info("AUTO_GRAB_AGREEMENT update dailyOrderCnt,orderId:{},masterId:{},recruitId:{}", orderId, masterId, recruitId);
            pushRecordService.increaseMasterRecruitDaily(masterId, Long.valueOf(recruitId), DateFormatterUtil.getNow());
        }

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("recruitId",Long.valueOf(recruitId)));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId",Long.valueOf(masterId)));
        EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder);

        AgreementMaster agreementMaster = esResponse.getDataList().get(0);
        Integer hasCooperationUser = StringUtils.isNotBlank(agreementMaster.getUserIds()) ? 1 : 0;

        Integer hasPrice =  !"noCooperationPrice".equals(agreementMaster.getPricingType()) ? 1 : 0;


        String today = DateFormatterUtil.getNow();
        masterInfoList.forEach(masterInfo -> {
            this.updateAgreementMasterPush(today,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()));
        });



        if(CollectionUtils.isNotEmpty( masterInfoList)){
            masterInfoList.forEach(masterInfo -> {
                this.updateAgreementMasterMatch(orderId,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()),masterInfo.getDistributeResult(),masterInfo.getDistributeResultRemark());
            });
        }


        List<CompensateDistribute> compensateDistributeList = getCompensateDistributeList(masterSourceType, orderBase.getBusinessLineId(), Long.valueOf(orderBase.getCategoryId()), orderGrab.getAppointType(), hasPrice, hasCooperationUser);

        if(CollectionUtils.isEmpty(compensateDistributeList)){
            log.error("compensateDistribute error,distributeResultNotices:" + JSON.toJSONString(orderDistributeResultMessage));
            return 0;
        }

        compensateDistributeList.forEach(compensateDistribute -> {
            CompensateDistributeMessage message = new CompensateDistributeMessage();
            message.setOrderId(orderBase.getOrderId());
            message.setCompensateDistribute(compensateDistribute);
            int intervalTime = compensateDistribute.getIntervalTime();
            long delayTime;
            if(intervalTime == 0){
                delayTime = 5L;
            }else{
                delayTime = intervalTime * 60 * 1000L;
            }
            pushQueueService.sendCompensateDistributeMessage(message,delayTime);
        });


        return 1;


    }


    private void updateAgreementMasterMatch(Long orderId,Long masterId,Long recruitId,Integer isDistributeSucc,String distributeFailReason){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterMatch agreementMasterMatch = agreementMasterMatchRepository.selectByAgreementMasterId(orderId,agreementMasterId);

        if(Objects.isNull(agreementMasterMatch)){
            return ;
        }

        AgreementMasterMatch updateAgreementMasterMatch = new AgreementMasterMatch();
        updateAgreementMasterMatch.setId(agreementMasterMatch.getId());
        updateAgreementMasterMatch.setIsAutoReceiveSucc(isDistributeSucc);
        updateAgreementMasterMatch.setAutoReceiveFailReason(distributeFailReason);
        agreementMasterMatchRepository.updateByPrimaryKeySelective(updateAgreementMasterMatch);
    }

    private void updateAgreementMasterPush(String today,Long masterId,Long recruitId){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterPush agreementMasterPush = agreementMasterPushRepository.selectByAgreementMasterId(agreementMasterId);

        if(Objects.isNull(agreementMasterPush)){
            agreementMasterPush = new AgreementMasterPush();
            agreementMasterPush.setAgreementMasterId(recruitId + ":" + masterId);
            agreementMasterPush.setMasterId(masterId);
            agreementMasterPush.setRecruitId(recruitId);
            agreementMasterPush.setDt(today);
            agreementMasterPush.setPushCountDaily(1L);
            agreementMasterPushRepository.insertSelective(agreementMasterPush);
            return ;
        }


        final String dt = agreementMasterPush.getDt();
        AgreementMasterPush updateAgreementMasterPush = new AgreementMasterPush();
        updateAgreementMasterPush.setId(agreementMasterPush.getId());
        if (org.apache.commons.lang.StringUtils.isEmpty(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else if (!today.equals(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else {
            updateAgreementMasterPush.setPushCountDaily(agreementMasterPush.getPushCountDaily() + 1L);
        }
        agreementMasterPushRepository.updateByPrimaryKeySelective(updateAgreementMasterPush);
    }

    private List<CompensateDistribute> getCompensateDistributeList(String masterSourceType, Integer businessLineId, Long categoryId, Integer appointType, Integer hasPrice, Integer hasCooperationUser) {

        if (businessLineId == 2 && MasterSourceType.TOC.code.equals(masterSourceType)) {
            businessLineId = 999;
        }

        List<CompensateDistribute> compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, hasPrice, hasCooperationUser);

        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, 2, hasCooperationUser);
        }


        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, hasPrice, 2);
        }

        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, 2, 2);
        }

        return compensateDistributeList;

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_OFFER_AGREEMENT.getCode(), this);
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_AGREEMENT.getCode(), this);


    }

    private String getMasterSourceType(Integer businessLineId, Long secondDivisionId) {

        final List<String> toCMasterCityList =
                new ArrayList<>(Arrays.asList(pushTocCityList.split(",")));

        if (businessLineId == 2
                && (toCMasterCityList.contains(secondDivisionId.toString()) || toCMasterCityList.contains("all"))) {
            return MasterSourceType.TOC.code;
        }else {
            return MasterSourceType.TOB.code;
        }
    }

}
