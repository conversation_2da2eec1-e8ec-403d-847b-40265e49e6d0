package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.repository.OrderDistributeRepository;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 合作经营师傅调度
 * @date 2025/3/12 10:36
 */
@Service
public class CooperationBusinessDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private OrderDistributeRepository orderDistributeRepository;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice) {

        GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
        getOrderIdRqt.setGlobalOrderTraceId(orderPushedResultNotice.getGlobalOrderTraceId());
        Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        Integer appointType = orderGrab.getAppointType();
        String pushMode = orderPushedResultNotice.getPushMode();


        //合作经营师傅自动抢单
        List<CooperationBusinessMasterAutoGrabDto> autoGrabDtoList = orderPushedResultNotice.getMasterInfoList().stream()
                .map(masterInfo -> {
                    CooperationBusinessMasterAutoGrabDto autoGrabDto = new CooperationBusinessMasterAutoGrabDto();
                    BeanUtil.copyProperties(masterInfo, autoGrabDto);
                    return autoGrabDto;
                })
                .collect(Collectors.toList());

        //排序
        autoGrabDtoList = autoGrabDtoList.stream()
                .sorted(Comparator.comparingInt(CooperationBusinessMasterAutoGrabDto::getAutoOfferSort))
                .collect(Collectors.toList());;

        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();

        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setAppointType(appointType);
        rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_COOPERATION_BUSINESS.getCode());

        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        autoGrabDtoList.forEach(autoGrabDto -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(autoGrabDto.getMasterId());
            masterList.add(masterPrice);
        });

        rqt.setMasterList(masterList);


        Long distributeId = orderDistributeRepository.insertOrderDistribute(orderId, pushMode, JSON.toJSONString(orderPushedResultNotice.getMasterInfoList()));
        rqt.setExtraId(String.valueOf(distributeId));
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),1L);
        return 1;
    }

    @Override
    public void afterPropertiesSet() {
        OrderDistributeContext.register(PushMode.COOPERATION_BUSINESS_MASTER.code, this);
    }
}
