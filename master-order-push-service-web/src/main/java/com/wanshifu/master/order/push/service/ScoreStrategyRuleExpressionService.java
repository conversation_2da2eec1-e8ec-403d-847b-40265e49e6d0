package com.wanshifu.master.order.push.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.ItemTypeEnum;
import com.wanshifu.master.order.push.domain.po.OrderScoreItem;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.CreateOrderScoringStrategyRqt;
import com.wanshifu.master.order.push.repository.OrderScoreItemRepository;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 17:50
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ScoreStrategyRuleExpressionService {


    @Lazy
    private final OrderScoreItemRepository orderScoreItemRepository;

    public List<ScoreRuleExpressionDto> getRuleExpressions(List<CreateOrderScoringStrategyRqt.RuleItem> itemList) {
        List<String> itemNames = itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getItemName).collect(Collectors.toList());
        List<OrderScoreItem> scoreItems = orderScoreItemRepository.selectByItemCodes(itemNames);
        //Map<item_code,feature_expression>
        Map<String, String> scoreItemFeatureExpressionMap = scoreItems.stream().collect(Collectors.toMap(OrderScoreItem::getItemCode, OrderScoreItem::getFeatureExpression));
        return itemList.stream().map(item -> {
            //部分匹配项隐藏的开启条件 例如：{"openConditionfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"},"filterfeatureExpression":{"ruleExpression":"is_high_quality_user == 1","ruleExpressionParams":"is_high_quality_user"}}
            String featureExpressionJson = scoreItemFeatureExpressionMap.getOrDefault(item.getItemName(), "");
            //开启条件中的参数名称
            String openConditionRuleExpression = "";
            String openConditionRuleParams = "";

            String filterFeatureRuleExpressionParams = "";
            String filterFeatureRuleExpression = "";
            if (StringUtils.isNotBlank(featureExpressionJson)) {
                ScoreItemFeatureExpressionDto scoreItemFeatureExpressionDto = JSON.parseObject(featureExpressionJson, ScoreItemFeatureExpressionDto.class);
                openConditionRuleParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                openConditionRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getOpenConditionfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");

                filterFeatureRuleExpressionParams = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpressionParams).orElse("");
                filterFeatureRuleExpression = Optional.ofNullable(scoreItemFeatureExpressionDto.getFilterfeatureExpression()).map(ScoreItemFeatureExpressionDto.FeatureExpression::getRuleExpression).orElse("");
            }

            List<CreateOrderScoringStrategyRqt.RuleItem.ScoreItem> scoreList = item.getScoreList();
            List<IfExpressDto> ifExpressDtos = scoreList.stream().map(scoreItem -> {
                String returnRuleExpression = QlExpressUtil.transitionQlExpress(scoreItem.getScore().toPlainString(), "*", item.getWeight().toPlainString(),null);
                String ifRuleExpression;
                if (StringUtils.equals(item.getAssignMode(), "range_value")) {
                    //区间 例: order_master_distance >= 12 and order_master_distance <100.30
                    ifRuleExpression = QlExpressUtil.transitionQlExpress("and",
                            Lists.newArrayList(
                                    new QlExpressDto(item.getItemName(), ">=", scoreItem.getStartValue().toPlainString()),
                                    new QlExpressDto(item.getItemName(), "<", scoreItem.getEndValue().toPlainString())
                            ));
                } else {
                    //枚举 例: is_like_low_price_offer == 'A'
                    if(ItemTypeEnum.MASTER_GROUP.getCode().equals(item.getItemType())){
                        ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "contain", scoreItem.getValue(),null);
                    }else{
                        ifRuleExpression = QlExpressUtil.transitionQlExpress(item.getItemName(), "=", scoreItem.getValue(),null);
                    }
                }
                return new IfExpressDto(ifRuleExpression, returnRuleExpression);
            }).collect(Collectors.toList());
            //计算匹配项分值的表达式 例如：if(a+b==12) then {return 7*0.6;}   else if(a+b==11)then{return 2;}    else {return 0;}
            String filterRuleExpression = QlExpressUtil.transitionQlExpress(ifExpressDtos, "0");
            //评分项隐藏的条件 不满足该项隐藏条件时，该项评分得0
            if (StringUtils.isNotBlank(filterFeatureRuleExpression)) {
                filterRuleExpression = StrUtil.format("if(!({})) return 0; ", filterFeatureRuleExpression) + filterRuleExpression;
            }

            String filterRuleParams = item.getItemName();
            if (StringUtils.isNotBlank(filterFeatureRuleExpressionParams)) {
                filterRuleParams = StrUtil.format("{},{}", filterFeatureRuleExpressionParams, filterRuleParams);
            }
            return new ScoreRuleExpressionDto(item.getItemTitle(), item.getItemName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }


    public List<SortRuleExpressionDto> buildSortRuleExpressions(List<CreateOrderScoringStrategyRqt.SortRule> ruleList){
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = new ArrayList<>(ruleList.size());
        ruleList.forEach(sortRule -> {
            SortRuleExpressionDto sortRuleExpressionDto = new SortRuleExpressionDto();
            buildOpenConditionExpression(sortRuleExpressionDto,sortRule);
            List<CreateOrderScoringStrategyRqt.RuleItem> ruleItemList = sortRule.getItemList();
            sortRuleExpressionDto.setScoreRuleList(this.getRuleExpressions(ruleItemList));
            sortRuleExpressionDtoList.add(sortRuleExpressionDto);
        });
        return sortRuleExpressionDtoList;
    }


    private void buildOpenConditionExpression(SortRuleExpressionDto sortRuleExpressionDto,CreateOrderScoringStrategyRqt.SortRule sortRule){
        CreateOrderScoringStrategyRqt.SortRule.OpenCondition openCondition = sortRule.getOpenCondition();

        //开启条件表达式
        String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                        .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                        .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                    //时效标签 和 用户人群 操作符符号转换
                    List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user");
                    if(specialTerms.contains(s.getItemName())){
                        t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                    }
                }));


        //服务 的开启条件特殊处理
        List<CreateOrderScoringStrategyRqt.SortRule.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

        List<String> serveExpression = serveItems.stream().map(it -> {
            List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
            String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
            String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

            List<List<Long>> serveIdList = it.getServeIdList();

            List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

            List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

            List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","),Long.class));
            }
            return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
        }).collect(Collectors.toList());
        String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
        if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
            openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
        } else {
            openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
        }

        sortRuleExpressionDto.setOpenConditionRuleExpression(openConditionRuleExpression);

        //开启条件表达式参数
        List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(CreateOrderScoringStrategyRqt.SortRule.OpenConditionItem::getItemName)
                .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serveItems)) {
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                openConditionRuleParamsList.add("lv1_serve_id");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                openConditionRuleParamsList.add("lv2_serve_ids");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                openConditionRuleParamsList.add("lv3_serve_ids");
        }
        //开启条件表达式参数
        String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));
        sortRuleExpressionDto.setOpenConditionRuleParams(openConditionRuleParams);
    }

}
