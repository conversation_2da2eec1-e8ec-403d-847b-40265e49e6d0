package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.RepushPolicy;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 重推机制Mapper
 * <AUTHOR>
 */
public interface RepushPolicyMapper extends IBaseCommMapper<RepushPolicy> {

    List<RepushPolicy> selectByCategoryIdAndCityId(@Param("orderFlag") String orderFlag,@Param("businessLineId") Integer businessLineId, @Param("categoryId") String categoryId, @Param("cityId") String cityId);

    RepushPolicy selectByCityAndCategory(@Param("orderFlag") String orderFlag,@Param("cityIdStr") String cityIdStr, @Param("categoryIdList") List<String> categoryIdList, @Param("policyId") Long policyId,@Param("businessLineId") Integer businessLineId);

}