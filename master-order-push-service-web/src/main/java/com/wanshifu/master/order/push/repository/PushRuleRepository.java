package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.mapper.PushRuleMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class PushRuleRepository extends BaseRepository<PushRule> {

    @Resource
    private PushRuleMapper pushRuleMapper;


    public int insertPushRule(Integer businessLineId,String ruleName,String ruleDesc,String pushRuleList,Long createAccountId){
        PushRule pushRule = new PushRule();
        pushRule.setBusinessLineId(businessLineId);
        pushRule.setRuleName(ruleName);
        pushRule.setRuleDesc(ruleDesc);
        pushRule.setPushRuleList(pushRuleList);
        pushRule.setCreateAccountId(createAccountId);
        pushRule.setUpdateAccountId(createAccountId);
        return this.insertSelective(pushRule);
    }


    public int updatePushRule(Integer ruleId,Integer businessLineId,String ruleName,String ruleDesc,String pushRuleList,Long updateAccountId){
        PushRule pushRule = new PushRule();
        pushRule.setRuleId(ruleId);
        pushRule.setBusinessLineId(businessLineId);
        pushRule.setRuleName(ruleName);
        pushRule.setRuleDesc(ruleDesc);
        pushRule.setPushRuleList(pushRuleList);
        pushRule.setUpdateAccountId(updateAccountId);
        pushRule.setUpdateTime(new Date());
        return this.updateByPrimaryKeySelective(pushRule);
    }


    public int softDelete(Integer ruleId){
        PushRule pushRule = new PushRule();
        pushRule.setRuleId(ruleId);
        pushRule.setIsDelete(1);
        return this.updateByPrimaryKeySelective(pushRule);
    }

    public List<PushRule> selectList(Integer businessLineId,String ruleName, Date createStartTime, Date createEndTime){
        return pushRuleMapper.selectList(businessLineId,ruleName,createStartTime,createEndTime);
    }


    public PushRule selectByRuleName(String ruleName,Integer ruleId) {
        Example example = new Example(PushRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleName", ruleName)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (ruleId != null) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }



}
