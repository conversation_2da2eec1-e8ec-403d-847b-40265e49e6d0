package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.MasterSettleInRqt;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.message.*;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.MasterRepository;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.api.grabdefinite.GrabDefiniteResourceApi;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.offer.GetOfferedPriceListByGlobalOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.api.response.offer.GetOfferedPriceListByGlobalOrderIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.push.api.OrderPushListApi;
import com.wanshifu.order.push.request.GetFamilyOrderPushListRqt;
import com.wanshifu.order.push.request.WaitOfferNoPageRqt;
import com.wanshifu.order.push.response.BatchOrderPushResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.queryparser.xml.builders.BooleanQueryBuilder;
import org.apache.lucene.search.BooleanQuery;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class MasterOperationNotifyServiceImpl implements MasterOperationNotifyService {

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private OrderPushListApi orderPushListApi;

    @Resource
    private GrabDefiniteResourceApi grabDefiniteResourceApi;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String orderMatchMasterTopic;


    @Value("${afresh.distribute.main.master:on}")
    private String afreshDistributeMainMaster;


    @Value("${push.gold.medal.master.offerPrice.num:2}")
    private Integer pushGoldMedalMasterOfferPriceNum;

    @Value("${push.gold.medal.master.offerPrice:300}")
    private BigDecimal pushGoldMedalMasterOfferPrice;



    private final String ORDER_MATCH_MASTER = "order_match_master";

    @Resource
    private OrderMatchMasterService orderMatchMasterService;


    @Resource
    private MasterRepository masterRepository;

    @Resource
    private MasterSelector masterSelector;


    @Resource
    private NormalOrderDistributeServiceImpl orderDistributeServiceImpl;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Resource
    private OrderDataBuilder orderDataBuilder;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;


    /**
     * 师傅预约客户
     * @param message
     * @return
     */
    @Override
    public Integer masterReserveCustomer(MasterReserveCustomerMessage message){
        return afreshDistributeMainMaster(message.getMasterId());
    }


    /**
     * 师傅挂起订单
     * @param message
     * @return
     */
    @Override
    public Integer masterHangOrder(MasterHangOrderMessage message){
        if(CollectionUtils.isEmpty(message.getOrderMasterInfoList())){
            return 0;
        }
        Set<Long> masterSet = message.getOrderMasterInfoList().stream().map(MasterHangOrderMessage.HangOrderServeInfo::getMasterId).collect(Collectors.toSet());
        masterSet.forEach(masterId -> afreshDistributeMainMaster(masterId));
        return masterSet.size();
    }

    /**
     * 服务完工-非返货
     * @param message
     * @return
     */
    @Override
    public Integer masterServeFinish(MasterServeFinishMessage message){
        return afreshDistributeMainMaster(message.getMasterId());
    }


    /**
     * 服务完工-返货
     * @param message
     * @return
     */
    @Override
    public Integer masterServeReturnFinish(MasterServeReturnFinishMessage message){
        return afreshDistributeMainMaster(message.getMasterId());
    }

    /**
     * 师傅同意退款
     * @param message
     * @return
     */
    @Override
    public Integer masterAgreeRefund(MasterAgreeRefundMessage message){
        return afreshDistributeMainMaster(message.getMasterId());
    }


    public int afreshDistributeMainMaster(Long masterId){

        if(!"on".equals(afreshDistributeMainMaster)){
            return 0;
        }

        MasterBaseSearch masterBaseSearch = masterBaseEsRepository.searchByMasterId(masterId);
        if(Objects.isNull(masterBaseSearch)){
            return 0;
        }


        List<AgreementMaster> agreementMasterList = agreementMasterEsRespository.searchMainMasterByMasterId(masterId);
        if(CollectionUtils.isEmpty(agreementMasterList)){
            return 0;
        }

        WaitOfferNoPageRqt waitOfferNoPageRqt = new WaitOfferNoPageRqt();
        waitOfferNoPageRqt.setMasterId(masterId);
        waitOfferNoPageRqt.setProvinceNextId(masterBaseSearch.getCityDivisionId());
        waitOfferNoPageRqt.setTmplCityMasterRole(1);
        List<BatchOrderPushResp> orderPushList =  orderPushListApi.getMasterOrderPushList(waitOfferNoPageRqt);
//        List<BatchOrderPushResp> orderPushList =  new ArrayList<>();
//        BatchOrderPushResp orderPushResp = new BatchOrderPushResp();
//        orderPushResp.setOrderId(61498833287L);
//        orderPushList.add(orderPushResp);

        if(CollectionUtils.isNotEmpty(orderPushList)){
            orderPushList.forEach(orderPush -> {
                Long orderId = orderPush.getOrderId();
                Integer isOrderLocking = grabDefiniteResourceApi.isOrderLocking(orderId);
                if(isOrderLocking == 1){
                    return ;
                }
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(orderPush.getOrderId());
                orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.AFRESH_NEW_MODEL_SINGLE.getCode());
                rocketMqSendService.sendSyncMessage(orderMatchMasterTopic,ORDER_MATCH_MASTER, JSON.toJSONString(orderMatchMasterRqt));
            });
        }

        return 1;

    }


    /**
     * 师傅更改休息状态
     * @param message
     * @return
     */
    @Override
    public Integer masterRestState(MasterRestStateMessage message){
        if(message.getRestState() == 1){
            return afreshDistributeMainMaster(message.getMasterId());
        }
        return 0;
    }



    @Override
    public Integer addTechnique(MasterAddTechniqueMessage message){
        Long masterId = message.getMasterId();
        Master master = masterRepository.selectByPrimaryKey(String.valueOf(masterId));
        if(Objects.isNull(master)){
            throw new BusException(String.format("师傅不存在，masterId:%d",message.getMasterId()));
        }
        String coreFourthDivisionIds = master.getCoreFourthDivisionIds();
        if(StringUtils.isBlank(coreFourthDivisionIds)){
            return 0;
        }
        Set<Long> fourthDivisionIdSet = Arrays.stream(coreFourthDivisionIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        if(StringUtils.isNotEmpty(master.getImportantFourthDivisionIds())){
            fourthDivisionIdSet.addAll(Arrays.stream(master.getImportantFourthDivisionIds().split(",")).map(Long::parseLong).collect(Collectors.toSet()));
        }
        if(StringUtils.isNotEmpty(master.getOtherFourthDivisionId())){
            fourthDivisionIdSet.addAll(Arrays.stream(master.getOtherFourthDivisionId().split(",")).map(Long::parseLong).collect(Collectors.toSet()));
        }
        List<Long> addTechniqueList = Stream.of(message.getAddTechniqueIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        BoolQueryBuilder masterBoolQueryBuilder = new BoolQueryBuilder();
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("masterId",masterId));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

        EsResponse<MasterBaseSearch> masterBaseSearchEsResponse = masterBaseEsRepository.search(masterBoolQueryBuilder);
        if(CollectionUtils.isEmpty(masterBaseSearchEsResponse.getDataList())){
            return 0;
        }

        MasterBaseSearch masterBase = masterBaseSearchEsResponse.getDataList().get(0);

        String masterTechniqueIds = masterBase.getMasterTechniqueIds();

        //限制推单的服务
        String masterForbiddenServeIds = masterBase.getMasterForbiddenServeIds();
        //限制推单的业务线
        String masterForbiddenBusinessIds = masterBase.getMasterForbiddenBusinessIds();

        Set<Long> orderIdSet = masterSelector.searchOrderList(message.getMasterId(), fourthDivisionIdSet,
                null, addTechniqueList, masterTechniqueIds,
                masterForbiddenServeIds, masterForbiddenBusinessIds,masterBase);
        log.info("addTechnique,orderIdSet",orderIdSet);
        if(CollectionUtils.isNotEmpty(orderIdSet)){
            orderMatchMasterService.directPush(masterId,orderIdSet);
        }
        return 1;
    }


    @Override
    public Integer addServeFourthDivision(MasterAddFourthDivisionMessage message){
        Long masterId = message.getMasterId();
        Master master = masterRepository.selectByPrimaryKey(String.valueOf(masterId));
        if(Objects.isNull(master)){
            throw new BusException(String.format("师傅不存在，masterId:%d",message.getMasterId()));
        }
        String coreFourthDivisionIds = master.getCoreFourthDivisionIds();

        if(StringUtils.isBlank(coreFourthDivisionIds)){
            return 0;
        }

        Set<Long> fourthDivisionIdSet = Arrays.stream(coreFourthDivisionIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        List<Long> addFourthDivisionIds = Stream.of(message.getAddServeFourthDivisionIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());


        BoolQueryBuilder masterBoolQueryBuilder = new BoolQueryBuilder();
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("masterId",masterId));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

        EsResponse<MasterBaseSearch> masterBaseSearchEsResponse = masterBaseEsRepository.search(masterBoolQueryBuilder);
        if(CollectionUtils.isEmpty(masterBaseSearchEsResponse.getDataList())){
            return 0;
        }

        MasterBaseSearch masterBase = masterBaseSearchEsResponse.getDataList().get(0);

        String masterTechniqueIds = masterBase.getMasterTechniqueIds();

        //限制推单的服务
        String masterForbiddenServeIds = masterBase.getMasterForbiddenServeIds();
        //限制推单的业务线
        String masterForbiddenBusinessIds = masterBase.getMasterForbiddenBusinessIds();

        Set<Long> orderIdSet = masterSelector.searchOrderList(message.getMasterId(), fourthDivisionIdSet,
                addFourthDivisionIds, null, masterTechniqueIds,
                masterForbiddenServeIds, masterForbiddenBusinessIds,masterBase);
        log.info("addServeFourthDivision,orderIdSet",orderIdSet);

        if(CollectionUtils.isNotEmpty(orderIdSet)){
            orderMatchMasterService.directPush(masterId,orderIdSet);
        }
        return 1;
    }


    @Override
    public void masterSettleIn(MasterSettleInRqt message){

        try{
            Thread.sleep(1000);
        }catch(Exception e){
        }


        Long masterId = message.getMasterId();
        Master master = masterRepository.selectByPrimaryKey(String.valueOf(message.getMasterId()));
        if(Objects.isNull(master)){
            throw new BusException(String.format("师傅不存在，masterId:%d",message.getMasterId()));
        }
        String coreFourthDivisionIds = master.getCoreFourthDivisionIds();


        Set<Long> fourthDivisionIdSet = Collections.emptySet();

        if(StringUtils.isNotBlank(coreFourthDivisionIds)){
            fourthDivisionIdSet = Arrays.stream(coreFourthDivisionIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }


        BoolQueryBuilder masterBoolQueryBuilder = new BoolQueryBuilder();
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("masterId",masterId));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        masterBoolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

        EsResponse<MasterBaseSearch> masterBaseSearchEsResponse = masterBaseEsRepository.search(masterBoolQueryBuilder);
        if(CollectionUtils.isEmpty(masterBaseSearchEsResponse.getDataList())){
            return ;
        }

        MasterBaseSearch masterBase = masterBaseSearchEsResponse.getDataList().get(0);

        log.info("pushOrderToTocMaster");
        pushOrderToTocMaster(Long.valueOf(masterBase.getMasterId()));

        //限制推单的服务
        String masterForbiddenServeIds = masterBase.getMasterForbiddenServeIds();
        //限制推单的业务线
        String masterForbiddenBusinessIds = masterBase.getMasterForbiddenBusinessIds();


        String masterTechniqueIds = masterBase.getMasterTechniqueIds();
        Set<Long> orderIdSet = Collections.emptySet();
        if(CollectionUtils.isEmpty(fourthDivisionIdSet)){
            if(StringUtils.isNotBlank(master.getLngLat())){
                double lat = Double.valueOf(master.getLngLat().split(",")[1]);
                double lng = Double.valueOf(master.getLngLat().split(",")[0]);
                if(lat > 0 && lng > 0){
                    orderIdSet = masterSelector.searchOrderListByDistance(lat, lng, masterTechniqueIds,
                            masterForbiddenServeIds, masterForbiddenBusinessIds);
                }
            }
        }else{
            orderIdSet = masterSelector.searchOrderListByDivision(fourthDivisionIdSet, masterTechniqueIds,
                    masterForbiddenServeIds, masterForbiddenBusinessIds);
        }


        String orderVersion = String.valueOf(System.currentTimeMillis());

        if(CollectionUtils.isEmpty(orderIdSet)){
            return ;
        }
        orderIdSet.forEach(orderId ->{

            try{
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(orderId);
                orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);

                JSONObject commonFeature = new JSONObject();
                commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
                commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                //长尾单,不更改推送参数:IS_NEARBY_PUSH
                commonFeature.put(FieldConstant.HAND_OFF_TAG, "new");
                commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH, false);

                Set<String> masterSet = Collections.singleton(master.getMasterId());

                commonFeature.put(FieldConstant.ORDER_LNG_LAT, orderDetailData.getOrderLngLat());


                pushControllerFacade.directPush(orderDetailData,orderVersion,masterSet,commonFeature);
            }catch(Exception e){
                log.error("masterSettleIn push order error",e);
            }

        });






    }



    @Value("${pushOrder.tocMaster.switch:on}")
    private String pushOrderTocMasterSwitch;

    @Value("${pushOrder.tocMaster.limitCount:20}")
    private Integer pushOrderTocMasterLimitCount;

    private Integer pushOrderToTocMaster(Long tocMasterId){

        if(!"on".equals(pushOrderTocMasterSwitch)){
            return 0;
        }

        MasterBaseSearch tocMaster = masterBaseEsRepository.searchByMasterId(tocMasterId);
        if(Objects.isNull(tocMaster) || MasterSourceType.TOB.code.equals(tocMaster.getMasterSourceType()) ||
                StringUtils.isBlank(tocMaster.getIdCardNumber())){
            return 0;
        }


        List<MasterBaseSearch> masterBaseSearchList = masterBaseEsRepository.searchByIdCardNumberAndMasterSourceType(tocMaster.getIdCardNumber(),MasterSourceType.TOB.code);
        if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
            MasterBaseSearch masterBaseSearch = masterBaseSearchList.get(0);
            if(Objects.isNull(masterBaseSearch.getCityDivisionId()) || masterBaseSearch.getCityDivisionId() == 0L){
                return 0;
            }
            GetFamilyOrderPushListRqt rqt = new GetFamilyOrderPushListRqt();
            rqt.setMasterId(Long.valueOf(masterBaseSearch.getMasterId()));
            rqt.setLimitCount(pushOrderTocMasterLimitCount);
            rqt.setProvinceNextId(masterBaseSearch.getCityDivisionId());
            List<BatchOrderPushResp> orderPushList =  orderPushListApi.getFamilyOrderPushList(rqt);
            if(CollectionUtils.isNotEmpty(orderPushList)){

                if(!apolloConfigUtils.checkPushOrderToMasterSwitch()){


                    orderPushList.forEach(resp -> {
                        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                        orderMatchMasterRqt.setMasterOrderId(resp.getOrderId());
                        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                        OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);

                        JSONObject commonFeature = new JSONObject();
                        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
                        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                        //长尾单,不更改推送参数:IS_NEARBY_PUSH
                        commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH, false);

                        Set<String> masterSet = Collections.singleton(String.valueOf(tocMasterId));

                        commonFeature.put(FieldConstant.ORDER_LNG_LAT, orderDetailData.getOrderLngLat());

                        String orderVersion = String.valueOf(System.currentTimeMillis());

                        pushControllerFacade.directPush(orderDetailData,orderVersion,masterSet,commonFeature);


                    });



                }else{

                    List<OrderDetailData> orderDetailDataList = new ArrayList<>();

                    orderPushList.forEach(resp -> {
                        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                        orderMatchMasterRqt.setMasterOrderId(resp.getOrderId());
                        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                        OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
                        orderDetailDataList.add(orderDetailData);
                    });


                    JSONObject commonFeature = new JSONObject();
                    commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                    commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                    commonFeature.put(FieldConstant.MATCH_SCENE_CODE, MatchSceneCode.TOC_MASTER_AT_TOB_APP_OFFER_LIMIT.getCode());


                    pushControllerFacade.pushOrderToMaster(orderDetailDataList.get(0).getOrderVersion(),orderDetailDataList,String.valueOf(tocMasterId),commonFeature,0,MasterSourceType.TOC.code);

                }


            }
        }

        return 1;
    }




    private Integer pushOrderToTocMasterByTobMasterId(Long tobMasterId){


        if(!"on".equals(pushOrderTocMasterSwitch)){
            return 0;
        }

        MasterBaseSearch tobMaster = masterBaseEsRepository.searchByMasterId(tobMasterId);
        if(Objects.isNull(tobMaster) || MasterSourceType.TOC.code.equals(tobMaster.getMasterSourceType()) ||
        StringUtils.isBlank(tobMaster.getIdCardNumber())){
            return 0;
        }


        List<MasterBaseSearch> tocMasterBaseSearchList = masterBaseEsRepository.searchByIdCardNumberAndMasterSourceType(tobMaster.getIdCardNumber(),MasterSourceType.TOC.code);
        if(CollectionUtils.isNotEmpty(tocMasterBaseSearchList)){
            MasterBaseSearch tocMaster = tocMasterBaseSearchList.get(0);
            if(Objects.isNull(tocMaster.getCityDivisionId()) || tocMaster.getCityDivisionId() == 0L){
                return 0;
            }

            String tocMasterId = tocMaster.getMasterId();
            GetFamilyOrderPushListRqt rqt = new GetFamilyOrderPushListRqt();
            rqt.setMasterId(Long.valueOf(tobMasterId));
            rqt.setLimitCount(pushOrderTocMasterLimitCount);
            rqt.setProvinceNextId(tocMaster.getCityDivisionId());
            List<BatchOrderPushResp> orderPushList =  orderPushListApi.getFamilyOrderPushList(rqt);
            if(CollectionUtils.isNotEmpty(orderPushList)){

                if(!apolloConfigUtils.checkPushOrderToMasterSwitch()){

                    orderPushList.forEach(resp -> {
                        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                        orderMatchMasterRqt.setMasterOrderId(resp.getOrderId());
                        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                        OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);

                        if(checkPushCondition(orderDetailData,tocMasterId)){
                            JSONObject commonFeature = new JSONObject();
                            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
                            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                            //长尾单,不更改推送参数:IS_NEARBY_PUSH
                            commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH, false);

                            Set<String> masterSet = Collections.singleton(String.valueOf(tocMasterId));

                            commonFeature.put(FieldConstant.ORDER_LNG_LAT, orderDetailData.getOrderLngLat());
                            orderDetailData.getPushExtraData().setMasterSourceType(MasterSourceType.TOC.code);
                            commonFeature.put(FieldConstant.MATCH_SCENE_CODE, MatchSceneCode.TOC_MASTER_AT_TOB_APP_OFFER_LIMIT.getCode());


                            pushControllerFacade.directPush(orderDetailData,orderDetailData.getOrderVersion(),masterSet,commonFeature);
                        }


                    });



                }else{
                    List<OrderDetailData> orderDetailDataList = new ArrayList<>();

                    orderPushList.forEach(resp -> {
                        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                        orderMatchMasterRqt.setMasterOrderId(resp.getOrderId());
                        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                        OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
                        orderDetailDataList.add(orderDetailData);
                    });


                    JSONObject commonFeature = new JSONObject();
                    commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                    commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                    commonFeature.put(FieldConstant.MATCH_SCENE_CODE, MatchSceneCode.TOC_MASTER_AT_TOB_APP_OFFER_LIMIT.getCode());


                    pushControllerFacade.pushOrderToMaster(orderDetailDataList.get(0).getOrderVersion(),orderDetailDataList,tocMasterId,commonFeature,0,MasterSourceType.TOC.code);

                }




            }
        }

        return 1;
    }

    private boolean checkPushCondition(OrderDetailData orderDetailData,String masterId){
        return checkAgreementMasterCondition(orderDetailData,masterId) && checkAgentCondition(orderDetailData,masterId);
    }

    private boolean checkAgreementMasterCondition(OrderDetailData orderDetailData,String masterId){

        if(Objects.isNull(orderDetailData.getFourthDivisionId()) || orderDetailData.getFourthDivisionId() == 0L){
            return false;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId", String.valueOf(masterId)));

        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));
        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        List<String> tagNameList = new ArrayList<>();
        tagNameList.add("similar_direct_operate");
        boolQueryBuilder.must(QueryBuilders.termsQuery("tagName",tagNameList));


        log.info("boolQueryBuilder:" + boolQueryBuilder.toString());
        EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(1, 5), null);
        if (Objects.nonNull(esResponse) &&  CollectionUtils.isNotEmpty(esResponse.getDataList())) {

            orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getFourthDivisionId())));

            esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(1, 5), null);
            if (Objects.isNull(esResponse) ||  CollectionUtils.isEmpty(esResponse.getDataList())) {
                return false;
            }
        }

        return true;

    }

    @Resource
    private AgentMasterEsRepository agentMasterEsRepository;

    @Resource
    private AgentInfoEsRepository agentInfoEsRepository;


    private boolean checkAgentCondition(OrderDetailData orderDetailData,String masterId){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId", masterId));
        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationStatus", 0));

        log.info("match agentMaster request:" + boolQueryBuilder.toString());
        EsResponse<AgentMasterIndex> esResponse = agentMasterEsRepository.search(boolQueryBuilder);

        if (Objects.nonNull(esResponse) &&  CollectionUtils.isNotEmpty(esResponse.getDataList())) {

            List<AgentMasterIndex> agentMasterList = esResponse.getDataList();

            Set<Long> agentIds = agentMasterList.stream().map(AgentMasterIndex::getAgentId).collect(Collectors.toSet());

            BoolQueryBuilder agentQueryBuilder = new BoolQueryBuilder();
            agentQueryBuilder.must(QueryBuilders.termsQuery("agentId",agentIds));

            agentQueryBuilder.must(QueryBuilders.termQuery("divisionId", String.valueOf(orderDetailData.getThirdDivisionId())));

            agentQueryBuilder.must(this.stringMatchQueryBuilderWithMinim("serveIds",
                    orderDetailData.getLv3ServeIds(), Operator.OR,1));
            agentQueryBuilder.must(QueryBuilders.termQuery("useStatus", 0));


            EsResponse<AgentInfoIndex> agentEsResponse = agentInfoEsRepository.search(agentQueryBuilder);
            // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
            if(Objects.nonNull(agentEsResponse) && CollectionUtils.isNotEmpty(agentEsResponse.getDataList())){
                return true;
            }
            return false;
        }

        return true;






    }


    public MatchQueryBuilder stringMatchQueryBuilderWithMinim(String fieldName, String value, Operator operator, Integer minimumShouldMatch) {
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        //设置要匹配的列。
        matchQueryBuilder.operator(operator);
        matchQueryBuilder.minimumShouldMatch(String.valueOf(minimumShouldMatch));
        return matchQueryBuilder;
    }


    private void setAgentQuery(BoolQueryBuilder boolQueryBuilder,MasterMatchCondition masterCondition) {

    }



    @Override
    public Integer masterOfferPrice(MasterOfferPriceMessage message){

        Long orderId = message.getMasterOrderId();
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        if(Objects.isNull(orderGrabByIdResp) || Objects.isNull(orderGrabByIdResp.getOrderGrab()) || Objects.isNull(orderGrabByIdResp.getOrderBase())){
            return 1;
        }


        if(message.getIsLastPush()) {

            if (CollectionUtils.isNotEmpty(message.getFullOfferPushScenes()) && message.getFullOfferPushScenes().contains("cooperative_business")) {
                //报价人数已满，触发推送合作经营师傅
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(message.getMasterOrderId());
                orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.COOPERATION_BUSINESS.getCode());
                pushQueueService.sendDelayPushMessage(500L, JSON.toJSONString(orderMatchMasterRqt));
            }


            if (CollectionUtils.isNotEmpty(message.getFullOfferPushScenes()) && message.getFullOfferPushScenes().contains(MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode())) {
                //报价人数已满
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(message.getMasterOrderId());
                orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode());
                pushQueueService.sendDelayPushMessage(500L, JSON.toJSONString(orderMatchMasterRqt));


            }


            if (CollectionUtils.isNotEmpty(message.getFullOfferPushScenes()) && message.getFullOfferPushScenes().contains("all_time_order")) {
                //报价人数已满，触发推送全时师傅
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(message.getMasterOrderId());
                orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.FULL_TIME_MASTER.getCode());
                pushQueueService.sendDelayPushMessage(500L, JSON.toJSONString(orderMatchMasterRqt));


            }



        }
            //师傅自动接单
        orderDistributeServiceImpl.orderDistribute(message.getMasterOrderId());

        matchGoldMedalMaster(message.getOfferPrice(),orderGrabByIdResp.getOrderBase(),orderGrabByIdResp.getOrderGrab());

        return 1;
    }


    private void matchGoldMedalMaster(BigDecimal offerPrice,OrderBase orderBase, OrderGrab orderGrab){

        if(offerPrice.compareTo(pushGoldMedalMasterOfferPrice) < 0){
            return ;
        }

        if(!(AppointType.OPEN.value.equals(orderGrab.getAppointType()) && OrderFrom.SITE.valueEn.equals(orderBase.getOrderFrom()) && AccountType.USER.code.equals(orderBase.getAccountType()) &&
                "5".equals(orderBase.getServeLevel1Ids()))){
            return ;
        }

        //企业公开报价订单，家具维修单
        GetOfferedPriceListByGlobalOrderIdRqt rqt = new GetOfferedPriceListByGlobalOrderIdRqt();
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        List<GetOfferedPriceListByGlobalOrderIdResp> respList = offerModuleResourceApi.getOfferedPriceListByGlobalOrderId(rqt);

        if(CollectionUtils.isEmpty(respList)){
            return ;
        }

        long count  = respList.stream()
                .filter(n -> n.getOfferPrice().compareTo(pushGoldMedalMasterOfferPrice) >= 0)
                .count();

        if(count == pushGoldMedalMasterOfferPriceNum){
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderGrab.getOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.GOLD_MEDAL_MASTER.code);
            orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.MASTER_OFFER_PRICE.getCode());
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.GOLD_MEDAL_MASTER);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }



    }


    @Override
    public Integer masterModifyOfferPrice(MasterModifyOfferPriceMessage message){


        Long orderId = message.getMasterOrderId();
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        if(Objects.isNull(orderGrabByIdResp) || Objects.isNull(orderGrabByIdResp.getOrderGrab()) || Objects.isNull(orderGrabByIdResp.getOrderBase())){
            return 0;
        }

        matchGoldMedalMaster(message.getOfferPrice(), orderGrabByIdResp.getOrderBase(), orderGrabByIdResp.getOrderGrab());

        return 1;
    }



    @Override
    public Integer tocMasterAtTobAppOfferLimit(TocMasterAtTobAppOfferLimitMessage message){
        this.pushOrderToTocMasterByTobMasterId(message.getMasterId());
        return 0;
    }


}
