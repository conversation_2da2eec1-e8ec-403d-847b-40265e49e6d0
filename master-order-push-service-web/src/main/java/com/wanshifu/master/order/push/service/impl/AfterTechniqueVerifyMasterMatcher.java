package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.AfterTechniqueVerifyMasterMatchLog;

import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.AfterTechniqueVerifyMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.training.api.skillTask.SkillTaskServiceApi;
import com.wanshifu.master.training.domain.api.response.skillTask.GetOrderTakingConfigResp;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 技能验证后师傅推单
 * @date 2025/7/1 11:57
 */
@Slf4j
@Component("after_technique_verify_master")
public class AfterTechniqueVerifyMasterMatcher extends AbstractOrderMasterMatcher{

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private HBaseClient hBaseClient;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private AfterTechniqueVerifyMasterMatchLogRepository afterTechniqueVerifyMasterMatchLogRepository;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private SkillTaskServiceApi skillTaskServiceApi;


    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {
        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            insertMatchLog(orderDetailData,"不推单的城市", orderDetailData.getOrderVersion());
            return false;
        }

        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(StringUtils.isNotBlank(pushMode) && (!PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.code.equals(pushMode))){
            return Boolean.FALSE;
        }

        return orderDetailData.getBusinessLineId() == 1 && AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType());
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {
        Long orderId = orderDetailData.getMasterOrderId();
        log.info("afterTechniqueVerifyMasterList match orderId:{}", orderId.toString());

        List<AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoList = matchAfterTechniqueVerifyMaster(orderDetailData, masterCondition);
        if (CollectionUtil.isEmpty(afterTechniqueVerifyMasterDtoList)) {
            return null;
        }

        MatchMasterResult masterResult = new MatchMasterResult();
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData("after_techniqueVerify_master_list",afterTechniqueVerifyMasterDtoList);

        Set<String> masterSet = afterTechniqueVerifyMasterDtoList.stream().map(AfterTechniqueVerifyMasterDto::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        masterResult.setMasterIdSet(masterSet);
        return masterResult;
    }


    private List<AfterTechniqueVerifyMasterDto> matchAfterTechniqueVerifyMaster(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        if (masterMatchCondition.getDivisionMatchLevel() != 4) {
            //订单无四级地址，不匹配
            String matchFailReason = "订单无四级地址，不匹配技能验证后师傅！";
            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = buildQuery(orderDetailData, masterMatchCondition);

        log.info("afterTechniqueVerifyMasterList match esQuery:" + boolQueryBuilder.toString());

        List<MasterBaseSearch> afterTechniqueVerifyMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                afterTechniqueVerifyMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("afterTechniqueVerifyMasterList match searchES result:" + JSON.toJSONString(afterTechniqueVerifyMasterList));

        if(CollectionUtils.isEmpty(afterTechniqueVerifyMasterList)){
            String matchFailReason = "未匹配到满足条件的技能验证后师傅！";
            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }

        return filterAfterTechniqueVerifyMaster(afterTechniqueVerifyMasterList, orderDetailData);
    }

    private List<AfterTechniqueVerifyMasterDto> filterAfterTechniqueVerifyMaster(List<MasterBaseSearch> afterTechniqueVerifyMasterList, OrderDetailData orderDetailData) {
        Long orderId = orderDetailData.getMasterOrderId();

        List<AfterTechniqueVerifyMasterMatchLog> afterTechniqueVerifyMasterMatchLogList = new ArrayList<>();
        Date now = new Date();
        afterTechniqueVerifyMasterList.forEach(afterTechniqueVerifyMaster -> {
            AfterTechniqueVerifyMasterMatchLog afterTechniqueVerifyMasterMatchLog = new AfterTechniqueVerifyMasterMatchLog();
            afterTechniqueVerifyMasterMatchLog.setMasterId(Long.valueOf(afterTechniqueVerifyMaster.getMasterId()));
            afterTechniqueVerifyMasterMatchLog.setOrderVersion(orderDetailData.getOrderVersion());
            afterTechniqueVerifyMasterMatchLog.setOrderId(orderId);
            afterTechniqueVerifyMasterMatchLog.setOrderNo(orderDetailData.getOrderNo());
            afterTechniqueVerifyMasterMatchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            afterTechniqueVerifyMasterMatchLog.setIsMatchSuccess(1);
            afterTechniqueVerifyMasterMatchLog.setMatchFailReason("");
            afterTechniqueVerifyMasterMatchLog.setCreateTime(now);
            afterTechniqueVerifyMasterMatchLog.setUpdateTime(now);
            afterTechniqueVerifyMasterMatchLogList.add(afterTechniqueVerifyMasterMatchLog);
        });


        Set<String> masterIdSet = afterTechniqueVerifyMasterList.stream().map(MasterBaseSearch::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        log.info("afterTechniqueVerifyMasterList after matchEs result:{}}", JSON.toJSONString(masterIdSet));
        //移除已推单师傅
        filterPushedMaster(masterIdSet, orderDetailData.getGlobalOrderId(), afterTechniqueVerifyMasterMatchLogList);
        log.info("afterTechniqueVerifyMasterList after filterPushedMaster result:{}}", JSON.toJSONString(masterIdSet));

        if (CollectionUtils.isEmpty(masterIdSet)) {
            afterTechniqueVerifyMasterMatchLogRepository.insertList(afterTechniqueVerifyMasterMatchLogList);
            return null;
        }
        final String orderPushExcludeMasterIds =
                StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
        outerMasterFilter(masterIdSet, orderPushExcludeMasterIds, afterTechniqueVerifyMasterMatchLogList);
        log.info("afterTechniqueVerifyMasterList after orderPushExcludeMasterIds:{}:{}:{}", orderId, masterIdSet, orderPushExcludeMasterIds);

        if(CollectionUtils.isEmpty(masterIdSet)){
            afterTechniqueVerifyMasterMatchLogRepository.insertList(afterTechniqueVerifyMasterMatchLogList);
            return null;
        }

        afterTechniqueVerifyMasterList = afterTechniqueVerifyMasterList.stream().filter(afterTechniqueVerifyMasterBase -> masterIdSet.contains(String.valueOf(afterTechniqueVerifyMasterBase.getMasterId()))).collect(Collectors.toList());

        //每日抢单上限过滤
        afterTechniqueVerifyMasterList = filterByDailyGrabOrderCnt(afterTechniqueVerifyMasterList, afterTechniqueVerifyMasterMatchLogList);
        if(CollectionUtils.isEmpty(afterTechniqueVerifyMasterList)){
            afterTechniqueVerifyMasterMatchLogRepository.insertList(afterTechniqueVerifyMasterMatchLogList);
            return null;
        }
        List<AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoList = afterTechniqueVerifyMasterList.stream()
                .map(afterTechniqueVerifyMaster -> {
                    AfterTechniqueVerifyMasterDto afterTechniqueVerifyMasterDto = new AfterTechniqueVerifyMasterDto();
                    BeanUtil.copyProperties(afterTechniqueVerifyMaster, afterTechniqueVerifyMasterDto);
                    return afterTechniqueVerifyMasterDto;
                })
                .collect(Collectors.toList());


        Set<String> masterSet = afterTechniqueVerifyMasterDtoList.stream().map(AfterTechniqueVerifyMasterDto::getMasterId).collect(Collectors.toSet());

        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData, masterSet);

        DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();

        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                .matchNewMasterDistributor(orderDetailData, orderFeatureContext, "after_verify_new_master");


        if (!orderDistributor.isMatched()) {
            String matchFailReason = "未匹配到技能验证后师傅派单调度策略！";
            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }

        //获取特征
        featureRepository.getMasterFeatures(pushFeature, masterSet, orderDistributor.getMasterFeatureSet());

        try {
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();

            masterFeatures.keySet().forEach(masterId -> {
                DefaultContext<String,Object> masterFeatureContext = masterFeatures.get(masterId);
                if(Objects.nonNull(masterFeatureContext)){
                    masterFeatureContext.keySet().forEach(featureCode -> {
                        Object masterFeatureValue = masterFeatureContext.get(featureCode);
                        if (Objects.nonNull(masterFeatureValue) && (masterFeatureValue instanceof List)) {
                            if(masterFeatureContext.get(featureCode + ":calculateValue") != null){
                                masterFeatureContext.put(featureCode, masterFeatureContext.get(featureCode + ":calculateValue"));
                                log.info("featureCode calculateValue replace: " + featureCode);
                            }
                        }
                    });
                }

            });

            log.info("afterTechniqueVerifyMasterList masterFeatures:" + JSON.toJSONString(masterFeatures));


            //过滤排序
            final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();

            List<ScorerMaster> scorerMasterList = orderDistributor
                    .rank(masterSet, orderFeatureContext, masterFeatures, rankDetail);

            log.info(String.format("afterTechniqueVerifyMasterList rankDetail:%s", JSON.toJSONString(rankDetail)));


            Map<Long,String> filterReasonMap = new HashMap<>();

            try{
                if(StringUtils.isNotBlank(rankDetail.getDetailInfo())){
                    Map<String, Object> filterDetailsMap = (Map) JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                    for(String key : filterDetailsMap.keySet()){
                        JSONArray jsonArray = (JSONArray)filterDetailsMap.get(key);
                        jsonArray.forEach(master -> {
                            filterReasonMap.put(Long.valueOf(String.valueOf(master)), key);
                        });
                    }
                }

            }catch(Exception e){
                log.error("rankDetail error",e);
            }

            log.info("after rank scorerMasterList:" + JSON.toJSONString(scorerMasterList));

            if(CollectionUtils.isEmpty(scorerMasterList)){
                afterTechniqueVerifyMasterMatchLogList.forEach(log -> {
                    if(masterSet.contains(String.valueOf(log.getMasterId()))){
                        log.setIsFilter(1);
                        log.setFilterReason(filterReasonMap.getOrDefault(log.getMasterId(),""));
                    }
                });
                afterTechniqueVerifyMasterMatchLogRepository.insertList(afterTechniqueVerifyMasterMatchLogList);
                return null;
            }

            List<String> masterList = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList());

            afterTechniqueVerifyMasterMatchLogList.forEach(log -> {
                if (log.getIsMatchSuccess() == 1) {
                    if (masterList.contains(String.valueOf(log.getMasterId()))) {
                        log.setIsFilter(0);
                    } else {
                        log.setIsFilter(1);
                        log.setFilterReason(filterReasonMap.getOrDefault(log.getMasterId(),""));
                    }
                }
            });



            //二次筛选、评分
            afterTechniqueVerifyMasterDtoList = afterTechniqueVerifyMasterDtoList.stream().filter(afterTechniqueVerifyMasterDto -> masterList.contains(afterTechniqueVerifyMasterDto.getMasterId())).collect(Collectors.toList());

            //根据分配规则筛选
            afterTechniqueVerifyMasterDtoList = distributeAfterTechniqueVerifyMaster(scorerMasterList, orderDistributor, afterTechniqueVerifyMasterDtoList);

            List<String> afterDistributeMasterList = CollectionUtil.isEmpty(afterTechniqueVerifyMasterDtoList) ? new ArrayList<>() : afterTechniqueVerifyMasterDtoList.stream().map(AfterTechniqueVerifyMasterDto::getMasterId).collect(Collectors.toList());

            afterTechniqueVerifyMasterMatchLogList.forEach(log -> {
                if (Objects.nonNull(log.getIsFilter()) && log.getIsFilter() == 0) {
                    if (afterDistributeMasterList.contains(String.valueOf(log.getMasterId()))) {
                        log.setIsDistribute(1);
                        log.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                    } else {
                        log.setIsDistribute(0);
                    }
                }
            });

            afterTechniqueVerifyMasterMatchLogRepository.insertList(afterTechniqueVerifyMasterMatchLogList);

            log.info("afterTechniqueVerifyMasterList distributeAfterTechniqueVerifyMaster:" + JSON.toJSONString(afterTechniqueVerifyMasterDtoList));

        } catch (Exception e) {

            log.error("afterTechniqueVerifyMasterList distribute error", e);
        }

        return afterTechniqueVerifyMasterDtoList;
    }

    /**
     * 技能验证后师傅分配
     *
     * @param scorerMasterList
     * @param orderDistributor
     * @param afterTechniqueVerifyMasterDtoList
     * @return
     */
    public List<AfterTechniqueVerifyMasterDto> distributeAfterTechniqueVerifyMaster(List<ScorerMaster> scorerMasterList, OrderDistributor orderDistributor,
                                                                                  List<AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoList) {

        String distributeRule = orderDistributor.getDistributeRule();

        if (OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)) {
            Collections.sort(scorerMasterList);
        } else if (OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)) {
            Collections.sort(scorerMasterList);
            scorerMasterList = scorerMasterList.size() <= 50 ? scorerMasterList : scorerMasterList.subList(0, 50);
            Collections.shuffle(scorerMasterList);
        }


        if (CollectionUtils.isNotEmpty(scorerMasterList)) {
            List<AfterTechniqueVerifyMasterDto> finalAfterTechniqueVerifyMasterDtoList = new ArrayList<>();
            Map<String, AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoMap = afterTechniqueVerifyMasterDtoList.stream().collect(Collectors.toMap(AfterTechniqueVerifyMasterDto::getMasterId, Function.identity()));
            for (int i = 0; i < scorerMasterList.size(); i++) {
                AfterTechniqueVerifyMasterDto afterTechniqueVerifyMasterDto = afterTechniqueVerifyMasterDtoMap.get(scorerMasterList.get(i).getMasterId());

                afterTechniqueVerifyMasterDto.setGrabSort(i + 1);
                afterTechniqueVerifyMasterDto.setScore(scorerMasterList.get(i).getScore());
                finalAfterTechniqueVerifyMasterDtoList.add(afterTechniqueVerifyMasterDto);
            }
            return finalAfterTechniqueVerifyMasterDtoList;
        }
        return null;
    }

    /**
     * 技能验证后师傅每日抢单上限限制过滤
     *
     * @param afterTechniqueVerifyMasterList
     * @param afterTechniqueVerifyMasterMatchLogList
     * @return
     */
    private List<MasterBaseSearch> filterByDailyGrabOrderCnt(List<MasterBaseSearch> afterTechniqueVerifyMasterList,
                                                                            List<AfterTechniqueVerifyMasterMatchLog> afterTechniqueVerifyMasterMatchLogList) {

        if (CollectionUtils.isEmpty(afterTechniqueVerifyMasterList)) {
            return afterTechniqueVerifyMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = afterTechniqueVerifyMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("after_technique_verify_grab_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "master_daily_data_cnt");
        Map<String, Integer> afterTechniqueVerifyGrabCntMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");

                if (jsonObject.containsKey("after_technique_verify_grab_cnt")) {
                    String afterTechniqueVerifyGrabCnt = (String) jsonObject.get("after_technique_verify_grab_cnt");
                    if (org.apache.commons.lang.StringUtils.isNotBlank(afterTechniqueVerifyGrabCnt)) {
                        afterTechniqueVerifyGrabCntMap.put(masterId, Integer.valueOf(afterTechniqueVerifyGrabCnt));
                    }
                }
            }
        }

        //用户抢单指标上线过滤
        GetOrderTakingConfigResp orderTakingConfigResp = null;
        try {
            orderTakingConfigResp = skillTaskServiceApi.getOrderTakingConfig();
        } catch (Exception e) {
            log.error("afterTechniqueVerifyMasterList filterByDailyGrabOrderCnt getDailyOrderLimit error", e);
            return afterTechniqueVerifyMasterList;
        }
        Integer dailyOrderLimit = 0;
        if (Objects.nonNull(orderTakingConfigResp) && Objects.nonNull(orderTakingConfigResp.getDailyOrderLimit())) {
            dailyOrderLimit = orderTakingConfigResp.getDailyOrderLimit();
        }
        Map<Long, String> grabFilterMap = Maps.newHashMap();
        Integer finalDailyOrderLimit = dailyOrderLimit;
        afterTechniqueVerifyMasterList = afterTechniqueVerifyMasterList.stream().filter(afterTechniqueVerifyMaster -> {
            Integer hasGrabCnt = afterTechniqueVerifyGrabCntMap.getOrDefault(afterTechniqueVerifyMaster.getMasterId(), 0);

            if (finalDailyOrderLimit == 0) {
                return true;
            }
            if (hasGrabCnt < finalDailyOrderLimit) {
                return true;
            }
            grabFilterMap.put(Long.valueOf(afterTechniqueVerifyMaster.getMasterId()), "该师傅抢单限制为".concat(finalDailyOrderLimit.toString()).concat("单，已抢单了").concat(hasGrabCnt.toString()).concat("单。"));
            return false;
        }).collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(grabFilterMap)) {
            afterTechniqueVerifyMasterMatchLogList.forEach(afterTechniqueVerifyMasterMatchLog -> {
                if (grabFilterMap.containsKey(afterTechniqueVerifyMasterMatchLog.getMasterId())) {
                    afterTechniqueVerifyMasterMatchLog.setIsMatchSuccess(0);
                    afterTechniqueVerifyMasterMatchLog.setMatchFailReason(grabFilterMap.get(afterTechniqueVerifyMasterMatchLog.getMasterId()));
                }
            });
        }

        return afterTechniqueVerifyMasterList;
    }


    public void insertMatchLog(OrderDetailData orderDetailData, String matchFailReason, String orderVersion) {
        AfterTechniqueVerifyMasterMatchLog afterTechniqueVerifyMasterMatchLog = new AfterTechniqueVerifyMasterMatchLog();
        afterTechniqueVerifyMasterMatchLog.setOrderId(orderDetailData.getMasterOrderId());
        afterTechniqueVerifyMasterMatchLog.setMasterId(0L);
        afterTechniqueVerifyMasterMatchLog.setOrderNo(orderDetailData.getOrderNo());
        afterTechniqueVerifyMasterMatchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        afterTechniqueVerifyMasterMatchLog.setIsMatchSuccess(0);
        afterTechniqueVerifyMasterMatchLog.setMatchFailReason(matchFailReason);
        afterTechniqueVerifyMasterMatchLog.setOrderVersion(orderVersion);
        afterTechniqueVerifyMasterMatchLog.setCreateTime(new Date());
        afterTechniqueVerifyMasterMatchLog.setUpdateTime(new Date());
        this.afterTechniqueVerifyMasterMatchLogRepository.insertSelective(afterTechniqueVerifyMasterMatchLog);
    }

    /**
     * 过滤已推送的技能验证后师傅
     *
     * @param masterIdSet
     * @param getGlobalOrderTraceId
     */
    public void filterPushedMaster(Set<String> masterIdSet, Long getGlobalOrderTraceId, List<AfterTechniqueVerifyMasterMatchLog> afterTechniqueVerifyMasterMatchLogList) {
        String masterIdStr = hBaseClient.querySingle("order_push", String.valueOf(getGlobalOrderTraceId), PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.getCode());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)) {
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            afterTechniqueVerifyMasterMatchLogList.forEach(afterTechniqueVerifyMasterMatchLog -> {
                if (pushedMasterIdSet.contains(String.valueOf(afterTechniqueVerifyMasterMatchLog.getMasterId()))) {
                    afterTechniqueVerifyMasterMatchLog.setIsMatchSuccess(0);
                    afterTechniqueVerifyMasterMatchLog.setMatchFailReason("已推送过的技能验证后师傅");
                }
            });

            masterIdSet.removeAll(pushedMasterIdSet);
        }
    }

    /**
     * 过滤外部设置的需排除师傅
     *
     * @param result
     * @param masterIds
     */
    private void outerMasterFilter(Set<String> result, String masterIds, List<AfterTechniqueVerifyMasterMatchLog> afterTechniqueVerifyMasterMatchLogList) {
        if (masterIds != null) {
            List<String> outerMasterList = Arrays.asList(masterIds.split(","));
            if (CollectionUtils.isNotEmpty(outerMasterList)) {
                afterTechniqueVerifyMasterMatchLogList.forEach(afterTechniqueVerifyMasterMatchLog -> {
                    if (outerMasterList.contains(String.valueOf(afterTechniqueVerifyMasterMatchLog.getMasterId()))) {
                        afterTechniqueVerifyMasterMatchLog.setIsMatchSuccess(0);
                        afterTechniqueVerifyMasterMatchLog.setMatchFailReason("外部设置的需排除的师傅");
                    }
                });
                outerMasterList.forEach(result::remove);
            }
        }
    }

    private BoolQueryBuilder buildQuery(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //服务区域筛选
        boolQueryBuilder.filter(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(masterMatchCondition.getFourthDivisionId())));

        boolQueryBuilder.filter(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterMatchCondition.freezingRecoverTime()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.termQuery("masterSourceType", MasterSourceType.TOB.code));

        //查询技能验证后师傅且开启派单
        boolQueryBuilder.filter(QueryBuilders.termQuery("afterTechniqueVerifyIsDistributeOrder", 1));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("afterTechniqueVerifyCategoryIds", Lists.newArrayList(orderDetailData.getOrderCategoryId())));

        //技能筛选
        Set<String> techniqueIdSet = Arrays.stream(orderDetailData.getOrderTechniques().split("\\|")).collect(Collectors.toSet());
        boolQueryBuilder.filter(QueryBuilders.termsQuery("masterTechniqueIds", techniqueIdSet));

        return boolQueryBuilder;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {

    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        try {
            if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
                return false;
            }
            String orderVersion = orderDetailData.getOrderVersion();
            long timeStamp = Long.parseLong(orderVersion);

            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.getCode());

            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.getCode());
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
            commonFeature.put("after_techniqueVerify_master_list", matchMasterResult.getExtraData().get("after_techniqueVerify_master_list"));
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            pushControllerFacade.afterTechniqueVerifyMasterPush(orderDetailData, orderDetailData.getOrderVersion(), timeMark, matchMasterResult.getMasterIdSet(), commonFeature);

            return true;
        } catch (Exception e) {
            log.error(String.format("执行技能验证后派单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;
    }
}
