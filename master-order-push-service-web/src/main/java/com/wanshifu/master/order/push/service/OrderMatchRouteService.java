package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.dto.ExclusiveOrderLabel;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;

public interface OrderMatchRouteService {

    Integer create(CreateOrderMatchRouteRqt rqt);


    Integer update(UpdateOrderMatchRouteRqt rqt);


    OrderMatchRoute detail(OrderMatchRouteDetailRqt rqt);

    SimplePageInfo<OrderMatchRoute> list(OrderMatchRouteListRqt rqt);


    void orderMatchRoute(String orderPushFlag,Long orderId,Integer businessLineId,Long categoryId,Integer appointType);

    ExclusiveOrderLabel getExclusiveOrderLabel(Long masterId, String pushMode, String pushModeType, Long globalOrderId, Long recruitId, boolean hasPrice);


    }
