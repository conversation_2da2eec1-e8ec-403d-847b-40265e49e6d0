package com.wanshifu.master.order.push.repository;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.OrderScoreItem;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27 18:02
 */
@Repository
public class OrderScoreItemRepository extends BaseRepository<OrderScoreItem> {

    public List<OrderScoreItem> selectByItemCodes(List<String> itemCodes) {
        if (CollectionUtils.isEmpty(itemCodes)) {
            return Collections.emptyList();
        }
        Example example = new Example(OrderScoreItem.class);
        example.createCriteria().andIn("itemCode", itemCodes)
                .andEqualTo("isDelete", 0);
        return this.selectByExample(example);
    }

    public List<OrderScoreItem> selectByItemName(String itemName) {
        Example example = new Example(OrderScoreItem.class);
        if (StringUtils.isNotBlank(itemName)) {
            example.createCriteria().andLike("itemName", StrUtil.format("%{}%", itemName));
        }
        return this.selectByExample(example);
    }
}
