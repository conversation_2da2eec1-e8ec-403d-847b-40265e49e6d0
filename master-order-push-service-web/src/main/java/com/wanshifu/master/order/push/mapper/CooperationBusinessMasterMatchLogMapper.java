package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 19:52
 */
public interface CooperationBusinessMasterMatchLogMapper extends IBaseCommMapper<CooperationBusinessMasterMatchLog> {


    List<CooperationBusinessMasterMatchLog> selectList(@Param("orderNo") String orderNo, @Param("masterId") Long masterId,
                                                       @Param("orderCreateTimeStart") Date orderCreateTimeStart,
                                                       @Param("orderCreateTimeEnd") Date orderCreateTimeEnd);

}
