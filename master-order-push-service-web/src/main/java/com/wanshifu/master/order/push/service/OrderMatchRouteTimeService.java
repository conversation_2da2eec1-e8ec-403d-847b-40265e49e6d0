package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteTimeRqt;

public interface OrderMatchRouteTimeService {

    Integer create(CreateOrderMatchRouteTimeRqt rqt);


    Integer update(UpdateOrderMatchRouteTimeRqt rqt);


    OrderMatchRouteTime detail(OrderMatchRouteTimeDetailRqt rqt);

    SimplePageInfo<OrderMatchRouteTime> list(OrderMatchRouteTimeListRqt rqt);



}
