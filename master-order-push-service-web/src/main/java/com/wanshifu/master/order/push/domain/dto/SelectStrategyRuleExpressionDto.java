package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 15:04
 */
@Data
@AllArgsConstructor
public class SelectStrategyRuleExpressionDto {


    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;


    /**
     * 召回规则表达式
     */
    private String filterRuleExpression;

    /**
     * 召回规则表达式参数
     */
    private String filterRuleParams;

}
