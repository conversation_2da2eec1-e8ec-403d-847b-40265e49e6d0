//package com.wanshifu.master.order.push.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.ql.util.express.DefaultContext;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.constant.ScorerItem;
//import com.wanshifu.master.order.push.domain.dto.ScorerMaster;
//import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
//import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
//import com.wanshifu.master.order.push.service.DistributeRankDetail;
//import com.wanshifu.util.LoCollectionsUtil;
//import com.wanshifu.util.QlExpressStatic;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @description
// * @date 2025/3/5 16:57
// */
//@Slf4j
//public class DistributeOrderDistributor {
//
//    /**
//     * 特征
//     */
//    private Set<String> admittanceOrderFeatureSet=new HashSet<>();
//    private Set<String> orderFeatureSet=new HashSet<>();
//    private Set<String> masterFeatureSet=new HashSet<>();
//    private boolean matched=false;
//
//    public boolean isMatched() {
//        return matched;
//    }
//
//    public void setMatched(boolean matched) {
//        this.matched = matched;
//    }
//
//    public Set<String> getAdmittanceOrderFeatureSet() {
//        return admittanceOrderFeatureSet;
//    }
//
//    public void addAdmittanceOrderFeatureCode(String featureCode){
//        admittanceOrderFeatureSet.add(featureCode);
//    }
//
//    public void addOrderFeatureCode(String featureCode){
//        orderFeatureSet.add(featureCode);
//    }
//
//    public void addMasterFeatureCode(String featureCode){
//        masterFeatureSet.add(featureCode);
//    }
//
//    public Set<String> getOrderFeatureSet() {
//        return orderFeatureSet;
//    }
//
//    public Set<String> getMasterFeatureSet() {
//        return masterFeatureSet;
//    }
//
//    /**
//     * 筛选策略
//     */
//    private List<Filter> selectStrategy=new ArrayList<>();
//    /**
//     * 评分策略
//     */
//    private List<Scorer> scoringStrategy=new ArrayList<>();
//
//    private String distributeRule;
//
//    public void setSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies) {
//        orderSelectStrategies.stream().forEach(row->{
//            try {
//                final String selectStrategyExpression = row.getSelectStrategyExpression();
//                final JSONObject selectStrategyExpressionJSON = JSONObject.parseObject(selectStrategyExpression);
//                this.selectStrategy.add(Filter.FilterBuilder.aFilter()
//                        .withFilterId(String.valueOf(row.getStrategyId()))
//                        .withAppointGroupExpression(selectStrategyExpressionJSON.getString(FieldConstant.APPOINT_GROUP_EXPRESSION))
//                        .withExpression(selectStrategyExpressionJSON.getString(FieldConstant.SELECT_RULE_EXPRESSION))
//                        .build());
//                final String selectRuleParams = selectStrategyExpressionJSON.getString(FieldConstant.SELECT_RULE_PARAMS);
//                final String appointGroupParams = selectStrategyExpressionJSON.getString(FieldConstant.APPOINT_GROUP_PARAMS);
//                if (StringUtils.isNotEmpty(selectRuleParams)) {
//                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(selectRuleParams));
//                }
//                if (StringUtils.isNotEmpty(appointGroupParams)) {
//                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(appointGroupParams));
//                }
//            }catch (Exception e){
//                log.warn("setSelectStrategy exception:{},[{}]",orderSelectStrategies,e);
//            }
//        });
//    }
//
//
//    public void setSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies, DefaultContext<String,Object> orderFeatures) {
//        orderSelectStrategies.stream().forEach(row->{
//            try {
//                final String selectRuleExpression = row.getSelectRuleExpression();
//                final JSONObject selectRuleExpressionJSON = JSONObject.parseObject(selectRuleExpression);
//
//                final String appointGroupParams = selectRuleExpressionJSON.getString(FieldConstant.APPOINT_GROUP_PARAMS);
//
//                //TODO 待确认多个筛选策略，指定人群场景下如何筛选？
//                if (StringUtils.isNotEmpty(appointGroupParams)) {
//                    this.selectStrategy.add(Filter.FilterBuilder.aFilter()
//                            .withFilterId(String.valueOf(row.getStrategyId()))
//                            .withAppointGroupExpression(selectRuleExpressionJSON.getString(FieldConstant.APPOINT_GROUP_EXPRESSION))
//                            .build());
//                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(appointGroupParams));
//                }
//
//                JSONArray selectRuleList = selectRuleExpressionJSON.getJSONArray("filterRuleList");
//
//                for (int j = 0; j < selectRuleList.size(); j++) {
//                    final JSONObject selectRule = selectRuleList.getJSONObject(j);
//                    final String openConditionRuleExpression = selectRule.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                    final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
//                            openConditionRuleExpression, orderFeatures
//                    );
//                    if (expressBoolean) {
//
//                        this.selectStrategy.add(Filter.FilterBuilder.aFilter()
//                                .withFilterId(String.valueOf(row.getStrategyId()))
//                                .withExpression(selectRule.getString(FieldConstant.FILTER_RULE_EXPRESSION))
//                                .build());
//
//                        final String filterRuleParams = selectRule.getString(FieldConstant.FILTER_RULE_PARAMS);
//                        this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(filterRuleParams));
//                    }
//                }
//
//
//            }catch (Exception e){
//                log.warn("setSelectStrategy exception:{},[{}]",orderSelectStrategies,e);
//            }
//        });
//    }
//
//    public void setScoringStrategy(List<OrderScoringStrategy> orderScoringStrategies) {
//        try {
//            for (OrderScoringStrategy orderScoringStrategy : orderScoringStrategies) {
//                final String scoringStrategyExpression = orderScoringStrategy.getScoringStrategyExpression();
//                final JSONArray objects = JSONObject.parseArray(scoringStrategyExpression);
//                for (int i = 0; i < objects.size(); i++) {
//                    final JSONObject outerRow = objects.getJSONObject(i);
//                    final String outerOpenConditionRuleExpression =outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                    final String outerOpenConditionRuleParams = outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
//                    this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(outerOpenConditionRuleParams));
//                    final JSONArray scoreRuleList = outerRow.getJSONArray(FieldConstant.SCORE_RULE_LIST);
//                    List<ScorerItem> scorerItemList=new ArrayList<>();
//                    for (int j = 0; j < scoreRuleList.size(); j++) {
//                        final JSONObject innerRow = scoreRuleList.getJSONObject(j);
//                        final String openConditionRuleExpression = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                        final String openConditionRuleParams = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
//                        final String ruleName = innerRow.getString(FieldConstant.RULE_NAME);
//                        this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(openConditionRuleParams));
//                        final String scoreRuleParams = innerRow.getString(FieldConstant.SCORE_RULE_PARAMS);
//                        this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(scoreRuleParams));
//                        final String scoreRuleExpression=innerRow.getString(FieldConstant.SCORE_RULE_EXPRESSION);
//                        scorerItemList.add(
//                                ScorerItem.ScorerItemBuilder.aScorerItem()
//                                        .withRuleName(ruleName)
//                                        .withAdmittance(openConditionRuleExpression)
//                                        .withScoreRuleExpression(scoreRuleExpression)
//                                        .build()
//                        );
//                    }
//                    this.scoringStrategy.add(
//                            Scorer.ScorerBuilder.aScorer()
//                                    .withAdmittance(outerOpenConditionRuleExpression)
//                                    .withScorerId(String.valueOf(orderScoringStrategy.getStrategyId()))
//                                    .withScorerItemList(scorerItemList)
//                                    .build()
//                    );
//                }
//            }
//        }catch (Exception e){
//            log.warn("OrderDistributor withScoringStrategy exception:[{}],[{}]",orderScoringStrategies,e);
//        }
//    }
//
//    /**
//     * 处理
//     * @return
//     */
//    public List<ScorerMaster> rank(Set<String> masterSet, DefaultContext<String, Object> orderFeatures,
//                                   DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
//                                   DistributeRankDetail rankDetail){
//        List<ScorerMaster> scorerMasterList=new ArrayList<>();
//
//        for (String masterId : masterSet) {
//            final DefaultContext<String, Object> masterContext = masterFeatures.get(masterId);
//            /**
//             * 过滤
//             */
//            if (filter(masterId,masterSet,masterContext,rankDetail)) {
//                continue;
//            }
//            /**
//             * 评分
//             */
//            scorer(masterId,scorerMasterList,orderFeatures,masterContext,rankDetail);
//        }
//        return scorerMasterList;
//    }
//
////    /**
////     * 过滤
////     * @param masterContext
////     */
////    private boolean filter(String masterId,Set<String> masterSet,DefaultContext<String, Object> masterContext,
////                           RankDetail rankDetail){
////        boolean result=false;
////        if (selectStrategy==null) {
////            return false;
////        }
////        for (Filter selectExpression : selectStrategy) {
////            result=QlExpressStatic.QlExpressBoolean(selectExpression.getExpression(),masterContext);
////            if (result) {
//////                masterSet.remove(masterId);
////                rankDetail.addFilterDetail(selectExpression.getFilterId(),masterId);
////                continue;
////            }
////            if (StringUtils.isNotEmpty(selectExpression.getAppointGroupExpression())) {
////                boolean groupResult=QlExpressStatic.QlExpressBoolean(selectExpression.getAppointGroupExpression()
////                        ,masterContext);
////                if (!groupResult) {
//////                    masterSet.remove(masterId);
////                    rankDetail.addFilterDetail(selectExpression.getFilterId(),masterId);
////                    result=true;
////                }
////            }
////        }
////        return result;
////    }
//
//
//    /**
//     * 过滤
//     * @param masterContext
//     */
//    private boolean filter(String masterId,Set<String> masterSet,DefaultContext<String, Object> masterContext,
//                           DistributeRankDetail rankDetail){
//        boolean result=false;
//        if (CollectionUtils.isEmpty(selectStrategy)) {
//            return false;
//        }
//        for (Filter filter : selectStrategy) {
//            log.info("masterContext"+ JSON.toJSONString(masterContext));
//            if(StringUtils.isNotBlank(filter.getExpression())){
//                result=QlExpressStatic.QlExpressBoolean(filter.getExpression(),masterContext);
//                if (result) {
//                    rankDetail.addFilterDetail(filter.getFilterId(),masterId);
//                    break;
//                }
//            }
//
//            if (StringUtils.isNotEmpty(filter.getAppointGroupExpression())) {
//                boolean groupResult=QlExpressStatic.QlExpressBoolean(filter.getAppointGroupExpression()
//                        ,masterContext);
//                if (!groupResult) {
////                    masterSet.remove(masterId);
//                    rankDetail.addFilterDetail(filter.getFilterId(),masterId);
//                    result=true;
//                    break;
//                }
//            }
//        }
//        return result;
//    }
//    /**
//     * 评分
//     * @param masterId
//     * @param scorerMaster
//     * @param masterContext
//     */
//    private void scorer(String masterId,List<ScorerMaster> scorerMaster,
//                        DefaultContext<String, Object> orderFeatures,
//                        DefaultContext<String, Object> masterContext,
//                        DistributeRankDetail rankDetail){
//        if (scoringStrategy==null||scoringStrategy.size()==0) {
//            scorerMaster.add(ScorerMaster.ScorerMasterBuilder.aScorerMaster()
//                    .withMasterId(masterId)
//                    .build());
//            return;
//        }
//        BigDecimal score=BigDecimal.ZERO;
//        for (Scorer scorer : scoringStrategy) {
//            score=score.add(scorer.mark(orderFeatures,masterContext,rankDetail,masterId));
//        }
//        final ScorerMaster build = ScorerMaster.ScorerMasterBuilder.aScorerMaster()
//                .withMasterId(masterId)
//                .withScore(score)
//                .build();
//        scorerMaster.add(build);
//    }
//
//
//    public List<Filter> getSelectStrategy() {
//        return selectStrategy;
//    }
//
//    public List<Scorer> getScoringStrategy() {
//        return scoringStrategy;
//    }
//
//
//    public static final class OrderDistributorBuilder {
//        private List<Filter> selectStrategy=new ArrayList<>();
//        private List<Scorer> scoringStrategy=new ArrayList<>();
//
//        private OrderDistributorBuilder() {
//        }
//
//        public static OrderDistributorBuilder anOrderDistributor() {
//            return new OrderDistributorBuilder();
//        }
//
//        public OrderDistributorBuilder withSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies) {
//            orderSelectStrategies.stream().forEach(row->{
//                try {
//                    final String selectStrategyExpression = row.getSelectStrategyExpression();
//                    final JSONObject selectStrategyExpressionJSON = JSONObject.parseObject(selectStrategyExpression);
//                    this.selectStrategy.add(
//                            Filter.FilterBuilder.aFilter()
//                                    .withFilterId(String.valueOf(row.getStrategyId()))
//                                    .withExpression(selectStrategyExpressionJSON.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION))
//                                    .build()
//                    );
//                }catch (Exception e){
//                }
//            });
//            return this;
//        }
//
//        public OrderDistributorBuilder withScoringStrategy(List<OrderScoringStrategy> orderScoringStrategies) {
//            try {
//                for (OrderScoringStrategy orderScoringStrategy : orderScoringStrategies) {
//                    final String scoringStrategyExpression = orderScoringStrategy.getScoringStrategyExpression();
//                    final JSONArray objects = JSONObject.parseArray(scoringStrategyExpression);
//                    for (int i = 0; i < objects.size(); i++) {
//                        final JSONObject outerRow = objects.getJSONObject(i);
//                        final String outerOpenConditionRuleExpression =outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                        final JSONArray scoreRuleList = outerRow.getJSONArray(FieldConstant.SCORE_RULE_LIST);
//                        List<ScorerItem> scorerItemList=new ArrayList<>();
//                        for (int j = 0; j < scoreRuleList.size(); j++) {
//                            final JSONObject innerRow = scoreRuleList.getJSONObject(j);
//                            final String openConditionRuleExpression = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                            final String scoreRuleExpression=innerRow.getString(FieldConstant.SCORE_RULE_EXPRESSION);
//                            scorerItemList.add(
//                                    ScorerItem.ScorerItemBuilder.aScorerItem()
//                                            .withAdmittance(openConditionRuleExpression)
//                                            .withScoreRuleExpression(scoreRuleExpression)
//                                            .build()
//                            );
//                        }
//                        this.scoringStrategy.add(
//                                Scorer.ScorerBuilder.aScorer()
//                                        .withAdmittance(outerOpenConditionRuleExpression)
//                                        .withScorerItemList(scorerItemList)
//                                        .build()
//                        );
//                    }
//                }
//            }catch (Exception e){
//                log.warn("OrderDistributor withScoringStrategy exception:[{}],[{}]",orderScoringStrategies,e);
//            }
//            return this;
//        }
//
//        public DistributeOrderDistributor build() {
//            DistributeOrderDistributor distributeOrderDistributor = new DistributeOrderDistributor();
//            distributeOrderDistributor.selectStrategy = this.selectStrategy;
//            distributeOrderDistributor.scoringStrategy = this.scoringStrategy;
//            return distributeOrderDistributor;
//        }
//    }
//
//    @Override
//    public String toString() {
//        return JSONObject.toJSONString(this);
//    }
//
//    public String getDistributeRule() {
//        return distributeRule;
//    }
//
//    public void setDistributeRule(String distributeRule) {
//        this.distributeRule = distributeRule;
//    }
//}
