package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "order_refund")
public class OrderRefund {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "master_id")
    private Long masterId;

    @Column(name = "order_serve_type")
    private Integer orderServeType;

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "refund_time")
    private Date refundTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;



}
