package com.wanshifu.master.order.push.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ql.util.express.DefaultContext;
import com.wanshifu.base.address.domain.po.PositionConvert;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.DistributeType;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.rqt.UpdateDirectAppointFailReasonRqt;
import com.wanshifu.master.order.push.repository.AgentOrderDistributeRepository;
import com.wanshifu.master.order.push.service.impl.*;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:19
 */
@Service
@Slf4j
@Deprecated
public class DistributeOrderDistributeServiceImpl implements DistributeOrderDistributeService {


    @Resource
    private AgentOrderDistributeRepository agentOrderDistributeRepository;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private OrderConfigCommon orderConfigCommon;

    @Resource
    private Tools tools;

    @Resource
    private InfoQueryApi infoQueryApi;

    @Autowired
    private DistributeFactory distributeFactory;

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String orderMatchMasterTopic;

    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private OrderDataBuilder orderDataBuilder;



    @Override
        public Integer supportMasterOrderDistribute(SupportMasterPushResultMessage message){

        Long globalOrderId = message.getGlobalOrderId();


        try{
            DefaultContext<String, Object> orderFeatureContext = initOrderFeatures(message.getGlobalOrderId());

            Set<String> masterSet = message.getMasterSet();
            OrderDistributeRqt orderDistributeRqt = new OrderDistributeRqt();
            orderDistributeRqt.setMasterOrderId(globalOrderId);
            orderDistributeRqt.setMasterSet(message.getMasterSet());

            //获取策略
            final OrderDistributor orderDistributor = distributeFactory
                    .matchDistributor(orderDataBuilder.buildEnterpriseOrder(globalOrderId),orderFeatureContext, DistributeType.NEW_MASTER_SUPPORT.getCode());
            if (!orderDistributor.isMatched()) {
                this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
                log.info("no matchDistributor matched!:{}",globalOrderId);
                return 0;
            }

            //获取特征
            featureRepository.orderFeatureReplenish(orderFeatureContext,orderDistributor.getOrderFeatureSet());
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures = featureRepository.masterFeatures(masterSet, orderDistributor.getMasterFeatureSet(),orderFeatureContext);
            log.info("master_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]",globalOrderId,orderFeatureContext,masterFeatures);


            //过滤排序
            //过滤排序
            final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(globalOrderId))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();

            final List<ScorerMaster> scorerMasterList = orderDistributor
                    .rank(masterSet,orderFeatureContext,masterFeatures,rankDetail);


            log.info("rankDetail: " + JSON.toJSONString(rankDetail));


            if(CollectionUtils.isEmpty(scorerMasterList)){
                this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
                return 0;
            }

            String distributeRule = orderDistributor.getDistributeRule();

            ScorerMaster scorerMaster = distribute(scorerMasterList,distributeRule);

            log.info("scorerMasterList:" + JSON.toJSONString(scorerMaster));

            this.sendSupportMasterMatchMessage(globalOrderId,scorerMaster,1L);

            return 1;
        }catch(Exception e){
            log.error("supportMasterOrderDistribute error,message:" + JSON.toJSONString(message) );
            this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
        }

        return 0;

    }



    @Override
    public Integer updateDirectAppointFailReason(UpdateDirectAppointFailReasonRqt rqt) {
        return agentOrderDistributeRepository.updateDirectAppointFailReason(rqt.getDistributeId(), rqt.getDirectAppointFailReason());
    }


    @Override
    public DefaultContext<String, Object> initOrderFeatures(OrderBase orderBase, OrderExtraData orderExtraData, OrderGrab orderGrab){

        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put(FieldConstant.ACCOUNT_ID,orderBase.getAccountId());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_ID,orderBase.getBusinessLineId());
        orderFeatures.put(FieldConstant.CATEGORY_ID,orderBase.getCategoryId());
        orderFeatures.put(FieldConstant.APPOINT_TYPE,orderGrab.getAppointType());

        JSONObject orderPosition = new JSONObject()
                .fluentPut("buyerAddressLatitude", orderExtraData.getBuyerAddressLatitude())
                .fluentPut("buyerAddressLongitude", orderExtraData.getBuyerAddressLongitude());

        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

        orderFeatures.put(FieldConstant.ORDER_LNG_LAT, orderExtraData.getBuyerAddressLongitude()+","+orderExtraData.getBuyerAddressLatitude());
        orderFeatures.put(FieldConstant.SECOND_DIVISION_ID, addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        orderFeatures.put(FieldConstant.THIRD_DIVISION_ID, orderBase.getThirdDivisionId());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());
        orderFeatures.put(FieldConstant.LV1_SERVE_ID,orderBase.getServeLevel1Ids());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderFeatures.put(FieldConstant.LV2_SERVE_IDS, Joiner.on(",").join(lv2ServeIdList));
                }

            }

        }
        orderFeatures.put(FieldConstant.LV3_SERVE_IDS,orderBase.getServeIds());

        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
        if (this.isEnterpriseOrder(orderBase)) {

            GetOrderBaseByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
            getOrderByGlobalIdRqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
            GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchNoLog(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderByGlobalIdRqt));
            if(Objects.isNull(getOrderBaseByGlobalIdRsp) || getOrderBaseByGlobalIdRsp.getOrderBase() == null){
                orderFeatures.put(FieldConstant.USER_ID, 0L);
            }else{
                orderFeatures.put(FieldConstant.USER_ID,"user".equals(getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountType()) ? getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountId() : 0L);
            }
        }else {
            orderFeatures.put(FieldConstant.USER_ID,orderBase.getAccountId());
        }

        orderFeatures.put(FieldConstant.ORDER_FROM,getOrderFrom(orderBase));
        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderExtraData));

        return orderFeatures;
    }


    public DefaultContext<String, Object> initOrderFeatures(Long globalOrderId){


        GetOrderBaseByGlobalIdRqt getOrderBaseByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
        getOrderBaseByGlobalIdRqt.setGlobalOrderTraceId(globalOrderId);
        GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp=  infoQueryApi.getOrderBaseByGlobalId(getOrderBaseByGlobalIdRqt);
        if(Objects.isNull(getOrderBaseByGlobalIdRsp) || Objects.isNull(getOrderBaseByGlobalIdRsp.getOrderBase())){
            return null;
        }

        com.wanshifu.enterprise.order.domain.po.OrderBase orderBase = getOrderBaseByGlobalIdRsp.getOrderBase();
        com.wanshifu.enterprise.order.domain.po.OrderExtraData orderExtraData = getOrderBaseByGlobalIdRsp.getOrderExtraData();


        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put(FieldConstant.ACCOUNT_ID,orderBase.getFromAccountId());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_ID,orderBase.getBussinessId().intValue());
        orderFeatures.put(FieldConstant.CATEGORY_ID,orderBase.getCategoryId());
        orderFeatures.put(FieldConstant.APPOINT_TYPE,AppointType.OPEN.value);

//        JSONObject orderPosition = new JSONObject()
//                .fluentPut("buyerAddressLatitude", orderExtraData.getBuyerAddressLatitude())
//                .fluentPut("buyerAddressLongitude", orderExtraData.getBuyerAddressLongitude());

//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

//        orderFeatures.put("order_lng_lat", orderExtraData.getBuyerAddressLongitude()+","+orderExtraData.getBuyerAddressLatitude());
        orderFeatures.put(FieldConstant.SECOND_DIVISION_ID, addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        orderFeatures.put(FieldConstant.THIRD_DIVISION_ID, orderBase.getThirdDivisionId());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBussinessId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderFeatures.put(FieldConstant.LV2_SERVE_IDS, Joiner.on(",").join(lv2ServeIdList));
                }
                orderFeatures.put(FieldConstant.LV1_SERVE_ID,serveBaseInfoRespList.get(0).getLevel1Id());
            }

        }
        orderFeatures.put(FieldConstant.LV3_SERVE_IDS,orderBase.getServeIds());

        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
        orderFeatures.put(FieldConstant.USER_ID,AccountType.USER.code.equals(orderBase.getFromAccountType()) ? orderBase.getFromAccountId() : 0L);


        orderFeatures.put(FieldConstant.ORDER_FROM,getOrderFrom(orderBase));
        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderExtraData));


        PositionConvert positionConvert = addressCommon.getPositionConvertByDivisionId(orderBase.getThirdDivisionId(),orderExtraData.getBuyerAddress());

        if(Objects.nonNull(positionConvert) && Objects.nonNull(positionConvert.getLatitude()) && Objects.nonNull(positionConvert.getLongitude() )&&
                positionConvert.getLatitude().compareTo(BigDecimal.ZERO) > 0 && positionConvert.getLongitude().compareTo(BigDecimal.ZERO) > 0){
            orderFeatures.put(FieldConstant.ORDER_LNG_LAT,positionConvert.getLongitude().toString() + "," + positionConvert.getLatitude().toString());
        }







        return orderFeatures;
    }


    private void collectContractMasterAutoOfferPrice(OrderPushedResultNotice pushedResultNotices){

        OrderBase orderBase = pushedResultNotices.getOrderBaseComposite().getOrderBase();
        OrderGrab orderGrab = pushedResultNotices.getOrderBaseComposite().getOrderGrab();

        List<OrderPushedResultNotice.MasterInfo> masterInfoList = pushedResultNotices.getMasterInfoList();

        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        if(orderGrab.getAppointType().equals(AppointType.OPEN.value)){
            rqt.setAppointDetailType(AppointDetailType.AUTO_OFFER_ZUOYEBANG.getCode());
            rqt.setAppointType(AppointType.OPEN.value);
        }

        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();

        masterInfoList.forEach(masterInfo -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(masterInfo.getMasterId());
            masterPrice.setPrice(masterInfo.getAutoPrice());
            masterList.add(masterPrice);
            rqt.setMasterList(masterList);
        });
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),500L);

    }

    private ScorerMaster distribute(List<ScorerMaster> scorerMasterList,String distributeRule){
        if(OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            return scorerMasterList.get(0);
        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            int index = scorerMasterList.size() >= 50 ? 50 : scorerMasterList.size();
            Random random = new Random();
            int randomNum = random.nextInt(index);
            return scorerMasterList.subList(0,index).get(randomNum);
        }else if(OrderDistributeRule.RANDOM.getCode().equals(distributeRule)){
            Random random = new Random();
            int randomNum = random.nextInt(scorerMasterList.size());
            return scorerMasterList.get(randomNum);
        }
        return null;
    }


    private final static String SUPPORT_MASTER_MATCH = "support_master_match";
    public boolean sendSupportMasterMatchMessage(Long globalOrderId, ScorerMaster scorerMaster, Long delayTime){
        Map<String,Long> resultMap = new HashMap<>();
        resultMap.put("globalOrderTraceId",globalOrderId);
        if(scorerMaster != null && scorerMaster.getMasterId() != null){
            resultMap.put("supportMasterId",Long.parseLong(scorerMaster.getMasterId()));
        }
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,SUPPORT_MASTER_MATCH,JSON.toJSONString(resultMap),delayTime);
        return true;
    }


    private boolean isEnterpriseOrder(OrderBase orderBase) {
        return AccountType.ENTERPRISE.code.equals(orderBase.getAccountType());
    }

    private String getOrderFrom(com.wanshifu.enterprise.order.domain.po.OrderBase orderBase){
        String orderFrom = orderBase.getOrderFrom();
        if(AccountType.USER.code.equals(orderBase.getFromAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom) || OrderFrom.THIRDPART.valueEn.equals(orderFrom)){
                return "site";
            }else  if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family";
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getFromAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom)){
                return "enterprise_inside";
            }else if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family_enterprise";
            }else if(OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderFrom)){
                return "family_outside";
            }
        }

        return "";


    }

    private String getOrderFrom(OrderBase orderBase){
        String orderFrom = orderBase.getOrderFrom();
        if(AccountType.USER.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom) || OrderFrom.THIRDPART.valueEn.equals(orderFrom)){
                return "site";
            }else  if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family";
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom)){
                return "enterprise_inside";
            }else if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family_enterprise";
            }else if(OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderFrom)){
                return "family_outside";
            }
        }

        return "";


    }


    private List<String> getTimeLinessTag(OrderExtraData orderExtraData){

        List<String> timeLinessTagList = new ArrayList<>();


        if(orderExtraData.getTimerFlag() != null && orderExtraData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderExtraData.getEmergencyOrderFlag() != null && orderExtraData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderExtraData.getOnTimeOrderFlag() != null && orderExtraData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(orderExtraData.getExpectDoorInStartDate() != null){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    private List<String> getTimeLinessTag(com.wanshifu.enterprise.order.domain.po.OrderExtraData orderExtraData){

        List<String> timeLinessTagList = new ArrayList<>();


//        if(orderExtraData.getTimerFlag() != null && orderExtraData.getTimerFlag() == 1){
//            timeLinessTagList.add("regular_time_order");
//        }
//
//        if(orderExtraData.getEmergencyOrderFlag() != null && orderExtraData.getEmergencyOrderFlag() == 1){
//            timeLinessTagList.add("emergency_order");
//        }
//
//        if(orderExtraData.getOnTimeOrderFlag() != null && orderExtraData.getOnTimeOrderFlag() == 1){
//            timeLinessTagList.add("on_time_order");
//        }
//
//        if(orderExtraData.getExpectDoorInStartDate() != null){
//            timeLinessTagList.add("expect_time_order");
//        }

        return timeLinessTagList;
    }

}
