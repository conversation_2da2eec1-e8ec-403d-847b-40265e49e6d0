package com.wanshifu.master.order.push.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.wanshifu.base.address.domain.lbs.TencentDirectionResp;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.exclusive.api.ExclusiveMasterApi;
import com.wanshifu.master.exclusive.domains.api.request.exclusiveMaster.GetRecruitServeInfoRqt;
import com.wanshifu.master.exclusive.domains.api.response.exclusiveMaster.GetRecruitServeInfoResp;
import com.wanshifu.master.exclusive.domains.po.RecruitBaseInfo;
import com.wanshifu.master.exclusive.domains.po.RecruitServePrice;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoResp;
import com.wanshifu.master.information.domain.po.MasterAggregation;
import com.wanshifu.master.order.push.domain.dto.CalculatePushDistanceParamVo;
import com.wanshifu.master.order.push.domain.dto.CalculatePushDistanceRespVo;
import com.wanshifu.master.order.push.domain.dto.MasterBase;
import com.wanshifu.master.order.push.domain.dto.OrderPushMaster;
import com.wanshifu.master.order.push.domain.enums.PushDistanceType;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.service.impl.AddressCommon;
import com.wanshifu.master.order.push.service.impl.Tools;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.util.BigDecimalUtil;
import com.wanshifu.util.LongUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderMasterCalculateService {


    @Resource
    private Tools tools;

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;


    /**
     * 最大距离
     */
    @Value("${max_push_distance:3000}")
    private String maxPushDistance;

    /**
     * 导航距离开放城市配置
     */
    @Value("${navigation.pushDistance.openCitys}")
    private String navigationPushDistanceOpenCitys;

    /**
     * 地址服务URL
     */
    @Value("${wanshifu.base-address-service.url}")
    private String baseAddressServiceUrl;


    @Resource
    private ExclusiveMasterApi exclusiveMasterApi;



    private static final Integer TIMEOUT_MILLISECOND = 500;



    /**
     * 校验师傅和订单绑定技能是否相关
     *
     * @param masterBase
     * @param orderBindTechniques 1040,1043|1040,1044|1043,1039|1044,1039
     * @return
     */
    public boolean checkTechniqueMatch(String orderBindTechniques, MasterBase masterBase) {

        if (masterBase == null || StringUtils.isBlank(orderBindTechniques)) {
            return false;
        }

        if (StringUtils.isBlank(masterBase.getMasterTechniqueIds())) {
            return false;
        }

        List<String> masterTechniqueList = Arrays.stream(masterBase.getMasterTechniqueIds().split(",")).collect(Collectors.toList());

       List<String> orderTechniqueIdList =  Arrays.stream(orderBindTechniques.split("\\|"))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        for(String orderTechnologyId : orderTechniqueIdList){
            if (StringUtils.isBlank(orderTechnologyId)) {
                return false;
            }
            if(masterTechniqueList.containsAll(Arrays.stream(orderTechnologyId.split(",")).collect(Collectors.toList()))){
                return true;
            }
        }
        return false;
    }


    private List<OrderPushMaster> filterBrandOrderBusiness(String pushMode,Long recruitId,
                                                           List<Long> masterIdsList,
                                                           List<OrderPushMaster> orderPushMasterList,
                                                           OrderDetailData orderDetailData) {

        if ("brand".equals(pushMode) && recruitId != null) {

            boolean hasPrice = brandOrderIfOrNotPrice(recruitId, new HashSet<>(masterIdsList), orderDetailData);
            if (LongUtil.isEmpty(orderDetailData.getFourthDivisionId())) {
                return orderPushMasterList;
            } else if (!hasPrice) {
                return orderPushMasterList;
            } else {


                List<OrderPushMaster> tmpList = orderPushMasterList
                        .stream()
                        .sorted(Comparator.comparing(OrderPushMaster::getPushDistance))
                        .filter(f -> f.getPushDistance() != null && f.getPushDistance() != 0L)
                        .collect(Collectors.toList());

                orderPushMasterList = CollUtil.isEmpty(tmpList) ? Collections.singletonList(orderPushMasterList.get(0)) : Collections.singletonList(tmpList.get(0));
            }
        }

        return orderPushMasterList;
    }


    private boolean brandOrderIfOrNotPrice(Long recruitId, Set<Long> pushMasterIds, OrderDetailData orderDetailData) {
        Long masterId = pushMasterIds.iterator().next();
        GetRecruitServeInfoRqt getRecruitServeInfoRqt = new GetRecruitServeInfoRqt();
        getRecruitServeInfoRqt.setNowTime(new Date());
        getRecruitServeInfoRqt.setMasterId(masterId);
        getRecruitServeInfoRqt.setRecruitId(recruitId);
        GetRecruitServeInfoResp recruitServeInfo = tools.catchNoLog(() -> exclusiveMasterApi.getRecruitServeInfo(getRecruitServeInfoRqt));
        if (recruitServeInfo == null) {
            return false;
        }
        RecruitBaseInfo recruitBaseInfo = recruitServeInfo.getRecruitBaseInfo();
        List<RecruitServePrice> recruitServePriceList = recruitServeInfo.getRecruitServePriceList();
        if (ObjectUtil.isAllEmpty(recruitBaseInfo, recruitServePriceList)) {
            log.error("brandOrderIfOrNotPrice=recruitServeInfo={}", recruitServeInfo);
            return false;
        }

        Long thirdDivisionId = orderDetailData.getThirdDivisionId();
        Long serveId = Long.parseLong(orderDetailData.getLv3ServeIds().split(",")[0]);
        AtomicReference<BigDecimal> ret = new AtomicReference<>(BigDecimal.ZERO);
        recruitServePriceList.stream()
                .filter(f -> Objects.equals(f.getServeId(), serveId))
                .filter(f -> "unite".equals(f.getPricingType()) || ("regional".equals(f.getPricingType()) && Objects.equals(f.getLv3DivisionId(), thirdDivisionId)))
                .findFirst()
                .ifPresent(e -> ret.set(e.getPrice()));

        return ret.get().compareTo(BigDecimal.ZERO) > 0;
    }


    /**
     * 计算推送距离
     *
     * @param params
     * @return
     */
    public CalculatePushDistanceRespVo calculatePushDistance(CalculatePushDistanceParamVo params) {
        Long pushDistance;
        Integer pushDistanceType;

        //师傅经度
        BigDecimal masterLongitude;
        BigDecimal masterLatitude;
        if (BigDecimalUtil.isEmpty(params.getMasterLatitude()) || BigDecimalUtil.isEmpty(params.getMasterLongitude())) {
            GetMasterInfoRqt getMasterInfoRqt = new GetMasterInfoRqt();
            getMasterInfoRqt.setMasterId(params.getMasterId());
            GetMasterInfoResp masterInfo = tools.catchLog(() -> commonQueryServiceApi.getMasterInfo(getMasterInfoRqt),"commonQueryServiceApi.getMasterInfo",getMasterInfoRqt);
            masterLongitude = Optional.ofNullable(masterInfo).map(GetMasterInfoResp::getLocationLongitude).orElse(BigDecimal.ZERO);
            masterLatitude = Optional.ofNullable(masterInfo).map(GetMasterInfoResp::getLocationLatitude).orElse(BigDecimal.ZERO);
        } else {
            masterLongitude = params.getMasterLongitude();
            masterLatitude = params.getMasterLatitude();
        }

        BigDecimal orderLongitude = params.getOrderLongitude();
        BigDecimal orderLatitude = params.getOrderLatitude();

        //7.13_0509迭代: 经纬度大于0才计算距离
        BigDecimal zero = BigDecimal.ZERO;
        boolean b = !BigDecimalUtil.isGreaterThan(masterLongitude, zero) || !BigDecimalUtil.isGreaterThan(masterLatitude, zero)
                || !BigDecimalUtil.isGreaterThan(orderLongitude, zero) || !BigDecimalUtil.isGreaterThan(orderLatitude, zero);
        //log.info("=======isGreaterThanBoolean========orderId={},masterId={},orderBaseInfo={}", params.getOrderId(), params.getMasterId(), b);

        if (b) {
            return new CalculatePushDistanceRespVo(0L, 0);
        }
       // log.info("=======isGreaterThan========orderId={},masterId={},masterLongitude={},masterLatitude={},orderLongitude={},orderLatitude={}", params.getOrderId(), params.getMasterId()
        //        , JSON.toJSONString(masterLongitude), JSON.toJSONString(masterLatitude), JSON.toJSONString(orderLongitude), JSON.toJSONString(orderLatitude));

        //直线距离小于30KM,则调导航距离，反之取直线距离
        Long straightLintDistance = this.getStraightLintDistance(masterLatitude.doubleValue(), masterLongitude.doubleValue(), orderLatitude.doubleValue(), orderLongitude.doubleValue());
      //  log.info("=======calculatePushDistanceLog========orderId={},masterId={},straightLintDistance={},maxPushDistance={}", params.getOrderId(), params.getMasterId(), straightLintDistance, maxPushDistance);

        pushDistance = straightLintDistance;
        pushDistanceType = PushDistanceType.STRAIGHT_LINE.code;

        if (straightLintDistance < Long.parseLong(maxPushDistance) && params.getCityDivisionId() != null && params.getCityDivisionId() > 0) {
            if (StringUtils.isNotEmpty(navigationPushDistanceOpenCitys)) {
                if ("all".equals(navigationPushDistanceOpenCitys)
                        || Arrays.asList(navigationPushDistanceOpenCitys.split(",")).contains(params.getCityDivisionId().toString())) {
                    pushDistance = this.getNavigationDistance(masterLatitude, masterLongitude, orderLatitude, orderLongitude, params.getOrderId(), params.getMasterId());
               //     log.debug("=======calculatePushDistanceLog========orderId={},masterId={},navigationDistance={},maxPushDistance={}", params.getOrderId(), params.getMasterId(), pushDistance, maxPushDistance);
                    pushDistanceType = PushDistanceType.NAVIGATION.code;
                }
            }
        }
        return new CalculatePushDistanceRespVo(pushDistance, pushDistanceType);
    }


    private static final Integer BUSINESS_CODE = 3;
    private final Type type = new TypeReference<ResponseEntity<List<TencentDirectionResp>>>() {
    }.getType();

    /**
     * 获取导航距离
     *
     * @param sourceLatitude
     * @param sourceLongitude
     * @param targetLatitude
     * @param targetLongitude
     * @return
     */
    public Long getNavigationDistance(BigDecimal sourceLatitude, BigDecimal sourceLongitude,
                                      BigDecimal targetLatitude, BigDecimal targetLongitude,
                                      Long orderId, Long masterId) {
        Long navigationDistance = 0L;
        //格式,英文逗号分割：维度,经度
        String from = String.format("%s,%s",
                BigDecimalUtil.reserveDecimalPlacesByScale(sourceLatitude, 6, RoundingMode.DOWN),
                BigDecimalUtil.reserveDecimalPlacesByScale(sourceLongitude, 6, RoundingMode.DOWN)
        );
        String to = String.format("%s,%s",
                BigDecimalUtil.reserveDecimalPlacesByScale(targetLatitude, 6, RoundingMode.DOWN),
                BigDecimalUtil.reserveDecimalPlacesByScale(targetLongitude, 6, RoundingMode.DOWN)
        );
        HashMap<String, Object> params = Maps.newHashMapWithExpectedSize(3);
        params.put("businessCode", BUSINESS_CODE);
        params.put("from", from);
        params.put("to", to);
        try {
            String url = baseAddressServiceUrl + "/lbs/queryDistance";
            //500毫秒超时
            String result = HttpUtil.get(url, params, TIMEOUT_MILLISECOND);
            log.info("=======lbsApiQueryDistanceResult========orderId={},masterId={},params={},result={}", orderId, masterId, JSON.toJSONString(params), JSON.toJSONString(result));
            ResponseEntity<List<TencentDirectionResp>> responseEntity = JSON.parseObject(result, type);
            if (!"200".equals(responseEntity.retCode)) {
                throw new BusException(responseEntity.getRetMesg());
            }
            if (CollectionUtils.isNotEmpty(responseEntity.retData)) {
                navigationDistance = Long.valueOf(responseEntity.retData.get(0).getDistance());
            }
            return navigationDistance;
        } catch (Exception e) {
            log.error("=======lbsApiQueryDistanceErrorLog========params,e={}", JSON.toJSONString(params), e);
            return 0L;
        }
    }


    @Data
    public static class ResponseEntity<T> {

        private String retCode;

        private T retData;

        private String retMesg;

    }


    /**
     * 获取直线距离
     * @param sourceLatitude
     * @param sourceLongitude
     * @param targetLatitude
     * @param targetLongitude
     * @return
     */
    public Long getStraightLintDistance(double sourceLatitude, double sourceLongitude, double targetLatitude, double targetLongitude) {
        GlobalCoordinates source = new GlobalCoordinates(sourceLatitude, sourceLongitude);
        GlobalCoordinates target = new GlobalCoordinates(targetLatitude, targetLongitude);
        GeodeticCurve curve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.Sphere, source, target);
        return new Double(curve.getEllipsoidalDistance()).longValue();
    }

}
