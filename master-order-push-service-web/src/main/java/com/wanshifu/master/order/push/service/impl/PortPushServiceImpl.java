package com.wanshifu.master.order.push.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.PushPortRuleApi;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.dto.PortPushRuleDTO;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.message.PortPushMessage;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.CreateRqt;
import com.wanshifu.master.order.push.repository.PushPortRuleRepository;
import com.wanshifu.master.order.push.service.PortPushService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class PortPushServiceImpl implements PortPushService {

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private PushPortRuleRepository pushPortRuleRepository;

    

    @Override
    public  void matchPushPortRule(String orderTag,OrderDetailData orderDetailData,Integer pushNum){

        if(MasterSourceType.TOC.code.equals(orderDetailData.getPushExtraData().getMasterSourceType())){

            List<String> orderTagList = new ArrayList<>();
            orderTagList.add(orderTag);
            if(OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom()) && AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())){
                //家庭总包端口规则
                orderTagList.add("family_enterprise");
            }

            List<PushPortRule> pushPortRuleList = pushPortRuleRepository.selectByCityAndLv1ServeId(orderTagList, Collections.singletonList(String.valueOf(orderDetailData.getSecondDivisionId())),
                    Collections.singletonList(orderDetailData.getLv1ServeIds()),Collections.singletonList(String.valueOf(orderDetailData.getAppointType())),null);


            if(CollectionUtils.isEmpty(pushPortRuleList)){
                pushPortRuleList = pushPortRuleRepository.selectByCityAndLv1ServeId(orderTagList, Collections.singletonList(String.valueOf(orderDetailData.getSecondDivisionId())),
                        Collections.singletonList("all"),Collections.singletonList(String.valueOf(orderDetailData.getAppointType())),null);
            }


            if(CollectionUtils.isEmpty(pushPortRuleList)){
                pushPortRuleList = pushPortRuleRepository.selectByCityAndLv1ServeId(orderTagList, Collections.singletonList("all"),
                        Collections.singletonList(orderDetailData.getLv1ServeIds()),Collections.singletonList(String.valueOf(orderDetailData.getAppointType())),null);
            }


            if(CollectionUtils.isEmpty(pushPortRuleList)){
                pushPortRuleList = pushPortRuleRepository.selectByCityAndLv1ServeId(orderTagList, Collections.singletonList("all"),
                        Collections.singletonList("all"),Collections.singletonList(String.valueOf(orderDetailData.getAppointType())),null);
            }

            if(CollectionUtils.isEmpty(pushPortRuleList)){
                return ;
            }
            

            pushPortRuleList.forEach(pushPortRule -> {
                if(pushPortRule.getRuleStatus() == 1){

                    boolean finalResult = false;
                    if(StringUtils.isNotBlank(pushPortRule.getPushRule())){
                        List<PortPushRuleDTO> pushRuleList = JSON.parseArray(pushPortRule.getPushRule(),PortPushRuleDTO.class);
                        if(CollectionUtils.isNotEmpty(pushRuleList)){
                            if(pushRuleList.size() == 1){

                                PortPushRuleDTO pushRuleDTO = pushRuleList.get(0);

                                finalResult = portPush(pushNum,pushRuleDTO,orderDetailData);



                            }else if(pushRuleList.size() == 2){
                                PortPushRuleDTO pushRule = pushRuleList.get(0).getWeight() > pushRuleList.get(1).getWeight() ? pushRuleList.get(0) : pushRuleList.get(1);
                                boolean result = portPush(pushNum,pushRule,orderDetailData);
                                if(!result){
                                    PortPushRuleDTO pushRuleDTO = pushRuleList.stream().filter(rule -> "push_interval_time".equals(rule.getCondition())).findFirst().orElse(null);
                                    if(Objects.nonNull(pushRuleDTO)){
                                        result = portPush(pushNum,pushRuleDTO,orderDetailData);
                                    }
                                }
                                finalResult = result;

                            }
                        }
                    }

                    if(!finalResult){
                        log.info(String.format("matchPushPort no push tobMaster，pushPortRule:%s,pushNum:%d,orderDetailData:%s" , JSON.toJSONString(pushPortRule),pushNum,JSON.toJSONString(orderDetailData)));
                    }
                }
            });



        }
    }

    private boolean portPush(Integer pushNum,PortPushRuleDTO pushRuleDTO,OrderDetailData orderDetailData){
        if("push_interval_time".equals(pushRuleDTO.getCondition())){
            PortPushMessage message = new PortPushMessage();
            message.setOrderDetailData(orderDetailData);
            message.setPortPushRuleDTO(pushRuleDTO);
            Long delayTime = pushRuleDTO.getIntervalTime() == 0 ? 100 : pushRuleDTO.getIntervalTime() * 60 * 1000L;
            pushQueueService.sendPortPushMessage(message,delayTime);
            return true;
        }else if("push_master_num".equals(pushRuleDTO.getCondition())){
            if(Objects.isNull(pushNum) || pushNum <=  pushRuleDTO.getPushNum()){
                OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
                rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
                rqt.setPushMode(PushMode.NORMAL.code);
                rqt.setMasterSourceType(MasterSourceType.TOB.getCode());
                pushQueueService.sendDelayPushMessage(100L,JSON.toJSONString(rqt));
                return true;
            }
        }

        return false;

    }
}
