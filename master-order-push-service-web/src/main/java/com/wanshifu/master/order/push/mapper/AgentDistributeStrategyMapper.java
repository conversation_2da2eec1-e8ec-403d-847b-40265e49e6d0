package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/26 18:32
 */
public interface AgentDistributeStrategyMapper extends IBaseCommMapper<AgentDistributeStrategy> {

    List<AgentDistributeStrategy> selectByServeId(@Param("agentId") Long agentId, @Param("serveId") Long serveId, @Param("thirdDivisionId") Long thirdDivisionId);


    List<AgentDistributeStrategy> selectList(@Param("strategyName") String strategyName, @Param("agentName") String agentName, @Param("serveName") String serveName,
                                             @Param("cityDivisionId") Long cityDivisionId, @Param("createTimeStart") Date createTimeStart,
                                             @Param("createTimeEnd") Date createTimeEnd,@Param("masterSourceType") String masterSourceType);

    List<AgentDistributeStrategy> selectByAgentIdAndServeIdsAndDivisionId(@Param("agentId") Long agendId, @Param("serveIdList")  List<Long> serveIdList,
                                                                          @Param("thirdDivisionId") Long thirdDivisionId);


    List<AgentDistributeStrategy> selectByServeIdsAndDivisionId(@Param("serveIdList")  List<Long> serveIdList,
                                                                          @Param("fourthDivisionId") Long fourthDivisionId);
}
