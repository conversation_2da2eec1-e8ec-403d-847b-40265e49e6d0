package com.wanshifu.master.order.push.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.message.EnterpriseAppointMessage;
import com.wanshifu.master.order.push.domain.message.OrderChangeGrabPushMessage;
import com.wanshifu.master.order.push.domain.message.PortPushMessage;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.orderPush.GetOrderPushScoreResp;
import com.wanshifu.master.order.push.domain.rqt.AgentPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.OrderPushRqt;
import com.wanshifu.master.order.push.domain.rqt.RefundSyncRqt;
import com.wanshifu.master.order.push.domain.rqt.orderPush.GetOrderPushScoreRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.request.offer.GetOrderOfferPriceListByGlobalOrderIdsRqt;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.SimpleOrderGrab;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.api.response.offer.GetOrderOfferPriceListByGlobalOrderIdsResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.offer.domains.po.OrderOfferPrice;
import com.wanshifu.order.offer.domains.vo.DirectAppointOrderMessage;
import com.wanshifu.order.offer.domains.vo.OrderCanceledAutoGrabMessage;
import com.wanshifu.order.offer.domains.vo.appointed.CancelMasterMessage;
import com.wanshifu.order.offer.domains.vo.publish.ExclusiveOrderTransferMessage;
import com.wanshifu.order.offer.domains.vo.publish.OrderPackageConfirmMessage;
import com.wanshifu.order.offer.domains.vo.push.OrderPushNotices;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单匹配师傅service
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderMatchMasterServiceImpl implements OrderMatchMasterService {


    @Resource
    private OrderDataBuilder orderDataBuilder;


    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;


    @Resource
    private PushRecordService pushRecordService;


    @Value("${is.intelligent.push:true}")
    private boolean isIntelligentPush;


    @Resource
    private Tools tools;


    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private AgreementMasterPushRepository agreementMasterPushRepository;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private MasterPushRepository masterPushRepository;

    @Resource
    private InfoQueryApi infoQueryApi;

    @Resource
    private BindingTechnologyCommon bindingTechnologyCommon;

    @Resource
    private OrderMasterPushRepository orderMasterPushRepository;

    @Resource
    private OrderRoutingStrategyRepository orderRoutingStrategyRepository;

    @Resource
    private OrderMatchRoutingRepository orderMatchRoutingRepository;

    @Resource
    private NewModelMatchDetailRepository newModelMatchDetailRepository;

    @Resource
    private CooperationBusinessMasterMatcher cooperationBusinessMasterMatcher;

    @Resource
    private AgreementMasterMatcher agreementMasterMatcher;


    @Resource
    private AddressCommon addressCommon;


    /**
     * 推单结果topic
     */
    @Value("${wanshifu.rocketMQ.order-push-result-topic}")
    private String orderPushResultTopic;

    public final String PACKAGE_ORDER_PUSH_TAG = "package_order_push";


    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String orderMatchMasterTopic;


    @Value("${new.model.push.switch:on}")
    private String newModelPushSwitch;


    /**
     * 家庭app推C端师傅城市开放配置
     */
    @Value("${push.toc.cityList:}")
    private String pushTocCityList;

    @Resource
    private PortPushService portPushService;

    @Resource
    private AgreementMasterMatchRepository agreementMasterMatchRepository;

    @Resource
    private OrderConfigCommon orderConfigCommon;

    @Resource
    private HBaseClient hBaseClient;


    @Override
    public int match(OrderMatchMasterRqt rqt){


        OrderDetailData orderDetailData = orderDataBuilder.build(rqt);
        if(!Objects.nonNull(orderDetailData)){
            return 0;
        }
        MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);

        //处理合作经营重复订单逻辑
        boolean isEndPush = handleCooperationBusinessRepeatOrderPush(orderDetailData, masterMatchCondition, rqt);
        if (isEndPush) {
            return 1;
        }

        if("on".equals(newModelPushSwitch) && CollectionUtils.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("new_model")){
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NEW_MODEL_MASTER);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

            return 1;
        }


        OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.DIRECT_APPOINT_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.FULL_TIME_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.TECHNIQUE_VERIFY_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }

        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.COOPERATION_BUSINESS_MASTER);
        if (orderMasterMatcher.executeMatch(orderDetailData, masterMatchCondition)) {
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.COLLECT_CONTRACT_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }

        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ORDER_PACKAGE_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.AGENT_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }

        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.SPECIAL_GROUP);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.FAMILY_AGREEMENT_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.AGREEMENT_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }

        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.AFTER_TECHNIQUE_VERIFY_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.BRAND_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }


        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.EXCLUSIVE_MASTER);
        if(orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition)){
            return 1;
        }

        log.info(String.format("未匹配到师傅，开启策略推单,rqt:%s", JSON.toJSONString(rqt)));
        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
        orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);



        return 1;
    }

    @Override
    public void agentPush(AgentPushRqt rqt) {
        if (Objects.isNull(rqt)) {
            return;
        }
        Integer appointType = rqt.getAppointType();

        String touchType = rqt.getTouchType();

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(rqt.getMasterOrderId());
        if (Objects.isNull(orderGrabByIdResp)
                || Objects.isNull(orderGrabByIdResp.getOrderGrab())
                || Objects.isNull(orderGrabByIdResp.getOrderBase())) {
            log.error("agentPush select orderGrab exception! agentPushRqt:{}", JSONUtil.toJsonStr(rqt));
            return;
        }


        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        boolean isFixedPrice = appointType == 4 || appointType == 5;
        if (!isFixedPrice) {
            //报价
            if ("push".equals(touchType)) {
                //推单后
                Integer confirmServeStatus = orderGrab.getConfirmServeStatus();
                if (Objects.nonNull(confirmServeStatus) && confirmServeStatus == 1) {
                    //已指派
                    return;
                }
                log.info("agentPush touchType:{},then pushNormalMaster,orderId:{},appointType:{}", touchType, orderBase.getOrderId(), appointType);
                this.handleAgentPushNormal(orderBase);
            } else {
                //首次查看后
                Integer offerNumber = orderGrab.getOfferNumber();
                if (Objects.nonNull(offerNumber) && offerNumber > 0) {
                    //已有报价
                    return;
                }
                log.info("agentPush touchType:{},then pushNormalMaster,orderId:{},appointType:{}", touchType, orderBase.getOrderId(), appointType);
                this.handleAgentPushNormal(orderBase);
            }
        } else {
            //一口价、预付款订单
            Long hiredMasterId = orderGrab.getHireMasterId();
            if (Objects.nonNull(hiredMasterId) && hiredMasterId > 0L) {
                //说明已抢单
                return;
            }
            log.info("agentPush touchType:{},then pushNormalMaster,orderId:{},appointType:{}", touchType, orderBase.getOrderId(), appointType);
            this.handleAgentPushNormal(orderBase);
        }


    }


    /**
     * 代理商推单后推普通师傅逻辑
     * @param orderBase
     */
    private void handleAgentPushNormal(OrderBase orderBase) {
        /*if (orderModifyTime.getTime() > pushTime.getTime()) {
            //推单时间早于订单修改时间，说明订单修改过，该条mq作废
            log.error("agentPush touchType:{},handleAgentPushNormal,orderId:{},orderModifyTime:{},pushTime:{}", touchType, orderBase.getOrderId(), orderModifyTime, pushTime);
            return;
        }*/
        //过滤已推的代理商师傅，再推普通师傅
        List<Long> orderPushEliminateMasterIds = Lists.newArrayList();
        String masterIdStr = hBaseClient.querySingle("order_push", String.valueOf(orderBase.getGlobalOrderTraceId()), "agent");
        if (!Strings.isNullOrEmpty(masterIdStr)) {
            orderPushEliminateMasterIds = Arrays.stream(masterIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }

        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(orderBase.getOrderId());
        orderMatchMasterRqt.setPushMode(PushMode.NORMAL.getCode());
        if (CollectionUtil.isNotEmpty(orderPushEliminateMasterIds)) {
            orderMatchMasterRqt.setOrderPushEliminateMasterIds(orderPushEliminateMasterIds);
        }
        log.info("handleAgentPushNormal ,orderId:{},orderMatchMasterRqt:{}", orderBase.getOrderId(), JSON.toJSONString(orderMatchMasterRqt));
        this.match(orderMatchMasterRqt);
    }

    /**
     * 处理合作经营重复订单逻辑
     * @param orderDetailData
     * @param masterMatchCondition
     * @param rqt
     * @return true:结束推单逻辑，false：继续推单逻辑
     */
    public boolean handleCooperationBusinessRepeatOrderPush(OrderDetailData orderDetailData,
                                                            MasterMatchCondition masterMatchCondition,
                                                            OrderMatchMasterRqt rqt) {
        if (CollectionUtils.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("definite_repeat")) {
            //definite_repeat：当前订单为一口价订单，且识别到已有重复订单的标签

            String matchFailReason = "当前一口价订单存在重复订单过滤！";
            cooperationBusinessMasterMatcher.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());

            agreementMasterMatcher.insertAgreementMasterMatch(orderDetailData, matchFailReason);

            log.info(String.format("识别到当前一口价订单为重复订单，直接走普通推单体系,rqt:%s", JSON.toJSONString(rqt)));
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
            orderMasterMatcher.executeMatch(orderDetailData, masterMatchCondition);
            return true;
        } else if (CollectionUtils.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("offer_repeat_and_grab")) {
            //offer_repeat_and_grab：当前订单为报价订单，且识别到已有重复订单且重复对象订单已被合作经营抢单或协议师傅自动抢单的标签
            String matchFailReason = "当前报价订单重复且重复对象订单已被合作经营抢单或协议师傅自动抢单！";

            log.info(String.format("当前报价订单重复且重复对象订单已被合作经营抢单或协议师傅自动抢单，不再推单！,rqt:%s", JSON.toJSONString(rqt)));
            agreementMasterMatcher.insertAgreementMasterMatch(orderDetailData, matchFailReason);
            return true;
        }
        return false;
    }



    @Override
    public void matchOrderPackageMaster(OrderPushRqt orderPushRqt){
        Long timeStamp = System.currentTimeMillis();
        String orderVersion = String.valueOf(timeStamp);
        //获取订单详细数据
        OrderDetailData orderDetailData = new OrderDetailData(orderPushRqt,orderVersion);
        orderDetailData.getPushExtraData().setOperateFlag(FieldConstant.CONFIRM_ORDER_PACKAGE);
        MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
        OrderMasterMatcher orderMasterMatcher;
        orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ORDER_PACKAGE_MASTER);
        orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
    }



    @Override
    public void orderRefundSync(RefundSyncRqt rqt){
        pushRecordService.saveOrderRefund(rqt.getUserId(),rqt.getMasterId(),rqt.getServeType(),rqt.getRefundTime(),rqt.getOrderId());
    }

    @Override
        public void notAppointOrderScheduledTask(OrderMatchMasterRqt rqt) {
        //判断是否指派
        if (isAppointed(rqt.getMasterOrderId())) {
            return;
        }

        //推普通师傅
        match(rqt);
    }

    /**
     * 是否指派
     * @param masterOrderId
     * @return
     */
    private boolean isAppointed(Long masterOrderId){
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(masterOrderId);
        final OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        if (1==orderGrab.getConfirmServeStatus()){
            return true;
        }
        return false;
    }

    /**
     * 创建订单，修改订单
     * @param orderPushNotices
     * @return
     */
    @Override
    public int orderPushNotices(OrderPushNotices orderPushNotices){

        OrderPushNotices.OrderPushInfo orderPushInfo = orderPushNotices.getOrderPushInfo();
        if(Objects.isNull(orderPushInfo)){
            return 0;
        }

        String masterTransferOrderType = orderPushNotices.getMasterTransferOrderType();
        if("interfere_tob".equals(masterTransferOrderType)){
            //B端一口价订单转单（当前用于合作经营）
            OrderBase orderBase = orderPushInfo.getFinalProductOrderInfo().getOrderBase();
            Long orderId = orderBase.getOrderId();
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderId);
            rqt.setPushMode(PushMode.NORMAL.code);
            rqt.setOrderPushEliminateMasterIds(Collections.singletonList(orderPushNotices.getMasterId()));
            this.match(rqt);
            return 1;
        }


        Long masterOrderId = null;
        Integer businessLineId = 1;
        if(Objects.nonNull(orderPushInfo.getFinalProductOrderInfo())){
            OrderBase orderBase = orderPushInfo.getFinalProductOrderInfo().getOrderBase();
            masterOrderId = orderBase.getOrderId();
            businessLineId = orderBase.getBusinessLineId();
            OrderGrab orderGrab = orderPushInfo.getFinalProductOrderInfo().getOrderGrab();
            if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType()) && Objects.isNull(orderGrab)){
                return 0;
            }
            if(AccountType.ENTERPRISE.code.equals(orderGrab.getAccountType()) && AppointType.NORMAL.value.equals(orderGrab.getAppointType())){
                log.info(String.format("总包直接指派订单无需推单,masterOrderId:%d",masterOrderId));
                return 0;
            }
        }else if(Objects.nonNull(orderPushInfo.getIkeaOrderInfo())){
            masterOrderId = orderPushInfo.getIkeaOrderInfo().getOrderBase().getOrderId();
            businessLineId = orderPushInfo.getIkeaOrderInfo().getOrderBase().getBusinessLineId();
        }else if(Objects.nonNull(orderPushInfo.getInnovateOrderInfo())){
            masterOrderId = orderPushInfo.getInnovateOrderInfo().getInfoOrderBase().getOrderId();
            businessLineId = orderPushInfo.getInnovateOrderInfo().getInfoOrderBase().getBusinessLineId();
        }

        OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
        rqt.setMasterOrderId(masterOrderId);
        rqt.setBusinessLineId(businessLineId);
        rqt.setHandoffTag(orderPushNotices.getHandoffTag());
        rqt.setMasterAutoOfferPriceInfoList(orderPushNotices.getMasterAutoOfferPriceInfoList());
        rqt.setMatchSceneCode(MatchSceneCode.ORDER_CREATE.getCode());

        if(isIntelligentPush){
            this.match(rqt);
        }else{
            this.nonIntelligentScreeMaster(rqt);
        }
        return 1;
    }


    /**
     * 总包直接指派匹配订单包
     * @param orderPackageConfirmMessage
     */
    @Override
    public void orderPackageConfirm(OrderPackageConfirmMessage orderPackageConfirmMessage){

        //获取订单详细数据
        OrderDetailData orderDetailData = orderDataBuilder.build(orderPackageConfirmMessage.getOrderBase(),orderPackageConfirmMessage.getOrderGrab(),
                orderPackageConfirmMessage.getOrderExtraData(),orderPackageConfirmMessage.getOrderServiceAttributeInfos(),orderPackageConfirmMessage.getOrderGoodsList());
        orderDetailData.getPushExtraData().setOperateFlag(FieldConstant.CONFIRM_ORDER_PACKAGE);
        orderDetailData.getPushExtraData().setHandoffTag(orderPackageConfirmMessage.getHandoffTag());
        MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
        OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ORDER_PACKAGE_MASTER);
        boolean result = orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);


        if(!result){
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("globalOrderTraceId",orderDetailData.getGlobalOrderId());
            resultMap.put("pushNumber",0);
            final String pushTime = DateFormatterUtil.getNowSp();
            resultMap.put("pushTime",pushTime);
            resultMap.put("firstPush",0);
            resultMap.put("businessLineId",orderDetailData.getBusinessLineId());
            pushQueueService.sendScheduledPushMessage(1L,JSON.toJSONString(resultMap),orderPushResultTopic,PACKAGE_ORDER_PUSH_TAG);
        }
    }


    /**
     * 特殊单转普通单（专属转单，品牌转单，代理商转单）
     * @param exclusiveOrderTransferMessage
     */
    @Override
    public void exclusiveOrderTransfer(ExclusiveOrderTransferMessage exclusiveOrderTransferMessage){

        Long globalOrderId = exclusiveOrderTransferMessage.getGlobalOrderTraceId();
        GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
        getOrderIdRqt.setGlobalOrderTraceId(globalOrderId);
        Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);
        OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
        rqt.setPushMode(PushMode.NORMAL.code);
        rqt.setMasterOrderId(orderId);
        rqt.setHandoffTag(exclusiveOrderTransferMessage.getHandoffTag());
        this.match(rqt);
    }


    /**
     * 订单取消指派师傅（用户一口价订单，家庭代理商订单）
     * @param cancelMasterMessage
     */
    @Override
    public void orderCancelMaster(CancelMasterMessage cancelMasterMessage){
        final String tagName = cancelMasterMessage.getTagName();
        String accountType = cancelMasterMessage.getAccountType();
        Integer appointType = cancelMasterMessage.getAppointType();
        Integer businessLineId = cancelMasterMessage.getBusinessLineId();

        if(!cancelMasterMessage.getIsRefreshPushOrder()){
            return ;
        }

        //家庭合作商改派后,需要重新推单,并且推送普通师傅
        if (FieldConstant.AGENT.equals(tagName)) {
            Long globalOrderId = cancelMasterMessage.getGlobalOrderTraceId();
            GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
            getOrderIdRqt.setGlobalOrderTraceId(globalOrderId);
            Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setRePushSource(RePushSource.orderCancelMaster.toString());
            rqt.setMasterOrderId(orderId);
            rqt.setTagName(tagName);
            rqt.setHandoffTag(cancelMasterMessage.getHandoffTag());
            rqt.setOrderPushEliminateMasterIds(Collections.singletonList(cancelMasterMessage.getCancelMasterId()));
            this.match(rqt);

            return;
        }


        //家庭合作商改派后,需要重新推单,并且推送普通师傅
        if ("sample_plate_appoint".equals(tagName)) {

            Long globalOrderId = cancelMasterMessage.getGlobalOrderTraceId();
            GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
            getOrderIdRqt.setGlobalOrderTraceId(globalOrderId);
            Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);

            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NEW_MODEL_MASTER);
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderId);
            orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.MAIN_MASTER_REFUSED_ORDER.getCode());
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

            return;
        }

        if((businessLineId == 1 && AccountType.USER.code.equals(accountType) && AppointType.DEFINITE_PRICE.value.equals(appointType)) ||
                (AccountType.USER.code.equals(accountType) && businessLineId == 2 && (AppointType.ADVANCE_PAY.value.equals(appointType) || AppointType.DEFINITE_PRICE.value.equals(appointType)))){
            Long globalOrderId = cancelMasterMessage.getGlobalOrderTraceId();
            GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
            getOrderIdRqt.setGlobalOrderTraceId(globalOrderId);
            Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setPushMode(PushMode.NORMAL.code);
            rqt.setMasterOrderId(orderId);
            rqt.setHandoffTag(cancelMasterMessage.getHandoffTag());
            rqt.setOrderPushEliminateMasterIds(Collections.singletonList(cancelMasterMessage.getCancelMasterId()));
            this.match(rqt);
        }


    }




    /**
     * 非智能推单（开发环境）
     */
    private void nonIntelligentScreeMaster(OrderMatchMasterRqt rqt){

        OrderDetailData orderDetailData = orderDataBuilder.build(rqt);
        if(!Objects.nonNull(orderDetailData)){
            return ;
        }

        MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
        OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NON_INTELLIGENT);
        orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

    }

    @Override
    public void orderMatchRoute(OrderMatchRouteMessage message){
        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(message.getMasterOrderId()));
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        if(Objects.isNull(orderGrab) || orderGrab.getIsDelete() == 1){
            return ;
        }
        OrderMatchRouteTime orderMatchRouteTime  = message.getOrderMatchRouteTime();
        Integer num = 0;
        if("appointTime".equals(orderMatchRouteTime.getSettingType())){
            num = (orderGrab.getHireMasterId() > 0 && orderGrab.getConfirmServeStatus() == 1) ? 1 : 0;
        }else if("receiveTime".equals(orderMatchRouteTime.getSettingType())){
            num = orderGrab.getOfferNumber();
        }
        if(num < orderMatchRouteTime.getSettingNum()){
            //满足条件推送普通师傅
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(message.getMasterOrderId());
            orderMatchMasterRqt.setHandoffTag("new");
            orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
            this.match(orderMatchMasterRqt);
        }
    }



    @Override
    public void orderCancelAutoGrab(OrderCanceledAutoGrabMessage orderCanceledAutoGrabMessage){

        Long orderId = orderCanceledAutoGrabMessage.getOrderId();
        OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
        rqt.setPushMode(PushMode.NORMAL.code);
        rqt.setMasterOrderId(orderId);
        rqt.setHandoffTag("new");
        rqt.setOrderPushEliminateMasterIds(Collections.singletonList(orderCanceledAutoGrabMessage.getReleaseNeedFilterMasterId()));

        Long masterId = orderCanceledAutoGrabMessage.getMasterId();

        this.match(rqt);

        updateNewModelDistributeResult(orderId,masterId,orderCanceledAutoGrabMessage);

    }


    public void updateNewModelDistributeResult(Long orderId,Long masterId,OrderCanceledAutoGrabMessage orderCanceledAutoGrabMessage){
        NewModelMatchDetail newModelMatchDetail = newModelMatchDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
        if(Objects.nonNull(newModelMatchDetail)){
            NewModelMatchDetail matchDetail = new NewModelMatchDetail();
            matchDetail.setId(newModelMatchDetail.getId());
            matchDetail.setDistributeResult(0);
            matchDetail.setIsDistribute(null);
            matchDetail.setDistributeRemark(null);
            String distributeFailRemark = "";
            String cancelType = orderCanceledAutoGrabMessage.getCancelType();
            if(CancelAutoGrabType.MASTER_MANUAL_CANCEL.getCode().equals(cancelType)){
                distributeFailRemark = CancelAutoGrabType.MASTER_MANUAL_CANCEL.getDesc();
                matchDetail.setAbandonTime(new Date());
            }else if(CancelAutoGrabType.SYSTEM_AUTO_CANCEL.getCode().equals(cancelType)){
                distributeFailRemark = CancelAutoGrabType.SYSTEM_AUTO_CANCEL.getDesc();
                matchDetail.setAbandonTime(new Date());
            }else if(CancelAutoGrabType.MASTER_MANUAL_CONFIRM_ERROR.getCode().equals(cancelType)){
                distributeFailRemark = CancelAutoGrabType.MASTER_MANUAL_CONFIRM_ERROR.getDesc();
                matchDetail.setAbandonTime(new Date());
            }else if(CancelAutoGrabType.BACKEND_APPOINT.getCode().equals(cancelType)){
            distributeFailRemark = CancelAutoGrabType.BACKEND_APPOINT.getDesc();
            matchDetail.setAbandonTime(new Date());
            }else{
                distributeFailRemark = "其他（订单修改或关闭等）";
            }
            matchDetail.setDistributeFailRemark(distributeFailRemark);
            newModelMatchDetailRepository.updateByPrimaryKeySelective(matchDetail);
        }
    }



    @Override
    public void distributeResultNotices(DistributeResultNotices distributeResultNotices){

        String pushMode = distributeResultNotices.getPushMode();
        Long orderId = distributeResultNotices.getOrderId();
        Integer distributeResult = distributeResultNotices.getDistributeResult();


        if("new_model_single".equals(pushMode) || "afresh_new_model_single".equals(pushMode)){
            handleNewModelOrderDistributeNotice(distributeResultNotices);
            return ;
        }




        if(distributeResult == 0){
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderId);
            rqt.setOrderPushEliminateMasterIds(Collections.singletonList(distributeResultNotices.getMasterInfoList().get(0).getMasterId()));
            this.match(rqt);

            if("agreement_master".equals(pushMode)){
                Set<DistributeResultNotices.MasterInfo> masterInfoList = distributeResultNotices.getMasterInfoList().stream().filter(masterInfo -> masterInfo.getDistributeResult() == 0).collect(Collectors.toSet());
                if(CollectionUtils.isNotEmpty(masterInfoList)){
                    masterInfoList.forEach(masterInfo -> {
                        this.updateAgreementMasterMatch(orderId,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()),masterInfo.getDistributeResult(),masterInfo.getDistributeResultRemark());
                    });
                }
            }
            return ;
        }

        if(!"agreement_master".equals(pushMode)){
            return ;
        }

        OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(distributeResultNotices.getOrderId(),null);
        OrderBase orderBase = orderBaseComposite.getOrderBase();
        OrderGrab orderGrab = orderBaseComposite.getOrderGrab();

        Integer businessLineId = orderBase.getBusinessLineId();
        Long secondDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());

        //推单师傅类型
        String masterSourceType = getMasterSourceType(businessLineId, secondDivisionId);
        if (MasterSourceType.TOC.code.equals(masterSourceType)) {
            //C端协议师傅推单后匹配端口规则
            OrderDetailData orderDetailData = new OrderDetailData();
            orderDetailData.setMasterOrderId(orderBase.getOrderId());
            orderDetailData.setSecondDivisionId(secondDivisionId);
            orderDetailData.setLv1ServeIds(orderBase.getServeLevel1Ids());
            orderDetailData.setAppointType(orderGrab.getAppointType());
            portPushService.matchPushPortRule("agreement_master",orderDetailData,0);
        }

        Set<DistributeResultNotices.MasterInfo> masterInfoList = distributeResultNotices.getMasterInfoList().stream().filter(masterInfo -> masterInfo.getDistributeResult() == 1).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(masterInfoList)){
            return ;
        }


        String recruitId = distributeResultNotices.getMasterInfoList().get(0).getRecruitId();
        Long masterId = distributeResultNotices.getMasterInfoList().get(0).getMasterId();


        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("recruitId",Long.valueOf(recruitId)));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId",Long.valueOf(masterId)));
        EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder);

        AgreementMaster agreementMaster = esResponse.getDataList().get(0);
        Integer hasCooperationUser = StringUtils.isNotBlank(agreementMaster.getUserIds()) ? 1 : 0;

        Integer hasPrice =  !"noCooperationPrice".equals(agreementMaster.getPricingType()) ? 1 : 0;


        String today = DateFormatterUtil.getNow();
        masterInfoList.forEach(masterInfo -> {
            this.updateAgreementMasterPush(today,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()));
        });



       if(CollectionUtils.isNotEmpty( distributeResultNotices.getMasterInfoList())){
           distributeResultNotices.getMasterInfoList().forEach(masterInfo -> {
               this.updateAgreementMasterMatch(orderId,masterInfo.getMasterId(),Long.valueOf(masterInfo.getRecruitId()),masterInfo.getDistributeResult(),masterInfo.getDistributeResultRemark());
           });
       }


        //TODO 如何获取有无价格，有无合作商家
        List<CompensateDistribute> compensateDistributeList = getCompensateDistributeList(masterSourceType, orderBase.getBusinessLineId(), Long.valueOf(orderBase.getCategoryId()), orderGrab.getAppointType(), hasPrice, hasCooperationUser);

        if(CollectionUtils.isEmpty(compensateDistributeList)){
            log.error("compensateDistribute error,distributeResultNotices:" + JSON.toJSONString(distributeResultNotices));
            return ;
        }

        compensateDistributeList.forEach(compensateDistribute -> {
            CompensateDistributeMessage message = new CompensateDistributeMessage();
            message.setOrderId(orderBase.getOrderId());
            message.setCompensateDistribute(compensateDistribute);
            int intervalTime = compensateDistribute.getIntervalTime();
            long delayTime;
            if(intervalTime == 0){
                delayTime = 5L;
            }else{
                delayTime = intervalTime * 60 * 1000L;
            }
            pushQueueService.sendCompensateDistributeMessage(message,delayTime);
        });



    }

    private String getMasterSourceType(Integer businessLineId, Long secondDivisionId) {

        final List<String> toCMasterCityList =
                new ArrayList<>(Arrays.asList(pushTocCityList.split(",")));

        if (businessLineId == 2
                && (toCMasterCityList.contains(secondDivisionId.toString()) || toCMasterCityList.contains("all"))) {
            return MasterSourceType.TOC.code;
        }else {
            return MasterSourceType.TOB.code;
        }
    }

    private List<String> getOrderTag(Long orderId){
        try{
            List<OrderExclusiveTagResp> orderExclusiveTagRespList = tools.catchLog(() -> normalOrderResourceApi.getOrderExclusiveTag(orderId,null),"normalOrderResourceApi.getOrderExclusiveTag",orderId);
            if(CollectionUtils.isNotEmpty(orderExclusiveTagRespList)){
                return orderExclusiveTagRespList.stream().map(OrderExclusiveTagResp::getTagName).collect(Collectors.toList());
            }
        }catch(Exception e){
            log.error("getOrderTag error" , e);
        }
        return new ArrayList<>();
    }


//    /**
//     * 匹配端口推送规则
//     * @param orderId
//     * @param orderTags
//     * @param secondDivisionId
//     * @param lv1ServeIds
//     * @param appointType
//     */
//    private void matchPushPortRule(Long orderId, List<String> orderTags, Long secondDivisionId,
//                                   String lv1ServeIds, Integer appointType) {
//        // TODO orderTags.get(0)?
//        PushPortRule pushPortRule = pushPortRuleRepository.selectByCityAndLv1ServeId(orderTags, Collections.singletonList(String.valueOf(secondDivisionId)),
//                Collections.singletonList(lv1ServeIds), appointType, null);
//        if (Objects.isNull(pushPortRule) || pushPortRule.getIsDelete() == 1 || pushPortRule.getRuleStatus() == 0) {
//            return;
//        }
//
//        OrderDetailData orderDetailData = new OrderDetailData();
//        orderDetailData.setMasterOrderId(orderId);
//
//        PortPushMessage message = new PortPushMessage();
//        message.setOrderDetailData(orderDetailData);
//        message.setPushPortRule(pushPortRule);
//        Long delayTime = pushPortRule.getIntervalTime() == 0 ? 2 * 60 * 1000L : pushPortRule.getIntervalTime() * 60 * 1000L;
//        pushQueueService.sendPortPushMessage(message, delayTime);
//    }

    private void handleNewModelOrderDistributeNotice(DistributeResultNotices distributeResultNotices){
        Integer distributeResult = distributeResultNotices.getDistributeResult();
        if(distributeResult == 0){

            String pushMode = distributeResultNotices.getPushMode();
            if("new_model_single".equals(pushMode)){
                OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NEW_MODEL_MASTER);
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setBusinessLineId(distributeResultNotices.getBusinessLineId());
                orderMatchMasterRqt.setMasterOrderId(distributeResultNotices.getOrderId());
                OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
                MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
                orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
            }



            Long orderId = distributeResultNotices.getOrderId();
            Long masterId = distributeResultNotices.getMasterInfoList().get(0).getMasterId();
            String distributeResultRemark = distributeResultNotices.getMasterInfoList().get(0).getDistributeResultRemark();

            NewModelMatchDetail newModelMatchDetail = newModelMatchDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
            if(Objects.nonNull(newModelMatchDetail)){
                NewModelMatchDetail matchDetail = new NewModelMatchDetail();
                matchDetail.setId(newModelMatchDetail.getId());
                matchDetail.setDistributeResult(0);
                matchDetail.setDistributeFailRemark(distributeResultRemark);
                matchDetail.setIsDistribute(null);
                matchDetail.setDistributeRemark(null);
                newModelMatchDetailRepository.updateByPrimaryKeySelective(matchDetail);
            }


        }else{

            Long orderId = distributeResultNotices.getOrderId();
            Long masterId = distributeResultNotices.getMasterInfoList().get(0).getMasterId();

            NewModelMatchDetail newModelMatchDetail = newModelMatchDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
            if(Objects.nonNull(newModelMatchDetail)){
                NewModelMatchDetail matchDetail = new NewModelMatchDetail();
                matchDetail.setId(newModelMatchDetail.getId());
                matchDetail.setDistributeResult(1);
                matchDetail.setIsDistribute(null);
                matchDetail.setDistributeRemark(null);
                newModelMatchDetailRepository.updateByPrimaryKeySelective(matchDetail);
            }

        }

    }


    private OrderRoutingStrategy getOrderRoutingStrategy(String orderTag, OrderDetailData orderDetailData) {
        OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectRoutingStrategy(orderTag,Collections.singletonList(String.valueOf(orderDetailData.getSecondDivisionId())) ,Collections.singletonList(String.valueOf(orderDetailData.getOrderCategoryId())),orderDetailData.getBusinessLineId() );

        if (orderRoutingStrategy == null) {
            //具体城市+不限类目
            orderRoutingStrategy = orderRoutingStrategyRepository.selectRoutingStrategy(orderTag,Collections.singletonList(String.valueOf(orderDetailData.getSecondDivisionId())) ,Collections.singletonList("all"),orderDetailData.getBusinessLineId());
        }

        if (orderRoutingStrategy == null) {
            //全国城市+具体类目
            orderRoutingStrategy = orderRoutingStrategyRepository.selectRoutingStrategy(orderTag,Collections.singletonList("all") ,Collections.singletonList(String.valueOf(orderDetailData.getOrderCategoryId())),orderDetailData.getBusinessLineId());
        }

        if (orderRoutingStrategy == null) {
            //全国城市+不限类目
            orderRoutingStrategy = orderRoutingStrategyRepository.selectRoutingStrategy(orderTag,Collections.singletonList("all") ,Collections.singletonList("all"),orderDetailData.getBusinessLineId());
        }
        return orderRoutingStrategy;
    }


    private List<CompensateDistribute> getCompensateDistributeList(String masterSourceType, Integer businessLineId, Long categoryId, Integer appointType, Integer hasPrice, Integer hasCooperationUser) {

        if (businessLineId == 2 && MasterSourceType.TOC.code.equals(masterSourceType)) {
            businessLineId = 999;
        }

        List<CompensateDistribute> compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, hasPrice, hasCooperationUser);

        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, 2, hasCooperationUser);
        }


        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, hasPrice, 2);
        }

        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId, "agreement", categoryId, appointType, 2, 2);
        }

        return compensateDistributeList;

    }



    private void updateAgreementMasterPush(String today,Long masterId,Long recruitId){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterPush agreementMasterPush = agreementMasterPushRepository.selectByAgreementMasterId(agreementMasterId);

        if(Objects.isNull(agreementMasterPush)){
            agreementMasterPush = new AgreementMasterPush();
            agreementMasterPush.setAgreementMasterId(recruitId + ":" + masterId);
            agreementMasterPush.setMasterId(masterId);
            agreementMasterPush.setRecruitId(recruitId);
            agreementMasterPush.setDt(today);
            agreementMasterPush.setPushCountDaily(1L);
            agreementMasterPushRepository.insertSelective(agreementMasterPush);
            return ;
        }


        final String dt = agreementMasterPush.getDt();
        AgreementMasterPush updateAgreementMasterPush = new AgreementMasterPush();
        updateAgreementMasterPush.setId(agreementMasterPush.getId());
        if (org.apache.commons.lang.StringUtils.isEmpty(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else if (!today.equals(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else {
            updateAgreementMasterPush.setPushCountDaily(agreementMasterPush.getPushCountDaily() + 1L);
        }
        agreementMasterPushRepository.updateByPrimaryKeySelective(updateAgreementMasterPush);
    }


    private void updateAgreementMasterMatch(Long orderId,Long masterId,Long recruitId,Integer isDistributeSucc,String distributeFailReason){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterMatch agreementMasterMatch = agreementMasterMatchRepository.selectByAgreementMasterId(orderId,agreementMasterId);

        if(Objects.isNull(agreementMasterMatch)){
            return ;
        }

        AgreementMasterMatch updateAgreementMasterMatch = new AgreementMasterMatch();
        updateAgreementMasterMatch.setId(agreementMasterMatch.getId());
        updateAgreementMasterMatch.setIsAutoReceiveSucc(isDistributeSucc);
        updateAgreementMasterMatch.setAutoReceiveFailReason(distributeFailReason);
        agreementMasterMatchRepository.updateByPrimaryKeySelective(updateAgreementMasterMatch);
    }



    @Override
    public void compensateDistribute(CompensateDistributeMessage distributeMessage){
        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(distributeMessage.getOrderId()));
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        if(Objects.isNull(orderGrab) || orderGrab.getIsDelete() == 1){
            return ;
        }
        CompensateDistribute compensateDistribute  = distributeMessage.getCompensateDistribute();
        Integer num = 0;
        if(CompensateType.NONE_APPOINT.getCode().equals(compensateDistribute.getCompensateType())){
            num = (orderGrab.getHireMasterId() > 0 && orderGrab.getConfirmServeStatus() == 1) ? 1 : 0;
        }else if(CompensateType.NONE_RECEIVE.getCode().equals(compensateDistribute.getCompensateType())){
            num = orderGrab.getOfferNumber();
        }
        if(num < compensateDistribute.getTriggerNum()){

            Integer orderRoutingStrategyId = distributeMessage.getOrderRoutingStrategyId();

            String masterType = "normal";
            if(Objects.nonNull(orderRoutingStrategyId)){
                OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectByPrimaryKey(orderRoutingStrategyId);
                OrderMatchRouting orderMatchRouting = orderMatchRoutingRepository.selectByPrimaryKey( orderRoutingStrategy.getMatchRoutingId());
                masterType = orderMatchRouting.getLv1MasterType();
            }

            //满足条件推送普通师傅
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderGrab.getOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            orderDetailData.getPushExtraData().setPushMode(distributeMessage.getPushMode());
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.asCode(masterType));
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }

    }

    @Override
    public GetOrderPushScoreResp getOrderPushScore(GetOrderPushScoreRqt rqt){
        Long orderId = rqt.getOrderId();
         String orderVersion = pushProgressRepository.getOrderLatestVersion(orderId);
         if(StringUtils.isNotBlank(orderVersion)){
             List<MasterPush> masterPushList = tools.catchLog(() -> masterPushRepository.selectPushScore(orderId,orderVersion,rqt.getMasterIdList()));
             if(CollectionUtils.isNotEmpty(masterPushList)){
                 GetOrderPushScoreResp resp = new GetOrderPushScoreResp();
                 resp.setOrderId(orderId);
                 List<GetOrderPushScoreResp.PushScore> pushScoreList = new ArrayList<>();
                 masterPushList.forEach(masterPush -> {
                     GetOrderPushScoreResp.PushScore pushScore = new GetOrderPushScoreResp.PushScore();
                     pushScore.setMasterId(masterPush.getMasterId());
                     pushScore.setScore(masterPush.getScore());
                     pushScoreList.add(pushScore);
                 });
                 resp.setPushScoreList(pushScoreList);
                 return resp;
             }else{
                 List<OrderMasterPush> orderMasterPushList = tools.catchNoLog(() -> orderMasterPushRepository.selectPushScore(orderId,orderVersion,rqt.getMasterIdList()));
                 if(CollectionUtils.isNotEmpty(orderMasterPushList)){
                     GetOrderPushScoreResp resp = new GetOrderPushScoreResp();
                     resp.setOrderId(orderId);
                     List<GetOrderPushScoreResp.PushScore> pushScoreList = new ArrayList<>();
                     orderMasterPushList.forEach(orderMasterPush -> {
                         GetOrderPushScoreResp.PushScore pushScore = new GetOrderPushScoreResp.PushScore();
                         pushScore.setMasterId(Long.parseLong(orderMasterPush.getMasterId()));
                         pushScore.setScore(orderMasterPush.getScore());
                         pushScoreList.add(pushScore);
                     });
                     resp.setPushScoreList(pushScoreList);
                     return resp;
                 }
             }

         }
         return null;
    }


    private final static String SUPPORT_MASTER_MATCH = "support_master_match";
    public boolean sendSupportMasterMatchMessage(Long globalOrderId,Long delayTime){
        Map<String,Long> resultMap = new HashMap<>();
        resultMap.put("globalOrderTraceId",globalOrderId);
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,SUPPORT_MASTER_MATCH,JSON.toJSONString(resultMap),delayTime);
        return true;
    }


    @Override
    public void matchSupportMaster(MatchSupportMasterRqt rqt){
        Long globalOrderId = rqt.getGlobalOrderTraceId();
        GetOrderByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderByGlobalIdRqt();
        getOrderByGlobalIdRqt.setGlobalOrderTraceId(rqt.getGlobalOrderTraceId());

        GetOrderBaseByGlobalIdRqt getOrderBaseByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
        getOrderBaseByGlobalIdRqt.setGlobalOrderTraceId(rqt.getGlobalOrderTraceId());
        GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp= tools.catchLogThrow(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderBaseByGlobalIdRqt));

        if(Objects.nonNull(getOrderBaseByGlobalIdRsp) && Objects.nonNull(getOrderBaseByGlobalIdRsp.getOrderBase())){
            com.wanshifu.enterprise.order.domain.po.OrderBase orderBase = getOrderBaseByGlobalIdRsp.getOrderBase();
            OrderDetailData orderDetailData = new OrderDetailData();
            orderDetailData.setGlobalOrderId(globalOrderId);
            orderDetailData.setLv3ServeIds(orderBase.getServeIds());
            orderDetailData.setFourthDivisionId(orderBase.getFourthDivisionId());
            Long timeStamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timeStamp);
            orderDetailData.setOrderVersion(orderVersion);
            Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());


            OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectStrategy("new_master_support",String.valueOf(cityDivisionId),String.valueOf(orderBase.getCategoryId()),(orderBase.getBussinessId().intValue()));

            if(Objects.isNull(orderRoutingStrategy)){
                orderRoutingStrategy = orderRoutingStrategyRepository.selectStrategy("new_master_support","all",String.valueOf(orderBase.getCategoryId()),(orderBase.getBussinessId().intValue()));
            }
            if(Objects.isNull(orderRoutingStrategy)){
                sendSupportMasterMatchMessage(globalOrderId,1L);
                return ;
            }
            OrderMatchRouting orderMatchRouting = orderMatchRoutingRepository.selectByPrimaryKey(orderRoutingStrategy.getMatchRoutingId());
            if(Objects.isNull(orderMatchRouting) || (!"support_master".equals(orderMatchRouting.getLv1MasterType()))){
                sendSupportMasterMatchMessage(globalOrderId,1L);
                return ;
            }
            String techniqueIds = bindingTechnologyCommon.getBindingTechnology(orderBase.getServeIds(),orderBase.getBussinessId(),orderBase.getOrderFrom(),orderBase.getFromAccountType());
            orderDetailData.setOrderTechniques(techniqueIds);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.SUPPORT_MASTER);
            boolean result = orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
            if(!result){
                sendSupportMasterMatchMessage(globalOrderId,1L);
            }
        }

    }


    @Override
    public void directPush(Long masterId,Set<Long> orderIdSet){

        String orderVersion = String.valueOf(System.currentTimeMillis());


        orderIdSet.forEach(orderId ->{

            try{
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(orderId);
                orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);

                JSONObject commonFeature = new JSONObject();
                commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
                commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
                commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
                //长尾单,不更改推送参数:IS_NEARBY_PUSH
                commonFeature.put(FieldConstant.HAND_OFF_TAG, "new");
                commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH, false);

                Set<String> masterSet = Collections.singleton(String.valueOf(masterId));

                commonFeature.put(FieldConstant.ORDER_LNG_LAT, orderDetailData.getOrderLngLat());


                pushControllerFacade.directPush(orderDetailData,orderVersion,masterSet,commonFeature);
            }catch(Exception e){
                log.error("masterSettleIn push order error",e);
            }

        });

    }


    @Override
    public void matchEnterpriseAppointMaster(EnterpriseAppointMessage message) {
        Long globalOrderId = message.getGlobalOrderTraceId();
        GetOrderByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderByGlobalIdRqt();
        getOrderByGlobalIdRqt.setGlobalOrderTraceId(message.getGlobalOrderTraceId());

        GetOrderBaseByGlobalIdRqt getOrderBaseByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
        getOrderBaseByGlobalIdRqt.setGlobalOrderTraceId(message.getGlobalOrderTraceId());
        GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchLogThrow(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderBaseByGlobalIdRqt));

        if (Objects.nonNull(getOrderBaseByGlobalIdRsp) && Objects.nonNull(getOrderBaseByGlobalIdRsp.getOrderBase())) {
            com.wanshifu.enterprise.order.domain.po.OrderBase orderBase = getOrderBaseByGlobalIdRsp.getOrderBase();
            OrderDetailData orderDetailData = new OrderDetailData();
            orderDetailData.setGlobalOrderId(globalOrderId);
            orderDetailData.setBusinessLineId(orderBase.getBussinessId().intValue());
            orderDetailData.setOrderCategoryId(Long.valueOf(orderBase.getCategoryId()));
            orderDetailData.setAppointType(2);
            orderDetailData.setLv3ServeIds(orderBase.getServeIds());
            orderDetailData.setFourthDivisionId(orderBase.getFourthDivisionId());
            Long timeStamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timeStamp);
            orderDetailData.setOrderVersion(orderVersion);
            Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());
            orderDetailData.setSecondDivisionId(cityDivisionId);

            if (org.apache.commons.lang3.StringUtils.isNotBlank(orderBase.getServeIds())) {
                List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBussinessId().intValue());
                if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                    orderDetailData.setLv1ServeIds(String.valueOf(serveBaseInfoRespList.get(0).getLevel1Id()));
                    List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                        orderDetailData.setLv2ServeIds(Joiner.on(",").join(lv2ServeIdList));
                    }

                }
            }

            orderDetailData.setLv3ServeIds(orderBase.getServeIds());

            MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ENTERPRISE_APPOINT);
            orderMasterMatcher.match(orderDetailData, masterMatchCondition);



        }
    }




    @Override
    public  void confirmAppointMaster(ConfirmAppointMasterMessage message){
        //合作经营师傅更新 当日指派合作经营订单量  指标
        if (!Strings.isNullOrEmpty(message.getOrderLabel()) && message.getOrderLabel().contains("cooperative_business")) {
            pushRecordService.increaseCooperationBusinessMasterCntDaily(message.getHireMasterId(),DateFormatterUtil.getNow());
            pushRecordService.saveCooperationBusinessAppointTime(message.getHireMasterId(),message.getConfirmTime());

        }


        //全时师傅
        if (!Strings.isNullOrEmpty(message.getOrderLabel()) && message.getOrderLabel().contains("all_time_grab")) {
            pushRecordService.saveFullTimeMasterAppointTime(message.getHireMasterId(),message.getConfirmTime());

        }

        if(AccountType.ENTERPRISE.code.equals(message.getAccountType()) && "agreement_master".equals(message.getRecruitType()) && Objects.nonNull(message.getRecruitId())){
            pushRecordService.increaseMasterRecruitDaily(message.getHireMasterId(),message.getRecruitId(),DateFormatterUtil.getNow());
        }/*else if(AccountType.USER.code.equals(message.getAccountType()) && AppointType.DEFINITE_PRICE.value.equals(message.getAppointType())){
            AgreementMasterMatch agreementMasterMatch = agreementMasterMatchRepository.selectByOrderIdAndMasterId(message.getOrderId(),message.getHireMasterId());
            if(Objects.nonNull(agreementMasterMatch)){
                pushRecordService.increaseMasterRecruitDaily(message.getHireMasterId(),agreementMasterMatch.getRecruitId(),DateFormatterUtil.getNow());
            }
        }*/
        return ;
    }


    @Override
    public void orderClose(OrderCloseMessage message) {
        log.info("orderCloseMessageConsume start,message:{}", JSONUtil.toJsonStr(message));
        List<Long> globalOrderTraceIdList = message.getGlobalOrderTraceIdList();
        if (CollectionUtil.isEmpty(globalOrderTraceIdList)) {
            log.error("orderCloseMessageConsume error, globalOrderTraceIdList is empty!");
            return;
        }

        handlerCooperationBusinessOrderClose(globalOrderTraceIdList);

    }


    /**
     * 合作经营订单关单逻辑处理
     * @param globalOrderTraceIdList
     */
    private void handlerCooperationBusinessOrderClose(List<Long> globalOrderTraceIdList) {

        for (Long globalOrderTraceId : globalOrderTraceIdList) {
            SimpleOrderGrab simpleOrderGrab = normalOrderResourceApi.getSimpleOrderGrab(null, null, globalOrderTraceId);
            if (Objects.isNull(simpleOrderGrab)
                    || Objects.isNull(simpleOrderGrab.getOrderGrab())
                    || Objects.isNull(simpleOrderGrab.getOrderBase())) {
                continue;
            }

            OrderBase orderBase = simpleOrderGrab.getOrderBase();
            OrderGrab orderGrab = simpleOrderGrab.getOrderGrab();
            if (orderBase.getBusinessLineId() != 1) {
                //非用户订单，也就是非合作经营订单，不处理
                continue;
            }
            if (!AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType())) {
                //非一口价订单，也就是非合作经营订单，不处理
                continue;
            }
            if (orderGrab.getConfirmServeStatus() == 1) {
                //确认履约状态后，不处理
                continue;
            }


            GetOrderOfferPriceListByGlobalOrderIdsRqt rqt = new GetOrderOfferPriceListByGlobalOrderIdsRqt();
            rqt.setGlobalOrderTraceIds(Lists.newArrayList(globalOrderTraceId));

            GetOrderOfferPriceListByGlobalOrderIdsResp resp = offerModuleResourceApi.getOrderOfferPriceListByGlobalOrderIds(rqt);
            if (Objects.isNull(resp) || CollectionUtil.isEmpty(resp.getOrderOfferPrices())) {
                log.info("handlerCooperationBusinessOrderClose error, offerResp is null, globalOrderTraceId:{}", globalOrderTraceId);
                continue;
            }

            List<OrderOfferPrice> orderOfferPriceList = resp.getOrderOfferPrices().get(0).getOrderOfferPriceList();
            if (CollectionUtil.isEmpty(orderOfferPriceList)) {
                log.info("handlerCooperationBusinessOrderClose error, orderOfferPriceList is null, globalOrderTraceId:{}", globalOrderTraceId);
                continue;
            }

            for (OrderOfferPrice orderOfferPrice : orderOfferPriceList) {

                if (!Strings.isNullOrEmpty(orderOfferPrice.getOfferLabel())
                        && orderOfferPrice.getOfferLabel().contains("cooperative_business") && DateUtil.isSameDay(new Date(), orderOfferPrice.getOfferTime())) {
                    //判断订单标签是否合作经营
                    //判断报价抢单时间是否今天

                    Long orderId = orderOfferPrice.getOrderId();
                    Long masterId = orderOfferPrice.getMasterId();
                    log.info("handlerCooperationBusinessOrderClose grab_cooperation_cnt sub 1 ,orderId:{},masterId:{}", orderId, masterId);
                    pushRecordService.subCooperationBusinessMasterGrabCntDaily(masterId, DateFormatterUtil.getNow());
                }
            }






        }


    }


    @Override
    public void portPush(PortPushMessage message){
        Long orderId = message.getOrderDetailData().getMasterOrderId();
        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(orderId));
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        if(Objects.isNull(orderGrab) || orderGrab.getIsDelete() == 1){
            return ;
        }
        Integer offerNum;
        if (Objects.nonNull(message.getPortPushRuleDTO())) {
            offerNum  = message.getPortPushRuleDTO().getOfferNum();
        }else{
            offerNum  = message.getPushPortRule().getOfferNum();
        }
        Integer num = 0;
        if(AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType())){
            num = (orderGrab.getHireMasterId() > 0 && orderGrab.getConfirmServeStatus() == 1) ? 1 : 0;
        }else if(AppointType.OPEN.value.equals(orderGrab.getAppointType())){
            num = orderGrab.getOfferNumber();
        }
        if(num < offerNum){
            //满足条件推送 B端普通师傅
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderGrab.getOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            orderDetailData.getPushExtraData().setMasterSourceType(MasterSourceType.TOB.code);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }


    }


    @Override
    public void pushGoldMedalMaster(PushGoldMedalMasterMessage message){

        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(message.getOrderId()));
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        if(Objects.isNull(orderGrab) || orderGrab.getIsDelete() == 1){
            return ;
        }

        if(orderGrab.getOfferNumber() == 0){
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderGrab.getOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.GOLD_MEDAL_MASTER.code);
            orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.NONE_OFFER_PRICE.getCode());
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.GOLD_MEDAL_MASTER);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

        }
    }


    @Override
    public  void orderChangeGrabPush(OrderChangeGrabPushMessage message){

        if(Objects.isNull(message.getGlobalOrderTraceId()) || CollectionUtils.isEmpty(message.getNewMasterIds())){
            return ;
        }


        if(Objects.nonNull(message.getOldMasterId())){
            message.getNewMasterIds().remove(message.getOldMasterId());
        }

        if(CollectionUtils.isEmpty(message.getNewMasterIds())){
            return ;
        }

        Set<String> masterIdSet = message.getNewMasterIds().stream().map(String::valueOf).collect(Collectors.toSet());

        OrderBase orderBase = normalOrderResourceApi.getSimpleOrderBase(null,null,message.getGlobalOrderTraceId());

        if(Objects.isNull(orderBase)){
            return ;
        }

        Long orderId = orderBase.getOrderId();
        OrderDetailData orderDetailData = orderDataBuilder.build(orderId);

        Long timestamp = System.currentTimeMillis();
        String orderVersion = String.valueOf(timestamp);
        pushProgressRepository.insertBasePushProgress(message.getGlobalOrderTraceId(),orderVersion,masterIdSet.size(),new Date(timestamp),PushMode.GOLD_MEDAL_MASTER.code);
        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.GOLD_MEDAL_MASTER.code);
        commonFeature.put(FieldConstant.MATCH_SCENE_CODE, MatchSceneCode.ORDER_CHANGE_GRAB.getCode());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
        pushControllerFacade.directPush(orderDetailData, orderVersion, masterIdSet, commonFeature);

    }

    @Override
    public int specialGroupPush(OrderMatchMasterRqt rqt) {
        Long masterOrderId = rqt.getMasterOrderId();
        SimpleOrderGrab simpleOrderGrab = normalOrderResourceApi.getSimpleOrderGrab(masterOrderId, null, null);
        if (simpleOrderGrab != null) {
            Integer confirmServeStatus = simpleOrderGrab.getOrderGrab().getConfirmServeStatus();
            if (confirmServeStatus != null && confirmServeStatus == 1) {
                log.info("订单{}已经确认雇佣，不再匹配", masterOrderId);
                return 1;
            }
        }
        rqt.setBusinessLineId(2);
        rqt.setExclusivePushModeList(
                Arrays.asList(
                        PushMode.FULL_TIME_MASTER.code,
                        PushMode.DIRECT_APPOINT_MASTER.code,
                        PushMode.TECHNIQUE_VERIFY_MASTER.code,
                        PushMode.COOPERATION_BUSINESS_MASTER.code,
                        PushMode.COLLECT_CONTRACT_MASTER.code,
                        PushMode.ORDER_PACKAGE_MASTER.code,
                        PushMode.AGENT_MASTER.code,
                        PushMode.SPECIAL_GROUP.code
                ));
        this.match(rqt);
        return 1;
    }
}
