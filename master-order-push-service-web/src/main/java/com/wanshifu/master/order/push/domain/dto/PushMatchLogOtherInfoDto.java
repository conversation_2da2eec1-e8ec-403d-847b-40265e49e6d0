package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoListByIdsResp;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/13 16:42
 */
@Data
public class PushMatchLogOtherInfoDto {

    /**
     * 订单信息
     */
    private Map<String, OrderBase> orderBaseMap;

    /**
     * 地址信息
     */
    private Map<Long, Address> addressMap;

    /**
     * 服务信息
     */
    private Map<Long, ServeBaseInfoResp> serveMap;

    /**
     * 师傅信息
     */
    private Map<Long, GetMasterInfoListByIdsResp> masterInfoMap;
}
