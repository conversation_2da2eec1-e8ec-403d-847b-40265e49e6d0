package com.wanshifu.master.order.push.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.SortRuleExpressionDto;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.po.SortingStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.StrategyRelate;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;
import com.wanshifu.master.order.push.domain.vo.common.RuleItem;
import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
import com.wanshifu.master.order.push.mapper.FilterStrategyMapper;
import com.wanshifu.master.order.push.mapper.SortingStrategyMapper;
import com.wanshifu.master.order.push.repository.SortingStrategyRepository;
import com.wanshifu.master.order.push.repository.SortingStrategySnapshotRepository;
import com.wanshifu.master.order.push.repository.StrategyRelateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述 :  精排策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-09 17:46
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SortingStrategyServiceImpl implements SortingStrategyService {

    private final SortingStrategyRepository sortingStrategyRepository;
    private final StrategyRelateRepository strategyRelateRepository;
    private final StrategyRuleExpressionService strategyRuleExpressionService;
    private final SortingStrategySnapshotRepository sortingStrategySnapshotRepository;


    /**
     * 校验匹配项参数
     *
     * @param itemList
     */
    private void checkRuleParams(List<RuleItem> itemList) {
        itemList.forEach(it -> {
            //赋值方式为区间
            if (StringUtils.equals("range_value", it.getAssignMode())) {
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> scoreItem.getStartValue() != null && scoreItem.getEndValue() != null), StrUtil.format("请检查{}的区间参数", it.getItemTitle()));
            } else {
                //赋值方式为枚举
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> StringUtils.isNotBlank(scoreItem.getValue())), StrUtil.format("请检查{}的枚举参数", it.getItemTitle()));
            }
        });
        Assert.isTrue(itemList.stream().map(RuleItem::getItemTitle).distinct().count() == itemList.size(), "匹配项名称不可重复!");
        Assert.isTrue(itemList.stream().map(RuleItem::getItemName).distinct().count() == itemList.size(), "不可存在两条相同的匹配项!");
        BigDecimal totalWeight = itemList.stream().map(RuleItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalWeight.compareTo(BigDecimal.ONE) == 0, "所有匹配项权重之和必须等于1");
    }

    private void checkStrategyName(String strategyName, Integer businessLineId, Long strategyId) {
        SortingStrategy sortingStrategy = sortingStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(sortingStrategy, "该业务线已存在相同策略名称!");
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "精排策略管理",
            tableName = "sorting_strategy", mapperClass = SortingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "sortingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int enable(EnableRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        sortingStrategyRepository.selectByStrategyId(rqt.getStrategyId());
        //1:启用 0:禁用
        Integer strategyStatus = rqt.getStrategyStatus();
        if (strategyStatus == 0) {
            StrategyRelate strategyRelate = strategyRelateRepository.selectBySortingStrategyId(strategyId);
            Assert.isNull(strategyRelate, "当前记录已被应用，不可禁用");
        }
        return sortingStrategyRepository.updateStatus(strategyId, rqt.getStrategyStatus(),rqt.getUpdateAccountId());
    }

    @Override
    public SimplePageInfo<SortingStrategy> list(ListRqt rqt) {
        Page<SortingStrategy> startPage = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        String categoryIds = rqt.getCategoryIds();
        List<Long> categoryIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(categoryIds) && !StringUtils.equals("all", categoryIds)) {
            categoryIdList = Arrays.stream(categoryIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        List<SortingStrategy> sortingStrategies = sortingStrategyRepository.selectList(rqt.getOrderFlag(),rqt.getBusinessLineId(), rqt.getStrategyName(), rqt.getStrategyStatus(), rqt.getCreateStartTime(), rqt.getCreateEndTime(), categoryIdList);

        SimplePageInfo<SortingStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(sortingStrategies);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "精排策略管理",
            tableName = "sorting_strategy", mapperClass = SortingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "sortingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int delete(DeleteRqt rqt) {
        SortingStrategy sortingStrategy = sortingStrategyRepository.selectByStrategyId(rqt.getStrategyId());
        Assert.isTrue(sortingStrategy.getStrategyStatus() == 0, "非禁用状态不可删除!");
        return sortingStrategyRepository.softDeleteByStrategyId(rqt.getStrategyId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "精排策略管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int createV2(CreateV2Rqt rqt) {
        String strategyName = rqt.getStrategyName();
        List<SortRule> ruleList = rqt.getRuleList();
        Integer businessLineId = rqt.getBusinessLineId();
        String orderFlag = rqt.getOrderFlag();
        if(businessLineId==2){
            orderFlag= "normal";
        }
        this.checkStrategyName(strategyName, businessLineId, null);
        this.checkSortRuleParams(ruleList);
        String sortingRule = JSON.toJSONString(ruleList);
        //获取精排策略表达式集合
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = strategyRuleExpressionService.buildSortRuleExpressions(ruleList,null);
        Long snapshotId = sortingStrategySnapshotRepository.insert(orderFlag,strategyName, rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList), businessLineId, rqt.getCreateAccountId());
        return sortingStrategyRepository.insert(orderFlag,strategyName,snapshotId, rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList), businessLineId, rqt.getCreateAccountId(),2);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "精排策略管理",
            tableName = "sorting_strategy", mapperClass = SortingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "sortingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int updateV2(UpdateV2Rqt rqt) {
        Long strategyId = rqt.getStrategyId();
        String strategyName = rqt.getStrategyName();
        List<SortRule> ruleList = rqt.getRuleList();
        Integer businessLineId = rqt.getBusinessLineId();
        sortingStrategyRepository.selectByStrategyId(strategyId);
        String orderFlag = rqt.getOrderFlag();
        if(businessLineId==2){
            orderFlag= "normal";
        }
        this.checkStrategyName(strategyName, businessLineId, strategyId);
        this.checkSortRuleParams(ruleList);
        String sortingRule = JSON.toJSONString(ruleList);
        //获取精排策略表达式json集合
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = strategyRuleExpressionService.buildSortRuleExpressions(ruleList,null);
        Long snapshotId = sortingStrategySnapshotRepository.insert(orderFlag,strategyName, rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList), businessLineId, rqt.getUpdateAccountId());

        return sortingStrategyRepository.update(orderFlag,strategyId, snapshotId,strategyName, rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList), businessLineId, rqt.getUpdateAccountId());
    }


    private void checkSortRuleParams(List<SortRule> ruleList){
        if(CollectionUtils.isNotEmpty(ruleList)){
            ruleList.forEach(sortRule -> checkRuleParams(sortRule.getItemList()));
        }
    }


    @Override
    public SortingStrategy detailV2(DetailRqt rqt) {
        SortingStrategy sortingStrategy = sortingStrategyRepository.selectByStrategyId(rqt.getStrategyId());
        return sortingStrategy;
    }

    @Override
    public List<SortingStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList){

        return sortingStrategySnapshotRepository.selectBySnapshotIdList(snapshotIdList);

    }

}