package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.CrowdType;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.*;
import com.wanshifu.master.order.push.mapper.PushLimitRuleMapper;
import com.wanshifu.master.order.push.repository.PushLimitRuleRepository;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.service.PushLimitRuleService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class PushLimitRuleServiceImpl implements PushLimitRuleService {

    @Resource
    private PushLimitRuleRepository pushLimitRuleRepository;


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "限制推送管理",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "ruleName")
    public int create(CreateRqt rqt) {
        String ruleName = rqt.getRuleName();
        String ruleDesc = rqt.getRuleDesc();
        Integer businessLineId = rqt.getBusinessLineId();
        checkRuleName(ruleName,null);
        if(CrowdType.CROWD_LABEL.getCode().equals(rqt.getCrowdType())){
            checkUniq(null,rqt.getBusinessLineId(),Arrays.asList(rqt.getCityIds().split(",")),rqt.getCrowdLabel());
        }
        String crowdGroup = Objects.nonNull(rqt.getCrowdGroup()) ? JSON.toJSONString(rqt.getCrowdGroup()) : null;
        String crowdGroupExpression = Objects.nonNull(rqt.getCrowdGroup()) ? JSON.toJSONString(getCrowdGroupExpression(rqt.getCrowdGroup())) : null;
        String limitServeRule = Objects.nonNull(rqt.getLimitServeRule()) ? JSON.toJSONString(rqt.getLimitServeRule()) : null;
        String limitServeRuleExpression = Objects.nonNull(rqt.getLimitServeRule()) ? JSON.toJSONString(getServeRuleExpression(rqt.getLimitServeRule())) : null;
        return pushLimitRuleRepository.insertPushLimitRule(businessLineId,ruleName,ruleDesc,rqt.getCityIds(),rqt.getCrowdType(),rqt.getCrowdLabel(),crowdGroup,
                crowdGroupExpression,
                rqt.getLimitRange(),limitServeRule,limitServeRuleExpression,
                JSON.toJSONString(rqt.getExclusiveRule()),rqt.getLimitRule(),rqt.getDecreaseDays(),rqt.getDecreasePercent(),rqt.getFixedDecreasePercent(),rqt.getCreateAccountId());
    }


    private OpenConditionExpressionDto getServeRuleExpression(CreateRqt.LimitServeRule limitServeRule){


        //开启条件表达式
        String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(limitServeRule.getCondition(),
                BeanCopyUtil.copyListProperties(limitServeRule.getItemList().stream()
                        .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                        .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                    //时效标签 和 用户人群 操作符符号转换
                    List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user","user_group");
                    if(specialTerms.contains(s.getItemName())){
                        t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                    }
                }));
        //服务 的开启条件特殊处理
        List<CreateRqt.Item> serveItems = limitServeRule.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

        List<String> serveExpression = serveItems.stream().map(it -> {
            List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
            String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
            String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

            List<List<Long>> serveIdList = it.getServeIdList();

            List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

            List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

            List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","),Long.class));
            }
            return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
        }).collect(Collectors.toList());
        String serveExpressions = QlExpressUtil.transitionQlExpressStr(limitServeRule.getCondition(), serveExpression);
        if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
            openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, limitServeRule.getCondition(), serveExpressions);
        } else {
            openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
        }

        //开启条件表达式参数
        List<String> openConditionRuleParamsList = limitServeRule.getItemList().stream().map(CreateRqt.Item::getItemName)
                .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serveItems)) {
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                openConditionRuleParamsList.add("lv1_serve_id");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                openConditionRuleParamsList.add("lv2_serve_ids");
            if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                openConditionRuleParamsList.add("lv3_serve_ids");
        }
        //开启条件表达式参数
        String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));

        return new OpenConditionExpressionDto(openConditionRuleExpression,openConditionRuleParams);
    }





    private CrowdGroupExpressionDto getCrowdGroupExpression(CreateRqt.CrowdGroup crowdGroup){
        CrowdGroupExpressionDto crowdGroupExpressionDto = new CrowdGroupExpressionDto();
        List<String> groupParamList = new ArrayList<>(Collections.emptyList());
        List<String> groupExpressions = crowdGroup.getItemList().stream().map(it -> {
            groupParamList.add(it.getItemName());
            String term;
            //师傅人群
            if ("master_group".equals(it.getItemName())) {
                term = "in".equals(it.getTerm()) ? "contain" : "notContain";
            } else {
                term = it.getTerm();
            }
            //单个指标本身的表达式
            String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(),null);

            return masterQuotaFilterRuleExpression;
        }).collect(Collectors.toList());


        String groupExpression = QlExpressUtil.transitionQlExpressStr(crowdGroup.getCondition(), groupExpressions);
        String groupParams = groupParamList.stream().distinct().collect(Collectors.joining(","));

        crowdGroupExpressionDto.setGroupExpression(groupExpression);
        crowdGroupExpressionDto.setGroupParams(groupParams);
        return crowdGroupExpressionDto;


    }


    private void checkRuleName(String ruleName, Integer ruleId) {
        PushLimitRule pushLimitRule = pushLimitRuleRepository.selectByRuleName(ruleName, ruleId);
        Assert.isNull(pushLimitRule, "已存在相同规则名称!");
    }


    private void checkUniq(Integer ruleId,Integer businessLineId,List<String> cityIdList,String crowdLabel) {
        PushLimitRule pushLimitRule = pushLimitRuleRepository.selectByCityIdAndBusinessLineId(ruleId,businessLineId,cityIdList,crowdLabel);
        Assert.isNull(pushLimitRule, "该业务线城市已存在相同人群标签的规则!");
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "限制推送管理",
            updateAccountIdFieldName = "updateAccountId",
            tableName = "push_limit_rule", mapperClass = PushLimitRuleMapper.class,
            mapperBeanName = "pushLimitRuleMapper", primaryKeyFieldName = "ruleId",
            configNameFieldNameFromEntity = "ruleName")
    public int update(UpdateRqt rqt) {
        String ruleName = rqt.getRuleName();
        String ruleDesc = rqt.getRuleDesc();
        Integer businessLineId = rqt.getBusinessLineId();
        checkRuleName(ruleName, rqt.getRuleId());

        if(CrowdType.CROWD_LABEL.getCode().equals(rqt.getCrowdType())){
            checkUniq(rqt.getRuleId(),rqt.getBusinessLineId(),Arrays.asList(rqt.getCityIds().split(",")),rqt.getCrowdLabel());
        }

        String crowdGroup = Objects.nonNull(rqt.getCrowdGroup()) ? JSON.toJSONString(rqt.getCrowdGroup()) : null;
        String crowdGroupExpression = Objects.nonNull(rqt.getCrowdGroup()) ? JSON.toJSONString(getCrowdGroupExpression(rqt.getCrowdGroup())) : null;
        String limitServeRule = Objects.nonNull(rqt.getLimitServeRule()) ? JSON.toJSONString(rqt.getLimitServeRule()) : null;
        String limitServeRuleExpression = Objects.nonNull(rqt.getLimitServeRule()) ? JSON.toJSONString(getServeRuleExpression(rqt.getLimitServeRule())) : null;
        return pushLimitRuleRepository.updatePushLimitRule(rqt.getRuleId(),businessLineId,ruleName,ruleDesc,rqt.getCityIds(),rqt.getCrowdType(),rqt.getCrowdLabel(),crowdGroup,
                crowdGroupExpression,
                rqt.getLimitRange(),limitServeRule,limitServeRuleExpression,
                JSON.toJSONString(rqt.getExclusiveRule()),rqt.getLimitRule(),rqt.getDecreaseDays(),rqt.getDecreasePercent(),rqt.getFixedDecreasePercent(),
                rqt.getUpdateAccountId());

    }

    @Override
    public PushLimitRule detail(DetailRqt rqt) {
        return pushLimitRuleRepository.selectByPrimaryKey(rqt.getRuleId());
    }

    @Override
   public SimplePageInfo<PushLimitRule> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        String ruleName = rqt.getRuleName();
        Date createStartTime = rqt.getCreateStartTime();
        Date createEndTime = rqt.getCreateEndTime();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<PushLimitRule> pushLimitRuleList = pushLimitRuleRepository.selectList((rqt.getCityId() != null && rqt.getCityId() > 0) ? String.valueOf(rqt.getCityId()) : null,null,ruleName, createStartTime, createEndTime,null);

        SimplePageInfo<PushLimitRule> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(pushLimitRuleList);
        return listRespSimplePageInfo;
    }


    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "限制推送管理",
            tableName = "push_limit_rule", mapperClass = PushLimitRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "pushLimitRuleMapper", primaryKeyFieldName = "ruleId",
            configNameFieldNameFromEntity = "ruleName")
    public Integer enable(EnableRqt rqt){
        Integer ruleId = rqt.getRuleId();
        //1:启用 0:禁用
        return pushLimitRuleRepository.updateStatus(ruleId, rqt.getRuleStatus(),rqt.getUpdateAccountId());
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "限制推送管理",
            tableName = "push_limit_rule", mapperClass = PushLimitRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "pushLimitRuleMapper", primaryKeyFieldName = "ruleId",
            configNameFieldNameFromEntity = "ruleName")
    public Integer delete(DeleteRqt rqt){
        PushLimitRule pushLimitRule = pushLimitRuleRepository.selectByPrimaryKey(rqt.getRuleId());
        Assert.isTrue(pushLimitRule.getRuleStatus() == 0, "非禁用状态不可删除!");
        return pushLimitRuleRepository.softDeleteByRuleId(rqt.getRuleId());

    }

}
