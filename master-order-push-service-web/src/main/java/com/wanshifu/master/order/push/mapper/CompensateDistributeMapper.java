package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.MatchRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
public interface CompensateDistributeMapper extends IBaseCommMapper<CompensateDistribute> {


    List<CompensateDistribute> selectList(@Param("businessLineId") Integer businessLineId, @Param("categoryId") Long categoryId, @Param("compensateType")String compensateType, @Param("orderPushFlag")String orderPushFlag);


    List<CompensateDistribute> selectByCategoryIdAndAppointType(@Param("businessLineId") Integer businessLineId,@Param("orderPushFlag") String orderPushFlag,@Param("categoryId") Long categoryId, @Param("appointType")Integer appointType,
                                                                @Param("hasPrice")Integer hasPrice,@Param("hasCooperationUser")Integer hasCooperationUser);


    List<CompensateDistribute> selectByCategoryIdAndCompensateType(@Param("businessLineId") Integer businessLineId,@Param("orderPushFlag") String orderPushFlag,@Param("categoryIdList") List<String> categoryIdList, @Param("appointTypeList")List<String> appointTypeList,
                                                                @Param("distributeId")Integer distributeId,@Param("hasPrice")Integer hasPrice,@Param("hasCooperationUser")Integer hasCooperationUser,@Param("compensateType")String compensateType);

    List<CompensateDistribute> match(MatchRqt rqt);

    }