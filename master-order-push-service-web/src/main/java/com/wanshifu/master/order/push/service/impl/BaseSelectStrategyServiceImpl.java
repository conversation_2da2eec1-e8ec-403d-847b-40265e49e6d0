package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.StrategyRelate;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.CreateRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.DeleteRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.EnableRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.ListRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.UpdateRqt;
import com.wanshifu.master.order.push.mapper.BaseSelectStrategyMapper;
import com.wanshifu.master.order.push.repository.BaseSelectStrategyRepository;
import com.wanshifu.master.order.push.repository.BaseSelectStrategySnapshotRepository;
import com.wanshifu.master.order.push.repository.StrategyRelateRepository;
import com.wanshifu.master.order.push.service.BaseSelectStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BaseSelectStrategyServiceImpl implements BaseSelectStrategyService {

    private final BaseSelectStrategyRepository baseSelectStrategyRepository;
    private final StrategyRelateRepository strategyRelateRepository;
    private final BaseSelectStrategySnapshotRepository baseSelectStrategySnapshotRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "初筛策略管理",
             createAccountIdFieldName = "createAccountId",
             businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {
        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), null);
        String orderFlag = rqt.getOrderFlag();
        Integer businessLineId = rqt.getBusinessLineId();
        if(businessLineId==2){
            orderFlag= "normal";
        }

        Long snapshotId = baseSelectStrategySnapshotRepository.insert(rqt.getStrategyName(), rqt.getStrategyDesc(),
                orderFlag,
                JSON.toJSONString(rqt.getRangeSelect()),
                JSON.toJSONString(rqt.getTechniqueSelect()),
                JSON.toJSONString(rqt.getStatusSelect()),
                rqt.getServeDataSelect() != null ? JSON.toJSONString(rqt.getServeDataSelect()) : null,
                rqt.getBusinessLineId(), rqt.getCreateAccountId());

        return baseSelectStrategyRepository.insert(rqt.getStrategyName(), snapshotId,
                rqt.getStrategyDesc(),
                rqt.getOrderFlag(),
                JSON.toJSONString(rqt.getRangeSelect()),
                JSON.toJSONString(rqt.getTechniqueSelect()),
                JSON.toJSONString(rqt.getStatusSelect()),
                rqt.getServeDataSelect() != null ? JSON.toJSONString(rqt.getServeDataSelect()) : null,
                rqt.getBusinessLineId(), rqt.getCreateAccountId(),1
        );
    }

    private void checkStrategyName(String strategyName, Integer businessLineId, Long strategyId) {
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(baseSelectStrategy, "该业务线已存在相同策略名称!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "初筛策略管理",
            tableName = "base_select_strategy", mapperClass = BaseSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "baseSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        baseSelectStrategyRepository.selectByStrategyId(strategyId);
        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), strategyId);
        String orderFlag = rqt.getOrderFlag();
        Integer businessLineId = rqt.getBusinessLineId();
        if(businessLineId==2){
            orderFlag= "normal";
        }
        Long snapshotId = baseSelectStrategySnapshotRepository.insert(rqt.getStrategyName(), rqt.getStrategyDesc(),
                orderFlag,
                JSON.toJSONString(rqt.getRangeSelect()),
                JSON.toJSONString(rqt.getTechniqueSelect()),
                JSON.toJSONString(rqt.getStatusSelect()),
                rqt.getServeDataSelect() != null ? JSON.toJSONString(rqt.getServeDataSelect()) : null,
                rqt.getBusinessLineId(), rqt.getUpdateAccountId());

        return baseSelectStrategyRepository.update(strategyId,snapshotId, rqt.getStrategyName(), rqt.getStrategyDesc(),
                orderFlag,
                JSON.toJSONString(rqt.getRangeSelect()),
                JSON.toJSONString(rqt.getTechniqueSelect()),
                JSON.toJSONString(rqt.getStatusSelect()),
                rqt.getServeDataSelect() != null ? JSON.toJSONString(rqt.getServeDataSelect()) : "",
                rqt.getBusinessLineId(), rqt.getUpdateAccountId()
        );
    }

    @Override
    public BaseSelectStrategy detail(DetailRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyRepository.selectByStrategyId(strategyId);
        return baseSelectStrategy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "初筛策略管理",
            tableName = "base_select_strategy", mapperClass = BaseSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "baseSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int enable(EnableRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        baseSelectStrategyRepository.selectByStrategyId(strategyId);
        //1:启用 0:禁用
        Integer strategyStatus = rqt.getStrategyStatus();
        if (strategyStatus == 0) {
            StrategyRelate strategyRelate = strategyRelateRepository.selectByBaseSelectStrategyId(strategyId);
            Assert.isNull(strategyRelate, "当前记录已被应用，不可禁用");
        }
        return baseSelectStrategyRepository.updateStatus(strategyId, rqt.getStrategyStatus(),rqt.getUpdateAccountId());
    }

    @Override
    public SimplePageInfo<BaseSelectStrategy> list(ListRqt rqt) {
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        Long businessLineId = rqt.getBusinessLineId();
        String strategyName = rqt.getStrategyName();
        Integer strategyStatus = rqt.getStrategyStatus();
        Date createStartTime = rqt.getCreateStartTime();
        Date createEndTime = rqt.getCreateEndTime();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<BaseSelectStrategy> baseSelectStrategyList = baseSelectStrategyRepository.selectList(businessLineId, strategyName, strategyStatus, createStartTime, createEndTime);

        SimplePageInfo<BaseSelectStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(baseSelectStrategyList);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "初筛策略管理",
            tableName = "base_select_strategy", mapperClass = BaseSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "baseSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int delete(DeleteRqt rqt) {
        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyRepository.selectByStrategyId(rqt.getStrategyId());
        Assert.isTrue(baseSelectStrategy.getStrategyStatus() == 0, "非禁用状态不可删除!");
        return baseSelectStrategyRepository.softDeleteByStrategyId(rqt.getStrategyId());
    }


    @Override
    public List<BaseSelectStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList){

        List<BaseSelectStrategySnapshot> baseSelectStrategySnapshotList = baseSelectStrategySnapshotRepository.selectBySnapshotIdList(snapshotIdList);

        return baseSelectStrategySnapshotList;

    }

    }