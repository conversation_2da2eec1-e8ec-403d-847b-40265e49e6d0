package com.wanshifu.master.order.push.mapper;


import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
public interface PriorityPushRuleMapper extends IBaseCommMapper<PriorityPushRule> {


    List<PriorityPushRule> selectList(@Param("businessLineId")Integer businessLineId,@Param("ruleName") String ruleName, @Param("cityId") Long cityId,
                                      @Param("categoryIdList")List<Long> categoryIdList, @Param("createStartTime")Date createStartTime,@Param("createEndTime") Date createEndTime);


    List<PriorityPushRule> selectByCategoryIdAndCityId(@Param("businessLineId") Integer businessLineId, @Param("categoryId") String categoryId, @Param("cityId") String cityId);

}