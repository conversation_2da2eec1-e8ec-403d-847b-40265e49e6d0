package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;
import com.wanshifu.master.order.push.mapper.OrderDynamicRoundsPushMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class OrderDynamicRoundsPushRepository extends BaseRepository<OrderDynamicRoundsPush> {

    @Resource
    private OrderDynamicRoundsPushMapper orderDynamicRoundsPushMapper;

    public List<OrderDynamicRoundsPush> list(ListRqt rqt){
        return orderDynamicRoundsPushMapper.list(rqt);
    }

}
