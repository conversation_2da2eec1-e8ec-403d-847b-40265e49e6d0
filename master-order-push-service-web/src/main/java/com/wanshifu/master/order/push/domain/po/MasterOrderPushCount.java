package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "master_order_push_count")
public class MasterOrderPushCount {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "master_id")
    private String masterId;

    @Column(name = "dt")
    private String dt;

    @Column(name = "order_cnt")
    private Long orderCnt;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

}
