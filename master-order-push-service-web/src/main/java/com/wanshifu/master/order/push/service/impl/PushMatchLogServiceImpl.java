package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoListByIdsRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoListByIdsResp;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.PushLogMatchType;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.PushMatchLogService;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetGoodsIdReq;
import com.wanshifu.order.offer.api.NormalOrderListApi;
import com.wanshifu.order.offer.domains.api.request.BatchOrderBaseForLimitRqt;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/25 20:49
 */
@Service
@Slf4j
public class PushMatchLogServiceImpl implements PushMatchLogService {

    @Resource
    private CooperationBusinessMasterMatchLogRepository cooperationBusinessMasterMatchLogRepository;

    @Resource
    private EnterpriseAgreementMasterMatchRepository enterpriseAgreementMasterMatchRepository;

    @Resource
    private AgreementMasterMatchRepository agreementMasterMatchRepository;

    @Resource
    private NewModelMatchDetailRepository newModelMatchDetailRepository;

    @Resource
    private ServeServiceApi serveServiceApi;

    @Resource
    private NormalOrderListApi normalOrderListApi;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Resource
    private OrderFullTimeMasterMatchLogRepository orderFullTimeMasterMatchLogRepository;


    @Override
    @Deprecated
    public SimplePageInfo<PushMatchLogDto> pushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {

        return null;
    }

    @Override
    public SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        if (!validPushMatchLogRqt(pushMatchLogRqt)) {
            return null;
        }

        SimplePageInfo<UserAgreementPushMatchLogDto> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(pushMatchLogRqt.getPageNum(), pushMatchLogRqt.getPageSize());

        List<AgreementMasterMatch> agreementMasterMatchList = agreementMasterMatchRepository.selectList(pushMatchLogRqt.getOrderNo(),
                pushMatchLogRqt.getMasterId(), pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd());

        List<UserAgreementPushMatchLogDto> pushMatchLogDtoList = buildPushMatchLogDtoListByAgreementMasterMatch(agreementMasterMatchList);
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return null;
        }

        //处理不同漏斗节点的状态
        handleUserAgreementStatus(pushMatchLogDtoList);

        //填充订单、服务、师傅相关信息
        List<String> orderNoList = pushMatchLogDtoList.stream().map(UserAgreementPushMatchLogDto::getOrderNo).collect(Collectors.toList());
        List<Long> masterIdList = pushMatchLogDtoList.stream().map(UserAgreementPushMatchLogDto::getMasterId).collect(Collectors.toList());

        PushMatchLogOtherInfoDto otherInfo = getOtherInfo(orderNoList, masterIdList);

        Map<String, OrderBase> orderBaseMap = otherInfo.getOrderBaseMap();
        Map<Long, Address> addressMap = otherInfo.getAddressMap();
        Map<Long, ServeBaseInfoResp> serveMap = otherInfo.getServeMap();
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = otherInfo.getMasterInfoMap();

        //填充信息
        for (UserAgreementPushMatchLogDto pushMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(pushMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        pushMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            pushMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            pushMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        pushMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                if (Objects.nonNull(serveMap.get(Long.parseLong(serveId)))) {
                                    serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                                }
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            pushMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(pushMatchLogDto.getMasterId()))) {
                pushMatchLogDto.setMasterName(masterInfoMap.get(pushMatchLogDto.getMasterId()).getTeamName());
            }

        }

        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushMatchLogDtoList);

        return result;
    }



    @Override
    public SimplePageInfo<OrderFullTimeMasterMatchLogDto> orderFullMasterMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        if (!validPushMatchLogRqt(pushMatchLogRqt)) {
            return null;
        }

        SimplePageInfo<OrderFullTimeMasterMatchLogDto> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(pushMatchLogRqt.getPageNum(), pushMatchLogRqt.getPageSize());

        List<OrderFullTimeMasterMasterMatchLog> orderFullTimeMasterMasterMatchLogs = orderFullTimeMasterMatchLogRepository.selectList(pushMatchLogRqt.getOrderNo(),
                pushMatchLogRqt.getMasterId(), pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd());

        List<OrderFullTimeMasterMatchLogDto> pushMatchLogDtoList = buildPushMatchLogDtoListByFullTimeMasterMatch(orderFullTimeMasterMasterMatchLogs);
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return null;
        }

        //处理不同漏斗节点的状态
        handleFullTimeMatchLog(pushMatchLogDtoList);

        //填充订单、服务、师傅相关信息
        List<String> orderNoList = pushMatchLogDtoList.stream().map(OrderFullTimeMasterMatchLogDto::getOrderNo).collect(Collectors.toList());
        List<Long> masterIdList = pushMatchLogDtoList.stream().map(OrderFullTimeMasterMatchLogDto::getMasterId).collect(Collectors.toList());

        PushMatchLogOtherInfoDto otherInfo = getOtherInfo(orderNoList, masterIdList);

        Map<String, OrderBase> orderBaseMap = otherInfo.getOrderBaseMap();
        Map<Long, Address> addressMap = otherInfo.getAddressMap();
        Map<Long, ServeBaseInfoResp> serveMap = otherInfo.getServeMap();
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = otherInfo.getMasterInfoMap();

        //填充信息
        for (OrderFullTimeMasterMatchLogDto masterMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(masterMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        masterMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            masterMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            masterMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        masterMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                if (Objects.nonNull(serveMap.get(Long.parseLong(serveId)))) {
                                    serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                                }
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            masterMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(masterMatchLogDto.getMasterId()))) {
                masterMatchLogDto.setMasterName(masterInfoMap.get(masterMatchLogDto.getMasterId()).getTeamName());
            }

        }

        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushMatchLogDtoList);

        return result;
    }

    @Override
    public SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        if (!validPushMatchLogRqt(pushMatchLogRqt)) {
            return null;
        }

        SimplePageInfo<EnterpriseAppointPushMatchLogDto> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(pushMatchLogRqt.getPageNum(), pushMatchLogRqt.getPageSize());

        List<EnterpriseOrderAgreementMasterMatch> enterpriseOrderAgreementMasterMatchList = enterpriseAgreementMasterMatchRepository.selectList(pushMatchLogRqt.getOrderNo(),
                pushMatchLogRqt.getMasterId(), pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd());

        List<EnterpriseAppointPushMatchLogDto> pushMatchLogDtoList = buildPushMatchLogDtoListByEnterpriseOrderAgreementMasterMatch(enterpriseOrderAgreementMasterMatchList);
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return null;
        }

        //处理不同漏斗节点的状态
        handleEnterpriseAppointStatus(pushMatchLogDtoList);

        //填充订单、服务、师傅相关信息
        List<String> orderNoList = pushMatchLogDtoList.stream().map(EnterpriseAppointPushMatchLogDto::getOrderNo).collect(Collectors.toList());
        List<Long> masterIdList = pushMatchLogDtoList.stream().map(EnterpriseAppointPushMatchLogDto::getMasterId).collect(Collectors.toList());

        PushMatchLogOtherInfoDto otherInfo = getOtherInfo(orderNoList, masterIdList);

        Map<String, OrderBase> orderBaseMap = otherInfo.getOrderBaseMap();
        Map<Long, Address> addressMap = otherInfo.getAddressMap();
        Map<Long, ServeBaseInfoResp> serveMap = otherInfo.getServeMap();
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = otherInfo.getMasterInfoMap();

        //填充信息
        for (EnterpriseAppointPushMatchLogDto pushMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(pushMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        pushMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            pushMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            pushMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        pushMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                if (Objects.nonNull(serveMap.get(Long.parseLong(serveId)))) {
                                    serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                                }
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            pushMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(pushMatchLogDto.getMasterId()))) {
                pushMatchLogDto.setMasterName(masterInfoMap.get(pushMatchLogDto.getMasterId()).getTeamName());
            }

        }

        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushMatchLogDtoList);

        return result;
    }

    @Override
    public SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        if (!validPushMatchLogRqt(pushMatchLogRqt)) {
            return null;
        }

        SimplePageInfo<CooperationBusinessPushMatchLogDto> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(pushMatchLogRqt.getPageNum(), pushMatchLogRqt.getPageSize());

        List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList = cooperationBusinessMasterMatchLogRepository.selectList(pushMatchLogRqt.getOrderNo(),
                pushMatchLogRqt.getMasterId(), pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd());

        List<CooperationBusinessPushMatchLogDto> pushMatchLogDtoList = buildPushMatchLogDtoListByCooperationBusinessMasterMatchLog(cooperationBusinessMasterMatchLogList);
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return null;
        }

        //处理不同漏斗节点的状态
        handleCooperationBusinessStatus(pushMatchLogDtoList);

        //填充订单、服务、师傅相关信息
        List<String> orderNoList = pushMatchLogDtoList.stream().map(CooperationBusinessPushMatchLogDto::getOrderNo).collect(Collectors.toList());
        List<Long> masterIdList = pushMatchLogDtoList.stream().map(CooperationBusinessPushMatchLogDto::getMasterId).collect(Collectors.toList());

        PushMatchLogOtherInfoDto otherInfo = getOtherInfo(orderNoList, masterIdList);

        Map<String, OrderBase> orderBaseMap = otherInfo.getOrderBaseMap();
        Map<Long, Address> addressMap = otherInfo.getAddressMap();
        Map<Long, ServeBaseInfoResp> serveMap = otherInfo.getServeMap();
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = otherInfo.getMasterInfoMap();

        //填充信息
        for (CooperationBusinessPushMatchLogDto pushMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(pushMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        pushMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            pushMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            pushMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        pushMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                if (Objects.nonNull(serveMap.get(Long.parseLong(serveId)))) {
                                    serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                                }
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            pushMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(pushMatchLogDto.getMasterId()))) {
                pushMatchLogDto.setMasterName(masterInfoMap.get(pushMatchLogDto.getMasterId()).getTeamName());
            }

        }

        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushMatchLogDtoList);

        return result;
    }

    @Override
    public SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(PushMatchLogRqt pushMatchLogRqt) {
        if (!validPushMatchLogRqt(pushMatchLogRqt)) {
            return null;
        }

        SimplePageInfo<NewModelCityPushMatchLogDto> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(pushMatchLogRqt.getPageNum(), pushMatchLogRqt.getPageSize());

        List<NewModelMatchDetail> newModelMatchDetailList = newModelMatchDetailRepository.selectList(pushMatchLogRqt.getOrderNo(),
                pushMatchLogRqt.getMasterId(), pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd());

        List<NewModelCityPushMatchLogDto> pushMatchLogDtoList = buildPushMatchLogDtoListByNewModelMatchDetail(newModelMatchDetailList);
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return null;
        }

        //处理不同漏斗节点的状态
        handleNewModelCityStatus(pushMatchLogDtoList);

        //填充订单、服务、师傅相关信息
        List<String> orderNoList = pushMatchLogDtoList.stream().map(NewModelCityPushMatchLogDto::getOrderNo).collect(Collectors.toList());
        List<Long> masterIdList = pushMatchLogDtoList.stream().map(NewModelCityPushMatchLogDto::getMasterId).collect(Collectors.toList());

        PushMatchLogOtherInfoDto otherInfo = getOtherInfo(orderNoList, masterIdList);

        Map<String, OrderBase> orderBaseMap = otherInfo.getOrderBaseMap();
        Map<Long, Address> addressMap = otherInfo.getAddressMap();
        Map<Long, ServeBaseInfoResp> serveMap = otherInfo.getServeMap();
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = otherInfo.getMasterInfoMap();

        //填充信息
        for (NewModelCityPushMatchLogDto pushMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(pushMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        pushMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            pushMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            pushMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        pushMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                if (Objects.nonNull(serveMap.get(Long.parseLong(serveId)))) {
                                    serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                                }
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            pushMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(pushMatchLogDto.getMasterId()))) {
                pushMatchLogDto.setMasterName(masterInfoMap.get(pushMatchLogDto.getMasterId()).getTeamName());
            }

        }

        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushMatchLogDtoList);

        return result;
    }

    private boolean validPushMatchLogRqt(PushMatchLogRqt pushMatchLogRqt) {
        if (Objects.isNull(pushMatchLogRqt)) {
            log.error("select pushMatchLogList error！rqt is null!");
            return false;
        }
        if (Objects.isNull(pushMatchLogRqt.getMasterId())
                && Strings.isNullOrEmpty(pushMatchLogRqt.getOrderNo())
                && (Objects.isNull(pushMatchLogRqt.getOrderCreateTimeStart()) || Objects.isNull(pushMatchLogRqt.getOrderCreateTimeEnd()))) {
            log.error("select pushMatchLogList error！params is valid!");
            return false;
        }

        if (Objects.nonNull(pushMatchLogRqt.getOrderCreateTimeStart())
                && Objects.nonNull(pushMatchLogRqt.getOrderCreateTimeEnd())
                && !DateUtil.isSameDay(pushMatchLogRqt.getOrderCreateTimeStart(), pushMatchLogRqt.getOrderCreateTimeEnd())) {
            throw new RuntimeException("下单日期时间范围仅支持1天查询！");
        }


        return true;
    }

    /**
     * 获取订单、地址、服务、师傅相关信息
     * @param orderNoList
     * @param masterIdList
     * @return
     */
    private PushMatchLogOtherInfoDto getOtherInfo(List<String> orderNoList, List<Long> masterIdList) {
        PushMatchLogOtherInfoDto result = new PushMatchLogOtherInfoDto();
        if (CollectionUtil.isNotEmpty(orderNoList)) {
            Map<String, OrderBase> orderBaseMap = null;
            Map<Long, Address> addressMap = null;
            Map<Long, ServeBaseInfoResp> serveMap = null;

            //查询订单相关信息
            BatchOrderBaseForLimitRqt batchOrderBaseForLimitRqt = new BatchOrderBaseForLimitRqt();
            batchOrderBaseForLimitRqt.setOrderNos(orderNoList);

            List<OrderBase> orderBaseList = null;
            try {

                orderBaseList = normalOrderListApi.batchGetOrderBaseForLimit(batchOrderBaseForLimitRqt);
            } catch (Exception e) {
                log.error("pushMatchLogList getOtherInfo invoke order-offer-service error!", e);
                throw new BusException("获取订单信息失败！");
            }
            if (CollectionUtil.isNotEmpty(orderBaseList)) {

                orderBaseMap = orderBaseList.stream().collect(Collectors.toMap(OrderBase::getOrderNo, Function.identity()));

                //获取地址信息
                Set<Long> thirdDivisionIds = orderBaseList.stream().map(OrderBase::getThirdDivisionId).collect(Collectors.toSet());
                List<Address> addressList = addressCommon.getAddressByDivisionIds(StrUtil.join(",", thirdDivisionIds));
                if (CollectionUtil.isNotEmpty(addressList)) {
                    addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));
                }

                //获取服务信息
                List<String> serveIdList = orderBaseList.stream().map(OrderBase::getServeIds).collect(Collectors.toList());
                Set<Long> serveIdSet = new HashSet<>();
                serveIdList.forEach(serveId -> {
                    if (Strings.isNullOrEmpty(serveId)) {
                        return;
                    }
                    serveIdSet.addAll(Arrays.stream(serveId.split(",")).map(Long::parseLong).collect(Collectors.toSet()));
                });
                ServeIdSetGoodsIdReq serveIdSetGoodsIdReq = new ServeIdSetGoodsIdReq();
                serveIdSetGoodsIdReq.setServeIdSet(serveIdSet);
                List<ServeBaseInfoResp> serveBaseInfoList = null;
                try {

                    serveBaseInfoList = serveServiceApi.getServes(serveIdSetGoodsIdReq);
                } catch (Exception e) {
                    log.error("pushMatchLogList fillOtherInfo invoke order-config-service error!", e);
                    throw new BusException("获取服务信息失败！");
                }
                if (CollectionUtil.isNotEmpty(serveBaseInfoList)) {
                    serveMap = serveBaseInfoList.stream().collect(Collectors.toMap(ServeBaseInfoResp::getServeId, Function.identity()));
                }
                result.setOrderBaseMap(orderBaseMap);
                result.setAddressMap(addressMap);
                result.setServeMap(serveMap);
            }

        }

        if (CollectionUtil.isNotEmpty(masterIdList)) {
            //查询师傅相关信息
            Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = null;

            GetMasterInfoListByIdsRqt getMasterInfoListByIdsRqt = new GetMasterInfoListByIdsRqt();
            getMasterInfoListByIdsRqt.setMasterIds(masterIdList);
            getMasterInfoListByIdsRqt.setPageSize(masterIdList.size());

            List<GetMasterInfoListByIdsResp> getMasterInfoListByIdsRespList = null;
            try {
                getMasterInfoListByIdsRespList = commonQueryServiceApi.getMasterInfoListByIds(getMasterInfoListByIdsRqt);
            } catch (Exception e) {
                log.error("pushMatchLogList getOtherInfo invoke master-information-service error!", e);
                throw new BusException("获取师傅信息失败！");
            }

            if (CollectionUtil.isNotEmpty(getMasterInfoListByIdsRespList)) {
                masterInfoMap = getMasterInfoListByIdsRespList.stream().collect(Collectors.toMap(GetMasterInfoListByIdsResp::getMasterId, Function.identity()));
            }
            result.setMasterInfoMap(masterInfoMap);
        }

        return result;

    }

    /**
     * 填充订单信息、服务信息、师傅信息
     * @param pushMatchLogDtoList
     */
    private void fillOtherInfo(List<PushMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        Map<String, OrderBase> orderBaseMap = null;
        Map<Long, Address> addressMap = null;
        Map<Long, ServeBaseInfoResp> serveMap = null;

        //查询订单相关信息
        BatchOrderBaseForLimitRqt batchOrderBaseForLimitRqt = new BatchOrderBaseForLimitRqt();
        batchOrderBaseForLimitRqt.setOrderNos(pushMatchLogDtoList.stream().map(PushMatchLogDto::getOrderNo).collect(Collectors.toList()));

        List<OrderBase> orderBaseList = null;
        try {

            orderBaseList = normalOrderListApi.batchGetOrderBaseForLimit(batchOrderBaseForLimitRqt);
        } catch (Exception e) {
            log.error("pushMatchLogList fillOtherInfo invoke order-offer-service error!", e);
            throw new BusException("获取订单信息失败！");
        }
        if (CollectionUtil.isNotEmpty(orderBaseList)) {

            orderBaseMap = orderBaseList.stream().collect(Collectors.toMap(OrderBase::getOrderNo, Function.identity()));

            //获取地址信息
            Set<Long> thirdDivisionIds = orderBaseList.stream().map(OrderBase::getThirdDivisionId).collect(Collectors.toSet());
            List<Address> addressList = addressCommon.getAddressByDivisionIds(StrUtil.join(",", thirdDivisionIds));
            if (CollectionUtil.isNotEmpty(addressList)) {
                addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, Function.identity()));
            }

            //获取服务信息
            List<String> serveIdList = orderBaseList.stream().map(OrderBase::getServeIds).collect(Collectors.toList());
            Set<Long> serveIdSet = new HashSet<>();
            serveIdList.forEach(serveId -> {
                serveIdSet.addAll(Arrays.stream(serveId.split(",")).map(Long::parseLong).collect(Collectors.toSet()));
            });
            ServeIdSetGoodsIdReq serveIdSetGoodsIdReq = new ServeIdSetGoodsIdReq();
            serveIdSetGoodsIdReq.setServeIdSet(serveIdSet);
            List<ServeBaseInfoResp> serveBaseInfoList = null;
            try {

                serveBaseInfoList = serveServiceApi.getServes(serveIdSetGoodsIdReq);
            } catch (Exception e) {
                log.error("pushMatchLogList fillOtherInfo invoke order-config-service error!", e);
                throw new BusException("获取服务信息失败！");
            }
            if (CollectionUtil.isNotEmpty(serveBaseInfoList)) {
                serveMap = serveBaseInfoList.stream().collect(Collectors.toMap(ServeBaseInfoResp::getServeId, Function.identity()));
            }

        }

        //查询师傅相关信息
        Map<Long, GetMasterInfoListByIdsResp> masterInfoMap = null;

        GetMasterInfoListByIdsRqt getMasterInfoListByIdsRqt = new GetMasterInfoListByIdsRqt();
        getMasterInfoListByIdsRqt.setMasterIds(pushMatchLogDtoList.stream().map(PushMatchLogDto::getMasterId).collect(Collectors.toList()));
        getMasterInfoListByIdsRqt.setPageSize(pushMatchLogDtoList.size());

        List<GetMasterInfoListByIdsResp> getMasterInfoListByIdsRespList = null;
        try {
            getMasterInfoListByIdsRespList = commonQueryServiceApi.getMasterInfoListByIds(getMasterInfoListByIdsRqt);
        } catch (Exception e) {
            log.error("pushMatchLogList fillOtherInfo invoke master-information-service error!", e);
            throw new BusException("获取师傅信息失败！");
        }

        if (CollectionUtil.isNotEmpty(getMasterInfoListByIdsRespList)) {
            masterInfoMap = getMasterInfoListByIdsRespList.stream().collect(Collectors.toMap(GetMasterInfoListByIdsResp::getMasterId, Function.identity()));
        }


        //填充信息
        for (PushMatchLogDto pushMatchLogDto : pushMatchLogDtoList) {
            if (Objects.nonNull(orderBaseMap)) {
                OrderBase orderBase = orderBaseMap.get(pushMatchLogDto.getOrderNo());
                if (Objects.nonNull(orderBase)) {
                    if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
                        pushMatchLogDto.setOrderSource("总包");
                    } else {
                        if (orderBase.getBusinessLineId() == 1) {
                            pushMatchLogDto.setOrderSource("平台");
                        } else if (orderBase.getBusinessLineId() == 2) {
                            pushMatchLogDto.setOrderSource("家庭");
                        }
                    }
                    if (Objects.nonNull(addressMap)) {
                        Address address = addressMap.get(orderBase.getThirdDivisionId());
                        pushMatchLogDto.setOrderCityName(address.getLv3DivisionName());
                    }

                    if (Objects.nonNull(serveMap)) {
                        String serveIds = orderBase.getServeIds();
                        if (!Strings.isNullOrEmpty(serveIds)) {
                            StringBuilder serveName = new StringBuilder();
                            for (String serveId : serveIds.split(",")) {
                                serveName.append(serveMap.get(Long.parseLong(serveId)).getName()).append(",");
                            }
                            if (serveName.toString().endsWith(",")) {
                                serveName.deleteCharAt(serveName.length() - 1);
                            }
                            pushMatchLogDto.setServeTypeName(serveName.toString());
                        }


                    }
                }
            }
            if (Objects.nonNull(masterInfoMap) && Objects.nonNull(masterInfoMap.get(pushMatchLogDto.getMasterId()))) {
                pushMatchLogDto.setMasterName(masterInfoMap.get(pushMatchLogDto.getMasterId()).getTeamName());
            }

        }

    }

    /**
     * 平台协议派单处理不同漏斗节点的状态
     * @param pushMatchLogDtoList
     */
    private void handleUserAgreementStatus(List<UserAgreementPushMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        pushMatchLogDtoList.forEach(pushMatchLogDto -> {
            if (Objects.isNull(pushMatchLogDto.getIsMatchSuccess()) || pushMatchLogDto.getIsMatchSuccess() == 0) {
                pushMatchLogDto.setIsCalculatePriceSuccess(null);
                pushMatchLogDto.setIsFilter(null);
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }

            if (Objects.isNull(pushMatchLogDto.getIsFilter()) || pushMatchLogDto.getIsFilter() == 1) {
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsDistribute()) || pushMatchLogDto.getIsDistribute() == 0) {
                pushMatchLogDto.setIsAutoGrabSuccess(null);
            }
        });
    }


    /**
     * 平台协议派单处理不同漏斗节点的状态
     * @param pushMatchLogDtoList
     */
    private void handleFullTimeMatchLog(List<OrderFullTimeMasterMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        pushMatchLogDtoList.forEach(pushMatchLogDto -> {
            if (Objects.isNull(pushMatchLogDto.getIsMatchSuccess()) || pushMatchLogDto.getIsMatchSuccess() == 0) {
                pushMatchLogDto.setIsFilter(null);
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }

            if (Objects.isNull(pushMatchLogDto.getIsFilter()) || pushMatchLogDto.getIsFilter() == 1) {
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsDistribute()) || pushMatchLogDto.getIsDistribute() == 0) {
                pushMatchLogDto.setIsAutoGrabSuccess(null);
            }
        });
    }

    /**
     * 总包直接指派处理不同漏斗节点的状态
     * @param pushMatchLogDtoList
     */
    private void handleEnterpriseAppointStatus(List<EnterpriseAppointPushMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        pushMatchLogDtoList.forEach(pushMatchLogDto -> {
            if (Objects.isNull(pushMatchLogDto.getIsMatchSuccess()) || pushMatchLogDto.getIsMatchSuccess() == 0) {
                pushMatchLogDto.setIsCalculatePriceSuccess(null);
                pushMatchLogDto.setIsFilter(null);
                pushMatchLogDto.setIsDistribute(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsFilter()) || pushMatchLogDto.getIsFilter() == 1) {
                pushMatchLogDto.setIsDistribute(null);
            }
        });
    }

    /**
     * 合作经营派单处理不同漏斗节点的状态
     * @param pushMatchLogDtoList
     */
    private void handleCooperationBusinessStatus(List<CooperationBusinessPushMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        pushMatchLogDtoList.forEach(pushMatchLogDto -> {
            if (Objects.isNull(pushMatchLogDto.getIsMatchSuccess()) || pushMatchLogDto.getIsMatchSuccess() == 0) {
                pushMatchLogDto.setIsFilter(null);
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsFilter()) || pushMatchLogDto.getIsFilter() == 1) {
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsDistribute()) || pushMatchLogDto.getIsDistribute() == 0) {
                pushMatchLogDto.setIsAutoGrabSuccess(null);
            }
        });
    }

    /**
     * 样板城市派单处理不同漏斗节点的状态
     * @param pushMatchLogDtoList
     */
    private void handleNewModelCityStatus(List<NewModelCityPushMatchLogDto> pushMatchLogDtoList) {
        if (CollectionUtil.isEmpty(pushMatchLogDtoList)) {
            return;
        }
        pushMatchLogDtoList.forEach(pushMatchLogDto -> {
            if (Objects.isNull(pushMatchLogDto.getIsMatchSuccess()) || pushMatchLogDto.getIsMatchSuccess() == 0) {
                pushMatchLogDto.setIsFilter(null);
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsFilter()) || pushMatchLogDto.getIsFilter() == 1) {
                pushMatchLogDto.setIsDistribute(null);
                pushMatchLogDto.setIsAutoGrabSuccess(null);
                return;
            }
            if (Objects.isNull(pushMatchLogDto.getIsDistribute()) || pushMatchLogDto.getIsDistribute() == 0) {
                pushMatchLogDto.setIsAutoGrabSuccess(null);
            }
        });
    }


    /**
     * 合作经营派单日志构建统一匹配日志
     * @param cooperationBusinessMasterMatchLogList
     * @return
     */
    private List<CooperationBusinessPushMatchLogDto> buildPushMatchLogDtoListByCooperationBusinessMasterMatchLog(List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {
        if (CollectionUtil.isEmpty(cooperationBusinessMasterMatchLogList)) {
            return null;
        }
        List<CooperationBusinessPushMatchLogDto> pushMatchLogDtoList = Lists.newArrayList();
        for (CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog : cooperationBusinessMasterMatchLogList) {
            CooperationBusinessPushMatchLogDto pushMatchLogDto = new CooperationBusinessPushMatchLogDto();
            pushMatchLogDto.setId(cooperationBusinessMasterMatchLog.getId());
            pushMatchLogDto.setMatchType(PushLogMatchType.COOPERATION_BUSINESS.getDesc());
            pushMatchLogDto.setOrderNo(cooperationBusinessMasterMatchLog.getOrderNo());
            pushMatchLogDto.setOrderCreateTime(cooperationBusinessMasterMatchLog.getOrderCreateTime());
            pushMatchLogDto.setMasterId(cooperationBusinessMasterMatchLog.getMasterId());
            pushMatchLogDto.setIsMatchSuccess(cooperationBusinessMasterMatchLog.getIsMatchSuccess());
            pushMatchLogDto.setMatchFailReason(cooperationBusinessMasterMatchLog.getMatchFailReason());
            pushMatchLogDto.setIsFilter(cooperationBusinessMasterMatchLog.getIsFilter());
            pushMatchLogDto.setFilterReason(cooperationBusinessMasterMatchLog.getFilterReason());
            pushMatchLogDto.setIsDistribute(cooperationBusinessMasterMatchLog.getIsDistribute());
            pushMatchLogDto.setDistributeRule(cooperationBusinessMasterMatchLog.getDistributeRule());
            pushMatchLogDto.setIsAutoGrabSuccess(cooperationBusinessMasterMatchLog.getIsAutoGrabSuccess());
            pushMatchLogDto.setAutoGrabFailReason(cooperationBusinessMasterMatchLog.getAutoGrabFailReason());
            pushMatchLogDto.setOrderVersion(cooperationBusinessMasterMatchLog.getOrderVersion());
            pushMatchLogDto.setCreateTime(cooperationBusinessMasterMatchLog.getCreateTime());
            pushMatchLogDtoList.add(pushMatchLogDto);
        }
        return pushMatchLogDtoList;
    }

    /**
     * 总包直接指派日志构建统一匹配日志
     * @param enterpriseOrderAgreementMasterMatchList
     * @return
     */
    private List<EnterpriseAppointPushMatchLogDto> buildPushMatchLogDtoListByEnterpriseOrderAgreementMasterMatch(List<EnterpriseOrderAgreementMasterMatch> enterpriseOrderAgreementMasterMatchList) {
        if (CollectionUtil.isEmpty(enterpriseOrderAgreementMasterMatchList)) {
            return null;
        }
        List<EnterpriseAppointPushMatchLogDto> pushMatchLogDtoList = Lists.newArrayList();
        for (EnterpriseOrderAgreementMasterMatch enterpriseOrderAgreementMasterMatch : enterpriseOrderAgreementMasterMatchList) {
            EnterpriseAppointPushMatchLogDto pushMatchLogDto = new EnterpriseAppointPushMatchLogDto();
            pushMatchLogDto.setId(enterpriseOrderAgreementMasterMatch.getId());
            pushMatchLogDto.setMatchType(PushLogMatchType.ENTERPRISE_APPOINT.getDesc());
            pushMatchLogDto.setOrderNo(enterpriseOrderAgreementMasterMatch.getOrderNo());
            pushMatchLogDto.setUserId(enterpriseOrderAgreementMasterMatch.getUserId());
            pushMatchLogDto.setOrderCreateTime(enterpriseOrderAgreementMasterMatch.getOrderCreateTime());
            pushMatchLogDto.setMasterId(enterpriseOrderAgreementMasterMatch.getMasterId());
            pushMatchLogDto.setRecruitId(enterpriseOrderAgreementMasterMatch.getRecruitId());
            pushMatchLogDto.setIsMatchSuccess(enterpriseOrderAgreementMasterMatch.getIsMatchSucc());
            pushMatchLogDto.setMatchFailReason(enterpriseOrderAgreementMasterMatch.getMatchFailReason());
            pushMatchLogDto.setIsCalculatePriceSuccess(enterpriseOrderAgreementMasterMatch.getIsCalculatePriceSucc());
            pushMatchLogDto.setCalculatePriceFailReason(enterpriseOrderAgreementMasterMatch.getCalculatePriceFailReason());
            pushMatchLogDto.setIsFilter(enterpriseOrderAgreementMasterMatch.getIsFilter());
            pushMatchLogDto.setFilterReason(enterpriseOrderAgreementMasterMatch.getFilterReason());
            pushMatchLogDto.setIsDistribute(enterpriseOrderAgreementMasterMatch.getIsDistribute());
            pushMatchLogDto.setDistributeRule(enterpriseOrderAgreementMasterMatch.getDistributeRule());
            pushMatchLogDto.setOrderVersion(enterpriseOrderAgreementMasterMatch.getOrderVersion());
            pushMatchLogDto.setCreateTime(enterpriseOrderAgreementMasterMatch.getCreateTime());
            pushMatchLogDtoList.add(pushMatchLogDto);
        }
        return pushMatchLogDtoList;
    }


    /**
     * 平台协议派单日志构建统一匹配日志
     * @param agreementMasterMatchList
     * @return
     */
    private List<UserAgreementPushMatchLogDto> buildPushMatchLogDtoListByAgreementMasterMatch(List<AgreementMasterMatch> agreementMasterMatchList) {
        if (CollectionUtil.isEmpty(agreementMasterMatchList)) {
            return null;
        }
        List<UserAgreementPushMatchLogDto> pushMatchLogDtoList = Lists.newArrayList();
        for (AgreementMasterMatch agreementMasterMatch : agreementMasterMatchList) {
            UserAgreementPushMatchLogDto pushMatchLogDto = new UserAgreementPushMatchLogDto();
            pushMatchLogDto.setId(agreementMasterMatch.getId());
            pushMatchLogDto.setMatchType(PushLogMatchType.USER_AGREEMENT.getDesc());
            pushMatchLogDto.setOrderNo(agreementMasterMatch.getOrderNo());
            pushMatchLogDto.setUserId(agreementMasterMatch.getUserId());
            pushMatchLogDto.setOrderCreateTime(agreementMasterMatch.getOrderCreateTime());
            pushMatchLogDto.setMasterId(agreementMasterMatch.getMasterId());
            pushMatchLogDto.setRecruitId(agreementMasterMatch.getRecruitId());
            pushMatchLogDto.setIsMatchSuccess(agreementMasterMatch.getIsMatchSucc());
            pushMatchLogDto.setMatchFailReason(agreementMasterMatch.getMatchFailReason());
            pushMatchLogDto.setIsCalculatePriceSuccess(agreementMasterMatch.getIsCalculatePriceSucc());
            pushMatchLogDto.setCalculatePriceFailReason(agreementMasterMatch.getCalculatePriceFailReason());
            pushMatchLogDto.setIsFilter(agreementMasterMatch.getIsFilter());
            pushMatchLogDto.setFilterReason(agreementMasterMatch.getFilterReason());
            pushMatchLogDto.setIsDistribute(agreementMasterMatch.getIsDistribute());
            pushMatchLogDto.setDistributeRule(agreementMasterMatch.getDistributeRule());
            pushMatchLogDto.setIsAutoGrabSuccess(agreementMasterMatch.getIsAutoReceiveSucc());
            pushMatchLogDto.setAutoGrabFailReason(agreementMasterMatch.getAutoReceiveFailReason());
            pushMatchLogDto.setOrderVersion(agreementMasterMatch.getOrderVersion());
            pushMatchLogDto.setCreateTime(agreementMasterMatch.getCreateTime());
            pushMatchLogDtoList.add(pushMatchLogDto);
        }
        return pushMatchLogDtoList;
    }



    /**
     * 平台协议派单日志构建统一匹配日志
     * @param orderFullTimeMasterMasterMatchLogs
     * @return
     */
    private List<OrderFullTimeMasterMatchLogDto> buildPushMatchLogDtoListByFullTimeMasterMatch(List<OrderFullTimeMasterMasterMatchLog> orderFullTimeMasterMasterMatchLogs) {
        if (CollectionUtil.isEmpty(orderFullTimeMasterMasterMatchLogs)) {
            return null;
        }
        List<OrderFullTimeMasterMatchLogDto> pushMatchLogDtoList = Lists.newArrayList();
        for (OrderFullTimeMasterMasterMatchLog matchLog : orderFullTimeMasterMasterMatchLogs) {
            OrderFullTimeMasterMatchLogDto pushMatchLogDto = new OrderFullTimeMasterMatchLogDto();
            pushMatchLogDto.setId(pushMatchLogDto.getId());
            pushMatchLogDto.setMatchType(PushLogMatchType.FULL_TIME_MASTER.getDesc());
            pushMatchLogDto.setOrderNo(matchLog.getOrderNo());
            pushMatchLogDto.setOrderCreateTime(matchLog.getOrderCreateTime());
            pushMatchLogDto.setMasterId(matchLog.getMasterId());
            pushMatchLogDto.setIsMatchSuccess(matchLog.getIsMatchSucc());
            pushMatchLogDto.setMatchFailReason(matchLog.getMatchFailReason());
            pushMatchLogDto.setIsFilter(matchLog.getIsFilter());
            pushMatchLogDto.setFilterReason(matchLog.getFilterReason());
            pushMatchLogDto.setIsDistribute(matchLog.getIsDistribute());
            pushMatchLogDto.setDistributeRule(matchLog.getDistributeRule());
            pushMatchLogDto.setIsAutoGrabSuccess(matchLog.getIsAutoReceiveSucc());
            pushMatchLogDto.setAutoGrabFailReason(matchLog.getAutoReceiveFailReason());
            pushMatchLogDto.setOrderVersion(matchLog.getOrderVersion());
            pushMatchLogDto.setCreateTime(matchLog.getCreateTime());
            pushMatchLogDtoList.add(pushMatchLogDto);
        }
        return pushMatchLogDtoList;
    }

    /**
     * 样板城市派单日志构建统一匹配日志
     * @param newModelMatchDetailList
     * @return
     */
    private List<NewModelCityPushMatchLogDto> buildPushMatchLogDtoListByNewModelMatchDetail(List<NewModelMatchDetail> newModelMatchDetailList) {
        if (CollectionUtil.isEmpty(newModelMatchDetailList)) {
            return null;
        }
        List<NewModelCityPushMatchLogDto> pushMatchLogDtoList = Lists.newArrayList();
        for (NewModelMatchDetail newModelMatchDetail : newModelMatchDetailList) {
            NewModelCityPushMatchLogDto pushMatchLogDto = new NewModelCityPushMatchLogDto();
            pushMatchLogDto.setId(newModelMatchDetail.getId());
            pushMatchLogDto.setMatchType(PushLogMatchType.NEW_MODEL_CITY.getDesc());
            pushMatchLogDto.setOrderNo(newModelMatchDetail.getOrderNo());
            pushMatchLogDto.setOrderCreateTime(newModelMatchDetail.getOrderCreateTime());
            pushMatchLogDto.setMasterId(newModelMatchDetail.getMasterId());
            pushMatchLogDto.setIsMainMaster(newModelMatchDetail.getIsMainMaster());
            pushMatchLogDto.setIsMatchSuccess(1);
            pushMatchLogDto.setIsFilter(newModelMatchDetail.getIsFilterOut());
            pushMatchLogDto.setFilterReason(newModelMatchDetail.getFilterRemark());
            pushMatchLogDto.setIsDistribute(newModelMatchDetail.getIsDistribute());
            pushMatchLogDto.setDistributeRule(newModelMatchDetail.getIsFilterOut() == 0
                    && newModelMatchDetail.getIsDistribute() == 0
                    && Strings.isNullOrEmpty(newModelMatchDetail.getDistributeRemark()) ? "分配其他高优先级师傅" :
                    newModelMatchDetail.getDistributeRemark());
            pushMatchLogDto.setIsAutoGrabSuccess(newModelMatchDetail.getDistributeResult());
            pushMatchLogDto.setAutoGrabFailReason(newModelMatchDetail.getDistributeFailRemark());
            pushMatchLogDto.setAbandonTime(newModelMatchDetail.getAbandonTime());
            pushMatchLogDto.setOrderVersion(newModelMatchDetail.getOrderVersion());
            pushMatchLogDto.setCreateTime(newModelMatchDetail.getCreateTime());
            pushMatchLogDtoList.add(pushMatchLogDto);
        }
        return pushMatchLogDtoList;
    }
}
