package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMaster;
import com.wanshifu.master.order.push.domain.enums.MasterType;
import com.wanshifu.master.order.push.domain.po.MasterPush;
import com.wanshifu.master.order.push.domain.po.OrderMasterPush;
import com.wanshifu.master.order.push.mapper.OrderMasterPushMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;


@Repository
public class OrderMasterPushRepository extends BaseRepository<OrderMasterPush> {


    /**
     * 分表控制标记
     */
    private static String TIME_FLAG = "0";

    @Resource
    private OrderMasterPushMapper orderMasterPushMapper;


    public List<OrderMasterPush> getOrderMasterPushList(Long orderId, String orderVersion,Set<String> roundsSet, Integer isAlternate) {
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
        return orderMasterPushMapper.getOrderMasterPushList(orderId,orderVersion,timeMark,roundsSet,isAlternate);
    }



//    /**
//     * 入库待推送列表
//     *
//     * @param orderId
//     * @param newMasterList
//     * @param oldMasterList
//     */
//    public int insertMasterPushList(String orderVersion, Long orderId, List<PushMaster> newMasterList,
//                                    List<PushMaster> oldMasterList,int batchSize) {
//
//        int count = 0;
//
//        //获取订单时间标记
//        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
//
//        if (!TIME_FLAG.equals(timeMark)) {
//            String tableName = "master_push_" + DateFormatterUtil.getNowInt();
//            masterPuhMapsper.createMasterPushTable(tableName);
//            TIME_FLAG = timeMark;
//        }
//
//        List<MasterPush> masterPushList = new ArrayList<>();
//
//
//        if(CollectionUtil.isNotEmpty(newMasterList)){
//            for (int i = 0; i < newMasterList.size(); i++) {
//                MasterPush masterPush = new MasterPush();
//                masterPush.setOrderId(orderId);
//                masterPush.setOrderVersion(orderVersion);
//                masterPush.setMasterType(MasterType.MASETR_NEW.code);
//                masterPush.setListOffset(i);
//                masterPush.setMasterId(Long.valueOf(newMasterList.get(i).getMasterId()));
//                masterPush.setScore(newMasterList.get(i).getScore());
//                masterPush.setCreateTime(new Date());
//                masterPush.setUpdateTime(new Date());
//                masterPushList.add(masterPush);
//            }
//
//        }
//
//
//
//        if(CollectionUtil.isNotEmpty(oldMasterList)){
//            for (int i = 0; i < oldMasterList.size(); i++) {
//                MasterPush masterPush = new MasterPush();
//                masterPush.setOrderId(orderId);
//                masterPush.setOrderVersion(orderVersion);
//                masterPush.setMasterType(MasterType.MASTER_OLD.code);
//                masterPush.setListOffset(i);
//                masterPush.setMasterId(Long.valueOf(oldMasterList.get(i).getMasterId()));
//                masterPush.setScore(oldMasterList.get(i).getScore());
//                masterPush.setCreateTime(new Date());
//                masterPush.setUpdateTime(new Date());
//                masterPushList.add(masterPush);
//            }
//
//        }
//
//        if(CollectionUtils.isNotEmpty(masterPushList)){
//            count = batchInsertMasterPushList(masterPushList,batchSize,timeMark);
//        }
//
//        return count;
//
//    }
//
//
//
//    /**
//     * 入库待推送列表
//     *
//     * @param orderId
//     * @param newMasterList
//     * @param oldMasterList
//     */
//    public int insertMasterPushList(String orderVersion, Long orderId, List<PushMaster> pushMasterList,int batchSize) {
//
//        int count = 0;
//
//        //获取订单时间标记
//        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
//
//        if (!TIME_FLAG.equals(timeMark)) {
//            String tableName = "master_push_" + DateFormatterUtil.getNowInt();
//            masterPushMapper.createMasterPushTable(tableName);
//            TIME_FLAG = timeMark;
//        }
//
//        if(CollectionUtil.isNotEmpty(pushMasterList)){
//            List<MasterPush> masterPushList = new ArrayList<>();
//            for (int i = 0; i < pushMasterList.size(); i++) {
//                MasterPush masterPush = new MasterPush();
//                masterPush.setOrderId(orderId);
//                masterPush.setOrderVersion(orderVersion);
//                masterPush.setMasterType(pushMasterList.get(i).isNewMaster() ? MasterType.MASETR_NEW.code : MasterType.MASTER_OLD.code);
//                masterPush.setPushOffset(i);
//                masterPush.setMasterId(Long.valueOf(pushMasterList.get(i).getMasterId()));
//                masterPush.setScore(pushMasterList.get(i).getScore());
//                masterPush.setCreateTime(new Date());
//                masterPush.setUpdateTime(new Date());
//                masterPushList.add(masterPush);
//            }
//            count = batchInsertMasterPushList(masterPushList,batchSize,timeMark);
//        }
//
//        return count;
//
//    }
//
    public int insertOrderMasterPushList(Long orderId,String orderVersion,List<DynamicRoundsMaster> dynamicRoundsMasterList ,
                                         int batchSize){

        //获取订单时间标记
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));

        if (!TIME_FLAG.equals(timeMark)) {
            String tableName = "order_master_push_" + DateFormatterUtil.getNowInt();
            orderMasterPushMapper.createOrderMasterPushTable(tableName);
            TIME_FLAG = timeMark;
        }


        int masterListSize = 0;
        Date currentDate = new Date();
        for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList){
            String rounds = dynamicRoundsMaster.getRounds();
            List<OrderMasterPush> orderMasterPushList = new ArrayList<>();
            dynamicRoundsMaster.getMasterList().forEach(pushMaster -> {
                OrderMasterPush orderMasterPush = new OrderMasterPush();
                orderMasterPush.setOrderId(orderId);
                orderMasterPush.setOrderVersion(orderVersion);
                orderMasterPush.setRounds(rounds);
                orderMasterPush.setMasterId(pushMaster.getMasterId());
                orderMasterPush.setScore(pushMaster.getScore());
                orderMasterPush.setIsAlternative(0);
                orderMasterPush.setIsPriorityPush((Objects.nonNull(pushMaster.isPriorityPush()) && pushMaster.isPriorityPush()) ? 1 : 0);
                orderMasterPush.setMasterType(pushMaster.isNewMaster() ? MasterType.MASETR_NEW.code : MasterType.MASTER_OLD.code);
                orderMasterPush.setPushTime(currentDate);
                orderMasterPushList.add(orderMasterPush);
            });

            dynamicRoundsMaster.getAlternativeMasterList().forEach(pushMaster -> {
                OrderMasterPush orderMasterPush = new OrderMasterPush();
                orderMasterPush.setOrderId(orderId);
                orderMasterPush.setOrderVersion(orderVersion);
                orderMasterPush.setRounds(rounds);
                orderMasterPush.setMasterId(pushMaster.getMasterId());
                orderMasterPush.setScore(pushMaster.getScore());
                orderMasterPush.setIsAlternative(1);
                orderMasterPush.setIsPriorityPush((Objects.nonNull(pushMaster.isPriorityPush()) && pushMaster.isPriorityPush()) ? 1 : 0);
                orderMasterPush.setMasterType(pushMaster.isNewMaster() ? MasterType.MASETR_NEW.code : MasterType.MASTER_OLD.code);
                orderMasterPushList.add(orderMasterPush);
            });

            masterListSize = masterListSize + orderMasterPushList.size();

            if(CollectionUtils.isNotEmpty(orderMasterPushList)){
                List<List<OrderMasterPush>> orderMasterPushBatchList = LocalCollectionsUtil.groupByBatch(orderMasterPushList, batchSize);
                for(List<OrderMasterPush> batch : orderMasterPushBatchList){
                    orderMasterPushMapper.insertOrderMasterPushList(batch,timeMark);
                }
            }
        }


        return masterListSize;
    }


//    public int updatePushTime(List<PushMaster> pushMasterList,String timeMark){
//        int batchSize = 100;
//        List<List<PushMaster>> pushMasterBatchList = LocalCollectionsUtil.groupByBatch(pushMasterList, batchSize);
//        for(List<PushMaster> batch : pushMasterBatchList){
//            Set<>
//            orderMasterPushMapper.updatePushTime(batch,timeMark);
//        }
//    }


    public int dropHistoryOrderMasterPushTable(String tableName){
        return orderMasterPushMapper.dropHistoryOrderMasterPushTable(tableName);
    }


    public List<OrderMasterPush> selectPushScore(Long orderId, String orderVersion, List<Long> masterIdList){
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
        return orderMasterPushMapper.selectPushScore(orderId,orderVersion,masterIdList,timeMark);
    }




}
