package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderScoreItemValue;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27 18:02
 */
@Repository
public class OrderScoreItemValueRepository extends BaseRepository<OrderScoreItemValue> {

    public List<OrderScoreItemValue> selectByScoreItemIds(List<Long> scoreItemIds) {
        if(CollectionUtils.isEmpty(scoreItemIds)){
            return Collections.emptyList();
        }
        Example example = new Example(OrderScoreItemValue.class);
        example.createCriteria().andIn("scoreItemId",scoreItemIds);
        return this.selectByExample(example);
    }
}
