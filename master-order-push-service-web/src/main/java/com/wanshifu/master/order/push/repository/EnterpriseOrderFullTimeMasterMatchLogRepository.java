package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.EnterpriseOrderAgreementMasterMatch;
import com.wanshifu.master.order.push.domain.po.EnterpriseOrderFullTimeMasterMatchLog;
import com.wanshifu.master.order.push.mapper.EnterpriseOrderAgreementMasterMatchMapper;
import com.wanshifu.master.order.push.mapper.EnterpriseOrderFullTimeMasterMatchLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class EnterpriseOrderFullTimeMasterMatchLogRepository extends BaseRepository<EnterpriseOrderFullTimeMasterMatchLog> {

    @Resource
    private EnterpriseOrderFullTimeMasterMatchLogMapper enterpriseOrderFullTimeMasterMatchLogMapper;


}
