package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

@Data
@Document(indexAlias = "exclusive_master", type = "exclusive_master")
public class ExclusiveMaster {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "recruit_id")
    private String recruitId;

    @Column(name = "master_id")
    private String masterId;

    @Column(name = "cooperation_start_time")
    private Long cooperationStartTime;

    @Column(name = "cooperation_end_time")
    private Long cooperationEndTime;

    @Column(name = "select_serve_ids")
    private String selectServeIds;

    @Column(name = "is_exclusive_status_normal")
    private Integer isExclusiveStatusNormal;

    @Column(name = "lv3_division_ids")
    private String lv3DivisionIds;

    @Column(name = "lv4_division_ids")
    private String lv4DivisionIds;

    @Column(name = "expect_appoint_number")
    private Long expectAppointNumber;

    @Column(name = "update_time")
    private Date updateTime;

}
