package com.wanshifu.master.order.push.service.impl;


import com.wanshifu.master.order.push.domain.dto.AutoReceiveMaster;

import java.util.Comparator;
import java.util.Random;

public class AutoReceiveMasterScoreComparator  implements Comparator<AutoReceiveMaster>{

    @Override
    public int compare(AutoReceiveMaster o1, AutoReceiveMaster o2){

        int result = o1.getMasterScore().compareTo(o2.getMasterScore());
        if(result != 0){
            if(result > 0){
                return -1;
            }else{
                return 1;
            }
        }else{
            Random random = new Random();
            int randomNum = random.nextInt(2);
            if(randomNum == 0){
                return -1;
            }else{
                return 1;
            }
        }

    }
}
