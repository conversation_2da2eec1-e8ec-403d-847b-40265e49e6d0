package com.wanshifu.master.order.push.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.api.OrderMessageApi;
import com.wanshifu.master.order.push.domain.api.response.GetMstOrderCancelCntIn30MinResp;
import com.wanshifu.master.order.push.domain.api.response.MasterGroupResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetMstOrderCancelCntIn30MinRqt;
import com.wanshifu.master.order.push.domain.api.rqt.GetPersonGroupByMasterIdsRqt;
import com.wanshifu.master.order.push.domain.common.PushCommonObject;
import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.common.PushParameter;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.message.*;
import com.wanshifu.master.order.push.domain.po.Master;
import com.wanshifu.master.order.push.domain.po.PushShuntRule;
import com.wanshifu.master.order.push.domain.resp.exclusive.GetExclusiveOrderInfoResp;
import com.wanshifu.master.order.push.domain.rqt.*;
import com.wanshifu.master.order.push.domain.rqt.exclusive.GetExclusiveOrderInfoRqt;
import com.wanshifu.master.order.push.domain.rqt.pushShuntRule.CreateRqt;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply.LongTailStrategyTuple;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.MasterRepository;
import com.wanshifu.master.order.push.repository.PushRecordFacade;
import com.wanshifu.master.order.push.repository.PushShuntRuleRepository;
import com.wanshifu.master.order.push.service.impl.AgentMasterMatcher;
import com.wanshifu.master.order.push.service.impl.ApolloConfigUtils;
import com.wanshifu.master.order.push.service.impl.FeatureRepository;
import com.wanshifu.master.order.push.util.DateFormatterUtil;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.mq.comsumeController.OrderMatchMasterConsumeController;
import com.wanshifu.mq.kafka.KafkaMessageSender;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 推单Queue
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PushQueueService {

	@Resource
	private RocketMqSendService rocketMqSendService;

	@Autowired
	private PushRecordFacade pushRecordFacade;

	/**
	 * 推单结果topic
	 */
	@Value("${wanshifu.rocketMQ.order-push-result-topic}")
	private String orderPushResultTopic;


	/**
	 * 推单mq
	 */
	@Value("${wanshifu.rocketMQ.order-push-topic}")
	private String orderPushTopic;


	/**
	 * 原推单mq topic
	 */
	@Value("${wanshifu.rocketMQ.order-smart-topic}")
	private String orderSmartTopic;


	/**
	 * 推单结果mq tag
	 */
	public final String TAG = "bigData_push";

	/**
	 * 团队师傅推单
	 */
	public final String PACKAGE_ORDER_PUSH_TAG = "package_order_push";


	@Value("${wanshifu.rocketMQ.order-match-master-topic}")
	private String orderMatchMasterTopic;


	private final String ORDER_MATCH_MASTER = "order_match_master";

	@Resource
	private OrderMasterCalculateService orderMasterCalculateService;

	private final String MATCH_RESULT = "match_result";


	@Resource
	private OrderMessageApi orderMessageApi;

//	@Resource
//	private OTSMasterSelector ;


	@Resource
	private MasterRepository masterRepository;

	@Resource
	private KafkaMessageSender kafkaMessageSender;

	@Value("${push.detail.upload.switch:off}")
	private String pushDetailUploadSwitch;


	@Value("${push.detail.upload.batch.size:1}")
	private Integer pushDetailUploadBatchSize;

    @Value("${longTail.push.master.size.limit:3000}")
    private int longTailPushMasterSizeLimit;

    @Value("${filter4UserInviteMasterOfferPrice.limit:50}")
    private int filter4UserInviteMasterOfferPriceLimit;

	@Value("${filter4UserInviteMasterOfferPrice.orderCancelCntIn30min:1}")
	private int contactableOrderCancelCntIn30min;



    @Resource
	private BigdataOpenServiceApi bigdataOpenServiceApi;

	@Resource
	private MasterBaseEsRepository masterBaseEsRepository;

	@Resource
	private ApolloConfigUtils apolloConfigUtils;


    /**
     * 发送推单mq消息
     *
     * @param masterOrderId
     * @param masterIds
     * @param totalPushedMasterNum
     * @param firstPush
     * @param pushAddress
     * @param businessLineId
     * @param commonFeature
     * @param masterSourceType     推送师傅来源类型，tob: B端师傅,toc: C端师傅
     * @return
     */
    public boolean pushByQueue(Long masterOrderId, String masterIds,
                               int totalPushedMasterNum, String firstPush, String pushAddress,
                               String businessLineId, JSONObject commonFeature, List<PushMaster> pushMasterList,
                               Integer firstTimeValidPush, Integer normalFirstTimeValidPush, Integer normalFirstMatchMasterNum,
                               String masterSourceType,OrderDetailData orderDetailData) {

        log.info("pushByQueue masterIds:" + masterIds);

        if (!FieldConstant.CONFIRM_ORDER_PACKAGE.equals(commonFeature.getString(FieldConstant.OPERATE_FLAG))) {
            return pushByQueueNew(masterOrderId, masterIds, totalPushedMasterNum, firstPush, pushAddress,
                    businessLineId, commonFeature, pushMasterList,
                    firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum, masterSourceType,orderDetailData);
        }

        // 下面不用看了，没用了
        log.info("pushByQueue masterIds:" + masterIds);

        final String pushTime = DateFormatterUtil.getNowSp();
        JSONObject pushContent = new JSONObject();
        pushContent.put("orderId", masterOrderId);
        if (masterOrderId == null) {
            pushContent.put("globalOrderTraceId", commonFeature.getLong(FieldConstant.GLOBAL_ORDER_ID));
        }
        pushContent.put("masterIds", masterIds);
        pushContent.put("pushNumber", totalPushedMasterNum);
        pushContent.put("pushTime", pushTime);
        pushContent.put("firstPush", Integer.valueOf(firstPush));
        pushContent.put("isNewOrderPush", 1);
        pushContent.put("pushAddress", Integer.valueOf(pushAddress));
        pushContent.put("businessLineId", Integer.valueOf(businessLineId));
        String pushMode = null;
        String myTag = TAG;
        if (commonFeature != null) {
            pushMode = commonFeature.getString(FieldConstant.PUSH_MODE);
            if (pushMode != null) {
                pushContent.put(FieldConstant.PUSH_MODE, pushMode);
                if (PushMode.PACKAGE_ORDER.code.equals(pushMode)) {
                    final String operateFlag = commonFeature.getString(FieldConstant.OPERATE_FLAG);
                    final String packageId = commonFeature.getString(FieldConstant.PACKAGE_ID);
                    pushContent.put(FieldConstant.PACKAGE_ID, packageId);
                    pushContent.put(FieldConstant.GOODS_ATTRIBUTE, commonFeature.getString(FieldConstant.PACKAGE_ATTRIBUTE_ID));
                    pushContent.put(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE,
                            commonFeature.getJSONObject(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE));
                    if (FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag)) {
                        myTag = PACKAGE_ORDER_PUSH_TAG;
                        pushContent.put(FieldConstant.PACKAGE_MODE, FieldConstant.PACKAGE);
                    }
                }

                if (PushMode.RECOMMEND_LIST.code.equals(pushMode)) {
                    myTag = "recommend_match_result";
                }

            }
            final Object recruitId = commonFeature.get(FieldConstant.RECRUIT_ID);
            if (recruitId != null) {
                pushContent.put(FieldConstant.RECRUIT_ID, recruitId);
            }
            final Object hasPrice = commonFeature.get(FieldConstant.HAS_PRICE);
            if (hasPrice != null) {
                pushContent.put(FieldConstant.HAS_PRICE, hasPrice);
            }
            final Object teamMasterOrderPush = commonFeature.get(FieldConstant.TEAM_MASTER_ORDER_PUSH);
            if (teamMasterOrderPush != null) {
                pushContent.put(FieldConstant.TEAM_MASTER_ORDER_PUSH, teamMasterOrderPush);
            }

            Integer pushFlag = commonFeature.getInteger(FieldConstant.PUSH_FLAG);
            if (pushFlag != null) {
                pushContent.put("pushFlag", pushFlag);
            }
            final Boolean accordingDistancePush = commonFeature.getBoolean(FieldConstant.IS_ACCORDING_DISTANCE_PUSH);
            Optional.ofNullable(accordingDistancePush).ifPresent(it -> pushContent.put("accordingDistancePush", it));

            if ("agent".equals(pushMode)) {
                pushContent.put(AgentMasterMatcher.AGENT_MASTER_LIST, commonFeature.get(AgentMasterMatcher.AGENT_MASTER_LIST));
            }
        }
        rocketMqSendService.sendSyncMessage(orderPushResultTopic, myTag, pushContent.toJSONString());
        //推送计数
//		pushRecordFacade.orderPushCount(pushTime,new HashSet<>(Arrays.asList(masterIds.split(SymbolConstant.COMMA))));

        pushRecordFacade.saveOrderPush(String.valueOf(masterOrderId), pushTime, masterIds, StringUtils.isNotBlank(pushMode) ? pushMode : PushMode.NORMAL.code);


        return true;
    }


	/**
	 * 发送分轮推单mq消息
	 * @param pushParameter
	 * @param orderVersion
	 * @param delayTime
	 * @param commonFeature
	 * @param pushCommonObject
	 * @return
	 */
	public boolean sendDelayOrder(PushParameter pushParameter, String orderVersion, Integer delayTime, JSONObject commonFeature, PushCommonObject pushCommonObject) {
		Long userOrderId=pushParameter.getUserOrderId();
		Long masterOrderId=pushParameter.getMasterOrderId();
		Long globalOrderId=pushParameter.getGlobalOrderId();
		Long enterpriseOrderId=pushParameter.getEnterpriseOrderId();
		String tag = "delay_push";

		JSONObject content = new JSONObject();
		content.put("userOrderId", userOrderId);
		content.put("masterOrderId", masterOrderId);
		content.put("globalOrderId", globalOrderId);
		content.put("enterpriseOrderId", enterpriseOrderId);
		content.put("orderVersion", orderVersion);
		content.put("commonFeature", commonFeature);
		content.put("pushConfig", pushCommonObject.getPushConfig());
		content.put("orderDetailData", pushCommonObject.getOrderDetailData());
		content.put("baseSelect", pushCommonObject.getBaseSelect());
		rocketMqSendService.sendDelayMessage(orderPushTopic,tag,content.toJSONString(),delayTime * 60 * 1000L );
		return true;
	}


	public boolean sendTimingOrder(Long timing, String  content) {
		String tag = "order_push";
		rocketMqSendService.sendSyncMessage(orderPushTopic,tag,content);
		return true;
	}


	/**
	 * 发送推单mq消息（原推单系统消费）
	 * @param content
	 * @return
	 */
	public boolean sendSmartPushMsg(String  content) {
		String tag = "smc_master_list";
		rocketMqSendService.sendSyncMessage(orderSmartTopic,tag,content);
		return true;
	}


	/**
	 * 发送重推mq消息
	 * @param repushPolicySnapshotId
	 * @param orderVersion
	 * @param orderPushRqt
	 * @param bestOfferNum
	 * @param delayTime
	 * @return
	 */
	public boolean sendRepushMessage(Long repushPolicySnapshotId , String orderVersion, OrderPushRqt orderPushRqt, Integer bestOfferNum, Long delayTime,Integer delayTimeMinute,
									 String handoffTag,String masterSourceType){
		RepushOrderRqt repushOrderRqt = new RepushOrderRqt();
		BeanUtils.copyProperties(orderPushRqt,repushOrderRqt);
		repushOrderRqt.setBestOfferNum(bestOfferNum);
		repushOrderRqt.setOrderVersion(orderVersion);
		repushOrderRqt.setRepushPolicySnapshotId(repushPolicySnapshotId);
		repushOrderRqt.setDelayTimeMinute(delayTimeMinute);
		repushOrderRqt.setHandoffTag(handoffTag);
		repushOrderRqt.setMasterSourceType(masterSourceType);
		rocketMqSendService.sendDelayMessage(orderPushTopic,"repush_order", JSON.toJSONString(repushOrderRqt),delayTime);
		return true;
	}


	/**
	 * 发送重推mq消息
	 * @param repushPolicySnapshotId
	 * @param orderVersion
	 * @param orderDetailData
	 * @param bestOfferNum
	 * @param delayTime
	 * @return
	 */
	public boolean sendAfreshPushMessage(Long repushPolicySnapshotId , String orderVersion, OrderDetailData orderDetailData, Integer bestOfferNum, Long delayTime,Integer delayTimeMinute,
									 String handoffTag){
		OrderAfreshPushRqt orderAfreshPushRqt = new OrderAfreshPushRqt();
		orderAfreshPushRqt.setOrderDetailData(orderDetailData);
		orderAfreshPushRqt.setBestOfferNum(bestOfferNum);
		orderAfreshPushRqt.setOrderVersion(orderVersion);
		orderAfreshPushRqt.setRepushPolicySnapshotId(repushPolicySnapshotId);
		orderAfreshPushRqt.setDelayTimeMinute(delayTimeMinute);
		orderAfreshPushRqt.setHandoffTag(handoffTag);
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,"afresh_push", JSON.toJSONString(orderAfreshPushRqt),delayTime);
		return true;
	}

    /**
     * 家庭协议师傅推送后多少分钟触发推普通师傅
     * @param orderDetailData
     * @param delayTime
     * @return
     */
    public boolean sendFamilyAgreementPushMessage(OrderDetailData orderDetailData, Long delayTime) {

        FamilyAgreementRePushNormalRqt familyAgreementRePushNormalRqt = new FamilyAgreementRePushNormalRqt();
        familyAgreementRePushNormalRqt.setMasterOrderId(orderDetailData.getMasterOrderId());
        familyAgreementRePushNormalRqt.setMasterSourceType(MasterSourceType.TOC.code);
        familyAgreementRePushNormalRqt.setBusinessLineId(orderDetailData.getBusinessLineId());
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic, "family_agreement_re_push_normal", JSON.toJSONString(familyAgreementRePushNormalRqt), delayTime);
        return true;
    }

    /**
     * 代理商推单后，查看后多少分钟无人接单推普通师傅
     * @param orderId
     * @param appointType
     * @param pushTime
     * @param delayTime
     * @return
     */
    public boolean sendAgentPushMessage(Long orderId, Integer appointType, Date pushTime, Long delayTime) {

        AgentPushRqt agentPushRqt = new AgentPushRqt();
        agentPushRqt.setMasterOrderId(orderId);
        agentPushRqt.setAppointType(appointType);
        agentPushRqt.setPushTime(pushTime);
        agentPushRqt.setTouchType("push");
        log.info("sendAgentPushMessage by push,sendAgentPushMessage:{}", JSONUtil.toJsonStr(agentPushRqt));
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic, "agent_push", JSON.toJSONString(agentPushRqt), delayTime);
        return true;
    }



	public boolean sendNearbyPushMessage(OrderDetailData orderDetailData, String orderVersion, NearbyPushRuleConfig nearbyPushRuleConfig, Integer maxOfferNum, Long delayTime){
		NearbyPushRqt nearbyPushRqt = new NearbyPushRqt();
		nearbyPushRqt.setBestOfferNum(nearbyPushRuleConfig.getBestOfferNum());
		nearbyPushRqt.setOrderVersion(orderVersion);
		nearbyPushRqt.setMaxOfferNum(maxOfferNum);
		nearbyPushRqt.setOrderDetailData(orderDetailData);
		nearbyPushRqt.setNearbyDistance(nearbyPushRuleConfig.getNearbyDistance());
		rocketMqSendService.sendDelayMessage(orderPushTopic,"nearby_push", JSON.toJSONString(nearbyPushRqt),delayTime);
		return true;
	}

	public boolean sendLongTailPushMessage(OrderDetailData orderDetailData,
										   String orderVersion,
										   Long delayTime,
										   LongTailStrategyTuple longTailStrategyTuple,
										   Integer maxOfferNum
	){
		LongTailOrderPushRqt longTailOrderPushRqt = new LongTailOrderPushRqt();
		longTailOrderPushRqt.setOrderVersion(orderVersion);
		longTailOrderPushRqt.setOrderDetailData(orderDetailData);
		longTailOrderPushRqt.setMaxOfferNum(maxOfferNum);
		longTailOrderPushRqt.setLongTailStrategyTuple(longTailStrategyTuple);
		rocketMqSendService.sendDelayMessage(orderPushTopic,"long_tail_push", JSON.toJSONString(longTailOrderPushRqt),delayTime);
		return true;
	}


	public boolean sendDelayPushMessage(Long delayTime,String content){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,ORDER_MATCH_MASTER,content,delayTime);
		return true;
	}

	public boolean sendScheduledPushMessage(Long delayTime,String content,String topic,String tag){
		rocketMqSendService.sendDelayMessage(topic,tag,content,delayTime);
		return true;
	}

	public boolean sendNormalPushMessage(Long masterOrderId,Integer businessLineId,List<Long> eliminateMaster,String handleTag,String masterSourceType){
		OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
		rqt.setMasterOrderId(masterOrderId);
		rqt.setBusinessLineId(businessLineId);
		rqt.setPushMode("normal");
		rqt.setOrderPushEliminateMasterIds(eliminateMaster);
		rqt.setHandoffTag(handleTag);
		rqt.setMasterSourceType(masterSourceType);
		sendDelayPushMessage(1000L,JSON.toJSONString(rqt));
		return true;
	}

	public boolean sendScheduledPushMessage(Long delayTime, Long masterOrderId, Integer businessLineId, List<Long> eliminateMaster, String handleTag){
		OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
		rqt.setMasterOrderId(masterOrderId);
		rqt.setBusinessLineId(businessLineId);
		rqt.setPushMode("normal");
		rqt.setOrderPushEliminateMasterIds(eliminateMaster);
		rqt.setHandoffTag(handleTag);
		sendScheduledPushMessage(
				delayTime,
				JSON.toJSONString(rqt),
				orderMatchMasterTopic,
				OrderMatchMasterConsumeController.EXCLUSIVE_ORDER_SCHEDULED_TASK
		);
		return true;
	}

	public boolean sendNotAppointScheduledPushMessage(Long delayTime, Long masterOrderId, Integer businessLineId, List<Long> eliminateMaster, String handleTag){
		OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
		rqt.setMasterOrderId(masterOrderId);
		rqt.setBusinessLineId(businessLineId);
		rqt.setPushMode("normal");
		rqt.setOrderPushEliminateMasterIds(eliminateMaster);
		rqt.setHandoffTag(handleTag);
		sendScheduledPushMessage(
				delayTime,
				JSON.toJSONString(rqt),
				orderMatchMasterTopic,
				OrderMatchMasterConsumeController.NOT_APPOINT_ORDER_SCHEDULED_TASK
		);
		return true;
	}

	public boolean sendOrderMatchMessage(Long orderId){
		OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
		orderMatchMasterRqt.setMasterOrderId(orderId);
		orderMatchMasterRqt.setPushMode("normal");
		rocketMqSendService.sendSyncMessage(orderMatchMasterTopic,ORDER_MATCH_MASTER,JSON.toJSONString(orderMatchMasterRqt));
		return true;

	}

    /**
     * 发送推单mq消息
     *
     * @param masterOrderId
     * @param masterIds
     * @param totalPushedMasterNum
     * @param firstPush
     * @param pushAddress
     * @param businessLineId
     * @param commonFeature
     * @param masterSourceType 推送师傅来源类型，tob: B端师傅,toc: C端师傅
     * @return
     */
    public boolean pushByQueueNew(Long masterOrderId, String masterIds,
                                  int totalPushedMasterNum, String firstPush, String pushAddress,
                                  String businessLineId, JSONObject commonFeature, List<PushMaster> pushMasterList,
                                  Integer firstTimeValidPush, Integer normalFirstTimeValidPush, Integer normalFirstMatchMasterNum,
                                  String masterSourceType,OrderDetailData orderDetailData) {


        final String pushTime = DateFormatterUtil.getNowSp();
        JSONObject pushContent = new JSONObject();
        pushContent.put("orderId", masterOrderId);
        pushContent.put("globalOrderTraceId", commonFeature.getLong(FieldConstant.GLOBAL_ORDER_ID));
        String pushScenarioType = commonFeature.getString(FieldConstant.PUSH_SCENARIO_TYPE);
		String matchSceneCode = commonFeature.getString(FieldConstant.MATCH_SCENE_CODE);

		pushContent.put("pushScenarioType", StringUtils.isNotBlank(pushScenarioType) ? pushScenarioType : "smart_push");
        pushContent.put("pushNumber", totalPushedMasterNum);
        pushContent.put("pushTime", pushTime);
        pushContent.put("firstPush", Integer.valueOf(firstPush));
        pushContent.put("isNewOrderPush", 1);
        pushContent.put("pushDivisionLevel", Integer.valueOf(pushAddress));
        pushContent.put("businessLineId", Integer.valueOf(businessLineId));
        pushContent.put("masterSourceType", masterSourceType);
		pushContent.put("matchSceneCode", matchSceneCode);
		if (Objects.nonNull(firstTimeValidPush)) {
            pushContent.put("firstTimeValidPush", firstTimeValidPush);
        }
        if (Objects.nonNull(normalFirstTimeValidPush)) {
            pushContent.put("normalFirstTimeValidPush", normalFirstTimeValidPush);
        }
        if (Objects.nonNull(normalFirstMatchMasterNum)) {
            pushContent.put("normalFirstMatchMasterNum", normalFirstMatchMasterNum);
        }
        String pushMode = null;
        String accountType = null;
        String appointType = null;
        String myTag = MATCH_RESULT;
        if (commonFeature != null) {
            pushMode = commonFeature.getString(FieldConstant.PUSH_MODE);
            accountType = commonFeature.getString(FieldConstant.ACCOUNT_TYPE);
            appointType = commonFeature.getString(FieldConstant.APPOINT_TYPE);
            if (pushMode != null) {
                pushContent.put("pushMode", pushMode);
                if (PushMode.PACKAGE_ORDER.code.equals(pushMode)) {
                    final String operateFlag = commonFeature.getString(FieldConstant.OPERATE_FLAG);
                    final String packageId = commonFeature.getString(FieldConstant.PACKAGE_ID);
                    pushContent.put("packageId", packageId);
                    pushContent.put("goodsAttribute", commonFeature.getString(FieldConstant.PACKAGE_ATTRIBUTE_ID));
                    pushContent.put("orderPackageAttributeRange",
                            commonFeature.getJSONObject(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE));
                    if (FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag)) {
                        myTag = PACKAGE_ORDER_PUSH_TAG;
                        pushContent.put(FieldConstant.PACKAGE_MODE, FieldConstant.PACKAGE);
                    }
                }

                if (PushMode.RECOMMEND_LIST.code.equals(pushMode)) {
                    myTag = "recommend_match_result";
                }

            }
            final Object recruitId = commonFeature.get(FieldConstant.RECRUIT_ID);
            if (recruitId != null) {
                pushContent.put("recruitId", recruitId);
            }
            final Object hasPrice = commonFeature.get(FieldConstant.HAS_PRICE);
            if (hasPrice != null) {
                pushContent.put("hasPrice", hasPrice);
            }
            final Object teamMasterOrderPush = commonFeature.get(FieldConstant.TEAM_MASTER_ORDER_PUSH);
            if (teamMasterOrderPush != null) {
                pushContent.put("teamMasterOrderPush", teamMasterOrderPush);
            }

            Integer pushFlag = commonFeature.getInteger(FieldConstant.PUSH_FLAG);
            if (pushFlag != null) {
                pushContent.put("pushFlag", pushFlag);
            }
            final Boolean accordingDistancePush = commonFeature.getBoolean(FieldConstant.IS_ACCORDING_DISTANCE_PUSH);
            Optional.ofNullable(accordingDistancePush).ifPresent(it -> pushContent.put("accordingDistancePushFlag", it));

            if ("agent".equals(pushMode)) {
                pushContent.put(AgentMasterMatcher.AGENT_MASTER_LIST, commonFeature.get(AgentMasterMatcher.AGENT_MASTER_LIST));
                pushContent.put(AgentMasterMatcher.AGENT_PUSH_TYPE, commonFeature.get(AgentMasterMatcher.AGENT_PUSH_TYPE));
                if (Objects.nonNull(commonFeature.get(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME))) {
                    pushContent.put(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME, commonFeature.get(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME));
                }
                if (Objects.nonNull(commonFeature.get(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME))) {
                    pushContent.put(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME, commonFeature.get(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME));
                }
                if (Objects.nonNull(commonFeature.get(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO))) {
                    pushContent.put(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO, commonFeature.get(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO));
                }
            }
        }


        Set<String> masterIdSet = Arrays.stream(masterIds.split(",")).collect(Collectors.toSet());

        List<OrderPushMaster> orderPushMasterList = new ArrayList<>();
        Integer pushFlag = pushContent.getInteger("pushFlag");
        BigDecimal orderLng = BigDecimal.ZERO;
        BigDecimal orderLat = BigDecimal.ZERO;
        String orderLngLat = commonFeature.getString(FieldConstant.ORDER_LNG_LAT);
        if (StringUtils.isNotBlank(orderLngLat)) {
            String[] orderLngLatArray = orderLngLat.split(",");
            orderLng = new BigDecimal(orderLngLatArray[0]);
            orderLat = new BigDecimal(orderLngLatArray[1]);
        }

        BigDecimal orderLongitude = orderLng;
        BigDecimal orderLatitude = orderLat;

        //TODO 查询t_master表获取师傅经纬度
//		Map<String,MasterBase> masterBaseMap = otsMasterSelector.searchMasterList(masterIdSet);
        List<Master> masterList = this.getMasterList(masterIdSet);
        Map<String, Master> masterBaseMap = masterList.stream().collect(Collectors.toMap(Master::getMasterId, each -> each, (value1, value2) -> value1));

        Map<String, AgreementMasterBase> agreementMasterBaseMap = null;
        Map<Long, PushExtraData.MasterAutoOfferPriceInfo> masterAutoOfferPriceInfoMap = null;


        if (PushMode.AGREEMENT_MASTER.code.equals(pushMode)) {
            List<AgreementMasterBase> agreementMasterBaseList = (List<AgreementMasterBase>) commonFeature.get("agreement_master_list");
            agreementMasterBaseMap = agreementMasterBaseList.stream().collect(Collectors.toMap(AgreementMasterBase::getMasterId, Function.identity()));
        }


		Map<String, TechniqueVerifyMaster> techniqueVerifyMasterMap = null;
		if (PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.code.equals(pushMode)) {
			List<TechniqueVerifyMaster> techniqueVerifyMasterList = (List<TechniqueVerifyMaster>) commonFeature.get("technique_verify_master_list");
			techniqueVerifyMasterMap = techniqueVerifyMasterList.stream().collect(Collectors.toMap(TechniqueVerifyMaster::getMasterId, Function.identity()));
		}

        Map<String, CooperationBusinessMasterDto> cooperationBusinessMasterMap = null;
        if (PushMode.COOPERATION_BUSINESS_MASTER.code.equals(pushMode)) {
            List<CooperationBusinessMasterDto> cooperationBusinessMasterDtoList = (List<CooperationBusinessMasterDto>) commonFeature.get("cooperation_business_master_list");
            cooperationBusinessMasterMap = cooperationBusinessMasterDtoList.stream().collect(Collectors.toMap(CooperationBusinessMasterDto::getMasterId, Function.identity()));
        }

        Map<String, AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoMap = null;
        if (PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.code.equals(pushMode)) {
            List<AfterTechniqueVerifyMasterDto> afterTechniqueVerifyMasterDtoList = (List<AfterTechniqueVerifyMasterDto>) commonFeature.get("after_techniqueVerify_master_list");
            afterTechniqueVerifyMasterDtoMap = afterTechniqueVerifyMasterDtoList.stream().collect(Collectors.toMap(AfterTechniqueVerifyMasterDto::getMasterId, Function.identity()));
        }


		Map<String, FullTimeMaster> fullTimeMasterMap = null;
		if (PushMode.FULL_TIME_MASTER_DISPATCH.code.equals(pushMode)) {
			List<FullTimeMaster> fullTimeMasterList = (List<FullTimeMaster>) commonFeature.get("full_time_master_list");
			fullTimeMasterMap = fullTimeMasterList.stream().collect(Collectors.toMap(FullTimeMaster::getMasterId, Function.identity()));
		}


        if (PushMode.COLLECT_CONTRACT_MASTER.code.equals(pushMode)) {
            List<PushExtraData.MasterAutoOfferPriceInfo> masterAutoOfferPriceInfoList = (List<PushExtraData.MasterAutoOfferPriceInfo>) commonFeature.get("master_auto_price_list");
            masterAutoOfferPriceInfoMap = masterAutoOfferPriceInfoList.stream().collect(Collectors.toMap(PushExtraData.MasterAutoOfferPriceInfo::getMasterId, Function.identity()));
        }

        Map<String, NewModelMaster> newModelMasterMap = null;

        if ("new_model".equals(pushMode) || "new_model_single".equals(pushMode)) {
            List<NewModelMaster> newModelMasterList = (List<NewModelMaster>) commonFeature.get("new_model_master_list");
            newModelMasterMap = newModelMasterList.stream().collect(Collectors.toMap(NewModelMaster::getMasterId, Function.identity()));
        }

        final Map<String, AgreementMasterBase> finalAgreementMasterBaseMap = agreementMasterBaseMap;
        final Map<Long, PushExtraData.MasterAutoOfferPriceInfo> finalMasterAutoPriceInfoMap = masterAutoOfferPriceInfoMap;
        final Map<String, NewModelMaster> finalNewModelMasterMap = newModelMasterMap;
        final Map<String, CooperationBusinessMasterDto> finalCooperationBusinessMasterMap = cooperationBusinessMasterMap;
        final Map<String, AfterTechniqueVerifyMasterDto> finalAfterTechniqueVerifyMasterDtoMap = afterTechniqueVerifyMasterDtoMap;

        int index = 1;



		Integer finalPushFlag = commonFeature.getInteger(FieldConstant.PUSH_FLAG);

		this.shuntPartTimeMaster(pushMasterList,orderDetailData,pushMode, finalPushFlag);

		log.info("pushMasterList:" + JSON.toJSONString(pushMasterList));
		Map<String, PushMaster> pushMasterMap = CollectionUtils.isNotEmpty(pushMasterList) ? pushMasterList.stream().collect(Collectors.toMap(PushMaster::getMasterId, each -> each, (value1, value2) -> value1)) : null;


		for (String masterId : masterIdSet) {
            OrderPushMaster orderPushMaster = new OrderPushMaster();
            orderPushMaster.setMasterId(Long.valueOf(masterId));
            AgreementMasterBase agreementMasterBase = finalAgreementMasterBaseMap != null ? finalAgreementMasterBaseMap.get(masterId) : null;
            if (agreementMasterBase != null) {
                orderPushMaster.setRecruitId(Integer.valueOf(agreementMasterBase.getRecruitId()));
                orderPushMaster.setCooperationPrice(agreementMasterBase.getCooperationPrice());
                orderPushMaster.setTagName(agreementMasterBase.getRecruitTagName());
                orderPushMaster.setAutoOfferSort(agreementMasterBase.getOfferSort());
                orderPushMaster.setAutoPrice(agreementMasterBase.getCooperationPrice());

            }

			TechniqueVerifyMaster techniqueVerifyMaster = techniqueVerifyMasterMap != null ? techniqueVerifyMasterMap.get(masterId) : null;
			if (techniqueVerifyMaster != null) {
				orderPushMaster.setAutoOfferSort(techniqueVerifyMaster.getOfferSort());
			}


			FullTimeMaster fullTimeMaster = fullTimeMasterMap != null ? fullTimeMasterMap.get(masterId) : null;
			if (fullTimeMaster != null) {
				orderPushMaster.setAutoOfferSort(fullTimeMaster.getOfferSort());
			}

			CooperationBusinessMasterDto cooperationBusinessMasterDto = finalCooperationBusinessMasterMap != null ? finalCooperationBusinessMasterMap.get(masterId) : null;
            if (cooperationBusinessMasterDto != null) {
                orderPushMaster.setAutoOfferSort(cooperationBusinessMasterDto.getGrabSort());
            }

            AfterTechniqueVerifyMasterDto afterTechniqueVerifyMasterDto = finalAfterTechniqueVerifyMasterDtoMap != null ? finalAfterTechniqueVerifyMasterDtoMap.get(masterId) : null;
            if (afterTechniqueVerifyMasterDto != null) {
                orderPushMaster.setAutoOfferSort(afterTechniqueVerifyMasterDto.getGrabSort());
            }

            PushExtraData.MasterAutoOfferPriceInfo masterAutoOfferPriceInfo = Objects.nonNull(finalMasterAutoPriceInfoMap) ? finalMasterAutoPriceInfoMap.get(Long.valueOf(masterId)) : null;
            if (Objects.nonNull(masterAutoOfferPriceInfo)) {
                orderPushMaster.setAutoPrice(masterAutoOfferPriceInfo.getTargetOfferPrice());
            }

            NewModelMaster newModelMaster = finalNewModelMasterMap != null ? finalNewModelMasterMap.get(masterId) : null;
            if (newModelMaster != null) {
                orderPushMaster.setPushTag(newModelMaster.getMasterCategory());
            }


            try {
                Master master = masterBaseMap.get(masterId);
                if (Objects.nonNull(master) && StringUtils.isNotBlank(master.getLngLat())) {
                    String masterLngLat = masterBaseMap.get(masterId).getLngLat();
                    CalculatePushDistanceParamVo calculatePushDistanceParamVo = new CalculatePushDistanceParamVo();
                    calculatePushDistanceParamVo.setMasterId(Long.valueOf(masterId));
                    calculatePushDistanceParamVo.setOrderId(masterOrderId);
                    calculatePushDistanceParamVo.setOrderLatitude(orderLatitude);
                    calculatePushDistanceParamVo.setOrderLongitude(orderLongitude);
                    if (StringUtils.isNotBlank(masterLngLat)) {
                        String[] masterLngLatArray = masterLngLat.split(",");
                        calculatePushDistanceParamVo.setMasterLatitude(new BigDecimal(masterLngLatArray[1]));
                        calculatePushDistanceParamVo.setMasterLongitude(new BigDecimal(masterLngLatArray[0]));
                    } else {
                        calculatePushDistanceParamVo.setMasterLatitude(BigDecimal.ZERO);
                        calculatePushDistanceParamVo.setMasterLongitude(BigDecimal.ZERO);
                    }
                    Long secondDivisionId = commonFeature.getLong(FieldConstant.SECOND_DIVISION_ID);
                    calculatePushDistanceParamVo.setCityDivisionId(secondDivisionId);
                    CalculatePushDistanceRespVo calculatePushDistanceResp = orderMasterCalculateService.calculatePushDistance(calculatePushDistanceParamVo);
                    orderPushMaster.setPushDistance(calculatePushDistanceResp.getPushDistance());
                    orderPushMaster.setPushDistanceType(calculatePushDistanceResp.getPushDistanceType());
                    orderPushMaster.setMasterLatitude(calculatePushDistanceParamVo.getMasterLatitude());
                    orderPushMaster.setMasterLongitude(calculatePushDistanceParamVo.getMasterLongitude());



                }

                orderPushMaster.setAccordingTechnologyPushFlag((Objects.nonNull(pushFlag) && (pushFlag == 4 || pushFlag == 5)) ? 0 : 1);

                orderPushMaster.setSort(index);



                if (pushMasterMap != null && pushMasterMap.get(masterId) != null) {
                    orderPushMaster.setScore(pushMasterMap.get(masterId).getScore());
                    orderPushMaster.setMustOrderFlag(pushMasterMap.get(masterId).getMustOrderFlag());
					orderPushMaster.setMasterSourceType(pushMasterMap.get(masterId).getMasterSourceType());
					orderPushMaster.setMasterTimeType(pushMasterMap.get(masterId).getMasterTimeType());
					if(Objects.isNull(orderPushMaster.getMasterTimeType())){
						orderPushMaster.setMasterTimeType(0);
					}
					pushMasterMap.get(masterId).setPushDistance(orderPushMaster.getPushDistance());
					orderPushMaster.setCrossCityPush(pushMasterMap.get(masterId).getIsCrossCityPush());
					orderPushMaster.setShuntFlag(pushMasterMap.get(masterId).getShuntFlag());

				} else {
                    orderPushMaster.setScore(BigDecimal.ZERO);
                    orderPushMaster.setMustOrderFlag(0);
					orderPushMaster.setMasterSourceType(MasterSourceType.TOB.code);
					orderPushMaster.setShuntFlag(0);
				}
                index = index + 1;

            } catch (Exception e) {
                log.error("计算推单距离或技能相关失败", e);
            }
            orderPushMasterList.add(orderPushMaster);
        }

        if ((Objects.nonNull(pushFlag) && (pushFlag == 4 || pushFlag == 5)) && orderPushMasterList.size() > longTailPushMasterSizeLimit) {
            log.info("pushMasterSizeLimitByLongTailPush >>> 订单推送技能不相关的师傅,人数限制3000！");
            orderPushMasterList = orderPushMasterList.stream()
                    .sorted(Comparator.comparing(OrderPushMaster::getPushDistance))
                    .limit(longTailPushMasterSizeLimit)
                    .collect(Collectors.toList());
        }

        try {
            filter4UserInviteMasterOfferPrice(masterOrderId, commonFeature.getLong(FieldConstant.GLOBAL_ORDER_ID), orderPushMasterList, masterBaseMap, pushFlag, pushMode, accountType, appointType, firstPush, businessLineId);
        } catch (Exception e) {
            log.error("filter4UserInviteMasterOfferPrice error", e);
        }

        pushContent.put("masterAddressInfoList", orderPushMasterList);

//		String pushModeType = commonFeature.getString(FieldConstant.PUSH_MODE_TYPE);
        String pushModeType = null;
        if (StringUtils.isNotBlank(pushModeType)) {
            pushContent.put("pushModeType", pushModeType);
        }

        if (PushMode.PRE_EXCLUSIVE.code.equals(pushMode) || "brand".equals(pushMode)) {
            Long recruitId = commonFeature.getLong(FieldConstant.RECRUIT_ID);
            Boolean hasPrice = (Boolean) commonFeature.get(FieldConstant.HAS_PRICE);
            pushContent.put("exclusiveOrderLabel", getExclusiveOrderLabel(orderPushMasterList.get(0).getMasterId(), pushMode, pushModeType, commonFeature.getLong(FieldConstant.GLOBAL_ORDER_ID),
                    recruitId, hasPrice));
        }
        rocketMqSendService.sendSyncMessage(orderMatchMasterTopic, myTag, pushContent.toJSONString());
        //推送计数
//		pushRecordFacade.orderPushCount(pushTime,new HashSet<>(Arrays.asList(masterIds.split(SymbolConstant.COMMA))));
        pushRecordFacade.saveOrderPush(String.valueOf(masterOrderId), pushTime, masterIds, StringUtils.isNotBlank(pushMode) ? pushMode : PushMode.NORMAL.code);

        uploadPushDetail(masterOrderId, pushMasterList);


        return true;
    }




	/**
	 * 发送推单mq消息
	 *
	 * @param orderDetailDataList
	 * @param pushAddress
	 * @param businessLineId
	 * @param commonFeature
	 * @param masterSourceType 推送师傅来源类型，tob: B端师傅,toc: C端师傅
	 * @return
	 */
	public boolean pushByQueue(List<OrderDetailData> orderDetailDataList, String pushAddress,
							   String businessLineId, JSONObject commonFeature, PushMaster pushMaster, Integer firstTimeValidPush,
							   String masterSourceType) {

		log.info("pushMasterList:" + JSON.toJSONString(pushMaster));

		String masterId = pushMaster.getMasterId();
		final String pushTime = DateFormatterUtil.getNowSp();

		PushOrderToMasterRqt pushOrderToMasterRqt = new PushOrderToMasterRqt();
		pushOrderToMasterRqt.setMasterId(Long.valueOf(pushMaster.getMasterId()));
		String pushScenarioType = commonFeature.getString(FieldConstant.PUSH_SCENARIO_TYPE);
		String matchSceneCode = commonFeature.getString(FieldConstant.MATCH_SCENE_CODE);

		pushOrderToMasterRqt.setPushScenarioType(StringUtils.isNotBlank(pushScenarioType) ? pushScenarioType : "smart_push");
		String pushMode = commonFeature.getString(FieldConstant.PUSH_MODE);
		pushOrderToMasterRqt.setPushMode(pushMode);
		pushOrderToMasterRqt.setPushDivisionLevel(Integer.valueOf(pushAddress));

		pushOrderToMasterRqt.setPushDivisionLevel(4);
		pushOrderToMasterRqt.setMasterSourceType(masterSourceType);
		pushOrderToMasterRqt.setMatchSceneCode(matchSceneCode);

		Integer pushFlag = commonFeature.getInteger(FieldConstant.PUSH_FLAG);
		if (pushFlag != null) {
			pushOrderToMasterRqt.setPushFlag(pushFlag);
		}

		final Boolean accordingDistancePush = commonFeature.getBoolean(FieldConstant.IS_ACCORDING_DISTANCE_PUSH);
		Optional.ofNullable(accordingDistancePush).ifPresent(it -> pushOrderToMasterRqt.setAccordingDistancePushFlag(it));


		MasterBaseSearch masterBaseSearch = masterBaseEsRepository.searchByMasterId(Long.valueOf(masterId));

		pushOrderToMasterRqt.setCityDivisionId(masterBaseSearch.getCityDivisionId());




		List<PushOrderToMasterRqt.PushOrderDetailInfo> pushOrderDetailInfoList = new ArrayList<>();

		List<Master> masterList = this.getMasterList(Collections.singleton(pushMaster.getMasterId()));
		Map<String, Master> masterBaseMap = masterList.stream().collect(Collectors.toMap(Master::getMasterId, each -> each, (value1, value2) -> value1));

		for (OrderDetailData orderDetailData : orderDetailDataList) {


			PushOrderToMasterRqt.PushOrderDetailInfo pushOrderDetailInfo = new PushOrderToMasterRqt.PushOrderDetailInfo();
			pushOrderDetailInfo.setOrderId(orderDetailData.getMasterOrderId());
			pushOrderDetailInfo.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
			pushOrderDetailInfo.setPushDivisionLevel(4);

			try {
				BigDecimal orderLng = BigDecimal.ZERO;
				BigDecimal orderLat = BigDecimal.ZERO;
				String orderLngLat = orderDetailData.getOrderLngLat();
				if (StringUtils.isNotBlank(orderLngLat)) {
					String[] orderLngLatArray = orderLngLat.split(",");
					orderLng = new BigDecimal(orderLngLatArray[0]);
					orderLat = new BigDecimal(orderLngLatArray[1]);
				}



				String masterLngLat = masterBaseMap.get(masterId).getLngLat();
				CalculatePushDistanceParamVo calculatePushDistanceParamVo = new CalculatePushDistanceParamVo();
				calculatePushDistanceParamVo.setMasterId(Long.valueOf(masterId));
				calculatePushDistanceParamVo.setOrderId(orderDetailData.getMasterOrderId());
				calculatePushDistanceParamVo.setOrderLatitude(orderLat);
				calculatePushDistanceParamVo.setOrderLongitude(orderLng);
				if (StringUtils.isNotBlank(masterLngLat)) {
					String[] masterLngLatArray = masterLngLat.split(",");
					calculatePushDistanceParamVo.setMasterLatitude(new BigDecimal(masterLngLatArray[1]));
					calculatePushDistanceParamVo.setMasterLongitude(new BigDecimal(masterLngLatArray[0]));
					pushOrderToMasterRqt.setMasterLatitude(new BigDecimal(masterLngLatArray[1]));
					pushOrderToMasterRqt.setMasterLongitude(new BigDecimal(masterLngLatArray[0]));
				} else {
					calculatePushDistanceParamVo.setMasterLatitude(BigDecimal.ZERO);
					calculatePushDistanceParamVo.setMasterLongitude(BigDecimal.ZERO);
					pushOrderToMasterRqt.setMasterLatitude(BigDecimal.ZERO);
					pushOrderToMasterRqt.setMasterLongitude(BigDecimal.ZERO);
				}
				Long secondDivisionId = commonFeature.getLong(FieldConstant.SECOND_DIVISION_ID);
				calculatePushDistanceParamVo.setCityDivisionId(secondDivisionId);
				CalculatePushDistanceRespVo calculatePushDistanceResp = orderMasterCalculateService.calculatePushDistance(calculatePushDistanceParamVo);
				pushOrderDetailInfo.setPushDistance(calculatePushDistanceResp.getPushDistance());
				pushOrderDetailInfo.setPushDistanceType(calculatePushDistanceResp.getPushDistanceType());


				pushOrderDetailInfo.setAccordingTechnologyPushFlag(1);

				if (Objects.nonNull(masterBaseSearch.getCityDivisionId())
						&& !masterBaseSearch.getCityDivisionId().equals(orderDetailData.getSecondDivisionId())) {
					pushOrderDetailInfo.setCrossCityPush(1);
				} else {
					pushOrderDetailInfo.setCrossCityPush(0);
				}

				pushOrderDetailInfo.setFirstPush(0);
				pushOrderDetailInfo.setFirstTimeValidPush(0);
				pushOrderDetailInfo.setBusinessLineId(orderDetailData.getBusinessLineId());



			} catch (Exception e) {
				log.error("计算推单距离或技能相关失败", e);
			}
			pushOrderDetailInfoList.add(pushOrderDetailInfo);
		}

		pushOrderToMasterRqt.setPushOrderDetailInfoList(pushOrderDetailInfoList);


		rocketMqSendService.sendSyncMessage(orderMatchMasterTopic, "push_orders_to_master",JSON.toJSONString(pushOrderToMasterRqt));
		//推送计数
//		pushRecordFacade.orderPushCount(pushTime,new HashSet<>(Arrays.asList(masterIds.split(SymbolConstant.COMMA))));
//		pushRecordFacade.saveOrderPush(String.valueOf(masterOrderId), pushTime, masterIds, StringUtils.isNotBlank(pushMode) ? pushMode : PushMode.NORMAL.code);

//		uploadPushDetail(masterOrderId, pushMasterList);


		return true;
	}

	@Resource
	private FeatureRepository featureRepository;


	@Resource
	private PushShuntRuleRepository pushShuntRuleRepository;




	private List<PushShuntRule> selectPushShuntRule(OrderDetailData orderDetailData){
		List<PushShuntRule> pushShuntRuleList = pushShuntRuleRepository.selectList(String.valueOf(orderDetailData.getSecondDivisionId()),orderDetailData.getBusinessLineId(),null,null,null,1);
		if(org.springframework.util.CollectionUtils.isEmpty(pushShuntRuleList)){
			pushShuntRuleList = pushShuntRuleRepository.selectList("all",orderDetailData.getBusinessLineId(),null,null,null,1);
		}
		return pushShuntRuleList;
	}


	private boolean checkExclusiveRule(PushShuntRule pushShuntRule,String pushMode,OrderDetailData orderDetailData){
		CreateRqt.ExclusiveRule exclusiveRule = JSON.parseObject(pushShuntRule.getExclusiveRule(),CreateRqt.ExclusiveRule.class);
		if(Objects.nonNull(exclusiveRule) && exclusiveRule.getItemList().stream().map(CreateRqt.Item::getItemValue).collect(Collectors.toList()).contains(pushMode)){
			if(CompensateType.NONE_RECEIVE.getCode().equals(pushMode)){
				CreateRqt.Item pushTypeItem = exclusiveRule.getItemList().stream().filter(item -> CompensateType.NONE_RECEIVE.getCode().equals(item.getItemValue())).findFirst().orElse(null);
				if(StringUtils.isNotBlank(pushTypeItem.getItemExtraValue())){
					Integer delayTime = Integer.valueOf(pushTypeItem.getItemExtraValue());
					Integer afreshDelayTime = orderDetailData.getPushExtraData().getAfreshDelayTime();
					if(afreshDelayTime <= delayTime){
						return false;
					}
				}
			}
			return true;
		}

		return false;
	}


	@Resource
	private QLExpressHandler qlExpressHandler;



	@Value("${selectMasterGroup.pageSize:400}")
	private Integer selectMasterGroupPageSize;

	private boolean checkOpenCondition(PushShuntRule pushShuntRule, DefaultContext<String,Object> orderFeatureContext){
		try{
			OpenConditionExpressionDto expressionDto = JSON.parseObject(pushShuntRule.getServeRuleExpression(), OpenConditionExpressionDto.class);
			boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(expressionDto.getOpenConditionRuleExpression(), orderFeatureContext,null,true,false);
			return result;
		}catch(Exception e){
			log.error("filterByMasterGroups",e);
		}
		return false;
	}


	private List<MasterGroupResp> getMasterGroup(Set<String> pushMasterIdSet){
		List<MasterGroupResp> masterGroupRespList = new ArrayList<>();
		final List<List<Long>> batch = LocalCollectionsUtil.groupByBatchLong(pushMasterIdSet, selectMasterGroupPageSize);
		for (List<Long> batchRequest : batch) {
			GetPersonGroupByMasterIdsRqt rqt = new GetPersonGroupByMasterIdsRqt();
			rqt.setAppId(6);
			rqt.setMasterIds(batchRequest);
			masterGroupRespList.addAll( bigdataOpenServiceApi.getPersonaGroupIdsByMasterIds(rqt));
		}
		return masterGroupRespList;
	}


	/**
	 * 分时师傅分流
	 * @param pushMasterList
	 */
	private void shuntPartTimeMaster(List<PushMaster> pushMasterList,OrderDetailData orderDetailData,String pushMode,
									 Integer finalPushFlag){

		try{

			if(!apolloConfigUtils.isOpenPartTimeMasterShunt()){
				return ;
			}

			if(Objects.nonNull(finalPushFlag) &&  (finalPushFlag == 4 || finalPushFlag == 5)){
				return ;
			}

			if(Objects.isNull(orderDetailData)){
				return ;
			}

			if(!PushMode.NORMAL.code.equals(pushMode)){
				return ;
			}

			String matchSceneCode = orderDetailData.getPushExtraData().getMatchSceneCode();
			String finalPushMode = MatchSceneCode.AFRESH_PUSH.getCode().equals(matchSceneCode) ? "none_receive" : pushMode;

			List<PushMaster> partTimeMasterList = pushMasterList.stream().filter(pushMaster -> Objects.nonNull(pushMaster.getMasterTimeType())
					&& pushMaster.getMasterTimeType() == 2).collect(Collectors.toList());

			if(CollectionUtils.isEmpty(partTimeMasterList)){
				return ;
			}


			DefaultContext<String,Object> orderFeatureContext = featureRepository.buildOrderFeatures(orderDetailData);
			featureRepository.queryUserGroups(orderDetailData,orderFeatureContext);

			List<PushShuntRule> pushShuntRuleList = selectPushShuntRule(orderDetailData);

			if(org.springframework.util.CollectionUtils.isEmpty(pushShuntRuleList)){
				return ;
			}


			pushShuntRuleList = pushShuntRuleList.stream().filter(pushShuntRule -> !checkExclusiveRule(pushShuntRule,finalPushMode,orderDetailData)).collect(Collectors.toList());
			if(org.springframework.util.CollectionUtils.isEmpty(pushShuntRuleList)){
				return ;
			}

			final DefaultContext<String,Object> orderFeatures = orderFeatureContext;
			pushShuntRuleList = pushShuntRuleList.stream().filter(pushShuntRule -> (!"serve".equals(pushShuntRule.getLimitRange())) || (checkOpenCondition(pushShuntRule,orderFeatures))).collect(Collectors.toList());
			if(org.springframework.util.CollectionUtils.isEmpty(pushShuntRuleList)){
				return ;
			}


			PushShuntRule crowdGroupPushShuntRule = pushShuntRuleList.stream().filter(pushLimitRule -> CrowdType.CROWD_GROUP.getCode().equals(pushLimitRule.getCrowdType())).findFirst().orElse(null);

			Set<String> masterIdSet = partTimeMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
			List<MasterGroupResp> masterGroupRespList = Objects.nonNull(crowdGroupPushShuntRule) ? getMasterGroup(masterIdSet) : null;


			for(PushShuntRule pushShuntRule : pushShuntRuleList){
				if(CrowdType.CROWD_LABEL.getCode().equals(pushShuntRule.getCrowdType())){
					shuntPartTimeMaster(pushShuntRule,partTimeMasterList);
				}else if(CrowdType.CROWD_GROUP.getCode().equals(pushShuntRule.getCrowdType())){
					shuntByCrowdGroup(pushShuntRule,partTimeMasterList,masterGroupRespList);
				}

			}
		}catch(Exception e){
			log.error("shuntPartTimeMaster error",e);
		}


	}


	private void shuntPartTimeMaster(PushShuntRule pushShuntRule,List<PushMaster> pushMasterList){

		if(CollectionUtils.isEmpty(pushMasterList)){
			return ;
		}

		if("fixed_percent".equals(pushShuntRule.getLimitRule())){

			pushMasterList.forEach(pushMaster -> {

				if(Objects.nonNull(pushMaster.getShuntFlag()) && pushMaster.getShuntFlag() == 1){
					return ;
				}

				Integer pushPercent = pushShuntRule.getPushPercent() <= 0 ? 0 : pushShuntRule.getPushPercent();
				if(pushPercent == 0){
					pushMaster.setShuntFlag(1);
				}else{
					int random = new Random().nextInt(100);
					if(random > pushPercent){
						pushMaster.setShuntFlag(1);
					}
				}

			});


		}

	}

	private void shuntByCrowdGroup(PushShuntRule pushShuntRule,List<PushMaster> pushMasterList,List<MasterGroupResp> masterGroupRespList ){


		CrowdGroupExpressionDto crowdGroupExpressionDto = JSON.parseObject(pushShuntRule.getCrowdGroupExpression(),CrowdGroupExpressionDto.class);
		Set<String> resultSet = new HashSet<>();

		if(org.apache.commons.collections.CollectionUtils.isNotEmpty(masterGroupRespList)){
			masterGroupRespList.forEach(masterGroupResp -> {
				String groupIds = StringUtils.isNotBlank(masterGroupResp.getGroupIds()) ? masterGroupResp.getGroupIds() : "0";
				List<Integer> groupIdList = Arrays.stream(groupIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
				DefaultContext  context = new DefaultContext();
				context.put(crowdGroupExpressionDto.getGroupParams(),groupIdList);
				try{
					boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(crowdGroupExpressionDto.getGroupExpression(), context,null,true,false);
					if(result){
						resultSet.add(String.valueOf(masterGroupResp.getMasterId()));
					}
				}catch(Exception e){
					log.error("filterByMasterGroups",e);
				}
			});
		}

		if(CollectionUtils.isNotEmpty(resultSet)){
			List<PushMaster> shuntMasterList = pushMasterList.stream().filter(pushMaster -> resultSet.contains(pushMaster.getMasterId())).collect(Collectors.toList());
			shuntPartTimeMaster(pushShuntRule,shuntMasterList);
		}


	}


    /**
     * 无人报价订单，企业邀请师傅报价的师傅列表筛选
     *
     * @param orderPushMasterList
     * @param masterBaseMap
     * @param pushFlag
     * @param pushMode
     * @param accountType
     * @param appointType
     * @param firstPush
     */
    private void filter4UserInviteMasterOfferPrice(Long masterOrderId, Long globalOrderId, List<OrderPushMaster> orderPushMasterList, Map<String, Master> masterBaseMap, Integer pushFlag,
                                                   String pushMode, String accountType, String appointType, String firstPush, String businessLineId) {

        log.info("filter4UserInviteMasterOfferPrice >>> orderId:{},pushFlag:{},pushMode:{},accountType:{},appointType:{},firstPush:{}", masterOrderId, pushFlag, pushMode, accountType, appointType, firstPush);
        if (CollectionUtil.isEmpty(orderPushMasterList) || CollectionUtil.isEmpty(masterBaseMap)) {
            return;
        }
        if (!PushMode.NORMAL.code.equals(pushMode)) {
            //非普通推单，过滤
            return;
        }
        if (Objects.nonNull(pushFlag) && (pushFlag == 4 || pushFlag == 5)) {
            //普通推单,排除长尾单
            return;
        }
        if (!Integer.valueOf(firstPush).equals(1)) {
            //只记录首次推单匹配的师傅
            return;
        }
        if (!"1".equals(businessLineId) || AccountType.ENTERPRISE.code.equals(accountType)) {
            //非企业订单,过滤
            return;
        }
        if (Objects.isNull(appointType) || !AppointType.OPEN.value.equals(Integer.valueOf(appointType))) {
            //非公开报价订单，过滤
            return;
        }
        List<OrderPushMaster> masterList = orderPushMasterList.stream().filter(e -> masterBaseMap.containsKey(String.valueOf(e.getMasterId()))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(masterList)) {
            return;
        }

        //过滤消息状态的师傅
        masterList = masterList.stream().filter(e -> masterBaseMap.get(String.valueOf(e.getMasterId())).getRestState() == 1).collect(Collectors.toList());
        log.info("filter4UserInviteMasterOfferPrice filterByRestState >>> masterList:{}", JSONUtil.toJsonStr(masterList));
        if (CollectionUtil.isEmpty(masterList)) {
            return;
        }

        //过滤当天非活跃状态的师傅
        masterList = masterList.stream().filter(e -> {
            Date masterActiveTime = masterBaseMap.get(String.valueOf(e.getMasterId())).getMasterActiveTime();
            return Objects.nonNull(masterActiveTime) && isToday(masterActiveTime);
        }).collect(Collectors.toList());
        log.info("filter4UserInviteMasterOfferPrice filterByMasterActiveTime >>> masterList:{}", JSONUtil.toJsonStr(masterList));
        if (CollectionUtil.isEmpty(masterList)) {
            return;
        }

        //过滤有报价限制的师傅
        masterList = masterList.stream().filter(e -> {
            String restrictAction = masterBaseMap.get(String.valueOf(e.getMasterId())).getRestrictAction();
            return Strings.isNullOrEmpty(restrictAction) || !restrictAction.contains("3");
        }).collect(Collectors.toList());
        log.info("filter4UserInviteMasterOfferPrice filterByOfferLimit >>> masterList:{}", JSONUtil.toJsonStr(masterList));
        if (CollectionUtil.isEmpty(masterList)) {
            return;
        }

		//按照推单匹配时的评分，按评分由高到低排序,score为null的排最后，取前50个师傅
		if (masterList.size() > filter4UserInviteMasterOfferPriceLimit) {
			masterList = masterList.stream()
					.sorted(Comparator.comparing(OrderPushMaster::getScore, Comparator.nullsLast(Comparator.reverseOrder())))
					.limit(filter4UserInviteMasterOfferPriceLimit)
					.collect(Collectors.toList());
		}

		GetMstOrderCancelCntIn30MinRqt rqt = new GetMstOrderCancelCntIn30MinRqt();
		rqt.setMasterIdList(masterList.stream().map(orderPushMaster -> orderPushMaster.getMasterId()).collect(Collectors.toList()));
		List<GetMstOrderCancelCntIn30MinResp> respList = bigdataOpenServiceApi.getMstOrderCancelCntIn30Min(rqt);

		if(CollectionUtils.isNotEmpty(respList)){
			List<Long> masterSet = respList.stream().filter(resp -> resp.getContactableOrderCancelCntIn30min() >= contactableOrderCancelCntIn30min).map(GetMstOrderCancelCntIn30MinResp::getMasterId).collect(Collectors.toList());
			masterList = masterList.stream().filter(orderPushMaster -> !masterSet.contains(orderPushMaster.getMasterId())).collect(Collectors.toList());
		}


        JSONObject pushContent = new JSONObject();
        pushContent.put("orderId", masterOrderId);
        pushContent.put("globalOrderId", globalOrderId);
        pushContent.put("masterList", masterList);
        rocketMqSendService.sendSyncMessage(orderMatchMasterTopic, "filter_master_for_no_offer", pushContent.toJSONString());
    }

    private static boolean isToday(Date date) {
        LocalDate today = LocalDate.now();
        LocalDate dateLocal = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return today.isEqual(dateLocal);
    }



	private void uploadPushDetail(Long masterOrderId,List<PushMaster> pushMasterList) {

		try{
			if(!"on".equals(pushDetailUploadSwitch)){
				return ;
			}

			Date pushTime = new Date();
			if (CollectionUtils.isNotEmpty(pushMasterList)) {
				List<PushEventDetail> pushEventDetailList = new ArrayList<>();
				pushMasterList.forEach(pushMaster -> {
					PushEventDetail pushEventDetail = new PushEventDetail();
					pushEventDetail.setOrderId(Long.valueOf(masterOrderId));
					pushEventDetail.setMasterId(Long.valueOf(pushMaster.getMasterId()));
					pushEventDetail.setPushDistance(pushMaster.getPushDistance());
					pushEventDetail.setScore(pushMaster.getScore());
					pushEventDetail.setBaseSelectType(pushMaster.getBaseSelectType());
					pushEventDetail.setRounds(pushMaster.getRounds());
					pushEventDetail.setPushTime(DateUtils.formatDateTime(pushTime));
					pushEventDetailList.add(pushEventDetail);
				});
				List<List<PushEventDetail>> batchList = LocalCollectionsUtil.groupByBatch(pushEventDetailList, pushDetailUploadBatchSize);
				batchList.forEach(batch -> {
					CompletableFuture.runAsync(() -> {
						kafkaMessageSender.sendMessageAsync("dwd_mst_order_push_other_info", "push_detail", JSON.toJSONString(batch));
					});
				});
				log.info("pushEventDetailList: " + JSON.toJSONString(pushEventDetailList));
			}
		}catch(Exception e){
			log.error("uploadPushDetail",e);
		}


	}


	private List<Master> getMasterList(Set<String> masterIdSet){
		List<Master> resultList = new ArrayList<>();
		final List<List<String>> batch = LocalCollectionsUtil.groupByBatchString(masterIdSet, 100);
		for (List<String> batchRequest : batch) {
			List<Master> masterList = masterRepository.selectByMasterIdSet(new HashSet<>(batchRequest));
			if(CollectionUtils.isNotEmpty(masterList)){
				resultList.addAll(masterList);
			}
		}
		return resultList;
	}


	private ExclusiveOrderLabel getExclusiveOrderLabel(Long masterId,String pushMode,String pushModeType,Long globalOrderId,Long recruitId,boolean hasPrice){
		GetExclusiveOrderInfoRqt getExclusiveOrderInfoRqt = new GetExclusiveOrderInfoRqt();
		getExclusiveOrderInfoRqt.setMasterId(masterId);
		getExclusiveOrderInfoRqt.setGlobalOrderTraceId(globalOrderId);
		getExclusiveOrderInfoRqt.setPushMode(pushMode);
		getExclusiveOrderInfoRqt.setPushModeType(pushModeType);
		getExclusiveOrderInfoRqt.setRecruitId(recruitId);
		getExclusiveOrderInfoRqt.setHasPrice(hasPrice);
		GetExclusiveOrderInfoResp getExclusiveOrderInfoResp = orderMessageApi.getExclusiveOrderInfo(getExclusiveOrderInfoRqt);
		if(Objects.nonNull(getExclusiveOrderInfoResp)){
			ExclusiveOrderLabel orderLabel = new ExclusiveOrderLabel();
			orderLabel.setExclusiveFlag(getExclusiveOrderInfoResp.getExclusiveFlag());
			orderLabel.setRecruitTagName(getExclusiveOrderInfoResp.getRecruitTagName());
			return orderLabel;
		}

		return null;
	}

	private final String ORDER_MATCH_ROUTE = "order_match_route";
	public boolean sendOrderMatchRouteMessage(OrderMatchRouteMessage message,Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,ORDER_MATCH_ROUTE,JSON.toJSONString(message),delayTime);
		return true;
	}


	private final String COMPENSATE_DISTRIBUTE = "compensate_distribute";
	public boolean sendCompensateDistributeMessage(CompensateDistributeMessage message,Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,COMPENSATE_DISTRIBUTE,JSON.toJSONString(message),delayTime);
		return true;
	}


	private final String WHEEL_ROUNDS_PUSH = "wheel_rounds_push";
	public boolean sendWheelRoundPush(WheelRoundsPushMessage message,Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,WHEEL_ROUNDS_PUSH,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	private final static String MATCH_SUPPORT_MASTER_RESULT = "match_support_master_result";
	public boolean sendSupportMasterMatchMessage(SupportMasterPushResultMessage message,Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,MATCH_SUPPORT_MASTER_RESULT,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	private final static String DYNAMIC_AUTO_RECEIVE = "dynamic_auto_receive";
	public boolean sendDynamicAutoReceiveMessage(DynamicAutoReceiveMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,DYNAMIC_AUTO_RECEIVE,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	private final static String MATCH_AGREEMENT_MASTER_RESULT = "match_agreement_master_result";
	public boolean sendAgreementMasterMatchMessage(EnterpriseMatchAgreementMasterResultMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,MATCH_AGREEMENT_MASTER_RESULT,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}

//	public boolean sendEnterpriseAppointMasterMatchMessage(EnterpriseAppointMasterResultMessage message, Long delayTime){
//		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,MATCH_AGREEMENT_MASTER_RESULT,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
//		return true;
//	}


	private final static String INTERFERE_ORDER_PUSH = "interfere_order_push";
	public boolean sendInterfereOrderPushMessage(List<PushMaster> pushMasterList,Long globalOrderId,Long orderId){
		JSONObject pushContent = new JSONObject();
		pushContent.put("orderId", orderId);
		pushContent.put("globalOrderTraceId", globalOrderId);
		pushContent.put("masterList", pushMasterList);
		rocketMqSendService.sendSyncMessage(orderMatchMasterTopic, INTERFERE_ORDER_PUSH, pushContent.toJSONString());

		return true;
	}


	private final static String LONG_TAIL_BATCH_PUSH = "long_tail_batch_push";
	public boolean sendLongTailBatchPushMessage(LongTailBatchPushMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,LONG_TAIL_BATCH_PUSH,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	private final static String PORT_PUSH = "port_push";
	public boolean sendPortPushMessage(PortPushMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,PORT_PUSH,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}

	private final static String NORMAL_ORDER_DISTRIBUTE = "normal_order_distribute";
	public boolean sendNormalOrderDistribute(CompensateDistributeMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,NORMAL_ORDER_DISTRIBUTE,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	private final static String PUSH_GOLD_MEDAL_MASTER = "push_gold_medal_master";
	public boolean sendPushGoldMedalMasterMessage(PushGoldMedalMasterMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,PUSH_GOLD_MEDAL_MASTER,JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}


	public boolean sendTechniqueMasterMatchResultMessage(TechniqueVerifyMasterMatchResultMessage message, Long delayTime){
		rocketMqSendService.sendDelayMessage(orderMatchMasterTopic,"support_master_match",JSON.toJSONString(message, SerializerFeature.DisableCircularReferenceDetect),delayTime);
		return true;
	}
}
