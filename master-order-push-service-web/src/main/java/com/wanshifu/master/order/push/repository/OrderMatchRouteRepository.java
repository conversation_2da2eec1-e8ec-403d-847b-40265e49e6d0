package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.mapper.OrderMatchRouteMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class OrderMatchRouteRepository extends BaseRepository<OrderMatchRoute> {

    @Resource
    private OrderMatchRouteMapper orderMatchRouteMapper;

    public Integer insert(String routeName,String routeDesc,String orderPushFlag,String orderPriorityMatchRule,
                          String orderStandbyMatchRule,Long createAccountId){
        OrderMatchRoute orderMatchRoute = new OrderMatchRoute();
        orderMatchRoute.setRouteName(routeName);
        orderMatchRoute.setRouteDesc(routeDesc);
        orderMatchRoute.setOrderPushFlag(orderPushFlag);
        orderMatchRoute.setOrderPriorityMatchRule(orderPriorityMatchRule);
        orderMatchRoute.setOrderStandbyMatchRule(orderStandbyMatchRule);
        orderMatchRoute.setCreateTime(new Date());
        orderMatchRoute.setUpdateTime(new Date());
        orderMatchRoute.setCreateAccountId(createAccountId);
        orderMatchRoute.setUpdateAccountId(createAccountId);
        return this.insertSelective(orderMatchRoute);

    }


    public Integer update(Integer routeId,String routeName,String routeDesc,String orderPushFlag,String orderPriorityMatchRule,
                          String orderStandbyMatchRule,Long updateAccountId){
        OrderMatchRoute orderMatchRoute = new OrderMatchRoute();
        orderMatchRoute.setRouteId(routeId);
        orderMatchRoute.setRouteName(routeName);
        orderMatchRoute.setRouteDesc(routeDesc);
        orderMatchRoute.setOrderPushFlag(orderPushFlag);
        orderMatchRoute.setOrderPriorityMatchRule(orderPriorityMatchRule);
        orderMatchRoute.setOrderStandbyMatchRule(orderStandbyMatchRule);
        orderMatchRoute.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(orderMatchRoute);

    }


    public OrderMatchRoute selectByRouteName(String routeName, Integer routeId) {
        Example example = new Example(OrderMatchRoute.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("routeName", routeName);
        if (routeId != null) {
            criteria.andNotEqualTo("routeId", routeId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public OrderMatchRoute selectByOrderPushFlag(String orderPushFlag, Integer routeId) {
        Example example = new Example(OrderMatchRoute.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderPushFlag", orderPushFlag);
        if (routeId != null) {
            criteria.andNotEqualTo("routeId", routeId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

    public List<OrderMatchRoute> selectList(OrderMatchRouteListRqt rqt){
        return orderMatchRouteMapper.selectList(rqt);
    }

    

}