package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import lombok.Data;

@Data
@Document(indexAlias = "master_order_package_appoint", type = "master_order_package_appoint")
public class MasterOrderPackageAppoint {

    private String id;

    private String masterId;

    private String packageConfigId;

    private Long remainAppointOrderNumber;

    private String serveId;

    private String attributeKey;

    private String attributeValueMax;

    private String isDelete;


}
