package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 推送师傅记录表
 * <AUTHOR>
 */
@Data
@Table(name = "master_push")
public class MasterPush {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "push_id")
    private Long pushId;

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "master_id")
    private Long masterId;

    @Column(name = "order_version")
    private String orderVersion;

    @Column(name = "master_type")
    private String masterType;

    @Column(name = "list_offset")
    private Integer listOffset;

    @Column(name = "push_offset")
    private Integer pushOffset;

    @Column(name = "score")
    private BigDecimal score;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
