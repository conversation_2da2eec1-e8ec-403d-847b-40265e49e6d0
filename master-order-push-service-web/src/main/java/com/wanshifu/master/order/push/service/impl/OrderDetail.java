package com.wanshifu.master.order.push.service.impl;


import com.wanshifu.master.order.push.util.DateFormatterUtil;

/**
 * <AUTHOR>
 */
public abstract class OrderDetail {

    private String orderId;
    private String type;
    private String time= DateFormatterUtil.timeStampToTime(System.currentTimeMillis());

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTime() {
        return time;
    }

    /**
     * 获取订单特征数据
     * @return
     */
    public abstract String getDetailInfo();

}
