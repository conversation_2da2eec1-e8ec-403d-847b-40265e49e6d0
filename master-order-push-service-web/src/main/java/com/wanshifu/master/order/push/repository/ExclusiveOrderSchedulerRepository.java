package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 专属
 * <AUTHOR>
 */
@Repository
public class ExclusiveOrderSchedulerRepository extends BaseRepository<ExclusiveOrderScheduler> {


    public int insert(
            Integer businessLineId,
            Integer categoryId,
            Integer scheduleTime,
            Long updateAccountId,
            Long createAccountId) {
        ExclusiveOrderScheduler exclusiveOrderScheduler = new ExclusiveOrderScheduler();
        exclusiveOrderScheduler.setBusinessLineId(businessLineId);
        exclusiveOrderScheduler.setCategoryId(categoryId);
        exclusiveOrderScheduler.setScheduleTime(scheduleTime);
        exclusiveOrderScheduler.setUpdateAccountId(updateAccountId);
        exclusiveOrderScheduler.setCreateAccountId(createAccountId);
        return super.insertSelective(exclusiveOrderScheduler);
    }

    public int insert(
            Integer businessLineId,
            List<Integer> categoryIdList,
            Integer scheduleTime,
            Long updateAccountId,
            Long createAccountId) {
        final ArrayList<ExclusiveOrderScheduler> exclusiveOrderSchedulers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(categoryIdList)) {
            categoryIdList.forEach(row->{
                final Integer cate = Integer.valueOf(row);
                ExclusiveOrderScheduler exclusiveOrderScheduler = new ExclusiveOrderScheduler();
                exclusiveOrderScheduler.setBusinessLineId(businessLineId);
                exclusiveOrderScheduler.setCategoryId(cate);
                exclusiveOrderScheduler.setScheduleTime(scheduleTime);
                exclusiveOrderScheduler.setUpdateAccountId(updateAccountId);
                exclusiveOrderScheduler.setCreateAccountId(createAccountId);
                exclusiveOrderScheduler.setIsDelete(0);
                final Date date = new Date();
                exclusiveOrderScheduler.setCreateTime(date);
                exclusiveOrderScheduler.setUpdateTime(date);
                exclusiveOrderSchedulers.add(exclusiveOrderScheduler);
            });
        }
        return super.insertList(exclusiveOrderSchedulers);
    }

    public int update(Long configId,
            Integer businessLineId,
            Integer categoryId,
            Integer scheduleTime,
            Long updateAccountId) {
        ExclusiveOrderScheduler exclusiveOrderScheduler = new ExclusiveOrderScheduler();
        exclusiveOrderScheduler.setConfigId(configId);
        exclusiveOrderScheduler.setBusinessLineId(businessLineId);
        exclusiveOrderScheduler.setCategoryId(categoryId);
        exclusiveOrderScheduler.setScheduleTime(scheduleTime);
        exclusiveOrderScheduler.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(exclusiveOrderScheduler);
    }


    public List<ExclusiveOrderScheduler> selectList(
            Integer businessLineId,
            List<Integer> categoryIdList) {

        Example example = new Example(ExclusiveOrderScheduler.class);
        Example.Criteria criteria = example.createCriteria();
        if (businessLineId != null) {
            criteria.andEqualTo("businessLineId", businessLineId);
        }
        if (categoryIdList != null&&categoryIdList.size()!=0) {
            criteria.andIn("categoryId",categoryIdList);
        }
        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        example.orderBy("updateTime").desc();
        return this.selectByExample(example);
    }



}