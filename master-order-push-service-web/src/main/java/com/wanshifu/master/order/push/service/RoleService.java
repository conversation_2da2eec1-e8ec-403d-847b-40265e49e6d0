package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.domain.resp.role.RoleDetailResp;
import com.wanshifu.master.order.push.domain.resp.role.RoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;

import java.util.List;

public interface RoleService {

    int add(AddRoleRqt rqt);

    int update(UpdateRoleRqt rqt);

    SimplePageInfo<RoleListResp> list(GetRoleListRqt rqt);

    int delete(DeleteRoleRqt rqt);

    RoleDetailResp detail(GetRoleDetailRqt rqt);

    int addAccount(AddAccountRqt rqt);

    List<Role> batchRoleList(BatchRoleListRqt rqt);


}
