package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.mapper.PushPortRuleMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * 推单端口规则
 * <AUTHOR>
 */
@Repository
public class PushPortRuleRepository extends BaseRepository<PushPortRule> {


    @Resource
    private PushPortRuleMapper pushPortRuleMapper;




    public int insertRule(String ruleName,String ruleDesc,String orderTag,String cityIds,String lv1ServeIds,
                          String appointType,Integer intervalTime,Integer offerNum,Long createAccountId,String pushRule){
        PushPortRule pushPortRule = new PushPortRule();
        pushPortRule.setRuleName(ruleName);
        pushPortRule.setRuleDesc(ruleDesc);
        pushPortRule.setOrderTag(orderTag);
        pushPortRule.setCityIds(cityIds);
        pushPortRule.setLv1ServeIds(lv1ServeIds);
        pushPortRule.setAppointType(appointType);
        pushPortRule.setIntervalTime(intervalTime);
        pushPortRule.setOfferNum(offerNum);
        pushPortRule.setRuleStatus(0);
        pushPortRule.setCreateAccountId(createAccountId);
        pushPortRule.setUpdateAccountId(createAccountId);
        pushPortRule.setPushRule(pushRule);
        return this.insertSelective(pushPortRule);
    }


    public int updateRule(Integer ruleId,String ruleName,String ruleDesc,String orderTag,String cityIds,String lv1ServeIds,
                          String appointType,Integer intervalTime,Integer offerNum,Long updateAccountId,String pushRule){
        PushPortRule pushPortRule = new PushPortRule();
        pushPortRule.setRuleId(ruleId);
        pushPortRule.setRuleName(ruleName);
        pushPortRule.setRuleDesc(ruleDesc);
        pushPortRule.setOrderTag(orderTag);
        pushPortRule.setCityIds(cityIds);
        pushPortRule.setLv1ServeIds(lv1ServeIds);
        pushPortRule.setAppointType(appointType);
        pushPortRule.setIntervalTime(intervalTime);
        pushPortRule.setOfferNum(offerNum);
        pushPortRule.setUpdateAccountId(updateAccountId);
        pushPortRule.setPushRule(pushRule);
        return this.updateByPrimaryKeySelective(pushPortRule);
    }


    public PushPortRule selectByRuleName(String ruleName ,Integer ruleId) {
        Example example = new Example(PushPortRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleName", ruleName)
                .andEqualTo("isDelete", 0);
        if (ruleId != null) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public List<PushPortRule> selectList(@Param("lv1ServeId") Long lv1ServeId, @Param("orderTag") String orderTag,
                                  @Param("appointType")Integer appointType){
        return pushPortRuleMapper.selectList(lv1ServeId,orderTag,appointType);
    }


    public int updateStatus(Integer ruleId, Integer ruleStatus,Long updateAccountId) {
        PushPortRule pushPortRule = new PushPortRule();
        pushPortRule.setRuleId(ruleId);
        pushPortRule.setRuleStatus(ruleStatus);
        pushPortRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(pushPortRule);
    }


    public List<PushPortRule> selectByCityAndLv1ServeId(List<String> orderTagList, List<String> cityIdList, List<String> lv1ServeIdList,List<String> appointTypeList, Integer ruleId) {
        return pushPortRuleMapper.selectByCityAndLv1ServeId(orderTagList, cityIdList,lv1ServeIdList, appointTypeList,ruleId);
    }


}