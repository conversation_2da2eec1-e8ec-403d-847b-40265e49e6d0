package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.Master;
import com.wanshifu.master.order.push.domain.po.MasterOrderPackageIndex;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.mapper.MasterMapper;
import com.wanshifu.master.order.push.mapper.MasterOrderPackageMapper;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterOrderPackageRepository extends BaseRepository<MasterOrderPackageIndex> {

    @Resource
    private MasterOrderPackageMapper masterOrderPackageMapper;


    public List<MasterOrderPackageIndex> selectByPackageConfigIds(Set<String> packageConfigIdSet){
        Example example = new Example(MasterOrderPackageIndex.class);
        example.createCriteria().andIn("packageConfigId", packageConfigIdSet)
                .andGreaterThan("remainAppointOrderNumber",0L)
                .andEqualTo("statementStatus","pre")
                .andEqualTo("hasPausePush","0")
                .andEqualTo("hasRecycle","0");
        return this.selectByExample(example);
    }


    public MasterOrderPackageIndex selectByPackageConfigIdAndMasterId(String packageConfigId,String masterId){
        MasterOrderPackageIndex masterOrderPackageIndex = new MasterOrderPackageIndex();
        masterOrderPackageIndex.setPackageConfigId(packageConfigId);
        masterOrderPackageIndex.setMasterId(masterId);
        return CollectionUtils.getFirstSafety(this.select(masterOrderPackageIndex));
    }


}