package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.manage.config.domain.api.request.release.serviceSkillRelation.GetServiceToSkillMapRqt;
import com.wanshifu.master.manage.config.domain.api.request.release.serviceSkillRelation.GetSkillIdsByServiceIdsRqt;
import com.wanshifu.master.manage.config.domain.api.request.release.serviceSkillRelation.GetSpecialPushRelationRqt;
import com.wanshifu.master.manage.config.service.api.release.ServiceSkillRelationServiceApi;
import com.wanshifu.master.manage.config.service.api.release.SkillServiceApi;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.api.TechniqueServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdAndLevelResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import com.wanshifu.util.SetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description: 作用描述 特殊类目绑定关系
 * @author: <EMAIL>
 * @createDate: 2021/1/2815:54
 * @version: 1.0
 **/
@Slf4j
@Service
public class BindingTechnologyCommon {

    @Resource
    TechniqueServiceApi techniqueServiceApi;

    @Resource
    ServeServiceApi serveServiceApi;

    @Resource
    SkillServiceApi skillServiceApi;

    @Resource
    OrderCommon orderCommon;

    @Resource
    ServiceSkillRelationServiceApi serviceSkillRelationServiceApi;

    @Resource
    Tools tools;

    @Value("${recursionCalculationTechnologyCount:10000}")
    int recursionCalculationTechnologyCount;

    /**
     * 收敛
     *
     * @param result
     * @return
     */
    public static Set<Set<Long>> constrict(Set<Set<Long>> result) {
        Set<Set<Long>> constrictResult = new HashSet<>();
        for (Set<Long> left : result) {
            boolean isRight = true;
            for (Set<Long> right : result) {
                if (left.containsAll(right) && (!left.equals(right))) {
                    isRight = false;
                    break;
                }
            }
            if (isRight) {
                constrictResult.add(left);
            }
        }
        return constrictResult;
    }

    /**
     * 排列组合
     *
     * @param source
     * @return
     */
    public static void assemble(List<List<Long>> source, Set<Set<Long>> result, HashSet<Long> current, int layer) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        if (source.size() == layer + 1) {
            for (Long number : source.get(layer)) {
                HashSet<Long> fresh = new HashSet<>(current);
                fresh.add(number);
                result.add(fresh);
            }
        } else {
            for (Long number : source.get(layer)) {
                HashSet<Long> fresh = new HashSet<>(current);
                fresh.add(number);
                assemble(source, result, fresh, layer + 1);
            }
        }
    }

    public static void assemble(List<List<Long>> source, Set<Set<Long>> result, HashSet<Long> current, int layer, AtomicInteger num, int limit) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        if (source.size() == layer + 1) {
            for (Long number : source.get(layer)) {
                HashSet<Long> fresh = new HashSet<>(current);
                fresh.add(number);
                result.add(fresh);
                num.set(num.get() + 1);
                if (num.get() >= limit) {
                    return;
                }

            }
        } else {
            for (Long number : source.get(layer)) {
                if (num.get() >= limit) {
                    return;
                }
                HashSet<Long> fresh = new HashSet<>(current);
                fresh.add(number);
                assemble(source, result, fresh, layer + 1, num, limit);
            }
        }
    }


    /***
     * 计算订单推单组合 成品业务特殊商品推单  卫浴、晾衣架
     * @return
     */
    public String getBindingTechnology(String serveIds,Long businessLineId,String orderFrom,String accountType) {


        Set<Long> severIdsSet = CollUtil.newHashSet(StringUtils.splitCommaToList(serveIds, Long::parseLong));

        //先转成二级服务
        List<ServeIdAndLevelResp> level2ServeIdByServeId = serveServiceApi.getLevel2ServeIdByServeId(severIdsSet);

        Set<Long> level2ServeIdSet = level2ServeIdByServeId.stream().map(ServeIdAndLevelResp::getServeId).collect(Collectors.toSet());

        String level2ServeIdStr = StringUtils.listToCommaSplit(ListUtil.toList(level2ServeIdSet), String::valueOf);

        GetServiceToSkillMapRqt serviceToSkillMapRqt = new GetServiceToSkillMapRqt();
        serviceToSkillMapRqt.setServiceIds(level2ServeIdStr);
        Map<String, Set<Long>> serviceToSkillMap = serviceSkillRelationServiceApi.getServiceToSkillMap(serviceToSkillMapRqt);
        if (ObjectUtil.isEmpty(serviceToSkillMap)) {
//            feiShuRobotNotifyUtils.sendRichTextNotice("计算订单推单组合异常", String.format("【参数】：serveIds=%s orderNo=%s\n\n【错误】：%s", level2ServeIdStr, orderBase.getOrderNo(), "调用师傅管理 serviceSkillRelationServiceApi.getServiceToSkillMap 返回为空"));
            return "";
        }

        //获取特殊推单规则
        {

            if (CollectionUtils.isNotEmpty(level2ServeIdSet)) {

                ServeIdSetReq serveIdSetReq = new ServeIdSetReq(level2ServeIdSet, 0L);
                List<ServeBaseInfoResp> serveBaseInfo = serveServiceApi.getServeBaseInfo(serveIdSetReq);
                Set<Long> level1ServeId = serveBaseInfo.stream().map(ServeBaseInfoResp::getLevel1Id).collect(Collectors.toSet());

                if (CollectionUtils.isNotEmpty(level1ServeId)) {

                    //查询特殊推单规则
                    GetSpecialPushRelationRqt pushRelationRqt = new GetSpecialPushRelationRqt();
                    pushRelationRqt.setServiceIds(level1ServeId);
                    Map<String, List<Long>> specialPushRelationMap = serviceSkillRelationServiceApi.getSpecialPushRelation(pushRelationRqt);

                    serveBaseInfo.forEach(e -> {
                        String level1Id = e.getLevel1Id().toString();
                        String serveId = e.getServeId().toString();

                        if (specialPushRelationMap.containsKey(level1Id)) {
                            List<Long> specialPush = specialPushRelationMap.get(level1Id);
                            if (serviceToSkillMap.containsKey(serveId)) {
                                serviceToSkillMap.get(serveId).addAll(specialPush);
                            } else {
                                serviceToSkillMap.put(serveId, CollUtil.newHashSet(specialPush));
                            }
                        }

                    });
                }
            }
        }

        //服务太多，计算技能组合会导致cpu占用过高，正式环境限制5个服务，其他环境限制3个服务
//        int serviceNum = 4;
//        if (tools.isProEnv()) {
//            serviceNum = 5;
//        }
//
//        listMap = listMap.entrySet().stream().limit(serviceNum).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        //v7.7 家庭订单 师傅具备订单内任一服务的技能，就推单
        if (orderCommon.isFamilyOrder(businessLineId, orderFrom, accountType)) {
            List<String> longList = serviceToSkillMap.values().stream().flatMap(Collection::stream).map(Object::toString).collect(Collectors.toList());
            return String.join("|", longList);
        }

        List<List<Long>> technologyIds = serviceToSkillMap.values().stream().map(ListUtil::toList).distinct().collect(Collectors.toList());

        Set<Set<Long>> result = new HashSet<>();
        // 组合
        assemble(technologyIds, result, new HashSet<>(), 0, new AtomicInteger(0), recursionCalculationTechnologyCount);
        // 收敛
        Set<Set<Long>> finalSet = constrict(result);
        //计算绑定技能
        TreeSet<String> resp = new TreeSet<>();
        for (Set<Long> info : finalSet) {
            resp.add(SetUtil.setToStr(info));
        }

        String join = String.join("|", resp);

        if (StringUtils.isEmpty(join)) {
//            feiShuRobotNotifyUtils.sendRichTextNotice("计算订单推单组合异常", String.format("【参数】：serveIds=%s orderNo=%s\n\n【错误】：%s", serveIds, orderBase.getOrderNo(), "调用师傅管理 serviceSkillRelationServiceApi.getServiceToSkillMap"));
        }
        return join;
    }

    /***
     * 宜家订单绑定关系 特殊推单
     * @return bindIds
     */
    public String getIkeaBindingTechnology() {
        List<Long> ikeaSpecialTechniqueIds = skillServiceApi.getIkeaSpecialSkillIds();
        log.info("===========techniqueAndServeServiceApi===getIkeaSpecialTechniqueIds resp={} ", ikeaSpecialTechniqueIds);
        if (CollectionUtils.isNotEmpty(ikeaSpecialTechniqueIds)) {
            return ikeaSpecialTechniqueIds.stream().map(String::valueOf).collect(Collectors.joining("|"));
        }
        return "";
    }

    /***
     * 计算订单推单组合 创新业务
     * @return technologyIds
     */
    public String getInnovateBindingTechnology(String serveIds) {
        Set<Long> skillIdsSet = this.getSkillIdSet(serveIds);
        return skillIdsSet.stream().map(String::valueOf).collect(Collectors.joining("|"));
    }

    private Set<Long> getSkillIdSet(String serveIds) {

        if (StringUtils.isEmpty(serveIds)) return Collections.emptySet();

        Set<Long> severIdsSet = CollUtil.newHashSet(StringUtils.splitCommaToList(serveIds, Long::parseLong));

        List<ServeIdAndLevelResp> level2ServeIdByServeId = serveServiceApi.getLevel2ServeIdByServeId(severIdsSet);

        String level2ServeIdsStr = StringUtils.listToCommaSplit(level2ServeIdByServeId, serveIdAndLevelResp -> serveIdAndLevelResp.getServeId().toString());

        GetSkillIdsByServiceIdsRqt rqt = new GetSkillIdsByServiceIdsRqt();
        rqt.setServiceIds(level2ServeIdsStr);
        return serviceSkillRelationServiceApi.getSkillIdsByServiceIds(rqt);
    }

    /***
     * 按照服务查找技能
     * @param serveIds
     * @return
     */
    public String getTechnologyByServeIds(String serveIds) {
        Set<Long> skillIdsSet = this.getSkillIdSet(serveIds);
        return SetUtil.setToStr(skillIdsSet);
    }

}
