package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

@Data
public class CalculatePushDistanceRespVo {
    /**
     * 师傅常驻地址和客户之间的路径规划距离
     */
    private Long pushDistance;

    /**
     * 推单距离类型 1：导航距离(默认) 2:直线距离
     */
    private Integer pushDistanceType;

    public CalculatePushDistanceRespVo(Long pushDistance,Integer pushDistanceType){
        this.pushDistance = pushDistance;
        this.pushDistanceType = pushDistanceType;

    }
}
