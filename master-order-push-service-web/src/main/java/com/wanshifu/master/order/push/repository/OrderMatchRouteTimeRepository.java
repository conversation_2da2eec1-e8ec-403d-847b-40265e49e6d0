package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.mapper.OrderMatchRouteTimeMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class OrderMatchRouteTimeRepository extends BaseRepository<OrderMatchRouteTime> {

    @Resource
    private OrderMatchRouteTimeMapper orderMatchRouteTimeMapper;


    public Integer insert(Integer businessLineId,String categoryIds,String appointTypes,String settingType,Integer settingTime,
                          Integer settingNum,Long createAccountId){
        OrderMatchRouteTime orderMatchRouteTime = new OrderMatchRouteTime();
        orderMatchRouteTime.setBusinessLineId(businessLineId);
        orderMatchRouteTime.setCategoryIds(categoryIds);
        orderMatchRouteTime.setAppointTypes(appointTypes);
        orderMatchRouteTime.setSettingType(settingType);
        orderMatchRouteTime.setSettingTime(settingTime);
        orderMatchRouteTime.setSettingNum(settingNum);
        orderMatchRouteTime.setCreateAccountId(createAccountId);
        orderMatchRouteTime.setUpdateAccountId(createAccountId);
        return this.insertSelective(orderMatchRouteTime);

    }


    public Integer update(Integer routeTimeId,Integer businessLineId,String categoryIds,String appointTypes,String settingType,Integer settingTime,
                          Integer settingNum,Long createAccountId){
        OrderMatchRouteTime orderMatchRouteTime = new OrderMatchRouteTime();
        orderMatchRouteTime.setRouteTimeId(routeTimeId);
        orderMatchRouteTime.setBusinessLineId(businessLineId);
        orderMatchRouteTime.setCategoryIds(categoryIds);
        orderMatchRouteTime.setAppointTypes(appointTypes);
        orderMatchRouteTime.setSettingType(settingType);
        orderMatchRouteTime.setSettingTime(settingTime);
        orderMatchRouteTime.setSettingNum(settingNum);
        orderMatchRouteTime.setUpdateAccountId(createAccountId);
        return this.updateByPrimaryKeySelective(orderMatchRouteTime);

    }


    public List<OrderMatchRouteTime> selectList(OrderMatchRouteTimeListRqt rqt){
        return orderMatchRouteTimeMapper.selectList(rqt);
    }


    public List<OrderMatchRouteTime> selectByCategoryIdAndAppointType(Integer businessLineId,Long categoryId,Integer appointType){
       return orderMatchRouteTimeMapper.selectByCategoryIdAndAppointType(businessLineId,categoryId,appointType);
    }


}