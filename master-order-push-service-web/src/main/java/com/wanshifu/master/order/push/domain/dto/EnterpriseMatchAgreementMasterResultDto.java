package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.message.EnterpriseMatchAgreementMasterResultMessage;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/16 19:39
 */
@Data
public class EnterpriseMatchAgreementMasterResultDto {

    /**
     * 能够指派的协议师傅
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMaster;

    /**
     * 过滤掉的协议师傅
     *
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMaster;
}
