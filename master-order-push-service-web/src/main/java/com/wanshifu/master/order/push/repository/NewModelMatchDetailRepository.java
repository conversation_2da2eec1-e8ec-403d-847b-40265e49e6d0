package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;

import com.wanshifu.master.order.push.domain.po.NewModelMatchDetail;
import com.wanshifu.master.order.push.mapper.NewModelMatchDetailMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Repository
public class NewModelMatchDetailRepository extends BaseRepository<NewModelMatchDetail> {


    @Resource
    private NewModelMatchDetailMapper newModelMatchDetailMapper;


    public NewModelMatchDetail selectByOrderIdAndMasterId(Long orderId,Long masterId){
        Condition condition = new Condition(NewModelMatchDetail.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId).andEqualTo("masterId",masterId).andEqualTo("isDistribute",1);
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public List<NewModelMatchDetail> selectList(String orderNo, Long masterId, Date orderCreateTimeStart, Date orderCreateTimeEnd) {
        return newModelMatchDetailMapper.selectList(orderNo, masterId, orderCreateTimeStart, orderCreateTimeEnd);
    }

}
