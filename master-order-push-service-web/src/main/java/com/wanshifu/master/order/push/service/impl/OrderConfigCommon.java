package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

@Component
public class OrderConfigCommon {


    @Resource
    private ServeServiceApi serveServiceApi;

    @Resource
    private Tools tools;




    /**
     * 获取订单服务名称
     *
     * @param orderDetailData
     * @return
     */
    public String getOrderServeName(OrderDetailData orderDetailData) {

        String serveLevel1Ids = orderDetailData.getLv1ServeIds();
        String serveIds = orderDetailData.getLv3ServeIds();
        String serveId = serveIds.contains(",") || StringUtils.isEmpty(serveIds) || !this.isFamilyOrder(orderDetailData) ? serveLevel1Ids : serveIds;

        //因为 serveLevel1Ids b端传错了，兼容使用ServeTypeId
        if (!this.isFamilyOrder(orderDetailData)) {
            serveId = orderDetailData.getOrderServeType().toString();
        }

        List<ServeBaseInfoResp> orderServeList = getServeList(serveId, orderDetailData.getBusinessLineId());
        if (CollectionUtils.isNotEmpty(orderServeList)) {
            ServeBaseInfoResp serve = orderServeList.get(0);
            Byte isServeGroup = serve.getIsServeGroup();
            if (1 == isServeGroup) {
                List<ServeBaseInfoResp> serveList = getServeList(serveLevel1Ids, orderDetailData.getBusinessLineId());
                if (CollectionUtils.isNotEmpty(serveList)) {
                    serve = serveList.get(0);
                    return serve.getName();
                }
            } else {
                return serve.getName();
            }
        }
        return "";
    }

    public boolean isFamilyOrder(OrderDetailData orderDetailData) {
        return orderDetailData.getBusinessLineId().equals(2) && (OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom()) || AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType()));
    }




    
    /**
     * 获取订单服务
     *
     * @param serveIds
     * @return
     */
    public List<ServeBaseInfoResp> getServeList(String serveIds, Integer businessLineId) {
        ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
        serveIdSetReq.setServeIdSet(new HashSet(Arrays.asList(serveIds.split(","))));
        if (businessLineId != null) {
            serveIdSetReq.setBusinessLineId(businessLineId.longValue());
        }
        return tools.catchLogThrow(() -> serveServiceApi.getServeBaseInfo(serveIdSetReq));
    }

    /**
     * 获取订单服务
     *
     * @param serveIds
     * @return
     */
    public List<ServeBaseInfoResp> getServeList(String serveIds, Long businessLineId) {
        ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
        serveIdSetReq.setServeIdSet(new HashSet(Arrays.asList(serveIds.split(","))));
        if (businessLineId != null) {
            serveIdSetReq.setBusinessLineId(businessLineId.longValue());
        }
        return serveServiceApi.getServeBaseInfo(serveIdSetReq);
    }

}
