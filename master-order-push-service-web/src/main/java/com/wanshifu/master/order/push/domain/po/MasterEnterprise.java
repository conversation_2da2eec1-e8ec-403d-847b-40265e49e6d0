package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "t_master_enterprise")
public class MasterEnterprise {

    @Id
    @Column(name = "id")
    private String id;

    private String enterpriseId;

    private String masterId;

    private String enterpriseLastBlacklistTime;

}
