package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.po.OrderStrategyRelate;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.CreateOrderScoringStrategyRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;

import com.wanshifu.master.order.push.mapper.OrderSelectStrategyMapper;
import com.wanshifu.master.order.push.repository.MasterQuotaRepository;
import com.wanshifu.master.order.push.repository.MasterQuotaValueRepository;
import com.wanshifu.master.order.push.repository.OrderSelectStrategyRepository;
import com.wanshifu.master.order.push.repository.OrderStrategyRelateRepository;
import com.wanshifu.master.order.push.service.OrderSelectStrategyService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 11:31
 */
@Service
public class OrderSelectStrategyServiceImpl implements OrderSelectStrategyService {


    @Resource
    private OrderSelectStrategyRepository orderSelectStrategyRepository;


    @Resource
    private MasterQuotaRepository masterQuotaRepository;

    @Resource
    private MasterQuotaValueRepository masterQuotaValueRepository;

    @Resource
    private OrderStrategyRelateRepository orderStrategyRelateRepository;



    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "非普通订单匹配", level2MenuName = "师傅筛选策略",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public Integer create(CreateOrderSelectStrategyRqt rqt){

        String strategyName = rqt.getStrategyName();
        Long businessLineId = rqt.getBusinessLineId();
        this.checkStrategyName(strategyName, businessLineId, null);
        CreateOrderSelectStrategyRqt.SelectStrategy selectStrategy = rqt.getSelectStrategy();
//        String selectStrategy = JSON.toJSONString(rqt.getSelectStrategy());
        //获取精排策略表达式集合
//        SelectStrategyExpressionDto selectExpression = this.getSelectExpression(rqt.getSelectStrategy());


        SelectStrategyExpressionDto selectRuleExpressionDto = this.getSelectExpression(businessLineId, selectStrategy);


        return orderSelectStrategyRepository.insert(businessLineId,strategyName,rqt.getOrderFrom(), rqt.getStrategyDesc(), rqt.getMasterResources(), JSON.toJSONString(selectStrategy), JSON.toJSONString(selectRuleExpressionDto), rqt.getCreateAccountId());

    }


    private List<SelectStrategyRuleExpressionDto> getRuleExpressions(Long businessLineId, List<RuleItem> ruleList) {

        List<String> allMasterQuotaCodes = ruleList.stream().flatMap(it -> it.getFilterRule().getItemList().stream()).map(RuleItem.FilterRuleItem::getItemName).filter(it -> !StringUtils.equals("master_group", it)).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        //<指标编码,指标条件表达式>   例如  <master_work_status,{"preCondition":{"ruleExpression":"is_high_quality == 1","ruleExpressionParams":"is_high_quality"},"calculateExpression":"allMatch(itemName,'term',value) "}>
        Map<String, String> masterQuotaFeatureExpressionMap = masterQuotaRepository.selectByCodes(allMasterQuotaCodes, businessLineId.intValue())
                .stream().collect(Collectors.toMap(MasterQuota::getQuotaCode, MasterQuota::getFeatureExpression));

        return ruleList.stream().map(rule -> {

            RuleItem.OpenCondition openCondition = rule.getOpenCondition();

            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换,增值服务操作符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user", "user_group", "value_added_service");
                        if (specialTerms.contains(s.getItemName())) {
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<RuleItem.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }

            RuleItem.FilterRule filterRule = rule.getFilterRule();
            String condition = filterRule.getCondition();
            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(RuleItem.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));
            //召回规则表达式参数
            List<String> filterRuleParamList = new ArrayList<>(Collections.emptyList());
            List<String> masterQuotaExpressions = filterRule.getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                filterRuleParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(), null);

                //指标是否包含前置条件
                String masterQuotaFeatureJson = masterQuotaFeatureExpressionMap.get(it.getItemName());
                if (StringUtils.isNotBlank(masterQuotaFeatureJson)) {
                    MasterQuotaFeatureExpressionDto masterQuotaFeatureExpressionDto = JSON.parseObject(masterQuotaFeatureJson, MasterQuotaFeatureExpressionDto.class);
                    String calculateExpression = masterQuotaFeatureExpressionDto.getCalculateExpression();
                    if (StringUtils.isNotBlank(calculateExpression)) {
                        //特殊的函数计算表达式
                        masterQuotaFilterRuleExpression = " " + calculateExpression
                                .replace("itemName", itemName)
                                .replace("term", term)
                                .replace("value", it.getItemValue());
                    }

                    if (masterQuotaFeatureExpressionDto.getPreCondition() != null) {
                        filterRuleParamList.addAll(Arrays.asList(masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpressionParams().split(",")));
                        masterQuotaOpenConditionRuleExpression = masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpression();
                        //(规则1开启条件满足 ? 规则1 : true )
                        //(规则1开启条件满足 ? 规则1 : false )
                        masterQuotaFilterRuleExpression = StrUtil.format(" ({} ?{}: {}) ", masterQuotaOpenConditionRuleExpression, masterQuotaFilterRuleExpression, StringUtils.equals(condition, "and"));
                    }
                }
                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());
            // (规则1开启条件满足 ? 规则1 : true ) and 规则2 and 规则3
            // (规则1开启条件满足 ? 规则1 : false ) or 规则2 or 规则3
            String filterRuleExpression = QlExpressUtil.transitionQlExpressStr(condition, masterQuotaExpressions);

            String filterRuleParams = filterRuleParamList.stream().distinct().collect(Collectors.joining(","));
            return new SelectStrategyRuleExpressionDto(rule.getRuleName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }


    private SelectStrategyExpressionDto getSelectExpression(Long businessLineId, CreateOrderSelectStrategyRqt.SelectStrategy selectStrategy) {
        SelectStrategyExpressionDto expressionDto = new SelectStrategyExpressionDto();


        if (selectStrategy.getAppointGroup().getIsAppointGroup() == 1) {
            List<String> appointGroupParamList = new ArrayList<>(Collections.emptyList());
            List<String> appointGroupExpressions = selectStrategy.getAppointGroup().getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                appointGroupParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(), null);

                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());


            String appointGroupExpression = QlExpressUtil.transitionQlExpressStr("and", appointGroupExpressions);

            String appointGroupParams = appointGroupParamList.stream().distinct().collect(Collectors.joining(","));

            expressionDto.setAppointGroupExpression(appointGroupExpression);
            expressionDto.setAppointGroupParams(appointGroupParams);
        }


        expressionDto.setFilterRuleList(this.getRuleExpressions(businessLineId, selectStrategy.getRuleList()));

        return expressionDto;

    }


    /**
     * 校验匹配项参数
     *
     * @param itemList
     */
    private void checkRuleParams(List<CreateOrderScoringStrategyRqt.RuleItem> itemList) {
        itemList.forEach(it -> {
            //赋值方式为区间
            if (StringUtils.equals("range_value", it.getAssignMode())) {
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> scoreItem.getStartValue() != null && scoreItem.getEndValue() != null), StrUtil.format("请检查{}的区间参数", it.getItemTitle()));
            } else {
                //赋值方式为枚举
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> StringUtils.isNotBlank(scoreItem.getValue())), StrUtil.format("请检查{}的枚举参数", it.getItemTitle()));
            }
        });
        Assert.isTrue(itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getItemTitle).distinct().count() == itemList.size(), "匹配项名称不可重复!");
        Assert.isTrue(itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getItemName).distinct().count() == itemList.size(), "不可存在两条相同的匹配项!");
        BigDecimal totalWeight = itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalWeight.compareTo(BigDecimal.ONE) == 0, "所有匹配项权重之和必须等于1");
    }

    private void checkStrategyName(String strategyName, Long businessLineId, Integer strategyId) {
        OrderSelectStrategy orderSelectStrategy = orderSelectStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(orderSelectStrategy, "该业务线已存在相同策略名称!");
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "非普通订单匹配", level2MenuName = "师傅筛选策略",
            tableName = "order_select_strategy", mapperClass = OrderSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer update(UpdateOrderSelectStrategyRqt rqt){

        String strategyName = rqt.getStrategyName();
        Long businessLineId = rqt.getBusinessLineId();
        this.checkStrategyName(strategyName, businessLineId, rqt.getStrategyId());
        String selectStrategy = JSON.toJSONString(rqt.getSelectStrategy());
        //获取精排策略表达式集合
//        SelectStrategyExpressionDto selectExpression = this.getSelectExpression(rqt.getSelectStrategy());


        SelectStrategyExpressionDto selectRuleExpressionDto = this.getSelectExpression(businessLineId, rqt.getSelectStrategy());


        return orderSelectStrategyRepository.update(rqt.getStrategyId(),businessLineId,strategyName,rqt.getOrderFrom(), rqt.getStrategyDesc(),rqt.getMasterResources(), selectStrategy, JSON.toJSONString(selectRuleExpressionDto), rqt.getCreateAccountId());



    }

    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "非普通订单匹配", level2MenuName = "师傅筛选策略",
            tableName = "order_select_strategy", mapperClass = OrderSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableOrderSelectStrategyRqt rqt){
        Condition condition = new Condition(OrderSelectStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);
        OrderSelectStrategy orderSelectStrategy = new OrderSelectStrategy();
        orderSelectStrategy.setStrategyStatus(rqt.getStrategyStatus());
        orderSelectStrategy.setUpdateAccountId(rqt.getUpdateAccountId());

        if (rqt.getStrategyStatus() == 0) {
            OrderStrategyRelate strategyRelate = orderStrategyRelateRepository.selectByOrderBaseSelectStrategyId(rqt.getStrategyId());
            Assert.isNull(strategyRelate, "当前记录已被应用，不可禁用");
        }

        int result = orderSelectStrategyRepository.updateByConditionSelective(orderSelectStrategy, condition);




        if(rqt.getStrategyStatus() == 1){
            org.springframework.util.Assert.isTrue(result > 0,"启用策略失败");
        }else{
            org.springframework.util.Assert.isTrue(result > 0,"禁用策略失败");
        }
        return 1;
    }

    @Override
    public OrderSelectStrategy detail(OrderSelectStrategyDetailRqt rqt){
        return orderSelectStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
    }

    @Override
    public SimplePageInfo<OrderSelectStrategy> list(GetOrderSelectStrategyListRqt rqt){
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectList(rqt);
        SimplePageInfo<OrderSelectStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(orderSelectStrategyList);
        return listRespSimplePageInfo;
    }



    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "非普通订单匹配", level2MenuName = "师傅筛选策略",
            tableName = "order_select_strategy", mapperClass = OrderSelectStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderSelectStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteOrderSelectStrategyRqt rqt){

        Condition condition = new Condition(OrderSelectStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", 0).andEqualTo("isDelete", 0);

        OrderSelectStrategy orderSelectStrategy = new OrderSelectStrategy();
        orderSelectStrategy.setIsDelete(1);
        orderSelectStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = orderSelectStrategyRepository.updateByConditionSelective(orderSelectStrategy, condition);
        org.springframework.util.Assert.isTrue(result > 0,"删除失败");
        return 1;

    }

    @Override
    public SimplePageInfo<MasterQuotaResp> masterQuota(MasterQuotaListRqt rqt) {
        String quotaName = rqt.getQuotaName();
        Page<?> startPage = PageHelper.startPage(rqt.pageNum, rqt.pageSize);
        List<MasterQuota> masterQuotas = masterQuotaRepository.selectQuotaList(quotaName, 1);
        // valueType值类型：range_value: 度量，enum_value: 枚举
        List<Long> enumMasterQuotaIds = masterQuotas.stream().filter(it -> StringUtils.equals("enum_value", it.getValueType())).
                map(MasterQuota::getQuotaId).collect(Collectors.toList());

        List<MasterQuotaValue> masterQuotaValues = masterQuotaValueRepository.selectByMasterQuotaIds(enumMasterQuotaIds);
        Map<Long, List<MasterQuotaValue>> masterQuotaValuesMap = masterQuotaValues.stream().collect(Collectors.groupingBy(MasterQuotaValue::getMasterQuotaId));

        List<MasterQuotaResp> respList = masterQuotas.stream().map(it -> {
            MasterQuotaResp masterQuotaResp = new MasterQuotaResp(it.getQuotaCode(), it.getQuotaName(), it.getQuotaDesc(), it.getValueType());
            if (StringUtils.equals(it.getValueType(), "enum_value")) {
                List<MasterQuotaValue> quotaValueList = masterQuotaValuesMap.getOrDefault(it.getQuotaId(), Collections.emptyList());
                List<MasterQuotaResp.EnumValue> enumValues = BeanCopyUtil.copyListProperties(quotaValueList, MasterQuotaResp.EnumValue.class, null);
                masterQuotaResp.setEnumValueList(enumValues);
            }
            return masterQuotaResp;
        }).collect(Collectors.toList());
        SimplePageInfo<MasterQuotaResp> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(respList);
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPages(startPage.getPages());
        return listRespSimplePageInfo;
    }
}
