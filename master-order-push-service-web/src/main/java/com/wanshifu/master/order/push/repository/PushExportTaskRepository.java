package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.mapper.PushExportTaskMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/26 18:06
 */
@Repository
public class PushExportTaskRepository extends BaseRepository<PushExportTask> {

    @Resource
    private PushExportTaskMapper pushExportTaskMapper;

    public List<PushExportTask> selectList(Date createTimeStart, Date createTimeEnd){
        return pushExportTaskMapper.selectList(createTimeStart, createTimeEnd);
    }

}
