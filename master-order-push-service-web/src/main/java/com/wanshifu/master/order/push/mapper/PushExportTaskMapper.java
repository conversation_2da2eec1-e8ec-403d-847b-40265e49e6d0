package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 18:05
 */
public interface PushExportTaskMapper extends IBaseCommMapper<PushExportTask> {

    List<PushExportTask> selectList(@Param("createTimeStart") Date createTimeStart,
                                    @Param("createTimeEnd") Date createTimeEnd);

}
