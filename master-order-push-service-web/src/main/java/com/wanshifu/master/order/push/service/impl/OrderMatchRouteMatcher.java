package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.dto.RouteMatchResult;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.OrderMatchRouteRepository;
import com.wanshifu.master.order.push.repository.OrderMatchRouteTimeRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class OrderMatchRouteMatcher {

    @Resource
    private OrderMatchRouteTimeRepository orderMatchRouteTimeRepository;


    @Resource
    private OrderMatchRouteRepository orderMatchRouteRepository;


    public RouteMatchResult match(String orderPushFlag, Integer businessLineId,Long categoryId,Integer appointType){
        OrderMatchRoute orderMatchRoute = orderMatchRouteRepository.selectByOrderPushFlag(orderPushFlag,null);
        if(Objects.isNull(orderMatchRoute)){
            return null;
        }
        List<OrderMatchRouteTime> orderMatchRouteTimeList = orderMatchRouteTimeRepository.selectByCategoryIdAndAppointType(businessLineId,categoryId,appointType);
        if(CollectionUtils.isEmpty(orderMatchRouteTimeList)){
            return null;
        }
        RouteMatchResult result = new RouteMatchResult();
        result.setOrderPriorityMatchRule(orderMatchRoute.getOrderPriorityMatchRule());
        result.setOrderStandbyMatchRule(orderMatchRoute.getOrderStandbyMatchRule());
        result.setOrderMatchRouteTimeList(orderMatchRouteTimeList);
        return result;
    }

}
