package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Deprecated
public interface PermissionSetMapper extends IBaseCommMapper<PermissionSet> {

    List<PermissionSet> selectList(@Param("permissionSetName") String permissionSetName, @Param("createTimeStart") Date createTimeStart, @Param("createTimeEnd") Date createTimeEnd,
                                   @Param("roleIdList")List<Integer> roleIdList);

}
