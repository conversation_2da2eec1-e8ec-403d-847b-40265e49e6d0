package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.Role;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface RoleMapper extends IBaseCommMapper<Role> {


    List<Role> selectList(@Param("roleName") String roleName,@Param("createTimeStart") Date createTimeStart,@Param("createTimeEnd") Date createTimeEnd);

}
