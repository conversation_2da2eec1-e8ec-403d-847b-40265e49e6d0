package com.wanshifu.master.order.push.repository;


//import com.wanshifu.master.order.push.service.PushRecordTableStoreService;
import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.common.PushMasterList;
import com.wanshifu.master.order.push.service.impl.PushRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class PushRecordFacade {


//	@Resource
//	private PushRecordTableStoreService pushRecordTableStoreService;

	@Resource
	private PushRecordService pushRecordService;

	/**
	 * 统计师傅每天推单数量
	 * @param pushTime
	 * @param masterIdSet
	 */
	@Async
	public void orderPushCount(String pushTime,Set<String> masterIdSet) {
		pushRecordService.orderPushCount(pushTime,masterIdSet);
	}

	/**
	 * 更新订单包师傅每日推单数量
	 * @param miToDay
	 * @param packageConfigId
	 * @param masterOrderId
	 * @param masterId
	 */
	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {
		pushRecordService.packageOrderPushCount(miToDay,packageConfigId,masterOrderId, masterId);
//		pushRecordTableStoreService.packageOrderPushCount(miToDay,packageConfigId,masterOrderId, masterId);

	}


	public void orderPush(Long globalOrderTraceId,Set<String> masterIdSet,String pushMode) {
		pushRecordService.saveOrderPush(globalOrderTraceId,masterIdSet,pushMode);

	}


	public void increasePushMainNewModelMasterCount(Long globalOrderTraceId) {
		pushRecordService.increasePushMainNewModelMasterCount(globalOrderTraceId);
	}

	/**
	 * 更新订单包师傅每日推单数量
	 */
	public void saveOrderPush(String masterOrderId, String pushTime, String pushMaster,String pushMode) {
//		pushRecordTableStoreService.saveOrderPush(masterOrderId,pushTime,pushMaster, pushMode);
	}


	@Async
	public void increasePushOrderCount(List<PushMaster> pushMasterList, String day){
		pushRecordService.increasePushOrderCountList(pushMasterList,day);
	}


	@Async
	public void increasePushOrderCountByBusinessLine(List<PushMaster> pushMasterList,String day,String businessLine){
		pushRecordService.increasePushOrderCountByBusinessLine(pushMasterList,day,businessLine);
	}
}