//package com.wanshifu.master.order.push.service;
//
//import cn.hutool.core.lang.Assert;
//import cn.hutool.core.util.StrUtil;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
//import com.wanshifu.framework.core.page.SimplePageInfo;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.annotation.FeishuNotice;
//import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
//import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
//import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;
//import com.wanshifu.master.order.push.mapper.ExclusiveOrderSchedulerMapper;
//
//import com.wanshifu.master.order.push.repository.ExclusiveOrderSchedulerRepository;
//import com.wanshifu.order.config.api.GoodsServiceApi;
//import com.wanshifu.order.config.domains.po.Goods;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
///**
// * <AUTHOR>
// */
//@Service
//@Slf4j
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class ExclusiveOrderSchedulerServiceImpl implements ExclusiveOrderSchedulerService{
//
//    private final ExclusiveOrderSchedulerRepository exclusiveOrderSchedulerRepository;
//
//    private final GoodsServiceApi goodsServiceApi;
//
//    @Override
//    @FeishuNotice(methodTypeName = "insert", level1MenuName = "推单设置", level2MenuName = "推单时间设置",
//            createAccountIdFieldName = "createAccountId",
//            businessLineIdFieldName = "businessLineId", configNameFieldName = "categoryId")
//    public int create(CreateRqt rqt) {
//        final String categoryIds = rqt.getCategoryIds();
//        if (StringUtils.isNotEmpty(categoryIds)) {
//            final List<Integer> collect = Arrays.asList(categoryIds.split(","))
//                    .stream().map(Integer::valueOf).collect(Collectors.toList());
//            checkCategory(rqt.getBusinessLineId(),collect);
//
//            exclusiveOrderSchedulerRepository.insert(
//                    rqt.getBusinessLineId(),
//                    collect,
//                    rqt.getScheduleTime(),
//                    rqt.getCreateAccountId(),
//                    rqt.getCreateAccountId()
//            );
//        }
//        return 0;
//    }
//
//    private void checkCategory(Integer businessLineId,List<Integer> categoryList) {
//        final List<ExclusiveOrderScheduler> existRecord = exclusiveOrderSchedulerRepository
//                .selectList(businessLineId, categoryList);
//
//        if (existRecord!=null&&existRecord.size()>0) {
//            //类目名称
//            final List<Long> existCategory = existRecord.stream().map(row -> Long.valueOf(row.getCategoryId())).collect(Collectors.toList());
//            List<Goods> goods = goodsQueryBatch(existCategory);
//            Map<Long, String> goodsNameMap = goods.stream().collect(Collectors.toMap(Goods::getGoodsId, Goods::getGoodsName));
//            final String goodsName = existCategory.stream().map(row -> goodsNameMap.get(row)).distinct().collect(Collectors.joining(","));
//
//            Assert.isNull(true, StrUtil.format("类目与配置【{}】存在重复!", goodsName));
//        }
//    }
//
//
//    /**
//     * 批量获取商品服务
//     *
//     * @param goodsIds
//     * @return
//     */
//    public List<Goods> goodsQueryBatch(List<Long> goodsIds) {
//        if (CollectionUtils.isEmpty(goodsIds)) {
//            return Collections.emptyList();
//        }
//        try {
//            return goodsServiceApi.queryBatch(goodsIds);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("queryBatch-批量获取商品服务失败!");
//        }
//        return Collections.emptyList();
//    }
//
//    @Override
//    @FeishuNotice(methodTypeName = "update", level1MenuName = "推单设置", level2MenuName = "推单时间设置",
//            updateAccountIdFieldName = "updateAccountId",
//            tableName = "exclusive_order_scheduler_base", mapperClass = ExclusiveOrderSchedulerMapper.class,
//            mapperBeanName = "exclusiveOrderSchedulerMapper", primaryKeyFieldName = "configId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "categoryId")
//    public int update(UpdateRqt rqt) {
//        exclusiveOrderSchedulerRepository.update(
//                rqt.getConfigId(),
//                rqt.getBusinessLineId(),
//                rqt.getCategoryId(),
//                rqt.getScheduleTime(),
//                rqt.getUpdateAccountId()
//        );
//        return 0;
//    }
//
//    @Override
//    public ExclusiveOrderScheduler detail(DetailRqt rqt) {
//        return null;
//    }
//
//    @Override
//    @FeishuNotice(methodTypeName = "enable", level1MenuName = "推单设置", level2MenuName = "推单时间设置",
//            tableName = "exclusive_order_scheduler_base", mapperClass = ExclusiveOrderSchedulerMapper.class,
//            updateAccountIdFieldName = "updateAccountId",
//            mapperBeanName = "exclusiveOrderSchedulerMapper", primaryKeyFieldName = "configId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "categoryId")
//    public int enable(EnableRqt rqt) {
//        return 0;
//    }
//
//    @Override
//    public SimplePageInfo<ExclusiveOrderScheduler> list(ListRqt rqt) {
//        Integer pageNum = rqt.getPageNum();
//        Integer pageSize = rqt.getPageSize();
//        final Integer businessLineId = rqt.getBusinessLineId();
//        final String categoryIds = rqt.getCategoryIds();
//
//        List<Integer> categoryIdsList=null;
//        if (categoryIds!=null&& StringUtils.isNotEmpty(categoryIds)) {
//            categoryIdsList = Arrays.asList(categoryIds.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
//        }
//
//        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
//        List<ExclusiveOrderScheduler> ExclusiveOrderSchedulerList =
//                exclusiveOrderSchedulerRepository.selectList(
//                        businessLineId,
//                        categoryIdsList
//                        );
//
//        SimplePageInfo<ExclusiveOrderScheduler> listRespSimplePageInfo = new SimplePageInfo<>();
//        listRespSimplePageInfo.setPages(startPage.getPages());
//        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
//        listRespSimplePageInfo.setTotal(startPage.getTotal());
//        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
//        listRespSimplePageInfo.setList(ExclusiveOrderSchedulerList);
//        return listRespSimplePageInfo;
//    }
//
//    @Override
//    @FeishuNotice(methodTypeName = "delete", level1MenuName = "推单设置", level2MenuName = "推单时间设置",
//            tableName = "exclusive_order_scheduler_base", mapperClass = ExclusiveOrderSchedulerMapper.class,
//            updateAccountIdFieldName = "updateAccountId",
//            mapperBeanName = "exclusiveOrderSchedulerMapper", primaryKeyFieldName = "configId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "categoryId")
//    public int delete(DeleteRqt rqt) {
//        return 0;
//    }
//
//    @Override
//    public List<ExclusiveOrderScheduler> selectBySnapshotIdList(List<Long> snapshotIdList) {
//        return null;
//    }
//}
