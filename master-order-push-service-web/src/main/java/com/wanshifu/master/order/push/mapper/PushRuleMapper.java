package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.AgreementMasterPush;
import com.wanshifu.master.order.push.domain.po.PushRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface PushRuleMapper extends IBaseCommMapper<PushRule> {


    List<PushRule> selectList(@Param("businessLineId") Integer businessLineId,@Param("ruleName") String ruleName,@Param("createStartTime") Date createStartTime,@Param("createEndTime") Date createEndTime);

}