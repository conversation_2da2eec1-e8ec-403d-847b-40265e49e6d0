package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.CreateOrderSelectStrategyRqt;
import com.wanshifu.master.order.push.repository.CooperationBusinessMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.OrderSelectStrategyRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.service.OrderMasterCalculateService;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 合作经营师傅推单匹配
 * @date 2025/3/10 17:27
 */
@Slf4j
@Component("cooperation_business_master")
public class CooperationBusinessMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private HBaseClient hBaseClient;

    @Resource
    private OrderMasterCalculateService orderMasterCalculateService;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private CooperationBusinessMasterMatchLogRepository cooperationBusinessMasterMatchLogRepository;

    @Resource
    private OrderSelectStrategyRepository orderSelectStrategyRepository;

    @Value("${orderMaster.navigationalDistance.city.list}")
    private String orderMasterNavigationalDistanceCityList;

    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {

        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();

        if(CollectionUtils.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.COOPERATION_BUSINESS_MASTER.code)){
            return false;
        }
        if(StringUtils.isNotBlank(orderDetailData.getPushExtraData().getMatchSceneCode()) && (!MatchSceneCode.ORDER_CREATE.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode()))){
            return false;
        }


        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            insertMatchLog(orderDetailData,"不推单的城市", orderDetailData.getOrderVersion());
            return false;
        }

        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(StringUtils.isNotBlank(pushMode) && (!PushMode.COOPERATION_BUSINESS_MASTER.code.equals(pushMode))){
            return Boolean.FALSE;
        }

        return orderDetailData.getBusinessLineId() == 1 && AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType());
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {

        Long orderId = orderDetailData.getMasterOrderId();
        log.info("cooperationBusinessMasterList match orderId:{}", orderId.toString());

        List<CooperationBusinessMasterDto> cooperationBusinessMasterList = matchCooperationBusinessMaster(orderDetailData, masterCondition);
        if (CollectionUtil.isEmpty(cooperationBusinessMasterList)) {
            return null;
        }

        MatchMasterResult masterResult = new MatchMasterResult();
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData("cooperation_business_master_list",cooperationBusinessMasterList);

        Set<String> masterSet = cooperationBusinessMasterList.stream().map(CooperationBusinessMasterDto::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        masterResult.setMasterIdSet(masterSet);
        return masterResult;
    }

    private List<CooperationBusinessMasterDto> matchCooperationBusinessMaster(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {

        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData);

        DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();

        //获取策略
        OrderDistributor orderDistributor = distributeFactory
                .matchCooperationBusinessDistributorOnly(orderDetailData, orderFeatureContext, PushMode.COOPERATION_BUSINESS_MASTER.getCode());

        if (orderDistributor.isMatched()) {
            //是否服务区域兜底
            Integer cooperationBusinessServeDivisionAtLast = orderDistributor.getCooperationBusinessServeDivisionAtLast();
            List<CooperationBusinessMasterDto> cooperationBusinessMasterList;
            if (Objects.nonNull(cooperationBusinessServeDivisionAtLast) && cooperationBusinessServeDivisionAtLast == 0) {
                //只按接单区域匹配
                cooperationBusinessMasterList = matchCooperationBusinessMasterByReceiveOrderLv4Division(orderDetailData, masterCondition);

            } else {
                //优先按合作经营师傅可接单区域匹配
                cooperationBusinessMasterList = matchCooperationBusinessMasterByReceiveOrderLv4DivisionPriority(orderDetailData, masterCondition);

                if (CollectionUtil.isNotEmpty(cooperationBusinessMasterList)) {
                    return cooperationBusinessMasterList;
                }
                cooperationBusinessMasterList = matchCooperationBusinessMasterByServeLv4Division(orderDetailData, masterCondition);

            }
            return cooperationBusinessMasterList;

        } else {
            String matchFailReason = "未匹配上合作经营调度策略！";
            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }


    }


    private List<CooperationBusinessMasterDto> filterCooperationBusinessMaster(List<MasterBaseSearch> cooperationBusinessMasterList,
                                                                               OrderDetailData orderDetailData) {
        Long orderId = orderDetailData.getMasterOrderId();
        List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList = new ArrayList<>();
        Date now = new Date();
        cooperationBusinessMasterList.forEach(cooperationBusinessMaster -> {
            CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = new CooperationBusinessMasterMatchLog();
            cooperationBusinessMasterMatchLog.setMasterId(Long.valueOf(cooperationBusinessMaster.getMasterId()));
            cooperationBusinessMasterMatchLog.setOrderVersion(orderDetailData.getOrderVersion());
            cooperationBusinessMasterMatchLog.setOrderId(orderId);
            cooperationBusinessMasterMatchLog.setOrderNo(orderDetailData.getOrderNo());
            cooperationBusinessMasterMatchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            cooperationBusinessMasterMatchLog.setIsMatchSuccess(1);
            cooperationBusinessMasterMatchLog.setMatchFailReason("");
            cooperationBusinessMasterMatchLog.setCreateTime(now);
            cooperationBusinessMasterMatchLog.setUpdateTime(now);
            cooperationBusinessMasterMatchLogList.add(cooperationBusinessMasterMatchLog);
        });


        Set<String> masterIdSet = cooperationBusinessMasterList.stream().map(MasterBaseSearch::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        log.info("cooperationBusinessMasterList after matchEs result:{}}", JSON.toJSONString(masterIdSet));
        //移除已推单师傅
        filterPushedMaster(masterIdSet, orderDetailData.getGlobalOrderId(), cooperationBusinessMasterMatchLogList);
        log.info("cooperationBusinessMasterList after filterPushedMaster result:{}}", JSON.toJSONString(masterIdSet));

        if (CollectionUtils.isEmpty(masterIdSet)) {
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            return null;
        }
        final String orderPushExcludeMasterIds =
                StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
        outerMasterFilter(masterIdSet, orderPushExcludeMasterIds, cooperationBusinessMasterMatchLogList);
        log.info("cooperationBusinessMasterList after orderPushExcludeMasterIds:{}:{}:{}", orderId, masterIdSet, orderPushExcludeMasterIds);

        if(CollectionUtils.isEmpty(masterIdSet)){
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            return null;
        }

        cooperationBusinessMasterList = cooperationBusinessMasterList.stream().filter(cooperationMasterBase -> masterIdSet.contains(String.valueOf(cooperationMasterBase.getMasterId()))).collect(Collectors.toList());

        //合作经营每日接单(用户指派指标、用户抢单指标)上限限制过滤
        cooperationBusinessMasterList = filterByCooperationBusinessDailyOrderCnt(cooperationBusinessMasterList, cooperationBusinessMasterMatchLogList);
        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            return null;
        }

        //合作经营接单范围限制过滤
        BigDecimal orderLng = BigDecimal.ZERO;
        BigDecimal orderLat = BigDecimal.ZERO;
        String orderLngLat = orderDetailData.getOrderLngLat();
        if (StringUtils.isNotBlank(orderLngLat)) {
            String[] orderLngLatArray = orderLngLat.split(",");
            orderLng = new BigDecimal(orderLngLatArray[0]);
            orderLat = new BigDecimal(orderLngLatArray[1]);
        }

        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData);

        DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();

        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                .matchDistributor(orderDetailData, orderFeatureContext, PushMode.COOPERATION_BUSINESS_MASTER.getCode());

        boolean isOpenOrderMasterNaviDistance = checkOrderMasterNaviDistanceCityOpen(orderDetailData);

        Integer distance = isOpenOrderMasterNaviDistance ? this.getMinMasterOrderDistance(orderDistributor) : null;

        cooperationBusinessMasterList = filterByCooperationBusinessReceiveOrderDistanceRange(cooperationBusinessMasterList,
                orderLng, orderLat, orderId, orderDetailData.getSecondDivisionId(),
                cooperationBusinessMasterMatchLogList,distance);
        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            return null;
        }

        Set<String> distributeMasterIdSet =  cooperationBusinessMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());

        pushFeature = featureRepository.buildPushFeature(orderDetailData, distributeMasterIdSet);

        orderFeatureContext = pushFeature.getOrderFeature();

        if(isOpenOrderMasterNaviDistance){
            orderDistributor.getMasterFeatureSet().add("lng_lat");
            orderDistributor.getMasterFeatureSet().add("order_master_navigational_distance");
        }

        orderDistributor.getMasterFeatureSet().add("week_grab_cnt");
        orderDistributor.getMasterFeatureSet().add("week_appoint_cnt");

        //获取特征
        featureRepository.getMasterFeatures(pushFeature, distributeMasterIdSet, orderDistributor.getMasterFeatureSet());


        if(isOpenOrderMasterNaviDistance){
            cooperationBusinessMasterList = filterByOrderMasterNavigationalDistance(cooperationBusinessMasterList,
                    pushFeature.getMasterFeature(),cooperationBusinessMasterMatchLogList);
            if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
                cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
                return null;
            }
        }


        cooperationBusinessMasterList = filterByWeekReceiveOrGrabOrderLimit(cooperationBusinessMasterList,
                pushFeature.getMasterFeature(),cooperationBusinessMasterMatchLogList);
        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)) {
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            return null;
        }


        List<CooperationBusinessMasterDto> cooperationBusinessMasterDtoList = cooperationBusinessMasterList.stream()
                .map(cooperationBusinessMaster -> {
                    CooperationBusinessMasterDto cooperationBusinessMasterDto = new CooperationBusinessMasterDto();
                    BeanUtil.copyProperties(cooperationBusinessMaster, cooperationBusinessMasterDto);
                    return cooperationBusinessMasterDto;
                })
                .collect(Collectors.toList());

        distributeMasterIdSet = cooperationBusinessMasterDtoList.stream().map(CooperationBusinessMasterDto::getMasterId).collect(Collectors.toSet());


        Set<String> finalDistributeMasterIdSet = distributeMasterIdSet;


        if (orderDistributor.isMatched()) {

            try {


                DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();



                masterFeatures.keySet().forEach(masterId -> {
                    DefaultContext<String,Object> masterFeatureContext = masterFeatures.get(masterId);
                    if(Objects.nonNull(masterFeatureContext)){
                        masterFeatureContext.keySet().forEach(featureCode -> {
                            Object masterFeatureValue = masterFeatureContext.get(featureCode);
                            if (Objects.nonNull(masterFeatureValue) && (masterFeatureValue instanceof List)) {
                                if(masterFeatureContext.get(featureCode + ":calculateValue") != null){
                                    masterFeatureContext.put(featureCode, masterFeatureContext.get(featureCode + ":calculateValue"));
                                    log.info("featureCode calculateValue replace: " + featureCode);
                                }
                            }
                        });
                    }

                });



                log.info("cooperationBusinessMasterList masterFeatures:" + JSON.toJSONString(masterFeatures));


                //过滤排序
                final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                        .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                        .withType(FieldConstant.RANK_DETAIL)
                        .build();
                log.info(String.format("cooperationBusinessMasterList rankDetail:%s", JSON.toJSONString(rankDetail)));


                final Set<String> finalMasterSet = orderDistributor
                        .filter(finalDistributeMasterIdSet, masterFeatures, rankDetail);


                Map<Long,String> filterReasonMap = new HashMap<>();

                try{
                    if(StringUtils.isNotBlank(rankDetail.getDetailInfo())){
                        Map<String, Object> filterDetailsMap = (Map) JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                        for(String key : filterDetailsMap.keySet()){
                            JSONArray jsonArray = (JSONArray)filterDetailsMap.get(key);
                            jsonArray.forEach(master -> {
                                filterReasonMap.put(Long.valueOf(String.valueOf(master)), key);
                            });
                        }
                    }

                }catch(Exception e){
                    log.error("rankDetail error",e);
                }


                if(CollectionUtils.isEmpty(finalMasterSet)){
                    cooperationBusinessMasterMatchLogList.forEach(log -> {
                        if(finalDistributeMasterIdSet.contains(String.valueOf(log.getMasterId()))){
                            log.setIsFilter(1);
                            log.setFilterReason(filterReasonMap.getOrDefault(log.getMasterId(),""));
                        }
                    });
                    cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
                    return null;
                }


                cooperationBusinessMasterMatchLogList.forEach(log -> {
                    if (log.getIsMatchSuccess() == 1) {
                        if (finalMasterSet.contains(String.valueOf(log.getMasterId()))) {
                            log.setIsFilter(0);
                        } else {
                            log.setIsFilter(1);
                            log.setFilterReason(filterReasonMap.getOrDefault(log.getMasterId(),""));
                        }
                    }
                });



                featureRepository.getWeekGrabOrAppointCnt(finalMasterSet,masterFeatures);


                if(orderDistributor.getMasterFeatureSet().contains("max_last_grab_time") ){
                    featureRepository.getMaxLastGrabTime(
                            finalMasterSet,
                            pushFeature.getMasterFeature()
                    );
                }


                if(orderDistributor.getMasterFeatureSet().contains("max_last_appoint_time") ){
                    featureRepository.getMaxLastAppointTime(
                            finalMasterSet,
                            pushFeature.getMasterFeature()
                    );
                }




                final List<ScorerMaster> scorerMasterList = orderDistributor
                        .score(finalMasterSet, pushFeature.getOrderFeature(),masterFeatures, rankDetail);



                List<String> masterList = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList());


                //二次筛选、评分
                cooperationBusinessMasterDtoList = cooperationBusinessMasterDtoList.stream().filter(cooperationBusinessMasterDto -> masterList.contains(cooperationBusinessMasterDto.getMasterId())).collect(Collectors.toList());

                //根据分配规则筛选
                cooperationBusinessMasterDtoList = distributeCooperationBusinessMaster(scorerMasterList, orderDistributor, cooperationBusinessMasterDtoList);

                List<String> afterDistributeMasterList = CollectionUtil.isEmpty(cooperationBusinessMasterDtoList) ? new ArrayList<>() : cooperationBusinessMasterDtoList.stream().map(CooperationBusinessMasterDto::getMasterId).collect(Collectors.toList());

                cooperationBusinessMasterMatchLogList.forEach(log -> {
                    if (Objects.nonNull(log.getIsFilter()) && log.getIsFilter() == 0) {
                        if (afterDistributeMasterList.contains(String.valueOf(log.getMasterId()))) {
                            log.setIsDistribute(1);
                            log.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                        } else {
                            log.setIsDistribute(0);
                        }
                    }
                });

                cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);

                log.info("cooperationBusinessMasterList distributeCooperationBusinessMaster:" + JSON.toJSONString(cooperationBusinessMasterDtoList));

            } catch (Exception e) {

                log.error("cooperationBusinessMaster distribute error", e);
            }


        } else {
            cooperationBusinessMasterMatchLogList.forEach(log -> {
                if(finalDistributeMasterIdSet.contains(String.valueOf(log.getMasterId()))){
                    log.setIsFilter(1);
                    log.setFilterReason("未匹配到调度策略！不走合作经营推单！");
                }
            });
            cooperationBusinessMasterMatchLogRepository.insertList(cooperationBusinessMasterMatchLogList);
            log.warn("cooperationBusinessMasterList dont match strategy! orderId:{},businessLineId:{},appointType:{},secondDivisionId：{}，orderCategoryId:{},lv1ServeIds:{}",
                    orderId, orderDetailData.getBusinessLineId(), orderDetailData.getAppointType(), orderDetailData.getSecondDivisionId(), orderDetailData.getOrderCategoryId(), orderDetailData.getLv1ServeIds());
            return null;
        }

        return cooperationBusinessMasterDtoList;
    }


    private Integer getMinMasterOrderDistance(OrderDistributor orderDistributor){

        List<Integer> orderSelectStrategyIdList  = orderDistributor.getFilterList().stream().map(Filter::getFilterId).map(Integer::valueOf).distinct().collect(Collectors.toList());
        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(orderSelectStrategyIdList);

        Map<Integer, OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream()
                .collect(Collectors.toMap(OrderSelectStrategy::getStrategyId, strategy -> strategy));

        Integer distance = null;

        for(Filter filter : orderDistributor.getFilterList()){
            OrderSelectStrategy orderSelectStrategy = orderSelectStrategyMap.get(Integer.valueOf(filter.getFilterId()));
            CreateOrderSelectStrategyRqt.SelectStrategy selectStrategy = JSON.parseObject(orderSelectStrategy.getSelectRule(),CreateOrderSelectStrategyRqt.SelectStrategy.class);

            for(RuleItem ruleItem : selectStrategy.getRuleList()){

                if(!ruleItem.getRuleName().equals(filter.getRuleName())){
                    continue;
                }

                RuleItem.FilterRuleItem orderMasterDistanceRuleItem = ruleItem.getFilterRule().getItemList().stream().filter(filterRuleItem -> "order_master_distance".equals(filterRuleItem.getItemName())).findFirst().orElse(null);
                if(Objects.nonNull(orderMasterDistanceRuleItem)){
                    if(Objects.isNull(distance)){
                        distance = Integer.valueOf(orderMasterDistanceRuleItem.getItemValue());
                    }else if(Integer.valueOf(orderMasterDistanceRuleItem.getItemValue()) < distance){
                        distance = Integer.valueOf(orderMasterDistanceRuleItem.getItemValue());
                    }

                }


                RuleItem.FilterRuleItem orderMasterNavigationalDistanceRuleItem = ruleItem.getFilterRule().getItemList().stream().filter(filterRuleItem -> "order_master_navigational_distance".equals(filterRuleItem.getItemName())).findFirst().orElse(null);
                if(Objects.nonNull(orderMasterNavigationalDistanceRuleItem)){
                    if(Objects.isNull(distance)){
                        distance = Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue());
                    }else if(Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue()) < distance){
                        distance = Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue());
                    }
                }
            }

        }


        return distance;


    }

    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {
        return;
    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }
        String orderVersion = orderDetailData.getOrderVersion();
        long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), PushMode.COOPERATION_BUSINESS_MASTER.getCode());

        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.COOPERATION_BUSINESS_MASTER.getCode());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
        commonFeature.put("cooperation_business_master_list", matchMasterResult.getExtraData().get("cooperation_business_master_list"));
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        pushControllerFacade.cooperationBusinessMasterPush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);

        return true;
    }


    /**
     * 合作经验师傅分配
     *
     * @param scorerMasterList
     * @param orderDistributor
     * @param cooperationBusinessMasterDtoList
     * @return
     */
    public List<CooperationBusinessMasterDto> distributeCooperationBusinessMaster(List<ScorerMaster> scorerMasterList, OrderDistributor orderDistributor,
                                                                      List<CooperationBusinessMasterDto> cooperationBusinessMasterDtoList) {

        String distributeRule = orderDistributor.getDistributeRule();
        List<ScorerMaster> scorerMasters = null;

        if (OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)) {
            Collections.sort(scorerMasterList);
            //产品确认限制为100
            scorerMasters = scorerMasterList.size() <= 100 ? scorerMasterList : scorerMasterList.subList(0, 100);
        } else if (OrderDistributeRule.RANDOM.getCode().equals(distributeRule)) {
            Collections.shuffle(scorerMasterList);
            scorerMasters = scorerMasterList.size() <= 100 ? scorerMasterList : scorerMasterList.subList(0, 100);
        } else if (OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)) {
            Collections.sort(scorerMasterList);
            scorerMasters = scorerMasterList.size() <= 50 ? scorerMasterList : scorerMasterList.subList(0, 50);
            Collections.shuffle(scorerMasters);
            scorerMasters = scorerMasterList.size() <= 50 ? scorerMasterList : scorerMasterList.subList(0, 50);
        } else {
            log.warn("未定义的分配规则，默认走产品限制兜底！");
            scorerMasters = scorerMasterList.size() <= 100 ? scorerMasterList : scorerMasterList.subList(0, 100);
        }


        if (CollectionUtils.isNotEmpty(scorerMasters)) {
            List<CooperationBusinessMasterDto> finalCooperationBusinessMasterList = new ArrayList<>();
            Map<String, CooperationBusinessMasterDto> cooperationBusinessMasterMap = cooperationBusinessMasterDtoList.stream().collect(Collectors.toMap(CooperationBusinessMasterDto::getMasterId, Function.identity()));
            for (int i = 0; i < scorerMasters.size(); i++) {
                CooperationBusinessMasterDto cooperationBusinessMasterDto = cooperationBusinessMasterMap.get(scorerMasters.get(i).getMasterId());

                cooperationBusinessMasterDto.setGrabSort(i + 1);
                cooperationBusinessMasterDto.setScore(scorerMasters.get(i).getScore());
                finalCooperationBusinessMasterList.add(cooperationBusinessMasterDto);
            }
            return finalCooperationBusinessMasterList;
        }
        return null;
    }


    public void insertMatchLog(OrderDetailData orderDetailData, String matchFailReason, String orderVersion) {
        CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = new CooperationBusinessMasterMatchLog();
        cooperationBusinessMasterMatchLog.setOrderId(orderDetailData.getMasterOrderId());
        cooperationBusinessMasterMatchLog.setMasterId(0L);
        cooperationBusinessMasterMatchLog.setOrderNo(orderDetailData.getOrderNo());
        cooperationBusinessMasterMatchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
        cooperationBusinessMasterMatchLog.setMatchFailReason(matchFailReason);
        cooperationBusinessMasterMatchLog.setOrderVersion(orderVersion);
        cooperationBusinessMasterMatchLog.setCreateTime(new Date());
        cooperationBusinessMasterMatchLog.setUpdateTime(new Date());
        this.cooperationBusinessMasterMatchLogRepository.insertSelective(cooperationBusinessMasterMatchLog);
    }


    /**
     * 合作经营每日接单(用户指派指标、用户抢单指标)上限限制过滤
     *
     * @param cooperationBusinessMasterList
     * @param cooperationBusinessMasterMatchLogList
     * @return
     */
    private List<MasterBaseSearch> filterByCooperationBusinessDailyOrderCnt(List<MasterBaseSearch> cooperationBusinessMasterList,
                                                                            List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {

        if (CollectionUtils.isEmpty(cooperationBusinessMasterList)) {
            return cooperationBusinessMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = cooperationBusinessMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("assign_cooperation_cnt");
        fieldColumnList.add("grab_cooperation_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList, fieldColumnList, "master_daily_data_cnt");
        Map<String, Integer> assignCooperationCntMap = new HashMap<>();
        Map<String, Integer> grabCooperationCntMap = new HashMap<>();

        if (Objects.nonNull(resultArray) && resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String) jsonObject.get("master_id");
                if (jsonObject.containsKey("assign_cooperation_cnt")) {
                    String assignCooperationCnt = (String) jsonObject.get("assign_cooperation_cnt");
                    if (org.apache.commons.lang.StringUtils.isNotBlank(assignCooperationCnt)) {
                        assignCooperationCntMap.put(masterId, Integer.valueOf(assignCooperationCnt));
                    }
                }
                if (jsonObject.containsKey("grab_cooperation_cnt")) {
                    String grabCooperationCnt = (String) jsonObject.get("grab_cooperation_cnt");
                    if (org.apache.commons.lang.StringUtils.isNotBlank(grabCooperationCnt)) {
                        grabCooperationCntMap.put(masterId, Integer.valueOf(grabCooperationCnt));
                    }
                }
            }
        }
        //用户指派指标接单上限过滤
        Map<Long, String> receiveFilterMap = Maps.newHashMap();

        cooperationBusinessMasterList = cooperationBusinessMasterList.stream().filter(cooperationMaster -> {
            Integer hasReceiveCnt = assignCooperationCntMap.getOrDefault(cooperationMaster.getMasterId(), 0);
            Integer receiveOrderLimit = cooperationMaster.getReceiveOrderLimit();
            if (receiveOrderLimit == 0) {
                return true;
            }

            if (hasReceiveCnt < receiveOrderLimit) {
                return true;
            }
            receiveFilterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅指派接单限制为".concat(receiveOrderLimit.toString()).concat("单，已指派了").concat(hasReceiveCnt.toString()).concat("单。"));
            return false;
        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(receiveFilterMap)) {
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (receiveFilterMap.containsKey(cooperationBusinessMasterMatchLog.getMasterId())) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason(receiveFilterMap.get(cooperationBusinessMasterMatchLog.getMasterId()));
                }
            });
        }

        if (CollectionUtils.isEmpty(cooperationBusinessMasterList)) {
            return cooperationBusinessMasterList;
        }

        //用户抢单指标上线过滤
        Map<Long, String> grabFilterMap = Maps.newHashMap();
        cooperationBusinessMasterList = cooperationBusinessMasterList.stream().filter(cooperationMaster -> {
            Integer hasGrabCnt = grabCooperationCntMap.getOrDefault(cooperationMaster.getMasterId(), 0);
            Integer grabOrderLimit = cooperationMaster.getGrabOrderLimit();
            if (grabOrderLimit == 0) {
                return true;
            }
            if (hasGrabCnt < grabOrderLimit) {
                return true;
            }
            grabFilterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅抢单限制为".concat(grabOrderLimit.toString()).concat("单，已抢单了").concat(hasGrabCnt.toString()).concat("单。"));
            return false;
        }).collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(grabFilterMap)) {
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (grabFilterMap.containsKey(cooperationBusinessMasterMatchLog.getMasterId())) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason(grabFilterMap.get(cooperationBusinessMasterMatchLog.getMasterId()));
                }
            });
        }

        return cooperationBusinessMasterList;
    }


    /**
     * 合作经营接单范围限制过滤
     *
     * @param cooperationBusinessMasterList
     * @param orderLongitude
     * @param orderLatitude
     * @return
     */
    private List<MasterBaseSearch> filterByCooperationBusinessReceiveOrderDistanceRange(List<MasterBaseSearch> cooperationBusinessMasterList,
                                                                                        BigDecimal orderLongitude, BigDecimal orderLatitude,
                                                                                        Long orderId, Long cityDivisionId,
                                                                                        List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList,
                                                                                        Integer distance) {

        if (CollectionUtils.isEmpty(cooperationBusinessMasterList)) {
            return cooperationBusinessMasterList;
        }

        if (BigDecimalUtil.isEmpty(orderLongitude) || BigDecimalUtil.isEmpty(orderLatitude)) {
            //订单无经纬度，则无法计算到距离，则不做距离过滤
            return cooperationBusinessMasterList;
        }

        //师傅所在地与订单地址距离，单位米
        Map<String, Long> masterDistanceMap = Maps.newHashMap();

        cooperationBusinessMasterList.forEach(cooperationMaster -> {
            String masterLngLat = cooperationMaster.getLatLng();
            String masterId = cooperationMaster.getMasterId();
            BigDecimal masterLng = BigDecimal.ZERO;
            BigDecimal masterLat = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(masterLngLat)) {
                String[] masterLngLatArray = masterLngLat.split(",");
                masterLat = new BigDecimal(masterLngLatArray[0]);
                masterLng = new BigDecimal(masterLngLatArray[1]);
            }
            if (BigDecimalUtil.isEmpty(masterLng) || BigDecimalUtil.isEmpty(masterLat)) {
                //师傅无经纬度，则无法计算到距离，则不做距离过滤
                masterDistanceMap.put(masterId, 0L);
                return;
            }
            CalculatePushDistanceParamVo calculatePushDistanceParamVo = new CalculatePushDistanceParamVo();
            calculatePushDistanceParamVo.setMasterId(Long.valueOf(masterId));
            calculatePushDistanceParamVo.setOrderId(orderId);
            calculatePushDistanceParamVo.setOrderLatitude(orderLatitude);
            calculatePushDistanceParamVo.setOrderLongitude(orderLongitude);
            calculatePushDistanceParamVo.setMasterLongitude(masterLng);
            calculatePushDistanceParamVo.setMasterLatitude(masterLat);
            calculatePushDistanceParamVo.setCityDivisionId(cityDivisionId);

            CalculatePushDistanceRespVo calculatePushDistanceResp = orderMasterCalculateService.calculatePushDistance(calculatePushDistanceParamVo);
            if (Objects.nonNull(calculatePushDistanceResp) && calculatePushDistanceResp.getPushDistanceType() == 2) {
                //计算出直线距离
                masterDistanceMap.put(masterId, calculatePushDistanceResp.getPushDistance());
            } else {
                //无直线距离计算结果，则不做距离过滤
                masterDistanceMap.put(masterId, 0L);
            }
        });

        Map<Long, String> rangeFilterMap = Maps.newHashMap();
        cooperationBusinessMasterList = cooperationBusinessMasterList.stream().filter(cooperationMaster -> {
            Long masterDistance = masterDistanceMap.getOrDefault(cooperationMaster.getMasterId(), 0L);
            Integer receiveOrderDistanceRange = cooperationMaster.getReceiveOrderDistanceRange();

            if(Objects.nonNull(distance) && (receiveOrderDistanceRange == 0 || distance < receiveOrderDistanceRange)){
                receiveOrderDistanceRange = distance;
            }

            if (receiveOrderDistanceRange == 0) {
                return true;
            }
            if (masterDistance < (receiveOrderDistanceRange * 1000)) {
                return true;
            }
            rangeFilterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅接单范围限制为直线".concat(receiveOrderDistanceRange.toString()).concat("公里，该订单距离师傅").concat(masterDistance.toString()).concat("米，已超师傅接单范围"));
            return false;

        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(rangeFilterMap)) {
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (rangeFilterMap.containsKey(cooperationBusinessMasterMatchLog.getMasterId())) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason(rangeFilterMap.get(cooperationBusinessMasterMatchLog.getMasterId()));
                }
            });
        }

        return cooperationBusinessMasterList;
    }



    private List<MasterBaseSearch> filterByOrderMasterNavigationalDistance(List<MasterBaseSearch> cooperationBusinessMasterList,
                                                                 DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                                                           List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {

        Map<Long, String> rangeFilterMap = Maps.newHashMap();


        List<MasterBaseSearch> masterBaseSearchList = cooperationBusinessMasterList.stream().filter(cooperationMaster -> {

            if(cooperationMaster.getReceiveOrderDistanceRange() == 0){
                return true;
            }

            BigDecimal navigationalDistance = (BigDecimal)masterFeatures.get(cooperationMaster.getMasterId()).get("order_master_navigational_distance");
            if(navigationalDistance.compareTo(new BigDecimal(cooperationMaster.getReceiveOrderDistanceRange())) <= 0){
                return true;
            }

            rangeFilterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅接单范围限制为导航".concat(String.valueOf(cooperationMaster.getReceiveOrderDistanceRange())).concat("公里，该订单距离师傅导航").concat(navigationalDistance.toString()).concat("公里，已超师傅接单范围"));
            return false;

        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(rangeFilterMap)) {
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (rangeFilterMap.containsKey(cooperationBusinessMasterMatchLog.getMasterId())) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason(rangeFilterMap.get(cooperationBusinessMasterMatchLog.getMasterId()));
                }
            });
        }

        return masterBaseSearchList;
    }


    private List<MasterBaseSearch> filterByWeekReceiveOrGrabOrderLimit(List<MasterBaseSearch> cooperationBusinessMasterList,
                                                                           DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                                                           List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {

        Map<Long, String> filterMap = Maps.newHashMap();


        List<MasterBaseSearch> masterBaseSearchList = cooperationBusinessMasterList.stream().filter(cooperationMaster -> {

            if(Objects.isNull(cooperationMaster.getCooperationBusinessWeekReceiveOrderLimit()) &&
                    Objects.isNull(cooperationMaster.getCooperationBusinessWeekGrabOrderLimit())){
                return true;
            }

            if(Objects.nonNull(cooperationMaster.getCooperationBusinessWeekReceiveOrderLimit()) && cooperationMaster.getCooperationBusinessWeekReceiveOrderLimit() > 0){
                Integer weekGrabCnt = (Integer)masterFeatures.get(cooperationMaster.getMasterId()).get("week_grab_cnt");
                if(Objects.nonNull(weekGrabCnt) && weekGrabCnt >= cooperationMaster.getCooperationBusinessWeekReceiveOrderLimit()){
                    filterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅周抢单量上限".concat(String.valueOf(cooperationMaster.getCooperationBusinessWeekGrabOrderLimit())).concat("单，当前已抢").concat(weekGrabCnt.toString()).concat("单，已达到周抢单量上限"));
                    return false;
                }
            }


            if(Objects.nonNull(cooperationMaster.getCooperationBusinessWeekGrabOrderLimit()) && cooperationMaster.getCooperationBusinessWeekGrabOrderLimit() > 0){
                Integer weekAppointCnt = (Integer)masterFeatures.get(cooperationMaster.getMasterId()).get("week_appoint_cnt");
                if(Objects.nonNull(weekAppointCnt) && weekAppointCnt >= cooperationMaster.getCooperationBusinessWeekGrabOrderLimit()){
                    filterMap.put(Long.valueOf(cooperationMaster.getMasterId()), "该师傅周指派单量上限".concat(String.valueOf(cooperationMaster.getCooperationBusinessWeekGrabOrderLimit())).concat("单，当前周已指派").concat(weekAppointCnt.toString()).concat("单，已达到周指派量上限"));
                    return false;
                }
            }


            return true;

        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(filterMap)) {
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (filterMap.containsKey(cooperationBusinessMasterMatchLog.getMasterId())) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason(filterMap.get(cooperationBusinessMasterMatchLog.getMasterId()));
                }
            });
        }

        return masterBaseSearchList;
    }

    /**
     * 过滤外部设置的需排除师傅
     *
     * @param result
     * @param masterIds
     */
    private void outerMasterFilter(Set<String> result, String masterIds, List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {
        if (masterIds != null) {
            List<String> outerMasterList = Arrays.asList(masterIds.split(","));
            if (CollectionUtils.isNotEmpty(outerMasterList)) {
                cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                    if (outerMasterList.contains(String.valueOf(cooperationBusinessMasterMatchLog.getMasterId()))) {
                        cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                        cooperationBusinessMasterMatchLog.setMatchFailReason("外部设置的需排除的师傅");
                    }
                });
                outerMasterList.forEach(result::remove);
            }
        }
    }


    /**
     * 过滤已推送的合作经营师傅
     *
     * @param masterIdSet
     * @param getGlobalOrderTraceId
     */
    public void filterPushedMaster(Set<String> masterIdSet, Long getGlobalOrderTraceId, List<CooperationBusinessMasterMatchLog> cooperationBusinessMasterMatchLogList) {
        String masterIdStr = hBaseClient.querySingle("order_push", String.valueOf(getGlobalOrderTraceId), PushMode.COOPERATION_BUSINESS_MASTER.getCode());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)) {
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            cooperationBusinessMasterMatchLogList.forEach(cooperationBusinessMasterMatchLog -> {
                if (pushedMasterIdSet.contains(String.valueOf(cooperationBusinessMasterMatchLog.getMasterId()))) {
                    cooperationBusinessMasterMatchLog.setIsMatchSuccess(0);
                    cooperationBusinessMasterMatchLog.setMatchFailReason("已推送过的合作经营师傅");
                }
            });

            masterIdSet.removeAll(pushedMasterIdSet);
        }
    }

    /**
     * 优先按师傅接单区域匹配合作经营师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<CooperationBusinessMasterDto> matchCooperationBusinessMasterByReceiveOrderLv4DivisionPriority(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        BoolQueryBuilder boolQueryBuilder = buildQueryByReceiveOrderLv4Division(orderDetailData, masterMatchCondition);

        log.info("cooperationBusinessMasterList search cooperationBusinessMasterByReceiveOrderLv4DivisionPriority request:" + boolQueryBuilder.toString());

        List<MasterBaseSearch> cooperationBusinessMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                cooperationBusinessMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("cooperationBusinessMasterList matchCooperationBusinessMasterByReceiveOrderLv4DivisionPriority searchES result:" + JSON.toJSONString(cooperationBusinessMasterList));

        BoolQueryBuilder boolQueryBuilder2 = new BoolQueryBuilder();
        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            boolQueryBuilder2 = buildQueryByServeDivision(orderDetailData, masterMatchCondition);
            log.info("cooperationBusinessMasterList search cooperationBusinessMasterByServeDivision request:" + boolQueryBuilder2.toString());

            pageNum = 1;
            while (true) {
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder2, new Pageable(pageNum, pageSize), null);
                if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                    cooperationBusinessMasterList.addAll(esResponse.getDataList());
                    pageNum++;
                } else {
                    break;
                }
            }
            log.info("cooperationBusinessMasterList matchCooperationBusinessMasterByServeDivision searchES result:" + JSON.toJSONString(cooperationBusinessMasterList));
        }


        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            String matchFailReason = "先接单区域后服务区域查询es都未匹配到合作经营师傅！";
            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }
        return filterCooperationBusinessMaster(cooperationBusinessMasterList, orderDetailData);
    }

    private BoolQueryBuilder buildQueryByReceiveOrderLv4Division(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //合作经营接单区域筛选
        if (masterMatchCondition.getDivisionMatchLevel() == 4) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("receiveOrderFourthDivisionIdsAnalyzed", Lists.newArrayList(masterMatchCondition.getFourthDivisionId())));

        } else {
            //合作经营师傅接单区域receiveOrderFourthDivisionIds字段，针对特殊地址例如东莞下的镇，也存在该字段，但订单那边把东莞下的镇当做3级地址，因此直接匹配。
            //特殊订单确实是没有识别到四级地址，用三级地址匹配不会匹配到，但后续会继续用服务区域的三级地址去匹配，因此也不会有问题
            boolQueryBuilder.must(QueryBuilders.termsQuery("receiveOrderFourthDivisionIdsAnalyzed", Lists.newArrayList(masterMatchCondition.getThirdDivisionId())));
        }

        boolQueryBuilder.must(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterMatchCondition.freezingRecoverTime()));
        boolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("cityDivisionId", orderDetailData.getSecondDivisionId()));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("violationPunishLimitBusinessLineIds", Lists.newArrayList(masterMatchCondition.getBusinessLineId())));


        //查询合作经营师傅且开启派单
        boolQueryBuilder.must(QueryBuilders.termQuery("isDistributeOrder", 1));

        //技能筛选
        Set<String> techniqueIdSet = Arrays.stream(orderDetailData.getOrderTechniques().split("\\|")).collect(Collectors.toSet());
        BoolQueryBuilder shouldQuery = this.boolQueryBuilder("masterTechniqueIds", techniqueIdSet, 1);
        boolQueryBuilder.must(shouldQuery);
        return boolQueryBuilder;
    }

    private BoolQueryBuilder buildQueryByServeDivision(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //服务区域筛选
        boolQueryBuilder.must(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));

        boolQueryBuilder.must(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterMatchCondition.freezingRecoverTime()));
        boolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("violationPunishLimitBusinessLineIds", Lists.newArrayList(masterMatchCondition.getBusinessLineId())));


        //查询合作经营师傅且开启派单
        boolQueryBuilder.must(QueryBuilders.termQuery("isDistributeOrder", 1));

        //技能筛选
        Set<String> techniqueIdSet = Arrays.stream(orderDetailData.getOrderTechniques().split("\\|")).collect(Collectors.toSet());
        BoolQueryBuilder shouldQuery = this.boolQueryBuilder("masterTechniqueIds", techniqueIdSet, 1);
        boolQueryBuilder.must(shouldQuery);
        return boolQueryBuilder;
    }


    /**
     * 按服务区域匹配合作经营师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<CooperationBusinessMasterDto> matchCooperationBusinessMasterByServeLv4Division(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        BoolQueryBuilder boolQueryBuilder = buildQueryByServeDivision(orderDetailData, masterMatchCondition);
        log.info("cooperationBusinessMasterList search cooperationBusinessMasterByServeDivision request:" + boolQueryBuilder.toString());

        List<MasterBaseSearch> cooperationBusinessMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                cooperationBusinessMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("cooperationBusinessMasterList matchCooperationBusinessMasterByServeDivision searchES result:" + JSON.toJSONString(cooperationBusinessMasterList));
        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = cooperationBusinessMasterMatchLogRepository.selectByOrderIdAndOrderVersion(orderDetailData.getMasterOrderId(),orderDetailData.getOrderVersion());
            if (Objects.isNull(cooperationBusinessMasterMatchLog)) {
                String matchFailReason = "按服务区域查询es未匹配到合作经营师傅！";
                this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            }

            return null;
        }

        return filterCooperationBusinessMaster(cooperationBusinessMasterList, orderDetailData);
    }

    /**
     * 按接单区域匹配合作经营师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<CooperationBusinessMasterDto> matchCooperationBusinessMasterByReceiveOrderLv4Division(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        BoolQueryBuilder boolQueryBuilder = buildQueryByReceiveOrderLv4Division(orderDetailData, masterMatchCondition);

        log.info("cooperationBusinessMasterList search cooperationBusinessMasterByReceiveOrderLv4Division request:" + boolQueryBuilder.toString());

        List<MasterBaseSearch> cooperationBusinessMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                cooperationBusinessMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("cooperationBusinessMasterList matchCooperationBusinessMasterByReceiveOrderLv4Division searchES result:" + JSON.toJSONString(cooperationBusinessMasterList));

        if(CollectionUtils.isEmpty(cooperationBusinessMasterList)){
            CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = cooperationBusinessMasterMatchLogRepository.selectByOrderIdAndOrderVersion(orderDetailData.getMasterOrderId(),orderDetailData.getOrderVersion());
            if (Objects.isNull(cooperationBusinessMasterMatchLog)) {
                String matchFailReason = "按接单区域查询es未匹配到合作经营师傅！";
                this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            }

            return null;
        }

        return filterCooperationBusinessMaster(cooperationBusinessMasterList, orderDetailData);
    }


    private BoolQueryBuilder boolQueryBuilder(String fieldName, Set<String> shouldConditions, Integer minimumShouldMatch) {
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        shouldConditions.stream()
                .map(shouldCondition -> shouldQuery.should(stringMatchQueryBuilder(fieldName, shouldCondition, Operator.AND)))
                .collect(Collectors.toList());
        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }

    private MatchQueryBuilder stringMatchQueryBuilder(String fieldName, String value, Operator operator) {
        //设置查询类型为MatchQuery。
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        matchQueryBuilder.operator(operator);
        return matchQueryBuilder;
    }

    private QueryBuilder getMatchOrderMasterServeDivisionQuery(MasterMatchCondition masterMatchCondition) {

        QueryBuilder queryBuilder;

        //区域
        switch (masterMatchCondition.getDivisionMatchLevel()) {
            case 3:
                queryBuilder = QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(masterMatchCondition.getThirdDivisionId()));
                break;
            case 4:
                queryBuilder = QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(masterMatchCondition.getFourthDivisionId()));
                break;
            default:
                //never 没有区域的订单不会执行到查询
                queryBuilder = QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(masterMatchCondition.getThirdDivisionId()));
                break;
        }
        return queryBuilder;
    }

    private boolean checkOrderMasterNaviDistanceCityOpen(OrderDetailData orderDetailData){

        if("all".equals(orderMasterNavigationalDistanceCityList)){
            return true;
        }

        if("0".equals(orderMasterNavigationalDistanceCityList)){
            return false;
        }


        List<Long> cityList = Arrays.asList(orderMasterNavigationalDistanceCityList.split(",")).stream().map(Long::valueOf).distinct().collect(Collectors.toList());
        return cityList.contains(orderDetailData.getSecondDivisionId());
    }
}
