package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import lombok.Data;

@Data
@Document(indexAlias = "order_package_base", type = "order_package_base")
public class OrderPackageBaseIndex {

    private Long packageId;

    private Long packageConfigId;

    private Long maxPushNumberDaily;

    private Long createTime;

    private Long platformAutoMode;

}
