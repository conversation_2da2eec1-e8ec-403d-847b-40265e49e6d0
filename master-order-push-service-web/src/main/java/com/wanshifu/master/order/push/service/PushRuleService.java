package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> xinze<PERSON>@wshifu.com
 * @date : 2023-02-01 17:11
 */
public interface PushRuleService {


    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    PushRule detail(DetailRqt rqt);


    SimplePageInfo<PushRule> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);




}