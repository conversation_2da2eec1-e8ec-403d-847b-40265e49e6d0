package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushShuntRule;
import com.wanshifu.master.order.push.domain.rqt.pushShuntRule.*;

/**
 * <AUTHOR>
 * @date 2025/03/08 15:13
 */
public interface PushShuntRuleService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    PushShuntRule detail(DetailRqt rqt);


    SimplePageInfo<PushShuntRule> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

}
