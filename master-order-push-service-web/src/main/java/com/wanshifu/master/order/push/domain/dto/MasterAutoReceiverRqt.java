package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MasterAutoReceiverRqt {

    private Long globalOrderTraceId;

    private Long orderId;

    private Integer appointType;

    private Long appointDetailType;

    private String offerStrategy;

    private List<MasterPrice> masterList;

    private String extraId;

    private String subsidiaryParamJSON;

    @Data
    public static class MasterPrice implements Comparable<MasterPrice>{

        private Long masterId;

        private BigDecimal price;

        private MasterScene masterScene;

        private String note;

        private Integer autoReceiveSort;

        @Override
        public int compareTo(MasterPrice o) {
            return this.price.compareTo(o.price);
        }

    }

    @Data
    public static class SubsidiaryParam {
        private List<MasterSubsidiaryInfo> masterSubsidiaryInfoList;
    }

    @Data
    public static class MasterSubsidiaryInfo {
        /**
         * 师傅ID
         */
        private Long masterId;

        /**
         * 扩展ID,对象extraType的ID
         */
        private Long extraId;

        /**
         * 扩展类型(关联extraId,必填),master_recruit_id:师傅招募ID,master_order_package_id:师傅订单包ID
         */
        private String extraType;

        /**
         * 扩展标签(关联extraId,非必填),pre_sale_master_order_package:预售师傅订单包,supplement_master_order_package:补充师傅订单包
         */
        private String extraLabel;


        /**
         *  直接指派方式：district:按照区域  district_price:按区域+价格
         */
        private String directAppointMethod;

    }

    @Data
    public static class MasterScene {

        /**
         * 场景id
         */
        private Integer sceneId;

        /**
         * 标签名
         */
        private String tagName;
    }


}
