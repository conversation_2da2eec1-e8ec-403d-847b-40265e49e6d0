package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.CreatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.ListPushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.UpdatePushExportTaskRqt;

/**
 * <AUTHOR>
 * @date 2025/5/28 21:42
 */
public interface PushExportTaskService {

    Long createPushExportTask(CreatePushExportTaskRqt createPushExportTaskRqt);

    void updatePushExportTask(UpdatePushExportTaskRqt updatePushExportTaskRqt);

    SimplePageInfo<PushExportTask> listPushExportTask(ListPushExportTaskRqt listPushExportTaskRqt);
}
