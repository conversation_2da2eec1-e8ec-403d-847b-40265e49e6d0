package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.MatchRqt;
import com.wanshifu.master.order.push.mapper.CompensateDistributeMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;


/**
 * 补偿调度策略Repository
 * <AUTHOR>
 */
@Repository
public class CompensateDistributeRepository extends BaseRepository<CompensateDistribute> {

    @Resource
    private CompensateDistributeMapper compensateDistributeMapper;

    public int insertDistribute(Integer businessLineId,String strategyName,String strategyDesc,String categoryIds,String appointTypes,String orderPushFlag,Integer hasPrice,Integer hasCooperationUser,
                                String compensateType,Integer intervalTime,Integer triggerNum,Long createAccountId){
        CompensateDistribute compensateDistribute = new CompensateDistribute();
        compensateDistribute.setBusinessLineId(businessLineId);
        compensateDistribute.setStrategyName(strategyName);
        compensateDistribute.setStrategyDesc(strategyDesc);
        compensateDistribute.setCategoryIds(categoryIds);
        compensateDistribute.setAppointTypes(appointTypes);
        compensateDistribute.setOrderPushFlag(orderPushFlag);
        compensateDistribute.setHasPrice(hasPrice);
        compensateDistribute.setHasCooperationUser(hasCooperationUser);
        compensateDistribute.setCompensateType(compensateType);
        compensateDistribute.setIntervalTime(intervalTime);
        compensateDistribute.setTriggerNum(triggerNum);
        compensateDistribute.setCreateAccountId(createAccountId);
        compensateDistribute.setUpdateAccountId(createAccountId);
        return this.insertSelective(compensateDistribute);
    }


    public int updateDistribute(Integer distributeId,Integer businessLineId,String strategyName,String strategyDesc,String categoryIds,String appointTypes,String orderPushFlag,Integer hasPrice,Integer hasCooperationUser,
                                String compensateType,Integer intervalTime,Integer triggerNum,Long updateAccountId){
        CompensateDistribute compensateDistribute = new CompensateDistribute();
        compensateDistribute.setDistributeId(distributeId);
        compensateDistribute.setStrategyName(strategyName);
        compensateDistribute.setStrategyDesc(strategyDesc);
        compensateDistribute.setBusinessLineId(businessLineId);
        compensateDistribute.setCategoryIds(categoryIds);
        compensateDistribute.setAppointTypes(appointTypes);
        compensateDistribute.setOrderPushFlag(orderPushFlag);
        compensateDistribute.setHasPrice(hasPrice);
        compensateDistribute.setHasCooperationUser(hasCooperationUser);
        compensateDistribute.setCompensateType(compensateType);
        compensateDistribute.setIntervalTime(intervalTime);
        compensateDistribute.setTriggerNum(triggerNum);
        compensateDistribute.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(compensateDistribute);
    }


    public List<CompensateDistribute> selectList(Integer businessLineId,Long categoryId, String compensateType, String orderPushFlag){
        return compensateDistributeMapper.selectList(businessLineId,categoryId,compensateType,orderPushFlag);
    }


    public List<CompensateDistribute> selectByCategoryIdAndAppointType(Integer businessLineId,String orderPushFlag,Long categoryId,Integer appointType,
                                                                       Integer hasPrice,Integer hasCooperationUser){
        return  compensateDistributeMapper.selectByCategoryIdAndAppointType(businessLineId,orderPushFlag,categoryId,appointType,hasPrice,hasCooperationUser);
    }


    public int softDeleteByStrategyId(Integer distributeId) {
        CompensateDistribute compensateDistribute = new CompensateDistribute();
        compensateDistribute.setDistributeId(distributeId);
        compensateDistribute.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(compensateDistribute);
    }


    public CompensateDistribute selectByCategoryIdAndCompensateType(Integer businessLineId,String orderPushFlag,List<String> categoryIdList, List<String> appointTypeList,
                                                                  Integer distributeId,Integer hasPrice,Integer hasCooperationUser,String compensateType){
        return CollectionUtils.getFirstSafety(compensateDistributeMapper.selectByCategoryIdAndCompensateType(businessLineId,orderPushFlag,categoryIdList,appointTypeList,distributeId,hasPrice,hasCooperationUser,compensateType));
    }


    public List<CompensateDistribute> match(MatchRqt rqt){
        return  compensateDistributeMapper.selectByCategoryIdAndAppointType(rqt.getBusinessLineId(),rqt.getOrderPushFlag(),
                rqt.getCategoryId(),rqt.getAppointType(),rqt.getHasPrice(),rqt.getHasCooperationUser());
    }




    }