package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.po.PushRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/03/08 14:32
 */
public interface PushLimitRuleMapper extends IBaseCommMapper<PushLimitRule> {


    List<PushLimitRule> selectList( @Param("cityId")String cityId,@Param("businessLineId")Integer businessLineId,@Param("ruleName") String ruleName,
                                    @Param("createStartTime") Date createStartTime, @Param("createEndTime") Date createEndTime,@Param("ruleStatus")Integer ruleStatus);



    List<PushLimitRule> selectByCityIdAndBusinessLineId(@Param("ruleId") Integer ruleId,@Param("businessLineId")  Integer businessLineId,
                                                        @Param("cityIdList")List<String> cityIdList,@Param("crowdLabel")  String crowdLabel);



    }
