package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushHandle;
import org.apache.ibatis.annotations.Param;

/**
 * 推送处理记录Mapper
 * <AUTHOR>
 */
public interface PushHandleMapper extends IBaseCommMapper<PushHandle> {


    int createPushHandleTable(@Param("tableName") String tableName);

    /**
     * 保存推单评分记录
     * @param pushHandle
     * @param timeMark
     * @return
     */
    int insertPushHandle(@Param("pushHandle") PushHandle pushHandle,@Param("timeMark")String timeMark);

}