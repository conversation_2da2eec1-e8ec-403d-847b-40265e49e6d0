package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import com.wanshifu.master.order.push.mapper.CooperationBusinessMasterMatchLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/20 19:59
 */
@Repository
public class CooperationBusinessMasterMatchLogRepository extends BaseRepository<CooperationBusinessMasterMatchLog> {

    @Resource
    private CooperationBusinessMasterMatchLogMapper cooperationBusinessMasterMatchLogMapper;

    public CooperationBusinessMasterMatchLog selectByOrderIdAndMasterId(Long orderId,Long masterId){
        CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = new CooperationBusinessMasterMatchLog();
        cooperationBusinessMasterMatchLog.setOrderId(orderId);
        cooperationBusinessMasterMatchLog.setMasterId(masterId);
        cooperationBusinessMasterMatchLog.setIsAutoGrabSuccess(1);
        return CollectionUtils.getFirstSafety(this.select(cooperationBusinessMasterMatchLog));
    }

    public CooperationBusinessMasterMatchLog selectByOrderIdAndOrderVersion(Long orderId, String orderVersion){
        CooperationBusinessMasterMatchLog cooperationBusinessMasterMatchLog = new CooperationBusinessMasterMatchLog();
        cooperationBusinessMasterMatchLog.setOrderId(orderId);
        cooperationBusinessMasterMatchLog.setOrderVersion(orderVersion);
        return CollectionUtils.getFirstSafety(this.select(cooperationBusinessMasterMatchLog));
    }

    public List<CooperationBusinessMasterMatchLog> selectList(String orderNo, Long masterId, Date orderCreateTimeStart, Date orderCreateTimeEnd) {

        return cooperationBusinessMasterMatchLogMapper.selectList(orderNo, masterId, orderCreateTimeStart, orderCreateTimeEnd);
    }
}
