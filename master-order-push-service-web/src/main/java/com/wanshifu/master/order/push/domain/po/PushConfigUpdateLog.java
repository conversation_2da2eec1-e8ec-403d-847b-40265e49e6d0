package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 推单配置变更表
 * @date 2025/4/11 13:56
 */
@Data
@ToString
@Table(name = "push_config_update_log")
public class PushConfigUpdateLog {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 配置表的主键id
     */
    @Column(name = "primary_key_id")
    private Long primaryKeyId;

    /**
     * 配置名称
     */
    @Column(name = "config_name")
    private String configName;


    /**
     * 配置表名
     */
    @Column(name = "table_name")
    private String tableName;

    /**
     * 配置详情
     */
    @Column(name = "config_detail")
    private String configDetail;

    /**
     * 日志时间
     */
    @Column(name = "create_time")
    private Date createTime;

}
