package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.po.PushShuntRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/03/08 14:32
 */
public interface PushShuntRuleMapper extends IBaseCommMapper<PushShuntRule> {


    List<PushShuntRule> selectList( @Param("cityId")String cityId,@Param("businessLineId")Integer businessLineId,@Param("ruleName") String ruleName,
                                    @Param("createStartTime") Date createStartTime, @Param("createEndTime") Date createEndTime,@Param("ruleStatus")Integer ruleStatus);



    List<PushShuntRule> selectByCityIdAndBusinessLineId(@Param("ruleId") Integer ruleId,@Param("businessLineId")  Integer businessLineId,
                                                        @Param("cityIdList")List<String> cityIdList,@Param("crowdLabel")  String crowdLabel);



    }
