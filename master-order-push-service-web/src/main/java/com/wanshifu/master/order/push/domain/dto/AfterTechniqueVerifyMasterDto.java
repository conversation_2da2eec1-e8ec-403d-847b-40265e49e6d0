package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/1 14:12
 */
@Data
public class AfterTechniqueVerifyMasterDto {

    /**
     * 师傅id
     */
    private String masterId;

    /**
     * 技能验证后师傅字段
     * 是否开启派单 1:开启,0:关闭
     */
    private Integer afterTechniqueVerifyIsDistributeOrder;

    /**
     * 技能验证后师傅字段
     * 可派单类目(技能验证后的类目)
     */
    private String afterTechniqueVerifyCategoryIds;

    /**
     * 抢单顺序
     */
    private Integer grabSort = 0;

    /**
     * 评分
     */
    private BigDecimal score = BigDecimal.ZERO;
}
