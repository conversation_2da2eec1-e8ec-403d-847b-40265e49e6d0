package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/4 19:02
 */
public class OrderDistributeContext {


    private static Map<String, OrderDistributeService> maps = new HashMap<>();

    public static OrderDistributeService getInstance(String pushMode) {
        if (Objects.isNull(pushMode) || !maps.containsKey(pushMode)){
            throw new IllegalArgumentException(String.format("参数违法,node:%s", pushMode));

        }
        return maps.get(pushMode);
    }

    public static void register(String pushMode, OrderDistributeService orderDistributeService) {
        Assert.notNull(pushMode, "type can't be null");
        maps.put(pushMode, orderDistributeService);
    }
}
