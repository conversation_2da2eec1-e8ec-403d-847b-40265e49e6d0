package com.wanshifu.master.order.push.service.impl;


import com.alibaba.fastjson.JSON;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象订单师傅匹配器
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOrderMasterMatcher implements OrderMasterMatcher{

    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    protected abstract boolean checkPreCondition(OrderDetailData orderDetailData);


    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public abstract MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition);


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    protected abstract void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult);


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    protected abstract boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult);



    @Override
    public boolean executeMatch(OrderDetailData orderDetailData,MasterMatchCondition masterCondition){

        try{
            if(!checkPreCondition(orderDetailData)){
                return false;
            }
            MatchMasterResult result = match(orderDetailData,masterCondition);
            boolean pushResult = executePush(orderDetailData,result);
            if(pushResult){
                afterPush(orderDetailData,masterCondition,result);
            }
            return pushResult;
        }catch(Exception e){
            log.error(String.format("推单失败,orderDetailData:%s", JSON.toJSON(orderDetailData)),e);
            return false;
        }

    }





}
