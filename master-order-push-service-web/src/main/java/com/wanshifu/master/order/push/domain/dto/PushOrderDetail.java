package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PushOrderDetail {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 全局订单iD
     */
    private Long globalOrderTraceId;

    /**
     * 推单距离
     */
    private Long pushDistance;

    /**
     * 推单距离类型
     */
    private Integer pushDistanceType;


    /**
     * 是否技能相关
     */
    private Integer accordingTechnologyPushFlag = 1;


    private BigDecimal score;

    private Integer crossCityPush;


}
