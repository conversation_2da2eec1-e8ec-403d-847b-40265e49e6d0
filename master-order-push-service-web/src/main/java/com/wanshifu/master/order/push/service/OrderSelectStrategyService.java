package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;


/**
 * <AUTHOR>
 * @date 2025/3/4 11:30
 */
public interface OrderSelectStrategyService {

    Integer create(CreateOrderSelectStrategyRqt rqt);


    Integer update(UpdateOrderSelectStrategyRqt rqt);

    Integer enable(EnableOrderSelectStrategyRqt rqt);

    OrderSelectStrategy detail(OrderSelectStrategyDetailRqt rqt);

    SimplePageInfo<OrderSelectStrategy> list(GetOrderSelectStrategyListRqt rqt);


    Integer delete(DeleteOrderSelectStrategyRqt rqt);


    SimplePageInfo<MasterQuotaResp> masterQuota(MasterQuotaListRqt rqt);
}
