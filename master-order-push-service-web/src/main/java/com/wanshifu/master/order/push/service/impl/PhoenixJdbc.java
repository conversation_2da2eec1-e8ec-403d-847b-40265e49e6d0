package com.wanshifu.master.order.push.service.impl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class PhoenixJdbc {

    static {
        try {
            Class.forName("org.apache.phoenix.jdbc.PhoenixDriver");
        } catch (ClassNotFoundException e) {
            System.out.println("error " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        String createSql = "create table if not exists user_tb(id VARCHAR,uname VARCHAR  CONSTRAINT pk PRIMARY KEY (id))";
        Connection connection = null;
        try {

            String ipList = "***********:2181";
            String znodeParent = "/hbase-emr-5g4yaki0";
            String url = "jdbc:phoenix:" + ipList + ":" +znodeParent;
            connection = DriverManager.getConnection(url);

            Statement statement = connection.createStatement();

            statement.executeUpdate(createSql);
            statement.executeUpdate("upsert into user_tb values ('1','张三')");
            statement.executeUpdate("upsert into user_tb values ('2','李四')");
            connection.commit();
        } catch (SQLException throwables) {
            System.out.println("error " + throwables.getMessage());
        }
    }
}