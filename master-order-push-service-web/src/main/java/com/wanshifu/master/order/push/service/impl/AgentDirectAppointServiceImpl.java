package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.information.domain.api.response.common.BatchGetMasterBaseInfoResp;
import com.wanshifu.master.order.push.api.MasterOrderApi;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AgentDistributePriority;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.GrabOrderValidRqt;
import com.wanshifu.master.order.push.domain.rqt.UpdateDirectAppointFailReasonRqt;
import com.wanshifu.master.order.push.repository.AgentDistributeStrategyRepository;
import com.wanshifu.master.order.push.repository.AgentOrderDistributeRepository;
import com.wanshifu.master.order.push.service.AgentDirectAppointService;
import com.wanshifu.master.order.push.service.DistributeOrderDistributeService;
import com.wanshifu.master.order.push.service.MasterCommon;
import com.wanshifu.master.order.push.service.impl.DistributeFactory;
import com.wanshifu.master.order.push.service.impl.Tools;
import com.wanshifu.master.order.push.util.DistanceUtil;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 描述 :  代理商直接指派service.
 * @date 2025/2/28 11:51
 */
@Service
@Slf4j
public class AgentDirectAppointServiceImpl implements AgentDirectAppointService {


    @Resource
    private AgentDistributeStrategyRepository agentDistributeStrategyRepository;

    @Resource
    private MasterCommon masterCommon;
    @Resource
    private DistributeFactory distributeFactory;
    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;
    @Resource
    private Tools tools;

    @Resource
    private MasterOrderApi masterOrderApi;

    @Resource
    private DistributeOrderDistributeService distributeOrderDistributeService;


    @Resource
    private AgentOrderDistributeRepository agentOrderDistributeRepository;

    /**
     * 新增的合作商的初始概率
     */
    @Value("${agent.addAgentProbability:0.15}")
    private BigDecimal addAgentProbability;

    /**
     * 合作商最低分概率
     */
    @Value("${agent.baselineAgentProbability:0.05}")
    private BigDecimal baselineAgentProbability;

    @Override
    public int agentDirectAppoint(AgentDirectAppointDto agentDirectAppointRqt) {
        String agentMasterList = agentDirectAppointRqt.getAgentMasterList();
        String serveIds = agentDirectAppointRqt.getServeIds();
        Long orderId = agentDirectAppointRqt.getOrderId();
        Long thirdDivisionId = agentDirectAppointRqt.getThirdDivisionId();
        BigDecimal orderLatitude = agentDirectAppointRqt.getOrderLatitude();
        BigDecimal orderLongitude = agentDirectAppointRqt.getOrderLongitude();
        if (StringUtils.isBlank(agentMasterList) || StringUtils.isBlank(serveIds) || Objects.isNull(orderId) || Objects.isNull(thirdDivisionId)) {
            return 0;
        }
        List<AgentPushMasterVo> agentPushMasterList = JSON.parseArray(agentMasterList, AgentPushMasterVo.class);
        if (CollectionUtils.isEmpty(agentPushMasterList)) {
            return 0;
        }
        agentPushMasterList = agentPushMasterList.stream().filter(it -> StringUtils.isNotBlank(it.getMasterList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentPushMasterList)) {
            return 0;
        }
        List<Long> serveIdList = Arrays.stream(serveIds.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        //组装代理商对应的调度策略(直接指派)
        List<AgentMasterDistributeStrategyVo> agentMasterDistributeStrategyVos = this.buildAgentMasterDistributeStrategy(agentPushMasterList, serveIdList, thirdDivisionId);
        if (CollectionUtils.isEmpty(agentMasterDistributeStrategyVos)) {
            return 0;
        }
        String remark;
        //判断是否都是随机指派策略
        boolean allRandom = agentMasterDistributeStrategyVos.stream().allMatch(it -> AgentDistributePriority.RANDOM.code.equals(it.getDistributePriority()));
        if (allRandom) {
            //全部为随机分配
            //随机取一个代理商
            AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo = RandomUtil.randomEle(agentMasterDistributeStrategyVos);
            //随机取一个师傅
            Long finalMasterId = RandomUtil.randomEle(agentMasterDistributeStrategyVo.getMasterList());
            AgentDistributeStrategy agentDistributeStrategy = agentMasterDistributeStrategyVo.getAgentDistributeStrategy();
            remark = "【全部为随机分配规则:随机合作商,随机师傅】";
            log.info(remark + "orderId:{},masterId:{},agentId:{},strategyId:{}", orderId, finalMasterId, agentMasterDistributeStrategyVo.getAgentId(), agentDistributeStrategy.getStrategyId());
            this.directAppoint(orderId, finalMasterId, agentMasterDistributeStrategyVo.getMasterList(), agentDistributeStrategy, agentDirectAppointRqt, remark);
            return 1;
        }
        //非全部随机（一定有智能分配,可能有随机分配）
        //筛选智能分配的合作商
        agentMasterDistributeStrategyVos = agentMasterDistributeStrategyVos.stream().filter(it -> AgentDistributePriority.SMART.code.equals(it.getDistributePriority())).collect(Collectors.toList());
        if (agentMasterDistributeStrategyVos.size() == 1 || agentMasterDistributeStrategyVos.size() >= 20) {
            //只有一个智能分配的合作商,订单地址和师傅常住地直线距离近者优先分配
            AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo = agentMasterDistributeStrategyVos.get(0);
            Long finalMasterId = this.getFinalMasterIdByDistance(agentMasterDistributeStrategyVo, orderLongitude, orderLatitude);
            AgentDistributeStrategy agentDistributeStrategy = agentMasterDistributeStrategyVo.getAgentDistributeStrategy();
            remark = "【仅有一个智能分配合作商或者合作商大于20个:按照距离分配师傅】";
            log.info(remark + "orderId:{},masterId:{},agentId:{},strategyId:{}", orderId, finalMasterId, agentMasterDistributeStrategyVo.getAgentId(), agentDistributeStrategy.getStrategyId());
            this.directAppoint(orderId, finalMasterId, agentMasterDistributeStrategyVo.getMasterList(), agentDistributeStrategy, agentDirectAppointRqt, remark);
            return 1;
        } else {
            //多个合作商，计算评分
            Map<String, Integer> agentScoreIdMap = Maps.newHashMap();
            agentMasterDistributeStrategyVos.forEach(it -> agentScoreIdMap.put(String.valueOf(it.getAgentId()), it.getAgentDistributeStrategy().getScoringStrategyId().intValue()));

            List<ScorerAgent> scorerAgents = distributeFactory.scoreAgent(orderId, agentScoreIdMap);
//            //todo 调试
//            List<ScorerAgent> scorerAgents = Lists.newArrayList();
//            scorerAgents.add(new ScorerAgent("223",new BigDecimal("1")));
//            scorerAgents.add(new ScorerAgent("377",new BigDecimal("30.53")));
//            scorerAgents.add(new ScorerAgent("378",new BigDecimal("0")));
//            scorerAgents.add(new ScorerAgent("379",new BigDecimal("0")));

            BigDecimal totalScore = scorerAgents.stream().map(ScorerAgent::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
            //新增的合作商
            List<ScorerAgent> addAgents = scorerAgents.stream().filter(it -> it.getScore().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
            long addAgentCount = addAgents.size();
            //新增的合作商总概率
            BigDecimal addAgentTotalProbability = addAgentProbability.multiply(new BigDecimal(String.valueOf(addAgentCount)));
            if (BigDecimal.ZERO.compareTo(totalScore) == 0 || addAgentProbability.multiply(new BigDecimal(String.valueOf(addAgentCount))).compareTo(BigDecimal.ONE) >= 0) {
                //评分均为0，或者新增合作商的总占比超过100%,则随机选取一个代理商 ,按照距离排序
                AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo = RandomUtil.randomEle(agentMasterDistributeStrategyVos);
                Long finalMasterId = this.getFinalMasterIdByDistance(agentMasterDistributeStrategyVo, orderLongitude, orderLatitude);
                AgentDistributeStrategy agentDistributeStrategy = agentMasterDistributeStrategyVo.getAgentDistributeStrategy();
                remark = "【多个智能分配合作商,评分均为0或者新增合作商的总占比超过100%:随机合作商,按照距离分配师傅】";
                log.info(remark + "orderId:{},masterId:{},agentId:{},strategyId:{}", orderId, finalMasterId, agentMasterDistributeStrategyVo.getAgentId(), agentDistributeStrategy.getStrategyId());
                this.directAppoint(orderId, finalMasterId, agentMasterDistributeStrategyVo.getMasterList(), agentDistributeStrategy, agentDirectAppointRqt, remark);
                return 1;
            }
            //已有合作商
            List<ScorerAgent> existAgents = scorerAgents.stream().filter(it -> it.getScore().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            List<ScorerAgentDto> existScorerAgentDtos = this.buildExistScorerAgentDtos(existAgents, totalScore, agentScoreIdMap);

            //新增的合作商总概率➗已存在的合作商的数量=每个已有合作商需要剥削的概率值
            BigDecimal needExploitProbability = addAgentTotalProbability.divide(new BigDecimal(existAgents.size()), 10, BigDecimal.ROUND_DOWN);
            //已有合作商进行概率分摊
            this.shareNeedExploitProbability(existScorerAgentDtos, needExploitProbability);
            //重新计算新增合作商的概率
            List<ScorerAgentDto> addScorerAgentDtos = this.getAddScorerAgentDtos(addAgents, existScorerAgentDtos, agentScoreIdMap);
            //合并最终需要按照概率分配的合作商
            List<ScorerAgentDto> allScorerAgentDtos = Lists.newArrayList();
            allScorerAgentDtos.addAll(addScorerAgentDtos);
            allScorerAgentDtos.addAll(existScorerAgentDtos);
            BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(BigDecimal.ZERO, BigDecimal.ONE);
            AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo = this.getAgentByProbability(randomBigDecimal,orderId, allScorerAgentDtos, agentMasterDistributeStrategyVos);

            if (agentMasterDistributeStrategyVo == null) {
                agentMasterDistributeStrategyVo = RandomUtil.randomEle(agentMasterDistributeStrategyVos);
            }
            AgentDistributeStrategy agentDistributeStrategy = agentMasterDistributeStrategyVo.getAgentDistributeStrategy();
            Long finalMasterId = this.getFinalMasterIdByDistance(agentMasterDistributeStrategyVo, orderLongitude, orderLatitude);
            remark = StrUtil.format("【多个智能分配合作商,智能选取:智能合作商,按照距离分配师傅,randomBigDecimal:{},scoreDetail:{}】", randomBigDecimal,JSON.toJSONString(allScorerAgentDtos));
            log.info(remark + "orderId:{},masterId:{},agentId:{},strategyId:{}", orderId, finalMasterId, agentMasterDistributeStrategyVo.getAgentId(), agentDistributeStrategy.getStrategyId());
            this.directAppoint(orderId, finalMasterId, agentMasterDistributeStrategyVo.getMasterList(), agentDistributeStrategy, agentDirectAppointRqt, remark);
            return 1;
        }
    }

    public void directAppoint(Long orderId, Long finalMasterId, List<Long> masterList, AgentDistributeStrategy agentDistributeStrategy, AgentDirectAppointDto agentDirectAppointRqt, String remark) {

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        if (orderGrabByIdResp == null || orderGrabByIdResp.getOrderBase() == null) {
            return;
        }

        Long distributeId = agentOrderDistributeRepository.insertAgentOrderDistribute(orderId, agentDistributeStrategy.getAgentId()
                , agentDirectAppointRqt.getServeIds(), agentDirectAppointRqt.getThirdDivisionId(), agentDistributeStrategy.getStrategyId()
                , agentDistributeStrategy.getDistributeRule(), agentDistributeStrategy.getDistributePriority(), remark);


        StringBuilder failReason = new StringBuilder();
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();

        GrabOrderValidRqt rqt = new GrabOrderValidRqt();
        rqt.setOrderId(orderId);
        rqt.setMasterId(finalMasterId);
        rqt.setGrabType(4);
        rqt.setOrderModifyTime(orderBase.getOrderModifyTime());
        JSONObject jsonObject = tools.catchNoLog(() -> masterOrderApi.autoGrabOrder(rqt));
        boolean result = (jsonObject != null) && Objects.equals(jsonObject.get("grabOrderFlag"), true);
        if (!result) {
            log.error(String.format("代理商订单直接指派失败,orderId:%d,masterId:%d", orderBase.getOrderId(), finalMasterId));

            if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("tipContent"))) {
                failReason.append("masterId:").append(finalMasterId).append(jsonObject.getString("tipContent"));
            }
            masterList.remove(finalMasterId);
            for (Long currentMasterId : masterList) {
                rqt.setMasterId(currentMasterId);
                jsonObject = tools.catchNoLog(() -> masterOrderApi.autoGrabOrder(rqt));
                result = (jsonObject != null) && Objects.equals(jsonObject.get("grabOrderFlag"), true);
                if (result) {
                    break;
                } else {
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("tipContent"))) {
                        failReason.append("masterId:").append(rqt.getMasterId()).append(jsonObject.getString("tipContent"));
                    }
                    log.error(String.format("代理商订单直接指派失败,orderId:%d,masterId:%d", orderBase.getOrderId(), currentMasterId));
                }
            }
            if (StringUtils.isNotBlank(failReason.toString())) {
                UpdateDirectAppointFailReasonRqt updateDirectAppointFailReasonRqt = new UpdateDirectAppointFailReasonRqt();
                updateDirectAppointFailReasonRqt.setDistributeId(distributeId);
                updateDirectAppointFailReasonRqt.setDirectAppointFailReason(remark + failReason.toString());
                this.updateDirectAppointFailReason(updateDirectAppointFailReasonRqt);
            }

        }
    }

    public Integer updateDirectAppointFailReason(UpdateDirectAppointFailReasonRqt rqt) {
        return agentOrderDistributeRepository.updateDirectAppointFailReason(rqt.getDistributeId(), rqt.getDirectAppointFailReason());
    }

    private Long getFinalMasterIdByDistance(AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo, BigDecimal orderLongitude, BigDecimal orderLatitude) {
        List<Long> masterList = agentMasterDistributeStrategyVo.getMasterList();
        masterList = this.sortMaster(masterList, orderLongitude, orderLatitude);
        return masterList.get(0);
    }

    private AgentMasterDistributeStrategyVo getAgentByProbability(BigDecimal randomBigDecimal, Long orderId, List<ScorerAgentDto> scorerAgentDtos, List<AgentMasterDistributeStrategyVo> agentMasterDistributeStrategyVos) {

        log.info("订单orderId:{},准备根据概率分配合作商!合作商:{},randomBigDecimal:{}", orderId, scorerAgentDtos, randomBigDecimal);
        // 根据概率进行选择
        BigDecimal cumulativeProbability = BigDecimal.ZERO;
        Long agentId = null;

        for (ScorerAgentDto scorerAgentDto : scorerAgentDtos) {
            cumulativeProbability = cumulativeProbability.add(scorerAgentDto.getFinalProbability());
            if (randomBigDecimal.compareTo(cumulativeProbability) <= 0) {
                agentId = Long.parseLong(scorerAgentDto.getAgentId());
                break;
            }
        }
        if (agentId == null) {
            log.error("订单orderId:{},根据概率分配合作商失败!合作商:{},randomBigDecimal:{}", orderId, scorerAgentDtos, randomBigDecimal);
            return null;
        }

        Long finalAgentId = agentId;
        return agentMasterDistributeStrategyVos.stream()
                .filter(it -> Objects.equals(it.getAgentId(), finalAgentId))
                .findFirst().orElse(null);
    }

    private List<ScorerAgentDto> getAddScorerAgentDtos(List<ScorerAgent> addAgents, List<ScorerAgentDto> existScorerAgentDtos, Map<String, Integer> agentScoreIdMap) {
        if (CollectionUtils.isEmpty(addAgents)) {
            return Collections.emptyList();
        }
        BigDecimal totalExistScorerAgentFinalProbability = existScorerAgentDtos.stream().map(ScorerAgentDto::getFinalProbability).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal finalProbability = BigDecimal.ONE.subtract(totalExistScorerAgentFinalProbability).divide(new BigDecimal(addAgents.size()), 10, BigDecimal.ROUND_UP);
        return addAgents.stream().map(it -> {
            String agentId = it.getAgentId();
            ScorerAgentDto scorerAgentDto = new ScorerAgentDto();
            scorerAgentDto.setScoringStrategyId(agentScoreIdMap.get(agentId).longValue());
            scorerAgentDto.setAgentId(agentId);
            scorerAgentDto.setScore(it.getScore());
            scorerAgentDto.setFinalProbability(finalProbability);
            return scorerAgentDto;
        }).collect(Collectors.toList());

    }

    /**
     * 剥削已存在代理商的分配概率
     *
     * @param existScorerAgentDtos
     * @param needExploitProbability
     */
    private void shareNeedExploitProbability(List<ScorerAgentDto> existScorerAgentDtos, BigDecimal needExploitProbability) {
        if (needExploitProbability.compareTo(BigDecimal.ZERO) > 0) {
            existScorerAgentDtos.forEach(it -> {
                BigDecimal canExploitProbability = it.getCanExploitProbability();
                //可剥削的概率>0
                if (canExploitProbability.compareTo(BigDecimal.ZERO) > 0) {
                    if (canExploitProbability.compareTo(needExploitProbability) >= 0) {
                        it.setExploitProbability(needExploitProbability);
                        it.setFinalProbability(it.getInitProbability().subtract(needExploitProbability));
                    } else {
                        it.setExploitProbability(canExploitProbability);
                        it.setFinalProbability(it.getInitProbability().subtract(canExploitProbability));
                    }
                }
            });
        }
    }


    private List<ScorerAgentDto> buildExistScorerAgentDtos(List<ScorerAgent> scorerAgents, BigDecimal totalScore, Map<String, Integer> agentScoreIdMap) {
        return scorerAgents.stream().map(it -> {
                    String agentId = it.getAgentId();
                    ScorerAgentDto scoreAgentDto = new ScorerAgentDto();
                    scoreAgentDto.setScoringStrategyId(agentScoreIdMap.get(agentId).longValue());
                    scoreAgentDto.setAgentId(agentId);
                    scoreAgentDto.setScore(it.getScore());
                    BigDecimal initProbability = it.getScore().divide(totalScore, 10, BigDecimal.ROUND_UP);
                    scoreAgentDto.setInitProbability(initProbability);
                    scoreAgentDto.setFinalProbability(initProbability);
                    scoreAgentDto.setCanExploitProbability(initProbability.compareTo(baselineAgentProbability) > 0 ? initProbability.subtract(baselineAgentProbability) : BigDecimal.ZERO);
                    return scoreAgentDto;
                }
        ).collect(Collectors.toList());
    }


    private List<Long> sortMaster(List<Long> masterList, BigDecimal orderLongitude, BigDecimal orderLatitude) {
        if (!BigDecimalUtil.isGreaterThan(orderLongitude, BigDecimal.ZERO) || !BigDecimalUtil.isGreaterThan(orderLatitude, BigDecimal.ZERO)) {
            //订单经纬度有误,将师傅打乱重新返回
            Collections.shuffle(masterList);
            return masterList;
        }
        Map<Long, BatchGetMasterBaseInfoResp> masterBaseInfoRespMap = masterCommon.getMasterByMasterIds(masterList)
                .stream().collect(Collectors.toMap(BatchGetMasterBaseInfoResp::getMasterId, Function.identity()));

        return masterList.stream()
                .sorted((m1, m2) -> {
                    BigDecimal m1lon = Optional.ofNullable(masterBaseInfoRespMap.get(m1)).map(BatchGetMasterBaseInfoResp::getLocationLongitude).orElse(BigDecimal.ZERO);
                    BigDecimal m1lat = Optional.ofNullable(masterBaseInfoRespMap.get(m1)).map(BatchGetMasterBaseInfoResp::getLocationLatitude).orElse(BigDecimal.ZERO);
                    BigDecimal m2lon = Optional.ofNullable(masterBaseInfoRespMap.get(m2)).map(BatchGetMasterBaseInfoResp::getLocationLongitude).orElse(BigDecimal.ZERO);
                    BigDecimal m2lat = Optional.ofNullable(masterBaseInfoRespMap.get(m2)).map(BatchGetMasterBaseInfoResp::getLocationLatitude).orElse(BigDecimal.ZERO);

                    double distance1 = DistanceUtil.distance(orderLongitude.doubleValue(), orderLongitude.longValue(), m1lon.doubleValue(), m1lat.doubleValue());
                    double distance2 = DistanceUtil.distance(orderLongitude.doubleValue(), orderLongitude.longValue(), m2lon.doubleValue(), m2lat.doubleValue());
                    return Double.compare(distance1, distance2);
                }).collect(Collectors.toList());
    }

    private List<AgentMasterDistributeStrategyVo> buildAgentMasterDistributeStrategy(List<AgentPushMasterVo> agentPushMasterVos, List<Long> serveIdList, Long thirdDivisionId) {

        return agentPushMasterVos.stream().map(it -> {

            AgentMasterDistributeStrategyVo agentMasterDistributeStrategyVo = new AgentMasterDistributeStrategyVo();
            agentMasterDistributeStrategyVo.setAgentId(it.getAgentId());
            agentMasterDistributeStrategyVo.setMasterList(Arrays.stream(it.getMasterList().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            AgentDistributeStrategy agentDistributeStrategy = this.selectAgentDistributeStrategy(it.getAgentId(), serveIdList, thirdDivisionId);

            agentMasterDistributeStrategyVo.setAgentDistributeStrategy(agentDistributeStrategy);
            agentMasterDistributeStrategyVo.setDistributePriority(Optional.ofNullable(agentDistributeStrategy).map(AgentDistributeStrategy::getDistributePriority).orElse(null));
            return agentMasterDistributeStrategyVo;
        }).filter(it -> it.getAgentDistributeStrategy() != null).collect(Collectors.toList());

    }

    private AgentDistributeStrategy selectAgentDistributeStrategy(Long agendId, List<Long> serveIdList, Long thirdDivisionId) {
        //直接指派的策略（随机指派和智能指派）
        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeStrategyRepository.selectByAgentIdAndServeIdsAndDivisionId(agendId, serveIdList, thirdDivisionId);
        if (CollectionUtils.isEmpty(agentDistributeStrategyList)) {
            return null;
        }
        Map<String, List<AgentDistributeStrategy>> agentDistributeStrategyMap = agentDistributeStrategyList.stream().collect(Collectors.groupingBy(AgentDistributeStrategy::getDistributePriority));
        List<AgentDistributeStrategy> smartAgentDistributeStrategyList = agentDistributeStrategyMap.get(AgentDistributePriority.SMART.code);
        if (CollectionUtils.isNotEmpty(smartAgentDistributeStrategyList)) {
            return smartAgentDistributeStrategyList.get(0);
        }
        List<AgentDistributeStrategy> randomAgentDistributeStrategyList = agentDistributeStrategyMap.get(AgentDistributePriority.RANDOM.code);
        if (CollectionUtils.isNotEmpty(randomAgentDistributeStrategyList)) {
            return randomAgentDistributeStrategyList.get(0);
        }
        return null;
    }


}
