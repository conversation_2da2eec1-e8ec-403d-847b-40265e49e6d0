package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_master_order")
public class MasterOrder {

    @Id
    @Column(name = "master_order_id")
    private String masterOrderId;

    @Column(name = "master_id")
    private String masterId;

    @Column(name = "is_disinterest_order")
    private String isDisinterestOrder;

    @Column(name = "update_time")
    private Date updateTime;
}
