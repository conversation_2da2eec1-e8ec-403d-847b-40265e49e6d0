package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.GetOrderScoringStrategyListRqt;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 16:14
 */
public interface OrderScoringStrategyMapper extends IBaseCommMapper<OrderScoringStrategy> {

    List<OrderScoringStrategy> selectList(GetOrderScoringStrategyListRqt rqt);
}
