package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderStrategyRelate;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 14:55
 */
@Repository
public class OrderStrategyRelateRepository extends BaseRepository<OrderStrategyRelate> {


    public int insert(Integer relateId, String strategyRelateType, Integer orderSelectStrategyId, Integer orderScoringStrategyId) {
        OrderStrategyRelate strategyRelate = new OrderStrategyRelate();
        strategyRelate.setRelateId(relateId);
        strategyRelate.setRelateType(strategyRelateType);
        strategyRelate.setOrderSelectStrategyId(orderSelectStrategyId);
        strategyRelate.setOrderScoringStrategyId(orderScoringStrategyId);
        return this.insertSelective(strategyRelate);
    }

    public void deleteByRelateId(Integer relateId) {
        Example example = new Example(OrderStrategyRelate.class);
        example.createCriteria().andEqualTo("relateId", relateId);
        this.deleteByExample(example);
    }

    public OrderStrategyRelate selectByOrderBaseSelectStrategyId(Integer orderSelectStrategyId) {
        Example example = new Example(OrderStrategyRelate.class);
        example.createCriteria().andEqualTo("orderSelectStrategyId", orderSelectStrategyId);
        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
    }



    public OrderStrategyRelate selectByOrderScoringStrategyId(Integer orderScoringStrategyId) {
        Example example = new Example(OrderStrategyRelate.class);
        example.createCriteria().andEqualTo("orderScoringStrategyId", orderScoringStrategyId);
        return CollectionUtils.getFirstSafety(this.selectByExampleAndRowBounds(example, new RowBounds(0, 1)));
    }

}
