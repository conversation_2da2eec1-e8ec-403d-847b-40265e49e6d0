package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.PriorityPushRuleExpressionDto;

import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.*;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroups;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroupsRuleItem;
import com.wanshifu.master.order.push.mapper.PriorityPushRuleMapper;
import com.wanshifu.master.order.push.repository.PriorityPushRuleRepository;
import com.wanshifu.master.order.push.service.PriorityPushRuleService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class PriorityPushRuleServiceImpl implements PriorityPushRuleService {


    @Resource
    private PriorityPushRuleRepository priorityPushRuleRepository;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "优先推送规则",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "ruleName")
    public int create(CreateRqt rqt){
        String pushRuleExpression = JSON.toJSONString(getRuleExpressions(rqt.getRuleList()));
        return priorityPushRuleRepository.insert(rqt.getBusinessLineId(),rqt.getRuleName(),rqt.getRuleDesc(),rqt.getCategoryIds(),rqt.getCityIds(), JSON.toJSONString(rqt.getRuleList()),pushRuleExpression,rqt.getCreateAccountId());
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "优先推送规则",
            tableName = "priority_push_rule", mapperClass = PriorityPushRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "priorityPushRuleMapper", primaryKeyFieldName = "ruleId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "ruleName")
    public int update(UpdateRqt rqt){
        String pushRuleExpression = JSON.toJSONString(getRuleExpressions(rqt.getRuleList()));
        return priorityPushRuleRepository.update(rqt.getRuleId(),rqt.getBusinessLineId(),rqt.getRuleName(),rqt.getRuleDesc(),rqt.getCategoryIds(),rqt.getCityIds(), JSON.toJSONString(rqt.getRuleList()),pushRuleExpression,rqt.getUpdateAccountId());
    }

    @Override
    public PriorityPushRule detail(DetailRqt rqt){
        return priorityPushRuleRepository.selectByPrimaryKey(rqt.getRuleId());
    }


    @Override
    public SimplePageInfo<PriorityPushRule> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        String ruleName = rqt.getRuleName();
        Long cityId = rqt.getCityId();
        String categoryIds = rqt.getCategoryIds();
        Date createStartTime = rqt.getCreateStartTime();
        Date createEndTime = rqt.getCreateEndTime();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<PriorityPushRule> baseSelectStrategyList = priorityPushRuleRepository.selectList(rqt.getBusinessLineId(),ruleName,cityId, categoryIds,createStartTime, createEndTime);

        SimplePageInfo<PriorityPushRule> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(baseSelectStrategyList);
        return listRespSimplePageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "优先推送规则",
            tableName = "priority_push_rule", mapperClass = PriorityPushRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "priorityPushRuleMapper", primaryKeyFieldName = "ruleId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "ruleName")
    public Integer enable(EnableRqt rqt){
        Integer ruleId = rqt.getRuleId();
        //1:启用 0:禁用
        Integer ruleStatus = rqt.getRuleStatus();
//        if (ruleStatus == 0) {
//            PriorityPushRule priorityPushRule = priorityPushRuleRepository.selectByPrimaryKey(ruleId);
//            Assert.isNull(priorityPushRule, "当前记录已被应用，不可禁用");
//        }
        return priorityPushRuleRepository.updateStatus(ruleId, ruleStatus,rqt.getUpdateAccountId());
    }


    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "优先推送规则",
            tableName = "priority_push_rule", mapperClass = PriorityPushRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "priorityPushRuleMapper", primaryKeyFieldName = "ruleId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "ruleName")
    public Integer delete(DeleteRqt rqt){
        Integer ruleId = rqt.getRuleId();
        return priorityPushRuleRepository.softDelete(ruleId);
    }




    private List<PriorityPushRuleExpressionDto> getRuleExpressions(List<PushGroupsRuleItem> ruleList) {



        return ruleList.stream().map(rule -> {


            PriorityPushRuleExpressionDto expressionDto = new PriorityPushRuleExpressionDto();

            PushGroupsRuleItem.OpenCondition openCondition = rule.getOpenCondition();

            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user","user_group","order_label");
                        if(specialTerms.contains(s.getItemName())){
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<PushGroupsRuleItem.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","),Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","),Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","),Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }

            PushGroups pushGroups = rule.getPushGroups();
            String condition = pushGroups.getCondition();
            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(PushGroupsRuleItem.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));

            List<String> pushGroupsParamList = new ArrayList<>(Collections.emptyList());
            List<String> pushGroupsExpressions = rule.getPushGroups().getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                pushGroupsParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(),null);

                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());


            String pushGroupsExpression = QlExpressUtil.transitionQlExpressStr("and", pushGroupsExpressions);

            String pushGroupsParams = pushGroupsParamList.stream().distinct().collect(Collectors.joining(","));

            expressionDto.setPushRuleExpression(pushGroupsExpression);
            expressionDto.setPushRuleParams(pushGroupsParams);

            return new PriorityPushRuleExpressionDto( openConditionRuleExpression, openConditionRuleParams, pushGroupsExpression,pushGroupsParams);
        }).collect(Collectors.toList());
    }


//    private PushGroupsExpressionDto getPushGroupsExpression(CreateRqt createRqt) {
//
//        PushGroupsExpressionDto expressionDto = new PushGroupsExpressionDto();
//
//
//
//        if(CollectionUtils.isNotEmpty(createRqt.getPushGroups().getItemList())){
//            List<String> pushGroupsParamList = new ArrayList<>(Collections.emptyList());
//            List<String> pushGroupsExpressions = createRqt.getPushGroups().getItemList().stream().map(it -> {
//                String itemName = it.getItemName();
//                //指标的开启条件
//                String masterQuotaOpenConditionRuleExpression = "";
//                pushGroupsParamList.add(it.getItemName());
//                String term;
//                //师傅人群
//                if ("master_group".equals(it.getItemName())) {
//                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
//                } else {
//                    term = it.getTerm();
//                }
//
//                //单个指标本身的表达式
//                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(),null);
//
//                return masterQuotaFilterRuleExpression;
//            }).collect(Collectors.toList());
//
//
//            String pushGroupsExpression = QlExpressUtil.transitionQlExpressStr("and", pushGroupsExpressions);
//
//            String pushGroupsParams = pushGroupsParamList.stream().distinct().collect(Collectors.joining(","));
//
//            expressionDto.setPushGroupsParamsExpression(pushGroupsExpression);
//            expressionDto.setPushGroupsParams(pushGroupsParams);
//        }
//
//
//
//        return expressionDto;
//
//    }


}
