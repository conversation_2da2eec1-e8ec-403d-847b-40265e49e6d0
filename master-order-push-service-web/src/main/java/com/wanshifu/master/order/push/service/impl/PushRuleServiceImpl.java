package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushRule.*;
import com.wanshifu.master.order.push.mapper.PushRuleMapper;
import com.wanshifu.master.order.push.repository.PushRuleRepository;
import com.wanshifu.master.order.push.service.PushRuleService;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PushRuleServiceImpl implements PushRuleService {

    @Resource
    private PushRuleRepository pushRuleRepository;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "推送规则管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "ruleName")
    public int create(CreateRqt rqt){
        String ruleName = rqt.getRuleName();
        String ruleDesc = rqt.getRuleDesc();
        Integer businessLineId = rqt.getBusinessLineId();
        checkRuleName(ruleName,null);
        checkPushRuleList(rqt.getPushRuleList());
        String pushRuleList = JSON.toJSONString(rqt.getPushRuleList());
        return pushRuleRepository.insertPushRule(businessLineId,ruleName,ruleDesc,pushRuleList,rqt.getCreateAccountId());
    }


    private void checkRuleName(String ruleName, Integer ruleId) {
        PushRule pushRule = pushRuleRepository.selectByRuleName(ruleName, ruleId);
        Assert.isNull(pushRule, "已存在相同规则名称!");
    }

    private void checkPushRuleList(List<CreateRqt.PushRuleEntity> pushRuleList) {
        //报价实验编号
        Integer offerTestId = null;
        //一口价实验编号
        Integer definiteTestId = null;
        //一口价实验编号
        Integer preTestId = null;

        //报价招标
        List<CreateRqt.PushRuleEntity> offerList = pushRuleList.stream().filter(pushRuleEntity -> pushRuleEntity.getAppointType() == 2).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(offerList)) {
            //对照组
            List<CreateRqt.PushRuleEntity> abOriginList = offerList.stream().filter(pushRuleEntity -> pushRuleEntity.getTestFlag() == 1).collect(Collectors.toList());
            //实验组
            List<CreateRqt.PushRuleEntity> abTestList = offerList.stream().filter(pushRuleEntity -> Objects.isNull(pushRuleEntity.getTestFlag()) || pushRuleEntity.getTestFlag() == 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(abOriginList)) {
                //配置了ABTest
                if (abOriginList.size() > 1) {
                    throw new IllegalArgumentException("相同下单模式不能同时开启ab实验！");
                }
                if (offerList.size() < 2) {
                    throw new IllegalArgumentException("报价下单模式，当开启了实验，必须包含2个及以上的配置内容！");
                }
                //默认开启实验的为对照组
                CreateRqt.PushRuleEntity originEntity = abOriginList.get(0);

                offerTestId = originEntity.getTestId();
                if (Objects.isNull(offerTestId)
                        || Objects.isNull(originEntity.getTestGroupId())
                        || Strings.isNullOrEmpty(originEntity.getTestGroupName())) {
                    throw new IllegalArgumentException("报价开启实验，对照组实验编号或实验组id或实验组别名称为空！");
                }
                for (CreateRqt.PushRuleEntity entity : abTestList) {
                    if (Objects.isNull(entity.getTestGroupId()) || Strings.isNullOrEmpty(entity.getTestGroupName())) {
                        throw new IllegalArgumentException("报价开启实验，实验组实验组id或实验组别名称为空！");
                    }
                }
                Set<Integer> testGroupIds = offerList.stream().map(CreateRqt.PushRuleEntity::getTestGroupId).collect(Collectors.toSet());
                if (testGroupIds.size() < offerList.size()) {
                    throw new IllegalArgumentException("同一个推单规则中,报价下单模式下，组别名称不可重复！");
                }
            } else {
                if (offerList.size() > 1) {
                    throw new IllegalArgumentException("报价下单模式，未开启实验，只允许单个配置内容！");
                }
            }

        }


        //一口价
        List<CreateRqt.PushRuleEntity> definiteList = pushRuleList.stream().filter(pushRuleEntity -> pushRuleEntity.getAppointType() == 4).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(definiteList)) {
            //对照组
            List<CreateRqt.PushRuleEntity> abOriginList = definiteList.stream().filter(pushRuleEntity -> pushRuleEntity.getTestFlag() == 1).collect(Collectors.toList());
            //实验组
            List<CreateRqt.PushRuleEntity> abTestList = definiteList.stream().filter(pushRuleEntity -> Objects.isNull(pushRuleEntity.getTestFlag()) || pushRuleEntity.getTestFlag() == 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(abOriginList)) {
                //配置了ABTest
                if (abOriginList.size() > 1) {
                    throw new IllegalArgumentException("相同下单模式不能同时开启ab实验！");
                }
                if (definiteList.size() < 2) {
                    throw new IllegalArgumentException("一口价下单模式，当开启了实验，必须包含2个及以上的配置内容！");
                }
                //默认开启实验的为对照组
                CreateRqt.PushRuleEntity originEntity = abOriginList.get(0);

                definiteTestId = originEntity.getTestId();
                if (Objects.isNull(definiteTestId)
                        || Objects.isNull(originEntity.getTestGroupId())
                        || Strings.isNullOrEmpty(originEntity.getTestGroupName())) {
                    throw new IllegalArgumentException("一口价开启实验，对照组实验编号或实验组id或实验组别名称为空！");
                }
                for (CreateRqt.PushRuleEntity entity : abTestList) {
                    if (Objects.isNull(entity.getTestGroupId()) || Strings.isNullOrEmpty(entity.getTestGroupName())) {
                        throw new IllegalArgumentException("一口价开启实验，实验组实验组id或实验组别名称为空！");
                    }
                }
                Set<Integer> testGroupIds = definiteList.stream().map(CreateRqt.PushRuleEntity::getTestGroupId).collect(Collectors.toSet());
                if (testGroupIds.size() < definiteList.size()) {
                    throw new IllegalArgumentException("同一个推单规则中,一口价下单模式下，组别名称不可重复！");
                }
            } else {
                if (definiteList.size() > 1) {
                    throw new IllegalArgumentException("一口价下单模式，未开启实验，只允许单个配置内容！");
                }
            }

        }


        //预付款
        List<CreateRqt.PushRuleEntity> preList = pushRuleList.stream().filter(pushRuleEntity -> pushRuleEntity.getAppointType() == 5).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(preList)) {
            //对照组
            List<CreateRqt.PushRuleEntity> abOriginList = preList.stream().filter(pushRuleEntity -> pushRuleEntity.getTestFlag() == 1).collect(Collectors.toList());
            //实验组
            List<CreateRqt.PushRuleEntity> abTestList = preList.stream().filter(pushRuleEntity -> Objects.isNull(pushRuleEntity.getTestFlag()) || pushRuleEntity.getTestFlag() == 0).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(abOriginList)) {
                //配置了ABTest
                if (abOriginList.size() > 1) {
                    throw new IllegalArgumentException("相同下单模式不能同时开启ab实验！");
                }
                if (preList.size() < 2) {
                    throw new IllegalArgumentException("预付款下单模式，当开启了实验，必须包含2个及以上的配置内容！");
                }
                //默认开启实验的为对照组
                CreateRqt.PushRuleEntity originEntity = abOriginList.get(0);

                preTestId = originEntity.getTestId();
                if (Objects.isNull(preTestId)
                        || Objects.isNull(originEntity.getTestGroupId())
                        || Strings.isNullOrEmpty(originEntity.getTestGroupName())) {
                    throw new IllegalArgumentException("预付款开启实验，对照组实验编号或实验组id或实验组别名称为空！");
                }
                for (CreateRqt.PushRuleEntity entity : abTestList) {
                    if (Objects.isNull(entity.getTestGroupId()) || Strings.isNullOrEmpty(entity.getTestGroupName())) {
                        throw new IllegalArgumentException("预付款开启实验，实验组实验组id或实验组别名称为空！");
                    }
                }
                Set<Integer> testGroupIds = preList.stream().map(CreateRqt.PushRuleEntity::getTestGroupId).collect(Collectors.toSet());
                if (testGroupIds.size() < preList.size()) {
                    throw new IllegalArgumentException("同一个推单规则中,报价下单模式下，组别名称不可重复！");
                }
            } else {
                if (preList.size() > 1) {
                    throw new IllegalArgumentException("预付款下单模式，未开启实验，只允许单个配置内容！");
                }
            }

        }


        Set<Integer> testIds = new HashSet<>();
        if (Objects.nonNull(offerTestId)) {
            testIds.add(offerTestId);
        }
        if (Objects.nonNull(definiteTestId)) {
            testIds.add(offerTestId);
        }
        if (Objects.nonNull(preTestId)) {
            testIds.add(offerTestId);
        }
        if (testIds.size() > 1) {
            throw new IllegalArgumentException("推单规则不允许配置多个不同大数据的实验编号,大数据暂不支持单个城市按交易模式进行实验！");
        }
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "推送规则管理",
            tableName = "push_rule", mapperClass = PushRuleMapper.class,
            mapperBeanName = "pushRuleMapper", primaryKeyFieldName = "ruleId",
            updateAccountIdFieldName = "updateAccountId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "ruleName")
    public int update(UpdateRqt rqt){
        Integer ruleId = rqt.getRuleId();
        String ruleName = rqt.getRuleName();
        Integer businessLineId = rqt.getBusinessLineId();
        String ruleDesc = rqt.getRuleDesc();
        String pushRuleList = JSON.toJSONString(rqt.getPushRuleList());
        this.checkRuleName(ruleName,ruleId);
        checkPushRuleList(rqt.getPushRuleList());
        return pushRuleRepository.updatePushRule(ruleId,businessLineId,ruleName,ruleDesc,pushRuleList,rqt.getUpdateAccountId());
    }

    @Override
    public PushRule detail(DetailRqt rqt){
        return pushRuleRepository.selectByPrimaryKey(rqt.getRuleId());
    }


    @Override
    public SimplePageInfo<PushRule> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        String ruleName = rqt.getRuleName();
        Date createStartTime = rqt.getCreateStartTime();
        Date createEndTime = rqt.getCreateEndTime();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<PushRule> pushRuleList = pushRuleRepository.selectList(rqt.getBusinessLineId(),ruleName, createStartTime, createEndTime);

        SimplePageInfo<PushRule> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(pushRuleList);
        return listRespSimplePageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "推送规则管理",
            tableName = "push_rule", mapperClass = PushRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "pushRuleMapper", primaryKeyFieldName = "ruleId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "ruleName")
    public Integer delete(DeleteRqt rqt){
        return pushRuleRepository.softDelete(rqt.getRuleId());
    }

}
