package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.rqt.pushRule.CreateRqt;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Data
public class DynamicRoundsMaster {


    private String rounds;

    private BigDecimal pushScoreStartValue;

    private BigDecimal pushScoreEndValue;

    private BigDecimal offerRate;

    private int masterNum;

    private Integer batchNum;

    private Integer deliveryPercent;

    private Integer isFirstDynamicRoundsPush = 0;

    private List<CreateRqt.WheelRoundRule> wheelRoundRuleList;
    
    private List<PushMaster> masterList = new ArrayList<>();

    private List<PushMaster> alternativeMasterList = new ArrayList<>();


}
