package com.wanshifu.master.order.push.repository;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.AutoReceiveMaster;
import com.wanshifu.master.order.push.domain.dto.ScorerMaster;
import com.wanshifu.master.order.push.domain.po.MasterAutoReceiveOrder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class MasterAutoReceiveOrderRepository extends BaseRepository<MasterAutoReceiveOrder> {


    @Async
    public int insertAutoReceiveOrder(Long orderId, List<ScorerMaster> scoreMasterList, List<AutoReceiveMaster> waitAutoReceiveMasterList,
                                      String autoReceiveMasterList){
        MasterAutoReceiveOrder masterAutoReceiveOrder = new MasterAutoReceiveOrder();
        masterAutoReceiveOrder.setOrderId(orderId);
        if(CollectionUtils.isNotEmpty(scoreMasterList)){
            masterAutoReceiveOrder.setScoreMasterList(JSON.toJSONString(scoreMasterList));
        }
        if(CollectionUtils.isNotEmpty(waitAutoReceiveMasterList)){
            masterAutoReceiveOrder.setWaitAutoReceiveMasterList(JSON.toJSONString(waitAutoReceiveMasterList));
        }
        masterAutoReceiveOrder.setAutoReceiveMasterList(autoReceiveMasterList);
        return this.insertSelective(masterAutoReceiveOrder);
    }


}