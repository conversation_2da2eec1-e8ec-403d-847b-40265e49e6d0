package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.MasterPush;
import com.wanshifu.master.order.push.domain.po.OrderMasterPush;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface OrderMasterPushMapper extends IBaseCommMapper<OrderMasterPush> {


    List<OrderMasterPush> getOrderMasterPushList(@Param("orderId") Long orderId, @Param("orderVersion") String orderVersion, @Param("timeMark") String timeMark,
                                                 @Param("roundsSet") Set<String> roundsSet, @Param("isAlternative")Integer isAlternative);


    int insertOrderMasterPushList(@Param("orderMasterPushList")List<OrderMasterPush> orderMasterPushList,@Param("timeMark")String timeMark);

    int createOrderMasterPushTable(@Param("tableName") String tableName);


    int dropHistoryOrderMasterPushTable(@Param("tableName") String tableName);


    List<OrderMasterPush> selectPushScore(@Param("orderId")Long orderId,@Param("orderVersion")String orderVersion,@Param("masterIdList")List<Long> masterIdList,@Param("timeMark")String timeMark);




}