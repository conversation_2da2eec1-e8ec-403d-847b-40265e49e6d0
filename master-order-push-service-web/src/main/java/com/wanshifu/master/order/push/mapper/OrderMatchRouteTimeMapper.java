package com.wanshifu.master.order.push.mapper;


import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMatchRouteTimeMapper extends IBaseCommMapper<OrderMatchRouteTime> {


    List<OrderMatchRouteTime> selectList(OrderMatchRouteTimeListRqt rqt);


    List<OrderMatchRouteTime>  selectByCategoryIdAndAppointType(@Param("businessLineId") Integer businessLineId,@Param("categoryId") Long categoryId,@Param("appointType") Integer appointType);





    }
