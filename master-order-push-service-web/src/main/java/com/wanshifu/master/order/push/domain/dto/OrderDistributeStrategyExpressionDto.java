package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

@Data
public class OrderDistributeStrategyExpressionDto {

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;

    /**
     * 筛选策略id
     */
    private Integer orderSelectStrategyId;

    /**
     * 评分策略id
     */
    private Integer orderScoringStrategyId;


    private String dispatchType;


    /**
     * 筛选策略id
     */
    private Integer directAppointSelectStrategyId;

    /**
     * 评分策略id
     */
    private Integer directAppointScoringStrategyId;

    /**
     * 分配规则
     */
    private String distributeRule;

    /**
     * 合作经营调度是否服务区域兜底
     * 1：是
     * 0：否
     */
    private Integer cooperationBusinessServeDivisionAtLast;

}
