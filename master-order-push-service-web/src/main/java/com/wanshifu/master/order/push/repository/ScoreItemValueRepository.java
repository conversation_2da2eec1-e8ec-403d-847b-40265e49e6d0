package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

@Repository
public class ScoreItemValueRepository extends BaseRepository<ScoreItemValue> {


    public List<ScoreItemValue> selectByScoreItemIds(List<Long> scoreItemIds) {
        if(CollectionUtils.isEmpty(scoreItemIds)){
            return Collections.emptyList();
        }
        Example example = new Example(ScoreItemValue.class);
        example.createCriteria().andIn("scoreItemId",scoreItemIds);
        return this.selectByExample(example);
    }
}