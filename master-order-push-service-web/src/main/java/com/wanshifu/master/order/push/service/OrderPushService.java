package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.message.LongTailBatchPushMessage;
import com.wanshifu.master.order.push.domain.rqt.*;

/**
 * 推单Service
 * <AUTHOR>
 */
public interface OrderPushService {

    /**
     * 订单推单
     * @param orderPushRqt
     * @return
     */
    int orderPush(OrderPushRqt orderPushRqt);

    /**
     * 推单重推
     * @param repushOrderRqt
     * @return
     */
    int repushOrder(RepushOrderRqt repushOrderRqt);

    /**
     * 重推同步（修改订单）
     * @param globalOrderId
     * @return
     */
    Integer repushSync(Long globalOrderId);


    /**
     * 附近更多推单
     * @param globalOrderId
     * @return
     */
//    int nearbyPush(NearbyPushRqt rqt);


    /**
     * 总包自动邀约推单
     *
     * @param inviteOrderPushRqt
     * @return
     */
    int inviteOrderPush(InviteOrderPushRqt inviteOrderPushRqt);


    Integer dropHistoryPushTable();

    Integer push(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition);


    Integer batchOrderMatch(BatchOrderMatchRqt batchOrderMatchRqt);

    int afreshPush(OrderAfreshPushRqt orderAfreshPushRqt);


    /**
     * 家庭协议推单多少分钟后推普通师傅
     * @param familyAgreementRePushNormalRqt
     */
    void familyAgreementRePushNormal(FamilyAgreementRePushNormalRqt familyAgreementRePushNormalRqt);



}
