package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.GetOrderScoringStrategyListRqt;
import com.wanshifu.master.order.push.mapper.OrderScoringStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 16:16
 */
@Repository
public class OrderScoringStrategyRepository extends BaseRepository<OrderScoringStrategy> {

    @Resource
    private OrderScoringStrategyMapper orderScoringStrategyMapper;

    public List<OrderScoringStrategy> selectList(GetOrderScoringStrategyListRqt rqt){
        return orderScoringStrategyMapper.selectList(rqt);
    }

    public List<OrderScoringStrategy> selectByStrategyIdList(List<Integer> strategyIdList) {
        Condition condition = new Condition(OrderScoringStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andIn("strategyId", strategyIdList);
        return this.selectByCondition(condition);
    }

    public List<OrderScoringStrategy> selectAvailableStrategyByIdList(List<Integer> strategyIdList) {
        Condition condition = new Condition(OrderScoringStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andIn("strategyId", strategyIdList);
        criteria.andEqualTo("strategyStatus",1);
        criteria.andEqualTo("isDelete",0);
        return this.selectByCondition(condition);
    }


    public OrderScoringStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Long businessLineId, Integer strategyId) {
        Example example = new Example(OrderScoringStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public int insert(Long businessLineId,String strategyName,String masterResources,String strategyDesc,String categoryIds,
                      String scoringStrategy,String scoringStrategyExpression,Long createAccountId){
        OrderScoringStrategy orderScoringStrategy = new OrderScoringStrategy();
        orderScoringStrategy.setBusinessLineId(businessLineId);
        orderScoringStrategy.setStrategyName(strategyName);
        orderScoringStrategy.setStrategyDesc(strategyDesc);
        orderScoringStrategy.setCategoryIds(categoryIds);
        orderScoringStrategy.setMasterResources(masterResources);
        orderScoringStrategy.setScoringStrategy(scoringStrategy);
        orderScoringStrategy.setScoringStrategyExpression(scoringStrategyExpression);
        orderScoringStrategy.setCreateAccountId(createAccountId);
        orderScoringStrategy.setUpdateAccountId(createAccountId);
        return orderScoringStrategyMapper.insertSelective(orderScoringStrategy);
    }


    public int update(Integer strategyId,Long businessLineId,String strategyName,String masterResources,String strategyDesc,String categoryIds,
                      String scoringStrategy,String scoringStrategyExpression,Long updateAccountId){
        OrderScoringStrategy orderScoringStrategy = new OrderScoringStrategy();
        orderScoringStrategy.setStrategyId(strategyId);
        orderScoringStrategy.setBusinessLineId(businessLineId);
        orderScoringStrategy.setStrategyName(strategyName);
        orderScoringStrategy.setMasterResources(masterResources);
        orderScoringStrategy.setStrategyDesc(strategyDesc);
        orderScoringStrategy.setCategoryIds(categoryIds);
        orderScoringStrategy.setScoringStrategy(scoringStrategy);
        orderScoringStrategy.setScoringStrategyExpression(scoringStrategyExpression);
        orderScoringStrategy.setUpdateAccountId(updateAccountId);
        return orderScoringStrategyMapper.updateByPrimaryKeySelective(orderScoringStrategy);
    }


}
