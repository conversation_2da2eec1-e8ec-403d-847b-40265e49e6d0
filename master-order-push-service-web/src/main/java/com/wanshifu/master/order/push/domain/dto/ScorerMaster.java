package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class ScorerMaster implements Comparable<ScorerMaster>{
    private String masterId;
    private BigDecimal score=BigDecimal.ZERO;

    public String getMasterId() {
        return masterId;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void addScore(BigDecimal scoreChange) {
        this.score = this.score.add(scoreChange);
    }

    @Override
    public int compareTo(ScorerMaster o) {
        return o.score.compareTo(this.score);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((masterId == null) ? 0 : masterId.hashCode());
        return result;
    }

    public static final class ScorerMasterBuilder {
        private String masterId;
        private BigDecimal score=BigDecimal.ZERO;

        private ScorerMasterBuilder() {
        }

        public static ScorerMasterBuilder aScorerMaster() {
            return new ScorerMasterBuilder();
        }

        public ScorerMasterBuilder withMasterId(String masterId) {
            this.masterId = masterId;
            return this;
        }

        public ScorerMasterBuilder withScore(BigDecimal score) {
            this.score = score;
            return this;
        }

        public ScorerMaster build() {
            ScorerMaster scorerMaster = new ScorerMaster();
            scorerMaster.masterId = this.masterId;
            scorerMaster.score = this.score;
            return scorerMaster;
        }
    }
}
