package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.api.OrderMessageApi;
import com.wanshifu.master.order.push.domain.dto.ExclusiveOrderLabel;
import com.wanshifu.master.order.push.domain.dto.OrderMatchRouteMessage;
import com.wanshifu.master.order.push.domain.dto.RouteMatchResult;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.resp.exclusive.GetExclusiveOrderInfoResp;

import com.wanshifu.master.order.push.domain.rqt.exclusive.GetExclusiveOrderInfoRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.mapper.OrderMatchRouteMapper;

import com.wanshifu.master.order.push.repository.OrderMatchRouteRepository;
import com.wanshifu.master.order.push.service.OrderMatchRouteService;
import com.wanshifu.master.order.push.service.PushQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class OrderMatchRouteServiceImpl implements OrderMatchRouteService {

    @Resource
    private OrderMatchRouteRepository orderMatchRouteRepository;

    @Resource
    private OrderMatchRouteMatcher orderMatchRouteMatcher;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private OrderMessageApi orderMessageApi;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "订单匹配", level2MenuName = "匹配路由配置",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "routeName")
    public Integer create(CreateOrderMatchRouteRqt rqt){
        String routeName = rqt.getRouteName();
        String orderPushFlag = rqt.getOrderPushFlag();
        this.checkRouteName(routeName,null);
        this.checkOrderPushFlag(orderPushFlag,null);
        orderMatchRouteRepository.insert(rqt.getRouteName(),rqt.getRouteDesc(),rqt.getOrderPushFlag(),rqt.getOrderPriorityMatchRule(),
                JSON.toJSONString(rqt.getOrderStandbyMatchRule()),rqt.getCreateAccountId());
        return 1;
    }

    private void checkRouteName(String routeName, Integer routeId) {
        OrderMatchRoute orderMatchRoute = orderMatchRouteRepository.selectByRouteName(routeName, routeId);
        Assert.isNull(orderMatchRoute, "该业务线已存在相同路由名称!");
    }


    private void checkOrderPushFlag(String orderPushFlag,Integer routeId) {
        OrderMatchRoute orderMatchRoute = orderMatchRouteRepository.selectByOrderPushFlag(orderPushFlag, routeId);
        Assert.isNull(orderMatchRoute, "该业务线已存在相同路由名称!");
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "订单匹配", level2MenuName = "匹配路由配置",
            tableName = "order_match_route", mapperClass = OrderMatchRouteMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderMatchRouteMapper", primaryKeyFieldName = "routeId",
            configNameFieldNameFromEntity = "routeName")
    public Integer update(UpdateOrderMatchRouteRqt rqt){
        String routeName = rqt.getRouteName();
        String orderPushFlag = rqt.getOrderPushFlag();
        this.checkRouteName(routeName, rqt.getRouteId());
        this.checkOrderPushFlag(orderPushFlag,rqt.getRouteId());
        orderMatchRouteRepository.update(rqt.getRouteId(),rqt.getRouteName(),rqt.getRouteDesc(),rqt.getOrderPushFlag(),rqt.getOrderPriorityMatchRule(),
                JSON.toJSONString(rqt.getOrderStandbyMatchRule()),rqt.getCreateAccountId());
        return 1;
    }

    @Override
    public OrderMatchRoute detail(OrderMatchRouteDetailRqt rqt){
        return orderMatchRouteRepository.selectByPrimaryKey(rqt.getRouteId());
    }

    @Override
    public SimplePageInfo<OrderMatchRoute> list(OrderMatchRouteListRqt rqt){
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<OrderMatchRoute> orderMatchRouteList = orderMatchRouteRepository.selectList(rqt);
        SimplePageInfo<OrderMatchRoute> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(orderMatchRouteList);
        return listRespSimplePageInfo;
    }


    @Override
    public void orderMatchRoute(String orderPushFlag,Long orderId,Integer businessLineId,Long categoryId,Integer appointType){
        final String pushFlag = orderPushFlag;
        RouteMatchResult routeMatchResult = orderMatchRouteMatcher.match(orderPushFlag,businessLineId,categoryId,appointType);
        if(Objects.nonNull(routeMatchResult)){
            List<CreateOrderMatchRouteRqt.MatchRule> matchRuleList = JSON.parseArray(routeMatchResult.getOrderStandbyMatchRule(),CreateOrderMatchRouteRqt.MatchRule.class);
            matchRuleList.forEach(matchRule -> {
                CreateOrderMatchRouteRqt.MatchRuleItem matchRuleItem = matchRule.getItemList().get(0);
                String itemName = matchRuleItem.getItemName();
                if("order_receive".equals(itemName)){
                    OrderMatchRouteTime orderReceiveRouteTime = routeMatchResult.getOrderMatchRouteTimeList().stream().filter(orderMatchRouteTime -> "receiveTime".equals(orderMatchRouteTime.getSettingType())).findFirst().orElse(null);
                    if(Objects.nonNull(orderReceiveRouteTime)){
                        OrderMatchRouteMessage orderMatchRouteMessage = new OrderMatchRouteMessage();
                        orderMatchRouteMessage.setMasterOrderId(orderId);
                        orderMatchRouteMessage.setOrderPriorityMatchRule(routeMatchResult.getOrderPriorityMatchRule());
                        orderMatchRouteMessage.setOrderMatchRouteTime(orderReceiveRouteTime);
                        orderMatchRouteMessage.setOrderPushFlag(pushFlag);
                        log.info("orderMatchRouteMessage:" + JSON.toJSONString(orderMatchRouteMessage));
                        pushQueueService.sendOrderMatchRouteMessage(orderMatchRouteMessage,orderReceiveRouteTime.getSettingTime() > 0 ? orderReceiveRouteTime.getSettingTime() * 60 * 1000L : 1000L);
                    }

                }if("order_appoint".equals(itemName)){
                    OrderMatchRouteTime orderReceiveAppointTime = routeMatchResult.getOrderMatchRouteTimeList().stream().filter(orderMatchRouteTime -> "appointTime".equals(orderMatchRouteTime.getSettingType())).findFirst().orElse(null);
                    if(Objects.nonNull(orderReceiveAppointTime)){
                        OrderMatchRouteMessage orderMatchRouteMessage = new OrderMatchRouteMessage();
                        orderMatchRouteMessage.setMasterOrderId(orderId);
                        orderMatchRouteMessage.setOrderPriorityMatchRule(routeMatchResult.getOrderPriorityMatchRule());
                        orderMatchRouteMessage.setOrderMatchRouteTime(orderReceiveAppointTime);
                        orderMatchRouteMessage.setOrderPushFlag(pushFlag);
                        log.info("orderMatchRouteMessage:" + JSON.toJSONString(orderMatchRouteMessage));
                        pushQueueService.sendOrderMatchRouteMessage(orderMatchRouteMessage,orderReceiveAppointTime.getSettingTime() > 0 ? orderReceiveAppointTime.getSettingTime() * 60 * 1000L : 1000L);
                    }
                }

            });


        }
    }


    @Override
    public ExclusiveOrderLabel getExclusiveOrderLabel(Long masterId, String pushMode, String pushModeType, Long globalOrderId, Long recruitId, boolean hasPrice){
        GetExclusiveOrderInfoRqt getExclusiveOrderInfoRqt = new GetExclusiveOrderInfoRqt();
        getExclusiveOrderInfoRqt.setMasterId(masterId);
        getExclusiveOrderInfoRqt.setGlobalOrderTraceId(globalOrderId);
        getExclusiveOrderInfoRqt.setPushMode(pushMode);
        getExclusiveOrderInfoRqt.setPushModeType(pushModeType);
        getExclusiveOrderInfoRqt.setRecruitId(recruitId);
        getExclusiveOrderInfoRqt.setHasPrice(hasPrice);
        GetExclusiveOrderInfoResp getExclusiveOrderInfoResp = orderMessageApi.getExclusiveOrderInfo(getExclusiveOrderInfoRqt);
        if(Objects.nonNull(getExclusiveOrderInfoResp)){
            ExclusiveOrderLabel orderLabel = new ExclusiveOrderLabel();
            orderLabel.setExclusiveFlag(getExclusiveOrderInfoResp.getExclusiveFlag());
            orderLabel.setRecruitTagName(getExclusiveOrderInfoResp.getRecruitTagName());
            return orderLabel;
        }

        return null;
    }


}
