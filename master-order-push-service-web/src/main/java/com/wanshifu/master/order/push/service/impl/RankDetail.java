package com.wanshifu.master.order.push.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.util.LoCollectionsUtil;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class RankDetail extends OrderDetail {
    private Set<String> masterSet;

    private JSONObject rankDetailJSON;
    private JSONObject filterDetailJSON;
    private JSONObject scorerDetailJSON;
    private Set<String> filterMasterSet;

    private RankDetail() {
    }

    private void setFilterDetailJSON(JSONObject filterDetailJSON) {
        this.filterDetailJSON = filterDetailJSON;
    }

    private void setScorerDetailJSON(JSONObject scorerDetailJSON) {
        this.scorerDetailJSON = scorerDetailJSON;
    }

    private void setRankDetailJSON(JSONObject rankDetailJSON) {
        this.rankDetailJSON = rankDetailJSON;
    }

    public Set<String> getFilterMasterSet(){
        return filterMasterSet;
    }

    public void addFilterDetail(String filterId, String ruleName,String master){
        if (filterDetailJSON!=null) {
            if(StringUtils.isNotBlank(ruleName)){
                LoCollectionsUtil.putJsonArray(filterDetailJSON,ruleName,master);
            }else{
                LoCollectionsUtil.putJsonArray(filterDetailJSON,filterId,master);
            }
            filterMasterSet.add(master);
        }
    }

    public void addScorerDetail(String scorerId, String master, BigDecimal score){
        if (scorerDetailJSON!=null) {
            LoCollectionsUtil.putJsonArray(scorerDetailJSON,scorerId,master+":"+score);
        }
    }
    /**
     * 获取订单特征数据
     *
     * @return
     */
    @Override
    public String getDetailInfo() {
        return rankDetailJSON.toJSONString();
    }

    public static final class RankDetailBuilder {
        private String orderId;
        private String type;
        private Set<String> masterSet;

        private RankDetailBuilder() {
        }

        public static RankDetailBuilder aRankDetail() {
            return new RankDetailBuilder();
        }

        public RankDetailBuilder withOrderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public RankDetailBuilder withType(String type) {
            this.type = type;
            return this;
        }

        public RankDetailBuilder withMasterSet(Set<String> masterSet) {
            this.masterSet = new HashSet<>(masterSet);
            return this;
        }

        public RankDetail build() {
            RankDetail rankDetail = new RankDetail();
            rankDetail.setOrderId(orderId);
            rankDetail.setType(type);
            rankDetail.masterSet = this.masterSet;
            rankDetail.filterMasterSet = new HashSet<>();
            final JSONObject details = new JSONObject();
            final JSONObject filterDetails = new JSONObject();
            final JSONObject scorerDetails = new JSONObject();
            details.put("filterDetails",filterDetails);
            details.put("scorerDetails",scorerDetails);
            rankDetail.setFilterDetailJSON(filterDetails);
            rankDetail.setScorerDetailJSON(scorerDetails);
            rankDetail.setRankDetailJSON(details);
            return rankDetail;
        }
    }
}
