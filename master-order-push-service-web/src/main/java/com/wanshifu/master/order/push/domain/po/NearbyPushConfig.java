package com.wanshifu.master.order.push.domain.po;


import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 附近推送配置表
 * <AUTHOR>
 */
@Data
@Table(name = "nearby_push_config")
public class NearbyPushConfig {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 类目id
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 城市id
     */
    @Column(name = "second_division_id")
    private Long secondDivisionId;

    /**
     * 规则配置
     */
    @Column(name = "rule_config")
    private String ruleConfig;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
