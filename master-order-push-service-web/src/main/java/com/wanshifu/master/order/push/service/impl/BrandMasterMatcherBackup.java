//package com.wanshifu.master.order.push.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alicloud.openservices.tablestore.model.BatchGetRowResponse;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.alicloud.openservices.tablestore.model.search.SearchQuery;
//import com.alicloud.openservices.tablestore.model.search.SearchRequest;
//import com.alicloud.openservices.tablestore.model.search.SearchResponse;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.wanshifu.enterprise.inner.api.api.enterprise.InfoApi;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.exclusive.api.ExclusiveMasterOtherApi;
//import com.wanshifu.master.exclusive.domains.api.request.other.GetRecruitIdsRqt;
//import com.wanshifu.master.exclusive.domains.api.response.other.GetRecruitIdsResp;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
//import com.wanshifu.master.order.push.domain.dto.ExclusiveOrderLabel;
//import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
//import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
//import com.wanshifu.master.order.push.domain.enums.PushMode;
//import com.wanshifu.master.order.push.domain.enums.RangeQueryType;
//import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
//import com.wanshifu.master.order.push.repository.PushProgressRepository;
//import com.wanshifu.master.order.push.service.*;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import com.wanshifu.order.config.domains.po.BusinessLine;
//import com.wanshifu.order.offer.domains.enums.AccountType;
//import com.wanshifu.order.offer.domains.enums.OrderFrom;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 品牌师傅匹配器
// * <AUTHOR>
// */
//@Slf4j
//@Component("brand")
//public class BrandMasterMatcher extends AbstractOrderMasterMatcher{
////
////    @Resource
////    private TableStoreClient tableStoreClient;
//
//    @Resource
//    private InfoApi enterpriseInfoApi;
//
//
//    @Resource
//    private ExclusiveMasterOtherApi exclusiveMasterOtherApi;
//
////    @Resource
////    private OTSMasterSelector otsMasterSelector;
//
//
//    @Value("${night.push.switch}")
//    private String nightPushSwitch;
//
//    @Value("${night.push.start.time}")
//    private String nightPushStartTime;
//
//    @Value("${night.push.end.time}")
//    private String nightPushEndTime;
//
//    @Resource
//    private PushControllerFacade pushControllerFacade;
//
//    @Resource
//    private PushProgressRepository pushProgressRepository;
//
//    @Resource
//    private PushQueueService pushQueueService;
//
//
//    @Resource
//    private OrderMatchRouteService orderMatchRouteRouteService;
//
//
//
//    /**
//     * 检查条件
//     * @param orderDetailData
//     * @return
//     */
//    @Override
//    protected boolean checkPreCondition(OrderDetailData orderDetailData){
//        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
//            return false;
//        }
//        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
//        if (SymbolConstant.ONE.equals(String.valueOf(emergencyOrderFlag))) {
//            return false;
//        }
//        if (orderDetailData.getBusinessLineId()!=1) {
//            return false;
//        }
//        if (orderDetailData.getAppointType()!=2) {
//            return false;
//        }
//        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
//        if (serveIdsArray==null||serveIdsArray.size()==0) {
//            return false;
//        }
//        //总包指派接口
//        if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
//            final boolean hasAppointedRecord =
//                    enterpriseInfoApi.hasAppointedRecord(orderDetailData.getGlobalOrderId());
//            if (hasAppointedRecord) {
//                return false;
//            }
//        }
//
//
//        if (!(
//                AccountType.USER.code.equals(orderDetailData.getAccountType())
//                        || AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())
//        )) {
//            return false;
//        }
//        if (orderDetailData.getUserId()==null) {
//            return false;
//        }
//
//        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getOrderFrom())) {
//            return false;
//        }
//        return true;
//    }
//
//
//
//    /**
//     * 获取业务范围
//     * @param businessLineIdString
//     * @param orderFrom
//     * @param accountType
//     * @param appointType
//     * @return
//     */
//    public String getRecruitBusiness(String businessLineIdString,
//                                     String orderFrom,String accountType,String appointType) {
//        if (StrUtil.isEmpty(accountType)) {
//            return null;
//        }
//        if (OrderFrom.IKEA.valueEn.equals(orderFrom)) {
//            return null;
//        }
//        Integer businessLineId = Integer.valueOf(businessLineIdString);
//        if (AccountType.ENTERPRISE.code.equals(accountType)) {
//            return null;
//        }
//        //家庭只有一口价
//        if (businessLineId == 2 && "4".equals(appointType) ) {
//            return "family";
//        }
//        //成品只有报价
//        if (AccountType.USER.code.equals(accountType) && businessLineId == 1 && "2".equals(appointType) ) {
//            return "finished_product";
//        }
//        return null;
//    }
//
//
//    /**
//     * 匹配师傅
//     * @param orderDetailData
//     * @param masterCondition
//     * @return
//     */
//    @Override
//    protected MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){
//
//        Long thirdDivisionId=orderDetailData.getThirdDivisionId();
//        Long fourthDivisionId=orderDetailData.getFourthDivisionId();
//
//        log.info("{},debug recruitBusiness::{},{},{},{}",
//                orderDetailData.getMasterOrderId(),
//                String.valueOf(orderDetailData.getBusinessLineId()),
//                orderDetailData.getOrderFrom(),
//                orderDetailData.getAccountType(),
//                String.valueOf(orderDetailData.getAppointType())
//        );
//        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
//        final Long userId = orderDetailData.getUserId();
//
//        final GetRecruitIdsResp getRecruitIdsResp = getRecruitIds(
//                orderDetailData.getMasterOrderId(),
//                thirdDivisionId,
//                fourthDivisionId,
//                "",
//                serveIdsArray,
//                userId
//        );
//
//        if(getRecruitIdsResp == null || getRecruitIdsResp.getRecruitId() == null){
//            return null;
//        }
//
//        Long recruitId = getRecruitIdsResp.getRecruitId();
//
//
//        log.info("{},brandOrder result:{},{},{},{},{},recruitIds:{}",
//                orderDetailData.getMasterOrderId(),
//                thirdDivisionId,
//                fourthDivisionId,
//                "",
//                serveIdsArray,
//                userId,
//                recruitId
//        );
//
//
//
//        masterCondition.setRecruitIds(String.valueOf(recruitId));
//
//        Set<String> result = searchBrandMaster(masterCondition);
//        final Integer bussinessId = masterCondition.getBusinessLineId();
//        /**
//         * 家庭业务线4级无师傅,按3级处理
//         */
//        if (result.size()==0&&"2".equals(bussinessId)&&4==masterCondition.getDivisionMatchLevel()) {
//            masterCondition.setDivisionMatchLevel(3);
//            result = searchBrandMaster(masterCondition);
//        }
//
//
//        /**
//         * 专属拉黑过滤
//         */
//        if (result.size() != 0) {
//            otsMasterSelector.exclusiveRefundBlackList(String.valueOf(orderDetailData.getOrderCategoryId()),result);
//        }
//
//        //TODO
//        if (result.size() != 0) {
//            final String orderPushExcludeMasterIds =
//                    StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
//            outerMasterFilter(result,orderPushExcludeMasterIds);
//            log.info("debug::orderPushExcludeMasterIds:{}:{}",orderDetailData.getMasterOrderId(),orderPushExcludeMasterIds);
//        }
//
//        MatchMasterResult masterResult = new MatchMasterResult(result);
//        masterResult.putExtraData(FieldConstant.RECRUIT_ID,String.valueOf(recruitId));
//        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
//        masterResult.putExtraData(FieldConstant.HAS_PRICE,getRecruitIdsResp.getHasPrice());
//
//        return masterResult;
//    }
//
//    /**
//     * 过滤外部师傅
//     */
//    private void outerMasterFilter(Set<String> result,String masterIds){
//        if (masterIds!=null) {
//            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
//            result.removeAll(outerMasterList);
//        }
//    }
//
//
//    /**
//     * 获取招募Id
//     * @param thirdDivisionId
//     * @param fourthDivisionId
//     * @param recruitBusiness
//     * @param serveIdArray
//     * @param userId
//     * @return
//     */
//    private GetRecruitIdsResp getRecruitIds(
//            Long masterOrderId,
//            Long thirdDivisionId,
//            Long fourthDivisionId,
//            String recruitBusiness,
//            List<Long> serveIdArray,
//            Long userId
//    ){
//
//        GetRecruitIdsRqt getRecruitIdsRqt = new GetRecruitIdsRqt();
//        getRecruitIdsRqt.setLv3DivisionId(thirdDivisionId);
//        getRecruitIdsRqt.setLv4DivisionId(fourthDivisionId);
//        getRecruitIdsRqt.setRecruitBusiness(recruitBusiness);
//        getRecruitIdsRqt.setServeId(serveIdArray.get(0));
//        getRecruitIdsRqt.setUserId(userId);
//        getRecruitIdsRqt.setNowTime(new Date());
//        final GetRecruitIdsResp getRecruitIdsResp = exclusiveMasterOtherApi.getRecruitIds(getRecruitIdsRqt);
//        return getRecruitIdsResp;
//    }
//
//
//    /**
//     * 匹配后续操作
//     * @param masterCondition
//     * @param matchMasterResult
//     */
//    @Override
//    protected void afterMatch(MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
//        return ;
//    }
//
//
//    /**
//     * 执行推单
//     * @param orderDetailData
//     * @param matchMasterResult
//     * @return
//     */
//    @Override
//    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
//
//        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
//            return false;
//        }
//
//
//        String orderVersion = orderDetailData.getOrderVersion();
//        Long timeStamp = Long.parseLong(orderVersion);
//
//        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), "brand_master_push");
//
//
//        log.info("nightSwitch::debug:{}{}",orderDetailData.getMasterOrderId(),nightPush());
//        if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {
//            //夜间推单
//            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
//            rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
//            rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
//            pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
//            return true;
//        }
//
//        JSONObject commonFeature = new JSONObject();
//
//        //专属订单
//        //	JSONObject commonFeature = new JSONObject();
//        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
//        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
//        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.BRAND.code);
//        commonFeature.put(FieldConstant.RECRUIT_ID,matchMasterResult.getExtraData().getInteger(FieldConstant.RECRUIT_ID));
//        commonFeature.put(FieldConstant.HAS_PRICE,
//                matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
//        commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
//        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
//        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
//        pushControllerFacade.exclusivePush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);
//
//
//        Boolean hasPrice = matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE);
//        if (hasPrice==null) {
//            hasPrice=false;
//        }
//
//
//        ExclusiveOrderLabel exclusiveOrderLabel = orderMatchRouteRouteService.getExclusiveOrderLabel(Long.valueOf(matchMasterResult.getMasterIdSet().iterator().next()),PushMode.BRAND.code,null,orderDetailData.getGlobalOrderId(),
//                matchMasterResult.getExtraData().getLong(FieldConstant.RECRUIT_ID),matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
//        if(Objects.nonNull(exclusiveOrderLabel) && org.apache.commons.lang.StringUtils.isNotBlank(exclusiveOrderLabel.getRecruitTagName())){
//            String recruitTagName = exclusiveOrderLabel.getRecruitTagName();
//            String orderPushFlag = "brand";
//            if("exclusive".equals(recruitTagName)){
//                orderPushFlag = "exclusive";
//            }else if("direct_appointment".equals(recruitTagName)){
//                orderPushFlag = "direct_appoint";
//            }else if("preferred".equals(recruitTagName)){
//                orderPushFlag = "excellent";
//            } else if("contract".equals(recruitTagName)){
//                orderPushFlag = "contract";
//            } else if("brand".equals(recruitTagName)){
//                orderPushFlag = "brand";
//            }
//            orderMatchRouteRouteService.orderMatchRoute(orderPushFlag,orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
//            return true;
//        }
//
//        //有价格-直接指派
//        if (FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&hasPrice) {
//
//        }else if(FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&!hasPrice){
//            //无价格的品牌-30分钟未指派-转推
//            exclusiveOrderTimeSchedule(orderDetailData,matchMasterResult,30);
//        }
//
//        return true;
//    }
//
//    private void exclusiveOrderTimeSchedule(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult,Integer minutes){
//
//        log.info("{},无价格-品牌师傅推送更多普通师傅转发延迟消息,getBusinessLineId:{},{},{}",orderDetailData.getMasterOrderId(),
//                orderDetailData.getBusinessLineId());
//        Integer schedulerTimeSecond=minutes*60;
//        //发消息
//        pushQueueService.sendNotAppointScheduledPushMessage(
//                schedulerTimeSecond*1000L,
//                orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),
//                matchMasterResult.getMasterIdSet().stream().map(Long::valueOf).collect(Collectors.toList()),
//                orderDetailData.getPushExtraData().getHandoffTag()
//        );
//    }
//
//    private static final String exclusiveMasterTableName = "t_exclusive_master";
//    private static final String brandMasterIndexName = "brand_master_index_v1";
//    private static final String TableName = "t_master_base";
//    private static final String indexName = "master_filter_v9";
//
//    @Value("${base.select.index:t_master_base_index}")
//    private String masterBaseIndex;
//
//
//
//    /**
//     * 搜索品牌师傅
//     * @param masterCondition
//     * @return
//     */
//    private static final String EXPECT_APPOINT_NUMBER="expect_appoint_number";
//    public Set<String> searchBrandMaster(MasterMatchCondition masterCondition) {
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList=new ArrayList<Query>();
//        setBrandQuery(mustQueryList,masterCondition);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(exclusiveMasterTableName, brandMasterIndexName, searchQuery);
//        /**
//         * 添加返回列
//         */
//        SearchRequest.ColumnsToGet columnsToGet = new SearchRequest.ColumnsToGet();
//        columnsToGet.setColumns(Arrays.asList(EXPECT_APPOINT_NUMBER));
//        searchRequest.setColumnsToGet(columnsToGet);
//
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        HashSet<String> masterIds=new HashSet<>();
//        final HashMap<String, Long> masterExpectAppointNumberMap = new HashMap<>();
//        for (Row row : rows) {
//            if (row!=null) {
//                String currentMasterId=row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString();
//                final Long expectAppointNumber = tableStoreClient.getLongValue(row, EXPECT_APPOINT_NUMBER);
//                masterExpectAppointNumberMap.put(currentMasterId,expectAppointNumber);
//                masterIds.add(currentMasterId);
//            }
//        }
//        /**
//         * 每日指派上限过滤
//         * 3级地址规则 TODO
//         */
//        brandMasterAppointFilter(masterIds,masterExpectAppointNumberMap,masterCondition.getOrderVersion());
//
//        if (masterIds.size() != 0) {
////			ocs黑名单√
////			账号冻结√
////			未入驻is_settle_status_normal√
//            List<Query> mustQuerys=new ArrayList<Query>();
//            mustQuerys.add(tableStoreClient.booleanTermQuery("is_push_restrict_normal",
//                    true));
//            mustQuerys.add(tableStoreClient.longRangeQuery("freezing_time", 0L,
//                    masterCondition.freezingRecoverTime()));
//            mustQuerys.add(tableStoreClient.booleanTermQuery("is_settle_status_normal",
//                    masterCondition.isSettleStatusNormal()));
//            masterIds = matchMasterBaseInfoBySet(masterIds, mustQuerys);
//        }
//        return masterIds;
//    }
//
//
//    /**
//     * 过滤师傅基础信息
//     * @param masterIds
//     * @param mustQueryList
//     * @return
//     */
//    private HashSet<String> matchMasterBaseInfoBySet(HashSet<String> masterIds,List<Query> mustQueryList){
//        HashSet<String> result=new HashSet<>();
//        if (mustQueryList==null||mustQueryList.size()==0) {
//            return masterIds;
//        }
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID, masterIds));
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(TableName, masterBaseIndex, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row!=null) {
//                result.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//            }
//        }
//        return result;
//    }
//
//
//    private static final String BRAND_APPOINT_NUMBER="brand_appoint_number";
//    private void brandMasterAppointFilter(Set<String> masterIds,
//                                          HashMap<String, Long> masterExpectAppointNumber,
//                                          String orderVersion){
//        final String timeDay = DateFormatterUtil.timeStampToTimeDay(Long.valueOf(orderVersion));
//        final Map<String, String> tMasterDailyFeature = getTMasterDailyFeature(masterIds, timeDay, BRAND_APPOINT_NUMBER);
//        Set<String> appointedMaxMasterSet=new HashSet<>();
//        for (String masterId : masterIds) {
//            final String brandAppointNumber = tMasterDailyFeature.get(masterId);
//            final Long brandExpectNumber = masterExpectAppointNumber.get(masterId);
//            if (brandAppointNumber!=null&&brandExpectNumber!=null) {
//                final Long brandAppointNumberLong = Long.valueOf(brandAppointNumber);
//                if (brandAppointNumberLong>=brandExpectNumber) {
//                    appointedMaxMasterSet.add(masterId);
//                }
//            }
//        }
//        masterIds.removeAll(appointedMaxMasterSet);
//    }
//
//
//    private Map<String,String> getTMasterDailyFeature(Set<String>masterList,String dateString,String columnName){
//        final HashMap<String, String> resultMap = new HashMap<>();
//        try {
//            final List<BatchGetRowResponse.RowResult> rowResults = tableStoreClient
//                    .batchQueryBySet(FieldConstant.SMC_MASTER_DATE_FEATURE
//                            , masterList
//                            ,FieldConstant.MASTER_ID,FieldConstant.DT,dateString, columnName);
//            for (BatchGetRowResponse.RowResult rowResult : rowResults) {
//                final Row row = rowResult.getRow();
//                if (row!=null) {
//                    final String masterId = row.getPrimaryKey()
//                            .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                    final String column = tableStoreClient.getValue(row, columnName);
//                    resultMap.put(masterId,column);
//                }
//            }
//        }catch (Exception e){
//        }
//        return resultMap;
//    }
//
//
//    /**
//     * 合作服务
//     * 合作街道
//     * 生效中
//     * 合作期内
//     * 招募下
//     * @param mustQueryList
//     * @param masterCondition
//     */
//    private void setBrandQuery(List<Query> mustQueryList, MasterMatchCondition masterCondition) {
//        //专属订单服务只会有一个,多个无法参加
//        final String serveIds = masterCondition.getServeIds();
//        //①合作服务
//        mustQueryList.add(tableStoreClient.stringTermQuery("select_serve_ids",serveIds));
//        //②生效中
////		mustQueryList.add(OTSUtil.stringTermQuery("exclusive_status","effective"));
//        mustQueryList.add(tableStoreClient.booleanTermQuery("is_exclusive_status_normal", true));
//        //③合作期内
//        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                "cooperation_start_time",
//                RangeQueryType.LESS_THAN_OREQUAL,
//                nowTimeStamp
//        ));
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                "cooperation_end_time",
//                RangeQueryType.GREATER_THAN_OREQUAL,
//                nowTimeStamp
//        ));
//
//        //④招募下
//        final String recruitIds = masterCondition.getRecruitIds();
//        mustQueryList.add(tableStoreClient.stringTermQuery("recruit_id",recruitIds));
//        //⑤街道
//        switch (masterCondition.getDivisionMatchLevel()) {
//            case 4:
//                mustQueryList.add(tableStoreClient.stringTermQuery("lv4_division_ids", String.valueOf(masterCondition.getFourthDivisionId())));
//                break;
//            default:
//                //never 没有区域的订单不会执行到查询
//                mustQueryList.add(tableStoreClient.stringTermQuery("lv3_division_ids", String.valueOf(masterCondition.getThirdDivisionId())));
//                break;
//        }
//
//    }
//
//
//    private boolean nightPush(){
//        if("on".equals(nightPushSwitch)){
//            return true;
//        }else{
//            return false;
//        }
//    }
//
//    public Long getSecondDayTimestamp(){
//        Calendar calendar = Calendar.getInstance();
//        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
//            calendar.add(Calendar.DATE,1);
//        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
//        }
//        calendar.set(Calendar.HOUR_OF_DAY,8);
//        calendar.set(Calendar.MINUTE,0);
//        return calendar.getTimeInMillis();
//    }
//
//
//}
