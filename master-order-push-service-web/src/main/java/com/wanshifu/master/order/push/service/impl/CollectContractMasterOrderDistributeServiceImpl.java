package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterBase;
import com.wanshifu.master.order.push.domain.dto.MasterAutoReceiverRqt;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/6 18:28
 */
@Service
public class CollectContractMasterOrderDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Resource
    private RocketMqSendService rocketMqSendService;


    /**
     * 作业帮功能合并开关
     */
    @Value("${order.orderPush.zuoyebangMergeSwitch:on}")
    private String zuoyebangMergeSwitch;

    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice){

        if(!"on".equals(zuoyebangMergeSwitch)){
            return 0;
        }

        this.collectContractMasterAutoOfferPrice(orderPushedResultNotice);
        return 1;

    }

    private void collectContractMasterAutoOfferPrice(OrderPushedResultNotice pushedResultNotices){

        OrderBase orderBase = pushedResultNotices.getOrderBaseComposite().getOrderBase();
        OrderGrab orderGrab = pushedResultNotices.getOrderBaseComposite().getOrderGrab();

        List<OrderPushedResultNotice.MasterInfo> masterInfoList = pushedResultNotices.getMasterInfoList();

        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        if(orderGrab.getAppointType().equals(AppointType.OPEN.value)){
            rqt.setAppointDetailType(AppointDetailType.AUTO_OFFER_ZUOYEBANG.getCode());
            rqt.setAppointType(AppointType.OPEN.value);
        }

        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();

        masterInfoList.forEach(masterInfo -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(masterInfo.getMasterId());
            masterPrice.setPrice(masterInfo.getAutoPrice());
            masterList.add(masterPrice);
            rqt.setMasterList(masterList);
        });
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),500L);

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeContext.register(PushMode.COLLECT_CONTRACT_MASTER.code, this);

    }
}
