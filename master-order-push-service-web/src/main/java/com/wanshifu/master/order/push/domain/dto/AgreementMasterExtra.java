package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class AgreementMasterExtra extends AgreementMaster {

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 招募id
     */
    private Long recruitId;

    /**
     * 匹配类型
     */
    private Integer type;

    /**
     * 四级地址
     */
    private Long fourthDivisionId;

    /**
     * 匹配类型：
     * 1：按四级地址匹配到了师傅
     * 2：订单有四级地址，按四级地址匹配不到师傅，再按三级地址匹配师傅
     * 3：订单无四级地址，按三级地址匹配师傅
     */
    private Integer matchType;

    /**
     * 订单和师傅距离
     */
    private Long orderDistance;


    /**
     * 合作四级地址列表
     */
    private Set<Long> fourthDivisionIdList;



    /**
     * 计价模式
     */
    private String pricingMethod;




}
