package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.OrderDistribute;
import org.springframework.stereotype.Repository;


/**
 * 基础特征Repository
 * <AUTHOR>
 */
@Repository
public class OrderDistributeRepository extends BaseRepository<OrderDistribute> {

    public Long insertOrderDistribute(Long orderId,String pushMode,String masterList){
        OrderDistribute orderDistribute = new OrderDistribute();
        orderDistribute.setOrderId(orderId);
        orderDistribute.setPushMode(pushMode);
        orderDistribute.setMasterList(masterList);
        this.insertSelective(orderDistribute);
        return orderDistribute.getId();
    }


}