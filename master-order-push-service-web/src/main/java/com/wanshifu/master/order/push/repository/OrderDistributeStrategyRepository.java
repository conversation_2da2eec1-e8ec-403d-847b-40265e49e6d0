package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.GetOrderDistributeStrategyListRqt;
import com.wanshifu.master.order.push.mapper.OrderDistributeStrategyMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;


@Repository
public class OrderDistributeStrategyRepository extends BaseRepository<OrderDistributeStrategy> {

    @Resource
    private OrderDistributeStrategyMapper orderDistributeStrategyMapper;

    public List<OrderDistributeStrategy> selectList(GetOrderDistributeStrategyListRqt rqt){
        return orderDistributeStrategyMapper.selectList(rqt);
    }

    public List<OrderDistributeStrategy> selectStrategyIds(GetOrderDistributeStrategyListRqt rqt){
        return orderDistributeStrategyMapper.selectStrategy(rqt);
    }


    public OrderDistributeStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Long businessLineId, Integer strategyId) {
        Example example = new Example(OrderDistributeStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public Integer insert(Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String distributeType,String categoryIds,String openCityMode,String cityIds,
                                             String distributeStrategy,String distributeStrategyExpression,String compensateDistributeStrategy,Long createAccountId,Long updateAccountId){
        OrderDistributeStrategy orderDistributeStrategy = new OrderDistributeStrategy();
        orderDistributeStrategy.setBusinessLineId(businessLineId);
        orderDistributeStrategy.setStrategyName(strategyName);
        orderDistributeStrategy.setOrderFrom(orderFrom);
        orderDistributeStrategy.setStrategyDesc(strategyDesc);
        orderDistributeStrategy.setDistributeType(distributeType);
        orderDistributeStrategy.setCategoryIds(categoryIds);
        orderDistributeStrategy.setOpenCityMode(openCityMode);
        orderDistributeStrategy.setCityIds(cityIds);
        orderDistributeStrategy.setDistributeStrategy(distributeStrategy);
        orderDistributeStrategy.setDistributeStrategyExpression(distributeStrategyExpression);
        orderDistributeStrategy.setCompensateDistributeStrategy(compensateDistributeStrategy);
        orderDistributeStrategy.setCreateAccountId(createAccountId);
        orderDistributeStrategy.setUpdateAccountId(updateAccountId);
        orderDistributeStrategyMapper.insertSelective(orderDistributeStrategy);
        return orderDistributeStrategy.getStrategyId();
    }


    public int update(Integer strategyId,Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String distributeType,String categoryIds,
                      String openCityMode,String cityIds,String distributeStrategy,String distributeStrategyExpression,
                      String compensateDistributeStrategy,Long updateAccountId){
        OrderDistributeStrategy orderDistributeStrategy = new OrderDistributeStrategy();
        orderDistributeStrategy.setStrategyId(strategyId);
        orderDistributeStrategy.setBusinessLineId(businessLineId);
        orderDistributeStrategy.setStrategyName(strategyName);
        orderDistributeStrategy.setOrderFrom(orderFrom);
        orderDistributeStrategy.setStrategyDesc(strategyDesc);
        orderDistributeStrategy.setDistributeType(distributeType);
        orderDistributeStrategy.setCategoryIds(categoryIds);
        orderDistributeStrategy.setOpenCityMode(openCityMode);
        orderDistributeStrategy.setCityIds(cityIds);
        orderDistributeStrategy.setDistributeStrategy(distributeStrategy);
        orderDistributeStrategy.setDistributeStrategyExpression(distributeStrategyExpression);
        orderDistributeStrategy.setCompensateDistributeStrategy(compensateDistributeStrategy);
        orderDistributeStrategy.setUpdateAccountId(updateAccountId);
        return orderDistributeStrategyMapper.updateByPrimaryKeySelective(orderDistributeStrategy);
    }


    public OrderDistributeStrategy selectByCityAndCategoryId(@Param("distributeType")String distributeType,@Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Long strategyId, @Param("businessLineId") Long businessLineId){
        return orderDistributeStrategyMapper.selectByCityAndCategoryId(distributeType,cityIdList,categoryIdList,strategyId,businessLineId);
    }



}