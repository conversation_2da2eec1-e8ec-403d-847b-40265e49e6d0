package com.wanshifu.master.order.push.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;

import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;
import com.wanshifu.master.order.push.repository.OrderDynamicRoundsPushRepository;
import com.wanshifu.master.order.push.service.OrderDynamicRoundsPushService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
public class OrderDynamicRoundsPushServiceImpl implements OrderDynamicRoundsPushService {

    @Resource
    private OrderDynamicRoundsPushRepository dynamicRoundsPushCountRepository;

    @Override
    public SimplePageInfo<OrderDynamicRoundsPush> list(ListRqt rqt){

        parseParams(rqt);

        Page page = PageHelper.startPage(rqt.getPageNum(),rqt.getPageSize());

        List<OrderDynamicRoundsPush> dynamicRoundsPushCountList = dynamicRoundsPushCountRepository.list(rqt);

        SimplePageInfo<OrderDynamicRoundsPush> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(dynamicRoundsPushCountList);
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPages(page.getPages());
        return listRespSimplePageInfo;

    }


    private void parseParams(ListRqt rqt){

        if(StringUtils.isNotBlank(rqt.getOrderFrom())){
            rqt.setOrderFromList(
                    Arrays.stream(Optional.ofNullable(rqt.getOrderFrom())
                            .orElse("0").split(",")).map(String::valueOf)
                            .collect(Collectors.toList()));
        }


        if(StringUtils.isNotBlank(rqt.getAppointType())){
            rqt.setAppointTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getAppointType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getServeTypeId())){
            rqt.setServeTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getServeTypeId())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getCategoryId())){
            rqt.setCategoryList(
                    Arrays.stream(Optional.ofNullable(rqt.getCategoryId())
                            .orElse("0").split(",")).map(Long::parseLong)
                            .collect(Collectors.toList()));
        }

    }



}
