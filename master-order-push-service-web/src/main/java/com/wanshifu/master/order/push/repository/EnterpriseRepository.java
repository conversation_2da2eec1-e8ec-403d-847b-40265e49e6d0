package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.Enterprise;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class EnterpriseRepository extends BaseRepository<Enterprise> {


}