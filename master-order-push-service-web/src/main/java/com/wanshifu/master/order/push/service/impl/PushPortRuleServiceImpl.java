package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.PushPortRule;

import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.pushPortRule.*;

import com.wanshifu.master.order.push.mapper.PushPortRuleMapper;
import com.wanshifu.master.order.push.repository.PushPortRuleRepository;
import com.wanshifu.master.order.push.service.PushPortRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 推单端口规则
 */
@Service
public class PushPortRuleServiceImpl implements PushPortRuleService {


    @Resource
    private PushPortRuleRepository pushPortRuleRepository;


    /**
     * 创建端口规则
     * @param rqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "推单设置", level2MenuName = "推单端口规则",
            createAccountIdFieldName = "createAccountId",
             configNameFieldName = "ruleName")
    public Integer create(CreateRqt rqt){
        this.checkRuleName(rqt.getRuleName(),null);
        this.checkRuleWeight(rqt);
        this.checkCityLv1ServeUniq(rqt.getOrderTag(),rqt.getCityIds(),rqt.getLv1ServeIds(),rqt.getAppointType(),null);
        return pushPortRuleRepository.insertRule(rqt.getRuleName(),rqt.getRuleDesc(),rqt.getOrderTag(),rqt.getCityIds(),rqt.getLv1ServeIds(),rqt.getAppointType(),rqt.getIntervalTime(),
                rqt.getOfferNum(),rqt.getCreateAccountId(), JSON.toJSONString(rqt.getRuleList()));
    }


    private void checkRuleWeight(CreateRqt rqt){
        if(rqt.getRuleList().size() != 2){
            return ;
        }
        Assert.isTrue(!rqt.getRuleList().get(0).getWeight().equals(rqt.getRuleList().get(1).getWeight()), "权重相同!");
    }

    /**
     * 更新端口规则
     * @param rqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "推单设置", level2MenuName = "推单端口规则",
            tableName = "push_port_rule", mapperClass = PushPortRuleMapper.class,
            mapperBeanName = "pushPortRuleMapper", primaryKeyFieldName = "ruleId",
            updateAccountIdFieldName = "updateAccountId",
            configNameFieldNameFromEntity = "ruleName")
    public Integer update(UpdateRqt rqt){
        this.checkRuleName(rqt.getRuleName(),rqt.getRuleId());
        this.checkRuleWeight(rqt);
        this.checkCityLv1ServeUniq(rqt.getOrderTag(),rqt.getCityIds(),rqt.getLv1ServeIds(),rqt.getAppointType(),rqt.getRuleId());
        return pushPortRuleRepository.updateRule(rqt.getRuleId(),rqt.getRuleName(),rqt.getRuleDesc(),rqt.getOrderTag(),rqt.getCityIds(),rqt.getLv1ServeIds(),rqt.getAppointType(),rqt.getIntervalTime(),
                rqt.getOfferNum(),rqt.getCreateAccountId(),JSON.toJSONString(rqt.getRuleList()));
    }


    private void checkCityLv1ServeUniq(String orderTag, String cityIds, String lv1ServeIds,String appointType, Integer ruleId) {
        List<String> cityIdList = Arrays.asList(cityIds.split(","));
        List<String> lv1ServeIdList = Arrays.asList(lv1ServeIds.split(","));
        List<String> appointTypeList = Arrays.stream(appointType.split(",")).distinct().collect(Collectors.toList());
        List<PushPortRule> pushPortRuleList = pushPortRuleRepository.selectByCityAndLv1ServeId(Collections.singletonList(orderTag),cityIdList, lv1ServeIdList, appointTypeList,ruleId);
        if (CollectionUtils.isNotEmpty(pushPortRuleList)) {
            Assert.isNull(true, StrUtil.format("城市及类目与配置【{}】存在重复!", pushPortRuleList.get(0).getRuleName()));
        }
    }


    /**
     * 校验规则名称
     *
     * @param ruleName
     * @param ruleId
     */
    private void checkRuleName(String ruleName,Integer ruleId) {
        PushPortRule pushPortRule = pushPortRuleRepository.selectByRuleName(ruleName,ruleId);
        Assert.isNull(pushPortRule, "已存在相同规则名称!");
    }

    /**
     * 端口规则详情
     * @param rqt
     * @return
     */
    @Override
    public PushPortRule detail(DetailRqt rqt){
        return pushPortRuleRepository.selectByPrimaryKey(rqt.getRuleId());

    }


    /**
     * 端口规则详情
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<PushPortRule> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<PushPortRule> pushPortRuleList = pushPortRuleRepository.selectList(rqt.getLv1ServeId(),rqt.getOrderTag(),rqt.getAppointType());

        SimplePageInfo<PushPortRule> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(pushPortRuleList);
        return listRespSimplePageInfo;
    }


    /**
     * 启用/禁用端口规则
     * @param rqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "推单设置", level2MenuName = "推单端口规则",
            tableName = "push_port_rule", mapperClass = PushPortRuleMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "pushPortRuleMapper", primaryKeyFieldName = "ruleId",
            configNameFieldNameFromEntity = "ruleName")
    public Integer enable(EnableRqt rqt){
        Integer ruleId = rqt.getRuleId();
        Integer ruleStatus = rqt.getRuleStatus();
        return pushPortRuleRepository.updateStatus(ruleId,ruleStatus,rqt.getUpdateAccountId());
    }



}
