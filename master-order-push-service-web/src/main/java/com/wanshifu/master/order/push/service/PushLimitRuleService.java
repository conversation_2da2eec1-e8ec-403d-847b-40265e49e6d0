package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.*;

/**
 * <AUTHOR>
 * @date 2025/03/08 15:13
 */
public interface PushLimitRuleService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    PushLimitRule detail(DetailRqt rqt);


    SimplePageInfo<PushLimitRule> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

}
