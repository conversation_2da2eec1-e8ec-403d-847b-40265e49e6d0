package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 策略Mapper
 * <AUTHOR>
 */
public interface LongTailStrategyGroupMapper extends IBaseCommMapper<LongTailStrategyGroup> {

    /**
     * 获取列表
     * @param longTailStrategyGroupName
     * @param isActive
     * @param createStartTime
     * @param createEndTime
     * @return
     */
    public List<LongTailStrategyGroup> selectList(
            @Param("businessLineId") Long businessLineId,
            @Param("longTailStrategyGroupName")String longTailStrategyGroupName,
            @Param("isActive")Integer isActive,
            @Param("createStartTime") Date createStartTime,
            @Param("createEndTime") Date createEndTime,
            @Param("categoryIdList") List<String> categoryIdList,
            @Param("cityId") String cityId
    );

    /**
     * 查询策略组
     * @param cityIdStr
     * @param categoryIdList
     * @param longTailStrategyGroupId
     * @param businessLineId
     * @return
     */
    LongTailStrategyGroup selectByCityAndCategory(
            @Param("cityIdStr") String cityIdStr,
            @Param("categoryIdList") List<String> categoryIdList,
            @Param("businessLineId") Integer businessLineId,
            @Param("longTailStrategyGroupId") Long longTailStrategyGroupId
    );

    /**
     * 查询策略组
     * @param cityIdStr
     * @param categoryIdList
     * @param longTailStrategyGroupId
     * @param businessLineId
     * @return
     */
    List<LongTailStrategyGroup> selectByCityAndCategoryActive(
            @Param("cityIdStr") String cityIdStr,
            @Param("categoryIdList") List<String> categoryIdList,
            @Param("businessLineId") Integer businessLineId,
            @Param("longTailStrategyGroupId") Long longTailStrategyGroupId
    );
}