package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/11 20:48
 */
@Data
public class CooperationBusinessMasterDto {

    /**
     * 师傅id
     */
    private String masterId;

    /**
     * 合作经营师傅字段
     * 是否限制推单 0:不限制,1:限制
     */
    private Integer isRestrictPushOrder;

    /**
     * 合作经营师傅字段
     * 是否开启派单 1:开启,0:关闭
     */
    private Integer isDistributeOrder;


    /**
     * 合作经营师傅字段
     * 最近结束合作时间
     */
    private Integer finishCooperativeTime;

    /**
     * 抢单顺序
     */
    private Integer grabSort = 0;

    /**
     * 评分
     */
    private BigDecimal score = BigDecimal.ZERO;
}
