package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;

/**
 * <AUTHOR>
 */
public interface LongTailStrategyService {
    /**
     * 策略管理-获取列表
     * @return
     */
    SimplePageInfo<LongTailStrategy> list(ListRqt rqt);

    /**
     * 策略管理-创建长尾单策略
     * @return
     */
    int create(CreateRqt createRqt);

    /**
     * 策略管理-修改长尾单策略
     * @return
     */
    int update(UpdateRqt updateRqt);

    /**
     * 策略管理-更新策略状态（启用/禁用）
     * @return
     */
    int updateStatus(EnableRqt enableRqt);

    /**
     * 策略管理-删除长尾单策略
     * @return
     */
    int delete(DeleteRqt deleteRqt);

    /**
     * 策略管理-策略详情
     * @return
     */
    LongTailStrategy detail(DetailRqt detailRqt);
}
