package com.wanshifu.master.order.push.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.enums.MasterType;
import com.wanshifu.master.order.push.domain.po.MasterPush;
import com.wanshifu.master.order.push.mapper.MasterPushMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 推单师傅表
 * <AUTHOR>
 */
@Repository
public class MasterPushRepository extends BaseRepository<MasterPush> {


    /**
     * 分表控制标记
     */
    private static String TIME_FLAG = "0";

    @Resource
    private MasterPushMapper masterPushMapper;


    public List<MasterPush> getMasterPushList(Long orderId,String orderVersion, String timeMark,Integer offset, Integer size,String masterType) {
        int endOffset = offset + size;
        return masterPushMapper.getMasterPushList(orderId,orderVersion,timeMark,offset,endOffset,masterType);
    }

    public List<MasterPush> getMasterPushList(Long orderId,String orderVersion, String timeMark,Integer offset, Integer size) {
        int endOffset = offset + size;
        return masterPushMapper.getMasterPushListByPushOffset(orderId,orderVersion,timeMark,offset,endOffset);
    }


    /**
     * 入库待推送列表
     *
     * @param orderId
     * @param newMasterList
     * @param oldMasterList
     */
    public int insertMasterPushList(String orderVersion, Long orderId, List<PushMaster> newMasterList,
                               List<PushMaster> oldMasterList,int batchSize) {

        int count = 0;

        //获取订单时间标记
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));

        if (!TIME_FLAG.equals(timeMark)) {
            String tableName = "master_push_" + DateFormatterUtil.getNowInt();
            masterPushMapper.createMasterPushTable(tableName);
            TIME_FLAG = timeMark;
        }

        List<MasterPush> masterPushList = new ArrayList<>();


        if(CollectionUtil.isNotEmpty(newMasterList)){
            for (int i = 0; i < newMasterList.size(); i++) {
                MasterPush masterPush = new MasterPush();
                masterPush.setOrderId(orderId);
                masterPush.setOrderVersion(orderVersion);
                masterPush.setMasterType(MasterType.MASETR_NEW.code);
                masterPush.setListOffset(i);
                masterPush.setMasterId(Long.valueOf(newMasterList.get(i).getMasterId()));
                masterPush.setScore(newMasterList.get(i).getScore());
                masterPush.setCreateTime(new Date());
                masterPush.setUpdateTime(new Date());
                masterPushList.add(masterPush);
            }

        }



        if(CollectionUtil.isNotEmpty(oldMasterList)){
            for (int i = 0; i < oldMasterList.size(); i++) {
                MasterPush masterPush = new MasterPush();
                masterPush.setOrderId(orderId);
                masterPush.setOrderVersion(orderVersion);
                masterPush.setMasterType(MasterType.MASTER_OLD.code);
                masterPush.setListOffset(i);
                masterPush.setMasterId(Long.valueOf(oldMasterList.get(i).getMasterId()));
                masterPush.setScore(oldMasterList.get(i).getScore());
                masterPush.setCreateTime(new Date());
                masterPush.setUpdateTime(new Date());
                masterPushList.add(masterPush);
            }

        }

        if(CollectionUtils.isNotEmpty(masterPushList)){
            count = batchInsertMasterPushList(masterPushList,batchSize,timeMark);
        }

        return count;

    }



    /**
     * 入库待推送列表
     *
     * @param orderId
     * @param newMasterList
     * @param oldMasterList
     */
    public int insertMasterPushList(String orderVersion, Long orderId, List<PushMaster> pushMasterList,int batchSize) {

        int count = 0;

        //获取订单时间标记
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));

        if (!TIME_FLAG.equals(timeMark)) {
            String tableName = "master_push_" + DateFormatterUtil.getNowInt();
            masterPushMapper.createMasterPushTable(tableName);
            TIME_FLAG = timeMark;
        }

        if(CollectionUtil.isNotEmpty(pushMasterList)){
            List<MasterPush> masterPushList = new ArrayList<>();
            for (int i = 0; i < pushMasterList.size(); i++) {
                MasterPush masterPush = new MasterPush();
                masterPush.setOrderId(orderId);
                masterPush.setOrderVersion(orderVersion);
                masterPush.setMasterType(pushMasterList.get(i).isNewMaster() ? MasterType.MASETR_NEW.code : MasterType.MASTER_OLD.code);
                masterPush.setPushOffset(i);
                masterPush.setMasterId(Long.valueOf(pushMasterList.get(i).getMasterId()));
                masterPush.setScore(pushMasterList.get(i).getScore());
                masterPush.setCreateTime(new Date());
                masterPush.setUpdateTime(new Date());
                masterPushList.add(masterPush);
            }
            count = batchInsertMasterPushList(masterPushList,batchSize,timeMark);
        }

        return count;

    }

    private int batchInsertMasterPushList(List<MasterPush> masterPushList,int batchSize,String timeMark){
        int count = 0;
        int batchNum = masterPushList.size() / batchSize;
        int leftNum = masterPushList.size() % batchSize;
        for(int index = 0;index < batchNum;index++){
            count = masterPushMapper.insertMasterPushList(masterPushList.subList(index * batchSize,(index + 1) * batchSize),timeMark) + count;
        }

        if(leftNum > 0){
            count = masterPushMapper.insertMasterPushList(masterPushList.subList(batchNum * batchSize,(batchNum * batchSize) + leftNum),timeMark) + count;
        }

        return count;
    }


    public Set<String> selectByMasterIdList(Long orderId, String orderVersion, String masterType,Integer offset,Set<String> masterSet){
        //获取订单时间标记
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
        return masterPushMapper.selectByMasterIdList(orderId,orderVersion,masterType,offset,masterSet,timeMark);
    }

    public Set<Long> selectByOrderIdAndMasterIds(Long globalOrderTraceId, Set<Long> masterIds, String timeMark) {
        return masterPushMapper.selectByOrderIdAndMasterIds(globalOrderTraceId,masterIds,timeMark);
    }

    public int dropHistoryPushTable(String tableName){
        return masterPushMapper.dropHistoryPushTable(tableName);
    }

    public List<MasterPush> selectPushScore(Long orderId,String orderVersion,List<Long> masterIdList){
        String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
        return masterPushMapper.selectPushScore(orderId,orderVersion,masterIdList,timeMark);
    }

}