package com.wanshifu.master.order.push.repository;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.Permission;
import com.wanshifu.master.order.push.mapper.PermissionMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
@Deprecated
public class PermissionRepository extends BaseRepository<Permission> {

    @Resource
    private PermissionMapper permissionMapper;



    public List<Permission> selectAllPermission(){
        Permission permission = new Permission();
        permission.setIsDelete(0);
        return permissionMapper.select(permission);
    }


    public List<Permission> selectList(String menuName){
        Example example = new Example(Permission.class);
        Example.Criteria criteria = example.createCriteria();
        
        if (StringUtils.isNotBlank(menuName)) {
            criteria.andLike("permissionName",  StrUtil.format("'%{}%'",menuName));
        }

        criteria.andEqualTo("isDelete", 0);
        example.orderBy("orderNum").asc();
        return this.selectByExample(example);
    }

    public List<Permission> selectByPermissionIdList(List<Integer> permissionIdList){
        Condition condition = new Condition(Permission.class);
        condition.createCriteria()
                .andIn("permissionId", permissionIdList)
                .andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }
}