package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@Data
public class DistributeResultNotices {

    /**
     * 调度结果：1: 调度成功，0：调度失败
     */
    private Integer distributeResult;

    /**
     * 业务线 1.成品  2. 家庭  3.创新（信息）
     *
     */
    private Integer businessLineId;

    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

    /**
     * 订单ID
     */
    private Long orderId;


    /**
     * 推单模式
     */
    private String pushMode;


    /**
     * 是否有价格 v7.3
     */
    private boolean hasPrice;


    private List<MasterInfo> masterInfoList;


    @Data
    public static class MasterInfo{

        /**
         * 师傅ID
         */
        private Long masterId;
        /**
         * 招募id
         */
        private String recruitId;

        /**
         * 标签名称: contract：直约 ，brand:品牌
         */
        private String tagName;

        /**
         * 标签扩展信息
         */
        private String tagExpand;

        private Integer distributeResult;

        private String distributeResultRemark;
    }
    
}
