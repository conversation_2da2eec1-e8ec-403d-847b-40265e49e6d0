package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.Permission;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;

import java.util.List;

@Deprecated
public interface PermissionSetService {

    int add(AddPermissionSetRqt rqt);


    int update(UpdatePermissionSetRqt rqt);

    SimplePageInfo<PermissionSet> list(GetPermissionSetListRqt rqt);


    List<Permission> menuList(GetMenuListRqt rqt);


    PermissionSet detail(GetPermissionSetDetailRqt rqt);


    List<Permission> permissionList(GetPermissionListRqt rqt);


    List<Permission> allPermissionList();

    Integer delete(DeletePermissionSetRqt rqt);



}
