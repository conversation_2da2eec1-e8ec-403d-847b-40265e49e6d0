package com.wanshifu.master.order.push.domain.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.ScorerItem;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.service.impl.RankDetail;
import com.wanshifu.master.order.push.service.impl.Scorer;
import com.wanshifu.util.LoCollectionsUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:44
 */
@Slf4j
public class AgentDistributor {


    private Set<String> orderFeatureSet=new HashSet<>();
    private Set<String> agentFeatureSet=new HashSet<>();

    public void addOrderFeatureCode(String featureCode){
        orderFeatureSet.add(featureCode);
    }

    public void addAgentFeatureCode(String featureCode){
        agentFeatureSet.add(featureCode);
    }

    public Set<String> getOrderFeatureSet() {
        return orderFeatureSet;
    }

    public Set<String> getAgentFeatureSet() {
        return agentFeatureSet;
    }


    private Map<String, List<Scorer>> scoringStrategy=new HashMap<>();


    /**
     * 评分
     * @param orderFeatures
     * @param context
     * @param rankDetail
     */
    public List<ScorerAgent> scorer(Set<String> agentSet, DefaultContext<String, Object> orderFeatures,
                                    DefaultContext<String, DefaultContext<String, Object>> context,
                                    RankDetail rankDetail){
        List<ScorerAgent> scorerAgent=new ArrayList<>();
        for (String agentId : agentSet) {
            final List<Scorer> scorerList = scoringStrategy.get(agentId);
            BigDecimal score=BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(scorerList)) {
                for (Scorer scorer : scorerList) {
                    score=score.add(scorer.mark(orderFeatures,context.get(agentId),rankDetail,agentId));
                }
            }
            scorerAgent.add(ScorerAgent.ScorerAgentBuilder.aScorerAgent()
                    .withAgentId(agentId)
                    .withScore(score)
                    .build());
        }
        return scorerAgent;
    }

    public void setScoringStrategy(Map<Integer, Set<String>> scorerMapping,List<OrderScoringStrategy> orderScoringStrategies) {
        try {
            for (OrderScoringStrategy orderScoringStrategy : orderScoringStrategies) {
                final String scoringStrategyExpression = orderScoringStrategy.getScoringStrategyExpression();
                final JSONArray objects = JSONObject.parseArray(scoringStrategyExpression);
                final Integer strategyId = orderScoringStrategy.getStrategyId();
                final ArrayList<Scorer> scorerArrayList = new ArrayList<>();
                for (int i = 0; i < objects.size(); i++) {
                    final JSONObject outerRow = objects.getJSONObject(i);
                    final String outerOpenConditionRuleExpression =outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                    final String outerOpenConditionRuleParams = outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                    this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(outerOpenConditionRuleParams));
                    final JSONArray scoreRuleList = outerRow.getJSONArray(FieldConstant.SCORE_RULE_LIST);
                    List<ScorerItem> scorerItemList=new ArrayList<>();
                    for (int j = 0; j < scoreRuleList.size(); j++) {
                        final JSONObject innerRow = scoreRuleList.getJSONObject(j);
                        final String openConditionRuleExpression = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                        final String openConditionRuleParams = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                        final String ruleName = innerRow.getString(FieldConstant.RULE_NAME);
                        this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(openConditionRuleParams));
                        final String scoreRuleParams = innerRow.getString(FieldConstant.SCORE_RULE_PARAMS);
                        this.agentFeatureSet.addAll(LoCollectionsUtil.stringToSet(scoreRuleParams));
                        final String scoreRuleExpression=innerRow.getString(FieldConstant.SCORE_RULE_EXPRESSION);
                        scorerItemList.add(
                                ScorerItem.ScorerItemBuilder.aScorerItem()
                                        .withRuleName(ruleName)
                                        .withAdmittance(openConditionRuleExpression)
                                        .withScoreRuleExpression(scoreRuleExpression)
                                        .build()
                        );
                    }
                    scorerArrayList.add(Scorer.ScorerBuilder.aScorer()
                            .withAdmittance(outerOpenConditionRuleExpression)
                            .withScorerId(String.valueOf(strategyId))
                            .withScorerItemList(scorerItemList)
                            .build());
                }
                final Set<String> agentSet = scorerMapping.get(strategyId);
                for (String agent : agentSet) {
                    this.scoringStrategy.put(agent,scorerArrayList);
                }
            }



        }catch (Exception e){
            log.warn("AgentDistributor withScoringStrategy exception:[{}],[{}]",orderScoringStrategies,e);
        }
    }
}
