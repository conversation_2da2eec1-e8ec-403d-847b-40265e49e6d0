package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.DistributeType;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.CreateOrderSelectStrategyRqt;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.OrderFullTimeMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.OrderSelectStrategyRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 技能验证单
 * <AUTHOR>
 * @date 2023-09-13 14:04:00
 */
@Slf4j
@Component("full_time_master")
public class FullTimeMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private FeatureRepository featureRepository;


    @Resource
    private OrderFullTimeMasterMatchLogRepository orderFullTimeMasterMatchLogRepository;

    @Resource
    private AddressApi addressApi;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;


    @Resource
    private HBaseClient hBaseClient;


    /**
     * 全时师傅派单过滤已推送师傅开关
     */
    @Value("${fullTimeMaster.dispatch.filterPushedMaster.switch:on}")
    private String filterPushedMasterSwitch;


    @Value("${full.time.master.special.city.list:441900}")
    private String fullTimeMasterSpecialCityList;


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){



        if(!apolloConfigUtils.isOpenFullTimeMasterDispatchSwitch()){
            return false;
        }


        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();

        if(CollectionUtils.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.FULL_TIME_MASTER.code)){
            return false;
        }

        if(StringUtils.isNotBlank(orderDetailData.getPushExtraData().getMatchSceneCode()) && (!MatchSceneCode.ORDER_CREATE.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode()))){
            return false;
        }


        if(!(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType()) && orderDetailData.getBusinessLineId() == 1)){
            return false;
        }

        String orderLngLat = orderDetailData.getOrderLngLat();

        if(StringUtils.isBlank(orderLngLat)){
            return false;
        }

        String[] orderLatLngArray = orderLngLat.split(",");

        if(Double.valueOf(orderLatLngArray[1]) <= 0 || Double.valueOf(orderLatLngArray[0]) <= 0){
            return false;
        }

        return  true;
    }


    public void insertMatchLog(OrderDetailData orderDetailData,String matchFailReason){
        OrderFullTimeMasterMasterMatchLog matchLog = new OrderFullTimeMasterMasterMatchLog();
        matchLog.setOrderId(orderDetailData.getMasterOrderId());
        matchLog.setAppointType(orderDetailData.getAppointType());
        matchLog.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
        matchLog.setMasterId(0L);
        matchLog.setOrderNo(orderDetailData.getOrderNo());
        matchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        matchLog.setIsMatchSucc(0);
        matchLog.setMatchFailReason(matchFailReason);
        matchLog.setCreateTime(new Date());
        matchLog.setUpdateTime(new Date());
        this.orderFullTimeMasterMatchLogRepository.insertSelective(matchLog);
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        DefaultContext<String, Object> orderFeatureContext = featureRepository.buildOrderFeatures(orderDetailData);

        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                .matchDistributor(orderDetailData, orderFeatureContext, DistributeType.FULL_TIME_MASTER.getCode());


        if(!orderDistributor.isMatched()){
//            this.insertMatchLog(orderDetailData,"未匹配到全时师傅调度策略");
            return null;
        }



        List<FullTimeMaster> fullTimeMasterList = matchFullTimeMaster(orderDistributor,orderDetailData);

        if(CollectionUtils.isEmpty(fullTimeMasterList)){
            return null;
        }

        fullTimeMasterList = filterPushedMaster(orderDetailData.getGlobalOrderId(),fullTimeMasterList);
        if(CollectionUtils.isEmpty(fullTimeMasterList)){
            return null;
        }


        List<OrderFullTimeMasterMasterMatchLog> matchLogList = fullTimeMasterList.stream().map(fullTimeMaster -> {
            OrderFullTimeMasterMasterMatchLog matchLog = new OrderFullTimeMasterMasterMatchLog();
            matchLog.setIsMatchSucc(1);
            matchLog.setMatchFailReason("");
            matchLog.setMasterId(Long.valueOf(fullTimeMaster.getMasterId()));
            matchLog.setOrderVersion(orderDetailData.getOrderVersion());
            matchLog.setOrderId(orderDetailData.getMasterOrderId());
            matchLog.setAppointType(orderDetailData.getAppointType());
            matchLog.setOrderNo(orderDetailData.getOrderNo());
            matchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            matchLog.setMatchDivisionLevel(fullTimeMaster.getMatchDivisionLevel());
            matchLog.setCreateTime(new Date());
            matchLog.setUpdateTime(new Date());
            return matchLog;
        }).collect(Collectors.toList());


        Set<String> masterSet = fullTimeMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());

        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,masterSet);

        orderDistributor.getMasterFeatureSet().add("full_time_master_daily_grab_cnt");
        orderDistributor.getMasterFeatureSet().add("full_time_master_daily_appoint_cnt");
        orderDistributor.getMasterFeatureSet().add("full_time_master_week_grab_cnt");
        orderDistributor.getMasterFeatureSet().add("full_time_master_week_appoint_cnt");
        orderDistributor.getMasterFeatureSet().add("full_time_master_min_week_grab_cnt");
        orderDistributor.getMasterFeatureSet().add("full_time_master_min_week_appoint_cnt");
        orderDistributor.getMasterFeatureSet().add("order_master_distance");

        Set<String> masterFeatureSet = orderDistributor.getMasterFeatureSet();
        boolean isOrderMasterNavigationDistance = false;
        if(masterFeatureSet.contains("order_master_navigational_distance")){
            isOrderMasterNavigationDistance = true;
            masterFeatureSet.remove("order_master_navigational_distance");
        }



        featureRepository.getMasterFeatures(pushFeature,masterSet,masterFeatureSet);

        DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();

        List<FullTimeMaster> finalFullTimeMasterList = filterByWeekReceiveOrGrabOrderLimit(fullTimeMasterList,masterFeatures,matchLogList);

        if(CollectionUtils.isEmpty(finalFullTimeMasterList)){
            orderFullTimeMasterMatchLogRepository.insertList(matchLogList);
            return new MatchMasterResult();
        }

        finalFullTimeMasterList = filterByOrderMasterDistance(finalFullTimeMasterList,masterFeatures,matchLogList);


        if(CollectionUtils.isEmpty(finalFullTimeMasterList)){
            orderFullTimeMasterMatchLogRepository.insertList(matchLogList);
            return new MatchMasterResult();
        }

        if(isOrderMasterNavigationDistance){
            Integer distance = getMinMasterOrderDistance(orderDistributor);
            finalFullTimeMasterList = filterByOrderMasterNavigationDistance(finalFullTimeMasterList,masterFeatures,distance,matchLogList);
            if(CollectionUtils.isEmpty(finalFullTimeMasterList)){
                orderFullTimeMasterMatchLogRepository.insertList(matchLogList);
                return new MatchMasterResult();
            }

            Set<String> finalFullTimeMasterIdSet = finalFullTimeMasterList.stream().map(FullTimeMaster::getMasterId).collect(Collectors.toSet());

            masterFeatures.keySet().removeIf(masterId -> !finalFullTimeMasterIdSet.contains(masterId));

            featureRepository.calculateOrderMasterNavigationalDistance(orderFeatureContext,masterFeatures);
        }




        masterSet = finalFullTimeMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());



        log.info("masterFeatures:" + JSON.toJSONString(masterFeatures));


        //过滤排序
        final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                .withType(FieldConstant.RANK_DETAIL)
                .build();



        if(orderDistributor.getMasterFeatureSet().contains("full_time_master_max_last_appoint_time") ){
            featureRepository.getFullTimeMasterMaxLastAppointTime(
                    masterSet,
                    pushFeature.getMasterFeature()
            );
        }

        handleFullTimeDistanceExclusive(finalFullTimeMasterList,masterFeatures);


        List<ScorerMaster> scorerMasterList = orderDistributor
                .rank(masterSet, orderFeatureContext, masterFeatures, rankDetail);



        Map<Long, String> filterReasonMap = new HashMap<>();


        try {
            if (Objects.nonNull(rankDetail) && org.apache.commons.lang.StringUtils.isNotBlank(rankDetail.getDetailInfo())) {
                Map<String, Object> filterDetailsMap = (Map) JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                for (String key : filterDetailsMap.keySet()) {
                    JSONArray jsonArray = (JSONArray) filterDetailsMap.get(key);
                    jsonArray.forEach(master -> {
                        filterReasonMap.put(Long.valueOf(String.valueOf(master)), key);
                    });
                }
            }

        } catch (Exception e) {
            log.error("rankDetail error", e);
        }


        List<String> masterList = CollectionUtils.isNotEmpty(scorerMasterList) ? scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList()) : new ArrayList<>();

        matchLogList.forEach(matchLog -> {

            if ((Objects.nonNull(matchLog.getIsFilter()) && matchLog.getIsFilter() == 1)
                    || !Strings.isNullOrEmpty(matchLog.getFilterReason())) {
                return;
            }
            matchLog.setIsFilter(masterList.contains(String.valueOf(matchLog.getMasterId())) ? 0 : 1);
            matchLog.setFilterReason(masterList.contains(String.valueOf(matchLog.getMasterId())) ? "" : filterReasonMap.getOrDefault(matchLog.getMasterId(),""));

        });


        if(CollectionUtils.isNotEmpty(scorerMasterList)){

            List<FullTimeMaster> distributeFullTimeMasterList = distribute(scorerMasterList,finalFullTimeMasterList,orderDistributor.getDistributeRule());

            Set<String> distributeMasterSet = distributeFullTimeMasterList.stream().map(FullTimeMaster::getMasterId).collect(Collectors.toSet());
            matchLogList.forEach(matchLog ->{

                if (Objects.nonNull(matchLog.getIsFilter())
                        && matchLog.getIsFilter() == 1) {
                    return;
                }
                matchLog.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                matchLog.setIsDistribute(distributeMasterSet.contains(String.valueOf(matchLog.getMasterId())) ? 1 : 0);
            });

            if(CollectionUtils.isNotEmpty(matchLogList)){
                orderFullTimeMasterMatchLogRepository.insertList(matchLogList);
            }

            MatchMasterResult matchMasterResult = new MatchMasterResult();
            matchMasterResult.setMasterIdSet(distributeMasterSet);
            matchMasterResult.putExtraData("full_time_master_list",distributeFullTimeMasterList);
            return matchMasterResult;
        }else{
            if(CollectionUtils.isNotEmpty(matchLogList)){
                orderFullTimeMasterMatchLogRepository.insertList(matchLogList);
            }
        }

        return new MatchMasterResult();


    }


    @Resource
    private OrderSelectStrategyRepository orderSelectStrategyRepository;

    private Integer getMinMasterOrderDistance(OrderDistributor orderDistributor){

        List<Integer> orderSelectStrategyIdList  = orderDistributor.getFilterList().stream().map(Filter::getFilterId).map(Integer::valueOf).distinct().collect(Collectors.toList());
        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(orderSelectStrategyIdList);

        Map<Integer, OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream()
                .collect(Collectors.toMap(OrderSelectStrategy::getStrategyId, strategy -> strategy));

        Integer distance = null;

        for(Filter filter : orderDistributor.getFilterList()){
            OrderSelectStrategy orderSelectStrategy = orderSelectStrategyMap.get(Integer.valueOf(filter.getFilterId()));
            CreateOrderSelectStrategyRqt.SelectStrategy selectStrategy = JSON.parseObject(orderSelectStrategy.getSelectRule(),CreateOrderSelectStrategyRqt.SelectStrategy.class);

            for(RuleItem ruleItem : selectStrategy.getRuleList()){

                if(!ruleItem.getRuleName().equals(filter.getRuleName())){
                    continue;
                }


                RuleItem.FilterRuleItem orderMasterNavigationalDistanceRuleItem = ruleItem.getFilterRule().getItemList().stream().filter(filterRuleItem -> "order_master_navigational_distance".equals(filterRuleItem.getItemName())).findFirst().orElse(null);
                if(Objects.nonNull(orderMasterNavigationalDistanceRuleItem)){
                    if(Objects.isNull(distance)){
                        distance = Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue());
                    }else if(Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue()) < distance){
                        distance = Integer.valueOf(orderMasterNavigationalDistanceRuleItem.getItemValue());
                    }
                }
            }

        }


        return distance;


    }



    public List<FullTimeMaster> filterByOrderMasterNavigationDistance(List<FullTimeMaster> fullTimeMasterList,DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                                                      Integer navigationDistance,List<OrderFullTimeMasterMasterMatchLog> matchLogList){
        if(Objects.isNull(navigationDistance)){
            return fullTimeMasterList;
        }
        Map<Long, String> filterMap = Maps.newHashMap();


        List<FullTimeMaster> finalFullTimeMasterList = fullTimeMasterList.stream().filter(fullTimeMaster -> {

            Long orderMasterDistance = (Long)masterFeatures.get(fullTimeMaster.getMasterId()).get("order_master_distance");
            if(Objects.nonNull(orderMasterDistance) && orderMasterDistance > navigationDistance){
                filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "师傅常住地距离客户地址导航距离大于配置的导航距离：".concat(String.valueOf(navigationDistance)).concat("公里"));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(filterMap)) {
            matchLogList.forEach(matchLog -> {
                if (filterMap.containsKey(matchLog.getMasterId())) {
                    matchLog.setIsFilter(1);
                    matchLog.setFilterReason(filterMap.get(matchLog.getMasterId()));
                }
            });
        }

        return finalFullTimeMasterList;
    }


    public List<FullTimeMaster> matchFullTimeMaster(OrderDistributor orderDistributor,OrderDetailData orderDetailData){

        Integer matchDivisionLevel = 4;
        Long fourthDivisionId = orderDetailData.getFourthDivisionId();
        if(Objects.nonNull(fourthDivisionId) && fourthDivisionId > 0){
            matchDivisionLevel = 4;
        }else{
            matchDivisionLevel = 3;
        }


        BoolQueryBuilder boolQueryBuilder = buildQueryBuilder(matchDivisionLevel,0,orderDetailData);

        if(Objects.isNull(boolQueryBuilder)){
            return null;
        }
        List<MasterBaseSearch> masterBaseSearchList = searchFullTimeMaster(boolQueryBuilder);


        if(matchDivisionLevel == 3){
            masterBaseSearchList =  levelThreeDivisionFilter(masterBaseSearchList,orderDetailData);
        }


        List<FullTimeMaster> fullTimeMasterList = new ArrayList<>();

        Set<String> tempMasterSet = new HashSet<>();

        for(MasterBaseSearch masterBaseSearch : masterBaseSearchList){

            if(!tempMasterSet.contains(masterBaseSearch.getMasterId())){
                FullTimeMaster fullTimeMaster = new FullTimeMaster();
                BeanUtils.copyProperties(masterBaseSearch,fullTimeMaster);

                fullTimeMaster.setIsServeUnderstake(0);
                fullTimeMaster.setMatchDivisionLevel(matchDivisionLevel);
                fullTimeMasterList.add(fullTimeMaster);
                tempMasterSet.add(masterBaseSearch.getMasterId());
            }

        }



        if(matchDivisionLevel == 4 && Objects.nonNull(orderDistributor.getCooperationBusinessServeDivisionAtLast()) &&
                orderDistributor.getCooperationBusinessServeDivisionAtLast() == 1){
            boolQueryBuilder = buildQueryBuilder(matchDivisionLevel,1,orderDetailData);
            if(Objects.nonNull(boolQueryBuilder)){
                masterBaseSearchList = searchFullTimeMaster(boolQueryBuilder);
                if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                    for(MasterBaseSearch masterBaseSearch : masterBaseSearchList){

                        if(!tempMasterSet.contains(masterBaseSearch.getMasterId())){
                            FullTimeMaster fullTimeMaster = new FullTimeMaster();
                            BeanUtils.copyProperties(masterBaseSearch,fullTimeMaster);
                            fullTimeMaster.setIsServeUnderstake(1);
                            fullTimeMaster.setMatchDivisionLevel(4);
                            fullTimeMasterList.add(fullTimeMaster);
                            tempMasterSet.add(masterBaseSearch.getMasterId());
                        }

                    }

                }
            }

        }
        return fullTimeMasterList;
    }


    /**
     * 过滤已推送的协议师傅
     * @param getGlobalOrderTraceId
     * @param fullTimeMasterList
     */
    public List<FullTimeMaster> filterPushedMaster(Long getGlobalOrderTraceId,List<FullTimeMaster> fullTimeMasterList){

        if(!"on".equals(filterPushedMasterSwitch)){
            return fullTimeMasterList;
        }

        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),PushMode.FULL_TIME_MASTER_DISPATCH.code);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)){
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            return fullTimeMasterList.stream().filter(fullTimeMaster -> !pushedMasterIdSet.contains(fullTimeMaster.getMasterId())).collect(Collectors.toList());
        }

        return fullTimeMasterList;

    }


    private BoolQueryBuilder buildQueryBuilder(Integer matchDivisionLevel,Integer serveDivisionAtLast,OrderDetailData orderDetailData){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if(matchDivisionLevel == 3){

            List<Long> specialCityList = StringUtils.isNotBlank(fullTimeMasterSpecialCityList) ? Arrays.stream(fullTimeMasterSpecialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();

            if(specialCityList.contains(orderDetailData.getSecondDivisionId())){
                boolQueryBuilder.must(QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(orderDetailData.getThirdDivisionId())));
            }else{
                if(Objects.nonNull(serveDivisionAtLast) && serveDivisionAtLast == 1){
                    boolQueryBuilder.must(QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(orderDetailData.getThirdDivisionId())));
                }else{
                    return null;
                }
            }
        }else{
            if(Objects.nonNull(serveDivisionAtLast) && serveDivisionAtLast == 1){
                boolQueryBuilder.must(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));
                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("fullTimeMasterServeFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));

            }else{
                boolQueryBuilder.must(QueryBuilders.termsQuery("fullTimeMasterServeFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));

            }
        }


        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterTimeType", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("fullTimeMasterDispatchStatus", 1));
        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("violationPunishLimitBusinessLineIds", Lists.newArrayList(orderDetailData.getBusinessLineId())));





        BoolQueryBuilder boolQueryBuilder1 = new BoolQueryBuilder();


        BoolQueryBuilder boolQueryBuilder2 = new BoolQueryBuilder();
        BoolQueryBuilder boolQueryBuilder3 = new BoolQueryBuilder();
        boolQueryBuilder3.mustNot(QueryBuilders.existsQuery("fullTimeMasterServeIds"));
        boolQueryBuilder2.should(boolQueryBuilder3);
        boolQueryBuilder2.should(QueryBuilders.termQuery("fullTimeMasterServeIds",""));
        boolQueryBuilder2.minimumShouldMatch(1);
        boolQueryBuilder1.should(boolQueryBuilder3);


        BoolQueryBuilder boolQueryBuilder4 = new BoolQueryBuilder();
        orderDetailData.getLv3ServeIdList().forEach(lv3ServeId -> boolQueryBuilder4.must(QueryBuilders.termQuery("fullTimeMasterServeIds", lv3ServeId)));
        boolQueryBuilder1.should(boolQueryBuilder4);
        boolQueryBuilder1.minimumShouldMatch(1);

        boolQueryBuilder.must(boolQueryBuilder1);

        BoolQueryBuilder shouldQuery = this
                .boolQueryBuilder(
                        "masterTechniqueIds",
                        orderDetailData.getOrderTechniqueSet(),
                        1);
        boolQueryBuilder.filter(shouldQuery);

        return boolQueryBuilder;
    }


    private List<FullTimeMaster> filterByWeekReceiveOrGrabOrderLimit(List<FullTimeMaster> fullTimeMasterList,
                                                                     DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                                                     List<OrderFullTimeMasterMasterMatchLog> orderFullTimeMasterMasterMatchLogList) {

        Map<Long, String> filterMap = Maps.newHashMap();


        List<FullTimeMaster> finalFullTimeMasterList = fullTimeMasterList.stream().filter(fullTimeMaster -> {

            if(Objects.isNull(fullTimeMaster.getFullTimeMasterWeekGrabOrderLimit()) &&
                    Objects.isNull(fullTimeMaster.getFullTimeMasterWeekAppointOrderLimit()) &&
                    Objects.isNull(fullTimeMaster.getFullTimeMasterDailyGrabOrderLimit()) &&
                    Objects.isNull(fullTimeMaster.getFullTimeMasterDailyAppointOrderLimit())){
                return true;
            }

            if(Objects.nonNull(fullTimeMaster.getFullTimeMasterDailyGrabOrderLimit()) && fullTimeMaster.getFullTimeMasterDailyGrabOrderLimit() > 0){
                Integer dailyGrabCnt = (Integer)masterFeatures.get(fullTimeMaster.getMasterId()).get("full_time_master_daily_grab_cnt");
                if(Objects.nonNull(dailyGrabCnt) && dailyGrabCnt >= fullTimeMaster.getFullTimeMasterDailyGrabOrderLimit()){
                    filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "该师傅日抢单量上限".concat(String.valueOf(fullTimeMaster.getFullTimeMasterDailyGrabOrderLimit())).concat("单，当前已抢").concat(dailyGrabCnt.toString()).concat("单，已达到日抢单量上限"));
                    return false;
                }
            }


            if(Objects.nonNull(fullTimeMaster.getFullTimeMasterDailyAppointOrderLimit()) && fullTimeMaster.getFullTimeMasterDailyAppointOrderLimit() > 0){
                Integer dailyAppointCnt = (Integer)masterFeatures.get(fullTimeMaster.getMasterId()).get("full_time_master_daily_appoint_cnt");
                if(Objects.nonNull(dailyAppointCnt) && dailyAppointCnt >= fullTimeMaster.getFullTimeMasterDailyAppointOrderLimit()){
                    filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "该师傅日派单量上限".concat(String.valueOf(fullTimeMaster.getFullTimeMasterDailyAppointOrderLimit())).concat("单，当前已派").concat(dailyAppointCnt.toString()).concat("单，已达到日指派量上限"));
                    return false;
                }
            }

            if(Objects.nonNull(fullTimeMaster.getFullTimeMasterWeekGrabOrderLimit()) && fullTimeMaster.getFullTimeMasterWeekGrabOrderLimit() > 0){
                Integer weekGrabCnt = (Integer)masterFeatures.get(fullTimeMaster.getMasterId()).get("full_time_master_week_grab_cnt");
                if(Objects.nonNull(weekGrabCnt) && weekGrabCnt >= fullTimeMaster.getFullTimeMasterWeekGrabOrderLimit()){
                    filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "该师傅周抢单量上限".concat(String.valueOf(fullTimeMaster.getFullTimeMasterWeekGrabOrderLimit())).concat("单，当前已抢").concat(weekGrabCnt.toString()).concat("单，已达到周抢单量上限"));
                    return false;
                }
            }


            if(Objects.nonNull(fullTimeMaster.getFullTimeMasterWeekAppointOrderLimit()) && fullTimeMaster.getFullTimeMasterWeekAppointOrderLimit() > 0){
                Integer weekAppointCnt = (Integer)masterFeatures.get(fullTimeMaster.getMasterId()).get("full_time_master_week_appoint_cnt");
                if(Objects.nonNull(weekAppointCnt) && weekAppointCnt >= fullTimeMaster.getFullTimeMasterWeekAppointOrderLimit()){
                    filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "该师傅周派单量上限".concat(String.valueOf(fullTimeMaster.getFullTimeMasterWeekAppointOrderLimit())).concat("单，当前已派").concat(weekAppointCnt.toString()).concat("单，已达到周指派量上限"));
                    return false;
                }
            }

            return true;

        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(filterMap)) {
            orderFullTimeMasterMasterMatchLogList.forEach(matchLog -> {
                if (filterMap.containsKey(matchLog.getMasterId())) {
                    matchLog.setIsFilter(1);
                    matchLog.setFilterReason(filterMap.get(matchLog.getMasterId()));
                }
            });
        }

        return finalFullTimeMasterList;
    }


    private void handleFullTimeDistanceExclusive(List<FullTimeMaster> fullTimeMasterList,DefaultContext<String, DefaultContext<String, Object>> masterFeatures){
        List<FullTimeMaster> distanceExclusiveFullTimeMasterList = fullTimeMasterList.stream().filter(fullTimeMaster -> Objects.nonNull(fullTimeMaster.getFullTimeMasterDistanceExclusive()) && fullTimeMaster.getFullTimeMasterDistanceExclusive() == 0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(distanceExclusiveFullTimeMasterList)){
            return ;
        }

        distanceExclusiveFullTimeMasterList.forEach(fullTimeMaster -> {

            masterFeatures.get(fullTimeMaster.getMasterId()).put("order_master_distance",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("order_master_navigational_distance",0);

            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_daily_grab_cnt",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_daily_appoint_cnt",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_week_grab_cnt",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_week_appoint_cnt",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_min_week_grab_cnt",0);
            masterFeatures.get(fullTimeMaster.getMasterId()).put("full_time_master_min_week_appoint_cnt",0);

        });
    }



    private List<FullTimeMaster> filterByOrderMasterDistance(List<FullTimeMaster> fullTimeMasterList,
                                                             DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                                             List<OrderFullTimeMasterMasterMatchLog> orderFullTimeMasterMasterMatchLogList) {

        Map<Long, String> filterMap = Maps.newHashMap();


        List<FullTimeMaster> finalFullTimeMasterList = fullTimeMasterList.stream().filter(fullTimeMaster -> {

            Integer fullTimeMasterServeDistanceRange = fullTimeMaster.getFullTimeMasterServeDistanceRange();
            if(Objects.isNull(fullTimeMasterServeDistanceRange) || fullTimeMasterServeDistanceRange == 0){
                return true;
            }

            Long orderMasterDistance = (Long)masterFeatures.get(fullTimeMaster.getMasterId()).get("order_master_distance");
            if(Objects.nonNull(orderMasterDistance) && orderMasterDistance > fullTimeMasterServeDistanceRange){
                filterMap.put(Long.valueOf(fullTimeMaster.getMasterId()), "该师傅接单范围为".concat(String.valueOf(fullTimeMaster.getFullTimeMasterServeDistanceRange())).concat("公里，当前订单距离").concat(orderMasterDistance.toString()).concat("公里，超出接单范围"));
                return false;
            }

            return true;

        }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(filterMap)) {
            orderFullTimeMasterMasterMatchLogList.forEach(matchLog -> {
                if (filterMap.containsKey(matchLog.getMasterId())) {
                    matchLog.setIsFilter(1);
                    matchLog.setFilterReason(filterMap.get(matchLog.getMasterId()));
                }
            });
        }

        return finalFullTimeMasterList;
    }






    /**
     * 获取地址子集集合
     * @return
     */
    private Set<String> getSubListByDivisionId(Long divisionId){
        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
        final Set<String> divisionList =
                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
        return divisionList;
    }


    private List<MasterBaseSearch> levelThreeDivisionFilter(List<MasterBaseSearch> masterBaseList, OrderDetailData orderDetailData){

        List<Long> specialCityList = StringUtils.isNotBlank(fullTimeMasterSpecialCityList) ? Arrays.stream(fullTimeMasterSpecialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();

        if(specialCityList.contains(orderDetailData.getSecondDivisionId())){
            return masterBaseList;
        }

        final Set<String> subDivisionIdList = getSubListByDivisionId(orderDetailData.getThirdDivisionId());
        if (CollectionUtils.isEmpty(subDivisionIdList)) {
            return null;
        }

        return masterBaseList.stream().filter(masterBase -> thirdLevelDivisionFullMatch(subDivisionIdList, masterBase.getServeFourthDivisionIds())).collect(Collectors.toList());


    }


    /**
     * 三级地址全匹配
     */
    private boolean thirdLevelDivisionFullMatch(
            Set<String> orderFullFourthDivisions,
            String packageFourthLevelDivisions){
        if (org.apache.commons.lang3.StringUtils.isEmpty(packageFourthLevelDivisions)) {
            return false;
        }
        final Set<String> packageFourthDivisionSet
                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());

        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
    }


    private List<FullTimeMaster> distribute(List<ScorerMaster> scorerMasterList,List<FullTimeMaster> fullTimeMasterList, String distributeRule){

        Map<String, ScorerMaster> scorerMasterMap = scorerMasterList.stream()
                .collect(Collectors.toMap(ScorerMaster::getMasterId, scorerMaster -> scorerMaster));

        fullTimeMasterList = fullTimeMasterList.stream().filter(fullTimeMaster -> {
            if(scorerMasterMap.containsKey(fullTimeMaster.getMasterId())){
                fullTimeMaster.setScore(scorerMasterMap.get(fullTimeMaster.getMasterId()).getScore());
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        Map<Integer,List<FullTimeMaster>> fullTimeMasterMap = fullTimeMasterList.stream().collect(Collectors.groupingBy(fullTimeMaster -> fullTimeMaster.getIsServeUnderstake()));

        List<FullTimeMaster> fullTimeMasters = fullTimeMasterMap.get(0);
        List<FullTimeMaster> understakeFullTimeMasters = fullTimeMasterMap.get(1);


        if(OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)){

            if(CollectionUtils.isNotEmpty(fullTimeMasters)){
                Collections.sort(fullTimeMasters);
            }

            if(CollectionUtils.isNotEmpty(understakeFullTimeMasters)){
                Collections.sort(understakeFullTimeMasters);
            }



        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)){


            if(CollectionUtils.isNotEmpty(fullTimeMasters)){
                Collections.sort(fullTimeMasters);
                int index = fullTimeMasters.size() >= 50 ? 50 : fullTimeMasters.size();
                fullTimeMasters = fullTimeMasters.subList(0,index);
                Collections.shuffle(fullTimeMasters);
            }

            if(CollectionUtils.isNotEmpty(understakeFullTimeMasters)){
                Collections.sort(understakeFullTimeMasters);
                int index = understakeFullTimeMasters.size() >= 50 ? 50 : understakeFullTimeMasters.size();
                understakeFullTimeMasters = understakeFullTimeMasters.subList(0,index);
                Collections.shuffle(understakeFullTimeMasters);
            }

        }else if(OrderDistributeRule.RANDOM.getCode().equals(distributeRule)){

            if(CollectionUtils.isNotEmpty(fullTimeMasters)){
                Collections.shuffle(fullTimeMasters);
            }

            if(CollectionUtils.isNotEmpty(understakeFullTimeMasters)){
                Collections.shuffle(understakeFullTimeMasters);
            }
        }

        int index = 0;
        if(CollectionUtils.isNotEmpty(fullTimeMasters)){
            for(int i = 0;i < fullTimeMasters.size();i++){
                fullTimeMasters.get(i).setOfferSort(i);
            }
            index = fullTimeMasters.size();
        }


        if(CollectionUtils.isNotEmpty(understakeFullTimeMasters)){
            for(int i = 0;i < understakeFullTimeMasters.size();i++){
                understakeFullTimeMasters.get(i).setOfferSort(i + index);
            }
        }

        List<FullTimeMaster> finalFullTimeMaster = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fullTimeMasters)){
            finalFullTimeMaster.addAll(fullTimeMasters);
        }

        if(CollectionUtils.isNotEmpty(understakeFullTimeMasters)){
            finalFullTimeMaster.addAll(understakeFullTimeMasters);
        }



        return finalFullTimeMaster;
    }





    public MatchQueryBuilder stringMatchQueryBuilder(String fieldName, String value, Operator operator) {
        //设置查询类型为MatchQuery。
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        matchQueryBuilder.operator(operator);
        return matchQueryBuilder;
    }

    /**
     * 索引API
     */
    public BoolQueryBuilder boolQueryBuilder(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch) {
        List<String> shouldConditionList = new ArrayList<>(shouldConditions);
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        shouldConditionList.stream()
                .map(shouldCondition->shouldQuery.should(stringMatchQueryBuilder(fieldName,shouldCondition,Operator.AND)))
                .collect(Collectors.toList());
        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }







    public List<MasterBaseSearch> searchFullTimeMaster(BoolQueryBuilder boolQueryBuilder){

        log.info("searchFullTimeMaster:" + boolQueryBuilder.toString());

        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        log.info("searchFullTimeMaster resp:" + JSON.toJSONString(masterBaseSearchList));

        return masterBaseSearchList;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult){


    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        try{
            if(matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
                return false;
            }
            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp),PushMode.FULL_TIME_MASTER.code);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
            commonFeature.put("full_time_master_list", matchMasterResult.getExtraData().get("full_time_master_list"));
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.FULL_TIME_MASTER_DISPATCH.code);
            pushControllerFacade.directPush(orderDetailData, orderVersion, matchMasterResult.getMasterIdSet(), commonFeature);



            return true;
        }catch(Exception e){
            log.error(String.format("执行全时师傅派单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;

    }



}
