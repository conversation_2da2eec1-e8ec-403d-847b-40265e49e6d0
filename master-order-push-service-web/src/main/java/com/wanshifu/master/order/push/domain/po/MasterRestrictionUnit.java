package com.wanshifu.master.order.push.domain.po;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MasterRestrictionUnit {
    private Integer businessLineId;
    private Map<String,MasterRestriction> masterRestrictionMap=new HashMap<>();
    private Set<String> innerRestrictMasterSet=new HashSet<>();
    private Map<String,MasterRestriction> innerMasterRestrictionMap=new HashMap<>();

    private Set<String> outerRestrictMasterSet=new HashSet<>();
    private Map<String,MasterRestriction> outerMasterRestrictionMap=new HashMap<>();

    public Map<String, MasterRestriction> getInnerMasterRestrictionMap() {
        return innerMasterRestrictionMap;
    }

    public Map<String, MasterRestriction> getOuterMasterRestrictionMap() {
        return outerMasterRestrictionMap;
    }

    public void addOuterRestrictMasterMap(String masterId, MasterRestriction masterRestriction){
        outerMasterRestrictionMap.put(masterId,masterRestriction);
    }
    public void addOuterRestrictMaster(String masterId){
        outerRestrictMasterSet.add(masterId);
    }

    public Set<String> getOuterRestrictMasterSet() {
        return outerRestrictMasterSet;
    }

    public void addInnerRestrictMasterMap(String masterId,MasterRestriction masterRestriction){
        innerMasterRestrictionMap.put(masterId,masterRestriction);
    }
    public void addInnerRestrictMaster(String masterId){
        innerRestrictMasterSet.add(masterId);
    }
    public Set<String> getInnerRestrictMasterSet() {
        return innerRestrictMasterSet;
    }

    public void setMasterRestrictionMap(Map<String, MasterRestriction> masterRestrictionMap) {
        this.masterRestrictionMap.putAll(masterRestrictionMap);
        if (masterRestrictionMap!=null) {
            for (Map.Entry<String, MasterRestriction> row : masterRestrictionMap.entrySet()) {
                if (!row.getValue().getResult()) {
                    addInnerRestrictMaster(row.getKey());
                    addInnerRestrictMasterMap(row.getKey(),row.getValue());
                }else {
                    addOuterRestrictMaster(row.getKey());
                    addOuterRestrictMasterMap(row.getKey(),row.getValue());
                }
            }
        }
    }

    public Map<String, MasterRestriction> getMasterRestrictionMap() {
        return masterRestrictionMap;
    }

    public Integer getBusinessLineId() {
        return businessLineId;
    }

    public void setBusinessLineId(Integer businessLineId) {
        this.businessLineId = businessLineId;
    }
}
