package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.annotation.Field;
import com.wanshifu.master.order.push.domain.annotation.FieldType;
import lombok.Data;

@Data
@Document(indexAlias = "agent_master", type = "agent_master")
public class AgentMasterIndex {

    /**
     * 师傅id
     */
    @Field(type = FieldType.Long)
    private Long masterId;

    /**
     * 合作商师傅id
     */
    @Field(type = FieldType.Long)
    private Long agentMasterId;

    /**
     * 合作商id
     */
    @Field(type = FieldType.Long)
    private Long agentId;

    /**
     * 是否主师傅
     */
    @Field(type = FieldType.Integer)
    private Integer mainMasterStatus;

    /**
     * 添加时间
     */
    @Field(type = FieldType.Text)
    private String addTime;

    /**
     * 合作状态
     */
    @Field(type = FieldType.Integer)
    private Integer cooperationStatus;

    /**
     * 师傅来源类型
     */
    @Field(type = FieldType.Text)
    private String masterSourceType;

}
