package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import com.wanshifu.master.order.push.domain.po.NewModelMatchDetail;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.CooperationBusinessMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.NewModelMatchDetailRepository;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/08/13 15:21
 */
@Service
@Slf4j
public class NewModelDispatchOrderDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {

    @Resource
    private NewModelMatchDetailRepository newModelMatchDetailRepository;

    @Resource
    private OrderDataBuilder orderDataBuilder;


    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage){


        Integer distributeResult = 1;
        if(CollectionUtils.isNotEmpty(orderDistributeResultMessage.getSuccessMasterList())){
            distributeResult = 1;
        }else{
            distributeResult = 0;
        }

        List<Long> successMasterList = orderDistributeResultMessage.getSuccessMasterList();
        List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
        Map<Long,JSONObject> failMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(failMasterList)){
            List<JSONObject> jsonObjectList = orderDistributeResultMessage.getMasterFailVos();
            if(CollectionUtils.isNotEmpty(jsonObjectList)){
                jsonObjectList.forEach(jsonObject -> {
                    failMap.put( jsonObject.getLong("masterId"),jsonObject.getJSONObject("object"));
                });
            }
        }
        Long orderId = orderDistributeResultMessage.getOrderId();
        Long masterId = distributeResult == 1 ? successMasterList.get(0) : failMasterList.get(0);

        if(distributeResult == 0){

            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NEW_MODEL_MASTER);
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderId);
            orderMatchMasterRqt.setMatchSceneCode(MatchSceneCode.MAIN_MASTER_REFUSED_ORDER.getCode());
            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

        }


        NewModelMatchDetail newModelMatchDetail = newModelMatchDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
        if(Objects.nonNull(newModelMatchDetail)){
            String distributeResultRemark = failMap.containsKey(masterId) ? failMap.get(masterId).getString("tipContent") : "";
            NewModelMatchDetail matchDetail = new NewModelMatchDetail();
            matchDetail.setId(newModelMatchDetail.getId());
            matchDetail.setDistributeResult(distributeResult);
            matchDetail.setDistributeFailRemark(distributeResultRemark);
            matchDetail.setIsDistribute(null);
            matchDetail.setDistributeRemark(null);
            newModelMatchDetailRepository.updateByPrimaryKeySelective(matchDetail);
        }

        return 1;
    }

    @Override
    public void afterPropertiesSet() {
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_NEW_MODEL_DISPATCH.getCode(), this);
    }


}
