package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushProgress;
import org.apache.ibatis.annotations.Param;

/**
 * 推送进度Mapper
 * <AUTHOR>
 */
public interface PushProgressMapper extends IBaseCommMapper<PushProgress> {

    String getOrderLatestVersion(@Param("orderId") Long orderId);

    PushProgress getOrderLatestPushProgress(@Param("orderId") Long orderId);

    Integer countByPushType(@Param("orderId") Long orderId,@Param("pushType") String pushType);



}