package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.order.offer.domains.vo.push.OrderPushNotices;
import lombok.Data;

import java.util.List;

@Data
public class OrderMatchMasterRqt {

    private Long masterOrderId;

    private Integer isExclusiveTeamMaster = 0;

    private List<Long> orderPushEliminateMasterIds;

    private String pushMode;

    private Integer businessLineId;

    /**
     * 切换开关标签 new-新 old-旧
     */
    private String handoffTag;
    /**
     * 订单标签(exclusive:专属,direct_appointment:直约,preferred:优选,contract:合约,brand:品牌,agent:代理商)
     */
    private String tagName;

    private String rePushSource;

    private String matchSceneCode;

    private String masterSourceType;

    /**
     * 排除的推单模式
     */
    private List<String> exclusivePushModeList;

    private List<OrderPushNotices.MasterAutoOfferPriceInfo> masterAutoOfferPriceInfoList;
}
