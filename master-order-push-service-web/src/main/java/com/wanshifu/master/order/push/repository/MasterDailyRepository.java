package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.Master;
import com.wanshifu.master.order.push.domain.po.MasterDaily;
import com.wanshifu.master.order.push.mapper.MasterDailyMapper;
import com.wanshifu.master.order.push.mapper.MasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterDailyRepository extends BaseRepository<MasterDaily> {

    @Resource
    private MasterDailyMapper masterDailyMapper;


    public List<MasterDaily> selectByMasterIdSet(Set<String> masterIdSet,String dt){
        return null;
    }



}