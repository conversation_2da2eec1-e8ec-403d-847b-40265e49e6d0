package com.wanshifu.master.order.push.aspect;

import cn.hutool.json.JSONUtil;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.api.IopAccountApi;
import com.wanshifu.master.order.push.api.resp.IopAccountResp;
import com.wanshifu.master.order.push.api.rqt.IopGetInfoListByAccountIdReq;
import com.wanshifu.master.order.push.domain.po.PushConfigUpdateLog;
import com.wanshifu.master.order.push.mapper.PushConfigUpdateLogMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.util.FeiShuTokenHelper;
import com.wanshifu.util.FeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/10 15:57
 */
@Slf4j
@Aspect
@Component
public class FeishuNoticeAspect {

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private PushConfigUpdateLogMapper pushConfigUpdateLogMapper;

    @Resource
    private IopAccountApi iopAccountApi;

    @Resource(name = "asyncUpdateExecutor")
    private Executor executor;

    @Autowired
    private Validator validator;

    @Around("@annotation(feishuNotice)")
    public Object around(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) throws Throwable {
        // 执行目标方法
        Object result = joinPoint.proceed();

        //异步处理配置变更后飞书告警
        CompletableFuture.runAsync(() -> this.doFeishuNoticeAfterUpdate(joinPoint,feishuNotice), executor);

        return result;
    }

    /**
     * 处理配置变更后飞书告警
     * @param joinPoint
     * @param feishuNotice
     */
    private void doFeishuNoticeAfterUpdate(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) {
        String noticeContent = "";
        // 参数校验
        // 使用Validator校验注解参数
        Set<ConstraintViolation<FeishuNotice>> violations = validator.validate(feishuNotice);
        if (!violations.isEmpty()) {
            // 构建消息内容

            noticeContent = buildValidExceptionContent(joinPoint, feishuNotice);
        }
        if (!Strings.isNullOrEmpty(noticeContent)) {
            // 发送飞书通知
            FeiShuUtil.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL, noticeContent);
            return;
        }
        // 执行额外的参数校验
        noticeContent = buildAnnotationParamsContent(joinPoint, feishuNotice);
        if (!Strings.isNullOrEmpty(noticeContent)) {
            // 发送飞书通知
            FeiShuUtil.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL, noticeContent);
            return;
        }

        try {
            String methodTypeName = feishuNotice.methodTypeName();

            if ("insert".equalsIgnoreCase(methodTypeName)) {
                noticeContent = doInsertLogicAndBuildNoticeContent(joinPoint, feishuNotice);
            }else{
                noticeContent = doLogicAndBuildNoticeContent(joinPoint, feishuNotice);
            }

            // 发送飞书通知
            FeiShuUtil.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL, noticeContent);
        } catch (Exception e) {
            FeiShuUtil.sendMsg(FeiShuTokenHelper.FEI_SHU_TALK_PUSH_CONFIG_UPDATE_NOTICE_URL, "推单系统后台配置变更后发送飞书通知异常");
            log.error("推单系统后台配置变更后发送飞书通知异常", e);
        }
    }

    /**
     * 构建参数校验异常飞书告警内容
     * @param joinPoint
     * @param feishuNotice
     * @return
     */
    private String buildValidExceptionContent(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) {
        // 获取操作类型
        String operationType = getOperationType(feishuNotice.methodTypeName());
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();

        String level1MenuName = feishuNotice.level1MenuName();
        String level2MenuName = feishuNotice.level2MenuName();
        return "【配置变更注解参数校验异常通知】\n" +
                "操作类型: " + operationType + "\n" +
                "配置菜单: " + level1MenuName + "->" + level2MenuName + "\n" +
                "异常内容: @FeishuNotice注解的methodTypeName、level1MenuName、level2MenuName字段未设置" + "\n" +
                "调用方法源: " + methodName;
    }

    /**
     * 构建注解参数校验飞书告警内容
     * @param feishuNotice
     */
    private String buildAnnotationParamsContent(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) {
        // 获取操作类型
        String operationType = getOperationType(feishuNotice.methodTypeName());
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getName();

        String level1MenuName = feishuNotice.level1MenuName();
        String level2MenuName = feishuNotice.level2MenuName();

        String methodTypeName = feishuNotice.methodTypeName();


        String errMsg = "";
        if ("insert".equalsIgnoreCase(methodTypeName)) {
            if (Strings.isNullOrEmpty(feishuNotice.configNameFieldName())) {
                errMsg = "@FeishuNotice注解的入参中规则配置名称字段configNameFieldName必填";
            }
        } else {
            if (Strings.isNullOrEmpty(feishuNotice.primaryKeyFieldName())) {
                errMsg = "@FeishuNotice注解的入参中主键id字段名primaryKeyFieldName必填";
            }

            if (Strings.isNullOrEmpty(feishuNotice.tableName())) {
                errMsg = "@FeishuNotice注解的入参中实体表名字段名tableName必填";
            }

            if (Objects.isNull(feishuNotice.mapperClass())
                    || feishuNotice.mapperClass().equals(void.class)) {
                errMsg = "@FeishuNotice注解的持久层Mapper类参数必填";
            }

            if (Strings.isNullOrEmpty(feishuNotice.mapperBeanName())) {
                errMsg = "@FeishuNotice注解的持久层Mapper类的beanName参数必填";
            }

            if (Strings.isNullOrEmpty(feishuNotice.configNameFieldNameFromEntity())) {
                errMsg = "@FeishuNotice注解的实体类中规则配置名称字段名configNameFieldNameFromEntity必填";
            }
        }
        if (Strings.isNullOrEmpty(errMsg)) {
            return null;
        } else {
            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("【配置变更注解参数校验异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            content.append("调用方法源: ").append(methodName);
            return content.toString();
        }

    }

    /**
     * 新增配置时，获取配置规则名称和配置业务线id，并构建飞书通知内容
     * @param joinPoint
     * @param feishuNotice
     * @return
     * @throws Exception
     */
    private String doInsertLogicAndBuildNoticeContent(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) {
        // 获取操作类型
        String operationType = getOperationType(feishuNotice.methodTypeName());

        String level1MenuName = feishuNotice.level1MenuName();
        String level2MenuName = feishuNotice.level2MenuName();
        // 构建消息内容
        StringBuilder content = new StringBuilder();

        //入参中创建人账号id
        String createAccountId = this.getCreateAccountIdFromRequest(joinPoint, feishuNotice, feishuNotice.createAccountIdFieldName());
        if (Strings.isNullOrEmpty(createAccountId)) {
            String errMsg = "新增配置时获取创建人账号异常，注解设置的入参中创建人账号id字段名：".concat(feishuNotice.createAccountIdFieldName()).concat("\n")
                    .concat("实际入参：").concat(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }

        //获取创建人账号信息
        IopAccountResp<IopAccountResp.IopAccount> iopAccountResp = null;
        try {
            iopAccountResp = iopAccountApi.getUserInfoByAccountId(new IopGetInfoListByAccountIdReq(Long.valueOf(createAccountId)));
        } catch (Exception e) {
            log.warn("调用智能运营获取账号信息失败！", e);
            String errMsg = "调用智能运营获取账号信息失败!账号id：".concat(createAccountId).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        if (Objects.isNull(iopAccountResp)
                || Objects.isNull(iopAccountResp.getRetData())
                || Strings.isNullOrEmpty(iopAccountResp.getRetData().getChineseName())) {
            String errMsg = "操作人账号不存在!账号id：".concat(createAccountId).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        String createAccountName = iopAccountResp.getRetData().getChineseName();


        //入参中获取配置规则名称
        String configName = this.getConfigNameFromRequest(joinPoint, feishuNotice, feishuNotice.configNameFieldName());


        if (Strings.isNullOrEmpty(configName)) {

            String errMsg = "新增配置时获取配置规则名称异常，注解设置的入参中规则配置名称字段名：".concat(feishuNotice.configNameFieldName()).concat("\n")
                    .concat("实际入参：").concat(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        //入参中获取配置业务线id
        Integer businessLineId = this.getBusinessLineIdFromRequest(joinPoint, feishuNotice, feishuNotice.businessLineIdFieldName());
        String businessLineName = "";
        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 1) {
                businessLineName = "企业成品业务线";
            }else if (businessLineId == 2) {
                businessLineName = "家庭业务线（原APP）";
            } else if (businessLineId == 999) {
                businessLineName = "家庭业务线（新APP）";
            }
        }

        content.append("【配置变更通知】\n");
        content.append("操作类型: ").append(operationType).append("\n");
        content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
        content.append("规则名称: ").append(configName).append("\n");
        content.append("业务线: ").append(businessLineName).append("\n");
        content.append("操作人: ").append(createAccountName).append("\n");
        content.append("操作时间: ").append(DateFormatterUtil.timeStampToTime(System.currentTimeMillis())).append("\n");
        content.append("配置参数内容: ").append(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));

        return content.toString();

    }

    /**
     * 启用或删除配置时，获取配置规则名称和配置业务线id，并构建飞书通知内容
     * 更新配置时，保存历史配置，并构建飞书通知内容
     * @param joinPoint
     * @param feishuNotice
     * @return
     * @throws Exception
     */
    private String doLogicAndBuildNoticeContent(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice) throws Exception {
        // 获取操作类型
        String operationType = getOperationType(feishuNotice.methodTypeName());

        String level1MenuName = feishuNotice.level1MenuName();
        String level2MenuName = feishuNotice.level2MenuName();
        // 构建消息内容
        StringBuilder content = new StringBuilder();

        //入参中获取配置规则名称
        Object primaryKey = this.getPrimaryKey(joinPoint, feishuNotice, feishuNotice.primaryKeyFieldName());


        if (Objects.isNull(primaryKey)) {

            String errMsg = operationType.concat("配置时获取主键id异常，注解设置的入参中主键id字段名：").concat(feishuNotice.primaryKeyFieldName()).concat("\n")
                    .concat("实际入参：").concat(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }

        // 获取Mapper实例
        Mapper<?> mapper = getMapperInstance(feishuNotice);
        if (Objects.isNull(mapper)) {

            String errMsg = operationType.concat("配置时无法获取Mapper实例，未指定mapperClass或mapperBeanName，表名：")
                    .concat(feishuNotice.tableName()).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }

        // 根据主键id查询配置
        Object entity = mapper.selectByPrimaryKey(primaryKey);

        if (Objects.isNull(entity)) {

            String errMsg = operationType.concat("配置时通过主键id获取配置为null，主键id:").concat(primaryKey.toString()).concat("\n")
                    .concat("表名：").concat(feishuNotice.tableName()).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }


        String updateAccountId = this.getUpdateAccountIdFromEntity(entity, feishuNotice, feishuNotice.updateAccountIdFieldName());
        if (Strings.isNullOrEmpty(updateAccountId)) {
            String errMsg = operationType.concat("配置时获取更新人账号异常，注解设置的入参更新人账号id字段名：").concat(feishuNotice.updateAccountIdFieldName()).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        //获取创建人账号信息
        IopAccountResp<IopAccountResp.IopAccount> iopAccountResp = null;
        try {
            iopAccountResp = iopAccountApi.getUserInfoByAccountId(new IopGetInfoListByAccountIdReq(Long.valueOf(updateAccountId)));
        } catch (Exception e) {
            log.warn("调用智能运营获取账号信息失败！", e);
            String errMsg = "调用智能运营获取账号信息失败!账号id：".concat(updateAccountId).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        if (Objects.isNull(iopAccountResp)
                || Objects.isNull(iopAccountResp.getRetData())
                || Strings.isNullOrEmpty(iopAccountResp.getRetData().getChineseName())) {
            String errMsg = "操作人账号不存在!账号id：".concat(updateAccountId).concat("\n");
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }
        String updateAccountName = iopAccountResp.getRetData().getChineseName();

        //实体中获取配置名称
        String configName = this.getConfigNameFromEntity(entity, feishuNotice, feishuNotice.configNameFieldNameFromEntity());

        if (Strings.isNullOrEmpty(configName)) {
            String errMsg = operationType.concat("配置时通过实体类中名称字段名获取实际名称值异常，主键id:").concat(primaryKey.toString()).concat("\n")
                    .concat("注解设置的实体类中名称字段名: ").concat(feishuNotice.configNameFieldNameFromEntity()).concat("\n")
                    .concat("表名：").concat(feishuNotice.tableName());
            content.append("【配置变更AOP逻辑异常通知】\n");
            content.append("操作类型: ").append(operationType).append("\n");
            content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
            content.append("异常内容: ").append(errMsg).append("\n");
            return content.toString();
        }

        //实体中获取配置业务线id
        Integer businessLineId = this.getBusinessLineIdFromEntity(entity, feishuNotice, feishuNotice.businessLineIdFieldNameFromEntity());
        String businessLineName = "";
        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 1) {
                businessLineName = "企业成品业务线";
            }else if (businessLineId == 2) {
                businessLineName = "家庭业务线（原APP）";
            } else if (businessLineId == 999) {
                businessLineName = "家庭业务线（新APP）";
            }
        }

        content.append("【配置变更通知】\n");
        content.append("操作类型: ").append(operationType).append("\n");
        content.append("配置菜单: ").append(level1MenuName).append("->").append(level2MenuName).append("\n");
        content.append("规则名称: ").append(configName).append("\n");
        content.append("业务线: ").append(businessLineName).append("\n");
        content.append("操作人: ").append(updateAccountName).append("\n");
        content.append("操作时间: ").append(DateFormatterUtil.timeStampToTime(System.currentTimeMillis())).append("\n");
        content.append("配置参数内容: ").append(JSONUtil.toJsonStr(joinPoint.getArgs()[0]));

        if ("update".equalsIgnoreCase(feishuNotice.methodTypeName())) {
            //更新配置时，保存历史配置快照

            PushConfigUpdateLog log = new PushConfigUpdateLog();
            log.setTableName(feishuNotice.tableName());
            log.setConfigName(configName);
            log.setCreateTime(new Date());
            log.setConfigDetail(JSONUtil.toJsonStr(entity));
            if (primaryKey instanceof Integer) {
                log.setPrimaryKeyId(((Integer) primaryKey).longValue());
            } else if (primaryKey instanceof Long){
                log.setPrimaryKeyId((Long) primaryKey);
            }

            pushConfigUpdateLogMapper.insert(log);
        }

        return content.toString();

    }

    @SuppressWarnings("unchecked")
    private Mapper<?> getMapperInstance(FeishuNotice feishuNotice) {
        try {
            // 优先使用mapperClass
            if (feishuNotice.mapperClass() != void.class) {
                // 获取类名的首字母小写作为beanName
                String simpleName = feishuNotice.mapperClass().getSimpleName();
                String beanName = StringUtils.uncapitalize(simpleName);
                return (Mapper<?>) applicationContext.getBean(beanName);
            }

            // 其次使用mapperBeanName
            if (!StringUtils.isEmpty(feishuNotice.mapperBeanName())) {
                return (Mapper<?>) applicationContext.getBean(feishuNotice.mapperBeanName());
            }

            log.error("无法获取Mapper实例，未指定mapperClass或mapperBeanName");
            return null;
        } catch (Exception e) {
            throw new IllegalArgumentException("获取Mapper实例失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据配置名称字段名称获取实际的配置名称
     * @param joinPoint
     * @param feishuNotice
     * @param configNameFieldName
     *
     * @return
     * @throws Exception
     */
    private String getConfigNameFromRequest(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice, String configNameFieldName) {
        if (!"insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(configNameFieldName)) {
            return null;
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 尝试从第一个参数中获取配置名称字段值
        try {
            Object paramObj = args[0];
            Field field = paramObj.getClass().getDeclaredField(configNameFieldName);
            field.setAccessible(true);
            Object value = field.get(paramObj);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("无法从参数中获取配置名称字段[{}]的值，使用注解中的默认值",
                    configNameFieldName, e);
            return null;
        }
    }

    /**
     * 入参中获取创建人账号id
     * @param joinPoint
     * @param feishuNotice
     * @param createAccountIdFieldName
     * @return
     */
    private String getCreateAccountIdFromRequest(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice, String createAccountIdFieldName) {
        if (!"insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(createAccountIdFieldName)) {
            return null;
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 尝试从第一个参数中获取配置名称字段值
        try {
            Object paramObj = args[0];
            Field field = paramObj.getClass().getDeclaredField(createAccountIdFieldName);
            field.setAccessible(true);
            Object value = field.get(paramObj);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("无法从参数中获取创建人账号id字段[{}]的值，使用注解中的默认值",
                    createAccountIdFieldName, e);
            return null;
        }
    }

    /**
     * 根据业务线id字段名获取实际的业务线id
     * @param joinPoint
     * @param feishuNotice
     * @param businessLineIdFieldName
     * @return
     * @throws Exception
     */
    private Integer getBusinessLineIdFromRequest(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice, String businessLineIdFieldName) {
        if (!"insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(businessLineIdFieldName)) {
            return null;
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 尝试从第一个参数中获取配置名称字段值
        try {
            Object paramObj = args[0];
            Field field = paramObj.getClass().getDeclaredField(businessLineIdFieldName);
            field.setAccessible(true);
            Object value = field.get(paramObj);
            if (Objects.nonNull(value)) {
                if (value instanceof Integer) {
                    return (Integer) value;
                } else if (value instanceof Long) {
                    return ((Long) value).intValue();
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("无法从参数中获取业务线id字段[{}]的值，使用注解中的默认值",
                    businessLineIdFieldName, e);
            return null;
        }
    }

    /**
     * 根据实体获取实际的业务线id
     * @param entity
     * @param feishuNotice
     * @param businessLineIdFieldNameFromEntity
     * @return
     * @throws Exception
     */
    private Integer getBusinessLineIdFromEntity(Object entity, FeishuNotice feishuNotice, String businessLineIdFieldNameFromEntity) {
        if ("insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(businessLineIdFieldNameFromEntity)) {
            return null;
        }

        try {
            // 获取字段（包括私有字段）
            Field field = entity.getClass().getDeclaredField(businessLineIdFieldNameFromEntity);
            field.setAccessible(true);
            Object value = field.get(entity);
            if (Objects.nonNull(value)) {
                if (value instanceof Integer) {
                    return (Integer) value;
                } else if (value instanceof Long) {
                    return ((Long) value).intValue();
                }
            }

            return null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("无法从实体中获取业务线id字段[{}]的值字段值", businessLineIdFieldNameFromEntity, e);
            return null;
        }
    }

    /**
     * 实体中获取更新人账号id
     * @param entity
     * @param feishuNotice
     * @param updateAccountIdFieldNameFromEntity
     * @return
     */
    private String getUpdateAccountIdFromEntity(Object entity, FeishuNotice feishuNotice, String updateAccountIdFieldNameFromEntity) {
        if ("insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(updateAccountIdFieldNameFromEntity)) {
            return null;
        }

        try {
            // 获取字段（包括私有字段）
            Field field = entity.getClass().getDeclaredField(updateAccountIdFieldNameFromEntity);
            field.setAccessible(true);
            Object value = field.get(entity);
            return value != null ? value.toString() : null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("无法从实体中获取更新人账号id字段[{}]的值字段值", updateAccountIdFieldNameFromEntity, e);
            return null;
        }
    }

    /**
     * 根据实体获取实际的配置名称
     * @param entity
     * @param feishuNotice
     * @param configNameFieldNameFromEntity
     * @return
     */
    private String getConfigNameFromEntity(Object entity, FeishuNotice feishuNotice, String configNameFieldNameFromEntity) {
        if ("insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(configNameFieldNameFromEntity)) {
            return null;
        }

        try {
            // 获取字段（包括私有字段）
            Field field = entity.getClass().getDeclaredField(configNameFieldNameFromEntity);
            field.setAccessible(true);
            Object value = field.get(entity);
            return value != null ? (String) value : null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("无法从实体中获取配置规则字段[{}]的值字段值", configNameFieldNameFromEntity, e);
            return null;
        }
    }

    /**
     * 根据主键id字段名获取实际的主键id
     * @param joinPoint
     * @param feishuNotice
     * @param primaryKeyFieldName
     * @return
     * @throws Exception
     */
    private Object getPrimaryKey(ProceedingJoinPoint joinPoint, FeishuNotice feishuNotice, String primaryKeyFieldName) throws Exception {
        if ("insert".equalsIgnoreCase(feishuNotice.methodTypeName())
                || Strings.isNullOrEmpty(primaryKeyFieldName)) {
            return null;
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 尝试从第一个参数中获取配置名称字段值
        try {
            Object paramObj = args[0];
            Field field = paramObj.getClass().getDeclaredField(primaryKeyFieldName);
            field.setAccessible(true);
            Object value = field.get(paramObj);
            if (Objects.nonNull(value)) {
                return value;
            }
            return null;
        } catch (Exception e) {
            log.warn("无法从参数中获取主键id字段[{}]的值，使用注解中的默认值",
                    primaryKeyFieldName, e);
            return null;
        }
    }

    private String getOperationType(String methodTypeName) {
        switch (methodTypeName.toLowerCase()) {
            case "insert":
                return "新增";
            case "update":
                return "更新";
            case "delete":
                return "删除";
            case "enable":
                return "启用/禁用";
            default:
                return methodTypeName;
        }
    }

}
