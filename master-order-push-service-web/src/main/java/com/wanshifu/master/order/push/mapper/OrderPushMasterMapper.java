package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderPushMaster;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 推送师傅记录Mapper
 * <AUTHOR>
 */
public interface OrderPushMasterMapper extends IBaseCommMapper<OrderPushMaster> {

    /**
     * 根据orderId查询推单明细
     * @param tableName
     * @param orderId
     * @return
     */
    List<OrderPushMaster> getOrderPushMasterList(@Param("tableName")String tableName, @Param("orderId")Long orderId);


    /**
     * 查询最小id
     * @param tableName
     * @return
     */
    Long getMinId(@Param("tableName")String tableName);


    /**
     * 查询最大id
     * @param tableName
     * @return
     */
    Long getMaxId(@Param("tableName")String tableName,@Param("pushTimeEnd")Date pushTimeEnd);


    /**
     * 根据范围删除推单明细数据
     * @param minPushId
     * @param maxPushId
     * @return
     */
    Integer rangeDeleteById(@Param("tableName")String tableName,@Param("minPushId")Long minPushId,@Param("maxPushId")Long maxPushId);

}