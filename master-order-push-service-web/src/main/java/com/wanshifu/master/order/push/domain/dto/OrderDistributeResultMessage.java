package com.wanshifu.master.order.push.domain.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class OrderDistributeResultMessage {

    private Long orderId;

    private Integer appointType;

    private List<Long> successMasterList;

    private List<Long> failMasterList;

    private Long appointDetailType;

    private String extraId;

    private List<JSONObject> masterFailVos;

}
