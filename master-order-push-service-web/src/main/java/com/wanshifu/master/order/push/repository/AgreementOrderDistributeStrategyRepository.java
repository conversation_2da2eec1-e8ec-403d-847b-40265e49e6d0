package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.ListRqt;
import com.wanshifu.master.order.push.mapper.AgreementOrderDistributeStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class AgreementOrderDistributeStrategyRepository extends BaseRepository<AgreementOrderDistributeStrategy> {

    @Resource
    private AgreementOrderDistributeStrategyMapper agreementOrderDistributeStrategyMapper;


    /**
     * 触达策略列表
     * @param rqt
     * @return
     */
    public List<AgreementOrderDistributeStrategy> selectList(ListRqt rqt){
        return agreementOrderDistributeStrategyMapper.selectList(rqt);
    }


    public int insert(String strategyName, String strategyDesc, Integer businessLineId, String categoryIds, String cityIds, String distributeStrategyList,
                      String distributeStrategyExpressionList,String compensateDistributeList, Long createAccountId) {
        AgreementOrderDistributeStrategy distributeStrategy = new AgreementOrderDistributeStrategy();
        distributeStrategy.setStrategyName(strategyName);
        distributeStrategy.setStrategyDesc(strategyDesc);
        distributeStrategy.setBusinessLineId(businessLineId);
        distributeStrategy.setCategoryIds(categoryIds);
        distributeStrategy.setCityIds(cityIds);
        distributeStrategy.setDistributeStrategyList(distributeStrategyList);
        distributeStrategy.setDistributeStrategyExpressionList(distributeStrategyExpressionList);
        distributeStrategy.setCompensateDistributeList(compensateDistributeList);
        distributeStrategy.setCompensateDistributeStrategyList("");
        distributeStrategy.setCreateAccountId(createAccountId);
        distributeStrategy.setUpdateAccountId(createAccountId);
        return this.insertSelective(distributeStrategy);
    }

    public Integer update(Integer strategyId,String strategyName, String strategyDesc, Integer businessLineId, String categoryIds, String cityIds, String distributeStrategyList,
                          String distributeStrategyExpressionList, String compensateDistributeList,Long updateAccountId) {
        AgreementOrderDistributeStrategy distributeStrategy = new AgreementOrderDistributeStrategy();
        distributeStrategy.setStrategyId(strategyId);
        distributeStrategy.setStrategyName(strategyName);
        distributeStrategy.setStrategyDesc(strategyDesc);
        distributeStrategy.setBusinessLineId(businessLineId);
        distributeStrategy.setCategoryIds(categoryIds);
        distributeStrategy.setCityIds(cityIds);
        distributeStrategy.setDistributeStrategyList(distributeStrategyList);
        distributeStrategy.setDistributeStrategyExpressionList(distributeStrategyExpressionList);
        distributeStrategy.setCompensateDistributeList(compensateDistributeList);
        distributeStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(distributeStrategy);
    }


    public AgreementOrderDistributeStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Integer strategyId) {
        Example example = new Example(AgreementOrderDistributeStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }
    
    
    public AgreementOrderDistributeStrategy selectByCategoryIdAndCityId(Integer businessLineId, Long categoryId, String cityId){
        return agreementOrderDistributeStrategyMapper.selectByCategoryIdAndCityId(businessLineId,categoryId,cityId);
    }



}
