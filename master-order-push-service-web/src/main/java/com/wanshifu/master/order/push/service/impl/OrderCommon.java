package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DataUtils;
import com.wanshifu.order.offer.api.IkeaOrderResourceApi;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderIkeaGoods;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class OrderCommon {

    @Resource
    private Tools tools;

    @Resource
    private IkeaOrderResourceApi ikeaOrderResourceApi;

    /**
     * 判断订单是否为动态配置爸版本
     *
     * @param orderBase
     * @return
     */
    public boolean isDynamicConfiguration(OrderBase orderBase) {
        return orderBase.getOrderServeVersion() == 1;
    }


    /**
     * 查询宜家订单信息（下沉）
     *
     * @param orderId
     * @param globalOrderTraceId
     * @return
     */
    public List<OrderIkeaGoods> queryOrderIkeaGoods(Long orderId, Long globalOrderTraceId) {
        com.wanshifu.order.offer.domains.api.request.ikeaorder.GetOrderIkeaGoodsRqt rqt = new com.wanshifu.order.offer.domains.api.request.ikeaorder.GetOrderIkeaGoodsRqt();
        rqt.setOrderId(orderId);
        List<com.wanshifu.order.offer.domains.po.OrderIkeaGoods> getOrderIkeaGoods = tools.catchLogThrow(() -> ikeaOrderResourceApi.getOrderIkeaGoods(rqt));
        if (CollectionUtils.isEmpty(getOrderIkeaGoods)) {
            return new ArrayList<>();
        }
        return DataUtils.copyList(OrderIkeaGoods.class, getOrderIkeaGoods);
    }

    /**
     * 判断是否为宜家订单
     *
     * @param orderBase 订单基础信息
     * @return boolean
     */
    public boolean isIkeaOrder(OrderBase orderBase) {
        return orderBase.getOrderFrom().equals(OrderFrom.IKEA.valueEn);
    }



    public boolean isFamilyOrder(Long businessLineId, String orderFrom, String accountType) {
        return businessLineId == 1L && (OrderFrom.APPLET.valueEn.equals(orderFrom) || AccountType.ENTERPRISE.code.equals(accountType));
    }
}
