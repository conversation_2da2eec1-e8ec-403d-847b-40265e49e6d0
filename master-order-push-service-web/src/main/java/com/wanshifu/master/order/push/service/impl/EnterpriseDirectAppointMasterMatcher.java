package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.supply.demand.domain.api.req.GetFenceInfoByDivisionLv3IdsReq;
import com.wanshifu.iop.supply.demand.domain.api.req.HitByOrderInfoReq;
import com.wanshifu.iop.supply.demand.domain.api.resp.GetFenceInfoByDivisionLv3IdsResp;
import com.wanshifu.iop.supply.demand.domain.api.resp.HitByOrderInfoResp;
import com.wanshifu.iop.supply.demand.service.api.InterFenceServiceApi;
import com.wanshifu.master.exclusive.api.ExclusiveMasterOtherApi;
import com.wanshifu.master.exclusive.domains.api.request.other.GetRecruitIdsRqt;
import com.wanshifu.master.exclusive.domains.api.response.other.GetRecruitIdsResp;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AgreementMasterLabel;
import com.wanshifu.master.order.push.domain.enums.RecruitBusinessEnum;
import com.wanshifu.master.order.push.domain.enums.RecruitDistrictMethod;
import com.wanshifu.master.order.push.domain.enums.RecruitPricingType;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.service.PushHandler;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.GeoValidationMethod;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 品牌师傅匹配器
 * <AUTHOR>
 */
@Slf4j
@Component("enterprise_appoint")
public class EnterpriseDirectAppointMasterMatcher extends AbstractOrderMasterMatcher{


    @Resource
    private ExclusiveMasterOtherApi exclusiveMasterOtherApi;


    @Resource
    private OrderServeInfoEsRepository orderServeInfoEsRepository;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;



    @Value("${new.model.master.nearby.distance}")
    private Integer nearbyDistance;



    @Value("${new.model.master.waitDoorIn.orderCnt:7}")
    private Integer waitDoorInOrderCnt;

    @Resource
    private HBaseClient hBaseClient;

    @Resource
    private AddressApi addressApi;

    @Resource
    private PushHandler pushHandler;


    @Resource
    private Tools tools;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){
//        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
//            return false;
//        }
//        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
//        if (SymbolConstant.ONE.equals(String.valueOf(emergencyOrderFlag))) {
//            return false;
//        }
//        if (orderDetailData.getBusinessLineId() != 2) {
//            return false;
//        }
//
//        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
//        if (serveIdsArray==null||serveIdsArray.size()==0) {
//            return false;
//        }
//        //总包指派接口
//        if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
//            GetOrderDemandInfoByGlobalIdRqt rqt = new GetOrderDemandInfoByGlobalIdRqt();
//            rqt.setGlobalOrderTraceId(rqt.getGlobalOrderTraceId());
//            rqt.setEnterpriseId(orderDetailData.getAccountId());
//            OrderDemandInfo orderDemandInfo =
//                    infoQueryApi.getOrderServeMaster(rqt);
//            if (Objects.nonNull(orderDemandInfo) && "master".equals(orderDemandInfo.getToAccountType()) &&
//                    Objects.nonNull(orderDemandInfo.getToAccountId()) && orderDemandInfo.getToAccountId() > 0) {
//                return false;
//            }
//        }
//
//
//        if (!(
//                AccountType.USER.code.equals(orderDetailData.getAccountType())
//                        || AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())
//        )) {
//            return false;
//        }
//        if (orderDetailData.getUserId()==null) {
//            return false;
//        }
//
//        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getOrderFrom())) {
//            return false;
//        }
        return true;
    }






    /**
     * 获取业务范围
     * @param orderDetailData
     * @return
     */
    public List<String> getCooperationBusiness(OrderDetailData orderDetailData) {

        String accountType = orderDetailData.getAccountType();
        if (StrUtil.isEmpty(accountType)) {
            return null;
        }
        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getAccountType())) {
            return null;
        }
        Integer businessLineId = orderDetailData.getBusinessLineId();
        if(businessLineId != 1 && businessLineId != 2){
            return null;
        }
        return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.ENTERPRISE.code);
    }



    /**
     * 匹配样板城市师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<AgreementMaster> matchAgreementMasterByCondition(OrderDetailData orderDetailData,Boolean isBrandAgreementMaster,MasterMatchCondition masterMatchCondition){

        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 1.合作业务
//        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationBusiness",cooperationBusinessList.get(0)));

        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        if(isBrandAgreementMaster){
            boolQueryBuilder.must(QueryBuilders.termQuery("tagName", AgreementMasterLabel.BRAND.getCode()));
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName",AgreementMasterLabel.BRAND.getCode()));
        }

        if(masterMatchCondition.getDivisionMatchLevel() == 4){
            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getFourthDivisionId())));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
        }

        if(Objects.nonNull(orderDetailData.getUserId()) && orderDetailData.getUserId() > 0){
            BoolQueryBuilder userBoolQueryBuilder = QueryBuilders.boolQuery();
            userBoolQueryBuilder.should(QueryBuilders.termsQuery("userIds", Collections.singletonList(orderDetailData.getUserId())));
            BoolQueryBuilder userNotExistQueryBuilder = QueryBuilders.boolQuery();
            userNotExistQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
            userBoolQueryBuilder.should(userNotExistQueryBuilder);
            userBoolQueryBuilder.minimumShouldMatch(1);
            boolQueryBuilder.must(userBoolQueryBuilder);
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }


        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));


        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));

        boolQueryBuilder.must(QueryBuilders.existsQuery("masterPriceType"));



        //4.合作服务
        if(CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())){
            return null;
        }
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));

        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        log.info("search enterprise direct appoint brand master request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }


        log.info("orderDetailData: " +  JSON.toJSONString(orderDetailData));

        agreementMasterList = filterExclusiveMaster(orderDetailData,agreementMasterList);

        agreementMasterList = filterStatusAbnormalMaster(agreementMasterList);

        agreementMasterList = filterCooperationBusinessEndMaster(orderDetailData,agreementMasterList);

        log.info("after filterStatusAbnormalMaster agreementMasterList:" + JSON.toJSONString(agreementMasterList));


        agreementMasterList = this.filterByDailyOrderCnt(agreementMasterList);


        log.info("after filterByDailyOrderCnt agreementMasterList:" + JSON.toJSONString(agreementMasterList));



        return agreementMasterList;
    }



    private List<AgreementMaster> filterExclusiveMaster(OrderDetailData orderDetailData,List<AgreementMaster> agreementMasterList){
        List<Long> exclusiveMasterIdList = orderDetailData.getPushExtraData().getOrderPushEliminateMasterIds();

        if(CollectionUtils.isNotEmpty(exclusiveMasterIdList) && CollectionUtils.isNotEmpty(agreementMasterList)){
            agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> !exclusiveMasterIdList.contains(agreementMaster.getMasterId())).collect(Collectors.toList());
        }

        return agreementMasterList;

    }





    private List<AgreementMasterExtra> matchAgreementMaster(OrderDetailData orderDetailData, Boolean isBrandAgreementMaster, MasterMatchCondition masterCondition){

        //匹配品牌协议师傅
        List<AgreementMaster> agreementMasterList = matchAgreementMasterByCondition(orderDetailData,isBrandAgreementMaster,masterCondition);

        Integer matchType = 1;
        if(masterCondition.getDivisionMatchLevel() == 3){
            matchType = 3;
            if(CollectionUtils.isEmpty(agreementMasterList)){
                return null;
            }
            levelThreeDivisionFilter(agreementMasterList,masterCondition);
        }else if(masterCondition.getDivisionMatchLevel() == 4 && CollectionUtils.isEmpty(agreementMasterList)){
            matchType = 3;
            masterCondition.setDivisionMatchLevel(3);
            agreementMasterList = matchAgreementMasterByCondition(orderDetailData,isBrandAgreementMaster,masterCondition);
            if(CollectionUtils.isNotEmpty(agreementMasterList)){
                //TODO 订单上门地址和师傅三级地址下的街道中心区的距离
            }
        }

        Integer type;
        if(masterCondition.getDivisionMatchLevel() == 4){
            type = 1;
        }else{
            type = 2;
        }

        final Integer finalMatchType = matchType;

        List<AgreementMasterExtra> enterpriseAgreementMasterList = BeanCopyUtil.copyListProperties(agreementMasterList, AgreementMasterExtra.class, (s, t) -> {
            t.setType(type);
            t.setMatchType(finalMatchType);
        });


        return enterpriseAgreementMasterList;
    }

    /**
     * 校验城市电子围栏开启状态
     * @param cityDivisionId
     * @return
     */
    private boolean checkCityGeoFence(Long cityDivisionId){

        if(!apolloConfigUtils.isOpenAgreementGeoFence()){
            return false;
        }


        GetFenceInfoByDivisionLv3IdsReq req = new GetFenceInfoByDivisionLv3IdsReq();
        List<Long> cityList = new ArrayList<>();
        cityList.add(cityDivisionId);
        req.setLv3DivisionIds(cityList);
        List<GetFenceInfoByDivisionLv3IdsResp> respList = tools.catchLog(() -> interFenceServiceApi.getInfoByDivisionLv3Id(req),"interFenceServiceApi.getInfoByDivisionLv3Id",req);
        if(CollectionUtils.isNotEmpty(respList)){
            return "open".equals(respList.get(0).getOpenStatus());
        }
        return false;
    }

    private List<AgreementMaster> filterByDailyOrderCnt(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = agreementMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("recruit_id");
        fieldColumnList.add("order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList,fieldColumnList,"master_recruit_daily");
        Map<String,Integer> orderCntMap = new HashMap<>();

        if(Objects.nonNull(resultArray) && resultArray.size() > 0){
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String)jsonObject.get("master_id");
                String recruitId = (String)jsonObject.get("recruit_id");
                if(jsonObject.containsKey("order_cnt")){
                    String orderCnt = (String)jsonObject.get("order_cnt");
                    if(org.apache.commons.lang.StringUtils.isNotBlank(orderCnt)){
                        orderCntMap.put(masterId + "_" + recruitId,Integer.valueOf(orderCnt));
                    }
                }
            }
        }


        return agreementMasterList.stream().filter(agreementMaster -> agreementMaster.getMaxDailyOrder() == 0  || orderCntMap.getOrDefault(agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId(),0) < agreementMaster.getMaxDailyOrder()).collect(Collectors.toList());
    }


    @Resource
    private InterFenceServiceApi interFenceServiceApi;

    /**
     * 匹配电子围栏协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @param cooperationBusinessList
     * @return
     */
    private List<AgreementMasterExtra> matchAgreementMasterByGeoFence(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,
                                                                      List<String> cooperationBusinessList){

        if(org.apache.commons.lang.StringUtils.isBlank(orderDetailData.getOrderLngLat())){
            return null;
        }

        if(Objects.isNull(orderDetailData.getFourthDivisionId()) || orderDetailData.getFourthDivisionId() == 0){
            return null;
        }

        //根据订单经纬度识别电子围栏
        HitByOrderInfoReq hitByOrderInfoReq = new HitByOrderInfoReq();
        hitByOrderInfoReq.setServeId(Long.valueOf(orderDetailData.getLv1ServeIds()));
        hitByOrderInfoReq.setLatitude(orderDetailData.getOrderLngLat());
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        hitByOrderInfoReq.setLatitude(latLngArray[0]);
        hitByOrderInfoReq.setLongitude(latLngArray[1]);
        hitByOrderInfoReq.setLv3DivisionId(orderDetailData.getSecondDivisionId());
        HitByOrderInfoResp hitByOrderInfoResp = tools.catchLog(() -> interFenceServiceApi.hitByOrderInfo(hitByOrderInfoReq),"interFenceServiceApi.hitByOrderInfo",hitByOrderInfoReq);
        log.info("hitByOrderInfo rqt:" + JSON.toJSONString(hitByOrderInfoReq) + ",resp:" + JSON.toJSONString(hitByOrderInfoResp));
        if(Objects.isNull(hitByOrderInfoResp) || Objects.isNull(hitByOrderInfoResp.getFenceId()) || hitByOrderInfoResp.getFenceId() <= 0){
            return null;
        }

        Long fenceId = hitByOrderInfoResp.getFenceId();

        //根据电子围栏id匹配协议师傅
        return matchAgreementMasterByGeoFenceId(orderDetailData,masterMatchCondition,fenceId,cooperationBusinessList);
    }


    private List<AgreementMasterExtra> matchAgreementMasterByGeoFenceId(OrderDetailData orderDetailData,MasterMatchCondition
            masterMatchCondition,Long geoFenceId, List<String> cooperationBusinessList) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));

        boolQueryBuilder.must(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));


        boolQueryBuilder.must(QueryBuilders.termQuery("geoFenceIds", String.valueOf(geoFenceId)));

        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        boolQueryBuilder.must(QueryBuilders.termQuery("masterCityDivisionId", orderDetailData.getSecondDivisionId()));


        //无指定用户id或下单用户在招募指定用户内
        if(Objects.nonNull(orderDetailData.getUserId()) && orderDetailData.getUserId() > 0){
            BoolQueryBuilder userBoolQueryBuilder = QueryBuilders.boolQuery();
            userBoolQueryBuilder.should(QueryBuilders.termsQuery("userIds", Collections.singletonList(orderDetailData.getUserId())));
            BoolQueryBuilder userNotExistQueryBuilder = QueryBuilders.boolQuery();
            userNotExistQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
            userBoolQueryBuilder.should(userNotExistQueryBuilder);
            userBoolQueryBuilder.minimumShouldMatch(1);
            boolQueryBuilder.must(userBoolQueryBuilder);
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }


        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));

        //4.合作服务
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        log.info("matchAgreementMasterByFenceId searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }


        agreementMasterList = filterExclusiveMaster(orderDetailData,agreementMasterList);


        log.info("matchAgreementMasterByFenceId agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        List<AgreementMasterExtra> enterpriseAgreementMasterList = BeanCopyUtil.copyListProperties(agreementMasterList, AgreementMasterExtra.class, (s, t) -> {
            t.setType(1);
            t.setMatchType(1);
        });

        return enterpriseAgreementMasterList;


    }

    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){


        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);


        List<AgreementMasterExtra> agreementMasterList = null;
        if(checkCityGeoFence(orderDetailData.getSecondDivisionId())){
            agreementMasterList = matchAgreementMasterByGeoFence(orderDetailData,masterMatchCondition,cooperationBusinessList);
        }

        if(CollectionUtils.isEmpty(agreementMasterList)){

            setDivisionMatchLevel(orderDetailData,masterMatchCondition);

            //匹配标签为“品牌”的协议师傅
            agreementMasterList = matchAgreementMaster(orderDetailData,true,masterMatchCondition);
        }

        log.info("agreementMasterList:" + JSON.toJSONString(agreementMasterList));


        log.info("after filterStatusAbnormalMaster agreementMasterList:" + JSON.toJSONString(agreementMasterList));
        if(CollectionUtils.isEmpty(agreementMasterList)){
            setDivisionMatchLevel(orderDetailData,masterMatchCondition);
            agreementMasterList = matchAgreementMaster(orderDetailData,false,masterMatchCondition);
        }



        Set<String> masterSet = null;
        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            masterSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        }


        MatchMasterResult masterResult = new MatchMasterResult(masterSet);
        masterResult.putExtraData("agreement_master_list",agreementMasterList);
        log.info("masterResult:" + JSON.toJSONString(masterResult));
        return masterResult;

    }


    private void levelThreeDivisionFilter(List<AgreementMaster> agreementMasterList,MasterMatchCondition masterMatchCondition){
        final Set<String> subDivisionIdList = getSubListByDivisionId(masterMatchCondition.getThirdDivisionId());
        if (CollectionUtils.isEmpty(subDivisionIdList)) {
            log.info("{},subDivision is empty:{},lv3:{},getDivisionMatchLevel:{}"
                    ,masterMatchCondition.getMasterOrderId()
                    ,subDivisionIdList
                    ,masterMatchCondition.getThirdDivisionId()
                    ,masterMatchCondition.getDivisionMatchLevel());
            return;
        }
        log.info("{},subDivision:{},lv3:{},getDivisionMatchLevel:{}"
                ,masterMatchCondition.getMasterOrderId()
                ,subDivisionIdList
                ,masterMatchCondition.getThirdDivisionId()
                ,masterMatchCondition.getDivisionMatchLevel());


        agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> thirdLevelDivisionFullMatch(subDivisionIdList, agreementMaster.getLv3DivisionIds())).collect(Collectors.toList());

    }


    /**
     * 获取地址子集集合
     * @return
     */
    private Set<String> getSubListByDivisionId(Long divisionId){
        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
        final Set<String> divisionList =
                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
        return divisionList;
    }


    /**
     * 三级地址全匹配
     */
    private boolean thirdLevelDivisionFullMatch(
            Set<String> orderFullFourthDivisions,
            String packageFourthLevelDivisions){
        if (org.apache.commons.lang3.StringUtils.isEmpty(packageFourthLevelDivisions)) {
            return false;
        }
        final Set<String> packageFourthDivisionSet
                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());

        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
    }


    public void setDivisionMatchLevel(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){
        if(orderDetailData.getFourthDivisionId() != null &&orderDetailData.getFourthDivisionId() > 0){
            masterMatchCondition.setDivisionMatchLevel(4);
        }else{
            masterMatchCondition.setDivisionMatchLevel(3);
        }
    }


    public List<AgreementMaster> filterCooperationBusinessEndMaster(OrderDetailData orderDetailData,List<AgreementMaster> agreementMasterList){
        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }
        Set<String> masterIdSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        Set<String> finalMasterSet = pushHandler.filterByPushLimitRule(orderDetailData,null,masterIdSet,"enterprise_appoint",null);
        return agreementMasterList.stream().filter(agreementMaster -> finalMasterSet.contains(String.valueOf(agreementMaster.getMasterId()))).collect(Collectors.toList());
    }



    public List<AgreementMaster> filterStatusAbnormalMaster(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }

        Set<String> masterSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        List<AgreementMaster> resultList = new ArrayList<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termQuery("isAccountNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                log.info("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }

            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                Set<String> masterIdSet = masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
                resultList.addAll(agreementMasterList.stream().filter(agreementMasterBase -> masterIdSet.contains(String.valueOf(agreementMasterBase.getMasterId()))).collect(Collectors.toList()));
            }
        }

        return resultList;
    }

    private Set<String> filterRestMaster(Set<String> masterSet){

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        Set<String> resultSet = new HashSet<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termQuery("restState", 1L));

            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                System.out.println("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                resultSet.addAll(masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet()));
            }

        }


        return resultSet;

    }


    /**
     *
     * @param masterSet
     * @param orderDetailData
     */
    private void filterByOrderCnt(Set<String> masterSet,OrderDetailData orderDetailData){

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termsQuery("nextServeNode", "serve_sign_position","reserve_customer","serve_complete"));
            boolQuery.must(QueryBuilders.termQuery("serveStatus", "serving"));
            boolQuery.must(QueryBuilders.termQuery("historyMark", 0L));
            boolQuery.must(QueryBuilders.termQuery("expectedDoorInDate", orderDetailData.getExpectDoorInStartDate()));


            List<OrderServeInfo> orderServeInfoList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<OrderServeInfo> esResponse = orderServeInfoEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                log.info("filterByWaitDoorInOrderCnt boolQuery :"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    orderServeInfoList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(orderServeInfoList)){
                Map<Long, List<OrderServeInfo>> orderServeInfoMap = orderServeInfoList.stream().collect(Collectors.groupingBy(item -> item.getMasterId()));
                for(Long masterId : orderServeInfoMap.keySet()){
                    if(orderServeInfoMap.get(masterId).size() >= waitDoorInOrderCnt){
                        masterSet.remove(String.valueOf(masterId));
                    }
                }
            }
        }

    }


    private String searchNearbyMaster(OrderDetailData orderDetailData,Set<String> availableMainMasterSet){

        if(StringUtils.isBlank(orderDetailData.getOrderLngLat())){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", availableMainMasterSet));

        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
        //设置中心点
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArray[1]),Double.valueOf(latLngArray[0])));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(nearbyDistance * 1000, DistanceUnit.METERS);
        boolQueryBuilder.must(geoDistanceQueryBuilder);

        GeoDistanceSortBuilder distanceSortBuilder = SortBuilders.geoDistanceSort("latLng", Double.valueOf(latLngArray[1]), Double.valueOf(latLngArray[0]));
        distanceSortBuilder.order(SortOrder.ASC);
        distanceSortBuilder.geoDistance(GeoDistance.PLANE);
        distanceSortBuilder.unit(DistanceUnit.METERS);
        distanceSortBuilder.validation(GeoValidationMethod.STRICT);

        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(1,100),Collections.singletonList(distanceSortBuilder));

        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList().get(0).getMasterId();
        }

        return null;

    }

    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }

    private static enum RestrictAction {
        PUSH{
            @Override
            public String getActionString(){
                return "1";
            };
        },LOGIN{
            @Override
            public String getActionString(){
                return "2";
            };
        },EXCLUSIVE{
            @Override
            public String getActionString(){
                return "14";
            };
        },NO_QUOTATION{
            @Override
            public String getActionString(){
                return "3";
            };
        },NO_APPOINT{
            @Override
            public String getActionString(){
                return "4";
            };
        };
        public String getActionString(){
            return null;
        };
    };


    /**
     * 获取招募Id
     * @param thirdDivisionId
     * @param fourthDivisionId
     * @param recruitBusiness
     * @param serveIdArray
     * @param userId
     * @return
     */
    private GetRecruitIdsResp getRecruitIds(
            Long masterOrderId,
            Long thirdDivisionId,
            Long fourthDivisionId,
            String recruitBusiness,
            List<Long> serveIdArray,
            Long userId
    ){

        GetRecruitIdsRqt getRecruitIdsRqt = new GetRecruitIdsRqt();
        getRecruitIdsRqt.setLv3DivisionId(thirdDivisionId);
        getRecruitIdsRqt.setLv4DivisionId(fourthDivisionId);
        getRecruitIdsRqt.setRecruitBusiness(recruitBusiness);
        getRecruitIdsRqt.setServeId(serveIdArray.get(0));
        getRecruitIdsRqt.setUserId(userId);
        getRecruitIdsRqt.setNowTime(new Date());
        final GetRecruitIdsResp getRecruitIdsResp = exclusiveMasterOtherApi.getRecruitIds(getRecruitIdsRqt);
        return getRecruitIdsResp;
    }


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
        return ;
    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){



        return true;
    }







    /**
     * 过滤师傅基础信息
     * @param masterIds
     * @param boolQueryBuilder
     * @return
     */
    private Set<String> matchMasterBaseInfoBySet(Set<String> masterIds,BoolQueryBuilder boolQueryBuilder){

        if (Objects.nonNull(boolQueryBuilder)) {
            return masterIds;
        }
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", masterIds));
        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder);
        if(Objects.isNull(esResponse) && CollectionUtils.isEmpty(esResponse.getDataList())){
            return masterIds;
        }
        return esResponse.getDataList().stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
    }








}
