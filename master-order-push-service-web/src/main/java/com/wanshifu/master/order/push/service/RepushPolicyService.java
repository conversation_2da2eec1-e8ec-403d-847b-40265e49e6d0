package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.RepushPolicy;
import com.wanshifu.master.order.push.domain.rqt.repushPolicy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> xin<PERSON><PERSON>@wshifu.com
 * @date : 2023-02-01 17:11
 */
public interface RepushPolicyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    RepushPolicy detail(DetailRqt rqt);

    int enable(EnableRqt rqt);

    SimplePageInfo<RepushPolicy> list(ListRqt rqt);

    int delete(DeleteRqt rqt);
}