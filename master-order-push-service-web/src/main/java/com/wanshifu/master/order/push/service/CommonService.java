package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import com.wanshifu.master.order.push.domain.rqt.common.*;

import java.util.List;

public interface CommonService {


    SimplePageInfo<MasterQuota> masterQuota(MasterQuotaRqt rqt);


    List<ScoreItem> masterItemList(MasterItemRqt rqt);

    List<MasterQuotaValue> getMasterQuotaValue(GetMasterQuotaValueRqt rqt);


    List<ScoreItemValue> getScoreItemValue(GetScoreItemValueRqt rqt);


    List<ScoreItem> getScoreItemByCodes(GetScoreItemByCodesRqt rqt);


    List<MasterQuota> getMasterQuotaByCodes(GetMasterQuotaByCodesRqt rqt);


}
