package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AgreementMasterPush;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.OrderRefund;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

@Repository
public class AgreementMasterPushRepository extends BaseRepository<AgreementMasterPush> {

    public AgreementMasterPush selectByMasterIdAndRecruitId(Long masterId,Long recruitId){
        AgreementMasterPush agreementMasterPush = new AgreementMasterPush();
        agreementMasterPush.setMasterId(masterId);
        agreementMasterPush.setRecruitId(recruitId);
        return CollectionUtils.getFirstSafety(this.select(agreementMasterPush));
    }


    public AgreementMasterPush selectByAgreementMasterId(String agreementMasterId){
        AgreementMasterPush agreementMasterPush = new AgreementMasterPush();
        agreementMasterPush.setAgreementMasterId(agreementMasterId);
        return CollectionUtils.getFirstSafety(this.select(agreementMasterPush));
    }


    public List<AgreementMasterPush> selectByAgreementMasterIdList(Set<String> agreementMasterIdList){
        Condition condition = new Condition(AgreementMasterPush.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("agreementMasterId", agreementMasterIdList);
        return this.selectByCondition(condition);
    }


}
