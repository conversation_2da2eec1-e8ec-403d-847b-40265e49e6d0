package com.wanshifu.master.order.push.mapper;


import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.ListRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AgreementOrderDistributeStrategyMapper extends IBaseCommMapper<AgreementOrderDistributeStrategy> {


    List<AgreementOrderDistributeStrategy> selectList(ListRqt rqt);


    AgreementOrderDistributeStrategy selectByCategoryIdAndCityId(@Param("businessLineId") Integer businessLineId,@Param("categoryId")  Long categoryId, @Param("cityId") String cityId);



    }