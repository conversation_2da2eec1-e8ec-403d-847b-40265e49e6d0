package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.GetOrderDistributeStrategyListRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface OrderDistributeStrategyMapper extends IBaseCommMapper<OrderDistributeStrategy> {

    List<OrderDistributeStrategy> selectList(GetOrderDistributeStrategyListRqt rqt);

    List<OrderDistributeStrategy> selectStrategy(GetOrderDistributeStrategyListRqt rqt);

    OrderDistributeStrategy selectByCityAndCategoryId(@Param("distributeType")String distributeType,@Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Long strategyId, @Param("businessLineId") Long businessLineId);

}
