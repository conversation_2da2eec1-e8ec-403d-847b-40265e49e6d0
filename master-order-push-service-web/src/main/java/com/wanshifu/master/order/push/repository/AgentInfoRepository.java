package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.mapper.AgentInfoMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:09
 */
@Repository
public class AgentInfoRepository extends BaseRepository<AgentInfo> {

    @Resource
    private AgentInfoMapper agentInfoMapper;

    public List<AgentInfo> selectList(String agentName,String masterSourceType){
        return agentInfoMapper.selectList(agentName,masterSourceType);
    }
}
