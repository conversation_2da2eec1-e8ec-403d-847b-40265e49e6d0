package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.RepushPolicy;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.mapper.RepushPolicyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 重推机制Repository
 * <AUTHOR>
 */
@Repository
public class RepushPolicyRepository extends BaseRepository<RepushPolicy> {

    @Resource
    private RepushPolicyMapper repushPolicyMapper;

    /**
     * 根据类目和城市查询重推机制
     * @param businessLineId
     * @param categoryId
     * @param cityId
     * @return
     */
    public RepushPolicy selectByCategoryIdAndCityId(String orderFlag,Integer businessLineId, String categoryId, String cityId){
        return CollectionUtils.getFirstSafety(repushPolicyMapper.selectByCategoryIdAndCityId(orderFlag,businessLineId,categoryId,cityId));
    }

    public RepushPolicy selectByPolicyId(Long policyId) {
        RepushPolicy repushPolicy = this.selectByPrimaryKey(policyId);
        Assert.isTrue(repushPolicy != null && Objects.equals(repushPolicy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return repushPolicy;
    }

    public Long insert(String version,String orderFlag, Integer businessLineId, Long snapshotId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
        RepushPolicy repushPolicy = new RepushPolicy();
        repushPolicy.setVersion(version);
        repushPolicy.setOrderFlag(orderFlag);
        repushPolicy.setSnapshotId(snapshotId);
        repushPolicy.setPolicyDesc(policyDesc);
        repushPolicy.setPolicyName(policyName);
        repushPolicy.setBusinessLineId(businessLineId);
        repushPolicy.setCityIds(cityIds);
        repushPolicy.setCategoryIds(categoryIds);
        repushPolicy.setPolicyStatus(CommonConstant.STRATEGY_STATUS_0);
        repushPolicy.setStrategyCombination(strategyCombinationJson);
        repushPolicy.setCreateAccountId(loginUserId);
        repushPolicy.setUpdateAccountId(loginUserId);
        this.insertSelective(repushPolicy);
        return repushPolicy.getPolicyId();
    }

    public int update(String version,String orderFlag,Long policyId, Long snapshotId, Integer businessLineId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
        RepushPolicy repushPolicy = new RepushPolicy();
        repushPolicy.setVersion(version);
        repushPolicy.setOrderFlag(orderFlag);
        repushPolicy.setPolicyId(policyId);
        repushPolicy.setSnapshotId(snapshotId);
        repushPolicy.setPolicyDesc(policyDesc);
        repushPolicy.setPolicyName(policyName);
        repushPolicy.setBusinessLineId(businessLineId);
        repushPolicy.setCityIds(cityIds);
        repushPolicy.setCategoryIds(categoryIds);
        repushPolicy.setStrategyCombination(strategyCombinationJson);
        repushPolicy.setUpdateAccountId(loginUserId);
        return this.updateByPrimaryKeySelective(repushPolicy);
    }

    public int updateStatus(Long policyId, Integer policyStatus,Long updateAccountId) {
        RepushPolicy repushPolicy = new RepushPolicy();
        repushPolicy.setPolicyId(policyId);
        repushPolicy.setPolicyStatus(policyStatus);
        repushPolicy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(repushPolicy);
    }

    public List<RepushPolicy> selectList(String orderFlag,Long businessLineId, List<Long> cityIdList, List<Long> categoryIdList, String policyName, Date createStartTime, Date createEndTime,Integer strategyStatus) {
        Example example = new Example(SortingStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        if (businessLineId != null) {
            criteria.andEqualTo("businessLineId", businessLineId);
        }
        if (StringUtils.isNotBlank(orderFlag)) {
            criteria.andEqualTo("orderFlag", orderFlag);
        }
        if (strategyStatus != null) {
            criteria.andEqualTo("policyStatus", strategyStatus);
        }

        if (CollectionUtils.isNotEmpty(cityIdList)) {
            String cityIdListSql = cityIdList.stream().map(it -> StrUtil.format(" FIND_IN_SET({},city_ids) ", it))
                    .collect(Collectors.joining("OR"));
            criteria.andCondition(StrUtil.format("({})",cityIdListSql));
        }
        if (CollectionUtils.isNotEmpty(categoryIdList)) {
            String categoryIdListSql = categoryIdList.stream().map(it -> StrUtil.format(" FIND_IN_SET({},category_ids) ", it))
                    .collect(Collectors.joining("OR"));
            criteria.andCondition(StrUtil.format("({})",categoryIdListSql));
        }

        if (StringUtils.isNotBlank(policyName)) {
            criteria.andCondition(StrUtil.format("policy_name like '%{}%'", policyName));
        }
        if (createStartTime != null) {
            criteria.andGreaterThanOrEqualTo("createTime", createStartTime);
        }
        if (createEndTime != null) {
            criteria.andLessThanOrEqualTo("createTime", createEndTime);
        }
        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        example.orderBy("updateTime").desc();
        return this.selectByExample(example);
    }

    public int softDeleteByStrategyId(Long policyId) {
        RepushPolicy repushPolicy = new RepushPolicy();
        repushPolicy.setPolicyId(policyId);
        repushPolicy.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return this.updateByPrimaryKeySelective(repushPolicy);
    }

    public RepushPolicy selectByCityAndCategory(String orderFlag, List<String> cityIdList, List<String> categoryIdList, Long policyId, Integer businessLineId) {
        return repushPolicyMapper.selectByCityAndCategory(orderFlag,CollectionUtils.isEmpty(cityIdList) ? "" : String.join(",", cityIdList), categoryIdList, policyId,businessLineId);
    }
}