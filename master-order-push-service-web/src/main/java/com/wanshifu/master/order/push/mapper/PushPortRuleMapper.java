package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.Enterprise;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 推单端口规则
 * <AUTHOR>
 */
public interface PushPortRuleMapper extends IBaseCommMapper<PushPortRule> {


    List<PushPortRule> selectList(@Param("lv1ServeId") Long lv1ServeId, @Param("orderTag") String orderTag,
                                      @Param("appointType")Integer appointType);


    List<PushPortRule> selectByCityAndLv1ServeId(@Param("orderTagList")List<String> orderTagList,@Param("cityIdList")  List<String> cityIdList,
                                           @Param("lv1ServeIdList") List<String> lv1ServeIdList,
                                           @Param("appointTypeList")List<String> appointTypeList,@Param("ruleId") Integer ruleId);


}