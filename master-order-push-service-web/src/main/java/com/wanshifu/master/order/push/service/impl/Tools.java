package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.UtilsRuntimeException;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.TransmittableContext;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 工具
 *
 * <AUTHOR>
 */
@Service
public class Tools {
    private static final Logger logger = LoggerFactory.getLogger(Tools.class);


    @Resource
    private Environment environment;

    /**
     * 失效时间
     */
    private static final int DEFAULT_EXPIRE_TIME = 2000;


    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;


    /**
     * 飞书群机器人签名
     */
    @Value("${fei_shu_robot_notify_sign}")
    private String feiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${fei_shu_robot_notify_url}")
    private String feiShuRobotNotifyUrl;



    /***
     * 误报错不给提示
     * @param supplier
     * @param <T>
     * @return
     */
    public <T> T catchNoLog(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
//            logger.error("Tools,catchLog", e);
        }
        return null;
    }

    /**
     * 误报错不给提示, 返回默认值
     *
     * @param supplier
     * @return T
     * @author: ZhiHao
     * @date: 2022/2/23
     */
    public <T> T catchNoLog(Supplier<T> supplier, T defaultValue) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog", e);
        }
        return defaultValue;
    }


    /**
     * 捕获调用第三方接口异常，并记录日志, 不抛异常
     *
     * @param supplier 处理程序
     * @param <T>      返回值类型
     * @return 返回结果
     */
    public <T> T catchLog(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog=", e);
        }
        return null;
    }

    /**
     * 第三方接口异常，并记录日志, 不抛异常,钉钉群通知
     *
     * @param supplier      处理程序
     * @param requestParams 请求参数
     * @param <T>           返回结果类型
     */
    public <T> T catchLog(Supplier<T> supplier, String methodName, Object requestParams) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog methodName={} requestParams={}", methodName, requestParams, e);
            feiShuRobotNotifyUtils.sendRichTextNotice(feiShuRobotNotifyUrl,feiShuRobotNotifySign,"第三方接口调用异常", String.format("【接口名】：%s\n\n【接口参数】%s\n\n【错误原因】：%s",
                    methodName, requestParams == null ? "" : JSON.toJSONString(requestParams), e));
        }
        return null;
    }



    /**
     * http请求参数设置仓库
     */
    public static ClientHttpRequestFactory clientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setReadTimeout(2000);
        clientHttpRequestFactory.setConnectTimeout(5000);

        return clientHttpRequestFactory;
    }

    /**
     * post http,现在只有一个接口用简单封装了下如有其它类型，可继续扩展
     *
     * @param url
     * @param postParam
     * @param <T>
     * @return
     */
    public <T> Map<String, Object> commonRequest(String url, T postParam) {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory());
        HttpEntity<T> entity = new HttpEntity<>(postParam);
        ResponseEntity<Map> resp;
        try {
            resp = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);
        } catch (Exception e) {
            logger.error("Tools..commonRequestError..msg={}", e.getMessage());
            throw new BusException(e.getMessage());
        }

        if (null == resp || null == resp.getBody()) {
            logger.error("Tools..commonRequestError..repose null");
            throw new BusException(url + "，请求结果null");
        }
        Map<String, Object> body = resp.getBody();
        String status = (String) body.get("status");
        if ("F".equals(status)) {
            Object msg = body.get("msg");
            logger.warn("Tools..commonRequestError.. msg={}", msg);
            throw new BusException(url + "，" + msg);
        }
        return resp.getBody();
    }


    /**
     * 捕获调用第三方接口异常,记录日志并抛出异常
     *
     * @param supplier 处理程序
     * @param <T>      返回结果类型
     */
    public <T> T catchLogThrow(Supplier<T> supplier) {
        T t;
        try {
            t = supplier.get();
        } catch (Exception e) {
            logger.error("Tools..catchLogThrow..", e);
            throw new BusException(e.getMessage());
        }
        return t;
    }

    /**
     * 判断是否为生成环境
     *
     * @return
     */
    public Boolean isProEnv() {
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            String env = activeProfiles[0];
            return env.equals("prod");
        }
        return false;
    }

    public Boolean isTestEnv() {
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            String env = activeProfiles[0];
            return env.equals("test");
        }
        return false;

    }


    /**
     * 获取当前环境对应字符串
     *
     * @return java.lang.String
     * @author: ZhiHao
     * @date: 2022/4/2
     */
    public String getProEnvStr() {
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            String env = activeProfiles[0];
            return env;
        }
        return "";
    }

    /***
     * 是否为压测流量
     * @return true: 是  false： 否
     */
    public static boolean isPressureBeta() {
        return TransmittableContext.isPtScene();
    }




    public static <T> List<T> copyList(Class<T> clazz, List<?> source) {
        List<T> result = Collections.emptyList();
        if (source != null && source.size() > 0) {
            result = new ArrayList<T>();
            try {
                for (Object object : source) {
                    T target = clazz.newInstance();
                    BeanUtil.copyProperties(object, target);
                    result.add(target);
                }
            } catch (Exception e) {
                throw new UtilsRuntimeException();
            }
        }
        return result;
    }

    /**
     * 捕获调用第三方接口异常,记录日志、钉钉通知, 并抛出异常
     *
     * @param supplier
     * @param methodName
     * @param requestParams
     * @return T
     * @author: ZhiHao
     * @date: 2021/11/12
     */
    public <T> T catchLogThrow(Supplier<T> supplier, String methodName, Object requestParams) {
        T t;
        try {
            t = supplier.get();
        } catch (Exception e) {
            logger.error("Tools,catchLog methodName={} requestParams={}", methodName, requestParams, e);
//            feiShuRobotNotifyUtils.sendRichTextNotice("第三方接口调用异常", String.format("【接口名】：%s \n\n【接口参数】%s\n\n【错误原因】：%s",
//                    methodName, requestParams, e.toString()));
            throw new BusException(e.getMessage());
        }
        return t;
    }
}
