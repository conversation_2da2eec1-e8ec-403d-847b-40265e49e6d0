package com.wanshifu.master.order.push.api.resp;

import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.service.impl.OrderDistributor;
import lombok.Data;

import java.util.List;

@Data
public class MatchOrderDistributeStrategyResp {

    private OrderDistributeStrategy orderDistributeStrategy;

    private List<OrderSelectStrategy> orderSelectStrategyList;

    private List<OrderScoringStrategy> orderScoringStrategyList;

    private List<OrderDistributor.CompensateDistributeDTO> compensateDistributeList;
}
