package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "t_enterprise")
public class Enterprise {

    @Id
    @Column(name = "enterprise_id")
    private String enterpriseId;

    @Column(name = "order_package_rule")
    private String orderPackageRule;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
