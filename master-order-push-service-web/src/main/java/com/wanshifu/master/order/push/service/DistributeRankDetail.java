//package com.wanshifu.master.order.push.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.wanshifu.master.order.push.service.impl.OrderDetail;
//import com.wanshifu.util.LoCollectionsUtil;
//
//import java.math.BigDecimal;
//import java.util.HashSet;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @description
// * @date 2025/2/28 15:48
// */
//public class DistributeRankDetail extends OrderDetail {
//
//
//    private Set<String> masterSet;
//
//    private JSONObject rankDetailJSON;
//    private JSONObject filterDetailJSON;
//    private JSONObject scorerDetailJSON;
//
//    private DistributeRankDetail() {
//    }
//
//    private void setFilterDetailJSON(JSONObject filterDetailJSON) {
//        this.filterDetailJSON = filterDetailJSON;
//    }
//
//    private void setScorerDetailJSON(JSONObject scorerDetailJSON) {
//        this.scorerDetailJSON = scorerDetailJSON;
//    }
//
//    private void setRankDetailJSON(JSONObject rankDetailJSON) {
//        this.rankDetailJSON = rankDetailJSON;
//    }
//
//    public void addFilterDetail(String filterId, String master){
//        if (filterDetailJSON!=null) {
//            LoCollectionsUtil.putJsonArray(filterDetailJSON,filterId,master);
//        }
//    }
//
//    public void addScorerDetail(String scorerId, String master, BigDecimal score){
//        if (scorerDetailJSON!=null) {
//            LoCollectionsUtil.putJsonArray(scorerDetailJSON,scorerId,master+":"+score);
//        }
//    }
//    /**
//     * 获取订单特征数据
//     *
//     * @return
//     */
//    @Override
//    public String getDetailInfo() {
//        return rankDetailJSON.toJSONString();
//    }
//
//    public static final class DistributeRankDetailBuilder {
//        private String orderId;
//        private String type;
//        private Set<String> masterSet;
//
//        private DistributeRankDetailBuilder() {
//        }
//
//        public static DistributeRankDetailBuilder aRankDetail() {
//            return new DistributeRankDetailBuilder();
//        }
//
//        public DistributeRankDetailBuilder withOrderId(String orderId) {
//            this.orderId = orderId;
//            return this;
//        }
//
//        public DistributeRankDetailBuilder withType(String type) {
//            this.type = type;
//            return this;
//        }
//
//        public DistributeRankDetailBuilder withMasterSet(Set<String> masterSet) {
//            this.masterSet = new HashSet<>(masterSet);
//            return this;
//        }
//
//        public DistributeRankDetail build() {
//            DistributeRankDetail rankDetail = new DistributeRankDetail();
//            rankDetail.setOrderId(orderId);
//            rankDetail.setType(type);
//            rankDetail.masterSet = this.masterSet;
//            final JSONObject details = new JSONObject();
//            final JSONObject filterDetails = new JSONObject();
//            final JSONObject scorerDetails = new JSONObject();
//            details.put("filterDetails",filterDetails);
//            details.put("scorerDetails",scorerDetails);
//            rankDetail.setFilterDetailJSON(filterDetails);
//            rankDetail.setScorerDetailJSON(scorerDetails);
//            rankDetail.setRankDetailJSON(details);
//            return rankDetail;
//        }
//    }
//}
