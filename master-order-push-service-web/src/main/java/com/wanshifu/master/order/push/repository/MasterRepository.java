package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.Enterprise;
import com.wanshifu.master.order.push.domain.po.Master;
import com.wanshifu.master.order.push.mapper.MasterMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterRepository extends BaseRepository<Master> {

    @Resource
    private MasterMapper masterMapper;


    public List<Master> selectByMasterIdSet(Set<String> masterIdSet){
        Condition condition = new Condition(Master.class);
        condition.createCriteria().andIn("masterId", masterIdSet);
        return masterMapper.selectByCondition(condition);
    }


}