package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.push.enums.PushScenarioType;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class OrderPushedResultNotice {

    /**
     * 业务线 1.成品  2. 家庭  3.创新（信息）
     *
     */
    private Integer businessLineId;

    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

    /**
     * 订单信息
     */
    private OrderBaseComposite orderBaseComposite;

    /**
     * 信息订单
     */
    private InfoOrderBase infoOrderBase;

     /**
     * 推送的师傅
     */
    @NotEmpty
    private Set<Long> pushMasterIds;


    /**
     * 普通推单首次有效推单(首轮首次)
     * 0：非，1：是
     */
    private Integer firstTimeValidPush;

    /**
     * 推送的师傅信息
     */
    private List<MasterInfo> masterInfoList;


    /**
     * 推单模式
     */
    private String pushMode;

    /**
     * 推单场景
     */
    private PushScenarioType pushScenarioType;

    /**
     * 推送的地址级别（3：三级地址推单，4：四级地址推单）
     */
    private Integer pushDivisionLevel;
    /**
     * 是否首单推送
     */
    private Integer firstPush;

    /**
     * 专属招募id v7.3
     */
    private Long recruitId;

    /**
     * 是否有价格 v7.3
     */
    private boolean hasPrice;

    /**
     * 是否团队师傅推单
     */
    private Integer teamMasterOrderPush;

    /**
     * 订单包id
     */
    private Long packageId;

    /**
     * 订单包匹配到的子属性
     */
    private String goodsAttribute;

    /**
     * 订单包子属性对应输入值范围
     */
    private String orderPackageAttributeRange;

    /**
     * 推单标识 0：正常推单 1：附近推单
     */
    private Integer pushFlag;

    /**
     * 推单时间
     */
    private Date pushTime;

    /**
     * 代理商推单师傅
     */
    private String agentMasterList;

    /**
     * directAppointPriority:直接指派优先
     * directAppoint:直接指派
     * push:定向推送
     */
    private String agentPushType;

    /**
     * 理商推送类型为push时才有数据，定向推送推单后无人接单重推时间配置
     */
    private Integer agentPushNoHiredRePushTime;

    /**
     * 理商推送类型为push时才有数据，定向推送推单首次查看后无人接单重推时间配置
     */
    private Integer agentFirstViewNoHiredRePushTime;

    /**
     * 直接指派优先的定向推送代理商信息
     */
    private List<AgentAfterDirectAppointPushInfo> agentAfterDirectAppointPushInfo;

    /**
     * 推送模式-细分类型(专属 pre_exclusive_single(专属单个师傅),pre_exclusive_single_transfer_grab_offer_price(专属转抢单))
     */
    private  String pushModeType;


    private Integer exclusiveOrderFlag;
    /**
     * 专属标签名称
     */
    private String recruitTagName;


    /**
     * 师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;


    @Data
    public static class MasterInfo{

        /**
         * 师傅ID
         */
        private Long masterId;
        /**
         * 招募id
         */
        private String recruitId;

        /**
         * 标签名称: contract：直约 ，brand:品牌
         */
        private String tagName;

        /**
         * 标签扩展信息
         */
        private String tagExpand;

        /**
         * 自动报价金额
         */
        private BigDecimal autoPrice;


        /**
         * 自动接单顺序
         */
        private Integer autoOfferSort;
    }
}