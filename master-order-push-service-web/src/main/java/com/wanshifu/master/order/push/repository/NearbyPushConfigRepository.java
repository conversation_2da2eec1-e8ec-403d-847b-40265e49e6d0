package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.NearbyPushConfig;
import com.wanshifu.master.order.push.mapper.NearbyPushConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * 附近推单配置Repository
 * <AUTHOR>
 */
@Repository
public class NearbyPushConfigRepository extends BaseRepository<NearbyPushConfig> {

    @Resource
    private NearbyPushConfigMapper nearbyPushConfigMapper;


    /**
     * 根据类目和城市查询附近推单配置
     * @param categoryId
     * @param secondDivisionId
     * @return
     */
    public NearbyPushConfig selectByCategoryDivisionId(Long categoryId,Long secondDivisionId){
        NearbyPushConfig pushConfig = new NearbyPushConfig();
        pushConfig.setCategoryId(categoryId);
        pushConfig.setSecondDivisionId(secondDivisionId);
        pushConfig.setIsDelete(0);
        return CollectionUtils.getFirstSafety(nearbyPushConfigMapper.select(pushConfig));
    }


}