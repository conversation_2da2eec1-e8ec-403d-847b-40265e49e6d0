package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述 :  开启条件表达式DTO.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-05 15:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributeStrategyExpressionDto {
    private String openConditionRuleParams;
    private String openConditionRuleExpression;
    private Integer selectStrategyId;
    private Integer scoreStrategyId;
    private String distributeRule;
    private Integer distributeNum;
}