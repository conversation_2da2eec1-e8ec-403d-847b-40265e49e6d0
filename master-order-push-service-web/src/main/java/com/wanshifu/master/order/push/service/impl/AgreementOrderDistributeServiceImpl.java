package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterBase;
import com.wanshifu.master.order.push.domain.dto.MasterAutoReceiverRqt;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.repository.OrderDistributeRepository;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetOrderIdRqt;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 协议订单调度
 */
@Service
public class AgreementOrderDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private RocketMqSendService rocketMqSendService;


    @Resource
    private OrderDistributeRepository orderDistributeRepository;


    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Value("${agreementOrderDistribute.move.switch:on}")
    private String agreementOrderDistributeMoveSwitch;


    @Value("${agreementOrderDistribute.delayTime:1000}")
    private Long agreementOrderDistributeDelayTime;




    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice){

        if(!"on".equals(agreementOrderDistributeMoveSwitch)){
            return 0;
        }

        GetOrderIdRqt getOrderIdRqt = new GetOrderIdRqt();
        getOrderIdRqt.setGlobalOrderTraceId(orderPushedResultNotice.getGlobalOrderTraceId());
        Long orderId = normalOrderResourceApi.getOrderId(getOrderIdRqt);

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();


        String pushMode = orderPushedResultNotice.getPushMode();



        //协议推单，协议师傅自动自动报价或抢单
        List<OrderPushedResultNotice.MasterInfo> masterInfoList = orderPushedResultNotice.getMasterInfoList();

        Map<Long, OrderPushedResultNotice.MasterInfo> masterInfoMap = masterInfoList.stream().collect(Collectors.toMap(OrderPushedResultNotice.MasterInfo::getMasterId, Function.identity()));

        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();
        masterInfoList.forEach(masterInfo -> {
            if(masterInfo.getRecruitId() == null){
                return;
            }
            AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
            agreementMasterBase.setMasterId(String.valueOf(masterInfo.getMasterId()));
            agreementMasterBase.setRecruitId(masterInfo.getRecruitId());
            agreementMasterBase.setCooperationPrice(Objects.nonNull(masterInfo.getAutoPrice()) ? masterInfo.getAutoPrice() : BigDecimal.ZERO);
            agreementMasterBase.setId(masterInfo.getRecruitId() + ":" + masterInfo.getMasterId());
            agreementMasterBaseList.add(agreementMasterBase);
        });


        Set<String> agreementMasterIdSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toSet());
//
        List<AgreementMaster> agreementMasterList = agreementMasterEsRespository.searchByIdSet(agreementMasterIdSet);


        Map<String,AgreementMaster> agreementMasterListMap = agreementMasterList.stream().collect(Collectors.toMap(AgreementMaster::getId, Function.identity()));




        Collections.sort(agreementMasterBaseList);


        Integer appointType = orderGrab.getAppointType();
        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        List<MasterAutoReceiverRqt.MasterSubsidiaryInfo> masterSubsidiaryInfoList = Lists.newArrayList();

        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setAppointType(appointType);
        if (AppointType.OPEN.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_OFFER_AGREEMENT.getCode());
        } else if (AppointType.DEFINITE_PRICE.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_AGREEMENT.getCode());
        }
        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        agreementMasterBaseList.forEach(agreementMasterBase -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterPrice.setPrice(agreementMasterBase.getCooperationPrice());
            masterPrice.setAutoReceiveSort(masterInfoMap.get(Long.valueOf(agreementMasterBase.getMasterId())).getAutoOfferSort());
            if(masterPrice.getAutoReceiveSort() == null){
                masterPrice.setAutoReceiveSort(0);
            }

            Integer sceneId = agreementMasterListMap.get(agreementMasterBase.getId()).getSceneId();
            String tagName = agreementMasterListMap.get(agreementMasterBase.getId()).getTagName();
            if (Objects.nonNull(sceneId)
                    && !Strings.isNullOrEmpty(tagName)) {

                MasterAutoReceiverRqt.MasterScene masterScene = new MasterAutoReceiverRqt.MasterScene();
                masterScene.setSceneId(sceneId);
                masterScene.setTagName(tagName);
                masterPrice.setMasterScene(masterScene);
            }

            masterList.add(masterPrice);

            MasterAutoReceiverRqt.MasterSubsidiaryInfo masterSubsidiaryInfo = new MasterAutoReceiverRqt.MasterSubsidiaryInfo();
            masterSubsidiaryInfo.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterSubsidiaryInfo.setExtraType("master_recruit_id");
            masterSubsidiaryInfo.setExtraId(Objects.isNull(agreementMasterBase.getRecruitId()) ? 0L : Long.parseLong(agreementMasterBase.getRecruitId()));
            masterSubsidiaryInfo.setDirectAppointMethod(agreementMasterListMap.get(agreementMasterBase.getId()).getDirectAppointMethod());
            masterSubsidiaryInfoList.add(masterSubsidiaryInfo);
        });

        List<MasterAutoReceiverRqt.MasterPrice> finalMasterList = masterList.stream()
                .sorted(Comparator.comparingInt(MasterAutoReceiverRqt.MasterPrice::getAutoReceiveSort))
                .collect(Collectors.toList());;
        rqt.setMasterList(finalMasterList);

        MasterAutoReceiverRqt.SubsidiaryParam subsidiaryParam = new MasterAutoReceiverRqt.SubsidiaryParam();
        subsidiaryParam.setMasterSubsidiaryInfoList(masterSubsidiaryInfoList);
        rqt.setSubsidiaryParamJSON(JSON.toJSONString(subsidiaryParam));

        Long distributeId = orderDistributeRepository.insertOrderDistribute(orderId,pushMode,JSON.toJSONString(orderPushedResultNotice.getMasterInfoList()));
        rqt.setExtraId(String.valueOf(distributeId));
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),agreementOrderDistributeDelayTime);
        return 1;

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeContext.register(PushMode.AGREEMENT_MASTER.code, this);

    }

}
