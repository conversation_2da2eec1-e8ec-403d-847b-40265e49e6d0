package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.dto.ScorerMaster;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.offer.domains.po.OrderServiceAttributeInfo;
import com.wanshifu.order.push.response.BatchOrderPushResp;

import java.util.List;

/**
 * 自动接单
 * <AUTHOR>
 */
public interface MasterAutoReceiveOrderService {

    /**
     * 自动接单
     * @param orderBase
     * @param orderGrab
     * @param orderServiceAttributeInfoList
     * @param scorerMasterList
     * @param orderDistributeRule
     * @return
     */
    Integer autoReceive(OrderBase orderBase, OrderGrab orderGrab, List<OrderServiceAttributeInfo> orderServiceAttributeInfoList, List<ScorerMaster> scorerMasterList,
                        String orderDistributeRule,Long cityDivisionId,List<BatchOrderPushResp> batchOrderPushRespList);


    /**
     * 校验自动接单条件
     * @param orderBase
     * @param orderGrab
     * @param orderServiceAttributeInfoList
     * @param orderExtraData
     * @return
     */
    boolean checkAutoReceiveCondition(OrderBase orderBase, OrderGrab orderGrab, List<OrderServiceAttributeInfo> orderServiceAttributeInfoList, OrderExtraData orderExtraData);


}
