package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.dto.ServeDataSelect;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Repository
public class BaseSelectStrategySnapshotRepository extends BaseRepository<BaseSelectStrategySnapshot> {

    public Long insert(String strategyName, String strategyDesc, String orderFlag, String rangeSelect, String techniqueSelect, String statusSelect,
                       String serveDataSelect,Integer businessLineId, Long accountId) {
        BaseSelectStrategySnapshot baseSelectStrategy = new BaseSelectStrategySnapshot();
        baseSelectStrategy.setStrategyName(strategyName);
        baseSelectStrategy.setStrategyDesc(strategyDesc);
        baseSelectStrategy.setOpenCondition("");
        baseSelectStrategy.setOrderFlag(orderFlag);
        baseSelectStrategy.setRangeSelect(rangeSelect);
        baseSelectStrategy.setTechniqueSelect(techniqueSelect);
        baseSelectStrategy.setStatusSelect(statusSelect);
        baseSelectStrategy.setServeDataSelect(serveDataSelect);
        baseSelectStrategy.setBusinessLineId(businessLineId);
        baseSelectStrategy.setCreateAccountId(accountId);
        baseSelectStrategy.setUpdateAccountId(accountId);
        super.insertSelective(baseSelectStrategy);
        return baseSelectStrategy.getSnapshotId();
    }


    public List<BaseSelectStrategySnapshot> selectBySnapshotIdList(List<Long> snapshotIdList){
        Condition condition = new Condition(BaseSelectStrategySnapshot.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("snapshotId", snapshotIdList);
        return this.selectByCondition(condition);
    }
}