package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.MasterDaily;
import com.wanshifu.master.order.push.domain.po.MasterEnterprise;
import com.wanshifu.master.order.push.domain.po.MasterUser;
import com.wanshifu.master.order.push.mapper.MasterDailyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterEnterpriseRepository extends BaseRepository<MasterEnterprise> {

    @Resource
    private MasterDailyMapper masterDailyMapper;


    public List<MasterEnterprise> selectByMasterIdAndEnterpriseId(String enterpriseId,Set<String> masterId){
        Example example = new Example(MasterEnterprise.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enterpriseId", enterpriseId);
        criteria.andIn("masterId",masterId);
        return this.selectByExample(example);

    }



}