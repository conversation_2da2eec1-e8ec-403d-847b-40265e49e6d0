package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:41
 */
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScorerAgent implements Comparable<ScorerAgent> {


    private String agentId;
    private BigDecimal score=BigDecimal.ZERO;

    public String getAgentId() {
        return agentId;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void addScore(BigDecimal scoreChange) {
        this.score = this.score.add(scoreChange);
    }

    @Override
    public int compareTo(ScorerAgent o) {
        return o.score.compareTo(this.score);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((agentId == null) ? 0 : agentId.hashCode());
        return result;
    }

    public static final class ScorerAgentBuilder {
        private String agentId;
        private BigDecimal score=BigDecimal.ZERO;

        private ScorerAgentBuilder() {
        }

        public static ScorerAgentBuilder aScorerAgent() {
            return new ScorerAgentBuilder();
        }

        public ScorerAgentBuilder withAgentId(String agentId) {
            this.agentId = agentId;
            return this;
        }

        public ScorerAgentBuilder withScore(BigDecimal score) {
            this.score = score;
            return this;
        }

        public ScorerAgent build() {
            ScorerAgent scorerAgent = new ScorerAgent();
            scorerAgent.agentId = this.agentId;
            scorerAgent.score = this.score;
            return scorerAgent;
        }
    }
}
