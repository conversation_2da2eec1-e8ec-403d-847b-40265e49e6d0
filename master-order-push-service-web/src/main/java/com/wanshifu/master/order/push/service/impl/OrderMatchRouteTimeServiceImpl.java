package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteTimeListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteTimeRqt;
import com.wanshifu.master.order.push.mapper.OrderMatchRouteTimeMapper;
import com.wanshifu.master.order.push.repository.OrderMatchRouteTimeRepository;
import com.wanshifu.master.order.push.service.OrderMatchRouteTimeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OrderMatchRouteTimeServiceImpl implements OrderMatchRouteTimeService {

    @Resource
    private OrderMatchRouteTimeRepository orderMatchRouteTimeRepository;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "订单匹配", level2MenuName = "路由时间配置",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "categoryIds")
    public Integer create(CreateOrderMatchRouteTimeRqt rqt){
        OrderMatchRouteTimeListRqt listRqt = new OrderMatchRouteTimeListRqt();
        listRqt.setBusinessLineId(rqt.getBusinessLineId());
        listRqt.setCategoryIdList(Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toList()));
        listRqt.setSettingType(rqt.getSettingType());
        List<OrderMatchRouteTime> orderMatchRouteTimeList = orderMatchRouteTimeRepository.selectList(listRqt);
        Assert.isTrue(CollectionUtils.isEmpty(orderMatchRouteTimeList), "已存在相同配置!");
        orderMatchRouteTimeRepository.insert(rqt.getBusinessLineId(),rqt.getCategoryIds(),rqt.getAppointTypes(),rqt.getSettingType(),rqt.getSettingTime(),
                rqt.getSettingNum(),rqt.getCreateAccountId());
        return 1;
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "订单匹配", level2MenuName = "路由时间配置",
            tableName = "order_match_route_time", mapperClass = OrderMatchRouteTimeMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderMatchRouteTimeMapper", primaryKeyFieldName = "routeTimeId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "categoryIds")
    public Integer update(UpdateOrderMatchRouteTimeRqt rqt){
        OrderMatchRouteTimeListRqt listRqt = new OrderMatchRouteTimeListRqt();
        listRqt.setBusinessLineId(rqt.getBusinessLineId());
        listRqt.setCategoryIdList(Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toList()));
        listRqt.setSettingType(rqt.getSettingType());
        List<OrderMatchRouteTime> orderMatchRouteTimeList = orderMatchRouteTimeRepository.selectList(listRqt);
        Set<OrderMatchRouteTime> orderMatchRouteTimeSet = orderMatchRouteTimeList.stream().filter(orderMatchRouteTime -> !orderMatchRouteTime.getRouteTimeId().equals(rqt.getRouteTimeId())).collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.isEmpty(orderMatchRouteTimeSet), "已存在相同配置!");
        orderMatchRouteTimeRepository.update(rqt.getRouteTimeId(),rqt.getBusinessLineId(),rqt.getCategoryIds(),rqt.getAppointTypes(),rqt.getSettingType(),rqt.getSettingTime(),
                rqt.getSettingNum(),rqt.getUpdateAccountId());
        return 1;
    }

    @Override
    public OrderMatchRouteTime detail(OrderMatchRouteTimeDetailRqt rqt){
        return orderMatchRouteTimeRepository.selectByPrimaryKey(rqt.getRouteTimeId());
    }

    @Override
    public SimplePageInfo<OrderMatchRouteTime> list(OrderMatchRouteTimeListRqt rqt){
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        if(StringUtils.isNotEmpty(rqt.getCategoryIds())){
            rqt.setCategoryIdList(Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toList()));
        }
        List<OrderMatchRouteTime> orderMatchRouteList = orderMatchRouteTimeRepository.selectList(rqt);
        SimplePageInfo<OrderMatchRouteTime> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(orderMatchRouteList);
        return listRespSimplePageInfo;
    }
}
