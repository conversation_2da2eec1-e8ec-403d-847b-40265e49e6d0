package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.ListRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
public interface NewMasterOrderDistributeStrategyMapper extends IBaseCommMapper<NewMasterOrderDistributeStrategy> {


    NewMasterOrderDistributeStrategy selectByCityAndCategoryId(@Param("distributeType")String distributeType, @Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Long strategyId, @Param("businessLineId") Long businessLineId);


    List<NewMasterOrderDistributeStrategy> selectList(ListRqt rqt);

    NewMasterOrderDistributeStrategy selectByCategoryIdAndCityId(@Param("businessLineId") Integer businessLineId, @Param("distributeType")  String distributeType, @Param("categoryId")  Long categoryId, @Param("cityId") String cityId);



}