package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.iop.activity.domain.api.request.out.MatchMasterActivityOrderRqt;
import com.wanshifu.iop.activity.domain.api.response.out.MatchMasterActivityOrderResp;
import com.wanshifu.iop.activity.service.api.OutBusinessServiceApi;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.domain.api.response.MasterGroupResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetPersonGroupByMasterIdsRqt;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.LongTailPushType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.enums.TechniqueSelectType;
import com.wanshifu.master.order.push.domain.message.LongTailBatchPushMessage;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.rqt.LongTailOrderPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.vo.StrategyTuple;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.PushRuleVo;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply.LongTailStrategyMatcher;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply.LongTailStrategyTuple;
import com.wanshifu.master.order.push.repository.LongTailStrategyGroupRepository;
import com.wanshifu.master.order.push.repository.LongTailStrategyRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LongTailPushServiceImpl implements LongTailPushService {



    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private LongTailStrategyGroupRepository longTailStrategyGroupRepository;


    @Resource
    private HBaseClient hBaseClient;



    @Value("${long.tail.push.delayTime:20}")
    private Integer longTailPushDelayTime;

    @Value("${long.tail.push.enable}")
    private boolean longTailPushEnable=true;


    @Resource
    private PushHandler pushHandler;


    @Resource
    private LongTailStrategyRepository longTailStrategyRepository;


    @Resource
    private FeatureQueryFacade featureQueryFacade;


    @Resource
    private BaseSelector baseSelector;


    @Resource
    private OutBusinessServiceApi outBusinessServiceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Resource
    private OrderServiceAttributeService orderServiceAttributeService;


    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private PushProgressRepository pushProgressRepository;


    @Resource
    private PushControllerFacade pushControllerFacade;



    @Resource
    private Tools tools;

    @Resource
    private NewModelMatchService newModelMatchService;


    @Resource
    private QLExpressHandler qlExpressHandler;





    private static final String TRIGGER_PARAMS="triggerParams";
    private static final String LONG_TAIL_STRATEGY_ID="longTailStrategyId";
    private static final String TRIGGER_EXPRESSION="triggerExpression";
    private static final String PUSH_MINUTES_INTERVAL="push_minutes_interval";
    private static final String OPEN_PARAMS="openParams";
    private static final String OPEN_EXPRESSION="openExpression";
    private static final String PRE_EXPRESSIONS="preExpressions";

    /**
     * 匹配长尾单策略
     */
    @Override
    public void matchLongTailStrategy(Integer businessLineId,OrderDetailData orderDetailData, String orderVersion, Integer maxOfferNum){
        if(!longTailPushEnable){
            return ;
        }
        List<LongTailStrategyMatcher> longTailStrategyMatcherList = getLongTailOrderPushStrategyMatcher(
                String.valueOf(orderDetailData.getSecondDivisionId()),
                new ArrayList<String>(){{add(String.valueOf(orderDetailData.getOrderCategoryId()));}},
                businessLineId
        );
        log.info("{},匹配长尾单策略:{}",orderDetailData.getMasterOrderId(), JSONObject.toJSONString(longTailStrategyMatcherList));
        if(CollectionUtils.isNotEmpty(longTailStrategyMatcherList)){
            longTailStrategyMatcherList.forEach(longTailStrategyMatcher -> {
                final DefaultContext<String, Object> orderFeature = initOrderFeature(orderDetailData);
                final List<LongTailStrategyTuple> matchedStrategy = longTailStrategyMatcher.match(orderFeature);
                if(CollectionUtils.isNotEmpty(matchedStrategy)){
                    matchedStrategy.forEach(longTailStrategyTuple->{
                        log.info("longTailDelayTime:"+getDelayTime(longTailStrategyTuple.getIntervalTime()));
                        pushQueueService.sendLongTailPushMessage(
                                orderDetailData,
                                orderVersion,
                                getDelayTime(longTailStrategyTuple.getIntervalTime()),
                                longTailStrategyTuple,
                                maxOfferNum);
                        log.info("发送长尾单推单mq消息,globalOrderId:{}",orderDetailData.getGlobalOrderId());
                    });
                }
            });
        }
    }


    private Long getDelayTime(Long intervalTime){
        Calendar calendar = Calendar.getInstance();
        Long delayTime = intervalTime * 60 * 1000L;
        if(DateFormatterUtil.isBetweenPeriodTime("20:00","08:00")){
            Random random = new Random();
            int randomNum = random.nextInt(longTailPushDelayTime);
            intervalTime = intervalTime + randomNum;
            delayTime = getSecondDayTimestamp() + intervalTime * 60 * 1000L - System.currentTimeMillis();
            randomNum = random.nextInt(60);
            delayTime = delayTime + randomNum * 1000L;
        }else{
            Long time = System.currentTimeMillis() + delayTime;
            calendar.setTimeInMillis(time);
            if(DateFormatterUtil.isBetweenPeriodTime("20:00","08:00",calendar)){
                delayTime = intervalTime * 60 * 1000L + 12 * 60 * 60 * 1000L;
            }
        }
        return delayTime;
    }


    public Long getSecondDayTimestamp(){
        Calendar calendar = Calendar.getInstance();
        if (DateFormatterUtil.isBetweenPeriodTime("20:00", "23:59")) {
            calendar.add(Calendar.DATE,1);
        }
        calendar.set(Calendar.HOUR_OF_DAY,8);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        return calendar.getTimeInMillis();
    }


    private List<LongTailStrategyMatcher> getLongTailOrderPushStrategyMatcher(
            String cityIdStr,
            List<String> categoryIdList,
            Integer businessLineId
    ){

        final List<LongTailStrategyGroup> longTailStrategyGroupList = longTailStrategyGroupRepository.selectByCityAndCategoryActive(
                cityIdStr,
                categoryIdList,
                businessLineId,
                null
        );
        if (CollectionUtils.isEmpty(longTailStrategyGroupList)) {
            return null;
        }
        List<LongTailStrategyMatcher> longTailStrategyMatcherList = new ArrayList<>();
        longTailStrategyGroupList.forEach(longTailStrategyGroup -> {
            final String strategyExpression = longTailStrategyGroup.getStrategyExpression();
            final LongTailStrategyMatcher longTailStrategyMatcher = new LongTailStrategyMatcher();
            if (StringUtils.isNotEmpty(strategyExpression)) {
                final JSONArray array = JSONArray.parseArray(strategyExpression);

                List<Long> longTailStrategyIdList=new ArrayList<>();
                final HashMap<Long, LongTailStrategyMatcher.LongTailStrategyGroupCondition> conditionMap = new HashMap<>();
                //解析表达式JSON,赋值策略组 ::
                for (int i = 0; i < array.size(); i++) {
                    final JSONObject row = array.getJSONObject(i);
                    final Long longTailStrategyId = row.getLong(LONG_TAIL_STRATEGY_ID);
                    longTailStrategyIdList.add(longTailStrategyId);
                    final LongTailStrategyMatcher.LongTailStrategyGroupCondition condition
                            = new LongTailStrategyMatcher.LongTailStrategyGroupCondition();
                    condition.setOpen(
                            StrategyTuple.StrategyTupleBuilder.aStrategyTuple()
                                    .withTriggerExpression(row.getString(TRIGGER_EXPRESSION))
                                    .withFeatureSet(new HashSet<>(
                                            JSONArray.parseArray(row.getString(TRIGGER_PARAMS),String.class)
                                    ))
                                    .build()
                    );
                    conditionMap.put(longTailStrategyId,condition);
                }
                //查询策略详情
                final List<LongTailStrategy> longTailStrategies = longTailStrategyRepository.selectList(
                        longTailStrategyIdList,
                        null,
                        null,
                        1,
                        null,
                        null
                );
                //解析表达式,赋值策略详情
                longTailStrategies.forEach(row->{
                    final Long longTailStrategyId = row.getLongTailStrategyId();
                    final LongTailStrategyMatcher.LongTailStrategyGroupCondition condition
                            = conditionMap.get(longTailStrategyId);
                    final LongTailStrategyTuple longTailStrategyTuple = new LongTailStrategyTuple();
                    RangeSelect rangeSelect = JSON.parseObject(row.getRangeSelect(),RangeSelect.class);
                    StatusSelect statusSelect = JSON.parseObject(row.getStatusSelect(),StatusSelect.class);
                    String appointGroupExpression = row.getAppointGroupExpression();
                    String filterRuleExpression = row.getFilterRuleExpression();
                    longTailStrategyTuple.setRangeSelect(rangeSelect);
                    longTailStrategyTuple.setStatusSelect(statusSelect);
                    longTailStrategyTuple.setAppointGroup(StringUtils.isNotBlank(appointGroupExpression) ? JSON.parseObject(appointGroupExpression, AppointGroupExpressionDto.class) : null);
                    longTailStrategyTuple.setFilterRuleExpressionList(StringUtils.isNotBlank(filterRuleExpression) ? JSON.parseArray(filterRuleExpression, FilterRuleExpression.class) : null);
                    longTailStrategyTuple.setPushRule(StringUtils.isNotBlank(row.getPushRule()) ? JSON.parseObject(row.getPushRule(), PushRuleVo.class) : null);
                    longTailStrategyTuple.setPushType(row.getPushType());
                    final JSONObject strategyExpressionJSON = JSON.parseObject(row.getStrategyExpression());
                    longTailStrategyTuple.setIntervalTime(strategyExpressionJSON.getLong(PUSH_MINUTES_INTERVAL));
                    longTailStrategyTuple.setTriggerCondition(
                            StrategyTuple.StrategyTupleBuilder.aStrategyTuple()
                                    .withTriggerExpression(strategyExpressionJSON.getString(OPEN_EXPRESSION))
                                    .withFeatureSet(new HashSet<>(
                                            JSONArray.parseArray(strategyExpressionJSON.getString(OPEN_PARAMS),String.class)
                                    ))
                                    .build()
                    );
                    longTailStrategyTuple.setPreCondition(
                            StrategyTuple.StrategyTupleBuilder.aStrategyTuple()
                                    .withTriggerExpression(strategyExpressionJSON.getString(PRE_EXPRESSIONS))
                                    .build());
                    condition.setTrigger(longTailStrategyTuple);

                });
                longTailStrategyMatcher.setLongTailStrategyGroupId(longTailStrategyMatcher.getLongTailStrategyGroupId());
                //备注:当前实现为:单个长尾单策略只能匹配策略组合中的单个开启条件
//            for (int i = 0; i < array.size(); i++) {}
                longTailStrategyMatcher.setLongTailStrategyGroupCondition(
                        new ArrayList<>(conditionMap.values())
                );
                longTailStrategyMatcher.setLongTailStrategyGroupId(longTailStrategyGroup.getLongTailStrategyGroupId());
                longTailStrategyMatcherList.add(longTailStrategyMatcher);
            }
        });

        return longTailStrategyMatcherList;
    }


    private DefaultContext<String, Object> initOrderFeature(OrderDetailData orderDetailData){
        final DefaultContext<String, Object> context = new DefaultContext<>();
        try {
            //服务
            context.put(FieldConstant.LV1_SERVE_ID, orderDetailData.getLv1ServeIds());
            context.put(FieldConstant.LV2_SERVE_IDS, orderDetailData.getLv2ServeIdList());
            context.put(FieldConstant.LV3_SERVE_IDS, orderDetailData.getLv3ServeIdList());
            context.put(FieldConstant.ORDER_SERVE_TYPE, orderDetailData.getOrderServeType());
            //下单模式
            context.put(FieldConstant.APPOINT_TYPE, orderDetailData.getAppointType());
            //订单来源
            context.put(FieldConstant.ORDER_FROM,getOrderFrom(orderDetailData));
            //时效标签
            context.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderDetailData));
            //下单用户
            String accountType = orderDetailData.getAccountType();
            Long userId=null;
            if (FieldConstant.ENTERPRISE.equals(accountType)) {
                context.put(FieldConstant.USER_ID, orderDetailData.getUserId());
                context.put(FieldConstant.ENTERPRISE_ID, orderDetailData.getAccountId());
                userId=orderDetailData.getUserId();
            } else {
                context.put(FieldConstant.USER_ID, orderDetailData.getAccountId());
                userId=orderDetailData.getAccountId();
            }
            //用户人群
            if (userId!=null) {
                final String groupIds = hBaseClient.querySingle("usr_groups_stat", String.valueOf(userId),"group_ids");
                if (StringUtils.isNotEmpty(groupIds)) {
                    final List<Integer> appointUser = Arrays.asList(
                            groupIds.split(","))
                            .stream().map(row -> Integer.valueOf(row)).collect(Collectors.toList());
                    context.put("appoint_user",appointUser);
                }
            }
        } catch (Exception e) {
            log.error(String.format("buildLongTailDimensionData error,orderDetailData:%s", JSON.toJSONString(orderDetailData)),e);
        }
        return context;
    }


    private String getOrderFrom(OrderDetailData orderDetailData){
        String orderFrom = orderDetailData.getOrderFrom();
        if("user".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "".equals("thirdpart")){
                return "site";
            }else{
                return "family";
            }
        }else if("enterprise".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "".equals("applet")){
                return "enterprise_inside";
            }else{
                return "enterprise_outside";
            }
        }

        return "";


    }
    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){
        List<String> timeLinessTagList = new ArrayList<>();

        if(StringUtils.isBlank(orderDetailData.getExpectDoorInStartDate())){
            return timeLinessTagList;
        }

        if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(StringUtils.isNotBlank(orderDetailData.getExpectDoorInStartDate())){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    public Set<String> filterByMasterGroups(Set<String> masterIdSet,AppointGroupExpressionDto appointGroupExpression){
        Set<String> resultSet = new HashSet<>();
        final List<List<Long>> batch = LocalCollectionsUtil.groupByBatchLong(masterIdSet, 400);
        for (List<Long> batchRequest : batch) {
            GetPersonGroupByMasterIdsRqt rqt = new GetPersonGroupByMasterIdsRqt();
            rqt.setAppId(5);
            rqt.setMasterIds(batchRequest);
            //TODO
            List<MasterGroupResp> masterGroupRespList = bigdataOpenServiceApi.getPersonaGroupIdsByMasterIds(rqt);
            if(CollectionUtils.isNotEmpty(masterGroupRespList)){
                masterGroupRespList.forEach(masterGroupResp -> {
                    String groupIds = StringUtils.isNotBlank(masterGroupResp.getGroupIds()) ? masterGroupResp.getGroupIds() : "0";
                    List<Long> groupIdList = Arrays.stream(groupIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    DefaultContext  context = new DefaultContext();
                    context.put(appointGroupExpression.getAppointGroupParams(),groupIdList);
                    try{
                        boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(appointGroupExpression.getAppointGroupExpression(), context,null,true,false);
                        if(result){
                            resultSet.add(String.valueOf(masterGroupResp.getMasterId()));
                        }
                    }catch(Exception e){
                        log.error("filterByMasterGroups",e);
                    }
                });
            }
        }
        return resultSet;
    }


    /**
     * 长尾单推送
     *
     * @param longTailOrderPushRqt
     * @return
     */
    @Override
    public int longTailPush(LongTailOrderPushRqt longTailOrderPushRqt) {


        OrderDetailData orderDetailData = longTailOrderPushRqt.getOrderDetailData();
        final LongTailStrategyTuple longTailStrategyTuple = longTailOrderPushRqt.getLongTailStrategyTuple();

        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(orderDetailData.getMasterOrderId()));

        if(Objects.isNull(orderGrabByIdResp) || Objects.isNull(orderGrabByIdResp.getOrderGrab())){
            log.info("longTailPush orderClose,orderId:{}",orderDetailData.getMasterOrderId());
            return 0;
        }

        OrderStatus orderStatus = pushControllerFacade.getOrderStatus(
                longTailOrderPushRqt.getMaxOfferNum(),
                orderDetailData.getGlobalOrderId(),
                longTailOrderPushRqt.getOrderVersion(),
                longTailOrderPushRqt.getMaxOfferNum()
        );

        if(OrderStatusValue.NORMAL != orderStatus.getOrderStatus() && OrderStatusValue.MODIFY_AND_REPUSH != orderStatus.getOrderStatus()){
            log.info("{} long tail order stop  status:{}",orderStatus.getOrderStatus());
            return 0;
        }

        final boolean isOpen = longTailStrategyTuple
                .getTriggerCondition()
                .executeQLExpression(initLongTailOpenContent(longTailOrderPushRqt.getOrderDetailData()));
        if (!isOpen) {
            log.info("longTailPush triggerCondition not match,orderId:{}",orderDetailData.getMasterOrderId());
            return 0;
        }

        Long timeStamp = System.currentTimeMillis();
        String orderVersion = String.valueOf(timeStamp);

        MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
        masterMatchCondition.setMasterOrderId(orderDetailData.getMasterOrderId());
        masterMatchCondition.setOrderLngLat(orderDetailData.getOrderLngLat());
        masterMatchCondition.setPickupAddressLngLat(orderDetailData.getPickupAddressLngLat());
        masterMatchCondition.setServeIds(orderDetailData.getLv3ServeIds());
        masterMatchCondition.setThirdDivisionId(orderDetailData.getThirdDivisionId());
        masterMatchCondition.setBusinessLineId(orderDetailData.getBusinessLineId());
        masterMatchCondition.setCategoryId(orderDetailData.getOrderCategoryId());

        masterMatchCondition.setTechnologysInDemandSet(orderDetailData.getOrderTechniqueSet());
        masterMatchCondition.setSecondDivisionId(orderDetailData.getSecondDivisionId());
        masterMatchCondition.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());


        String pushType = longTailOrderPushRqt.getLongTailStrategyTuple().getPushType();

        TechniqueSelect techniqueSelect = new TechniqueSelect();
        if(LongTailPushType.OUT_DISTRICT.getCode().equals(pushType)){
            techniqueSelect.setTechniqueType(TechniqueSelectType.FILTER_OUT_NO_MATCH_TECHNIQUE.code);
        }else{
            techniqueSelect.setTechniqueType(TechniqueSelectType.FILTER_OUT_MATCH_TECHNIQUE.code);
        }

        List<BaseSelectMaster> baseSelectMasterList = baseSelector.searchMasterByBaseSelectStrategyIndex(
                masterMatchCondition,
                longTailStrategyTuple.getRangeSelect(),
                techniqueSelect,
                longTailStrategyTuple.getStatusSelect()
        );

        Set<String> masterSet = baseSelectMasterList.stream().map(BaseSelectMaster::getMasterId).collect(Collectors.toSet());


        String pushTypeString = (StringUtils.isBlank(pushType) || LongTailPushType.BONUS_ORDER.getCode().equals(pushType)) ? "bonus_order_push" : "nearby_more_push";



        if(CollectionUtils.isNotEmpty(masterSet)){

            AppointGroupExpressionDto appointGroupExpressionDto = longTailOrderPushRqt.getLongTailStrategyTuple().getAppointGroup();

            if(Objects.nonNull(appointGroupExpressionDto)){
                masterSet = this.filterByMasterGroups(masterSet,longTailOrderPushRqt.getLongTailStrategyTuple().getAppointGroup());
            }


            if(CollectionUtils.isNotEmpty(longTailStrategyTuple.getFilterRuleExpressionList())){
                masterSet = filterByRules(orderDetailData,masterSet,longTailStrategyTuple.getFilterRuleExpressionList());
            }



            //附近红包单则根据智能运营红包进行筛选
            if(StringUtils.isBlank(pushType) || LongTailPushType.BONUS_ORDER.getCode().equals(pushType)){
                masterSet = matchMasterActivityOrder(orderGrabByIdResp,orderDetailData,masterSet);
            }



            List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
            if(CollectionUtils.isNotEmpty(agreementMasterList)){
                List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
                masterSet.removeAll(agreementMasterIdList);
            }


            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion,masterSet.size(),new Date(timeStamp),pushTypeString);

            if(CollectionUtils.isEmpty(masterSet)){
                return 0;
            }



            List<String> finalMasterSet = new ArrayList<>(masterSet);

            longTailPush(orderDetailData,finalMasterSet,pushType,longTailOrderPushRqt.getLongTailStrategyTuple().getPushRule());

        }else{
            pushProgressRepository.insertBasePushZeroProgress(orderDetailData.getGlobalOrderId(),orderVersion,new Date(timeStamp),pushTypeString);
        }

        return 0;
    }



    private void longTailPush(OrderDetailData orderDetailData, List<String> masterSet, String pushType, PushRuleVo pushRule){
        Integer pushFlag  = LongTailPushType.BONUS_ORDER.getCode().equals(pushType) ? 4 :  LongTailPushType.NEARBY_MORE.getCode().equals(pushType) ? 5: 6;
        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
        //长尾单,不更改推送参数:IS_NEARBY_PUSH
        commonFeature.put(FieldConstant.PUSH_FLAG, pushFlag);
        commonFeature.put(FieldConstant.IS_ACCORDING_DISTANCE_PUSH, true);
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
        Set<String> batchMasterSet = (Objects.nonNull(pushRule) && "batch_all".equals(pushRule.getPushType())) ? ( masterSet.size() <= pushRule.getBatchCount() ? new HashSet(masterSet) : new HashSet(masterSet.subList(0,pushRule.getBatchCount()))) : new HashSet(masterSet);

        pushControllerFacade.directPush(orderDetailData, orderDetailData.getOrderVersion(), batchMasterSet, commonFeature);

        if(Objects.nonNull(pushRule) && "batch_all".equals(pushRule.getPushType()) && masterSet.size() > pushRule.getBatchCount()){
            LongTailBatchPushMessage message = new LongTailBatchPushMessage();
            message.setOrderDetailData(orderDetailData);
            message.setPushType(pushType);
            message.setPushRule(pushRule);
            message.setMasterSet(masterSet.subList(pushRule.getBatchCount(),masterSet.size()));
            pushQueueService.sendLongTailBatchPushMessage(message,pushRule.getBatchTime() * 60 * 1000L);
        }
    }


    @Override
    public void longTailBatchPush(LongTailBatchPushMessage message){
        this.longTailPush(message.getOrderDetailData(),message.getMasterSet(),message.getPushType(),message.getPushRule());
    }

    private Set<String> filterByRules(OrderDetailData orderDetailData,Set<String> masterIdSet,List<FilterRuleExpression> filterRuleExpressionList){

        if(CollectionUtils.isEmpty(masterIdSet)){
            return masterIdSet;
        }

        if(CollectionUtils.isEmpty(filterRuleExpressionList)){
            return masterIdSet;
        }

        PushFilterList pushFilterList = new PushFilterList();

        filterRuleExpressionList.forEach(expression -> pushFilterList.addFilter(new PushFilter(qlExpressHandler.getExpressRunner(),expression.getOpenConditionRuleExpression(),expression.getFilterRuleExpression(),
                expression.getRuleName(),expression.getOpenConditionRuleParams(), expression.getFilterRuleParams())));

        List<String> openConditionRuleParamsList = filterRuleExpressionList.stream().filter(filterRuleExpression -> StringUtils.isNotBlank(filterRuleExpression.getOpenConditionRuleParams())).map(FilterRuleExpression::getOpenConditionRuleParams).collect(Collectors.toList());

        Set<String> orderFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(openConditionRuleParamsList)){
            openConditionRuleParamsList.forEach(openConditionRuleParams -> orderFeatureSet.addAll(Arrays.asList(openConditionRuleParams.split(","))));
        }

        PushCommonObject pushCommonObject = new PushCommonObject();
        pushCommonObject.setMasterSet(masterIdSet);
        pushCommonObject.setOrderDetailData(orderDetailData);
        PushFeature pushFeature = featureQueryFacade.getOrderFeatures(pushCommonObject , orderFeatureSet);


        pushHandler.checkCondition(pushFilterList,null,pushFeature);


        if(CollectionUtils.isEmpty(pushFilterList.getFilterList())){
            return masterIdSet;
        }

        Set<String> masterFeatureSet = new HashSet<>();
        masterFeatureSet.addAll(pushFilterList.getMasterFeatureSet());


        featureQueryFacade.getMasterFeatures(pushFeature,pushCommonObject.getMasterSet(),masterFeatureSet);
        DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();

        return masterIdSet.stream().filter(masterId -> {

            DefaultContext<String,Object> masterFeatureMap = masterFeatures.get(masterId);
            try{
                for (PushFilter filter : pushFilterList.getFilterList()) {
                    if (filter.execute(masterFeatures.get(masterId))){
                        return false;
                    }
                }
                return true;
            }catch(Exception e){
                log.error(String.format("filter to master failed, masterId:%s,masterFeature:%s",masterId,masterFeatureMap),e);
            }
            return true;
        }).collect(Collectors.toSet());

    }


    private Set<String> matchMasterActivityOrder(OrderGrabByIdResp orderGrabByIdResp, OrderDetailData orderDetailData, Set<String> masterIdSet){

        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        Set<String> masterSet = new HashSet<>();
        MatchMasterActivityOrderRqt rqt = new MatchMasterActivityOrderRqt();
        rqt.setOrderNo(orderBase.getOrderNo());
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setCreateOrderUserId(orderBase.getAccountId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setOrderFrom(orderBase.getOrderFrom());
        rqt.setCategoryId(orderBase.getCategoryId());
        rqt.setServeTypeId(orderBase.getServeTypeId());
        rqt.setServeType(orderBase.getServeType());
        rqt.setBusinessLineId(orderBase.getBusinessLineId());
        rqt.setServeLevel1Ids(orderBase.getServeLevel1Ids());
        rqt.setServeIds(orderBase.getServeIds());
        rqt.setThirdDivisionId(orderBase.getThirdDivisionId());
        rqt.setFourthDivisionId(orderBase.getFourthDivisionId());
        rqt.setAppointType(orderGrab.getAppointType());
        rqt.setFromAccountType(orderBase.getAccountType());
        if(CollectionUtils.isEmpty(orderGrabByIdResp.getOrderServiceAttributeInfos()) && CollectionUtils.isEmpty(orderGrabByIdResp.getOrderGoodsList())){
            rqt.setGroupGoodNum(1);
        }else{
            BigDecimal goodsNum = orderServiceAttributeService.getGoodsNum(orderGrabByIdResp.getOrderServiceAttributeInfos(),orderGrabByIdResp.getOrderGoodsList(),null);
            rqt.setGroupGoodNum(Objects.nonNull(goodsNum) ? goodsNum.intValue() : 1);
        }
        rqt.setOfferNum(Long.valueOf(orderGrab.getOfferNumber()));
        rqt.setCreateOrderTime(orderBase.getOrderCreateTime());
        rqt.setPushOrderTime(new Date());
        rqt.setIsOrderPackage(0);
        rqt.setIsOrderContract(0);
        rqt.setSkillRelatedState(0);

        int batchSize = 100;
        List<String> masterIdList = new ArrayList<>(masterIdSet);
        int batchNum = masterIdList.size() / batchSize;
        int leftNum = masterIdList.size() % batchSize;
        for(int index = 0;index < batchNum;index++){
            rqt.setMasterIds(masterIdList.subList(index * batchSize,(index + 1) * batchSize).stream().map(Long::parseLong).collect(Collectors.toList()));
            List<MatchMasterActivityOrderResp> masterActivityOrderRespList = tools.catchLog(() -> outBusinessServiceApi.matchMasterActivityOrder(rqt),"outBusinessServiceApi.matchMasterActivityOrder",rqt);
            log.info(String.format("matchMasterActivityOrder rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSON(masterActivityOrderRespList)));
            if(CollectionUtils.isNotEmpty(masterActivityOrderRespList)){
                List<String> resultList = masterActivityOrderRespList.stream().map(MatchMasterActivityOrderResp::getMasterId).map(String::valueOf).collect(Collectors.toList());
                masterSet.addAll(resultList);
            }
        }

        if(leftNum > 0){
            rqt.setMasterIds(masterIdList.subList(batchNum * batchSize,(batchNum * batchSize) + leftNum).stream().map(Long::parseLong).collect(Collectors.toList()));
            List<MatchMasterActivityOrderResp> masterActivityOrderRespList = tools.catchLog(() -> outBusinessServiceApi.matchMasterActivityOrder(rqt),"outBusinessServiceApi.matchMasterActivityOrder",rqt);
            log.info(String.format("matchMasterActivityOrder rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSON(masterActivityOrderRespList)));
            if(CollectionUtils.isNotEmpty(masterActivityOrderRespList)){
                List<String> resultList = masterActivityOrderRespList.stream().map(MatchMasterActivityOrderResp::getMasterId).map(String::valueOf).collect(Collectors.toList());
                masterSet.addAll(resultList);
            }
        }

        return masterSet;
    }


    private DefaultContext<String, Object> initLongTailOpenContent(OrderDetailData orderDetailData){
        final DefaultContext<String, Object> context = new DefaultContext<>();
        //下单模式
        context.put(FieldConstant.APPOINT_TYPE, orderDetailData.getAppointType());

        OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderDetailData.getMasterOrderId(),null);
        OrderGrab orderGrab = orderBaseComposite.getOrderGrab();
        if(Objects.isNull(orderGrab)){
            return context;
        }

        //报价人数
        final Long globalOrderId = orderDetailData.getGlobalOrderId();
        final Integer offerNum = orderGrab.getOfferNumber();
        context.put(FieldConstant.OFFER_NUM, offerNum);


        if(orderGrab.getIsDelete() == 0 && orderGrab.getConfirmServeStatus() == 1 && orderGrab.getHireMasterId() != null){
            context.put(FieldConstant.IS_APPOINT, 1);
        }else{
            context.put(FieldConstant.IS_APPOINT, 0);
        }
        return context;
    }
}
