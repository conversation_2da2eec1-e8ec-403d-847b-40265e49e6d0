package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class BaseSelectStrategyRepository extends BaseRepository<BaseSelectStrategy> {

    public BaseSelectStrategy selectByStrategyId(Integer strategyId){
        Condition condition = new Condition(BaseSelectStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", strategyId);
        condition.selectProperties("rangeSelect","techniqueSelect","statusSelect");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }


    public BaseSelectStrategy selectByStrategyId(Long strategyId) {
        BaseSelectStrategy baseSelectStrategy = this.selectByPrimaryKey(strategyId);
        Assert.isTrue(baseSelectStrategy != null && Objects.equals(baseSelectStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return baseSelectStrategy;
    }


    public int insert(String strategyName, Long snapshotId, String strategyDesc, String orderFlag, String rangeSelect,
                      String techniqueSelect, String statusSelect, String serveDataSelect,
                      Integer businessLineId, Long accountId,Integer strategyVersion) {
        BaseSelectStrategy baseSelectStrategy = new BaseSelectStrategy();
        baseSelectStrategy.setSnapshotId(snapshotId);
        baseSelectStrategy.setStrategyName(strategyName);
        baseSelectStrategy.setStrategyDesc(strategyDesc);
        baseSelectStrategy.setOpenCondition("");
        baseSelectStrategy.setOrderFlag(orderFlag);
        baseSelectStrategy.setRangeSelect(rangeSelect);
        baseSelectStrategy.setTechniqueSelect(techniqueSelect);
        baseSelectStrategy.setStatusSelect(statusSelect);
        baseSelectStrategy.setServeDataSelect(serveDataSelect);
        baseSelectStrategy.setStrategyStatus(CommonConstant.STRATEGY_STATUS_0);
        baseSelectStrategy.setBusinessLineId(businessLineId);
        baseSelectStrategy.setStrategyVersion(strategyVersion);
        baseSelectStrategy.setCreateAccountId(accountId);
        baseSelectStrategy.setUpdateAccountId(accountId);
        return super.insertSelective(baseSelectStrategy);
    }

    public int update(Long strategyId,Long snapshotId, String strategyName, String strategyDesc, String orderFlag, String rangeSelect,
                      String techniqueSelect, String statusSelect,String serveDataSelect, Integer businessLineId, Long accountId) {
        BaseSelectStrategy baseSelectStrategy = new BaseSelectStrategy();
        baseSelectStrategy.setStrategyId(strategyId);
        baseSelectStrategy.setSnapshotId(snapshotId);
        baseSelectStrategy.setStrategyName(strategyName);
        baseSelectStrategy.setStrategyDesc(strategyDesc);
        baseSelectStrategy.setOpenCondition("");
        baseSelectStrategy.setOrderFlag(orderFlag);
        baseSelectStrategy.setRangeSelect(rangeSelect);
        baseSelectStrategy.setTechniqueSelect(techniqueSelect);
        baseSelectStrategy.setStatusSelect(statusSelect);
        baseSelectStrategy.setServeDataSelect(serveDataSelect);
        baseSelectStrategy.setBusinessLineId(businessLineId);
        baseSelectStrategy.setUpdateTime(new Date());
        baseSelectStrategy.setUpdateAccountId(accountId);
        return this.updateByPrimaryKeySelective(baseSelectStrategy);
    }

    public int updateStatus(Long strategyId, Integer strategyStatus,Long updateAccountId) {
        BaseSelectStrategy baseSelectStrategy = new BaseSelectStrategy();
        baseSelectStrategy.setStrategyId(strategyId);
        baseSelectStrategy.setStrategyStatus(strategyStatus);
        baseSelectStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(baseSelectStrategy);
    }

    public List<BaseSelectStrategy> selectList(Long businessLineId, String strategyName, Integer strategyStatus, Date createStartTime, Date createEndTime) {
        Example example = new Example(BaseSelectStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        if (businessLineId != null) {
            criteria.andEqualTo("businessLineId", businessLineId);
        }
        if (StringUtils.isNotBlank(strategyName)) {
            criteria.andLike("strategyName", StrUtil.format("%{}%", strategyName));
        }
        if (strategyStatus != null) {
            criteria.andEqualTo("strategyStatus", strategyStatus);
        }
        if (createStartTime != null) {
            criteria.andGreaterThanOrEqualTo("createTime", createStartTime);
        }
        if (createEndTime != null) {
            criteria.andLessThanOrEqualTo("createTime", createEndTime);
        }
        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        example.orderBy("updateTime").desc();
        return this.selectByExample(example);
    }

    public BaseSelectStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Long strategyId) {
        Example example = new Example(BaseSelectStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

    public int softDeleteByStrategyId(Long strategyId) {
        BaseSelectStrategy baseSelectStrategy = new BaseSelectStrategy();
        baseSelectStrategy.setStrategyId(strategyId);
        baseSelectStrategy.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(baseSelectStrategy);
    }

    public List<BaseSelectStrategy> selectByStrategyIds(List<Long> baseSelectStrategyIds) {
        Example example = new Example(BaseSelectStrategy.class);
        example.createCriteria().andIn("strategyId", baseSelectStrategyIds);
        return this.selectByExample(example);
    }


}