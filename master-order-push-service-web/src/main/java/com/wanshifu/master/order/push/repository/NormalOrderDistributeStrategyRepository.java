package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.ListRqt;
import com.wanshifu.master.order.push.mapper.NormalOrderDistributeStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class NormalOrderDistributeStrategyRepository extends BaseRepository<NormalOrderDistributeStrategy> {

    @Resource
    private NormalOrderDistributeStrategyMapper normalOrderDistributeStrategyMapper;


    /**
     * 触达策略列表
     * @param rqt
     * @return
     */
    public List<NormalOrderDistributeStrategy> selectList(ListRqt rqt){
        return normalOrderDistributeStrategyMapper.selectList(rqt);
    }


    public int insert(String strategyName, String strategyDesc, Integer businessLineId, String categoryIds, String cityIds,
                      String compensateDistributeList, Long createAccountId) {
        NormalOrderDistributeStrategy distributeStrategy = new NormalOrderDistributeStrategy();
        distributeStrategy.setStrategyName(strategyName);
        distributeStrategy.setStrategyDesc(strategyDesc);
        distributeStrategy.setBusinessLineId(businessLineId);
        distributeStrategy.setCategoryIds(categoryIds);
        distributeStrategy.setCityIds(cityIds);
        distributeStrategy.setCompensateDistributeList(compensateDistributeList);
        distributeStrategy.setCompensateDistributeStrategyList("");
        distributeStrategy.setCreateAccountId(createAccountId);
        distributeStrategy.setUpdateAccountId(createAccountId);
        return this.insertSelective(distributeStrategy);
    }

    public Integer update(Integer strategyId,String strategyName, String strategyDesc, Integer businessLineId, String categoryIds, String cityIds, String compensateDistributeList,Long updateAccountId) {
        NormalOrderDistributeStrategy distributeStrategy = new NormalOrderDistributeStrategy();
        distributeStrategy.setStrategyId(strategyId);
        distributeStrategy.setStrategyName(strategyName);
        distributeStrategy.setStrategyDesc(strategyDesc);
        distributeStrategy.setBusinessLineId(businessLineId);
        distributeStrategy.setCategoryIds(categoryIds);
        distributeStrategy.setCityIds(cityIds);
        distributeStrategy.setCompensateDistributeList(compensateDistributeList);
        distributeStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(distributeStrategy);
    }


    public NormalOrderDistributeStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Integer strategyId) {
        Example example = new Example(NormalOrderDistributeStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public NormalOrderDistributeStrategy selectByCategoryIdAndCityId(Integer businessLineId,String categoryId, String cityId){
        return CollectionUtils.getFirstSafety(normalOrderDistributeStrategyMapper.selectByCategoryIdAndCityId(businessLineId,categoryId,cityId));
    }



}
