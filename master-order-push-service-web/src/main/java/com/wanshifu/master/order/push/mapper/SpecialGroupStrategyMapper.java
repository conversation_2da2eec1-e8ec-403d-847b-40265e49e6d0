package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 特殊人群策略Mapper
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
public interface SpecialGroupStrategyMapper extends IBaseCommMapper<SpecialGroupStrategy> {

    /**
     * 查询策略列表
     */
    List<SpecialGroupStrategy> selectList(@Param("strategyName") String strategyName,
                                          @Param("cityId") Long cityId,
                                          @Param("serveIdList") List<Long> serveIdList,
                                          @Param("createStartTime") Date createStartTime,
                                          @Param("createEndTime") Date createEndTime,
                                          @Param("strategyStatus") Integer strategyStatus);

    /**
     * 根据城市和服务查询策略
     */
    List<SpecialGroupStrategy> selectByCityAndServe(@Param("cityId") String cityId,
                                                    @Param("serveIdList") List<Long> serveIdList,
                                                    @Param("serveModel") String serveModel);
}
