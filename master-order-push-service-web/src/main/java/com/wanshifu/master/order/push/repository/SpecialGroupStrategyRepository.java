package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.mapper.SpecialGroupStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特殊人群策略Repository
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Repository
public class SpecialGroupStrategyRepository extends BaseRepository<SpecialGroupStrategy> {

    @Resource
    private SpecialGroupStrategyMapper specialGroupStrategyMapper;

    /**
     * 插入特殊人群策略
     */
    public int insert(String strategyName,
                      String strategyDesc,
                      List<List<Long>> serveIds,
                      String regionLevel,
                      String cityIds,
                      String pushGroups,
                      String serveModel,
                      Integer delayMinutes,
                      String filterGroups,
                      Long createAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();

        strategy.setStrategyName(strategyName);
        strategy.setStrategyDesc(strategyDesc);
        strategy.setServeIdArray(serveIds.toString());
        strategy.setRegionLevel(regionLevel);
        strategy.setCityIds(cityIds);
        strategy.setPushGroups(pushGroups);
        strategy.setServeModels(serveModel);
        strategy.setDelayMinutes(delayMinutes);
        strategy.setFilterGroups(filterGroups);
        strategy.setStrategyStatus(0); // 默认禁用
        strategy.setCreateAccountId(createAccountId);
        strategy.setUpdateAccountId(createAccountId);

        setLevelServeIds(serveIds, strategy);

        return this.insertSelective(strategy);
    }


    /**
     * 更新特殊人群策略
     */
    public int update(Long strategyId,
                      String strategyName,
                      String strategyDesc,
                      List<List<Long>> serveIds,
                      String regionLevel,
                      String cityIds,
                      String pushGroups,
                      String serveModels,
                      Integer delayMinutes,
                      String filterGroups,
                      Long updateAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setStrategyName(strategyName);
        strategy.setStrategyDesc(strategyDesc);
        strategy.setServeIdArray(serveIds.toString());
        strategy.setRegionLevel(regionLevel);
        strategy.setCityIds(cityIds);
        strategy.setPushGroups(pushGroups);
        strategy.setServeModels(serveModels);
        strategy.setDelayMinutes(delayMinutes);
        strategy.setFilterGroups(filterGroups);
        strategy.setUpdateAccountId(updateAccountId);

        setLevelServeIds(serveIds, strategy);

        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 更新策略状态
     */
    public int updateStatus(Long strategyId, Integer strategyStatus) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setStrategyStatus(strategyStatus);
        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 查询策略列表
     */
    public List<SpecialGroupStrategy> selectList(String strategyName,
                                                 Long cityId,
                                                 String serveIds,
                                                 Date createStartTime,
                                                 Date createEndTime,
                                                 Integer strategyStatus) {
        List<Long> serveIdList = null;
        if (StringUtils.isNotBlank(serveIds)) {
            serveIdList = Arrays.stream(serveIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        return specialGroupStrategyMapper.selectList(strategyName, cityId, serveIdList, createStartTime, createEndTime, strategyStatus);
    }


    /**
     * 根据策略名称查询策略（用于重名校验）
     */
    public SpecialGroupStrategy selectByStrategyName(String strategyName, Long strategyId) {
        Example example = new Example(SpecialGroupStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

    /**
     * 根据城市和服务查询策略
     */
    public List<SpecialGroupStrategy> selectByCityAndServe(String cityId, List<Long> serveIdList, String serveModel) {
        return specialGroupStrategyMapper.selectByCityAndServe(cityId, serveIdList, serveModel);
    }



    private void setLevelServeIds(List<List<Long>> serveIds, SpecialGroupStrategy strategy) {
        List<String> serveIdList = new ArrayList<>();
        for (List<Long> sub : serveIds) {
            if (CollectionUtils.isEmpty(sub)) {
                continue;
            }
            Long last = sub.get(sub.size() - 1);
            serveIdList.add(last.toString());
        }

        strategy.setServeIds(String.join(",", serveIdList));
    }
}
