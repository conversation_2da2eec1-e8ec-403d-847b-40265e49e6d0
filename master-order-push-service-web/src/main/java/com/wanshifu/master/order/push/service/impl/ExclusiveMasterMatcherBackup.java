//package com.wanshifu.master.order.push.service.impl;
//
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.alicloud.openservices.tablestore.model.search.SearchQuery;
//import com.alicloud.openservices.tablestore.model.search.SearchRequest;
//import com.alicloud.openservices.tablestore.model.search.SearchResponse;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.wanshifu.base.address.api.AddressApi;
//import com.wanshifu.base.address.domain.po.Address;
//import com.wanshifu.framework.core.page.SimplePageInfo;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.exclusive.api.ExclusiveMasterApi;
//import com.wanshifu.master.exclusive.api.ExclusiveMasterOtherApi;
//import com.wanshifu.master.exclusive.domains.api.request.exclusiveMaster.BatchGetStateInfoRqt;
//import com.wanshifu.master.exclusive.domains.api.request.other.GetRecruitIdsRqt;
//import com.wanshifu.master.exclusive.domains.api.response.exclusiveMaster.BatchGetStateInfoResp;
//import com.wanshifu.master.exclusive.domains.api.response.other.GetRecruitIdsResp;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
//import com.wanshifu.master.order.push.domain.dto.*;
//import com.wanshifu.master.order.push.domain.enums.PushMode;
//import com.wanshifu.master.order.push.domain.enums.RangeQueryType;
//import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
//import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
//import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
//import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.ListRqt;
//import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
//import com.wanshifu.master.order.push.repository.PushProgressRepository;
//import com.wanshifu.master.order.push.service.*;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import com.wanshifu.order.offer.domains.enums.AccountType;
//import com.wanshifu.order.offer.domains.enums.OrderFrom;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 专属师傅匹配器
// * <AUTHOR>
// */
//@Slf4j
//@Component("pre_exclusive")
//public class ExclusiveMasterMatcher extends AbstractOrderMasterMatcher{
//
//
//    @Resource
//    private ExclusiveMasterOtherApi exclusiveMasterOtherApi;
//
//    @Resource
//    private PushDataCenterRepository pushDataCenterRepository;
//
//    @Resource
//    private TableStoreClient tableStoreClient;
//
//    @Resource
//    private OTSMasterSelector otsMasterSelector;
//
//    @Value("${night.push.switch}")
//    private String nightPushSwitch;
//
//    @Value("${night.push.start.time}")
//    private String nightPushStartTime;
//
//    @Value("${night.push.end.time}")
//    private String nightPushEndTime;
//
//    @Resource
//    private PushControllerFacade pushControllerFacade;
//
//    @Resource
//    private PushProgressRepository pushProgressRepository;
//
//    @Resource
//    private PushQueueService pushQueueService;
//
//
//    @Resource
//    private ExclusiveMasterApi exclusiveMasterApi;
//
//    @Resource
//    private Tools tools;
//
//    @Resource
//    private AddressApi addressApi;
//
//    @Resource
//    private ExclusiveOrderSchedulerService exclusiveOrderSchedulerService;
//
//    @Resource
//    private OrderMatchRouteService orderMatchRouteRouteService;
//
//
//    @Value("${exclusive.limit.user}")
//    private String exclusiveLimitUser;
//
//
//    private static final List<String> noExclusiveOrderServeIdList =new ArrayList<String>(){{
//        add("4003104");add("4013104");add("3003104");add("3013104");
//    }};
//
//
//    /**
//     * 专属订单商品属性检查
//     * @param orderDetailData
//     * @return
//     */
//    private boolean orderGoodsAttributionCheck(OrderDetailData orderDetailData){
//        final String orderServeVersion = orderDetailData.getOrderVersion();
//        final List<String> orderAttachedGoods = orderDetailData.getOrderAttachedGoods();
//        final JSONArray orderAncillaryGoods =orderDetailData.getOrderAncillaryGoods();
//        if (SymbolConstant.ONE.equals(orderServeVersion)) {
//            final boolean have = orderAttachedGoods.stream().anyMatch(s ->
//                    s.toString().contains("含儿童书架") || s.toString().contains("含陪读凳")
//            );
//            if (have) {
//                return false;
//            }
//        }else {
//            for (int i = 0; i < orderAncillaryGoods.size(); i++) {
//                final JSONObject goodsRow = orderAncillaryGoods.getJSONObject(i);
//                final boolean have = ObjectUtil.isNotEmpty(goodsRow.get("includingReadingStool"))
//                        || ObjectUtil.isNotEmpty(goodsRow.get("includingChildrensBookshelf"));
//                if (have) {
//                    return false;
//                }
//            }
//        }
//
//        return false;
//    }
//
//
//    /**
//     * 获取业务范围
//     * @param businessLineIdString
//     * @param orderFrom
//     * @param accountType
//     * @param appointType
//     * @return
//     */
//    public List<String> getRecruitBusiness(Long categoryId,String businessLineIdString,
//                                     String orderFrom,String accountType,String appointType) {
//        if (StrUtil.isEmpty(accountType)) {
//            return null;
//        }
//        if (OrderFrom.IKEA.valueEn.equals(orderFrom)) {
//            return null;
//        }
//        List<String> recruitBusinessList = new ArrayList<>();
//        Integer businessLineId = Integer.valueOf(businessLineIdString);
//        if (AccountType.ENTERPRISE.code.equals(accountType)) {
//            if(categoryId == 1L && "2".equals(appointType) && businessLineId == 1){
//                recruitBusinessList.add("finished_product");
//            }else{
//                return null;
//            }
//
//        }
//        //家庭只有一口价
//        if (businessLineId == 2 && "4".equals(appointType) ) {
//            recruitBusinessList.add("family");
//        }
//        //成品只有报价
//        if (AccountType.USER.code.equals(accountType) && businessLineId == 1 && "2".equals(appointType) ) {
//            if(categoryId == 1L){
//                recruitBusinessList.add("finished_product");
//                recruitBusinessList.add("enterprise");
//            }else{
//                recruitBusinessList.add("finished_product");
//            }
//
//        }
//        return recruitBusinessList;
//    }
//
//
//    private boolean isLimitUser(Long userId){
//
//        try{
//            if(userId == null){
//                return false;
//            }
//
//            if(StringUtils.isBlank(exclusiveLimitUser) || "0".equals(exclusiveLimitUser)){
//                return false;
//            }
//
//            Set<Long> limitUserIdSet = Arrays.stream(exclusiveLimitUser.split(",")).map(Long::parseLong).collect(Collectors.toSet());
//            if (CollectionUtils.isNotEmpty(limitUserIdSet)) {
//                if (limitUserIdSet.contains(userId)) {
//                    return true;
//                } else {
//                    return false;
//                }
//            } else {
//                return false;
//            }
//        }catch(Exception e){
//            log.error("isLimitUser error",e);
//        }
//        return false;
//    }
//
//    /**
//     * 检查条件
//     * @param orderDetailData
//     * @return
//     */
//    @Override
//    protected boolean checkPreCondition(OrderDetailData orderDetailData){
//
//
//        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
//            return false;
//        }
//
//        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
//        if (SymbolConstant.ONE.equals(emergencyOrderFlag)) {
//                log.info("debug1::{},{}",orderDetailData.getMasterOrderId(),emergencyOrderFlag);
//            return false;
//        }
//        List<String> recruitBusinessList = getRecruitBusiness(orderDetailData.getOrderCategoryId(),String.valueOf(orderDetailData.getBusinessLineId()),orderDetailData.getOrderFrom(),
//                orderDetailData.getAccountType(),
//                String.valueOf(orderDetailData.getAppointType()));
//        if (CollectionUtils.isEmpty(recruitBusinessList)) {
//            log.info("debug2::{},{}",orderDetailData.getMasterOrderId(),recruitBusinessList);
//            return false;
//        }
//        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
//        if (serveIdsArray==null||serveIdsArray.size()!=1) {
//            log.info("debug3::{},{}",orderDetailData.getMasterOrderId(),serveIdsArray);
//            return false;
//        }
//        Long serveId=serveIdsArray.get(0);
//
//
//        //专属商品属性判断 特殊处理1
//        //安装 送装 儿童学习桌 电动儿童学习桌 有新增的俩附属商品 不算专属订单
//        if (noExclusiveOrderServeIdList.contains(serveId)) {
//            final boolean attributionCheck = orderGoodsAttributionCheck(orderDetailData);
//            if (!attributionCheck) {
//                log.info("debug4::{},{}",orderDetailData.getMasterOrderId(),attributionCheck);
//                return false;
//            }
//        }
//
//        //专属商品属性判断 特殊处理2
//        //V7,12_0425 订单属性包含: 拆旧服务||寄回旧件 不走专属逻辑
//        final String isSendBackOld = String.valueOf(orderDetailData.getIsSendBackOld());
//        final String demolishType = orderDetailData.getDemolishType();
//        if (Objects.equals(isSendBackOld, "1") ||
//                (StrUtil.isNotBlank(demolishType) && (!Objects.equals(demolishType, "nouse")))) {
//            log.info("debug5::{},{},{}",orderDetailData.getMasterOrderId(),isSendBackOld,demolishType);
//            return false;
//        }
//
//
//        if(isLimitUser(orderDetailData.getUserId())){
//            return false;
//        }
//
//        return true;
//    }
//
//
//    private static final String exclusiveMasterTableName = "t_exclusive_master";
//    private static final String exclusiveMasterIndexName = "exclusive_master_index_v1";
//
//    /**
//     * 匹配师傅
//     * @param orderDetailData
//     * @param masterCondition
//     * @return
//     */
//    @Override
//    protected MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){
//
//
//
//        Long thirdDivisionId=orderDetailData.getThirdDivisionId();
//        Long fourthDivisionId=orderDetailData.getFourthDivisionId();
//        final List<String> recruitBusinessList = getRecruitBusiness(orderDetailData.getOrderCategoryId(),
//                String.valueOf(orderDetailData.getBusinessLineId()),
//                orderDetailData.getOrderFrom(),
//                orderDetailData.getAccountType(),
//                String.valueOf(orderDetailData.getAppointType())
//        );
//        log.info("{},debug recruitBusiness::{},{},{},{}",
//                orderDetailData.getMasterOrderId(),
//                String.valueOf(orderDetailData.getBusinessLineId()),
//                orderDetailData.getOrderFrom(),
//                orderDetailData.getAccountType(),
//                String.valueOf(orderDetailData.getAppointType())
//        );
//        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
//        final Long userId = orderDetailData.getUserId();
//
//        //【专属订单特殊处理】的特殊处理:排除定制的特殊处理
//        judgeRegionMode(masterCondition,String.valueOf(orderDetailData.getThirdDivisionId()), String.valueOf(orderDetailData.getFourthDivisionId()));
//
//
//        Set<String> result = null;
//
//        int divisionMatchLevel = masterCondition.getDivisionMatchLevel();
//
//        GetRecruitIdsResp getRecruitIdsResp = null;
//
//        for(String recruitBusiness : recruitBusinessList){
//            getRecruitIdsResp = getRecruitIds(
//                    orderDetailData.getMasterOrderId(),
//                    thirdDivisionId,
//                    fourthDivisionId,
//                    recruitBusiness,
//                    serveIdsArray,
//                    userId
//            );
//
//
//            if(Objects.nonNull(getRecruitIdsResp) && Objects.nonNull(getRecruitIdsResp.getRecruitId())){
//                masterCondition.setDivisionMatchLevel(divisionMatchLevel);
//                Long recruitId = getRecruitIdsResp.getRecruitId();
//                //专属师傅模式
//                masterCondition.setRecruitIds(String.valueOf(recruitId));
//                result = this.searchExclusiveMaster(masterCondition);
//                log.debug("{},debug::PRE_EXCLUSIVE result:{}",orderDetailData,result);
//                if(CollectionUtils.isNotEmpty(result)){
//                    break;
//                }
//            }
//
//        }
//
//        if(Objects.isNull(getRecruitIdsResp) || Objects.isNull(getRecruitIdsResp.getRecruitId()) || CollectionUtils.isEmpty(result)){
//            return null;
//        }
//
//
//
////        log.info("{},exclusive Order result:{},{},{},{},{},recruitIds:{}",
////                orderDetailData.getMasterOrderId(),
////                thirdDivisionId,
////                fourthDivisionId,
//////                recruitBusiness,
////                serveIdsArray,
////                userId,
////                recruitId
////        );
//
//
//
//
//
////			//2022-06-01 临时过滤爱果乐专属单退款师傅
////			if (result.size() != 0&&5409421897L==orderBase.getAccountId()) {
////				OTSMasterSelector.instance().exclusiveBlackListAiguole(result);
////			}
////			2022-06-14 删除过滤
//
//        //四级地址过滤
//        if (masterCondition.getDivisionMatchLevel()==3&&result.size() != 0) {
//            levelThreeDivisionFilter(result,masterCondition);
//        }
//
//        /**
//         *专属拉黑过滤
//         */
//        if (result.size() != 0) {
//            otsMasterSelector.exclusiveRefundBlackList(String.valueOf(orderDetailData.getOrderCategoryId()),result);
//        }
//        /**
//         * 专属退款过滤
//         */
//        //TODO
//        if (result.size() != 0) {
//            final String orderPushExcludeMasterIds =
//                    StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
//            outerMasterFilter(result,orderPushExcludeMasterIds);
//            log.info("debug::orderPushExcludeMasterIds:{}:{}",orderDetailData.getMasterOrderId(),orderPushExcludeMasterIds);
//        }
//
//        if (result.size() != 0) {
//            //黑名单
//            blackListFilter(result,orderDetailData.getAccountType(),orderDetailData.getAccountId());
//        }
//
//        //TODO
//        otsMasterSelector.filterPushedMaster(String.valueOf(orderDetailData.getMasterOrderId()),result,PushMode.PRE_EXCLUSIVE.code);
//
//        Long recruitId = getRecruitIdsResp.getRecruitId();
//        MatchMasterResult masterResult = new MatchMasterResult(result);
//        masterResult.putExtraData(FieldConstant.RECRUIT_ID,String.valueOf(recruitId));
//        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
//        masterResult.putExtraData(FieldConstant.HAS_PRICE,getRecruitIdsResp.getHasPrice());
//
//        //2023-11-16:工作项********
////        if("new".equals(orderDetailData.getPushExtraData().getHandoffTag())){
////            String pushModeType = null;
////            if(CollectionUtils.isNotEmpty(result)){
////                pushModeType = screenSystemAppointMaster(result,getRecruitIdsResp.getHasPrice(),masterCondition.getDivisionMatchLevel(),recruitId);
////            }
////
////            if(StringUtils.isNotBlank(pushModeType)){
////                masterResult.putExtraData(FieldConstant.PUSH_MODE_TYPE,pushModeType);
////            }
////        }
//
//
//
//        return masterResult;
//
//    }
//
//
//    private void levelThreeDivisionFilter(Set<String> masterIds,MasterMatchCondition masterMatchCondition){
//        final Set<String> subDivisionIdList = getSubListByDivisionId(masterMatchCondition.getThirdDivisionId());
//        if (CollectionUtils.isEmpty(subDivisionIdList)) {
//            log.info("{},subDivision is empty:{},lv3:{},getDivisionMatchLevel:{}"
//                    ,masterMatchCondition.getMasterOrderId()
//                    ,subDivisionIdList
//                    ,masterMatchCondition.getThirdDivisionId()
//                    ,masterMatchCondition.getDivisionMatchLevel());
//            return;
//        }
//        log.info("{},subDivision:{},lv3:{},getDivisionMatchLevel:{}"
//                ,masterMatchCondition.getMasterOrderId()
//                ,subDivisionIdList
//                ,masterMatchCondition.getThirdDivisionId()
//                ,masterMatchCondition.getDivisionMatchLevel());
//
//        final Map<String, String> masterDivision = pushDataCenterRepository.getMasterBaseFeature(masterIds, FieldConstant.SERVE_FOURTH_DIVISION_IDS);
//        final HashSet<String> tmpMasterSet = new HashSet<>(masterIds);
//        //按3级地址扩展时,过滤[不匹配全区域]的师傅
//        for (String currentMasterId : tmpMasterSet) {
//            final String masterLevelFourDivision = masterDivision.get(currentMasterId);
//            final boolean fullMatch = thirdLevelDivisionFullMatch(subDivisionIdList, masterLevelFourDivision);
//            if (!fullMatch) {
//                masterIds.remove(currentMasterId);
//            }
//        }
//    }
//
//    /**
//     * 三级地址全匹配
//     */
//    private boolean thirdLevelDivisionFullMatch(
//            Set<String> orderFullFourthDivisions,
//            String packageFourthLevelDivisions){
//        if (org.apache.commons.lang3.StringUtils.isEmpty(packageFourthLevelDivisions)) {
//            return false;
//        }
//        final Set<String> packageFourthDivisionSet
//                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());
//
//        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
//    }
//    /**
//     * 获取地址子集集合
//     * @return
//     */
//    private Set<String> getSubListByDivisionId(Long divisionId){
//        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
//        final Set<String> divisionList =
//                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
//        return divisionList;
//    }
//
//    private String screenSystemAppointMaster(Set<String> masterIdsList,Boolean hasPrice,Integer divisionMatchLevel,Long recruitId) {
//
//        if (4 == divisionMatchLevel && hasPrice) {
//            List<Long> masterList = masterIdsList.stream().map(Long::parseLong).collect(Collectors.toList());
//            //获取师傅自动指派开关状态
//            BatchGetStateInfoRqt batchGetStateInfoRqt = new BatchGetStateInfoRqt();
//            batchGetStateInfoRqt.setRecruitId(recruitId);
//            batchGetStateInfoRqt.setMasterIdList(masterList);
//            List<BatchGetStateInfoResp> batchGetStateInfo = tools.catchLog(() -> exclusiveMasterApi.batchGetStateInfo(batchGetStateInfoRqt), "batchGetStateInfo", batchGetStateInfoRqt);
//            if (batchGetStateInfo != null && batchGetStateInfo.stream().anyMatch(stateInfo -> stateInfo.getSystemAppoint() == 1)) {
//                List<Long> collect = batchGetStateInfo.stream().filter(stateInfo -> stateInfo.getSystemAppoint() == 1).map(BatchGetStateInfoResp::getMasterId).collect(Collectors.toList());
//                masterIdsList.retainAll(Collections.singletonList(String.valueOf(collect.get(new Random().nextInt(masterIdsList.size())))));
//                return "pre_exclusive_single";
//            } else {
//                return "pre_exclusive_single_transfer_grab_offer_price";
//            }
//        }
//
//        return null;
//
//    }
//
//
//    /**
//     * 用户/总包 拉黑过滤
//     */
//    public Set<String> blackListFilter(Set<String> result,String accountType,Long accountId){
//        Set<String> blackMasterList=new HashSet<>();
//        try {
//            String accountIdString=String.valueOf(accountId);
//            if (FieldConstant.ENTERPRISE.equals(accountType)) {
//                blackMasterList=otsMasterSelector.enterpriseBlackListFilter(
//                        accountIdString,result);
//            } else if(FieldConstant.USER.equals(accountType)) {
//                blackMasterList=otsMasterSelector.userBlackListFilter(
//                        accountIdString,result
//                );
//            }
//            if (blackMasterList!=null) {
//                result.removeAll(blackMasterList);
//            }
//        } catch (Exception e) {
//            log.warn("blackListFilter failed :: accountId:{},e:{}",accountId,e);
//        }
//        return blackMasterList;
//    }
//
//    /**
//     * 过滤外部师傅
//     */
//    private void outerMasterFilter(Set<String> result,String masterIds){
//        if (masterIds!=null) {
//            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
//            result.removeAll(outerMasterList);
//        }
//    }
//
//
//    /**
//     * 筛选专属师傅
//     * @param masterCondition
//     * @return
//     */
//    public Set<String> searchExclusiveMaster(MasterMatchCondition masterCondition) {
//        Set<String> result = searchExclusiveMasterByIndex(masterCondition);
//        final Integer bussinessId = masterCondition.getBusinessLineId();
//        /**
//         * 家庭业务线4级无师傅,按3级处理
//         */
//        if (result.size()==0&&"2".equals(bussinessId)&&4==masterCondition.getDivisionMatchLevel()) {
//            masterCondition.setDivisionMatchLevel(3);
//            result = searchExclusiveMasterByIndex(masterCondition);
//        }
//        return result;
//    }
//
//
//    /**
//     * 搜索专属师傅
//     * @param masterCondition
//     * @return
//     */
//    public Set<String> searchExclusiveMasterByIndex(MasterMatchCondition masterCondition) {
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList=new ArrayList<Query>();
//        setExclusiveQuery(mustQueryList,masterCondition);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(exclusiveMasterTableName, exclusiveMasterIndexName, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        HashSet<String> masterIds=new HashSet<>();
//        for (Row row : rows) {
//            if (row!=null) {
//                masterIds.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//            }
//        }
//
//        if (masterIds.size() != 0) {
////			ocs黑名单√
////			账号冻结√
////			未入驻is_settle_status_normal√
//            List<Query> mustQuerys=new ArrayList<Query>();
//            mustQuerys.add(tableStoreClient.booleanTermQuery("is_push_restrict_normal",
//                    true));
//            mustQuerys.add(tableStoreClient.longRangeQuery("freezing_time", 0L,
//                    masterCondition.freezingRecoverTime()));
//            mustQuerys.add(tableStoreClient.booleanTermQuery("is_settle_status_normal",
//                    masterCondition.isSettleStatusNormal()));
//            masterIds = matchMasterBaseInfoBySet(masterIds, mustQuerys);
//        }
//        return masterIds;
//    }
//
//
//    /**
//     * 判断区域匹配模式
//     * @return
//     */
//    public void judgeRegionMode(MasterMatchCondition masterMatchCondition,String orderThirdDivisionId,String orderForthDivisionId) {
//        if (orderForthDivisionId!=null&&!SymbolConstant.ZERO.equals(orderForthDivisionId)) {
//            masterMatchCondition.setDivisionMatchLevel(4);
//        }else if (orderThirdDivisionId!=null&&!SymbolConstant.ZERO.equals(orderThirdDivisionId)) {
//            masterMatchCondition.setDivisionMatchLevel(3);
//        }else {
//            masterMatchCondition.setDivisionMatchLevel(0);
//        }
//    }
//
//
//    /**
//     * 获取招募Id
//     * @param thirdDivisionId
//     * @param fourthDivisionId
//     * @param recruitBusiness
//     * @param serveIdArray
//     * @param userId
//     * @return
//     */
//    private GetRecruitIdsResp getRecruitIds(
//            Long masterOrderId,
//            Long thirdDivisionId,
//            Long fourthDivisionId,
//            String recruitBusiness,
//            List<Long> serveIdArray,
//            Long userId
//    ){
//
//        GetRecruitIdsRqt getRecruitIdsRqt = new GetRecruitIdsRqt();
//        getRecruitIdsRqt.setLv3DivisionId(thirdDivisionId);
//        getRecruitIdsRqt.setLv4DivisionId(fourthDivisionId);
//        getRecruitIdsRqt.setRecruitBusiness(recruitBusiness);
//        getRecruitIdsRqt.setServeId(serveIdArray.get(0));
//        getRecruitIdsRqt.setUserId(userId);
//        getRecruitIdsRqt.setNowTime(new Date());
//        final GetRecruitIdsResp getRecruitIdsResp = exclusiveMasterOtherApi.getRecruitIds(getRecruitIdsRqt);
//        if (getRecruitIdsResp != null && getRecruitIdsResp.getRecruitId() != null) {
//            return getRecruitIdsResp;
//        }else {
//            return null;
//        }
//    }
//
//
//    private static final String MASTER_BASE = "t_master_base";
//
//    @Value("${base.select.index:t_master_base_index}")
//    private String masterBaseIndex;
//
//
//    /**
//     * 过滤师傅基础信息
//     * @param masterIds
//     * @param mustQueryList
//     * @return
//     */
//    private HashSet<String> matchMasterBaseInfoBySet(HashSet<String> masterIds,List<Query> mustQueryList){
//        HashSet<String> result=new HashSet<>();
//        if (mustQueryList==null||mustQueryList.size()==0) {
//            return masterIds;
//        }
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID, masterIds));
//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(MASTER_BASE, masterBaseIndex, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//        List<Row> rows = resp.getRows();
//        while(resp.getNextToken() != null){
//            //把token设置到下一次请求中
//            searchRequest.getSearchQuery().setToken(resp.getNextToken());
//            resp = tableStoreClient.getSyncClient().search(searchRequest);
//            if (!resp.isAllSuccess()){
//                throw new RuntimeException("not all success");
//            }
//            rows.addAll(resp.getRows());
//        }
//        for (Row row : rows) {
//            if (row!=null) {
//                result.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//            }
//        }
//        return result;
//    }
//
//
//    /**
//     * 合作服务
//     * 合作街道
//     * 生效中
//     * 合作期内
//     * 招募下
//     * @param mustQueryList
//     * @param masterCondition
//     */
//    private void setExclusiveQuery(List<Query> mustQueryList, MasterMatchCondition masterCondition) {
//        //专属订单服务只会有一个,多个无法参加
//        final String serveIds = masterCondition.getServeIds();
//        //①合作服务
//        mustQueryList.add(tableStoreClient.stringTermQuery("select_serve_ids",serveIds));
//        //②生效中
////		mustQueryList.add(OTSUtil.stringTermQuery("exclusive_status","effective"));
//        mustQueryList.add(tableStoreClient.booleanTermQuery("is_exclusive_status_normal", true));
//        //③合作期内
//        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                "cooperation_start_time",
//                RangeQueryType.LESS_THAN_OREQUAL,
//                nowTimeStamp
//        ));
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                "cooperation_end_time",
//                RangeQueryType.GREATER_THAN_OREQUAL,
//                nowTimeStamp
//        ));
//
//        //④招募下
//        final String recruitIds = masterCondition.getRecruitIds();
//        mustQueryList.add(tableStoreClient.stringTermQuery("recruit_id",recruitIds));
//        //⑤街道
//        switch (masterCondition.getDivisionMatchLevel()) {
//            case 4:
//                mustQueryList.add(tableStoreClient.stringTermQuery("lv4_division_ids", String.valueOf(masterCondition.getFourthDivisionId())));
//                break;
//            default:
//                //never 没有区域的订单不会执行到查询
//                mustQueryList.add(tableStoreClient.stringTermQuery("lv3_division_ids", String.valueOf(masterCondition.getThirdDivisionId())));
//                break;
//        }
//
//    }
//
//
//    /**
//     * 匹配后续操作
//     * @param masterCondition
//     * @param matchMasterResult
//     */
//    @Override
//    protected void afterMatch(MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
//        return ;
//    }
//
//
//    /**
//     * 执行推单
//     * @param orderDetailData
//     * @param matchMasterResult
//     * @return
//     */
//    @Override
//    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
//
//        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
//            return false;
//        }
//        String orderVersion = orderDetailData.getOrderVersion();
//        Long timeStamp = Long.parseLong(orderVersion);
//
//        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), "exclusive_master_push");
//
//        log.info("nightSwitch::debug:{}{}",orderDetailData.getMasterOrderId(),nightPush());
//        if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {
//            //夜间推单
//            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
//            rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
//            rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
//            pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
//            return true;
//        }
//
//        JSONObject commonFeature = new JSONObject();
//
//        //专属订单
//        //	JSONObject commonFeature = new JSONObject();
//        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
//        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
//        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.PRE_EXCLUSIVE.code);
//        commonFeature.put(FieldConstant.RECRUIT_ID,matchMasterResult.getExtraData().getLong(FieldConstant.RECRUIT_ID));
//        commonFeature.put(FieldConstant.HAS_PRICE,
//                matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
//        commonFeature.put(FieldConstant.PUSH_MODE_TYPE, matchMasterResult.getExtraData().getString(FieldConstant.PUSH_MODE_TYPE));
//        commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
//        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
//        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
//        pushControllerFacade.exclusivePush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);
//
//        Boolean hasPrice = matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE);
//        if (hasPrice==null) {
//            hasPrice=false;
//        }
//
//
//
//        ExclusiveOrderLabel exclusiveOrderLabel = orderMatchRouteRouteService.getExclusiveOrderLabel(Long.valueOf(matchMasterResult.getMasterIdSet().iterator().next()),PushMode.PRE_EXCLUSIVE.code,null,orderDetailData.getGlobalOrderId(),
//                matchMasterResult.getExtraData().getLong(FieldConstant.RECRUIT_ID),matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
//        if(Objects.nonNull(exclusiveOrderLabel) && StringUtils.isNotBlank(exclusiveOrderLabel.getRecruitTagName())){
//            String recruitTagName = exclusiveOrderLabel.getRecruitTagName();
//            String orderPushFlag = "exclusive";
//            if("exclusive".equals(recruitTagName)){
//                orderPushFlag = "exclusive";
//            }else if("direct_appointment".equals(recruitTagName)){
//                orderPushFlag = "direct_appoint";
//            }else if("preferred".equals(recruitTagName)){
//                orderPushFlag = "excellent";
//            } else if("contract".equals(recruitTagName)){
//                orderPushFlag = "contract";
//            } else if("brand".equals(recruitTagName)){
//                orderPushFlag = "brand";
//            }
//            orderMatchRouteRouteService.orderMatchRoute(orderPushFlag,orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
//            return true;
//        }
//
//        //有价格
//        if (FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&hasPrice) {
//            exclusiveOrderSchedule(orderDetailData,matchMasterResult);
//        }else if(FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&!hasPrice){
//            //无价格的专属-60分钟未指派-转推
//            exclusiveOrderTimeSchedule(orderDetailData,matchMasterResult,60);
//        }
//        return true;
//    }
//
//
//
//
//
//    private void exclusiveOrderTimeSchedule(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult,Integer minutes){
//
//        log.info("{},无价格-专属师傅推送更多普通师傅转发延迟消息,getBusinessLineId:{},{},{}",orderDetailData.getMasterOrderId(),
//                orderDetailData.getBusinessLineId());
//        Integer schedulerTimeSecond=minutes*60;
//        //发消息
//        pushQueueService.sendNotAppointScheduledPushMessage(
//                schedulerTimeSecond*1000L,
//                orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),
//                matchMasterResult.getMasterIdSet().stream().map(Long::valueOf).collect(Collectors.toList()),
//                orderDetailData.getPushExtraData().getHandoffTag()
//        );
//    }
//
//    private void exclusiveOrderSchedule(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
//
//        log.info("{},专属师傅推送更多普通师傅转发延迟消息,getBusinessLineId:{},{},{}",orderDetailData.getMasterOrderId(),
//                orderDetailData.getBusinessLineId());
//
//        //查配置-时间
//        final ListRqt listRqt = new ListRqt();
//        listRqt.setBusinessLineId(orderDetailData.getBusinessLineId());
//        listRqt.setCategoryIds(String.valueOf(orderDetailData.getOrderCategoryId()));
//        final SimplePageInfo<ExclusiveOrderScheduler> SchedulerResponse = exclusiveOrderSchedulerService.list(listRqt);
//        final List<ExclusiveOrderScheduler> SchedulerInfo = SchedulerResponse.getList();
//        Integer schedulerTime=0;
//        Integer schedulerTimeSecond=0;
//        if (CollectionUtils.isNotEmpty(SchedulerInfo)) {
//            schedulerTime=SchedulerInfo.get(0).getScheduleTime();
//            schedulerTimeSecond=schedulerTime*60;
//        }
//        if (schedulerTime==null||schedulerTime==0) {
//            schedulerTimeSecond=1;
//        }
//        //发消息
//        pushQueueService.sendScheduledPushMessage(
//                schedulerTimeSecond*1000L,
//                orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),
//                matchMasterResult.getMasterIdSet().stream().map(Long::valueOf).collect(Collectors.toList()),
//                orderDetailData.getPushExtraData().getHandoffTag()
//        );
//    }
//
//    public Long getSecondDayTimestamp(){
//        Calendar calendar = Calendar.getInstance();
//        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
//            calendar.add(Calendar.DATE,1);
//        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
//        }
//        calendar.set(Calendar.HOUR_OF_DAY,8);
//        calendar.set(Calendar.MINUTE,0);
//        return calendar.getTimeInMillis();
//    }
//
//
//    private boolean nightPush(){
//        if("on".equals(nightPushSwitch)){
//            return true;
//        }else{
//            return false;
//        }
//    }
//
//}
