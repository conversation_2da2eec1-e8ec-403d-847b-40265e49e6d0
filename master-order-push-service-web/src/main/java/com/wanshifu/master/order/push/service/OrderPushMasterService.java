package com.wanshifu.master.order.push.service;


import com.wanshifu.master.order.push.domain.po.OrderPushMaster;

import java.util.List;

/**
 * 描述 : 推单明细Service
 *
 * <AUTHOR> ch<PERSON><PERSON>@wshifu.com
 * @date : 2023-12-12 16:15
 */
public interface OrderPushMasterService {

    /**
     * 根据订单id查询推单明细
     * @param orderId
     * @return
     */
    List<OrderPushMaster> getOrderPushMasterList(Long orderId);



    /**
     * 根据订单id查询推单明细
     * @return
     */
    Integer deleteExpiredOrderPushMasterList();


}
