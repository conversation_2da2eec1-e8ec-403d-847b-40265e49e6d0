package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.resp.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailGroupRule;
import com.wanshifu.master.order.push.mapper.LongTailStrategyGroupMapper;
import com.wanshifu.master.order.push.mapper.LongTailStrategyMapper;
import com.wanshifu.master.order.push.repository.LongTailStrategyGroupRepository;
import com.wanshifu.master.order.push.service.LongTailStrategyGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyGroupServiceImpl implements LongTailStrategyGroupService {

    private final LongTailStrategyGroupRepository longTailStrategyGroupRepository;
    /**
     * 策略管理-获取列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<LongTailStrategyGroup> list(ListRqt rqt) {
        Page<ListResp> startPage = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        final String categoryIds = rqt.getCategoryId();
        List<String> categoryIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(categoryIds) && !StringUtils.equals("all", categoryIds)) {
            categoryIdList = Arrays.stream(categoryIds.split(",")).collect(Collectors.toList());
        }
        final List<LongTailStrategyGroup> longTailStrategyGroups = longTailStrategyGroupRepository.selectList(
                rqt.getBusinessLineId(),
                rqt.getLongTailStrategyGroupName(),
                rqt.getIsActive(),
                rqt.getCreateStartTime(),
                rqt.getCreateEndTime(),
                categoryIdList,
                rqt.getCityId()
        );

        SimplePageInfo<LongTailStrategyGroup> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(longTailStrategyGroups);
        return listRespSimplePageInfo;

    }

    /**
     * 策略管理-创建长尾单策略
     *
     * @param createRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略组合",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "longTailStrategyGroupName")
    public int create(CreateRqt createRqt) {
        final String longTailStrategyGroupName = createRqt.getLongTailStrategyGroupName();
        final String longTailStrategyGroupDesc = createRqt.getLongTailStrategyGroupDesc();
        final Integer businessLineId = createRqt.getBusinessLineId();
        final String pushType = createRqt.getPushType();
        final String categoryIds = createRqt.getCategoryIds();
        final String openCityMode = createRqt.getOpenCityMode();
        String cityIds = createRqt.getCityIds();
        if (ALL.equals(openCityMode)) {
            cityIds=ALL;
        }
        final String strategyJson = JSON.toJSONString(createRqt.getLongTailGroupRuleList());
        final String strategyExpression =getLongTailGroupExpression(createRqt.getLongTailGroupRuleList());
        final Long updateAccountId = createRqt.getCreateAccountId();
        final Long createAccountId = createRqt.getCreateAccountId();
        checkStrategyName(longTailStrategyGroupName,businessLineId,null);
        checkCityNumber(cityIds);
        checkCityCategoryUnique(cityIds,categoryIds,businessLineId,null);
        longTailStrategyGroupRepository.insert(
                longTailStrategyGroupName,
                longTailStrategyGroupDesc,
                businessLineId,
                pushType,
                categoryIds,
                openCityMode,
                cityIds,
                strategyJson,
                strategyExpression,
                updateAccountId,
                createAccountId
        );
        return 0;
    }

    private String getLongTailGroupExpression(List<LongTailGroupRule> longTailGroupRuleList){
        final JSONArray strategyExpressionArray = new JSONArray();
        longTailGroupRuleList.forEach(row->{
                    strategyExpressionArray.add(row.triggerQLExpressionJSON());
                }
        );
        return strategyExpressionArray.toJSONString();
    }

    private static final String ALL="all";
    private void checkCityCategoryUnique(String cityIds, String categoryIds,  Integer businessLineId,Long longTailStrategyGroupId) {
        List<String> categoryIdList = Arrays.asList(categoryIds.split(","));
        LongTailStrategyGroup longTailStrategyGroup = longTailStrategyGroupRepository
                .selectByCityAndCategory(cityIds, categoryIdList, businessLineId,longTailStrategyGroupId);
        if (longTailStrategyGroup != null) {
            Assert.isNull(true, StrUtil.format("城市及类目与配置【{}】存在重复!",
                    longTailStrategyGroup.getLongTailStrategyGroupName()));
        }
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Integer businessLineId, Long strategyId) {
        final LongTailStrategyGroup longTailStrategyGroup = longTailStrategyGroupRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(longTailStrategyGroup, "该业务线已存在相同策略名称!");
    }

    private void checkCityNumber(String cityIds) {
        if (!StringUtils.equals(cityIds, "all")) {
            Assert.isTrue(cityIds.split(",").length <= 100, "所选城市不得超过100个");
        }
    }

    /**
     * 策略管理-修改长尾单策略
     *
     * @param updateRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略组合",
            tableName = "long_tail_strategy_group", mapperClass = LongTailStrategyGroupMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyGroupMapper", primaryKeyFieldName = "longTailStrategyGroupId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyGroupName")
    public int update(UpdateRqt updateRqt) {
        final Long longTailStrategyGroupId = updateRqt.getLongTailStrategyGroupId();
        final String longTailStrategyGroupName = updateRqt.getLongTailStrategyGroupName();
        final String longTailStrategyGroupDesc = updateRqt.getLongTailStrategyGroupDesc();
        final Integer businessLineId = updateRqt.getBusinessLineId();
        final String categoryIds = updateRqt.getCategoryIds();
        final String pushType = updateRqt.getPushType();
        final String openCityMode = updateRqt.getOpenCityMode();
        String cityIds = updateRqt.getCityIds();
        if (ALL.equals(openCityMode)) {
            cityIds=ALL;
        }
        final String strategyJson = JSON.toJSONString(updateRqt.getLongTailGroupRuleList());
        final String strategyExpression =getLongTailGroupExpression(updateRqt.getLongTailGroupRuleList());
        final Long updateAccountId = updateRqt.getCreateAccountId();
        final Long createAccountId = updateRqt.getCreateAccountId();
        checkStrategyName(longTailStrategyGroupName,businessLineId,longTailStrategyGroupId);
        checkCityNumber(cityIds);
        checkCityCategoryUnique(cityIds,categoryIds,businessLineId,longTailStrategyGroupId);
        longTailStrategyGroupRepository.update(
                longTailStrategyGroupId,
                longTailStrategyGroupName,
                longTailStrategyGroupDesc,
                businessLineId,
                pushType,
                categoryIds,
                openCityMode,
                cityIds,
                strategyJson,
                strategyExpression,
                updateAccountId,
                createAccountId
        );
        return 0;
    }

    /**
     * 策略管理-更新策略状态（启用/禁用）
     *
     * @param enableRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略组合",
            tableName = "long_tail_strategy_group", mapperClass = LongTailStrategyGroupMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyGroupMapper", primaryKeyFieldName = "longTailStrategyGroupId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyGroupName")
    public int updateStatus(EnableRqt enableRqt) {
        final Long longTailStrategyGroupId = enableRqt.getLongTailStrategyGroupId();
        final Integer isActive = enableRqt.getIsActive();
        longTailStrategyGroupRepository.updateStatus(
                longTailStrategyGroupId,
                isActive,
                enableRqt.getUpdateAccountId()
        );
        return 0;
    }

    /**
     * 策略管理-删除长尾单策略
     *
     * @param deleteRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略组合",
            tableName = "long_tail_strategy_group", mapperClass = LongTailStrategyGroupMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyGroupMapper", primaryKeyFieldName = "longTailStrategyGroupId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyGroupName")
    public int delete(DeleteRqt deleteRqt) {
        final Long longTailStrategyGroupId = deleteRqt.getLongTailStrategyGroupId();
        longTailStrategyGroupRepository.delete(
                longTailStrategyGroupId
        );
        return 0;
    }

    /**
     * 策略管理-策略详情
     *
     * @param detailRqt
     * @return
     */
    @Override
    public LongTailStrategyGroup detail(DetailRqt detailRqt) {
        Long strategyId = detailRqt.getLongTailStrategyGroupId();
        final LongTailStrategyGroup longTailStrategyGroup = longTailStrategyGroupRepository.selectByStrategyId(strategyId);
        return longTailStrategyGroup;
    }
}
