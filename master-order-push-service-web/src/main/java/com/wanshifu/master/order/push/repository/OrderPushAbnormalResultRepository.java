package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.OrderPushAbnormalResult;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public class OrderPushAbnormalResultRepository extends BaseRepository<OrderPushAbnormalResult> {

    public int saveAbnormalResult(Long globalOrderId,String msgId,String topic,String tag,String msgBody){

        OrderPushAbnormalResult abnormalResult = new OrderPushAbnormalResult();
        abnormalResult.setGlobalOrderId(globalOrderId);
        abnormalResult.setMsgId(msgId);
        abnormalResult.setTopic(topic);
        abnormalResult.setTag(tag);
        abnormalResult.setMsgBody(msgBody);
        abnormalResult.setCreateTime(new Date());
        abnormalResult.setUpdateTime(new Date());
        return this.insertSelective(abnormalResult);

    }

}
