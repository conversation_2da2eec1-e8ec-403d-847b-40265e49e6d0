package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.annotation.Field;
import com.wanshifu.master.order.push.domain.annotation.FieldType;
import lombok.Data;


/**
 * 协议师傅
 * <AUTHOR>
 */
@Data
@Document(indexAlias = "agreement_master", type = "agreement_master")
public class AgreementMaster {

    /**
     * 协议师傅id
     */
    @Field(type = FieldType.Keyword)
    private String id;


    /**
     * 场景id,1: 无场景，4：新合约场景
     */
    @Field(type = FieldType.Integer)
    private Integer sceneId;

    /**
     * 招募id
     */
    @Field(type = FieldType.Long)
    private Long recruitId;

    /**
     * 师傅id
     */
    @Field(type = FieldType.Long)
    private Long masterId;

    /**
     * 每日最大接单量
     */
    @Field(type = FieldType.Long)
    private Long maxDailyOrder;

    /**
     * 标签
     */
    @Field(type = FieldType.Keyword)
    private String tagName;


    /**
     * 样板师傅类型，1：主力师傅，2：储备师傅
     */
    @Field(type = FieldType.Integer)
    private Integer masterCategory;

    /**
     * 指定用户人群
     */
    @Field(type = FieldType.Text)
    private String userIds;


    /**
     * 师傅价格类型,master:取师傅价（默认）,recruit:取招募活动价
     */
    @Field(type = FieldType.Keyword)
    private String masterPriceType ;

    /**
     * 定价方式,noCooperationPrice:无合作价格,unite:统一定价,city:按城市定价,regional:按区域定价
     */
    @Field(type = FieldType.Keyword)
    private String pricingType;


    /**
     * 合作区域（三级）
     */
    @Field(type = FieldType.Text)
    private String lv3DivisionIds;

    /**
     * 合作区域（四级）
     */
    @Field(type = FieldType.Text)
    private String lv4DivisionIds;


    /**
     * 自动报价涨幅百分比
     */
    @Field(type = FieldType.Long)
    private Long offerPriceIncreasePercent;


    /**
     * 直接指派方式, district:按照区域 district_price:按区域+价格(区域+订单价高于合作价),district_price_allocation:按地区+有价格派
     */
    @Field(type = FieldType.Keyword)
    private String directAppointMethod;


    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    @Field(type = FieldType.Keyword)
    private String masterSourceType;


    /**
     * 师傅所在地经纬度
     */
    @Field(type = FieldType.Text)
    private String masterLocation;


    private String isOrderBackstop;


    /**
     * 招募区域模式，geo_fence：电子围栏，district_package：街道包
     */
    @Field(type = FieldType.Keyword)
    private String recruitDistrictMethod;


    /**
     * 指派模式
     */
    @Field(type = FieldType.Text)
    private String appointType;

    /**
     * 师傅所在城市id
     */
    @Field(type = FieldType.Long)
    private Long masterCityDivisionId;

    /**
     * 合作业务
     */
    @Field(type = FieldType.Text)
    private String cooperationBusiness;

    /**
     * 合作服务id集合
     */
    @Field(type = FieldType.Text)
    private String serveIds;

    /**
     * 协议师傅状态
     */
    @Field(type = FieldType.Integer)
    private Integer agreementMasterStatus;

    /**
     * 合作开始时间
     */
    @Field(type = FieldType.Long)
    private Long cooperationStartTime;

    /**
     * 合作结束时间
     */
    @Field(type = FieldType.Long)
    private Long cooperationEndTime;




}
