package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.enums.StrategyRelateTypeEnum;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.RepushPolicy;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.rqt.repushPolicy.*;
import com.wanshifu.master.order.push.domain.vo.repushPolicy.PushStrategyVo;
import com.wanshifu.master.order.push.mapper.RepushPolicyMapper;
import com.wanshifu.master.order.push.mapper.StrategyCombinationMapper;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.RepushPolicyService;
import com.wanshifu.util.QlExpressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 16:48
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RepushPolicyServiceImpl implements RepushPolicyService {

    private final RepushPolicyRepository repushPolicyRepository;
    private final BaseSelectStrategyRepository baseSelectStrategyRepository;
    private final SortingStrategyRepository sortingStrategyRepository;
    private final FilterStrategyRepository filterStrategyRepository;
    private final StrategyRelateRepository strategyRelateRepository;
    private final RepushPolicySnapshotRepository repushPolicySnapshotRepository;


    private final  String VERSION = "v2.0.0";

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "订单重推机制",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "policyName")
    public int create(CreateRqt rqt) {
        Integer businessLineId = rqt.getBusinessLineId();
        String policyDesc = rqt.getPolicyDesc();
        String policyName = rqt.getPolicyName();
        String cityIds = rqt.getCityIds();
        String categoryIds = rqt.getCategoryIds();
        String orderFlag = rqt.getOrderFlag();
        if (businessLineId == BusinessLineEnum.FAMILY.code) {
            orderFlag = "normal";
        }

        this.checkCityNumber(cityIds);
        List<PushStrategyVo> pushStrategyVoList = rqt.getPushStrategy();
        //城市+类目+订单标识唯一
        this.checkCityCategoryUniq(orderFlag, cityIds, categoryIds, businessLineId, null);
        pushStrategyVoList.forEach(it -> this.checkStrategy(it.getStrategyCombination(), categoryIds));

        String strategyCombinationJson = this.getPushStrategyJson(pushStrategyVoList);
        Long snapshotId = repushPolicySnapshotRepository.insert(VERSION, orderFlag, businessLineId, policyDesc, policyName, cityIds, categoryIds, strategyCombinationJson, rqt.getCreateAccountId());
        Long policyId = repushPolicyRepository.insert(VERSION, orderFlag, businessLineId, snapshotId, policyDesc, policyName, cityIds, categoryIds, strategyCombinationJson, rqt.getCreateAccountId());

        pushStrategyVoList.forEach(it -> {
            PushStrategyVo.StrategyCombination strategyCombination = it.getStrategyCombination();
            strategyRelateRepository.insert(policyId, StrategyRelateTypeEnum.REPUSH_POLICY.getCode(), strategyCombination.getBaseSelectStrategyId(), strategyCombination.getFilterStrategyId(), strategyCombination.getSortingStrategyId());
        });
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "订单重推机制",
            tableName = "repush_policy", mapperClass = RepushPolicyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "repushPolicyMapper", primaryKeyFieldName = "policyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "policyName")
    public int update(UpdateRqt rqt) {
        Long policyId = rqt.getPolicyId();
        Integer businessLineId = rqt.getBusinessLineId();
        String policyDesc = rqt.getPolicyDesc();
        String policyName = rqt.getPolicyName();
        String categoryIds = rqt.getCategoryIds();
        String cityIds = rqt.getCityIds();
        String orderFlag = rqt.getOrderFlag();
        if (businessLineId == BusinessLineEnum.FAMILY.code) {
            orderFlag = "normal";
        }
        this.checkCityNumber(cityIds);
        List<PushStrategyVo> pushStrategyVoList = rqt.getPushStrategy();
        pushStrategyVoList.forEach(it -> this.checkStrategy(it.getStrategyCombination(), categoryIds));

        String strategyCombinationJson = this.getPushStrategyJson(pushStrategyVoList);
        RepushPolicy repushPolicy = repushPolicyRepository.selectByPolicyId(policyId);
        //城市+类目+订单标识唯一
        this.checkCityCategoryUniq(orderFlag,cityIds, categoryIds, businessLineId, repushPolicy.getPolicyId());
        Long snapshotId = repushPolicySnapshotRepository.insert(VERSION, orderFlag, businessLineId, policyDesc, policyName, cityIds, categoryIds, strategyCombinationJson, rqt.getUpdateAccountId());
        repushPolicyRepository.update(VERSION, orderFlag, policyId, snapshotId, businessLineId, policyDesc, policyName, cityIds, categoryIds, strategyCombinationJson, rqt.getUpdateAccountId());
        //删除关联表信息
        strategyRelateRepository.deleteByRelateIdAndRelateTypes(policyId,Lists.newArrayList(StrategyRelateTypeEnum.REPUSH_POLICY.getCode()));
        pushStrategyVoList.forEach(it -> {
            PushStrategyVo.StrategyCombination strategyCombination = it.getStrategyCombination();
            strategyRelateRepository.insert(policyId, StrategyRelateTypeEnum.REPUSH_POLICY.getCode(), strategyCombination.getBaseSelectStrategyId(), strategyCombination.getFilterStrategyId(), strategyCombination.getSortingStrategyId());
        });
        return 1;
    }


    private void checkCityNumber(String cityIds) {
        if (!StringUtils.equals(cityIds, "all")) {
            Assert.isTrue(cityIds.split(",").length <= 100, "所选城市不得超过100个");
        }
    }

    private void checkCityCategoryUniq(String orderFlag, String cityIds, String categoryIds, Integer businessLineId, Long policyId) {
        List<String> cityIdList = Arrays.asList(cityIds.split(","));
        List<String> categoryIdList =Arrays.asList(categoryIds.split(","));

        RepushPolicy repushPolicy = repushPolicyRepository.selectByCityAndCategory(orderFlag,cityIdList, categoryIdList, policyId,businessLineId);
        if (repushPolicy != null) {
            Assert.isNull(true, StrUtil.format("城市及类目与配置【{}】存在重复!", repushPolicy.getPolicyName()));
        }
    }

    private void checkStrategy(PushStrategyVo.StrategyCombination strategyCombination, String categoryIds) {

        BaseSelectStrategy baseSelectStrategy = baseSelectStrategyRepository.selectByPrimaryKey(strategyCombination.getBaseSelectStrategyId());
        Assert.isTrue((Objects.equals(baseSelectStrategy.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(baseSelectStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的初筛策略必须已被启用");

        FilterStrategy filterStrategie = filterStrategyRepository.selectByPrimaryKey(strategyCombination.getFilterStrategyId());
        Assert.isTrue((Objects.equals(filterStrategie.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(filterStrategie.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的召回策略必须已被启用");

        SortingStrategy sortingStrategy = sortingStrategyRepository.selectByPrimaryKey(strategyCombination.getSortingStrategyId());
        Assert.isTrue((Objects.equals(sortingStrategy.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(sortingStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的精排策略必须已被启用");
        //检查类目是否符合
        if (!StringUtils.equals(categoryIds, "all")) {
            Assert.isTrue(StrUtil.equals(filterStrategie.getCategoryIds(), "all") || CollectionUtils.containsAny(Arrays.asList(categoryIds.split(",")), Arrays.asList(filterStrategie.getCategoryIds().split(","))), "所选择的召回策略类目不符合!");
            Assert.isTrue(StrUtil.equals(sortingStrategy.getCategoryIds(), "all") || CollectionUtils.containsAny(Arrays.asList(categoryIds.split(",")), Arrays.asList(sortingStrategy.getCategoryIds().split(","))), "所选择的精排策略类目不符合!");
        }

    }

    private String getPushStrategyJson(List<PushStrategyVo> pushStrategyList) {
        List<PushStrategyVo> pushStrategyVos = pushStrategyList.stream().map(pushStrategy -> {

            PushStrategyVo.OpenCondition openCondition = pushStrategy.getOpenCondition();
            //开启条件参数
            List<String> qlExpressionParamsList = Lists.newArrayList();
            //且或关系 and/or
            String outerCondition = openCondition.getCondition();
            //选择的服务处理
            List<PushStrategyVo.OpenConditionItem> serveItemList = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());
            //服务的ql表达式
            List<String> serveQlExpressionList = serveItemList.stream().map(it -> {
                String term = it.getTerm();
                String itemCondition = StringUtils.equals(term, "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(term, "in") ? "or" : "and";

                List<List<Long>> serveIdList = Optional.ofNullable(it.getServeIdList()).orElse(Collections.emptyList());
                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());
                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());
                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());

                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();

                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", term, StringUtils.join(serveLevel1Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) qlExpressionParamsList.add("lv1_serve_id");
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) qlExpressionParamsList.add("lv2_serve_ids");
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) qlExpressionParamsList.add("lv3_serve_ids");
                return StrUtil.format(" ({}) ", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            //选择的非服务的开启条件处理
            List<PushStrategyVo.OpenConditionItem> otherItemList = openCondition.getItemList().stream()
                    .filter(it -> !StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());
            //非服务的开启条件参数
            List<String> otherQlExpressionParamList = otherItemList.stream().map(PushStrategyVo.OpenConditionItem::getItemName).distinct().collect(Collectors.toList());
            //非服务开启条件表达式
            List<String> otherQlExpressionList = otherItemList.stream().map(it -> QlExpressUtil.transitionQlExpress(it.getItemName(), it.getTerm(), it.getItemValue(), null))
                    .collect(Collectors.toList());
            serveQlExpressionList.addAll(otherQlExpressionList);
            qlExpressionParamsList.addAll(otherQlExpressionParamList);
            //整体策略的ql表达式
            String qlExpressStr = QlExpressUtil.transitionQlExpressStr(outerCondition, serveQlExpressionList);
            //整体策略的ql表达式参数
            String qlExpressionParams = StringUtils.join(qlExpressionParamsList, ",");
            pushStrategy.getOpenCondition().setOpenConditionQlExpression(new PushStrategyVo.OpenConditionQlExpression(qlExpressStr, qlExpressionParams));
            return pushStrategy;
        }).collect(Collectors.toList());
        return JSON.toJSONString(pushStrategyVos);
    }



    @Override
    public RepushPolicy detail(DetailRqt rqt) {
        RepushPolicy repushPolicy = repushPolicyRepository.selectByPolicyId(rqt.getPolicyId());
        return repushPolicy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "订单重推机制",
            tableName = "repush_policy", mapperClass = RepushPolicyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "repushPolicyMapper", primaryKeyFieldName = "policyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "policyName")
    public int enable(EnableRqt rqt) {
        RepushPolicy repushPolicy = repushPolicyRepository.selectByPolicyId(rqt.getPolicyId());
        return repushPolicyRepository.updateStatus(rqt.getPolicyId(), rqt.getPolicyStatus(),rqt.getUpdateAccountId());
    }

    @Override
    public SimplePageInfo<RepushPolicy> list(ListRqt rqt) {
        String categoryIds = rqt.getCategoryIds();
        String cityIds = rqt.getCityIds();
        Integer strategyStatus = rqt.getStrategyStatus();
        List<Long> categoryIdList = Lists.newArrayList();
        List<Long> cityIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(categoryIds) && !StringUtils.equals("all", categoryIds)) {
            categoryIdList = Arrays.stream(categoryIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(cityIds)) {
            cityIdList = Arrays.stream(cityIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }

        Page<RepushPolicy> startPage = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<RepushPolicy> repushPolicies = repushPolicyRepository.selectList(rqt.getOrderFlag(),rqt.getBusinessLineId(),cityIdList, categoryIdList, rqt.getPolicyName(), rqt.getCreateStartTime(), rqt.getCreateEndTime(),strategyStatus);
        SimplePageInfo<RepushPolicy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(repushPolicies);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "订单重推机制",
            tableName = "repush_policy", mapperClass = RepushPolicyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "repushPolicyMapper", primaryKeyFieldName = "policyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "policyName")
    public int delete(DeleteRqt rqt) {
        RepushPolicy repushPolicy = repushPolicyRepository.selectByPolicyId(rqt.getPolicyId());
        Assert.isTrue(repushPolicy.getPolicyStatus() == 0, "非禁用状态不可删除!");
        //删除关联表信息
        strategyRelateRepository.deleteByRelateIdAndRelateTypes(rqt.getPolicyId(),Lists.newArrayList(StrategyRelateTypeEnum.REPUSH_POLICY.getCode()));

        return repushPolicyRepository.softDeleteByStrategyId(rqt.getPolicyId());
    }
}