package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;

import com.wanshifu.master.order.push.domain.po.NewModelMatchDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface NewModelMatchDetailMapper extends IBaseCommMapper<NewModelMatchDetail> {

    List<NewModelMatchDetail> selectList(@Param("orderNo") String orderNo, @Param("masterId") Long masterId,
                                         @Param("orderCreateTimeStart") Date orderCreateTimeStart,
                                         @Param("orderCreateTimeEnd") Date orderCreateTimeEnd);
}