package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.mapper.FilterStrategyMapper;
import com.wanshifu.master.order.push.mapper.LongTailStrategyMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 策略Repsitory
 * <AUTHOR>
 */
@Repository
public class LongTailStrategyRepository extends BaseRepository<LongTailStrategy> {

    @Resource
    private LongTailStrategyMapper longTailStrategyMapper;


    public int insert(
            String longTailStrategyName,
            Integer businessLineId,
            String pushType,
            String setRangeSelect,
            String setStatusSelect,
            String setFilterRule,
            String setFilterRuleExpression,
            String setPushRule,
            String longTailStrategyDesc,
            String strategyJson,
            String strategyExpression,
            String appointGroupExpression,
            Long updateAccountId,
            Long createAccountId
    ){
        final LongTailStrategy longTailStrategy = new LongTailStrategy();
        longTailStrategy.setLongTailStrategyName(longTailStrategyName);
        longTailStrategy.setLongTailStrategyDesc(longTailStrategyDesc);
        longTailStrategy.setBusinessLineId(businessLineId);
        longTailStrategy.setPushType(pushType);
        longTailStrategy.setRangeSelect(setRangeSelect);
        longTailStrategy.setStatusSelect(setStatusSelect);
        longTailStrategy.setFilterRule(setFilterRule);
        longTailStrategy.setFilterRuleExpression(setFilterRuleExpression);
        longTailStrategy.setPushRule(setPushRule);
        longTailStrategy.setStrategyJson(strategyJson);
        longTailStrategy.setStrategyExpression(strategyExpression);
        longTailStrategy.setAppointGroupExpression(appointGroupExpression);
        longTailStrategy.setUpdateAccountId(updateAccountId);
        longTailStrategy.setCreateAccountId(createAccountId);
        return longTailStrategyMapper.insertSelective(longTailStrategy);
    }

    public int update(
            Long longTailStrategyId,
            String pushType,
            String longTailStrategyName,
            String setRangeSelect,
            String setStatusSelect,
            String setFilterRule,
            String setFilterRuleExpression,
            String setPushRule,
            String longTailStrategyDesc,
            String strategyJson,
            String strategyExpression,
            String appointGroupExpression,
            Long updateAccountId
    ){
        final LongTailStrategy longTailStrategy = new LongTailStrategy();
        longTailStrategy.setLongTailStrategyId(longTailStrategyId);
        longTailStrategy.setPushType(pushType);
        longTailStrategy.setLongTailStrategyName(longTailStrategyName);
        longTailStrategy.setLongTailStrategyDesc(longTailStrategyDesc);
        longTailStrategy.setRangeSelect(setRangeSelect);
        longTailStrategy.setStatusSelect(setStatusSelect);
        longTailStrategy.setFilterRule(setFilterRule);
        longTailStrategy.setFilterRuleExpression(setFilterRuleExpression);
        longTailStrategy.setPushRule(setPushRule);
        longTailStrategy.setStrategyJson(strategyJson);
        longTailStrategy.setStrategyExpression(strategyExpression);
        longTailStrategy.setAppointGroupExpression(appointGroupExpression);
        longTailStrategy.setUpdateAccountId(updateAccountId);
        return longTailStrategyMapper.updateByPrimaryKeySelective(longTailStrategy);
    }

    public int updateStatus(
            Long longTailStrategyId,
            Integer isActive,
            Long updateAccountId
    ){
        final LongTailStrategy longTailStrategy = new LongTailStrategy();
        longTailStrategy.setLongTailStrategyId(longTailStrategyId);
        longTailStrategy.setIsActive(isActive);
        longTailStrategy.setUpdateAccountId(updateAccountId);
        return longTailStrategyMapper.updateByPrimaryKeySelective(longTailStrategy);
    }

    public int delete(
            Long longTailStrategyId
    ){
        final LongTailStrategy longTailStrategy = new LongTailStrategy();
        longTailStrategy.setLongTailStrategyId(longTailStrategyId);
        longTailStrategy.setIsDelete(1);
        return longTailStrategyMapper.updateByPrimaryKeySelective(longTailStrategy);
    }


    public List<LongTailStrategy> selectList(List<Long> longTailStrategyIdList,Long businessLineId,
            String longTailStrategyName, Integer isActive,Date createStartTime,Date createEndTime
    ) {
        return longTailStrategyMapper.selectList(longTailStrategyIdList,businessLineId,longTailStrategyName, isActive, createStartTime,createEndTime);
    }

    public LongTailStrategy selectByStrategyId(Long strategyId) {
        LongTailStrategy longTailStrategy = longTailStrategyMapper.selectByPrimaryKey(strategyId);
        Assert.isTrue(longTailStrategy != null && Objects.equals(longTailStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return longTailStrategy;
    }

    public LongTailStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Long strategyId) {
        Example example = new Example(LongTailStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("longTailStrategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (strategyId != null) {
            criteria.andNotEqualTo("longTailStrategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(longTailStrategyMapper.selectByExample(example));
    }

}