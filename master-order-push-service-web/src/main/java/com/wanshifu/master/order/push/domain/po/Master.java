package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "t_master")
public class Master {

    @Id
    @Column(name = "master_id")
    private String masterId;

    @Column(name = "restrict_action")
    private String restrictAction;

    @Column(name = "restrict_action")
    private String coreDivisionId;

    @Column(name = "core_fourth_division_ids")
    private String coreFourthDivisionIds;

    @Column(name = "important_fourth_division_ids")
    private String importantFourthDivisionIds;

    @Column(name = "restrict_action")
    private String otherFourthDivisionId;

    @Column(name = "limit_lv1_goods_ids")
    private String limitLv1GoodsIds;

    @Column(name = "lng_lat")
    private String lngLat;

    /**
     * 师傅休息状态，0:休息,1:在线
     */
    @Column(name = "rest_state")
    private Integer restState;

    /**
     * 师傅最后活跃时间
     */
    @Column(name = "master_active_time")
    private Date masterActiveTime;


    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
