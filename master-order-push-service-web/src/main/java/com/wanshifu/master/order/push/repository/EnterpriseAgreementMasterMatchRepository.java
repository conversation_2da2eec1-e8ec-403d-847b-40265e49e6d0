package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.EnterpriseOrderAgreementMasterMatch;
import com.wanshifu.master.order.push.mapper.EnterpriseOrderAgreementMasterMatchMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class EnterpriseAgreementMasterMatchRepository extends BaseRepository<EnterpriseOrderAgreementMasterMatch> {

    @Resource
    private EnterpriseOrderAgreementMasterMatchMapper enterpriseOrderAgreementMasterMatchMapper;

    public List<EnterpriseOrderAgreementMasterMatch> selectList(String orderNo, Long masterId, Date orderCreateTimeStart, Date orderCreateTimeEnd) {

        return enterpriseOrderAgreementMasterMatchMapper.selectList(orderNo, masterId, orderCreateTimeStart, orderCreateTimeEnd);
    }

}
