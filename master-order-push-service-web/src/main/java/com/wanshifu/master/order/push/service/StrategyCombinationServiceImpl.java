//package com.wanshifu.master.order.push.service;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.lang.Assert;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
//import com.google.common.collect.Lists;
//import com.wanshifu.framework.core.page.SimplePageInfo;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.annotation.FeishuNotice;
//import com.wanshifu.master.order.push.domain.constant.CommonConstant;
//import com.wanshifu.master.order.push.domain.dto.AlternateStrategyDto;
//import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
//import com.wanshifu.master.order.push.domain.enums.StrategyRelateTypeEnum;
//import com.wanshifu.master.order.push.domain.po.*;
//import com.wanshifu.master.order.push.domain.resp.strategyCombination.DetailResp;
//import com.wanshifu.master.order.push.domain.rqt.strategyCombination.*;
//import com.wanshifu.master.order.push.domain.vo.strategyCombination.AlternateStrategyVo;
//import com.wanshifu.master.order.push.domain.vo.strategyCombination.PriorityStrategyVo;
//import com.wanshifu.master.order.push.mapper.SortingStrategyMapper;
//import com.wanshifu.master.order.push.mapper.StrategyCombinationMapper;
//import com.wanshifu.master.order.push.repository.*;
//import com.wanshifu.util.BeanCopyUtil;
//import com.wanshifu.util.QlExpressUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
//
///**
// * 描述 :  .
// *
// * <AUTHOR> <EMAIL>
// * @date : 2023-02-01 17:11
// */
//@Service
//@Slf4j
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class StrategyCombinationServiceImpl implements StrategyCombinationService {
//
//    private final StrategyCombinationRepository strategyCombinationRepository;
//    private final BaseSelectStrategyRepository baseSelectStrategyRepository;
//    private final SortingStrategyRepository sortingStrategyRepository;
//    private final FilterStrategyRepository filterStrategyRepository;
//
//    private final StrategyRelateRepository strategyRelateRepository;
//    private final StrategyCombinationSnapshotRepository strategyCombinationSnapshotRepository;
//
//    @Resource
//    private PushRuleRepository pushRuleRepository;
//
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "策略组合配置",
//            createAccountIdFieldName = "createAccountId",
//            businessLineIdFieldName = "businessLineId", configNameFieldName = "combinationName")
//    public int create(CreateRqt rqt) {
//
//        Integer businessLineId = rqt.getBusinessLineId();
//        String combinationName = rqt.getCombinationName();
//        String combinationDesc = rqt.getCombinationDesc();
//        String categoryIds = rqt.getCategoryIds();
//        String cityIds = rqt.getCityIds();
//        String orderFlag = rqt.getOrderFlag();
//        if(businessLineId==2){
//            orderFlag= "normal";
//        }
//        //优先推荐路由
//        PriorityStrategyVo priorityStrategy = rqt.getPriorityStrategy();
//        String priorityStrategyJson = JSON.toJSONString(priorityStrategy);
//        //备用推荐路由
//        AlternateStrategyVo alternateStrategy = rqt.getAlternateStrategy();
//        String alternateStrategyJson = this.getAlternateStrategyJson(alternateStrategy);
//        //校验所引用的策略
//        this.checkStrategy(priorityStrategy, alternateStrategy, categoryIds);
//        this.checkCityNumber(cityIds);
//        //城市+类目+订单标识唯一
//        this.checkCityCategoryUniq(orderFlag,cityIds, categoryIds,businessLineId, null);
//
//        Long snapshotId = strategyCombinationSnapshotRepository.insert(orderFlag,combinationName, combinationDesc, categoryIds, cityIds, priorityStrategyJson, alternateStrategyJson, businessLineId, rqt.getCreateAccountId());
//        Long combinationId = strategyCombinationRepository.create(orderFlag,combinationName,snapshotId, combinationDesc, categoryIds, cityIds, priorityStrategyJson, alternateStrategyJson, businessLineId, rqt.getCreateAccountId());
//        strategyRelateRepository.insert(combinationId, StrategyRelateTypeEnum.STRATEGY_COMBINATION_PRIORITY.getCode(), priorityStrategy.getStrategyList().getBaseSelectStrategyId(), priorityStrategy.getStrategyList().getFilterStrategyId(), priorityStrategy.getStrategyList().getSortingStrategyId());
//        if (alternateStrategy != null) {
//            strategyRelateRepository.insert(combinationId, StrategyRelateTypeEnum.STRATEGY_COMBINATION_ALTERNATE.getCode(), alternateStrategy.getStrategyList().getBaseSelectStrategyId(), alternateStrategy.getStrategyList().getFilterStrategyId(), alternateStrategy.getStrategyList().getSortingStrategyId());
//        }
//        return 1;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "策略组合配置",
//            tableName = "strategy_combination", mapperClass = StrategyCombinationMapper.class,
//            updateAccountIdFieldName = "updateAccountId",
//            mapperBeanName = "strategyCombinationMapper", primaryKeyFieldName = "combinationId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "combinationName")
//    public int update(UpdateRqt rqt) {
//        Long combinationId = rqt.getCombinationId();
//        Integer businessLineId = rqt.getBusinessLineId();
//        String combinationName = rqt.getCombinationName();
//        String combinationDesc = rqt.getCombinationDesc();
//        String categoryIds = rqt.getCategoryIds();
//        String cityIds = rqt.getCityIds();
//
//        String orderFlag = rqt.getOrderFlag();
//        if(businessLineId==2){
//            orderFlag= "normal";
//        }
//        StrategyCombination strategyCombination = strategyCombinationRepository.selectByStrategyId(combinationId);
//        //优先推荐路由
//        PriorityStrategyVo priorityStrategy = rqt.getPriorityStrategy();
//        String priorityStrategyJson = JSON.toJSONString(priorityStrategy);
//        //备用推荐路由
//        AlternateStrategyVo alternateStrategy = rqt.getAlternateStrategy();
//        //校验所引用的策略
//        this.checkStrategy(priorityStrategy, alternateStrategy, categoryIds);
//        this.checkCityNumber(cityIds);
//        //城市+类目唯一
//        this.checkCityCategoryUniq(orderFlag, cityIds, categoryIds,businessLineId, strategyCombination.getCombinationId());
//
//        String alternateStrategyJson = this.getAlternateStrategyJson(alternateStrategy);
//        Long snapshotId = strategyCombinationSnapshotRepository.insert(orderFlag,combinationName, combinationDesc, categoryIds, cityIds, priorityStrategyJson, alternateStrategyJson, businessLineId, rqt.getUpdateAccountId());
//
//        strategyCombinationRepository.update(orderFlag,combinationId, snapshotId,combinationName, combinationDesc, categoryIds, cityIds, priorityStrategyJson, alternateStrategyJson, businessLineId, rqt.getUpdateAccountId());
//        strategyRelateRepository.deleteByRelateIdAndRelateTypes(combinationId,Lists.newArrayList( StrategyRelateTypeEnum.STRATEGY_COMBINATION_PRIORITY.getCode(),StrategyRelateTypeEnum.STRATEGY_COMBINATION_ALTERNATE.getCode()));
//
//        strategyRelateRepository.insert(combinationId, StrategyRelateTypeEnum.STRATEGY_COMBINATION_PRIORITY.getCode(), priorityStrategy.getStrategyList().getBaseSelectStrategyId(), priorityStrategy.getStrategyList().getFilterStrategyId(), priorityStrategy.getStrategyList().getSortingStrategyId());
//        if (alternateStrategy != null) {
//            strategyRelateRepository.insert(combinationId, StrategyRelateTypeEnum.STRATEGY_COMBINATION_ALTERNATE.getCode(), alternateStrategy.getStrategyList().getBaseSelectStrategyId(), alternateStrategy.getStrategyList().getFilterStrategyId(), alternateStrategy.getStrategyList().getSortingStrategyId());
//        }
//        return 1;
//    }
//
//    private void checkCityCategoryUniq(String orderFlag, String cityIds, String categoryIds, Integer businessLineId, Long combinationId) {
//        List<String> cityIdList = Arrays.asList(cityIds.split(","));
//        List<String> categoryIdList = Arrays.asList(categoryIds.split(","));
//        StrategyCombination strategyCombination = strategyCombinationRepository.selectByCityAndCategory(orderFlag,cityIdList, categoryIdList, combinationId,businessLineId);
//        if (strategyCombination != null) {
//            Assert.isNull(true, StrUtil.format("城市及类目与配置【{}】存在重复!", strategyCombination.getCombinationName()));
//        }
//    }
//
//    private void checkCityNumber(String cityIds) {
//        if (!StringUtils.equals(cityIds, "all")) {
//            Assert.isTrue(cityIds.split(",").length <= 100, "所选城市不得超过100个");
//        }
//    }
//
//    private void checkStrategy(PriorityStrategyVo priorityStrategy, AlternateStrategyVo alternateStrategy, String categoryIds) {
//
//        List<Long> baseSelectStrategyIds = Lists.newArrayList(priorityStrategy.getStrategyList().getBaseSelectStrategyId());
//        List<Long> filterStrategyIds = Lists.newArrayList(priorityStrategy.getStrategyList().getFilterStrategyId());
//        List<Long> sortingStrategyIds = Lists.newArrayList(priorityStrategy.getStrategyList().getSortingStrategyId());
//        if (alternateStrategy != null) {
//            baseSelectStrategyIds.add(alternateStrategy.getStrategyList().getBaseSelectStrategyId());
//            filterStrategyIds.add(alternateStrategy.getStrategyList().getFilterStrategyId());
//            sortingStrategyIds.add(alternateStrategy.getStrategyList().getSortingStrategyId());
//        }
//
//        if (CollectionUtils.isNotEmpty(baseSelectStrategyIds)) {
//            List<BaseSelectStrategy> baseSelectStrategyList = baseSelectStrategyRepository.selectByStrategyIds(baseSelectStrategyIds);
//            Assert.isTrue(baseSelectStrategyList.stream().allMatch(it -> Objects.equals(it.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(it.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的初筛策略必须已被启用");
//        }
//        if (CollectionUtils.isNotEmpty(filterStrategyIds)) {
//            List<FilterStrategy> filterStrategies = filterStrategyRepository.selectByStrategyIds(filterStrategyIds);
//            Assert.isTrue(filterStrategies.stream().allMatch(it -> Objects.equals(it.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(it.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的召回策略必须已被启用");
//            //检查类目是否符合
//            if (!StringUtils.equals(categoryIds, "all")) {
//                boolean allMatch = filterStrategies.stream().allMatch(it -> StrUtil.equals(it.getCategoryIds(), "all") || CollectionUtils.containsAny(Arrays.asList(categoryIds.split(",")), Arrays.asList(it.getCategoryIds().split(","))));
//                Assert.isTrue(allMatch, "所选择的召回策略类目不符合!");
//            }
//        }
//        if (CollectionUtils.isNotEmpty(sortingStrategyIds)) {
//            List<SortingStrategy> sortingStrategies = sortingStrategyRepository.selectByStrategyIds(sortingStrategyIds);
//            Assert.isTrue(sortingStrategies.stream().allMatch(it -> Objects.equals(it.getStrategyStatus(), CommonConstant.STRATEGY_STATUS_1) && Objects.equals(it.getIsDelete(), CommonConstant.DELETE_STATUS_0)), "选择的精排策略必须已被启用");
//            //检查类目是否符合
//            if (!StringUtils.equals(categoryIds, "all")) {
//                boolean allMatch = sortingStrategies.stream().allMatch(it -> StrUtil.equals(it.getCategoryIds(), "all") || CollectionUtils.containsAny(Arrays.asList(categoryIds.split(",")), Arrays.asList(it.getCategoryIds().split(","))));
//                Assert.isTrue(allMatch, "所选择的精排策略类目不符合!");
//            }
//        }
//    }
//
//    /**
//     * 组装备用推荐路由json
//     *
//     * @param alternateStrategy
//     * @return
//     */
//    private String getAlternateStrategyJson(AlternateStrategyVo alternateStrategy) {
//        if (alternateStrategy == null) {
//            return "";
//        }
//        AlternateStrategyVo.OpenCondition openCondition = alternateStrategy.getOpenCondition();
//        String condition = openCondition.getCondition();
//        List<AlternateStrategyVo.OpenConditionItem> itemList = openCondition.getItemList();
//        //备用路由里的开启条件表达式
//        String openConditionQlExpress = QlExpressUtil.transitionQlExpress(condition, itemList.stream().map(it -> new QlExpressDto(it.getItemName(), it.getTerm(), String.valueOf(it.getItemValue()))).collect(Collectors.toList()));
//        //备用路由里的开启条件表达式参数名称
//        String openConditionQlExpressParams = itemList.stream().map(AlternateStrategyVo.OpenConditionItem::getItemName).distinct().collect(Collectors.joining(","));
//        AlternateStrategyDto alternateStrategyDto = new AlternateStrategyDto();
//        BeanCopyUtil.copyProperties(alternateStrategy, alternateStrategyDto);
//        alternateStrategyDto.setOpenConditionQlExpression(new AlternateStrategyDto.OpenConditionQlExpression(openConditionQlExpress, openConditionQlExpressParams));
//        return JSON.toJSONString(alternateStrategyDto);
//    }
//
//    @Override
//    public DetailResp combinationDetail(DetailRqt rqt) {
//        StrategyCombination strategyCombination = strategyCombinationRepository.selectByPrimaryKey(rqt.getCombinationId());
//
//        DetailResp detailResp = new DetailResp();
//        BeanUtil.copyProperties(strategyCombination, detailResp);
//        DetailResp.PriorityStrategyVo priorityStrategyVo = JSON.parseObject(strategyCombination.getPriorityStrategyCombination(), DetailResp.PriorityStrategyVo.class);
//        DetailResp.AlternateStrategyVo alternateStrategyVo = JSON.parseObject(strategyCombination.getAlternateStrategyCombination(), DetailResp.AlternateStrategyVo.class);
//        if(StringUtils.isBlank(priorityStrategyVo.getPushRuleType())){
//            priorityStrategyVo.setPushRuleType("old");
//        }
//
//        if(priorityStrategyVo.getStrategyList() != null){
//            priorityStrategyVo.getStrategyList().setBaseSelectStrategyName(
//                    Optional.ofNullable(baseSelectStrategyRepository.selectByPrimaryKey(priorityStrategyVo.getStrategyList().getBaseSelectStrategyId())).map(BaseSelectStrategy::getStrategyName).orElse("")
//            );
//            priorityStrategyVo.getStrategyList().setFilterStrategyName(
//                    Optional.ofNullable(filterStrategyRepository.selectByPrimaryKey(priorityStrategyVo.getStrategyList().getFilterStrategyId())).map(FilterStrategy::getStrategyName).orElse("")
//            );
//            priorityStrategyVo.getStrategyList().setSortingStrategyName(
//                    Optional.ofNullable(sortingStrategyRepository.selectByPrimaryKey(priorityStrategyVo.getStrategyList().getSortingStrategyId())).map(SortingStrategy::getStrategyName).orElse("")
//            );
//        }
//
//        Integer pushRuleId = priorityStrategyVo.getPushRuleId();
//        if (pushRuleId != null && pushRuleId > 0){
//            PushRule pushRule = pushRuleRepository.selectByPrimaryKey(pushRuleId);
//            priorityStrategyVo.setPushRuleName(pushRule.getRuleName());
//        }
//
//
//
//        if(alternateStrategyVo!=null){
//
//            if(StringUtils.isBlank(alternateStrategyVo.getPushRuleType())){
//                alternateStrategyVo.setPushRuleType("old");
//            }
//
//            if(alternateStrategyVo.getStrategyList() != null){
//                alternateStrategyVo.getStrategyList().setBaseSelectStrategyName(
//                        Optional.ofNullable(baseSelectStrategyRepository.selectByPrimaryKey(alternateStrategyVo.getStrategyList().getBaseSelectStrategyId())).map(BaseSelectStrategy::getStrategyName).orElse("")
//                );
//                alternateStrategyVo.getStrategyList().setFilterStrategyName(
//                        Optional.ofNullable(filterStrategyRepository.selectByPrimaryKey(alternateStrategyVo.getStrategyList().getFilterStrategyId())).map(FilterStrategy::getStrategyName).orElse("")
//                );
//                alternateStrategyVo.getStrategyList().setSortingStrategyName(
//                        Optional.ofNullable(sortingStrategyRepository.selectByPrimaryKey(alternateStrategyVo.getStrategyList().getSortingStrategyId())).map(SortingStrategy::getStrategyName).orElse("")
//                );
//            }
//
//            Integer alternativePushRuleId = alternateStrategyVo.getPushRuleId();
//            if (alternativePushRuleId != null && alternativePushRuleId > 0){
//                PushRule pushRule = pushRuleRepository.selectByPrimaryKey(alternativePushRuleId);
//                alternateStrategyVo.setPushRuleName(pushRule.getRuleName());
//            }
//
//        }
//        detailResp.setPriorityStrategy(priorityStrategyVo);
//        detailResp.setAlternateStrategy(alternateStrategyVo);
//        return detailResp;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "策略组合配置",
//            tableName = "strategy_combination", mapperClass = StrategyCombinationMapper.class,
//            updateAccountIdFieldName = "updateAccountId",
//            mapperBeanName = "strategyCombinationMapper", primaryKeyFieldName = "combinationId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "combinationName")
//    public int enable(EnableRqt rqt) {
//        Long combinationId = rqt.getCombinationId();
//        strategyCombinationRepository.selectByStrategyId(combinationId);
//        return strategyCombinationRepository.updateStatus(combinationId, rqt.getCombinationStatus(),rqt.getUpdateAccountId());
//    }
//
//    @Override
//    public SimplePageInfo<StrategyCombination> list(ListRqt rqt) {
//        String categoryIds = rqt.getCategoryIds();
//        String cityIds = rqt.getCityIds();
//        Integer combinationStatus = rqt.getCombinationStatus();
//        String combinationName = rqt.getCombinationName();
//        Long businessLineId = rqt.getBusinessLineId();
//        Date createStartTime = rqt.getCreateStartTime();
//        Date createEndTime = rqt.getCreateEndTime();
//        Integer pageNum = rqt.getPageNum();
//        Integer pageSize = rqt.getPageSize();
//        List<Long> categoryIdList = Lists.newArrayList();
//        List<Long> cityIdList = Lists.newArrayList();
//
//        if (StringUtils.isNotBlank(categoryIds) && !StringUtils.equals("all", categoryIds)) {
//            categoryIdList = Arrays.stream(categoryIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
//        }
//        if (StringUtils.isNotBlank(cityIds)) {
//            cityIdList = Arrays.stream(cityIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
//        }
//
//        Page<?> startPage = PageHelper.startPage(pageNum, pageSize);
//        List<StrategyCombination> strategyCombinationList = strategyCombinationRepository.selectList(rqt.getOrderFlag(),businessLineId, cityIdList, categoryIdList, combinationName, combinationStatus, createStartTime, createEndTime);
//
//        SimplePageInfo<StrategyCombination> listRespSimplePageInfo = new SimplePageInfo<>();
//        listRespSimplePageInfo.setPages(startPage.getPages());
//        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
//        listRespSimplePageInfo.setTotal(startPage.getTotal());
//        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
//        listRespSimplePageInfo.setList(strategyCombinationList);
//        return listRespSimplePageInfo;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "策略组合配置",
//            tableName = "strategy_combination", mapperClass = StrategyCombinationMapper.class,
//            updateAccountIdFieldName = "updateAccountId",
//            mapperBeanName = "strategyCombinationMapper", primaryKeyFieldName = "combinationId",
//            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "combinationName")
//    public int delete(DeleteRqt rqt) {
//        StrategyCombination strategyCombination = strategyCombinationRepository.selectByStrategyId(rqt.getCombinationId());
//        Assert.isTrue(strategyCombination.getCombinationStatus() == 0, "非禁用状态不可删除!");
//        strategyRelateRepository.deleteByRelateIdAndRelateTypes(rqt.getCombinationId(),Lists.newArrayList( StrategyRelateTypeEnum.STRATEGY_COMBINATION_PRIORITY.getCode(),StrategyRelateTypeEnum.STRATEGY_COMBINATION_ALTERNATE.getCode()));
//
//        return strategyCombinationRepository.softDeleteByStrategyId(rqt.getCombinationId());
//    }
//
//    @Override
//    public StrategyCombination detail(DetailRqt rqt) {
//        return strategyCombinationRepository.selectByPrimaryKey(rqt.getCombinationId());
//    }
//}