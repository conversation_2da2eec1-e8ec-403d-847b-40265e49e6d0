package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistribute;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Repository
public class NormalOrderDistributeRepository extends BaseRepository<NormalOrderDistribute> {


    public List<NormalOrderDistribute> selectByOrderId(Long orderId){
        NormalOrderDistribute normalOrderDistribute = new NormalOrderDistribute();
        normalOrderDistribute.setOrderId(orderId);
        normalOrderDistribute.setDistributeType("auto_receive");
        return this.select(normalOrderDistribute);
    }



}