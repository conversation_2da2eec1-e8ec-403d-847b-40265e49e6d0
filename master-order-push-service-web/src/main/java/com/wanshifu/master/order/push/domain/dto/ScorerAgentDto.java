package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:42
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScorerAgentDto extends ScorerAgent {

    private Long scoringStrategyId;
    /**
     * 初始概率 0-1 eg:0.4114804368402274
     */
    private BigDecimal initProbability =BigDecimal.ZERO;

    /**
     * 可剥削的概率
     */
    private BigDecimal canExploitProbability =BigDecimal.ZERO;

    /**
     * 实际剥削的概率
     */
    private BigDecimal exploitProbability =BigDecimal.ZERO;

    /**
     * 最终的概率
     */
    private BigDecimal finalProbability =BigDecimal.ZERO;
}
