package com.wanshifu.master.order.push.domain.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Set;

/**
 * 匹配师傅结果对象
 * <AUTHOR>
 */
@Data
public class MatchMasterResult {

    /**
     * 匹配师傅id集合
     */
    private Set<String> masterIdSet;

    /**
     * 扩展数据
     */
    private JSONObject extraData;

    public MatchMasterResult(){

    }

    public MatchMasterResult(Set<String> masterIdSet){
        this.masterIdSet = masterIdSet;
    }

    public void putExtraData(String key,Object value){
        if(extraData == null){
            extraData = new JSONObject();
        }
        extraData.put(key,value);
    }
}
