package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;


public interface OrderDistributeStrategyService {

    Integer create(CreateOrderDistributeStrategyRqt rqt);


    Integer update(UpdateOrderDistributeStrategyRqt rqt);

    Integer enable(EnableOrderDistributeStrategyRqt rqt);

    GetOrderDistributeStrategyDetailResp detail(OrderDistributeStrategyDetailRqt rqt);

    SimplePageInfo<OrderDistributeStrategy> list(GetOrderDistributeStrategyListRqt rqt);


    Integer delete(DeleteOrderDistributeStrategyRqt rqt);
}
