package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.domain.po.MasterOrderPushCount;
import lombok.Data;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

@Repository
public class MasterOrderPushCountRepository extends BaseRepository<MasterOrderPushCount> {

    public List<MasterOrderPushCount> selectByMasterIdSet(Set<String> masterIdSet){
        Condition condition = new Condition(MasterOrderPushCount.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("masterId", masterIdSet);
        return this.selectByCondition(condition);
    }


    public List<MasterOrderPushCount> selectByMasterIdSetAndDt(Set<String> masterIdSet,String dt){
        Condition condition = new Condition(MasterOrderPushCount.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("masterId", masterIdSet).andEqualTo("dt",dt);
        return this.selectByCondition(condition);
    }

}
