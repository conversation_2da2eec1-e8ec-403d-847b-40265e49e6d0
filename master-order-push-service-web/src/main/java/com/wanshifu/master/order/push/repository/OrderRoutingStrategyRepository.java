package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.mapper.OrderRoutingStrategyMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
@Repository
public class OrderRoutingStrategyRepository extends BaseRepository<OrderRoutingStrategy> {

    @Resource
    private OrderRoutingStrategyMapper orderRoutingStrategyMapper;



    public Integer insertStrategy(Integer businessLineId,String strategyName,String strategyDesc,String orderFrom,String orderTag,
                                  String categoryIds,String cityIds,Integer matchRoutingId,Long createAccountId){

        OrderRoutingStrategy orderRoutingStrategy = new OrderRoutingStrategy();
        orderRoutingStrategy.setStrategyName(strategyName);
        orderRoutingStrategy.setBusinessLineId(businessLineId);
        orderRoutingStrategy.setStrategyDesc(strategyDesc);
        orderRoutingStrategy.setOrderFrom(orderFrom);
        orderRoutingStrategy.setOrderTag(orderTag);
        orderRoutingStrategy.setCategoryIds(categoryIds);
        orderRoutingStrategy.setCityIds(cityIds);
        orderRoutingStrategy.setMatchRoutingId(matchRoutingId);
        orderRoutingStrategy.setCreateAccountId(createAccountId);
        orderRoutingStrategy.setUpdateAccountId(createAccountId);
        return this.insertSelective(orderRoutingStrategy);

    }


    public Integer updateStrategy(Integer strategyId,Integer businessLineId,String strategyName,String strategyDesc,String orderFrom,String orderTag,
                                  String categoryIds,String cityIds,Integer matchRoutingId,Long updateAccountId){

        OrderRoutingStrategy orderRoutingStrategy = new OrderRoutingStrategy();
        orderRoutingStrategy.setStrategyId(strategyId);
        orderRoutingStrategy.setBusinessLineId(businessLineId);
        orderRoutingStrategy.setStrategyName(strategyName);
        orderRoutingStrategy.setStrategyDesc(strategyDesc);
        orderRoutingStrategy.setOrderFrom(orderFrom);
        orderRoutingStrategy.setOrderTag(orderTag);
        orderRoutingStrategy.setCategoryIds(categoryIds);
        orderRoutingStrategy.setCityIds(cityIds);
        orderRoutingStrategy.setMatchRoutingId(matchRoutingId);
        orderRoutingStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(orderRoutingStrategy);

    }


    public List<OrderRoutingStrategy> selectList(Integer businessLineId,Integer strategyStatus,String strategyName, Date createStartTime, Date createEndTime,List<Long> categoryIdList,Long cityId){
        return orderRoutingStrategyMapper.selectList(businessLineId,strategyStatus,strategyName,createStartTime,createEndTime,categoryIdList,cityId);
    }


    public int updateStatus(Integer strategyId, Integer strategyStatus,Long updateAccountId) {
        OrderRoutingStrategy orderRoutingStrategy = new OrderRoutingStrategy();
        orderRoutingStrategy.setStrategyId(strategyId);
        orderRoutingStrategy.setStrategyStatus(strategyStatus);
        orderRoutingStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(orderRoutingStrategy);
    }

    public int softDeleteByStrategyId(Integer strategyId) {
        OrderRoutingStrategy orderRoutingStrategy = new OrderRoutingStrategy();
        orderRoutingStrategy.setStrategyId(strategyId);
        orderRoutingStrategy.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(orderRoutingStrategy);
    }

    public OrderRoutingStrategy selectByCityAndCategory(@Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Integer strategyId, @Param("businessLineId") Integer businessLineId){
        return orderRoutingStrategyMapper.selectByCityAndCategory(cityIdList,categoryIdList,strategyId,businessLineId);
    }


    public OrderRoutingStrategy selectRoutingStrategy(String orderTag, List<String> cityIdList, List<String> categoryIdList,  Integer businessLineId){
        return orderRoutingStrategyMapper.selectRoutingStrategy(orderTag,cityIdList,categoryIdList,businessLineId);
    }


    public OrderRoutingStrategy selectStrategy(@Param("orderTag") String orderTag,@Param("cityId") String cityId, @Param("categoryId") String categoryId,@Param("businessLineId")Integer businessLineId){
        return orderRoutingStrategyMapper.selectStrategy(orderTag,cityId,categoryId,businessLineId);
    }

}