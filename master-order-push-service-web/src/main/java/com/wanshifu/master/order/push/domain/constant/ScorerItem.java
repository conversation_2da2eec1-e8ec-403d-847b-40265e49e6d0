package com.wanshifu.master.order.push.domain.constant;

import com.ql.util.express.DefaultContext;
import com.wanshifu.util.QlExpressStatic;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ScorerItem {
    String ruleName;
    String admittance;
    String scoreRuleExpression;

    public String getRuleName() {
        return ruleName;
    }

    public BigDecimal mark(DefaultContext<String, Object> orderFeatures,
                           DefaultContext<String, Object> masterContext){
        BigDecimal result=BigDecimal.ZERO;

        if (!SymbolConstant.EMPTY_STRING.equals(admittance)) {
            final boolean admittanceResult = QlExpressStatic.QlExpressBoolean(admittance, orderFeatures);
            if (!admittanceResult) {
                return result;
            }
        }
        result = QlExpressStatic.QlExpressNumber(scoreRuleExpression, masterContext);
//        System.out.println("==================");
//        System.out.println("ruleName:"+ruleName);
//        System.out.println("admittance:"+admittance);
//        System.out.println("orderFeatures:"+orderFeatures);
//        System.out.println("scoreRuleExpression:"+scoreRuleExpression);
//        System.out.println("masterContext:"+masterContext);
//        System.out.println("result:"+result);
//        System.out.println("==================");
        return result;
    }


    public static final class ScorerItemBuilder {
        String ruleName;
        String admittance;
        String scoreRuleExpression;

        private ScorerItemBuilder() {
        }

        public static ScorerItemBuilder aScorerItem() {
            return new ScorerItemBuilder();
        }

        public ScorerItemBuilder withRuleName(String ruleName) {
            this.ruleName = ruleName;
            return this;
        }

        public ScorerItemBuilder withAdmittance(String admittance) {
            this.admittance = admittance;
            return this;
        }

        public ScorerItemBuilder withScoreRuleExpression(String scoreRuleExpression) {
            this.scoreRuleExpression = scoreRuleExpression;
            return this;
        }

        public ScorerItem build() {
            ScorerItem scorerItem = new ScorerItem();
            scorerItem.scoreRuleExpression = this.scoreRuleExpression;
            scorerItem.admittance = this.admittance;
            scorerItem.ruleName = this.ruleName;
            return scorerItem;
        }
    }
}
