package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.OrderPushMaster;
import com.wanshifu.master.order.push.repository.OrderMasterPushRepository;
import com.wanshifu.master.order.push.repository.OrderPushMasterRepository;
import com.wanshifu.master.order.push.service.OrderPushMasterService;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.po.OrderBase;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 推单明细Service实现类
 * <AUTHOR>
 */
@Service
public class OrderPushMasterServiceImpl implements OrderPushMasterService {

    @Resource
    private OrderPushMasterRepository orderPushMasterRepository;

    @Resource

    /**
     * 原推单mq topic
     */
    @Value("${orderPushMaster.user.tableNum:1}")
    private Integer orderPushMasterUserTableNum;


    /**
     * 原推单mq topic
     */
    @Value("${orderPushMaster.enterprise.tableNum:1}")
    private Integer orderPushMasterEnterpriseTableNum;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;


    /**
     * 根据订单查询推单明细
     * @param orderId
     * @return
     */
    @Override
    public List<OrderPushMaster> getOrderPushMasterList(Long orderId){

        OrderBaseComposite orderBaseComposite =  normalOrderResourceApi.getOrderBaseComposite(orderId);
        if(Objects.nonNull(orderBaseComposite)){
            OrderBase orderBase = orderBaseComposite.getOrderBase();
            String tableName = null;
            if(AccountType.USER.code.equals(orderBase.getAccountType())){
                tableName = "order_push_master_user_" + orderId % orderPushMasterUserTableNum;
            }else if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
                tableName = "order_push_master_enterprise_" + orderId % orderPushMasterEnterpriseTableNum;
            }
            if(StringUtils.isNotEmpty(tableName)){
                List<OrderPushMaster> orderPushMasterList = orderPushMasterRepository.getOrderPushMasterList(tableName,orderId);
                return orderPushMasterList;
            }

        }
        return null;
    }


    /**
     * 清除过期的推单明细数据
     * @param
     * @return
     */
    @Override
    public Integer deleteExpiredOrderPushMasterList() {
        for (int index = 0; index < orderPushMasterUserTableNum; index++) {
            Long pageSize = 200L;
            String tableName = "order_push_master_user_" + index;
            Long minId = orderPushMasterRepository.getMinId(tableName);
            Long maxId = orderPushMasterRepository.getMaxId(tableName, DateUtils.addMonths(new Date(), -1));

            if(Objects.isNull(minId) || Objects.isNull(maxId)){
                return 0;
            }

            for (Long begin = minId; begin <= maxId; begin += pageSize) {
                try {
                    Long subMaxOfferPriceId = (begin + pageSize) >= maxId ? maxId : begin + pageSize;
                    Long finalBegin = begin;
                    CompletableFuture.runAsync(() -> orderPushMasterRepository.rangeDeleteById(tableName,finalBegin, subMaxOfferPriceId));

                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return orderPushMasterUserTableNum;
    }



}
