package com.wanshifu.master.order.push.service.impl;

import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ApolloConfigUtils {



    /**
     * 不推单城市列表
     */
    @Value("${no.push.cityList:}")
    private String noPushCityList;


    /**
     * 金牌维修师傅推单开关
     */
    @Value("${push.goldMedalMaster.switch:on}")
    private String pushGoldMedalMasterSwitch;


    /**
     * 协议电子围栏开关
     */
    @Value("${agreement.geoFence.switch:off}")
    private String agreementGeoFenceSwitch;


    @Value("${mustOrderFlag.city.list}")
    private String mustOrderFlagCityList;

    @Value("${auto.punishment.push.restrict.switch:on}")
    private String autoPunishmentPushRestrictSwitch;


    /**
     * 不推单城市列表
     */
    @Value("${pushOrderToMaster.switch:on}")
    private String pushOrderToMasterSwitch;


    @Value("${enterprise.techniqueVerify.master.switch:on}")
    private String enterpriseTechniqueVerifyMasterSwitch;

    @Value("${user.techniqueVerify.master.switch:on}")
    private String userTechniqueVerifyMasterSwitch;


    @Value("${push.techniqueVerify.master.switch:on}")
    private String pushTechniqueVerifyMasterSwitch;


    /**
     * 技能验证派单技能跨类目
     */
    @Value("${techniqueVerifyOrder.crossCategory.switch:on}")
    private String techniqueVerifyOrderCrossCategorySwitch;




    /**
     * 全时师傅派单开关
     */
    @Value("${fullTimeMaser.dispatch.switch:on}")
    private String fullTimeMasterDispatchSwitch;


    /**
     * 全时师傅派单开关
     */
    @Value("${partTimeMaster.shunt.switch:on}")
    private String partTimeMasterShuntSwitch;


    /**
     * 校验订单所在的城市是否推单，true: 不推单，false: 推单
     * @param cityDivisionId
     * @return
     */
    public boolean checkIsNoPushCity(Long cityDivisionId){

        try{

            if(StringUtils.isBlank(noPushCityList)){
                return false;
            }

            if("0".equals(noPushCityList)){
                return false;
            }


            List<Long> noPushCitys = Arrays.stream(noPushCityList.split(",")).map(Long::parseLong).collect(Collectors.toList());

            return noPushCitys.contains(cityDivisionId);

        }catch(Exception e){
            log.error("checkIsNoPushCity error",e);
        }

        return false;

    }


    /**
     * 判断金牌维修师傅开关状态
     * @return
     */
    public boolean isPushGoldMedalMaster(){
        return "on".equals(pushGoldMedalMasterSwitch);
    }


    /**
     * 判断协议电子围栏开关状态
     * @return
     */
    public boolean isOpenAgreementGeoFence(){
        return "on".equals(agreementGeoFenceSwitch);
    }


    /**
     * 自动处罚限制推单开关
     * @return
     */
    public boolean isOpenAutoPunishmentPushRestrictSwitch(){
        return "on".equals(autoPunishmentPushRestrictSwitch);
    }


    /**
     * 判断必看订单开关
     * @param cityDivisionId
     * @return
     */
    public boolean checkMustOrderFlagSwitch(Long cityDivisionId){

        try{
            if(org.apache.commons.lang3.StringUtils.isBlank(mustOrderFlagCityList)){
                return false;
            }

            if ("all".equals(mustOrderFlagCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(mustOrderFlagCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }

        return false;

    }


    public boolean checkPushOrderToMasterSwitch(){
        return "on".equals(pushOrderToMasterSwitch);
    }

    /**
     * 总包直接指派技能验证师傅（新师傅）
     * @return
     */
    public boolean isOpenEnterpriseTechniqueVerifyMaster(){
        return "on".equals(enterpriseTechniqueVerifyMasterSwitch);
    }


    /**
     * 平台直接指派技能验证师傅（新师傅）
     * @return
     */
    public boolean isOpenUserTechniqueVerifyMaster(){
        return "on".equals(userTechniqueVerifyMasterSwitch);
    }


    /**
     * 平台直接指派技能验证师傅（新师傅）
     * @return
     */
    public boolean isOpenPushTechniqueVerifyMaster(){
        return "on".equals(pushTechniqueVerifyMasterSwitch);
    }


    /**
     * 技能验证派单技能跨类目
     * @return
     */
    public boolean isTechniqueVerifyOrderCrossCategory(){
        return "on".equals(techniqueVerifyOrderCrossCategorySwitch);
    }



    /**
     * 全时师傅派单开关
     * @return
     */
    public boolean isOpenFullTimeMasterDispatchSwitch(){
        return "on".equals(fullTimeMasterDispatchSwitch);
    }



    /**
     * 分时师傅分流开关
     * @return
     */
    public boolean isOpenPartTimeMasterShunt(){
        return "on".equals(partTimeMasterShuntSwitch);
    }


    public static String APPLICATION_NAME;
    @Value("${spring.application.name}")
    private void setBargainPriceEverydayFeeRuleByServiceIdSwitch(String applicationName) {
        APPLICATION_NAME = applicationName;
    }

}
