package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.RepushPolicySnapshot;
import org.springframework.stereotype.Repository;

/**
 * 重推机制快照Repository
 * <AUTHOR>
 */
@Repository
public class RepushPolicySnapshotRepository extends BaseRepository<RepushPolicySnapshot> {

    public Long insert(String version,String orderFlag, Integer businessLineId, String policyDesc, String policyName, String cityIds, String categoryIds, String strategyCombinationJson, Long loginUserId) {
        RepushPolicySnapshot repushPolicySnapshot = new RepushPolicySnapshot();
        repushPolicySnapshot.setVersion(version);
        repushPolicySnapshot.setOrderFlag(orderFlag);
        repushPolicySnapshot.setPolicyDesc(policyDesc);
        repushPolicySnapshot.setPolicyName(policyName);
        repushPolicySnapshot.setBusinessLineId(businessLineId);
        repushPolicySnapshot.setCityIds(cityIds);
        repushPolicySnapshot.setCategoryIds(categoryIds);
        repushPolicySnapshot.setStrategyCombination(strategyCombinationJson);
        repushPolicySnapshot.setCreateAccountId(loginUserId);
        repushPolicySnapshot.setUpdateAccountId(loginUserId);
        this.insertSelective(repushPolicySnapshot);
        return repushPolicySnapshot.getSnapshotId();
    }

}