package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyByIdListRqt;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 16:19
 */
public interface OrderScoringStrategyService {

    Integer create(CreateOrderScoringStrategyRqt rqt);


    Integer update(UpdateOrderScoringStrategyRqt rqt);

    Integer enable(EnableOrderScoringStrategyRqt rqt);

    OrderScoringStrategy detail(OrderScoringStrategyDetailRqt rqt);

    List<OrderScoringStrategy> selectAvailableStrategyByIdList(OrderScoringStrategyByIdListRqt rqt);

    SimplePageInfo<OrderScoringStrategy> list(GetOrderScoringStrategyListRqt rqt);


    Integer delete(DeleteOrderScoringStrategyRqt rqt);


    List<OrderScoringItemListResp> orderScoreItemList(String itemName);

}
