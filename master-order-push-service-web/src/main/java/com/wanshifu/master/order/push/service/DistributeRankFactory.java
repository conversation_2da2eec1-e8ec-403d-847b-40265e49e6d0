//package com.wanshifu.master.order.push.service;
//
//import com.ql.util.express.DefaultContext;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.dto.AgentDistributor;
//import com.wanshifu.master.order.push.domain.dto.ScorerAgent;
//import com.wanshifu.master.order.push.service.impl.DistributeFactory;
//import com.wanshifu.master.order.push.service.impl.FeatureRepository;
//import com.wanshifu.master.order.push.service.impl.RankDetail;
//import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
//import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
//import com.wanshifu.order.offer.domains.po.OrderBase;
//import com.wanshifu.order.offer.domains.po.OrderExtraData;
//import com.wanshifu.order.offer.domains.po.OrderGrab;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @description
// * @date 2025/2/28 15:38
// */
//@Component
//@RestController
//@Deprecated
//public class DistributeRankFactory {
//
//    @Autowired
//    DistributeFactory distributeFactory;
//
//    @Resource
//    private AppointedModuleResourceApi appointedModuleResourceApi;
//
//
//    @Resource
//    private FeatureRepository featureRepository;
//
//    @Resource
//    private DistributeOrderDistributeService distributeOrderDistributeService;
//
//
//    private DefaultContext<String, Object> initOrderFeatures(Long masterOrderId){
//        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(masterOrderId);
//        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
//        OrderExtraData orderExtraData = orderGrabByIdResp.getOrderExtraData();
//        final OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
//        return distributeOrderDistributeService.initOrderFeatures(orderBase,orderExtraData,orderGrab);
//    }
//
//    @PostMapping(value = "test22")
//    public void test22(){
//        scoreAgent(61323578140L,
//                new HashMap<String,Integer>(50){{
//                    put("10",19);
//                }});
//    }
//
//    public List<ScorerAgent> scoreAgent(Long masterOrderId, Map<String,Integer> agentScoreId){
//        final Set<String> agentSet = agentScoreId.keySet();
//        //订单特征初始化
//        final DefaultContext<String, Object> orderFeatures = initOrderFeatures(masterOrderId);
//        final AgentDistributor agentDistributor = distributeFactory.generateByScorer(agentScoreId);
//        System.out.println(orderFeatures);
//        //获取特征
//        featureRepository.orderFeatureReplenish(orderFeatures,agentDistributor.getOrderFeatureSet());
//        final DefaultContext<String, DefaultContext<String, Object>> agentFeatures
//                = featureRepository
//                .agentFeatures(agentSet, agentDistributor.getAgentFeatureSet(),orderFeatures);
//        System.out.println(agentFeatures);
//        //评分
//        final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
//                .withOrderId(String.valueOf(masterOrderId))
//                .withType(FieldConstant.RANK_DETAIL)
//                .build();
//        final List<ScorerAgent> scorerAgent = agentDistributor.scorer(agentSet, orderFeatures, agentFeatures, rankDetail);
//        return scorerAgent;
//    }
//}
