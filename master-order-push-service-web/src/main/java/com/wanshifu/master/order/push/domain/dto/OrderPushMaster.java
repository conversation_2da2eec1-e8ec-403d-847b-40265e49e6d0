package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 推单师傅
 */
@Data
public class OrderPushMaster {

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 推单距离
     */
    private Long pushDistance;

    /**
     * 推单距离类型
     */
    private Integer pushDistanceType;

    /**
     * 师傅经纬度
     */
    private BigDecimal masterLongitude;

    /**
     * 师傅经纬度
     */
    private BigDecimal masterLatitude;

    /**
     * 是否技能相关
     */
    private Integer accordingTechnologyPushFlag = 1;

    private Integer recruitId;

    private String tagName;

    /**
     * 推送标记：main_master, reserve_master
     */
    private String pushTag;

    private BigDecimal cooperationPrice;

    private Integer sort;

    /**
     * 跨城推送
     */
    private Integer crossCityPush;

    private BigDecimal score;

    /**
     * 自动报价金额
     */
    private BigDecimal autoPrice;

    private Integer autoOfferSort;

    /**
     * 必接订单标记
     */
    private Integer mustOrderFlag;

    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    private String masterSourceType;
    
    private Integer shuntFlag = 0;

    private Integer masterTimeType;
}
