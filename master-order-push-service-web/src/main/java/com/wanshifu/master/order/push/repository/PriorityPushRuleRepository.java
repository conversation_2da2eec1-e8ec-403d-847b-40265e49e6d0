package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.mapper.PriorityPushRuleMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class PriorityPushRuleRepository extends BaseRepository<PriorityPushRule> {

    @Resource
    private PriorityPushRuleMapper priorityPushRuleMapper;


    public int insert(Integer businessLineId,String ruleName,String ruleDesc,
                      String categoryIds,String cityIds,
                          String pushRule,String pushRuleExpression,Long createAccountId){
        PriorityPushRule priorityPushRule = new PriorityPushRule();
        priorityPushRule.setBusinessLineId(businessLineId);
        priorityPushRule.setRuleName(ruleName);
        priorityPushRule.setRuleDesc(ruleDesc);
//        priorityPushRule.setAppointType(appointType);
//        priorityPushRule.setOrderFrom(orderFrom);
        priorityPushRule.setCategoryIds(categoryIds);
        priorityPushRule.setCityIds(cityIds);
        priorityPushRule.setPushRule(pushRule);
        priorityPushRule.setPushRuleExpression(pushRuleExpression);
        priorityPushRule.setCreateAccountId(createAccountId);
        priorityPushRule.setUpdateAccountId(createAccountId);
        return this.insertSelective(priorityPushRule);
    }


    public int update(Integer ruleId,Integer businessLineId,String ruleName,String ruleDesc,String categoryIds,String cityIds,
                      String pushRule,String pushRuleExpression,Long updateAccountId){
        PriorityPushRule priorityPushRule = new PriorityPushRule();
        priorityPushRule.setRuleId(ruleId);
        priorityPushRule.setBusinessLineId(businessLineId);
        priorityPushRule.setRuleName(ruleName);
        priorityPushRule.setRuleDesc(ruleDesc);
//        priorityPushRule.setAppointType(appointType);
//        priorityPushRule.setOrderFrom(orderFrom);
        priorityPushRule.setCategoryIds(categoryIds);
        priorityPushRule.setCityIds(cityIds);
        priorityPushRule.setPushRule(pushRule);
        priorityPushRule.setPushRuleExpression(pushRuleExpression);
        priorityPushRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(priorityPushRule);
    }


    public int updateStatus(Integer ruleId, Integer ruleStatus,Long updateAccountId) {
        PriorityPushRule priorityPushRule = new PriorityPushRule();
        priorityPushRule.setRuleId(ruleId);
        priorityPushRule.setRuleStatus(ruleStatus);
        priorityPushRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(priorityPushRule);
    }


    public List<PriorityPushRule> selectList(Integer businessLineId,String ruleName,Long cityId,
                                             String categoryIds,Date createStartTime, Date createEndTime) {
        List<Long> categoryIdList = null;
        if(StringUtils.isNotBlank(categoryIds)){
            categoryIdList = Arrays.stream(categoryIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        return priorityPushRuleMapper.selectList(businessLineId,ruleName,cityId,categoryIdList,createStartTime,createEndTime);
    }


    public int softDelete(Integer ruleId){
        PriorityPushRule priorityPushRule = new PriorityPushRule();
        priorityPushRule.setRuleId(ruleId);
        priorityPushRule.setIsDelete(1);
        return this.updateByPrimaryKeySelective(priorityPushRule);
    }


    /**
     * 根据类目和城市查询策略组合
     * @param businessLineId
     * @param categoryId
     * @param cityId
     * @return
     */
    public PriorityPushRule selectByCategoryIdAndCityId(Integer businessLineId, String categoryId, String cityId){
        return CollectionUtils.getFirstSafety(priorityPushRuleMapper.selectByCategoryIdAndCityId(businessLineId,categoryId,cityId));
    }




}