package com.wanshifu.master.order.push.mapper;


import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.ListRqt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NormalOrderDistributeStrategyMapper extends IBaseCommMapper<NormalOrderDistributeStrategy> {


    List<NormalOrderDistributeStrategy> selectList(ListRqt rqt);


    List<NormalOrderDistributeStrategy> selectByCategoryIdAndCityId(@Param("businessLineId")Integer businessLineId,@Param("categoryId")String categoryId,@Param("cityId")String cityId);


}