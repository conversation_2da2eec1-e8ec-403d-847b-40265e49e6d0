package com.wanshifu.master.order.push.domain.po;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * 若已经确定为限制则result为true,否则需要进一步判断
 */
public class MasterRestriction {
    String masterId;
    String freqType;
    Integer restrictionOrderNum;
    boolean result=false;

    public String getFreqType() {
        return freqType;
    }

    public Integer getRestrictionOrderNum() {
        return restrictionOrderNum;
    }

    public MasterRestriction(String masterId) {
        this.masterId = masterId;
    }

    public void setMasterRestriction(String freqType, Integer restrictionOrderNum) {
        this.freqType = freqType;
        this.restrictionOrderNum = restrictionOrderNum;
    }

    public void setMasterRestriction(boolean result) {
        this.result = result;
    }

    public String getMasterId() {
        return masterId;
    }

    public boolean getResult() {
        return result;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MasterRestriction.class.getSimpleName() + "[", "]")
                .add("masterId='" + masterId + "'")
                .add("freqType='" + freqType + "'")
                .add("restrictionOrderNum=" + restrictionOrderNum)
                .add("result=" + result)
                .toString();
    }
}
