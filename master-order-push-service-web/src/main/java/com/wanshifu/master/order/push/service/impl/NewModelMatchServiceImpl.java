package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.RecruitBusinessEnum;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.service.NewModelMatchService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class NewModelMatchServiceImpl implements NewModelMatchService {


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private AddressApi addressApi;


    /**
     * 获取业务范围
     * @param orderDetailData
     * @return
     */
    public List<String> getCooperationBusiness(OrderDetailData orderDetailData) {

        String accountType = orderDetailData.getAccountType();
        if (StrUtil.isEmpty(accountType)) {
            return null;
        }
        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getAccountType())) {
            return null;
        }
        Integer businessLineId = orderDetailData.getBusinessLineId();
        if(businessLineId != 1 && businessLineId != 2){
            return null;
        }
        if (businessLineId == 1) {
            if(AccountType.USER.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FINISHED_PRODUCT.code);
            }else if(AccountType.ENTERPRISE.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.ENTERPRISE.code);
            }
        }else if (businessLineId == 2) {
            return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FAMILY.code);
        }
        return null;
    }


    /**
     * 匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    @Override
    public List<AgreementMaster> matchNewModelMaster(OrderDetailData orderDetailData){

//        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);
//        if(CollectionUtils.isEmpty(cooperationBusinessList)){
//            return null;
//        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 1.合作业务
//        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationBusiness",cooperationBusinessList.get(0)));

//        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));

        List<String> tagNameList = new ArrayList<>();
        tagNameList.add("exclusive_sample_plate");
        tagNameList.add("similar_direct_operate");
        boolQueryBuilder.must(QueryBuilders.termsQuery("tagName",tagNameList));

        boolQueryBuilder.must(QueryBuilders.termQuery("masterCategory",1L));
//        addressApi.getSubListByDivisionIds(orderDetailData.getSecondDivisionId());

//
//        if(orderDetailData.getFourthDivisionId() != null && orderDetailData.getFourthDivisionId() > 0){
//            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(masterMatchCondition.getFourthDivisionId())));
//        }else{
//            boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId())));
//        }

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
//        if(CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())){
//            return null;
//        }
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));

//        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        log.info("search agreementmaster request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return agreementMasterList;
    }


}
