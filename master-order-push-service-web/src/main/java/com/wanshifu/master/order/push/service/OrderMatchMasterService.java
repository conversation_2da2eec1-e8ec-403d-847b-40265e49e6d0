package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.dto.CompensateDistributeMessage;
import com.wanshifu.master.order.push.domain.message.EnterpriseAppointMessage;
import com.wanshifu.master.order.push.domain.message.OrderChangeGrabPushMessage;
import com.wanshifu.master.order.push.domain.message.PortPushMessage;
import com.wanshifu.master.order.push.domain.po.DistributeResultNotices;
import com.wanshifu.master.order.push.domain.resp.orderPush.GetOrderPushScoreResp;
import com.wanshifu.master.order.push.domain.rqt.AgentPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderPushRqt;
import com.wanshifu.master.order.push.domain.rqt.RefundSyncRqt;
import com.wanshifu.master.order.push.domain.rqt.orderPush.GetOrderPushScoreRqt;
import com.wanshifu.order.offer.domains.vo.OrderCanceledAutoGrabMessage;
import com.wanshifu.order.offer.domains.vo.appointed.CancelMasterMessage;
import com.wanshifu.order.offer.domains.vo.publish.ExclusiveOrderTransferMessage;
import com.wanshifu.order.offer.domains.vo.publish.OrderPackageConfirmMessage;
import com.wanshifu.order.offer.domains.vo.push.OrderPushNotices;

import java.util.Set;

/**
 * 订单匹配师傅service
 * <AUTHOR>
 */
public interface OrderMatchMasterService {

    /**
     * 匹配推单
     * @param rqt
     * @return
     */
    int match(OrderMatchMasterRqt rqt);

    /**
     * 代理商定向推送后、首次查看后多少分钟无人接单消费逻辑
     * @param rqt
     */
    void agentPush(AgentPushRqt rqt);


    /**
     * 总包直接指派匹配订单包
     * @param orderPushRqt
     */
    void matchOrderPackageMaster(OrderPushRqt orderPushRqt);


    /**
     * 订单退款同步
     * @param rqt
     */
    void orderRefundSync(RefundSyncRqt rqt);


    void notAppointOrderScheduledTask(OrderMatchMasterRqt rqt);


    /**
     * 创建订单推单
     * @param orderPushNotices
     */
    int orderPushNotices(OrderPushNotices orderPushNotices);


    /**
     * 总包直接指派确认订单包
     * @param orderPackageConfirmMessage
     */
    void orderPackageConfirm(OrderPackageConfirmMessage orderPackageConfirmMessage);


    /**
     * 订单转单
     * @param exclusiveOrderTransferMessage
     */
    void exclusiveOrderTransfer(ExclusiveOrderTransferMessage exclusiveOrderTransferMessage);


    /**
     * 取消指派师傅
     * @param cancelMasterMessage
     */
    void orderCancelMaster(CancelMasterMessage cancelMasterMessage);


    /**
     * 订单匹配路由
     * @param message
     */
    void orderMatchRoute(OrderMatchRouteMessage message);

    /**
     * 订单取消锁单
     * @param orderCanceledAutoGrabMessage
     */
    void orderCancelAutoGrab(OrderCanceledAutoGrabMessage orderCanceledAutoGrabMessage);

    /**
     * 订单调度结果通知
     * @param distributeResultNotices
     */
    void distributeResultNotices(DistributeResultNotices distributeResultNotices);

    /**
     * 补偿调度
     * @param distributeMessage
     */
    void compensateDistribute(CompensateDistributeMessage distributeMessage);

    /**
     * 查询推单分数
     * @param rqt
     * @return
     */
    GetOrderPushScoreResp getOrderPushScore(GetOrderPushScoreRqt rqt);

    /**
     * 总包匹配扶持师傅
     * @param rqt
     */
    void matchSupportMaster(MatchSupportMasterRqt rqt);

    void matchEnterpriseAppointMaster(EnterpriseAppointMessage message);

    /**
     * 订单推送指定师傅
     * @param masterId
     * @param orderIdSet
     */
    void directPush(Long masterId, Set<Long> orderIdSet);

    /**
     * 确认雇佣师傅
     * @param message
     */
    void confirmAppointMaster(ConfirmAppointMasterMessage message);

    /**
     * 订单关单
     * @param message
     */
    void orderClose(OrderCloseMessage message);

    /**
     * 端口推送，C端订单推送B端普通师傅
     * @param message
     */
    void portPush(PortPushMessage message);

    void pushGoldMedalMaster(PushGoldMedalMasterMessage message);


    void orderChangeGrabPush(OrderChangeGrabPushMessage message);

    int specialGroupPush(OrderMatchMasterRqt rqt);

}
