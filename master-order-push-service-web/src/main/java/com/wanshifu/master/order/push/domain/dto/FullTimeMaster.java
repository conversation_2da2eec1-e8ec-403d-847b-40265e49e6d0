package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FullTimeMaster extends MasterBaseSearch  implements Comparable<FullTimeMaster>{

    private String masterId;

    private Integer offerSort;

    private Integer isServeUnderstake;

    private BigDecimal score;

    private Integer matchDivisionLevel;

    private BigDecimal price;

    @Override
    public int compareTo(FullTimeMaster o) {
        return o.score.compareTo(this.score);
    }

}
