package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderDemandInfoByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.po.OrderDemandInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.exclusive.api.ExclusiveMasterOtherApi;
import com.wanshifu.master.exclusive.domains.api.request.other.GetRecruitIdsRqt;
import com.wanshifu.master.exclusive.domains.api.response.other.GetRecruitIdsResp;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.ExclusiveOrderLabel;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.po.ExclusiveMaster;
import com.wanshifu.master.order.push.domain.po.Master;
import com.wanshifu.master.order.push.domain.po.MasterDaily;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 品牌师傅匹配器
 * <AUTHOR>
 */
@Slf4j
@Component("brand_master")
public class BrandMasterMatcher extends AbstractOrderMasterMatcher{





    @Resource
    private InfoQueryApi infoQueryApi;





    @Resource
    private ExclusiveMasterOtherApi exclusiveMasterOtherApi;



    @Value("${night.push.switch}")
    private String nightPushSwitch;

    @Value("${night.push.start.time}")
    private String nightPushStartTime;

    @Value("${night.push.end.time}")
    private String nightPushEndTime;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private ExclusiveMasterEsRepository exclusiveMasterEsRepository;

    @Resource
    private MasterDailyRepository masterDailyRepository;

    @Resource
    private MasterRepository masterRepository;


    @Resource
    private OrderMatchRouteService orderMatchRouteRouteService;

    @Resource
    private NewModelMatchService newModelMatchService;


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){
        if(PushMode.NORMAL.code.equals(orderDetailData.getPushExtraData().getPushMode())){
            return false;
        }
        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
        if (SymbolConstant.ONE.equals(String.valueOf(emergencyOrderFlag))) {
            return false;
        }
        if (orderDetailData.getBusinessLineId()!=1) {
            return false;
        }
        if (orderDetailData.getAppointType()!=2) {
            return false;
        }
        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
        if (serveIdsArray==null||serveIdsArray.size()==0) {
            return false;
        }
        //总包指派接口
        if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
            GetOrderDemandInfoByGlobalIdRqt rqt = new GetOrderDemandInfoByGlobalIdRqt();
            rqt.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
            rqt.setEnterpriseId(orderDetailData.getAccountId());
            OrderDemandInfo orderDemandInfo =
                    infoQueryApi.getOrderServeMaster(rqt);
            if (Objects.nonNull(orderDemandInfo) && "master".equals(orderDemandInfo.getToAccountType()) &&
                    Objects.nonNull(orderDemandInfo.getToAccountId()) && orderDemandInfo.getToAccountId() > 0) {
                return false;
            }
        }


        if (!(
                AccountType.USER.code.equals(orderDetailData.getAccountType())
                        || AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())
        )) {
            return false;
        }
        if (orderDetailData.getUserId()==null) {
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(StringUtils.isNotBlank(pushMode) && (!"brand".equals(pushMode))){
            return Boolean.FALSE;
        }

        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getOrderFrom())) {
            return false;
        }
        return true;
    }



    /**
     * 获取业务范围
     * @param businessLineIdString
     * @param orderFrom
     * @param accountType
     * @param appointType
     * @return
     */
    public String getRecruitBusiness(String businessLineIdString,
                                     String orderFrom,String accountType,String appointType) {
        if (StrUtil.isEmpty(accountType)) {
            return null;
        }
        if (OrderFrom.IKEA.valueEn.equals(orderFrom)) {
            return null;
        }
        Integer businessLineId = Integer.valueOf(businessLineIdString);
        if (AccountType.ENTERPRISE.code.equals(accountType)) {
            return null;
        }
        //家庭只有一口价
        if (businessLineId == 2 && "4".equals(appointType) ) {
            return "family";
        }
        //成品只有报价
        if (AccountType.USER.code.equals(accountType) && businessLineId == 1 && "2".equals(appointType) ) {
            return "finished_product";
        }
        return null;
    }


    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){

        Long thirdDivisionId=orderDetailData.getThirdDivisionId();
        Long fourthDivisionId=orderDetailData.getFourthDivisionId();

        log.info("{},debug recruitBusiness::{},{},{},{}",
                orderDetailData.getMasterOrderId(),
                String.valueOf(orderDetailData.getBusinessLineId()),
                orderDetailData.getOrderFrom(),
                orderDetailData.getAccountType(),
                String.valueOf(orderDetailData.getAppointType())
        );
        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
        final Long userId = orderDetailData.getUserId();

        final GetRecruitIdsResp getRecruitIdsResp = getRecruitIds(
                orderDetailData.getMasterOrderId(),
                thirdDivisionId,
                fourthDivisionId,
                "",
                serveIdsArray,
                userId
        );

        if(getRecruitIdsResp == null || getRecruitIdsResp.getRecruitId() == null){
            return null;
        }

        Long recruitId = getRecruitIdsResp.getRecruitId();


        log.info("{},brandOrder result:{},{},{},{},{},recruitIds:{}",
                orderDetailData.getMasterOrderId(),
                thirdDivisionId,
                fourthDivisionId,
                "",
                serveIdsArray,
                userId,
                recruitId
        );



        masterCondition.setRecruitIds(String.valueOf(recruitId));

        Set<String> result = searchBrandMaster(masterCondition);
        final Integer bussinessId = masterCondition.getBusinessLineId();
        /**
         * 家庭业务线4级无师傅,按3级处理
         */
        if ((Objects.isNull(result) || CollectionUtils.isEmpty(result)) && "2".equals(bussinessId) && 4 == masterCondition.getDivisionMatchLevel()) {
            masterCondition.setDivisionMatchLevel(3);
            result = searchBrandMaster(masterCondition);
        }


        /**
         * 专属拉黑过滤
         */
        if (CollectionUtils.isNotEmpty(result)) {
            this.filterRestrictExclusiveMaster(String.valueOf(orderDetailData.getOrderCategoryId()),result);
        }

        //TODO
        if (CollectionUtils.isNotEmpty(result)) {
            final String orderPushExcludeMasterIds =
                    StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
            outerMasterFilter(result,orderPushExcludeMasterIds);
            log.info("debug::orderPushExcludeMasterIds:{}:{}",orderDetailData.getMasterOrderId(),orderPushExcludeMasterIds);
        }

        MatchMasterResult masterResult = new MatchMasterResult(result);
        masterResult.putExtraData(FieldConstant.RECRUIT_ID,String.valueOf(recruitId));
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData(FieldConstant.HAS_PRICE,getRecruitIdsResp.getHasPrice());


        if(masterResult != null && CollectionUtils.isNotEmpty(masterResult.getMasterIdSet())){
            List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
            if(CollectionUtils.isEmpty(agreementMasterList)){
                return masterResult;
            }
            List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(agreementMasterIdList)){
                masterResult.getMasterIdSet().removeAll(agreementMasterIdList);
            }
        }

        return masterResult;
    }

    public void filterRestrictExclusiveMaster(String categoryId,Set<String> masterIdSet){
        List<Master> masterList = masterRepository.selectByMasterIdSet(masterIdSet);
        Set<String> restrictMasterList = masterList.stream().filter(master -> Arrays.asList(master.getRestrictAction().split(",")).contains(RestrictAction.EXCLUSIVE.getActionString()) &&
                Arrays.asList(master.getLimitLv1GoodsIds().split(",")).contains(categoryId)).map(Master::getMasterId).collect(Collectors.toSet());
        masterIdSet.removeAll(restrictMasterList);
    }

    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }

    private static enum RestrictAction {
        PUSH{
            @Override
            public String getActionString(){
                return "1";
            };
        },LOGIN{
            @Override
            public String getActionString(){
                return "2";
            };
        },EXCLUSIVE{
            @Override
            public String getActionString(){
                return "14";
            };
        },NO_QUOTATION{
            @Override
            public String getActionString(){
                return "3";
            };
        },NO_APPOINT{
            @Override
            public String getActionString(){
                return "4";
            };
        };
        public String getActionString(){
            return null;
        };
    };


    /**
     * 获取招募Id
     * @param thirdDivisionId
     * @param fourthDivisionId
     * @param recruitBusiness
     * @param serveIdArray
     * @param userId
     * @return
     */
    private GetRecruitIdsResp getRecruitIds(
            Long masterOrderId,
            Long thirdDivisionId,
            Long fourthDivisionId,
            String recruitBusiness,
            List<Long> serveIdArray,
            Long userId
    ){

        GetRecruitIdsRqt getRecruitIdsRqt = new GetRecruitIdsRqt();
        getRecruitIdsRqt.setLv3DivisionId(thirdDivisionId);
        getRecruitIdsRqt.setLv4DivisionId(fourthDivisionId);
        getRecruitIdsRqt.setRecruitBusiness(recruitBusiness);
        getRecruitIdsRqt.setServeId(serveIdArray.get(0));
        getRecruitIdsRqt.setUserId(userId);
        getRecruitIdsRqt.setNowTime(new Date());
        final GetRecruitIdsResp getRecruitIdsResp = exclusiveMasterOtherApi.getRecruitIds(getRecruitIdsRqt);
        return getRecruitIdsResp;
    }


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){

    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){

        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }


        String orderVersion = orderDetailData.getOrderVersion();
        Long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), PushMode.BRAND_MASTER.code);


        log.info("nightSwitch::debug:{}{}",orderDetailData.getMasterOrderId(),nightPush());
        if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {
            //夜间推单
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
            rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
            rqt.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
            pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
            return true;
        }

        JSONObject commonFeature = new JSONObject();

        //专属订单
        //	JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, "brand");
        commonFeature.put(FieldConstant.RECRUIT_ID,matchMasterResult.getExtraData().getInteger(FieldConstant.RECRUIT_ID));
        commonFeature.put(FieldConstant.HAS_PRICE,
                matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
        commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
        pushControllerFacade.exclusivePush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);



        Boolean hasPrice = matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE);
        if (hasPrice==null) {
            hasPrice=false;
        }

        ExclusiveOrderLabel exclusiveOrderLabel = orderMatchRouteRouteService.getExclusiveOrderLabel(Long.valueOf(matchMasterResult.getMasterIdSet().iterator().next()),"brand",null,orderDetailData.getGlobalOrderId(),
                matchMasterResult.getExtraData().getLong(FieldConstant.RECRUIT_ID),matchMasterResult.getExtraData().getBoolean(FieldConstant.HAS_PRICE));
        if(Objects.nonNull(exclusiveOrderLabel) && org.apache.commons.lang.StringUtils.isNotBlank(exclusiveOrderLabel.getRecruitTagName())){
            String recruitTagName = exclusiveOrderLabel.getRecruitTagName();
            String orderPushFlag = "brand";
            if("exclusive".equals(recruitTagName)){
                orderPushFlag = "exclusive";
            }else if("direct_appointment".equals(recruitTagName)){
                orderPushFlag = "direct_appoint";
            }else if("preferred".equals(recruitTagName)){
                orderPushFlag = "excellent";
            } else if("contract".equals(recruitTagName)){
                orderPushFlag = "contract";
            } else if("brand".equals(recruitTagName)){
                orderPushFlag = "brand";
            }
            orderMatchRouteRouteService.orderMatchRoute(orderPushFlag,orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
            return true;
        }


        //有价格-直接指派
        if (FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&hasPrice) {

        }else if(FieldConstant.NEW.equals(orderDetailData.getPushExtraData().getHandoffTag())&&!hasPrice){
            //无价格的品牌-30分钟未指派-转推
            exclusiveOrderTimeSchedule(orderDetailData,matchMasterResult,30);
        }

        return true;
    }


    private void exclusiveOrderTimeSchedule(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult,Integer minutes){

        log.info("{},无价格-品牌师傅推送更多普通师傅转发延迟消息,getBusinessLineId:{},{},{}",orderDetailData.getMasterOrderId(),
                orderDetailData.getBusinessLineId());
        Integer schedulerTimeSecond=minutes*60;
        //发消息
        pushQueueService.sendNotAppointScheduledPushMessage(
                schedulerTimeSecond*1000L,
                orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),
                matchMasterResult.getMasterIdSet().stream().map(Long::valueOf).collect(Collectors.toList()),
                orderDetailData.getPushExtraData().getHandoffTag()
        );
    }



    /**
     * 搜索品牌师傅
     * @param masterCondition
     * @return
     */
    public Set<String> searchBrandMaster(MasterMatchCondition masterCondition) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        setBrandQuery(boolQuery,masterCondition);

        EsResponse<ExclusiveMaster> esResponse = exclusiveMasterEsRepository.search(boolQuery);

        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            Set<String> masterIds=new HashSet<>();
            final HashMap<String, Long> masterExpectAppointNumberMap = new HashMap<>();
            for (ExclusiveMaster exclusiveMaster : esResponse.getDataList()) {
                if (exclusiveMaster!=null) {
                    String currentMasterId=exclusiveMaster.getMasterId();
                    final Long expectAppointNumber = exclusiveMaster.getExpectAppointNumber();
                    masterExpectAppointNumberMap.put(currentMasterId,expectAppointNumber);
                    masterIds.add(currentMasterId);
                }
            }

            /**
             * 每日指派上限过滤
             * 3级地址规则 TODO
             */
            brandMasterAppointFilter(masterIds,masterExpectAppointNumberMap,masterCondition.getOrderVersion());

            if (masterIds.size() != 0) {
//			ocs黑名单√
//			账号冻结√
//			未入驻is_settle_status_normal√
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal",
                        true));
                boolQueryBuilder.must(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterCondition.freezingRecoverTime()));
                boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal",
                        masterCondition.isSettleStatusNormal()));
                masterIds = matchMasterBaseInfoBySet(masterIds, boolQueryBuilder);
            }
            return masterIds;
        }

        return null;


    }


    /**
     * 过滤师傅基础信息
     * @param masterIds
     * @param boolQueryBuilder
     * @return
     */
    private Set<String> matchMasterBaseInfoBySet(Set<String> masterIds,BoolQueryBuilder boolQueryBuilder){

        if (Objects.nonNull(boolQueryBuilder)) {
            return masterIds;
        }
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", masterIds));
        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder);
        if(Objects.isNull(esResponse) && CollectionUtils.isEmpty(esResponse.getDataList())){
            return masterIds;
        }
        return esResponse.getDataList().stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
    }


    private void brandMasterAppointFilter(Set<String> masterIds,
                                          HashMap<String, Long> masterExpectAppointNumber,
                                          String orderVersion){
        final String timeDay = DateFormatterUtil.timeStampToTimeDay(Long.valueOf(orderVersion));
        final Map<String, String> tMasterDailyFeature = getTMasterDailyFeature(masterIds, timeDay);
        Set<String> appointedMaxMasterSet=new HashSet<>();
        for (String masterId : masterIds) {
            final String brandAppointNumber = tMasterDailyFeature.get(masterId);
            final Long brandExpectNumber = masterExpectAppointNumber.get(masterId);
            if (brandAppointNumber!=null&&brandExpectNumber!=null) {
                final Long brandAppointNumberLong = Long.valueOf(brandAppointNumber);
                if (brandAppointNumberLong>=brandExpectNumber) {
                    appointedMaxMasterSet.add(masterId);
                }
            }
        }
        masterIds.removeAll(appointedMaxMasterSet);
    }


    private Map<String,String> getTMasterDailyFeature(Set<String>masterList,String dateString){
        final HashMap<String, String> resultMap = new HashMap<>();
        try {
            List<MasterDaily> masterDailyList = masterDailyRepository.selectByMasterIdSet(masterList,dateString);
            for (MasterDaily masterDaily : masterDailyList) {
                final String masterId = masterDaily.getMasterId();
                final String column = String.valueOf(masterDaily.getBrandAppointNumber());
                resultMap.put(masterId,column);
            }
        }catch (Exception e){
        }
        return resultMap;
    }


    /**
     * 合作服务
     * 合作街道
     * 生效中
     * 合作期内
     * 招募下
     * @param boolQueryBuilder
     * @param masterCondition
     */
    private void setBrandQuery(BoolQueryBuilder boolQueryBuilder, MasterMatchCondition masterCondition) {
        //专属订单服务只会有一个,多个无法参加
        final String serveIds = masterCondition.getServeIds();
        //①合作服务
        boolQueryBuilder.must(QueryBuilders.termQuery("selectServeIds",serveIds));
        //②生效中
//		mustQueryList.add(OTSUtil.stringTermQuery("exclusive_status","effective"));
        boolQueryBuilder.must(QueryBuilders.termQuery("isExclusiveStatusNormal", 1));
        //③合作期内
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //④招募下
        final String recruitIds = masterCondition.getRecruitIds();
        boolQueryBuilder.must(QueryBuilders.termQuery("recruitId",recruitIds));
        //⑤街道
        switch (masterCondition.getDivisionMatchLevel()) {
            case 4:
                boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(masterCondition.getFourthDivisionId())));
                break;
            default:
                //never 没有区域的订单不会执行到查询
                boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterCondition.getThirdDivisionId())));
                break;
        }

    }


    private boolean nightPush(){
        if("on".equals(nightPushSwitch)){
            return true;
        }else{
            return false;
        }
    }

    public Long getSecondDayTimestamp(){
        Calendar calendar = Calendar.getInstance();
        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
            calendar.add(Calendar.DATE,1);
        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
        }
        calendar.set(Calendar.HOUR_OF_DAY,8);

        Random random = new Random();
        int minute = random.nextInt(5);
        calendar.set(Calendar.MINUTE,minute);
        return calendar.getTimeInMillis();
    }


}
