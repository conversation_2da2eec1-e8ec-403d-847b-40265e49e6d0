package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.db.handler.HandleHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.DistributeType;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.message.TechniqueVerifyMasterMatchResultMessage;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.EnterpriseTechniqueVerifyMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.training.api.skillTask.SkillTaskServiceApi;
import com.wanshifu.master.training.domain.api.request.skillTask.IsMatchSkillTaskConfigRqt;
import com.wanshifu.order.config.api.TechniqueServiceApi;
import com.wanshifu.order.config.domains.dto.technique.ServeTypeAndGoodsResp;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商师傅匹配器
 * <AUTHOR>
 * @date 2023-09-13 14:04:00
 */
@Slf4j
@Component("enterprise_technique_verify_master")
public class EnterpriseTechniqueVerifyMasterMatcher extends AbstractOrderMasterMatcher {



    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private TechniqueServiceApi techniqueServiceApi;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private SkillTaskServiceApi skillTaskServiceApi;

    @Resource
    private EnterpriseTechniqueVerifyMasterMatchLogRepository enterpriseTechniqueVerifyMasterMatchLogRepository;


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){
        boolean result = checkEnterpriseTechniqueVerifyMasterCondition(orderDetailData);
        if(!result){
            Long globalOrderTraceId = orderDetailData.getGlobalOrderId();
            this.sendTechniqueMasterMatchResultMessage(globalOrderTraceId,null);
        }
        return result;
    }


    private boolean checkEnterpriseTechniqueVerifyMasterCondition(OrderDetailData orderDetailData){

        if(!apolloConfigUtils.isOpenEnterpriseTechniqueVerifyMaster()){
            return false;
        }


        IsMatchSkillTaskConfigRqt rqt = new IsMatchSkillTaskConfigRqt();
        rqt.setCategoryId(orderDetailData.getOrderCategoryId());
        rqt.setCityId(orderDetailData.getSecondDivisionId());
        Integer result = skillTaskServiceApi.isMatchSkillTaskConfig(rqt);

        if(Objects.isNull(result) || result == 0){
            return false;
        }

        if(BusinessLineEnum.ENTERPRISE.code != orderDetailData.getBusinessLineId()){
            return false;
        }


        if(Objects.isNull(orderDetailData.getFourthDivisionId()) || orderDetailData.getFourthDivisionId() == 0){
            return false;
        }

        //加急单
        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            return false;
        }

        //旧件寄回
        if(orderDetailData.getIsSendBackOld() != null && orderDetailData.getIsSendBackOld() == 1 && "pay_another".equals(orderDetailData.getSendBackOldPayMode())){
            return false;
        }

        //需师傅带配件
        if (orderDetailData.getIsParts() != null && orderDetailData.getIsParts() == 1) {
            return false;
        }

        //有当日装服务
        if (CollectionUtil.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("same_day_outfit")) {
            return false;
        }

        return  true;
    }




    private void insertAgreementMasterMatch(OrderDetailData orderDetailData,String matchFailReason){
        EnterpriseTechniqueVerifyMasterMatchLog log = new EnterpriseTechniqueVerifyMasterMatchLog();
        log.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
        log.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
        log.setMasterId(0L);
        log.setOrderNo(orderDetailData.getOrderNo());
        log.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        log.setIsMatchSucc(0);
        log.setMatchFailReason(matchFailReason);
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        this.enterpriseTechniqueVerifyMasterMatchLogRepository.insertSelective(log);
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition condition) {


        try {
            List<MasterBaseSearch> techniqueVerifyMasterList = matchTechniqueVerifyMaster(orderDetailData, condition);
            if (CollectionUtils.isEmpty(techniqueVerifyMasterList)) {
                insertAgreementMasterMatch(orderDetailData, "未匹配到技能验证师傅");
                return null;
            }


            Set<String> masterSet = techniqueVerifyMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());

            PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData, masterSet);

            DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();


            //获取策略
            final OrderDistributor orderDistributor = distributeFactory
                    .matchDistributor(orderDetailData, orderFeatureContext, DistributeType.ENTERPRISE_APPOINT_NEW_MASTER.getCode());

            if (!orderDistributor.isMatched()) {
                insertAgreementMasterMatch(orderDetailData, "未配置总包直接指派新师傅调度策略");
                return null;
            }


            List<EnterpriseTechniqueVerifyMasterMatchLog> matchLogList = techniqueVerifyMasterList.stream().map(techniqueVerifyMaster -> {
                EnterpriseTechniqueVerifyMasterMatchLog matchLog = new EnterpriseTechniqueVerifyMasterMatchLog();
                matchLog.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
                matchLog.setMasterId(Long.valueOf(techniqueVerifyMaster.getMasterId()));
                matchLog.setOrderVersion(orderDetailData.getOrderVersion());
                matchLog.setIsMatchSucc(1);
                matchLog.setMatchFailReason("");
                matchLog.setOrderNo(orderDetailData.getOrderNo());
                matchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
                matchLog.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
                matchLog.setCreateTime(new Date());
                matchLog.setUpdateTime(new Date());
                return matchLog;
            }).collect(Collectors.toList());

            featureRepository.getMasterFeatures(pushFeature, masterSet, orderDistributor.getMasterFeatureSet());

            DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();


            log.info("masterFeatures:" + JSON.toJSONString(masterFeatures));


            //过滤排序
            final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();


            List<ScorerMaster> scorerMasterList = orderDistributor
                    .rank(masterSet, orderFeatureContext, masterFeatures, rankDetail);

            Map<Long, String> filterReasonMap = new HashMap<>();


            try {
                if (Objects.nonNull(rankDetail) && org.apache.commons.lang.StringUtils.isNotBlank(rankDetail.getDetailInfo())) {
                    Map<String, Object> filterDetailsMap = (Map) JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                    for (String key : filterDetailsMap.keySet()) {
                        JSONArray jsonArray = (JSONArray) filterDetailsMap.get(key);
                        jsonArray.forEach(master -> {
                            filterReasonMap.put(Long.valueOf(String.valueOf(master)), key);
                        });
                    }
                }

            } catch (Exception e) {
                log.error("rankDetail error", e);
            }


            List<String> masterList = CollectionUtils.isNotEmpty(scorerMasterList) ? scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList()) : new ArrayList<>();

            matchLogList.forEach(matchLog -> {

                if ((Objects.nonNull(matchLog.getIsFilter()) && matchLog.getIsFilter() == 1)
                        || !Strings.isNullOrEmpty(matchLog.getFilterReason())) {
                    //前面计价时取最低价已经过滤或者设置了过滤原因的,直接continue
                    return;
                }
                matchLog.setIsFilter(masterList.contains(String.valueOf(matchLog.getMasterId())) ? 0 : 1);
                matchLog.setFilterReason(masterList.contains(String.valueOf(matchLog.getMasterId())) ? "" : filterReasonMap.getOrDefault(matchLog.getMasterId(),""));

            });


            if(CollectionUtils.isNotEmpty(scorerMasterList)){
                ScorerMaster scorerMaster = distribute(scorerMasterList,orderDistributor.getDistributeRule());

                matchLogList.forEach(matchLog ->{

                    if (Objects.nonNull(matchLog.getIsFilter())
                            && matchLog.getIsFilter() == 1) {
                        return;
                    }
                    matchLog.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                    matchLog.setIsDistribute(String.valueOf(matchLog.getMasterId()).equals(scorerMaster.getMasterId()) ? 1 : 0);
                });

                if(CollectionUtils.isNotEmpty(matchLogList)){
                    enterpriseTechniqueVerifyMasterMatchLogRepository.insertList(matchLogList);
                }
                return new MatchMasterResult(Collections.singleton(scorerMaster.getMasterId()));
            }else{
                if(CollectionUtils.isNotEmpty(matchLogList)){
                    enterpriseTechniqueVerifyMasterMatchLogRepository.insertList(matchLogList);
                }
                return new MatchMasterResult();
            }
        }catch(Exception e){
            log.error("enterprise match techniqueVerifyMaster error",e);
        }

        return new MatchMasterResult();
    }



    private ScorerMaster distribute(List<ScorerMaster> scorerMasterList,String distributeRule){
        if(OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            return scorerMasterList.get(0);
        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            int index = scorerMasterList.size() >= 50 ? 50 : scorerMasterList.size();
            Random random = new Random();
            int randomNum = random.nextInt(index);
            return scorerMasterList.subList(0,index).get(randomNum);
        }else if(OrderDistributeRule.RANDOM.getCode().equals(distributeRule)){
            Random random = new Random();
            int randomNum = random.nextInt(scorerMasterList.size());
            return scorerMasterList.get(randomNum);
        }
        return null;
    }





    public MatchQueryBuilder stringMatchQueryBuilder(String fieldName, String value, Operator operator) {
        //设置查询类型为MatchQuery。
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        matchQueryBuilder.operator(operator);
        return matchQueryBuilder;
    }

    /**
     * 索引API
     */
    public BoolQueryBuilder boolQueryBuilder(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch) {
        List<String> shouldConditionList = new ArrayList<>(shouldConditions);
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        shouldConditionList.stream()
                .map(shouldCondition->shouldQuery.should(stringMatchQueryBuilder(fieldName,shouldCondition,Operator.AND)))
                .collect(Collectors.toList());
        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }




    /**
     * 匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<MasterBaseSearch> matchTechniqueVerifyMaster(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));
        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termsQuery("toTechniqueVerifyCategoryIds", Lists.newArrayList(orderDetailData.getOrderCategoryId())));


        if(!apolloConfigUtils.isTechniqueVerifyOrderCrossCategory()){
            String orderTechniqueIds = orderDetailData.getOrderTechniques().replace("|",",");

            Map<Long, List<ServeTypeAndGoodsResp>> respMap = techniqueServiceApi.getServeTypeAndGoodsByTechniqueIds(orderTechniqueIds);

            Set<String> finalTechniqueIds =  orderDetailData.getOrderTechniqueSet().stream().filter(techniqueIds -> {
                Set<Long> techniqueIdSet = Arrays.asList(techniqueIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
                for(Long techniqueId : techniqueIdSet){
                    List<ServeTypeAndGoodsResp> respList = respMap.get(techniqueId);
                    if(CollectionUtils.isEmpty(respList) || (! respList.get(0).getGoodsId().equals(orderDetailData.getOrderCategoryId()))){
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toSet());



            BoolQueryBuilder shouldQuery = this
                    .boolQueryBuilder(
                            "masterTechniqueIds",
                            finalTechniqueIds,
                            1);
            boolQueryBuilder.filter(shouldQuery);
        }else{

            BoolQueryBuilder shouldQuery = this
                    .boolQueryBuilder(
                            "masterTechniqueIds",
                            orderDetailData.getOrderTechniqueSet(),
                            1);
            boolQueryBuilder.filter(shouldQuery);
        }


        log.info("matchTechniqueVerifyMaster boolQueryBuilder" + boolQueryBuilder.toString());
        List<MasterBaseSearch> techniqueVerifyMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                techniqueVerifyMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return techniqueVerifyMasterList;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult){


    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){


        if( Objects.nonNull(matchMasterResult) && CollectionUtils.isNotEmpty(matchMasterResult.getMasterIdSet())){
            sendTechniqueMasterMatchResultMessage(orderDetailData.getGlobalOrderId(),Long.valueOf(matchMasterResult.getMasterIdSet().iterator().next()));
        }else{
            sendTechniqueMasterMatchResultMessage(orderDetailData.getGlobalOrderId(),null);
        }
        return true;

    }


    private void sendTechniqueMasterMatchResultMessage(Long globalOrderTraceId,Long masterId){
        TechniqueVerifyMasterMatchResultMessage message = new TechniqueVerifyMasterMatchResultMessage();
        message.setGlobalOrderTraceId(globalOrderTraceId);
        if(Objects.nonNull(masterId)){
            message.setSupportMasterId(masterId);
        }
        pushQueueService.sendTechniqueMasterMatchResultMessage(message,1L);
    }




}
