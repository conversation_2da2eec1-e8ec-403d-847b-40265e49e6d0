package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.PortPushService;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 家庭协议师傅匹配
 * @date 2025/4/24 19:19
 */
@Slf4j
@Component("family_agreement_master")
public class FamilyAgreementMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private PortPushService portPushService;

    /**
     * 家庭协议师傅推单后延迟推普通师傅时间
     * 默认10分钟
     */
    @Value("${family.agreement.re.push.normal.delay.time:10}")
    private int familyAgreementRePushNormalDelayTime;

    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {

        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            //不推单的城市
            return false;
        }

        if(PushMode.NORMAL.code.equals(orderDetailData.getPushExtraData().getPushMode())){
            return false;
        }
        if (orderDetailData.getBusinessLineId() != 2) {
            //不是家庭订单，不匹配
            return false;
        }
        if (!"toc".equals(orderDetailData.getPushExtraData().getMasterSourceType())) {
            //不推c端师傅，不匹配
            return false;
        }
        return true;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {
        Long orderId = orderDetailData.getMasterOrderId();
        log.info("familyAgreementMasterList match orderId:{}", orderId.toString());

        List<FamilyAgreementMasterDto> familyAgreementMasterDtoList = matchFamilyAgreementMaster(orderDetailData, masterCondition);
        if (CollectionUtil.isEmpty(familyAgreementMasterDtoList)) {
            return null;
        }

        MatchMasterResult masterResult = new MatchMasterResult();
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData("family_agreement_master_list",familyAgreementMasterDtoList);

        Set<String> masterSet = familyAgreementMasterDtoList.stream().map(FamilyAgreementMasterDto::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        masterResult.setMasterIdSet(masterSet);
        return masterResult;
    }

    /**
     * 匹配家庭协议师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    private List<FamilyAgreementMasterDto> matchFamilyAgreementMaster(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {
        BoolQueryBuilder boolQueryBuilder = buildQuery(orderDetailData, masterCondition);
        if (Objects.isNull(boolQueryBuilder)) {
            return null;
        }
        log.info("familyAgreementMasterList search  request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }
        log.info("familyAgreementMasterList agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        List<FamilyAgreementMasterDto> familyAgreementMasterDtoList = new ArrayList<>();

        for (AgreementMaster agreementMaster : agreementMasterList) {
            FamilyAgreementMasterDto familyAgreementMasterDto = new FamilyAgreementMasterDto();
            familyAgreementMasterDto.setMasterId(String.valueOf(agreementMaster.getMasterId()));
            familyAgreementMasterDto.setRecruitId(String.valueOf(agreementMaster.getRecruitId()));
            familyAgreementMasterDto.setId(agreementMaster.getRecruitId() + ":" + agreementMaster.getMasterId());
            familyAgreementMasterDto.setRecruitTagName(agreementMaster.getTagName());
            familyAgreementMasterDtoList.add(familyAgreementMasterDto);
        }

        return familyAgreementMasterDtoList;
    }

    /**
     * 构建查询家庭直约师傅query
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private BoolQueryBuilder buildQuery(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationBusiness", "family"));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));

        Long fourthDivisionId = orderDetailData.getFourthDivisionId();
        if (Objects.nonNull(fourthDivisionId) && fourthDivisionId > 0) {
            //有四级地址按4级地址匹配
            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getFourthDivisionId())));
        } else {
            //无四级地址分2种
            //1: 东莞、仙桃这种特殊地址，订单那边是把4级地址提到了3级地址位置； 而师傅那边是上级地址没有，下级地址填充上，没有改动地址层级，所以东莞跟仙桃的处理方式还不一样
            //2：确实是没有识别到四级地址，则按3级地址匹配
            //统一先请求地址库判断地址层级，再根据层级进行匹配
            Address address = addressCommon.getAddressByDivisionId(orderDetailData.getThirdDivisionId());
            if (Objects.isNull(address)) {
                log.error("familyAgreementMasterList match address is null,divisionId:{}", orderDetailData.getThirdDivisionId());
                return null;
            }
            if (address.getDivisionLevel() == 4) {
                //三级地址
                boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
            } else if (address.getDivisionLevel() == 5) {
                //四级地址
                boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
            }
        }

        //合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //c端师傅
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", MasterSourceType.TOC.code));
        //直约师傅
        boolQueryBuilder.must(QueryBuilders.termQuery("tagName", "direct_appointment"));

        //合作服务
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            log.error("familyAgreementMasterList match lv3ServeIdList is empty!");
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        return boolQueryBuilder;
    }

    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {

    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }
        String orderVersion = orderDetailData.getOrderVersion();
        long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp),  PushMode.FAMILY_AGREEMENT_MASTER.getCode());


        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.FAMILY_AGREEMENT_MASTER.getCode());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
        commonFeature.put("family_agreement_master_list", matchMasterResult.getExtraData().get("family_agreement_master_list"));
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        pushControllerFacade.familyAgreementMasterPush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);

        //家庭协议师傅推单后延迟推普通师傅
        pushQueueService.sendFamilyAgreementPushMessage(orderDetailData, familyAgreementRePushNormalDelayTime * 60 * 1000L);


        log.info("发送家庭协议师傅推单后延迟推普通师傅推单mq消息,globalOrderId:{}",orderDetailData.getGlobalOrderId());

        return true;
    }
}
