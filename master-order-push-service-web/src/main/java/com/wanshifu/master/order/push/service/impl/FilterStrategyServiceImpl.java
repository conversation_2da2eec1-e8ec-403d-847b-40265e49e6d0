package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.FilterStrategyRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.MasterQuotaFeatureExpressionDto;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.StrategyRelate;
import com.wanshifu.master.order.push.domain.resp.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;
import com.wanshifu.master.order.push.domain.vo.filterStrategy.RuleItem;
import com.wanshifu.master.order.push.mapper.FilterStrategyMapper;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.FilterStrategyService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FilterStrategyServiceImpl implements FilterStrategyService {

    private final FilterStrategyRepository filterStrategyRepository;

    private final StrategyRelateRepository strategyRelateRepository;

    private final MasterQuotaValueRepository backendMasterQuotaValueRepository;

    private final MasterQuotaRepository masterQuotaRepository;


    private final FilterStrategySnapshotRepository filterStrategySnapshotRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "普通订单匹配", level2MenuName = "召回策略管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {
        List<RuleItem> ruleList = rqt.getRuleList();
        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        String strategyDesc = rqt.getStrategyDesc();
        String categoryIds = rqt.getCategoryIds();
        String orderFlag = rqt.getOrderFlag();
        if(businessLineId==2){
            orderFlag= "normal";
        }
        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), null);
        this.checkRuleParams(ruleList);

        //召回规则表达式
        List<FilterStrategyRuleExpressionDto> ruleExpressionJson = this.getRuleExpressions(ruleList, businessLineId);

        Long snapshotId = filterStrategySnapshotRepository.insert(orderFlag,strategyName, strategyDesc, JSON.toJSONString(ruleList), JSON.toJSONString(ruleExpressionJson), businessLineId, categoryIds, rqt.getCreateAccountId());

        return filterStrategyRepository.create(orderFlag,strategyName, snapshotId,strategyDesc, JSON.toJSONString(ruleList), JSON.toJSONString(ruleExpressionJson), businessLineId, categoryIds, rqt.getCreateAccountId(),1);
    }


    private List<FilterStrategyRuleExpressionDto> getRuleExpressions(List<RuleItem> ruleList, Integer businessLineId) {

        List<String> allMasterQuotaCodes = ruleList.stream().flatMap(it -> it.getFilterRule().getItemList().stream()).map(RuleItem.FilterRuleItem::getItemName).filter(it -> !StringUtils.equals("master_group", it)).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 3) {
                businessLineId = 1;
            } else if (businessLineId == 999) {
                businessLineId = 2;
            }
        }

        //<指标编码,指标条件表达式>   例如  <master_work_status,{"preCondition":{"ruleExpression":"is_high_quality == 1","ruleExpressionParams":"is_high_quality"},"calculateExpression":"allMatch(itemName,'term',value) "}>
        Map<String, String> masterQuotaFeatureExpressionMap = masterQuotaRepository.selectByCodes(allMasterQuotaCodes, businessLineId)
                .stream().collect(Collectors.toMap(MasterQuota::getQuotaCode, MasterQuota::getFeatureExpression));

        return ruleList.stream().map(rule -> {

            RuleItem.OpenCondition openCondition = rule.getOpenCondition();

            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user", "user_group");
                        if (specialTerms.contains(s.getItemName())) {
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<RuleItem.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }


            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(RuleItem.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));


            RuleItem.FilterRule filterRule = rule.getFilterRule();
            String condition = filterRule.getCondition();

            //召回规则表达式参数
            List<String> filterRuleParamList = new ArrayList<>(Collections.emptyList());
            List<String> masterQuotaExpressions = filterRule.getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                filterRuleParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(), null);

                //指标是否包含前置条件
                String masterQuotaFeatureJson = masterQuotaFeatureExpressionMap.get(it.getItemName());
                if (StringUtils.isNotBlank(masterQuotaFeatureJson)) {
                    MasterQuotaFeatureExpressionDto masterQuotaFeatureExpressionDto = JSON.parseObject(masterQuotaFeatureJson, MasterQuotaFeatureExpressionDto.class);
                    String calculateExpression = masterQuotaFeatureExpressionDto.getCalculateExpression();
                    if (StringUtils.isNotBlank(calculateExpression)) {
                        //特殊的函数计算表达式
                        masterQuotaFilterRuleExpression = " " + calculateExpression
                                .replace("itemName", itemName)
                                .replace("term", term)
                                .replace("value", it.getItemValue());
                    }

                    if (masterQuotaFeatureExpressionDto.getPreCondition() != null) {
                        filterRuleParamList.addAll(Arrays.asList(masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpressionParams().split(",")));
                        masterQuotaOpenConditionRuleExpression = masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpression();
                        //(规则1开启条件满足 ? 规则1 : true )
                        //(规则1开启条件满足 ? 规则1 : false )
                        masterQuotaFilterRuleExpression = StrUtil.format(" ({} ?{}: {}) ", masterQuotaOpenConditionRuleExpression, masterQuotaFilterRuleExpression, StringUtils.equals(condition, "and"));
                    }
                }
                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());
            // (规则1开启条件满足 ? 规则1 : true ) and 规则2 and 规则3
            // (规则1开启条件满足 ? 规则1 : false ) or 规则2 or 规则3
            String filterRuleExpression = QlExpressUtil.transitionQlExpressStr(condition, masterQuotaExpressions);

            String filterRuleParams = filterRuleParamList.stream().distinct().collect(Collectors.joining(","));
            return new FilterStrategyRuleExpressionDto(rule.getRuleName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }

    private void checkRuleParams(List<RuleItem> ruleLists) {
        List<RuleItem> ruleListCopy = BeanCopyUtil.copyListProperties(ruleLists, RuleItem.class, null);
        //规则名称
        List<String> ruleNames = ruleListCopy.stream().map(RuleItem::getRuleName).distinct().collect(Collectors.toList());
        Assert.isTrue(ruleListCopy.size() == ruleNames.size(), "不可存在相同的规则名称");
        //不能存在开启条件一样的两条规则,仅顺序不同，也认为是相同的条件
        List<RuleItem.OpenCondition> distinctOpenConditions = ruleListCopy.stream().map(RuleItem::getOpenCondition).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> {
            List<RuleItem.OpenConditionItem> itemList = BeanCopyUtil.copyListProperties(o.getItemList(), RuleItem.OpenConditionItem.class, null);
            itemList.sort(Comparator.comparingInt(c -> c.getItemName().hashCode()));
            String itemListStr = itemList.stream().map(it -> {
                List<List<Long>> serveIdList = Optional.ofNullable(it.getServeIdList()).orElse(Collections.emptyList());
                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).sorted(Long::compare).collect(Collectors.toList());
                return it.getItemName() + "_" + it.getTerm() + "_" + it.getItemValue() + "_" + serveLevel1Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel2Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel3Ids.stream().map(Object::toString).collect(Collectors.joining(","));
            }).collect(Collectors.joining(";"));
            return o.getCondition() + ";" + itemListStr;
        }))), ArrayList::new));
        Assert.isTrue(distinctOpenConditions.size() == ruleLists.stream().map(RuleItem::getOpenCondition).count(), "不能存在开启条件一样的两条规则");
        boolean checkServe = ruleLists.stream().allMatch(it -> it.getOpenCondition().getItemList().stream().allMatch(item -> !"serve".equals(item.getItemName()) || !(CollectionUtils.isEmpty(item.getServeIdList()))));
        Assert.isTrue(checkServe, "开启条件选择服务时,至少选择一个服务!");
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Integer businessLineId, Long strategyId) {
        FilterStrategy filterStrategy = filterStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(filterStrategy, "该业务线已存在相同策略名称!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "update", level1MenuName = "普通订单匹配", level2MenuName = "召回策略管理",
            tableName = "filter_strategy", mapperClass = FilterStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "filterStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        String strategyDesc = rqt.getStrategyDesc();
        String categoryIds = rqt.getCategoryIds();
        List<RuleItem> ruleList = rqt.getRuleList();
        String orderFlag = rqt.getOrderFlag();
        if(businessLineId==2){
            orderFlag= "normal";
        }
        FilterStrategy filterStrategy = filterStrategyRepository.selectByStrategyId(strategyId);
        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), strategyId);
        //召回规则表达式
        List<FilterStrategyRuleExpressionDto> ruleExpressionJson = this.getRuleExpressions(ruleList, businessLineId);
        Long snapshotId = filterStrategySnapshotRepository.insert(orderFlag,strategyName, strategyDesc, JSON.toJSONString(ruleList), JSON.toJSONString(ruleExpressionJson), businessLineId, categoryIds, rqt.getUpdateAccountId());
        return filterStrategyRepository.update(orderFlag,strategyId,snapshotId, strategyName, strategyDesc, JSON.toJSONString(ruleList), JSON.toJSONString(ruleExpressionJson), businessLineId, categoryIds, rqt.getUpdateAccountId());
    }

    @Override
    public FilterStrategy detail(DetailRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        FilterStrategy filterStrategy = filterStrategyRepository.selectByStrategyId(strategyId);
       return filterStrategy;
    }

    @Override
    public SimplePageInfo<FilterStrategy> list(ListRqt rqt) {
        Page<ListResp> startPage = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        String categoryIds = rqt.getCategoryIds();
        List<Long> categoryIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(categoryIds) && !StringUtils.equals("all", categoryIds)) {
            categoryIdList = Arrays.stream(categoryIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        List<FilterStrategy> filterStrategyList = filterStrategyRepository.selectList(rqt.getBusinessLineId(), rqt.getStrategyName(), rqt.getStrategyStatus(), rqt.getCreateStartTime(), rqt.getCreateEndTime(), categoryIdList,rqt.getOrderFlag());

        SimplePageInfo<FilterStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(filterStrategyList);
        return listRespSimplePageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "普通订单匹配", level2MenuName = "召回策略管理",
            tableName = "filter_strategy", mapperClass = FilterStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "filterStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int enable(EnableRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        filterStrategyRepository.selectByStrategyId(strategyId);
        //1:启用 0:禁用
        Integer strategyStatus = rqt.getStrategyStatus();
        if (strategyStatus == 0) {
            StrategyRelate strategyRelate = strategyRelateRepository.selectByFilterStrategyId(strategyId);
            Assert.isNull(strategyRelate, "当前记录已被应用，不可禁用");
        }
        return filterStrategyRepository.updateStatus(strategyId, rqt.getStrategyStatus(),rqt.getUpdateAccountId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "普通订单匹配", level2MenuName = "召回策略管理",
            tableName = "filter_strategy", mapperClass = FilterStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "filterStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int delete(DeleteRqt rqt) {
        FilterStrategy filterStrategy = filterStrategyRepository.selectByStrategyId(rqt.getStrategyId());
        Assert.isTrue(filterStrategy.getStrategyStatus() == 0, "非禁用状态不可删除!");
        return filterStrategyRepository.softDeleteByStrategyId(rqt.getStrategyId());
    }


    @Override
    public List<FilterStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList){
        return filterStrategySnapshotRepository.selectBySnapshotIdList(snapshotIdList);

    }
}