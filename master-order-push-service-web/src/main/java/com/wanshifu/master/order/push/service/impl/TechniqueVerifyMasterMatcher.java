package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.OrderTechniqueVerifyMasterMatch;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.OrderTechniqueVerifyMasterMatchRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.training.api.skillTask.SkillTaskServiceApi;
import com.wanshifu.master.training.domain.api.request.skillTask.IsMatchSkillTaskConfigRqt;
import com.wanshifu.order.config.api.TechniqueServiceApi;
import com.wanshifu.order.config.domains.dto.technique.ServeTypeAndGoodsResp;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 技能验证单
 * <AUTHOR>
 * @date 2023-09-13 14:04:00
 */
@Slf4j
@Component("technique_verify_master")
public class TechniqueVerifyMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private TechniqueServiceApi techniqueServiceApi;


    @Resource
    private SkillTaskServiceApi skillTaskServiceApi;


    @Resource
    private OrderTechniqueVerifyMasterMatchRepository orderTechniqueVerifyMasterMatchRepository;



    /**
     * 技能验证师傅派单过滤已推送师傅开关
     */
    @Value("${techniqueVerifyMaster.dispatch.filterPushedMaster.switch:on}")
    private String filterPushedMasterSwitch;

    @Resource
    private HBaseClient hBaseClient;





    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){


        String pushMode = orderDetailData.getPushExtraData().getPushMode();

        if(StringUtils.isNotBlank(orderDetailData.getPushExtraData().getMatchSceneCode()) && (!MatchSceneCode.ORDER_CREATE.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode()))){
            return false;
        }

        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();
        if(CollectionUtils.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.code)){
            return false;
        }

        if(PushMode.TECHNIQUE_VERIFY_MASTER.code.equals(pushMode)){
            if(!apolloConfigUtils.isOpenPushTechniqueVerifyMaster()){
                return false;
            }
        }else{

            if(!apolloConfigUtils.isOpenUserTechniqueVerifyMaster()){
                return false;
            }

            IsMatchSkillTaskConfigRqt rqt = new IsMatchSkillTaskConfigRqt();
            rqt.setCategoryId(orderDetailData.getOrderCategoryId());
            rqt.setCityId(orderDetailData.getSecondDivisionId());
            Integer result = skillTaskServiceApi.isMatchSkillTaskConfig(rqt);

            if(Objects.isNull(result) || result == 0){
                return false;
            }
        }

        if(BusinessLineEnum.ENTERPRISE.code != orderDetailData.getBusinessLineId()){
            return false;
        }

        if(!(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType()) && Objects.nonNull(orderDetailData.getOrderPayStatus()) && orderDetailData.getOrderPayStatus() == 1)){
            return false;
        }


        if(Objects.isNull(orderDetailData.getFourthDivisionId()) || orderDetailData.getFourthDivisionId() == 0){
            return false;
        }

        //加急单
        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            return false;
        }

        //旧件寄回
        if(orderDetailData.getIsSendBackOld() != null && orderDetailData.getIsSendBackOld() == 1 && "pay_another".equals(orderDetailData.getSendBackOldPayMode())){
            return false;
        }

        //需师傅带配件
        if (orderDetailData.getIsParts() != null && orderDetailData.getIsParts() == 1) {
            return false;
        }

        //有当日装服务
        if (CollectionUtil.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("same_day_outfit")) {
            return false;
        }

        return  true;
    }


    public void insertTechniqueVerifyMatchLog(OrderDetailData orderDetailData,String matchFailReason){
        OrderTechniqueVerifyMasterMatch matchLog = new OrderTechniqueVerifyMasterMatch();
        matchLog.setOrderId(orderDetailData.getMasterOrderId());
        matchLog.setAppointType(orderDetailData.getAppointType());
        matchLog.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
        matchLog.setMasterId(0L);
        matchLog.setOrderNo(orderDetailData.getOrderNo());
        matchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        matchLog.setIsMatchSucc(0);
        matchLog.setMatchFailReason(matchFailReason);
        matchLog.setCreateTime(new Date());
        matchLog.setUpdateTime(new Date());
        this.orderTechniqueVerifyMasterMatchRepository.insertSelective(matchLog);
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition condition) {


        String pushMode = orderDetailData.getPushExtraData().getPushMode();

        List<MasterBaseSearch> masterBaseSearchList = matchTechniqueVerifyMaster(orderDetailData,condition);
        if(CollectionUtils.isEmpty(masterBaseSearchList)){
            if(PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.code.equals(pushMode)){
                insertTechniqueVerifyMatchLog(orderDetailData,"未匹配到技能验证师傅");
            }
            return null;
        }



        Set<String> masterSet = masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());


        if(PushMode.TECHNIQUE_VERIFY_MASTER.code.equals(pushMode)){
            return new MatchMasterResult(masterSet);
        }


        masterBaseSearchList = filterPushedMaster(orderDetailData.getGlobalOrderId(),masterBaseSearchList);
        if(CollectionUtils.isEmpty(masterBaseSearchList)){
            return null;
        }



        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,masterSet);

        DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();


        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                .matchNewMasterDistributor(orderDetailData, orderFeatureContext,"new_master");

        if(!orderDistributor.isMatched()){
            insertTechniqueVerifyMatchLog(orderDetailData,"未匹配到新师傅调度策略");
            return null;
        }


        List<OrderTechniqueVerifyMasterMatch> matchLogList = new ArrayList<>();
        masterBaseSearchList.forEach(techniqueVerifyMaster -> {
            OrderTechniqueVerifyMasterMatch matchLog = new OrderTechniqueVerifyMasterMatch();
            matchLog.setIsMatchSucc(1);
            matchLog.setMatchFailReason("");
            matchLog.setMasterId(Long.valueOf(techniqueVerifyMaster.getMasterId()));
            matchLog.setOrderVersion(orderDetailData.getOrderVersion());
            matchLog.setOrderId(orderDetailData.getMasterOrderId());
            matchLog.setAppointType(orderDetailData.getAppointType());
            matchLog.setOrderNo(orderDetailData.getOrderNo());
            matchLog.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            matchLog.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
            matchLog.setCreateTime(new Date());
            matchLog.setUpdateTime(new Date());
            matchLogList.add(matchLog);
        });

        featureRepository.getMasterFeatures(pushFeature,masterSet,orderDistributor.getMasterFeatureSet());

        DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();


        log.info("masterFeatures:" + JSON.toJSONString(masterFeatures));


        //过滤排序
        final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                .withType(FieldConstant.RANK_DETAIL)
                .build();


        List<ScorerMaster> scorerMasterList = orderDistributor
                .rank(masterSet, orderFeatureContext, masterFeatures, rankDetail);



        Map<Long, String> filterReasonMap = new HashMap<>();


        try {
            if (Objects.nonNull(rankDetail) && org.apache.commons.lang.StringUtils.isNotBlank(rankDetail.getDetailInfo())) {
                Map<String, Object> filterDetailsMap = (Map) JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                for (String key : filterDetailsMap.keySet()) {
                    JSONArray jsonArray = (JSONArray) filterDetailsMap.get(key);
                    jsonArray.forEach(master -> {
                        filterReasonMap.put(Long.valueOf(String.valueOf(master)), key);
                    });
                }
            }

        } catch (Exception e) {
            log.error("rankDetail error", e);
        }


        List<String> masterList = CollectionUtils.isNotEmpty(scorerMasterList) ? scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList()) : new ArrayList<>();

        matchLogList.forEach(matchLog -> {

            if ((Objects.nonNull(matchLog.getIsFilter()) && matchLog.getIsFilter() == 1)
                    || !Strings.isNullOrEmpty(matchLog.getFilterReason())) {
                return;
            }
            matchLog.setIsFilter(masterList.contains(String.valueOf(matchLog.getMasterId())) ? 0 : 1);
            matchLog.setFilterReason(masterList.contains(String.valueOf(matchLog.getMasterId())) ? "" : filterReasonMap.getOrDefault(matchLog.getMasterId(),""));

        });


        if(CollectionUtils.isNotEmpty(scorerMasterList)){
            List<TechniqueVerifyMaster> techniqueVerifyMasterList = distribute(scorerMasterList,orderDistributor.getDistributeRule());

            Set<String> distributeMasterSet = techniqueVerifyMasterList.stream().map(TechniqueVerifyMaster::getMasterId).collect(Collectors.toSet());
            matchLogList.forEach(matchLog ->{

                if (Objects.nonNull(matchLog.getIsFilter())
                        && matchLog.getIsFilter() == 1) {
                    return;
                }
                matchLog.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                matchLog.setIsDistribute(distributeMasterSet.contains(String.valueOf(matchLog.getMasterId())) ? 1 : 0);
            });

            if(CollectionUtils.isNotEmpty(matchLogList)){
                orderTechniqueVerifyMasterMatchRepository.insertList(matchLogList);
            }

            Set<String> finalMasterIdSet = techniqueVerifyMasterList.stream().map(TechniqueVerifyMaster::getMasterId).collect(Collectors.toSet());
            MatchMasterResult matchMasterResult = new MatchMasterResult();
            matchMasterResult.setMasterIdSet(finalMasterIdSet);
            matchMasterResult.putExtraData("technique_verify_master_list",techniqueVerifyMasterList);
            return matchMasterResult;
        }else{
            if(CollectionUtils.isNotEmpty(matchLogList)){
                orderTechniqueVerifyMasterMatchRepository.insertList(matchLogList);
            }
            return new MatchMasterResult();
        }
    }


    private List<TechniqueVerifyMaster> distribute(List<ScorerMaster> scorerMasterList, String distributeRule){
        if(OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            int index = scorerMasterList.size() >= 50 ? 50 : scorerMasterList.size();
            scorerMasterList = scorerMasterList.subList(0,index);
            Collections.shuffle(scorerMasterList);
        }else if(OrderDistributeRule.RANDOM.getCode().equals(distributeRule)){
            Collections.shuffle(scorerMasterList);
        }

        if(CollectionUtils.isNotEmpty(scorerMasterList)){
            List<TechniqueVerifyMaster> techniqueVerifyMasterList = new ArrayList<>();
            for(int i = 0;i < scorerMasterList.size();i++){
                TechniqueVerifyMaster techniqueVerifyMaster = new TechniqueVerifyMaster();
                ScorerMaster scorerMaster = scorerMasterList.get(i);
                techniqueVerifyMaster.setMasterId(scorerMaster.getMasterId());
                techniqueVerifyMaster.setOfferSort(i + 1);
                techniqueVerifyMasterList.add(techniqueVerifyMaster);
            }
            return techniqueVerifyMasterList;
        }

        return null;
    }



    /**
     * 过滤已推送的技能验证师傅
     * @param getGlobalOrderTraceId
     * @param masterBaseSearchList
     */
    public List<MasterBaseSearch> filterPushedMaster(Long getGlobalOrderTraceId,List<MasterBaseSearch> masterBaseSearchList){

        if(!"on".equals(filterPushedMasterSwitch)){
            return masterBaseSearchList;
        }

        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.code);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)){
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            return masterBaseSearchList.stream().filter(masterBaseSearch -> !pushedMasterIdSet.contains(masterBaseSearch.getMasterId())).collect(Collectors.toList());
        }

        return masterBaseSearchList;

    }



    public MatchQueryBuilder stringMatchQueryBuilder(String fieldName, String value, Operator operator) {
        //设置查询类型为MatchQuery。
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        matchQueryBuilder.operator(operator);
        return matchQueryBuilder;
    }

    /**
     * 索引API
     */
    public BoolQueryBuilder boolQueryBuilder(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch) {
        List<String> shouldConditionList = new ArrayList<>(shouldConditions);
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        shouldConditionList.stream()
                .map(shouldCondition->shouldQuery.should(stringMatchQueryBuilder(fieldName,shouldCondition,Operator.AND)))
                .collect(Collectors.toList());
        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }




    /**
     * 匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<MasterBaseSearch> matchTechniqueVerifyMaster(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));
        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.must(QueryBuilders.termsQuery("toTechniqueVerifyCategoryIds", Lists.newArrayList(orderDetailData.getOrderCategoryId())));


        if(!apolloConfigUtils.isTechniqueVerifyOrderCrossCategory()){
            String orderTechniqueIds = orderDetailData.getOrderTechniques().replace("|",",");

            Map<Long, List<ServeTypeAndGoodsResp>> respMap = techniqueServiceApi.getServeTypeAndGoodsByTechniqueIds(orderTechniqueIds);

            Set<String> finalTechniqueIds =  masterMatchCondition.getTechnologysInDemandSet().stream().filter(techniqueIds -> {
                Set<Long> techniqueIdSet = Arrays.asList(techniqueIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
                for(Long techniqueId : techniqueIdSet){
                    List<ServeTypeAndGoodsResp> respList = respMap.get(techniqueId);
                    if(CollectionUtils.isEmpty(respList) || (! respList.get(0).getGoodsId().equals(orderDetailData.getOrderCategoryId()))){
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(finalTechniqueIds)){
                return null;
            }


            BoolQueryBuilder shouldQuery = this
                    .boolQueryBuilder(
                            "masterTechniqueIds",
                            finalTechniqueIds,
                            1);
            boolQueryBuilder.filter(shouldQuery);
        }else{
            BoolQueryBuilder shouldQuery = this
                    .boolQueryBuilder(
                            "masterTechniqueIds",
                            masterMatchCondition.getTechnologysInDemandSet(),
                            1);
            boolQueryBuilder.filter(shouldQuery);
        }




        List<MasterBaseSearch> techniqueVerifyMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                techniqueVerifyMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return techniqueVerifyMasterList;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult){


    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        try{
            if(matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
                return false;
            }
            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp),"technique_verify_master_dispatch");
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 4);
            String pushMode = orderDetailData.getPushExtraData().getPushMode();
            if(!PushMode.TECHNIQUE_VERIFY_MASTER.code.equals(pushMode)){
                commonFeature.put(FieldConstant.PUSH_MODE, PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH);
                commonFeature.put("technique_verify_master_list", matchMasterResult.getExtraData().get("technique_verify_master_list"));
            }else{
                commonFeature.put(FieldConstant.PUSH_MODE, orderDetailData.getPushExtraData().getPushMode());
            }
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
            pushControllerFacade.directPush(orderDetailData, orderVersion, matchMasterResult.getMasterIdSet(), commonFeature);

            return true;
        }catch(Exception e){
            log.error(String.format("执行技能验证派单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;

    }



}
