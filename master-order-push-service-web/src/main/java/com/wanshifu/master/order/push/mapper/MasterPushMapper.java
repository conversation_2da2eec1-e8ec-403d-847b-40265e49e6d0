package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.MasterPush;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 推送师傅记录Mapper
 * <AUTHOR>
 */
public interface MasterPushMapper extends IBaseCommMapper<MasterPush> {

    List<MasterPush> getMasterPushList(@Param("orderId") Long orderId,@Param("orderVersion") String orderVersion,@Param("timeMark") String timeMark,
                                       @Param("beginOffset") Integer beginOffset,@Param("endOffset") Integer endOffset,@Param("masterType") String masterType);

    List<MasterPush> getMasterPushListByPushOffset(@Param("orderId") Long orderId,@Param("orderVersion") String orderVersion,@Param("timeMark") String timeMark,
                                       @Param("beginOffset") Integer beginOffset,@Param("endOffset") Integer endOffset);

    int insertMasterPushList(@Param("masterPushList")List<MasterPush> masterPushList,@Param("timeMark")String timeMark);

    Set<String> selectByMasterIdList(@Param("orderId") Long orderId,@Param("orderVersion") String orderVersion,@Param("masterType")String masterType,@Param("offset")Integer offset,
                                      @Param("masterIdSet") Set<String> masterIdSet, @Param("timeMark") String timeMark);

    int createMasterPushTable(@Param("tableName") String tableName);

    Set<Long> selectByOrderIdAndMasterIds(@Param("orderId") Long orderId,@Param("masterIdSet")  Set<Long> masterIds, @Param("timeMark")  String timeMark);


    int dropHistoryPushTable(@Param("tableName") String tableName);


    List<MasterPush> selectPushScore(@Param("orderId")Long orderId,@Param("orderVersion")String orderVersion,@Param("masterIdList")List<Long> masterIdList,@Param("timeMark")String timeMark);


    }