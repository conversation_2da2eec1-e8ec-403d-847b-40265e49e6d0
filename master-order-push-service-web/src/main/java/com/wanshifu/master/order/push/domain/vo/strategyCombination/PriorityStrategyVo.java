package com.wanshifu.master.order.push.domain.vo.strategyCombination;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  组合策略-优先策略Vo.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-13 09:36
 */
@Data
public class PriorityStrategyVo {


    /**
     * 选择的初筛、召回、精排策略id
     */
    @NotNull
    @Valid
    public StrategyList strategyList;


    @NotNull
    @ValueIn("new,old")
    private String pushRuleType;

    /**
     * 推送规则
     */
    private List<PushRuleItem> pushRule;


    private Integer pushRuleId;


    /**
     * 组合策略
     */
    @Data
    public static class StrategyList {

        /**
         * 初筛策略
         */
        @NotNull
        private Long baseSelectStrategyId;

        /**
         * 召回策略
         */
        @NotNull
        private Long filterStrategyId;

        /**
         * 精排策略id
         */
        @NotNull
        private Long sortingStrategyId;
    }


    /**
     * 推送规则
     */
    @Data
    private static class PushRuleItem {

        /**
         * 指派模式 2:报价招标 4:一口价 5:预付款
         */
        @ValueIn("2,4,5")
        @NotNull
        private Integer appointType;

        /**
         * 最佳报价数
         */
        @NotNull
        private Integer bestOfferNum;

        /**
         * 推送时间间隔
         */
        @NotNull
        private Integer delayMinutesBetweenRounds;

        /**
         * 首轮推送人数
         */
        @NotNull
        private Integer firstPushMasterNumPerRound;

        /**
         * 非首轮推送人数
         */
        @NotNull
        private Integer delayPushMasterNumPerRound;

        /**
         * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String firstPushMasterFlag;

        /**
         * 首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal firstPushMasterPercent;

        /**
         * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String delayPushMasterFlag;

        /**
         * 非首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal delayPushMasterPercent;
    }
}