package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.po.PushRule;
import com.wanshifu.master.order.push.domain.po.PushShuntRule;
import com.wanshifu.master.order.push.mapper.PushLimitRuleMapper;
import com.wanshifu.master.order.push.mapper.PushShuntRuleMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/08 14:36
 */
@Repository
public class PushShuntRuleRepository extends BaseRepository<PushShuntRule> {

    @Resource
    private PushShuntRuleMapper pushShuntRuleMapper;


    public int insertPushShuntRule(Integer businessLineId,String ruleName,String ruleDesc,String cityIds,String crowdType,String crowdLabel,String crowdGroup,
                                   String crowdGroupExpression,
                                   String limitRange,
                              String limitServeRule,String serveRuleExpression,String exclusiveRule,String limitRule,
                              Integer pushPercent,Long createAccountId){
        PushShuntRule pushShuntRule = new PushShuntRule();
        pushShuntRule.setBusinessLineId(businessLineId);
        pushShuntRule.setRuleName(ruleName);
        pushShuntRule.setRuleDesc(ruleDesc);
        pushShuntRule.setCityIds(cityIds);
        pushShuntRule.setCrowdType(crowdType);
        pushShuntRule.setCrowdLabel(crowdLabel);
        pushShuntRule.setCrowdGroup(crowdGroup);
        pushShuntRule.setCrowdGroupExpression(crowdGroupExpression);
        pushShuntRule.setServeRuleExpression(serveRuleExpression);
        pushShuntRule.setLimitRange(limitRange);
        pushShuntRule.setLimitServeRule(limitServeRule);
        pushShuntRule.setExclusiveRule(exclusiveRule);
        pushShuntRule.setPushPercent(pushPercent);
        pushShuntRule.setLimitRule(limitRule);
        pushShuntRule.setCreateAccountId(createAccountId);
        pushShuntRule.setUpdateAccountId(createAccountId);
        return this.insertSelective(pushShuntRule);
    }


    public int updatePushShuntRule(Integer ruleId,Integer businessLineId,String ruleName,String ruleDesc,String cityIds,String crowdType,String crowdLabel,String crowdGroup,String crowdGroupExpression,
                                   String limitRange,
                                   String limitServeRule,String serveRuleExpression,String exclusiveRule,String limitRule,
                                   Integer pushPercent,Long updateAccountId){
        PushShuntRule pushShuntRule = new PushShuntRule();
        pushShuntRule.setBusinessLineId(businessLineId);
        pushShuntRule.setRuleId(ruleId);
        pushShuntRule.setRuleName(ruleName);
        pushShuntRule.setRuleDesc(ruleDesc);
        pushShuntRule.setCityIds(cityIds);
        pushShuntRule.setCrowdType(crowdType);
        pushShuntRule.setCrowdLabel(crowdLabel);
        pushShuntRule.setCrowdGroup(crowdGroup);
        pushShuntRule.setCrowdGroupExpression(crowdGroupExpression);
        pushShuntRule.setServeRuleExpression(serveRuleExpression);
        pushShuntRule.setLimitRange(limitRange);
        pushShuntRule.setPushPercent(pushPercent);
        pushShuntRule.setLimitServeRule(limitServeRule);
        pushShuntRule.setExclusiveRule(exclusiveRule);
        pushShuntRule.setLimitRule(limitRule);
        pushShuntRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(pushShuntRule);
    }





    public List<PushShuntRule> selectList(String cityId,Integer businessLineId,String ruleName, Date createStartTime, Date createEndTime,Integer ruleStatus){
        return pushShuntRuleMapper.selectList(cityId,businessLineId,ruleName,createStartTime,createEndTime,ruleStatus);
    }

    public int softDeleteByRuleId(Integer ruleId) {
        PushShuntRule pushShuntRule = new PushShuntRule();
        pushShuntRule.setRuleId(ruleId);
        pushShuntRule.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(pushShuntRule);
    }



    public int updateStatus(Integer ruleId, Integer ruleStatus,Long updateAccountId) {
        PushShuntRule pushShuntRule = new PushShuntRule();
        pushShuntRule.setRuleId(ruleId);
        pushShuntRule.setRuleStatus(ruleStatus);
        pushShuntRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(pushShuntRule);
    }



    public PushShuntRule selectByCityIdAndBusinessLineId(Integer ruleId,Integer businessLineId, List<String> cityIdList,String crowdLabel){
        return CollectionUtils.getFirstSafety(pushShuntRuleMapper.selectByCityIdAndBusinessLineId(ruleId,businessLineId,cityIdList,crowdLabel));
    }


    public PushShuntRule selectByRuleName(String ruleName,Integer ruleId) {
        Example example = new Example(PushShuntRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleName", ruleName)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (ruleId != null) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

}
