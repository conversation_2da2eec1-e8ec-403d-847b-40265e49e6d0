package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderRefund;
import com.wanshifu.master.order.push.mapper.OrderRefundMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 推单师傅表
 * <AUTHOR>
 */
@Repository
public class OrderRefundRepository extends BaseRepository<OrderRefund> {



    @Resource
    private OrderRefundMapper orderRefundMapper;


    public List<OrderRefund> selectByUserIdAndMasterId(Long userId,Set<String> masterIdSet,Integer orderServeType){
        Condition condition = new Condition(OrderRefund.class);
        condition.selectProperties("masterId");
        condition.createCriteria().andEqualTo("userId", userId)
                .andIn("masterId",masterIdSet)
                .andEqualTo("orderServeType",orderServeType)
                .andGreaterThan("refundTime",DateFormatterUtil.getAssignMinutesTimeStampMils(-120));
        return orderRefundMapper.selectByCondition(condition);
    }

    public OrderRefund selectByUserIdAndMasterId(Long userId,Long masterId,Integer orderServeType){
        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setUserId(userId);
        orderRefund.setMasterId(userId);
        orderRefund.setOrderServeType(orderServeType);
        return CollectionUtils.getFirstSafety(this.select(orderRefund));
    }




}