package com.wanshifu.master.order.push.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.information.api.AutoPunishApi;
import com.wanshifu.master.information.domain.api.request.autoPunish.GetMasterRestrictInfoByMasterIdRqt;
import com.wanshifu.master.information.domain.api.response.autoPunish.BatchGetMasterRestrictInfoByMasterIdsRsp;
import com.wanshifu.master.information.domain.po.AutoPenaltiesRestrictionConfig;
import com.wanshifu.master.information.domain.po.AutoPenaltiesRestrictionMaster;
import com.wanshifu.master.information.domain.po.AutoPenaltiesRestrictionRuleConfig;
import com.wanshifu.master.offline.domain.vo.GetMasterIdCardRestrictLimitPushOrdersStateParam;
import com.wanshifu.master.offline.service.api.MasterIdCardRestrictApi;
import com.wanshifu.master.order.domains.sink.api.request.orderCommon.BatchGetMasterExtraOfferNumRqt;
import com.wanshifu.master.order.domains.sink.api.response.orderCommon.BatchGetMasterExtraOfferNumResp;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.domain.api.response.MasterGroupResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetPersonGroupByMasterIdsRqt;
import com.wanshifu.master.order.push.domain.common.PushFilterList;
import com.wanshifu.master.order.push.domain.common.PushScorerList;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.CreateRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.impl.ApolloConfigUtils;
import com.wanshifu.master.order.push.service.impl.FeatureRepository;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.master.order.serviceSink.api.OrderCommonServiceSinkApi;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.offer.OfferPriceListResp;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderOfferPrice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 推单处理器：过滤，推单评分
 * <AUTHOR>
 */
@Component
@Slf4j
public class PushHandler {

    @Resource
    private PushHandleRepository pushHandleRepository;

    @Resource
    private PushProgressRepository pushProgressRepository;


    @Value("${pushByRound.enable}")
    private Boolean pushByRoundEnable;


    @Value("${mustOrderFlag.last30dOfferOrderCnt:0}")
    private Long mustOrderFlagLast30dOfferOrderCnt;

    @Value("${mustOrderFlag.orderMasterDistance:5}")
    private Long mustOrderFlagOrderMasterDistance;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;


    @Resource
    private OrderCommonServiceSinkApi orderCommonServiceSinkApi;


    @Resource
    private PushCommon pushCommon;


    @Resource
    private SortingStrategyRepository sortingStrategyRepository;


    @Resource
    private PriorityPushRuleRepository priorityPushRuleRepository;


    @Resource
    private FilterStrategyRepository filterStrategyRepository;

    @Resource
    private MasterIdCardRestrictApi masterIdCardRestrictApi;

    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;


    private PriorityPushRule getPriorityPushRule(Integer businessLineId,OrderDetailData orderDetailData) {

        PriorityPushRule priorityPushRule = priorityPushRuleRepository.selectByCategoryIdAndCityId( businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), String.valueOf(orderDetailData.getSecondDivisionId()));

        if (priorityPushRule == null) {
            //具体城市+不限类目
            priorityPushRule = priorityPushRuleRepository.selectByCategoryIdAndCityId( businessLineId, "all", String.valueOf(orderDetailData.getSecondDivisionId()));
        }

        if (priorityPushRule == null) {
            //全国城市+具体类目
            priorityPushRule = priorityPushRuleRepository.selectByCategoryIdAndCityId( businessLineId, String.valueOf(orderDetailData.getOrderCategoryId()), "all");
        }

        if (priorityPushRule == null) {
            //全国城市+不限类目
            priorityPushRule = priorityPushRuleRepository.selectByCategoryIdAndCityId( businessLineId, "all", "all");
        }
        return priorityPushRule;
    }



    /**
     * 校验过滤器和评分器开启条件
     * @param pushFilterList 过滤器列表
     * @param pushScorerList 评分器列表
     * @param pushFeature 推送特征实体
     */
    public void checkCondition(PushFilterList pushFilterList, PushScorerList pushScorerList, PushFeature pushFeature) {
        DefaultContext<String, Object> conditionContext = pushFeature.getOrderFeature();

        //校验过滤器开启条件
        pushFilterList.setFilterList(pushFilterList.getFilterList().stream().filter(pushFilter -> {
            String conditionExpression = pushFilter.getConditionExpression();
            if (StringUtils.isBlank(conditionExpression)) {
                return true;
            }
            boolean checkConditionSucc = false;
            try {
                checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
            } catch (Exception e) {
                log.error("校验召回策略开启条件失败",e);
            }
            return checkConditionSucc;
        }).collect(Collectors.toList()));

        if(Objects.isNull(pushScorerList) || CollectionUtils.isEmpty(pushScorerList.getPushScorerObjectList())){
            return ;
        }
        //校验评分器开启条件
        pushScorerList.setPushScorerObjectList(pushScorerList.getPushScorerObjectList().stream().filter(pushScorerObject -> {
            if(pushScorerObject.getOpenCondition() == null || StringUtils.isBlank(pushScorerObject.getOpenCondition().getConditionExpression())){
                return true;
            }
            String conditionExpression = pushScorerObject.getOpenCondition().getConditionExpression();
            boolean checkConditionSucc = false;
            try {
                checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
            } catch (Exception e) {
                log.error("校验精排策略匹配项开启条件失败",e);
            }

            return checkConditionSucc;
        }).collect(Collectors.toList()));


        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(pushScorerList.getPushScorerObjectList())){
            pushScorerList.setPushScorerObjectList(Collections.singletonList(pushScorerList.getPushScorerObjectList().get(0)));
            pushScorerList.getPushScorerObjectList().stream().forEach(pushScorerObject -> {
                pushScorerObject.setScorerList(pushScorerObject.getScorerList().stream().filter(pushScorer -> {
                    String conditionExpression = pushScorer.getConditionExpression();
                    if (StringUtils.isBlank(conditionExpression)) {
                        return true;
                    }
                    boolean checkConditionSucc = false;
                    try {
                        checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, conditionContext, null, true, false);
                    } catch (Exception e) {
                        log.error("校验精排策略匹配项开启条件失败",e);
                    }

                    return checkConditionSucc;
                }).collect(Collectors.toList()));
            });
        }
    }


    public Integer getBusinessLineId(OrderDetailData orderDetailData){
        Integer businessLineId = 1;
        String masterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();
        if(orderDetailData.getBusinessLineId() == 1){
            businessLineId = 1;
        }else if(orderDetailData.getBusinessLineId() == BusinessLineEnum.FAMILY.code && OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom())){
            //家庭小程序订单推单分流新师傅APP
            businessLineId = MasterSourceType.TOB.code.equals(masterSourceType) ? 2 : 999;
        }else{
            businessLineId = orderDetailData.getBusinessLineId();
        }
        return businessLineId;
    }


    public List<PushMaster> handle(PushStrategySnapshot pushStrategySnapshot, PushCommonObject pushCommonObject,
                                   PushFilterList pushFilterList, PushScorerList pushScorerList, PushFeature pushFeature,
                                   String timeMark, String orderVersion, List<BaseSelectMaster> baseSelectMasterList,boolean isToRepush) {


        Integer businessLineId = getBusinessLineId(pushCommonObject.getOrderDetailData());


        DefaultContext<String, Object> orderFeature = pushFeature.getOrderFeature();

        PriorityPushRule priorityPushRule = getPriorityPushRule(businessLineId,pushCommonObject.getOrderDetailData());
        List<PriorityPushRuleExpressionDto> expressionDtoList = null;

        String pushGroupsExpression = null;
        if(Objects.nonNull(priorityPushRule)){
            if(StringUtils.isNotBlank(priorityPushRule.getPushGroupsExpression())){
                PushGroupsExpressionDto pushGroupsExpressionDto = JSON.parseObject(priorityPushRule.getPushGroupsExpression(), PushGroupsExpressionDto.class);
                if(Objects.nonNull(pushGroupsExpressionDto)){
                    pushGroupsExpression = pushGroupsExpressionDto.getPushGroupsParamsExpression();
                }
            }

            if(StringUtils.isNotBlank(priorityPushRule.getPushRuleExpression())){
                expressionDtoList = JSON.parseArray(priorityPushRule.getPushRuleExpression(), PriorityPushRuleExpressionDto.class);

                //校验过滤器开启条件
                expressionDtoList = expressionDtoList.stream().filter(expressionDto -> {
                    String conditionExpression = expressionDto.getOpenConditionRuleExpression();
                    if (StringUtils.isBlank(conditionExpression)) {
                        return true;
                    }
                    boolean checkConditionSucc = false;
                    try {
                        checkConditionSucc = (Boolean) qlExpressHandler.getExpressRunner().execute(conditionExpression, orderFeature, null, true, false);
                    } catch (Exception e) {
                        log.error("校验召回策略开启条件失败",e);
                    }
                    return checkConditionSucc;
                }).collect(Collectors.toList());
            }
        }


        Map<String,BaseSelectMaster> baseSelectMasterMap =  baseSelectMasterList.stream().collect(Collectors.toMap(BaseSelectMaster::getMasterId, each->each,(value1, value2) -> value1));

        List<PushMaster> resultMasterList = new ArrayList<PushMaster>();
        Long globalOrderId = pushCommonObject.getOrderDetailData().getGlobalOrderId();
        DefaultContext<String, DefaultContext<String, Object>> masterFeatureListMap = pushFeature.getMasterFeature();
        // 构建师傅处理进度
        PushHandle pushHandle = new PushHandle(globalOrderId,orderVersion,pushCommonObject.getMasterSet().size());
        for (String masterId : masterFeatureListMap.keySet()) {
            DefaultContext<String, Object> masterFeatureMap = masterFeatureListMap.get(masterId);
            masterFeatureMap.put(FieldConstant.TEAM_MASTER_ORDER_PUSH,orderFeature.get(FieldConstant.TEAM_MASTER_ORDER_PUSH));
            PushMaster pushMaster = new PushMaster();
            pushMaster.setMasterId(masterId);
            BaseSelectMaster baseSelectMaster = baseSelectMasterMap.get(masterId);
            pushMaster.setBaseSelectType(baseSelectMaster != null ? baseSelectMaster.getBaseSelectType() : "technique");
            pushMaster.setIsCrossCityPush(baseSelectMaster != null ? baseSelectMaster.getIsCrossCityPush() : 0);
            pushMaster.setIdCardNumber(baseSelectMaster.getIdCardNumber());
            pushMaster.setAdditionalQuoteOrderNum(baseSelectMaster.getAdditionalQuoteOrderNum());
            pushMaster.setMasterSourceType(baseSelectMaster.getMasterSourceType());
            pushMaster.setMasterTimeType(baseSelectMaster.getMasterTimeType());
            // 强推标记。若是强推师傅，直接进入待推送列表，不走后续流程。
            this.markDirectPushMaster(globalOrderId, masterFeatureMap, pushMaster);
            this.markPriorityPush(globalOrderId,masterFeatureMap,pushMaster,expressionDtoList,pushGroupsExpression);
            this.markMasterDegree(orderFeature, masterFeatureMap, pushMaster);
            if(apolloConfigUtils.checkMustOrderFlagSwitch(pushCommonObject.getOrderDetailData().getSecondDivisionId())){
                this.markMustOrderFlag(masterFeatureMap,pushMaster);
            }
            if (pushMaster.isForcePushMaster()) {
                resultMasterList.add(pushMaster);
                pushHandle.addDirectPushMasterNum();
                continue;
            }


            // 召回师傅，若是被召回，则不走后续评分流程
            boolean isFilter = this.filterMaster(masterId, pushFilterList, masterFeatureMap, pushHandle);

            if (isFilter) {
                continue;
            }

            //若开启分轮推送，则进行评分，否则不进行评分，一次全部推送
            if (pushByRoundEnable) {
                this.scoreToMaster(pushScorerList, masterFeatureMap, pushMaster, pushHandle);
            }




            resultMasterList.add(pushMaster);

            }



        if (resultMasterList.size() == 0) {
            //过滤后没有可推送师傅，则更新推送进度为STOP
            pushProgressRepository.updatePushOverMessage(globalOrderId, orderVersion, PushStatus.STOP.code, "NO_MASTER_AFTER_FILTER");
        }

        //自动处罚限制推单
        resultMasterList = filterByAutoPunishmentPushRestrict(pushCommonObject.getOrderDetailData().getMasterOrderId(),orderVersion,resultMasterList,masterFeatureListMap,pushCommonObject.getOrderDetailData().getBusinessLineId());

        //推送规则限制推单
        resultMasterList = filterByPushLimitRule(pushCommonObject.getOrderDetailData(),orderFeature,
                resultMasterList,isToRepush ?  PushMode.NORMAL.code : "none_receive",pushHandle);

        //合作经营报价次数
        resultMasterList = filterByCooperationBusinessOfferNum(pushCommonObject.getOrderDetailData(),orderFeature,resultMasterList,isToRepush ?
                PushMode.NORMAL.code : "none_receive",pushHandle);

        //身份证号限制推单
        resultMasterList = filterByIdCardRestrict(pushCommonObject.getOrderDetailData(), resultMasterList);

        resultMasterList = filterOfferPriceMaster(resultMasterList,pushCommonObject.getOrderDetailData());

        // 入库处理进度
        pushHandleRepository.insertPushHanle(pushHandle,pushStrategySnapshot);

        return resultMasterList;
    }






    /**
     * 根据精排策略获取评分器列表
     * @param sortingStrategyId  精排策略id
     * @return
     */
    public PushScorerList getPushScorerList(Long sortingStrategyId){
        SortingStrategy sortingStrategy = sortingStrategyRepository.selectByStrategyId(sortingStrategyId);
        PushScorerList pushScorerList = new PushScorerList();
        pushScorerList.setSnapshotId(sortingStrategy.getSnapshotId());
        pushScorerList.setStrategyName(sortingStrategy.getStrategyName());
        if(sortingStrategy.getStrategyVersion() == 1){
            PushScorerObject pushScorerObject = new PushScorerObject();
            List<PushScorer> scorerList = new ArrayList<>();
            List<ScoreRuleExpression> expressionList = JSON.parseArray(sortingStrategy.getRuleExpression(),ScoreRuleExpression.class);
            expressionList.forEach(expression -> scorerList.add(new PushScorer(qlExpressHandler.getExpressRunner()
                    ,expression.getOpenConditionRuleExpression()
                    ,expression.getScoreRuleExpression()
                    ,expression.getRuleName()
                    ,expression.getOpenConditionRuleParams()
                    ,expression.getScoreRuleParams())));
            pushScorerObject.setScorerList(scorerList);
            pushScorerList.addPushScorerObject(pushScorerObject);
        }else{
            List<SortRuleExpression> expressionList = JSON.parseArray(sortingStrategy.getRuleExpression(),SortRuleExpression.class);
            expressionList.forEach(expression -> pushScorerList.addPushScorerObject(buildPushScoreObject(expression)));
        }

        return pushScorerList;
    }


    private PushScorerObject buildPushScoreObject(SortRuleExpression sortRuleExpression){
        OpenCondition openCondition = new OpenCondition(sortRuleExpression.getOpenConditionRuleExpression(),sortRuleExpression.getOpenConditionRuleParams());
        List<PushScorer> pushScorerList = new ArrayList<>(sortRuleExpression.getScoreRuleList().size());
        sortRuleExpression.getScoreRuleList().forEach(scoreRuleExpression -> pushScorerList.add(new PushScorer(qlExpressHandler.getExpressRunner()
                ,scoreRuleExpression.getOpenConditionRuleExpression()
                ,scoreRuleExpression.getScoreRuleExpression()
                ,scoreRuleExpression.getRuleName()
                ,scoreRuleExpression.getOpenConditionRuleParams()
                ,scoreRuleExpression.getScoreRuleParams())));
        return  new PushScorerObject(openCondition,pushScorerList);
    }



    /**
     * 根据召回策略获取过滤器列表
     * @param filterStrategyId 召回策略id
     * @return
     */
    public PushFilterList getPushFilterList(Long filterStrategyId){
        FilterStrategy filterStrategy = filterStrategyRepository.selectByStrategyId(filterStrategyId);

        PushFilterList pushFilterList = new PushFilterList();
        pushFilterList.setSnapshotId(filterStrategy.getSnapshotId());
        List<FilterRuleExpression> expressionList = JSON.parseArray(filterStrategy.getRuleExpression(),FilterRuleExpression.class);
        expressionList.forEach(expression -> pushFilterList.addFilter(new PushFilter(qlExpressHandler.getExpressRunner(),expression.getOpenConditionRuleExpression(),expression.getFilterRuleExpression(),
                expression.getRuleName(),expression.getOpenConditionRuleParams(), expression.getFilterRuleParams())));
        if(!pushFilterList.getMasterFeatureSet().contains(FieldConstant.TEAM_MASTER_ORDER_PUSH)){
            this.addBaseFilterStrategyRule(pushFilterList);
        }
        return pushFilterList;
    }



    /**
     * 添加固定的召回规则
     *
     * @param pushFilterList 过滤规则
     */
    private void addBaseFilterStrategyRule(PushFilterList pushFilterList) {
        BaseFilterStrategyRule[] baseFilterStrategyRules = BaseFilterStrategyRule.values();
        if (baseFilterStrategyRules != null && baseFilterStrategyRules.length > 0) {
            Arrays.stream(baseFilterStrategyRules).forEach(expression -> pushFilterList.addFilter(new PushFilter(qlExpressHandler.getExpressRunner(), expression.getOpenConditionRuleExpression(), expression.getFilterRuleExpression(),
                    expression.getRuleName(), expression.getOpenConditionRuleParams(), expression.getFilterRuleParams())));
        }
    }








    private static final String IS_FORCED_PUSH = "is_forced_push";

    /**
     * 标记强推师傅
     *
     * @param masterFeature
     *            待标记师傅
     * @return 标记后的师傅
     */
    private void markDirectPushMaster(Long orderId, DefaultContext<String,Object> masterFeature,PushMaster pushMaster) {
        String isForcedPush;
        try {
            isForcedPush = masterFeature.get(IS_FORCED_PUSH).toString();
            if (isForcedPush != null && "1".equals(isForcedPush)) {
                pushMaster.setForcePushMaster();
            }
        } catch (Exception e) {
            log.error(String.format("markDirectPushMaster failed, masterId:%s,masterFeature:%s",pushMaster.getMasterId(),masterFeature),e);
        }
    }


    @Resource
    private QLExpressHandler qlExpressHandler;

    /**
     * 标记强推师傅
     *
     * @param masterFeature
     *            待标记师傅
     * @return 标记后的师傅
     */
    private void markPriorityPush(Long orderId, DefaultContext<String,Object> masterFeature,PushMaster pushMaster ,
                                  List<PriorityPushRuleExpressionDto> expressionDtoList,String pushGroupsExpression) {


        try {


            if(StringUtils.isBlank(pushGroupsExpression) && CollectionUtils.isEmpty(expressionDtoList)){
                pushMaster.setPriorityPush(false);
                return ;
            }

            if(StringUtils.isNotBlank(pushGroupsExpression)){
                boolean isPriorityPush = (Boolean)qlExpressHandler.getExpressRunner().execute(pushGroupsExpression, masterFeature,null,true,false);
                pushMaster.setPriorityPush(isPriorityPush);
                return ;
            }


            if(CollectionUtils.isEmpty(expressionDtoList)){
                pushMaster.setPriorityPush(false);
                return ;
            }
            for(PriorityPushRuleExpressionDto expressionDto : expressionDtoList){
                boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(expressionDto.getPushRuleExpression(), masterFeature,null,true,false);
                if(result){
                    pushMaster.setPriorityPush(true);
                    break;
                }
            }
        } catch (Exception e) {
            log.error(String.format("markDirectPushMaster failed, masterId:%s,masterFeature:%s",pushMaster.getMasterId(),masterFeature),e);
            pushMaster.setPriorityPush(false);
        }
    }

    /**
     * 标记师傅类型
     *
     * @param engineContextMaster
     *            待标记师傅
     * @return 标记后的师傅
     */
    private static final String SKILL_DEGREE = "skill_degree";
    private static final String SMC_SERVE_DEGREE = "smc_serve_degree";
    private void markMasterDegree(DefaultContext<String,Object> orderFeature, DefaultContext<String,Object> masterFeatureMap, PushMaster pushMaster) {

        String skillDegree;
        try {
            String busId=orderFeature.get(FieldConstant.BUSINESS_LINE_ID).toString();
            if ("2".equals(busId)) {
                skillDegree = masterFeatureMap.get(SMC_SERVE_DEGREE).toString();
                if (skillDegree != null && "0".equals(skillDegree)) {
                    pushMaster.setNewMaster();
                }
            }else {
                skillDegree = masterFeatureMap.get(SKILL_DEGREE).toString();
                if (skillDegree != null && "0".equals(skillDegree)) {
                    pushMaster.setNewMaster();
                }
            }
        } catch (Exception e) {
            log.error(String.format("markMasterDegree failed, masterId:%s,masterFeature:%s",pushMaster.getMasterId(),masterFeatureMap),e);
        }
    }


    /**
     * 过滤师傅
     *
     * @param pushFilterList
     *            待过滤师傅
     * @return true=淘汰掉,false=保留
     */
    private boolean filterMaster(String masterId, PushFilterList pushFilterList, DefaultContext<String, Object> masterFeatureMap,
                                 PushHandle pushHandle) {
        try{
            for (PushFilter filter : pushFilterList.getFilterList()) {
                if (filter.execute(masterFeatureMap)){
                    pushHandle.addFilterMessage(filter.getFilterName(), masterId);
                    return true;
                }
            }
            return false;
        }catch(Exception e){
            log.error(String.format("filter to master failed, masterId:%s,masterFeature:%s",masterId,masterFeatureMap),e);
        }
        return false;
    }


    /**
     * 给师傅评分
     *
     * @param pushScorerList
     *            规则引擎
     * @param masterFeatureMap
     *            待评分师傅
     * @return 评分后的师傅
     */
    private void scoreToMaster(PushScorerList pushScorerList, DefaultContext<String, Object> masterFeatureMap, PushMaster pushMaster,
                               PushHandle pushHandle) {

        if(pushScorerList == null || CollectionUtils.isEmpty(pushScorerList.getPushScorerObjectList()) ||
        CollectionUtils.isEmpty(pushScorerList.getPushScorerObjectList().get(0).getScorerList())){
            log.info("未找到评分器");
            return ;
        }

        try {
            pushScorerList.getPushScorerObjectList().get(0).getScorerList().forEach(scorer -> {
                scorer.getMasterFeatureList().forEach(featureCode -> {
                    Object masterFeatureValue = masterFeatureMap.get(featureCode);
                    if (masterFeatureValue instanceof List) {
                        if(masterFeatureMap.get(featureCode + ":calculateValue") != null){
                            masterFeatureMap.put(featureCode, masterFeatureMap.get(featureCode + ":calculateValue"));
                        }
                    }
                });
                BigDecimal score = scorer.execute(masterFeatureMap);
                if (score != null) {
                    pushMaster.addScore(score);
                    // 更新处理进度
                    pushHandle.addScoreMessage(scorer.getScorerName(), pushMaster.getMasterId(), score);
                }
            });
        } catch (Exception e) {
            log.error(String.format("score to master failed, masterId:%s,masterFeature:%s", pushMaster.getMasterId(), masterFeatureMap), e);
        }

    }


    /**
     * 标记必接师傅
     * @param masterFeatureContext 师傅指标数据
     * @param pushMaster 待推送师傅
     */
    private void markMustOrderFlag(DefaultContext<String,Object> masterFeatureContext,PushMaster pushMaster){

        try{
            Long last30dOfferOrderCnt = (Long)masterFeatureContext.get("last_30d_offer_order_cnt:calculateValue");
            Long orderMasterDistance = (Long)masterFeatureContext.get("order_master_distance");
            Long orderMasterServeDivisionLevel = (Long)masterFeatureContext.get("order_master_serve_division_level");

            if (last30dOfferOrderCnt >= mustOrderFlagLast30dOfferOrderCnt
                    && (orderMasterDistance <= mustOrderFlagOrderMasterDistance || orderMasterServeDivisionLevel == 1L)
                    && !"toc".equals(pushMaster.getMasterSourceType())) {
                //c端师傅app下架必看分区
                pushMaster.setMustOrderFlag(1);
            } else {
                pushMaster.setMustOrderFlag(0);
            }
        }catch(Exception e){
            pushMaster.setMustOrderFlag(0);
            log.error("markMustOrderFlag error");
        }



    }


    @Resource
    private HBaseClient hBaseClient;


    /**
     * 校验当天推送单量上限（）
     * @param dayTimeString
     * @param
     * @return
     */
    private Map<String,Long> getMasterDailyPushOrderCnt(String dayTimeString,DefaultContext<String, DefaultContext<String, Object>> masterFeatureListMap,Integer businessLineId){

        final Map<String,Long> result = new HashMap<>(50);
        Map<String,String> idCardNumberMap = new HashMap<>();

        try {

            Set<String> idCardNumberSet = new HashSet<>();
            for(String masterId : masterFeatureListMap.keySet()){
                String idCardNumber = (String)masterFeatureListMap.get(masterId).get("id_card_number");
                if(StringUtils.isNotBlank(idCardNumber)){
                    idCardNumberSet.add(idCardNumber);
                    idCardNumberMap.put(idCardNumber,masterId);
                }
            }

            if(CollectionUtils.isEmpty(idCardNumberSet)){
                return result;
            }

            String tableName = businessLineId == 0 ? "master_daily" : "master_business_daily";

            List<String> fieldColumnList = new ArrayList<>();
            fieldColumnList.add("push_order_cnt");
            fieldColumnList.add("id_card_number");

            List<String> rowKeyList = new ArrayList<>();

            if(businessLineId == 0){
                for(String idCardNumber : idCardNumberSet){
                    rowKeyList.add(idCardNumber + "_tob" + "_" + dayTimeString);
                }
            }else if(businessLineId == 1){
                for(String idCardNumber : idCardNumberSet){
                    rowKeyList.add(idCardNumber + "_tob" + "_" + dayTimeString);
                }
            }else if(businessLineId == BusinessLineEnum.FAMILY.code){
                for(String idCardNumber : idCardNumberSet){
                    rowKeyList.add(idCardNumber + "_toc" + "_" + dayTimeString);
                }
            }

            JSONArray resultArray = hBaseClient.batchQuery(rowKeyList,fieldColumnList,tableName);

            if(Objects.nonNull(resultArray) && resultArray.size() > 0){
                for (int i = 0; i < resultArray.size(); i++) {
                    JSONObject jsonObject = (JSONObject) resultArray.get(i);
                    String idCardNumber = (String)jsonObject.get("id_card_number");
                    if(StringUtils.isNotBlank(idCardNumber) && jsonObject.containsKey("push_order_cnt")){
                         String pushOrderCnt = (String)jsonObject.get("push_order_cnt");
                         if(StringUtils.isNotBlank(pushOrderCnt)){
                             result.put(idCardNumberMap.get(idCardNumber),Long.valueOf((pushOrderCnt)));
                         }
                    }
                }
            }

        }catch (Exception e){
            log.warn("getMasterPushCount [{}],{}",dayTimeString,e);
        }




        return result;
    }


    /**
     * 过滤师傅推送数量限制
     * @param orderVersion
     * @param
     */
    private List<PushMaster> filterByAutoPunishmentPushRestrict(Long masterOrderId, String orderVersion,List<PushMaster> pushMasterList, DefaultContext<String, DefaultContext<String, Object>>
            masterFeatureListMap, Integer businessLineId){

        if(!apolloConfigUtils.isOpenAutoPunishmentPushRestrictSwitch()){
            return pushMasterList;
        }

        if(businessLineId != BusinessLineEnum.ENTERPRISE.code && businessLineId != BusinessLineEnum.FAMILY.code){
            return pushMasterList;
        }

        if(CollectionUtils.isEmpty(pushMasterList)){
            return pushMasterList;
        }

        Set<String> masterIdSet = pushMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
        try{
            final String dayTimeString = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
            final List<MasterRestrictionUnit> masterRestrictionUnitList = getMasterRestrict(pushMasterList,businessLineId);

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(masterRestrictionUnitList)) {
                log.info("{}::ocsPushRestrictFilter masterRestrict:{}",masterOrderId,
                        JSONObject.toJSONString(masterRestrictionUnitList));

                for(MasterRestrictionUnit masterRestrict : masterRestrictionUnitList){
                    //外部过滤
                    masterIdSet.removeAll(masterRestrict.getOuterRestrictMasterSet());
                    log.info("{}::ocsPushRestrictFilter outer filter:{}",masterOrderId,
                            masterRestrict.getOuterRestrictMasterSet());

                    //内部过滤
                    final Set<String> innerRestrictMasterSet = masterRestrict.getInnerRestrictMasterSet();
                    if (innerRestrictMasterSet==null||innerRestrictMasterSet.size()==0) {
                        continue;
                    }
                    final Map<String, Long> masterPushCount = getMasterDailyPushOrderCnt(dayTimeString,masterFeatureListMap,masterRestrict.getBusinessLineId());
                    final Map<String, MasterRestriction> innerMasterRestrictionMap = masterRestrict.getInnerMasterRestrictionMap();

                    final HashSet<String> innerFilterMasterSet = new HashSet<>();
                    for (Map.Entry<String, MasterRestriction> row : innerMasterRestrictionMap.entrySet()) {
                        final String masterId = row.getKey();
                        final MasterRestriction restriction = row.getValue();
                        final Long pushCount = masterPushCount.getOrDefault(masterId, 0L);
                        final Integer restrictionOrderNum = restriction.getRestrictionOrderNum();
                        if (restrictionOrderNum!=null) {
                            if (pushCount.compareTo(restrictionOrderNum.longValue())>=0) {
                                innerFilterMasterSet.add(masterId);
                            }
                        }
                    }
                    log.info("{}::ocsPushRestrictFilter inner filter::Source:{},Count:{},Result:{}",masterOrderId,
                            innerRestrictMasterSet,
                            masterPushCount,
                            innerFilterMasterSet
                    );
                    masterIdSet.removeAll(innerFilterMasterSet);
                }


            }

            return pushMasterList.stream().filter(pushMaster -> masterIdSet.contains(pushMaster.getMasterId())).collect(Collectors.toList());
        }catch(Exception e){
            log.error("ocsPushRestrictFilter error",e);
        }
        return pushMasterList;
    }


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;


    @Resource
    private AutoPunishApi autoPunishApi;

    /**
     * 获取师傅限制推单配置
     * @param pushMasterList
     * @return
     */
    public List<MasterRestrictionUnit> getMasterRestrict(List<PushMaster> pushMasterList,Integer businessLineId){

        List<PushMaster> hasIdCardNumberPushMasterList = pushMasterList.stream().filter(pushMaster -> StringUtils.isNotBlank(pushMaster.getIdCardNumber())).collect(Collectors.toList());


        if(CollectionUtils.isEmpty(hasIdCardNumberPushMasterList)){
            return null;
        }

        Set<String> idCardNumberSet = hasIdCardNumberPushMasterList.stream().map(PushMaster::getIdCardNumber).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(idCardNumberSet)){
            return null;
        }


        Map<String, PushMaster> pushMasterMap = hasIdCardNumberPushMasterList.stream()
                .filter(pushMaster -> pushMaster.getIdCardNumber() != null)
                .collect(Collectors.toMap(PushMaster::getIdCardNumber, Function.identity()));

        final List<List<String>> idCardNumberBatchList = LocalCollectionsUtil.groupByBatchString(idCardNumberSet, 400);

        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();


        for (List<String> idCardNumberBatch : idCardNumberBatchList) {
            QueryBuilder queryBuilder= QueryBuilders.boolQuery().filter(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("idCardNumber", idCardNumberBatch)));

            EsResponse esResponse = masterBaseEsRepository.searchAfter(queryBuilder, SortBuilders.fieldSort("masterId"),100,Integer.MAX_VALUE);

            if(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
            }

        }


        if(com.wanshifu.framework.utils.CollectionUtils.isEmpty(masterBaseSearchList)){
            return null;
        }


        Set<String> masterIdSet = masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());


        Map<String, MasterBaseSearch> masterBaseMap = masterBaseSearchList.stream()
                .filter(masterBase -> masterBase.getMasterId() != null)
                .collect(Collectors.toMap(MasterBaseSearch::getMasterId, Function.identity()));

        final List<List<Long>> batch = LocalCollectionsUtil.groupByBatchLong(masterIdSet, 499);


        List<MasterRestrictionUnit> restrictResultList = new ArrayList<>();

        for (List<Long> batchRequest : batch) {
            final GetMasterRestrictInfoByMasterIdRqt getMasterRestrictInfoByMasterIdRqt = new GetMasterRestrictInfoByMasterIdRqt();
            getMasterRestrictInfoByMasterIdRqt.setMasterIds(batchRequest);
            getMasterRestrictInfoByMasterIdRqt.setRestrictStatus(1);
            getMasterRestrictInfoByMasterIdRqt.setRestrictionType("restriction_push_orders");
            getMasterRestrictInfoByMasterIdRqt.setBusinessLineId(businessLineId);
            final BatchGetMasterRestrictInfoByMasterIdsRsp batchGetMasterRestrictInfoByMasterIdsRsp = autoPunishApi.batchGetMasterRestrictInfoByMasterIds(getMasterRestrictInfoByMasterIdRqt);
            parseMasterRestrictionUnit(batchGetMasterRestrictInfoByMasterIdsRsp,restrictResultList,masterBaseMap,pushMasterMap);
        }

        return restrictResultList;
    }


    public void parseMasterRestrictionUnit(
            BatchGetMasterRestrictInfoByMasterIdsRsp batchGetMasterRestrictInfoByMasterIdsRsp,
            List<MasterRestrictionUnit> restrictResultUnitList,Map<String, MasterBaseSearch> masterBaseMap,Map<String, PushMaster> pushMasterMap
    ){
        if (batchGetMasterRestrictInfoByMasterIdsRsp==null) {
            return;
        }
        try {
            final List<AutoPenaltiesRestrictionMaster> autoPenaltiesRestrictionMasterList = batchGetMasterRestrictInfoByMasterIdsRsp.getAutoPenaltiesRestrictionMasterList();

            Map<Integer, List<AutoPenaltiesRestrictionMaster>> autoRestrictionMasterMap = autoPenaltiesRestrictionMasterList.stream()
                    .collect(Collectors.groupingBy(AutoPenaltiesRestrictionMaster::getBusinessLineId));

            final Map<Long, AutoPenaltiesRestrictionRuleConfig> restrictionRuleConfigMap = batchGetMasterRestrictInfoByMasterIdsRsp.getRestrictionRuleConfigMap();
            final Map<Long, AutoPenaltiesRestrictionConfig> restrictionConfigMap = batchGetMasterRestrictInfoByMasterIdsRsp.getRestrictionConfigMap();

            for(List<AutoPenaltiesRestrictionMaster> autoPenaltiesRestrictionMasters : autoRestrictionMasterMap.values()){

                MasterRestrictionUnit masterRestrictionUnit = new MasterRestrictionUnit();
                final HashMap<String,MasterRestriction> restrictResult = new HashMap<>(50);
                for (int i = 0; i < autoPenaltiesRestrictionMasters.size(); i++) {
                    final AutoPenaltiesRestrictionMaster masterRow = autoPenaltiesRestrictionMasters.get(i);
                    String masterId = String.valueOf(masterRow.getMasterId());
                    masterId = pushMasterMap.get(masterBaseMap.get(masterId).getIdCardNumber()).getMasterId();
                    final Long restrictionRuleConfigId = masterRow.getRestrictionRuleConfigId();
                    final AutoPenaltiesRestrictionRuleConfig ruleConfig = restrictionRuleConfigMap.get(restrictionRuleConfigId);
                    final Long restrictionConfigId = ruleConfig.getRestrictionConfigId();
                    final AutoPenaltiesRestrictionConfig type = restrictionConfigMap.get(restrictionConfigId);
                    final String restrictionAdditionalType = type.getRestrictionAdditionalType();
                    final MasterRestriction masterRestriction = getMapValue(restrictResult, masterId);
                    masterRestrictionUnit.setBusinessLineId(masterRow.getBusinessLineId());
                    //restriction_freq,restriction_time
                    if ("restriction_freq".equals(restrictionAdditionalType)) {
                        final JSONObject restrictionJsonConfig = JSONObject.parseObject(ruleConfig.getRestrictionJsonConfig());
                        final String freqType = restrictionJsonConfig.getString("freqType");
                        final Integer restrictionOrderNum = restrictionJsonConfig.getInteger("restrictionOrderNum");
                        masterRestriction.setMasterRestriction(freqType,restrictionOrderNum);
                    }else if ("restriction_time".equals(restrictionAdditionalType)) {
                        masterRestriction.setMasterRestriction(true);
                    }else {
                        continue;
                    }
                }
                masterRestrictionUnit.setMasterRestrictionMap(restrictResult);

                restrictResultUnitList.add(masterRestrictionUnit);
            }

        }catch (Exception e){
            log.warn("parseRestrictResponse:{},{}",batchGetMasterRestrictInfoByMasterIdsRsp,e);
        }
    }


    public MasterRestriction getMapValue(Map<String,MasterRestriction> map,String masterId){
        if (map==null) {
            return null;
        }
        final MasterRestriction v = map.get(masterId);
        if (v==null) {
            final MasterRestriction masterRestriction = new MasterRestriction(masterId);
            map.put(masterId,masterRestriction);
            return masterRestriction;
        }else{
            return v;
        }
    }

    @Resource
    private PushLimitRuleRepository pushLimitRuleRepository;




    public List<PushMaster> filterByPushLimitRule(OrderDetailData orderDetailData,DefaultContext<String,Object> orderFeatureContext,
                                                  List<PushMaster> pushMasterList,String pushMode,
                                                  PushHandle pushHandle){

        try{
            if(CollectionUtils.isEmpty(pushMasterList)){
                return pushMasterList;
            }
            Set<String> pushMasterIdSet = pushMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
            Set<String> masterIdSet = filterByPushLimitRule(orderDetailData,orderFeatureContext,pushMasterIdSet,pushMode,pushHandle);
            if(CollectionUtils.isEmpty(masterIdSet)){
                return null;
            }
            return pushMasterList.stream().filter(pushMaster -> masterIdSet.contains(pushMaster.getMasterId())).collect(Collectors.toList());
        }catch(Exception e){
            log.error("filterByPushLimitRule error",e);
        }
        return pushMasterList;

    }

    /**
     * 身份证限制推单过滤
     * @param orderDetailData
     * @param pushMasterList
     * @return
     */
    public List<PushMaster> filterByIdCardRestrict(OrderDetailData orderDetailData,
                                                   List<PushMaster> pushMasterList){

        if(CollectionUtils.isEmpty(pushMasterList)){
            return pushMasterList;
        }
        String masterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();

        List<PushMaster> hasIdCardNumberList = pushMasterList.stream().filter(pushMaster -> !Strings.isNullOrEmpty(pushMaster.getIdCardNumber())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(hasIdCardNumberList)){
            return pushMasterList;
        }
        List<String> idCardNumberList = hasIdCardNumberList.stream().map(PushMaster::getIdCardNumber).collect(Collectors.toList());
        List<List<String>> idCardNumberBatchList = LocalCollectionsUtil.groupByBatch(idCardNumberList, 500);


        GetMasterIdCardRestrictLimitPushOrdersStateParam param = new GetMasterIdCardRestrictLimitPushOrdersStateParam();
        param.setBusinessLineId(orderDetailData.getBusinessLineId());
        param.setMasterSourceType(masterSourceType);

        //被限制推单的身份证号码
        List<String> restrictIdCardNumberList = new ArrayList<>();

        for (List<String> idCardNumberBatch : idCardNumberBatchList){
            try {
                param.setIdCardList(idCardNumberBatch);
                Map<String, Integer> masterIdCardRestrictRes = masterIdCardRestrictApi.getMasterIdCardRestrictLimitPushOrdersState(param);
                if (Objects.isNull(masterIdCardRestrictRes)) {
                    log.error("查询师傅是否被身份证限制推单失败！未正确返回！req:{}", JSONUtil.toJsonStr(param));
                    continue;
                }
                for (Map.Entry<String, Integer> entry : masterIdCardRestrictRes.entrySet()) {
                    //0:未限制，1：限制推单
                    Integer status = entry.getValue();

                    if (status == 1) {
                        restrictIdCardNumberList.add(entry.getKey());
                    }
                }
            } catch (Exception e) {
                log.error("查询师傅是否被身份证限制推单失败！req:{}", JSONUtil.toJsonStr(param), e);
                return pushMasterList;
            }
        }

        if (CollectionUtil.isEmpty(restrictIdCardNumberList)) {
            return pushMasterList;
        }

        return pushMasterList.stream().filter(pushMaster -> !restrictIdCardNumberList.contains(pushMaster.getIdCardNumber())).collect(Collectors.toList());

    }


    public List<PushMaster> filterByCooperationBusinessOfferNum(OrderDetailData orderDetailData,DefaultContext<String,Object> orderFeatureContext,
                                                                List<PushMaster> pushMasterList,String pushMode,PushHandle pushHandle){

        try{
            if(!MatchSceneCode.COOPERATION_BUSINESS.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
                return pushMasterList;
            }


            //推送合作经营场景，过滤满额订单每日报价次数已满的合作经营师傅
            List<Long> masterIdList = pushMasterList.stream().map(PushMaster::getMasterId).map(Long::valueOf).collect(Collectors.toList());
            List<List<Long>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterIdList, 20);

            List<BatchGetMasterExtraOfferNumResp> finalRespList = new ArrayList<>();
            for(List<Long> masterBatch : masterBatchList){
                BatchGetMasterExtraOfferNumRqt rqt = new BatchGetMasterExtraOfferNumRqt();
                rqt.setMasterIdList(masterBatch);
                List<BatchGetMasterExtraOfferNumResp> respList = orderCommonServiceSinkApi.batchGetMasterExtraOfferNum(rqt);
                if(!CollectionUtils.isEmpty(respList)){
                    finalRespList.addAll(respList);
                }
            }

            if(CollectionUtils.isEmpty(finalRespList)){
                return pushMasterList;
            }

            Map<Long, BatchGetMasterExtraOfferNumResp> respMap = finalRespList.stream()
                    .collect(Collectors.toMap(BatchGetMasterExtraOfferNumResp::getMasterId, p -> p));

            List<PushMaster> finalPushMasterList = pushMasterList.stream().filter(pushMaster -> {
                Long masterId = Long.valueOf(pushMaster.getMasterId());
                BatchGetMasterExtraOfferNumResp offerNumResp = respMap.get(masterId);
                if(Objects.isNull(pushMaster.getAdditionalQuoteOrderNum()) || pushMaster.getAdditionalQuoteOrderNum() == 0){
                    return false;
                }
                return Objects.isNull(offerNumResp) || offerNumResp.getOfferNum() < pushMaster.getAdditionalQuoteOrderNum();
            }).collect(Collectors.toList());


            Set<String> finalMasterIdSet = finalPushMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());

            pushMasterList.forEach(pushMaster -> {
                if(!finalMasterIdSet.contains(pushMaster.getMasterId())){
                    pushHandle.addFilterMessage("合作经营过滤每日报价次数已满的师傅",pushMaster.getMasterId());
                }
            });

            return finalPushMasterList;

        }catch(Exception e){
            log.error("filterByCooperationBusinessOfferNum error",e);
        }
        return pushMasterList;

    }


    @Resource
    private FeatureRepository featureRepository;


    /**
     * 根据推单限制规则过滤师傅
     * @param orderDetailData
     * @param pushMode
     */
    public Set<String> filterByPushLimitRule(OrderDetailData orderDetailData, DefaultContext<String,Object> orderFeatureContext,
                                             Set<String> pushMasterIdSet,String pushMode,PushHandle pushHandle){

        if(Objects.isNull(orderFeatureContext)){
            orderFeatureContext = featureRepository.buildOrderFeatures(orderDetailData);
            featureRepository.queryUserGroups(orderDetailData,orderFeatureContext);


        }

        List<PushLimitRule> pushLimitRuleList = selectPushLimitRule(orderDetailData);

        if(CollectionUtils.isEmpty(pushLimitRuleList)){
            return pushMasterIdSet;
        }


        pushLimitRuleList = pushLimitRuleList.stream().filter(pushLimitRule -> !checkExclusiveRule(pushLimitRule,pushMode,orderDetailData)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(pushLimitRuleList)){
            return pushMasterIdSet;
        }

        final DefaultContext<String,Object> orderFeatures = orderFeatureContext;
        pushLimitRuleList = pushLimitRuleList.stream().filter(pushLimitRule -> (!"serve".equals(pushLimitRule.getLimitRange())) || (checkOpenCondition(pushLimitRule,orderFeatures))).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(pushLimitRuleList)){
            return pushMasterIdSet;
        }


        PushLimitRule crowdLabelPushLimitRule = pushLimitRuleList.stream().filter(pushLimitRule -> CrowdType.CROWD_LABEL.getCode().equals(pushLimitRule.getCrowdType())).findFirst().orElse(null);
        PushLimitRule crowdGroupPushLimitRule = pushLimitRuleList.stream().filter(pushLimitRule -> CrowdType.CROWD_GROUP.getCode().equals(pushLimitRule.getCrowdType())).findFirst().orElse(null);

        Set<Long> masterIdSet = pushMasterIdSet.stream().map(Long::valueOf).collect(Collectors.toSet());
        List<MasterBaseSearch> masterList = Objects.nonNull(crowdLabelPushLimitRule) ? masterBaseEsRepository.searchCooperationBusinessEndMaster(masterIdSet) : null;
        List<MasterGroupResp> masterGroupRespList = Objects.nonNull(crowdGroupPushLimitRule) ? getMasterGroup(pushMasterIdSet) : null;


        for(PushLimitRule pushLimitRule : pushLimitRuleList){
            if(CrowdType.CROWD_LABEL.getCode().equals(pushLimitRule.getCrowdType())){
                pushMasterIdSet = filterByCrowdLabel(pushMasterIdSet,pushLimitRule,masterList,pushHandle);
            }else if(CrowdType.CROWD_GROUP.getCode().equals(pushLimitRule.getCrowdType())){
                pushMasterIdSet = filterByCrowdGroup(pushMasterIdSet,pushLimitRule,masterGroupRespList,pushHandle);
            }

        }


        return pushMasterIdSet;

    }



    @Value("${selectMasterGroup.pageSize:400}")
    private Integer selectMasterGroupPageSize;

    private List<MasterGroupResp> getMasterGroup(Set<String> pushMasterIdSet){
        List<MasterGroupResp> masterGroupRespList = new ArrayList<>();
        final List<List<Long>> batch = LocalCollectionsUtil.groupByBatchLong(pushMasterIdSet, selectMasterGroupPageSize);
        for (List<Long> batchRequest : batch) {
            GetPersonGroupByMasterIdsRqt rqt = new GetPersonGroupByMasterIdsRqt();
            rqt.setAppId(6);
            rqt.setMasterIds(batchRequest);
            masterGroupRespList.addAll( bigdataOpenServiceApi.getPersonaGroupIdsByMasterIds(rqt));
        }
        return masterGroupRespList;
    }


    private List<PushLimitRule> selectPushLimitRule(OrderDetailData orderDetailData){
        List<PushLimitRule> pushLimitRuleList = pushLimitRuleRepository.selectList(String.valueOf(orderDetailData.getSecondDivisionId()),orderDetailData.getBusinessLineId(),null,null,null,1);
        if(CollectionUtils.isEmpty(pushLimitRuleList)){
            pushLimitRuleList = pushLimitRuleRepository.selectList("all",orderDetailData.getBusinessLineId(),null,null,null,1);
        }
        return pushLimitRuleList;
    }


    private boolean checkExclusiveRule(PushLimitRule pushLimitRule,String pushMode,OrderDetailData orderDetailData){
        CreateRqt.ExclusiveRule exclusiveRule = JSON.parseObject(pushLimitRule.getExclusiveRule(),CreateRqt.ExclusiveRule.class);
        if(Objects.nonNull(exclusiveRule) && exclusiveRule.getItemList().stream().map(CreateRqt.Item::getItemValue).collect(Collectors.toList()).contains(pushMode)){
            if(CompensateType.NONE_RECEIVE.getCode().equals(pushMode)){
                CreateRqt.Item pushTypeItem = exclusiveRule.getItemList().stream().filter(item -> CompensateType.NONE_RECEIVE.getCode().equals(item.getItemValue())).findFirst().orElse(null);
                if(StringUtils.isNotBlank(pushTypeItem.getItemExtraValue())){
                    Integer delayTime = Integer.valueOf(pushTypeItem.getItemExtraValue());
                    Integer afreshDelayTime = orderDetailData.getPushExtraData().getAfreshDelayTime();
                    if(afreshDelayTime <= delayTime){
                        return false;
                    }
                }
            }
            return true;
        }

        return false;
    }


    private boolean checkOpenCondition(PushLimitRule pushLimitRule,DefaultContext<String,Object> orderFeatureContext){
        try{
            OpenConditionExpressionDto expressionDto = JSON.parseObject(pushLimitRule.getServeRuleExpression(), OpenConditionExpressionDto.class);
            boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(expressionDto.getOpenConditionRuleExpression(), orderFeatureContext,null,true,false);
           return result;
        }catch(Exception e){
            log.error("filterByMasterGroups",e);
        }
        return false;
    }


    /**
     * 根据合作经营标签过滤师傅
     * @param pushMasterIdSet
     * @param pushLimitRule
     * @return
     */
    public Set<String> filterByCrowdLabel(Set<String> pushMasterIdSet,PushLimitRule pushLimitRule,List<MasterBaseSearch> masterList,
                                          PushHandle pushHandle){

        if(CollectionUtils.isEmpty(masterList)){
            return pushMasterIdSet;
        }

        if(PushLimitRuleType.STOP_PUSHING_IMMEDIATELY.getCode().equals(pushLimitRule.getLimitRule())){
            Set<String> filterMasterIdSet = masterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
            pushMasterIdSet.removeAll(filterMasterIdSet);
            log.info("pushLimitRule filterByCrowdLabel:" + JSON.toJSONString(filterMasterIdSet));
            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(filterMasterIdSet))){
                filterMasterIdSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }
            return pushMasterIdSet;
        }else if(PushLimitRuleType.DECREASE_PUSH_BY_PERCENT.getCode().equals(pushLimitRule.getLimitRule())){
            Set<String> filterMasterIdSet = new HashSet<>();
            masterList.forEach(masterBaseSearch -> {
                Long finishCooperativeTime = masterBaseSearch.getFinishCooperativeTime();
                if(Objects.isNull(finishCooperativeTime) || finishCooperativeTime <= 0L){
                    return ;
                }
                Long pastDays = DateUtils.pastDays(new Date(finishCooperativeTime * 1000)) + 1;
                if(pastDays.intValue() < pushLimitRule.getDecreaseDays()){
                    return ;
                }
                int leftPercent = 100 - ((pastDays.intValue() / pushLimitRule.getDecreaseDays()) * pushLimitRule.getDecreasePercent());
                leftPercent = leftPercent <= 0 ? 0 : leftPercent;
                if(leftPercent == 0){
                    filterMasterIdSet.add(masterBaseSearch.getMasterId());
                }else{
                    int random = new Random().nextInt(100);
                    if(random > leftPercent){
                        filterMasterIdSet.add(masterBaseSearch.getMasterId());
                    }
                }

            });

            pushMasterIdSet.removeAll(filterMasterIdSet);
            log.info("pushLimitRule filterByCrowdLabel:" + JSON.toJSONString(filterMasterIdSet));

            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(filterMasterIdSet))){
                filterMasterIdSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }

            return pushMasterIdSet;
        }else if(PushLimitRuleType.FIXED_PERCENT.getCode().equals(pushLimitRule.getLimitRule())){

            Set<String> filterMasterIdSet = new HashSet<>();

            masterList.forEach(masterBaseSearch -> {
                int leftPercent = 100 - pushLimitRule.getFixedDecreasePercent();
                leftPercent = leftPercent <= 0 ? 0 : leftPercent;
                if(leftPercent == 0){
                    filterMasterIdSet.add(masterBaseSearch.getMasterId());
                }else{
                    int random = new Random().nextInt(100);
                    if(random > leftPercent){
                        filterMasterIdSet.add(masterBaseSearch.getMasterId());
                    }
                }

            });


            pushMasterIdSet.removeAll(filterMasterIdSet);
            log.info("pushLimitRule filterByCrowdLabel:" + JSON.toJSONString(filterMasterIdSet));

            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(filterMasterIdSet))){
                filterMasterIdSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }

            return pushMasterIdSet;

        }
        return pushMasterIdSet;
    }

    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;


    /**
     * 根据人群过滤师傅
     * @param pushMasterIdSet
     * @param pushLimitRule
     * @param masterGroupRespList
     * @return
     */
    public Set<String> filterByCrowdGroup(Set<String> pushMasterIdSet,PushLimitRule pushLimitRule,List<MasterGroupResp> masterGroupRespList,PushHandle pushHandle){

        CrowdGroupExpressionDto crowdGroupExpressionDto = JSON.parseObject(pushLimitRule.getCrowdGroupExpression(),CrowdGroupExpressionDto.class);
        Set<String> resultSet = new HashSet<>();

        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(masterGroupRespList)){
            masterGroupRespList.forEach(masterGroupResp -> {
                String groupIds = StringUtils.isNotBlank(masterGroupResp.getGroupIds()) ? masterGroupResp.getGroupIds() : "0";
                List<Integer> groupIdList = Arrays.stream(groupIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                DefaultContext  context = new DefaultContext();
                context.put(crowdGroupExpressionDto.getGroupParams(),groupIdList);
                try{
                    boolean result = (Boolean)qlExpressHandler.getExpressRunner().execute(crowdGroupExpressionDto.getGroupExpression(), context,null,true,false);
                    if(result){
                        resultSet.add(String.valueOf(masterGroupResp.getMasterId()));
                    }
                }catch(Exception e){
                    log.error("filterByMasterGroups",e);
                }
            });
        }


        if(PushLimitRuleType.STOP_PUSHING_IMMEDIATELY.getCode().equals(pushLimitRule.getLimitRule())){
            pushMasterIdSet.removeAll(resultSet);
            log.info("pushLimitRule filterByCrowdGroup:" + JSON.toJSONString(resultSet));
            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(resultSet))){
                resultSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }
            return pushMasterIdSet;
        }else if(PushLimitRuleType.DECREASE_PUSH_BY_PERCENT.getCode().equals(pushLimitRule.getLimitRule())){
            Set<String> filterMasterIdSet = new HashSet<>();
            resultSet.forEach(masterId -> {
                Long pastDays = DateUtils.pastDays(pushLimitRule.getCreateTime()) + 1;
                if(pastDays.intValue() < pushLimitRule.getDecreaseDays()){
                    return ;
                }
                int leftPercent = 100 - ((pastDays.intValue() / pushLimitRule.getDecreaseDays()) * pushLimitRule.getDecreasePercent());
                leftPercent = leftPercent <= 0 ? 0 : leftPercent;
                if(leftPercent == 0){
                    filterMasterIdSet.add(masterId);
                }else{
                    int random = new Random().nextInt(100);
                    if(random > leftPercent){
                        filterMasterIdSet.add(masterId);
                    }
                }

            });

            pushMasterIdSet.removeAll(filterMasterIdSet);
            log.info("pushLimitRule filterByCrowdGroup:" + JSON.toJSONString(filterMasterIdSet));
            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(filterMasterIdSet))){
                filterMasterIdSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }
            return pushMasterIdSet;
        }else if(PushLimitRuleType.FIXED_PERCENT.getCode().equals(pushLimitRule.getLimitRule())){

            Set<String> filterMasterIdSet = new HashSet<>();

            resultSet.forEach(masterId -> {
                int leftPercent = 100 - pushLimitRule.getFixedDecreasePercent();
                leftPercent = leftPercent <= 0 ? 0 : leftPercent;
                if(leftPercent == 0){
                    filterMasterIdSet.add(masterId);
                }else{
                    int random = new Random().nextInt(100);
                    if(random > leftPercent){
                        filterMasterIdSet.add(masterId);
                    }
                }

            });


            pushMasterIdSet.removeAll(filterMasterIdSet);
            log.info("pushLimitRule filterByCrowdLabel:" + JSON.toJSONString(filterMasterIdSet));

            if(Objects.nonNull(pushHandle) && (!CollectionUtils.isEmpty(filterMasterIdSet))){
                filterMasterIdSet.forEach(masterId -> pushHandle.addFilterMessage(pushLimitRule.getRuleName(),masterId));
            }

            return pushMasterIdSet;

        }

        return pushMasterIdSet;
    }


    /**
     * 过滤已报价师傅
     * @param pushMasterList
     * @param orderDetailData
     * @return
     */
    public List<PushMaster> filterOfferPriceMaster(List<PushMaster> pushMasterList,OrderDetailData orderDetailData){

        try{
            if(!MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode())){
                return pushMasterList;
            }

            if(CollectionUtils.isEmpty(pushMasterList)){
                return pushMasterList;
            }


            OfferPriceListResp offerPriceListResp = offerModuleResourceApi.orderOfferPriceList(orderDetailData.getGlobalOrderId(),orderDetailData.getAccountId(),orderDetailData.getAccountType());
            if(Objects.isNull(offerPriceListResp) || CollectionUtils.isEmpty(offerPriceListResp.getOrderOfferPriceInfoList())){
                return pushMasterList;
            }

            List<OrderOfferPrice> orderOfferPriceList = offerPriceListResp.getOrderOfferPriceInfoList().stream().map(orderOfferPriceInfo -> orderOfferPriceInfo.getOrderOfferPrice()).collect(Collectors.toList());

            Set<String> offerPriceMasterList = orderOfferPriceList.stream().map(OrderOfferPrice::getMasterId).map(String::valueOf).collect(Collectors.toSet());

            return pushMasterList.stream().filter(pushMaster -> !offerPriceMasterList.contains(pushMaster.getMasterId())).collect(Collectors.toList());
        }catch(Exception e){
            log.error("filterOfferPriceMaster error",e);
        }

        return pushMasterList;


    }


}
