//package com.wanshifu.master.order.push.service;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyByIdListRqt;
//import com.wanshifu.master.order.push.domain.dto.AgentDistributor;
//import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
//import com.wanshifu.util.LoCollectionsUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @description
// * @date 2025/2/28 15:43
// */
//@Slf4j
//@Component
//public class DistributorFactory {
//
////    @Resource
////    private OrderScoringStrategyService orderScoringStrategyService;
//
////    @Resource
////    OrderDistributeStrategyRepository orderDistributeStrategyRepository;
////
////    @Resource
////    OrderSelectStrategyRepository orderSelectStrategyRepository;
////
////    @Resource
////    OrderScoringStrategyRepository orderScoringStrategyRepository;
////
////    @Resource
////    private FeatureRepository featureRepository;
//
//
////    public DistributeOrderDistributor matchDistributor(OrderDistributeRqt orderDistributeRqt,
////                                                       DefaultContext<String, Object> orderFeatures, String distributeType){
////        final List<OrderDistributeStrategy> orderDistributeStrategies = orderDistributeStrategyRepository
////                .selectStrategyIds(
////                        GetOrderDistributeStrategyListRqt
////                                .GetOrderDistributeStrategyListRqtBuilder
////                                .aGetOrderDistributeStrategyListRqt()
////                                .withBusinessLineId((Integer)orderFeatures.get("business_line_id"))
////                                .withCategoryIds(String.valueOf((Integer)orderFeatures.get("category_id")))
////                                .withCityId((Long)orderFeatures.get("second_division_id"))
////                                .withStrategyStatus(1)
////                                .withDistributeType(distributeType)
////                                .build()
////                );
////
////        /**
////         * 初始化分配器
////         */
////        final DistributeOrderDistributor orderDistributor =new DistributeOrderDistributor();
////        extractDistributeFeatures(orderDistributor,orderDistributeStrategies);
////        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getAdmittanceOrderFeatureSet());
////
////        log.info("master_order_id:{}-before distribute orderFeatures[{}]"
////                ,orderDistributeRqt.getMasterOrderId()
////                ,orderFeatures
////        );
////        /**
////         * 过滤分配策略开启条件
////         */
////        final List<Integer> orderSelectStrategyIds =new ArrayList<>();
////        final List<Integer> orderScorerStrategyIds =new ArrayList<>();
////        out:for (OrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
////            final String distributeStrategyExpression =
////                    orderDistributeStrategy.getDistributeStrategyExpression();
////            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
////            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
////                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
////                String groupOpen=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
////                final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
////                        groupOpen, orderFeatures
////                );
////                if (expressBoolean) {
////                    orderSelectStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SELECT_STRATEGY_ID));
////                    orderScorerStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SCORING_STRATEGY_ID));
////                    orderDistributor.setDistributeRule(strategyGroup.getString(FieldConstant.ORDER_DISTRIBUTE_RULE));
////                    orderDistributor.setMatched(true);
////                    break out;
////                }
////            }
////        }
////        if (orderSelectStrategyIds.size()!=0) {
////            final List<OrderSelectStrategy> orderSelectStrategies =
////                    orderSelectStrategyRepository.selectAvailableStrategyByIdList(orderSelectStrategyIds);
////            if(StringUtils.isNotBlank(orderSelectStrategies.get(0).getSelectRuleExpression())){
////                orderDistributor.setSelectStrategy(orderSelectStrategies,orderFeatures);
////            }else{
////                orderDistributor.setSelectStrategy(orderSelectStrategies);
////            }
////        }
////        if (orderScorerStrategyIds.size()!=0) {
////            final List<OrderScoringStrategy> orderScoringStrategies =
////                    orderScoringStrategyRepository.selectAvailableStrategyByIdList(orderScorerStrategyIds);
////            orderDistributor.setScoringStrategy(orderScoringStrategies);
////        }
////
////        return orderDistributor;
////    }
////
////    public AgentDistributor generateByScorer(Map<String,Integer> agentScoreId){
////        final AgentDistributor agentDistributor =new AgentDistributor();
////        final Map<Integer, Set<String>> scorerMapping = new HashMap<>();
////        final List<Integer> scorerList =
////                new ArrayList<Integer>(
////                        agentScoreId.entrySet().stream().map(
////                                row -> {
////                                    LoCollectionsUtil.putToSet(scorerMapping,row.getValue(),row.getKey());
////                                    return row.getValue();
////                                }
////                        ).collect(Collectors.toSet())
////                );
////        if (CollectionUtil.isEmpty(scorerList)) {
////            agentDistributor.setScoringStrategy(scorerMapping, null);
////        } else {
////            OrderScoringStrategyByIdListRqt rqt = new OrderScoringStrategyByIdListRqt();
////            rqt.setStrategyIdList(scorerList);
////
////            final List<OrderScoringStrategy> orderScoringStrategies =
////                    orderScoringStrategyService.selectAvailableStrategyByIdList(rqt);
////            agentDistributor.setScoringStrategy(scorerMapping, orderScoringStrategies);
////        }
////        return agentDistributor;
////    }
//
//
////    private void extractDistributeFeatures(DistributeOrderDistributor distributeOrderDistributor,
////                                           List<OrderDistributeStrategy> orderDistributeStrategies){
////        for (OrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
////            final String distributeStrategyExpression =
////                    orderDistributeStrategy.getDistributeStrategyExpression();
////            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
////            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
////                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
////                String groupOpenFeatures=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
////                if (groupOpenFeatures!=null) {
////                    for (String featureCode : groupOpenFeatures.split(SymbolConstant.COMMA)) {
////                        distributeOrderDistributor.addAdmittanceOrderFeatureCode(featureCode);
////                    }
////                }
////            }
////        }
////    }
//}
