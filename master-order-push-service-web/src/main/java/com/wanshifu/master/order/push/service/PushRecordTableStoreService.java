//package com.wanshifu.master.order.push.service;
//
//import com.alicloud.openservices.tablestore.AsyncClient;
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.TableStoreCallback;
//import com.alicloud.openservices.tablestore.model.*;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
//import com.wanshifu.master.order.push.domain.po.DeleteRow;
//import com.wanshifu.master.order.push.domain.po.IncrementRow;
//import com.wanshifu.master.order.push.service.impl.OrderPackageMasterMatcher;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.Set;
//
///**
// * <AUTHOR>
// */
//@Component
//@Slf4j
//public class PushRecordTableStoreService {
//
//	@Autowired
//	TableStoreClient tableStoreClient;
//
//	public static final String T_MASTER_PUSH_COUNT="t_master_push_count";
//	private static String day_time_flag = "0";
//	public void orderPushCount(String pushTime, Set<String> masterSet) {
//		final String dayNow = DateFormatterUtil.formatDateString(pushTime,"yyyyMMdd");
//		if (!day_time_flag.equals(dayNow)&&!SymbolConstant.ZERO.equals(day_time_flag)) {
//			//init
//			day_time_flag = dayNow;
//			increaseCount(dayNow,masterSet);
//			clearCount(dayNow,masterSet);
//		}else {
//			//increment
//			increaseCount(dayNow,masterSet);
//		}
//	}
//
//
//	private void clearCount(String dayTime, Set<String> masterIdSet){
//		final ArrayList<DeleteRow> rows = new ArrayList<>();
//		final String dayString = DateFormatterUtil.assignDayString(dayTime, -2);
//		for (String masterId : masterIdSet) {
//			final PrimaryKey masterRow = tableStoreClient
//					.generatePrimaryKeyWithOneColumn(FieldConstant.MASTER_ID, masterId);
//			final DeleteRow deleteRow = new DeleteRow(masterRow, dayString);
//			rows.add(deleteRow);
//		}
//		tableStoreClient.batchDeleteRowAsync(T_MASTER_PUSH_COUNT,rows);
//	}
//
//	private void increaseCount(String dayTime,Set<String> masterIdSet){
//		final ArrayList<IncrementRow> rows = new ArrayList<>();
//		for (String masterId : masterIdSet) {
//			final PrimaryKey masterRow = tableStoreClient
//					.generatePrimaryKeyWithOneColumn(FieldConstant.MASTER_ID, masterId);
//			final IncrementRow incrementRow = new IncrementRow(masterRow,
//					new Column(dayTime, ColumnValue.fromLong(1L)));
//			rows.add(incrementRow);
//		}
//		tableStoreClient.batchIncrementRowAsync(T_MASTER_PUSH_COUNT,rows);
//	}
//
//
//	public static final String PUSH_COUNT_TAG="push_count_tag";
//	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {
//		final SyncClient syncClient = tableStoreClient.getSyncClient();
//		//构造主键
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(FieldConstant.PACKAGE_CONFIG_ID, PrimaryKeyValue.fromString(packageConfigId));
//		primaryKeyBuilder.addPrimaryKeyColumn(FieldConstant.MASTER_ID, PrimaryKeyValue.fromString(masterId));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//
//		//读取一行数据。
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(OrderPackageMasterMatcher.MASTER_ORDER_PACKAGE, primaryKey);
//		criteria.setMaxVersions(1);
//		GetRowResponse getRowResponse = syncClient.getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		final String pushCountTag = tableStoreClient.getValue(row, PUSH_COUNT_TAG);
//		RowUpdateChange rowUpdateChange = new RowUpdateChange(OrderPackageMasterMatcher.MASTER_ORDER_PACKAGE, primaryKey);
//		if (pushCountTag == null) {
//			rowUpdateChange.put(PUSH_COUNT_TAG, ColumnValue.fromString(miToDay));
//			rowUpdateChange.put(OrderPackageMasterMatcher.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY, ColumnValue.fromLong(1));
//		} else if (miToDay.compareTo(pushCountTag)>0) {
//			rowUpdateChange.put(PUSH_COUNT_TAG, ColumnValue.fromString(miToDay));
//			rowUpdateChange.put(OrderPackageMasterMatcher.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY, ColumnValue.fromLong(1));
//		} else {
//			rowUpdateChange.increment(new Column(OrderPackageMasterMatcher.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY,
//					ColumnValue.fromLong(1)));
//		}
//		try {
//			syncClient.updateRow(new UpdateRowRequest(rowUpdateChange));
//		}
//		catch (Exception e){
//			log.warn("[2] packageOrderPushCount exception:[{}]",e);
//		}
//	}
//
//
//	private static final String ORDER_REFUND = "t_order_refund";
//	public void saveOrderRefund(Long userId, Long masterId, Integer orderServeType, Date refundTime,Long orderId) {
//		final AsyncClient asyncClientDataCenter = tableStoreClient.getAsyncClient();
//		PrimaryKey primaryKey =tableStoreClient.generatePrimaryKeyWithThreeColumnsString(
//				FieldConstant.USER_ID,String.valueOf(userId),
//				FieldConstant.MASTER_ID,String.valueOf(masterId),
//				FieldConstant.ORDER_SERVE_TYPE,String.valueOf(orderServeType)
//		);
//		RowUpdateChange rowUpdateChange=new RowUpdateChange(ORDER_REFUND, primaryKey);
//		rowUpdateChange.put(FieldConstant.REFUND_TIME, ColumnValue.fromLong(refundTime.getTime()));
//		rowUpdateChange.put(FieldConstant.ORDER_ID, ColumnValue.fromString(String.valueOf(orderId)));
//
//		asyncClientDataCenter.updateRow(new UpdateRowRequest(rowUpdateChange), new TableStoreCallback<UpdateRowRequest, UpdateRowResponse>() {
//			@Override
//			public void onCompleted(UpdateRowRequest updateRowRequest, UpdateRowResponse updateRowResponse) {
//			}
//			@Override
//			public void onFailed(UpdateRowRequest updateRowRequest, Exception e) {
//
//			}
//		});
//	}
//
//	public static String ORDER_PUSH = "order_push";
//	public void saveOrderPush(String masterOrderId, String pushTime, String pushMaster,String pushMode) {
//		try{
//			final AsyncClient asyncClient = tableStoreClient.getAsyncClient();
//			PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(
//					FieldConstant.MASTER_ORDER_ID,masterOrderId,
//					FieldConstant.PUSH_TIME,pushTime
//			);
//			RowUpdateChange rowUpdateChange=new RowUpdateChange(ORDER_PUSH, primaryKey);
//			rowUpdateChange.put(FieldConstant.MASTER_ID_LIST, ColumnValue.fromString(pushMaster));
//			rowUpdateChange.put(FieldConstant.PUSH_MODE, ColumnValue.fromString(pushMode));
//
//			asyncClient.updateRow(new UpdateRowRequest(rowUpdateChange), new TableStoreCallback<UpdateRowRequest, UpdateRowResponse>() {
//				@Override
//				public void onCompleted(UpdateRowRequest updateRowRequest, UpdateRowResponse updateRowResponse) {
//				}
//				@Override
//				public void onFailed(UpdateRowRequest updateRowRequest, Exception e) {
//
//				}
//			});
//		}catch(Exception e){
//			log.error("保存推单记录失败",e);
//		}
//
//	}
//
//
//}