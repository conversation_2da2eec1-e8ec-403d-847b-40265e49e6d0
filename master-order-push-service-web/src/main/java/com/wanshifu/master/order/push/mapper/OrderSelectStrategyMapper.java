package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.GetOrderSelectStrategyListRqt;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 11:22
 */
public interface OrderSelectStrategyMapper extends IBaseCommMapper<OrderSelectStrategy> {

    List<OrderSelectStrategy> selectList(GetOrderSelectStrategyListRqt rqt);
}
