package com.wanshifu.master.order.push.service.impl;


import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import org.springframework.stereotype.Component;

/**
 * 订单师傅匹配器
 * <AUTHOR>
 */
@Component
public interface OrderMasterMatcher {

    /**
     * 订单匹配师傅
     * @param masterCondition
     * @return
     */
    boolean executeMatch(OrderDetailData orderDetailData, MasterMatchCondition masterCondition);


    MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition);



}
