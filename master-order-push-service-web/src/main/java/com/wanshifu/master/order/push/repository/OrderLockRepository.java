package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderLock;
import org.springframework.stereotype.Repository;


@Repository
public class OrderLockRepository extends BaseRepository<OrderLock> {


    public OrderLock selectByCityDivisionId(Long cityDivisionId){
        OrderLock orderLock = new OrderLock();
        orderLock.setCityDivisionId(cityDivisionId);
        return CollectionUtils.getFirstSafety(select(orderLock));
    }

}