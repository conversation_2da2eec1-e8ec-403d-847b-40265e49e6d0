package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.AppointGroupExpressionDto;
import com.wanshifu.master.order.push.domain.dto.FilterStrategyRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.MasterQuotaFeatureExpressionDto;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.resp.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.FilterRuleItemVo;
import com.wanshifu.master.order.push.domain.vo.filterStrategy.RuleItem;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailRule;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.Trigger;
import com.wanshifu.master.order.push.mapper.BaseSelectStrategyMapper;
import com.wanshifu.master.order.push.mapper.LongTailStrategyMapper;
import com.wanshifu.master.order.push.repository.LongTailStrategyRepository;
import com.wanshifu.master.order.push.repository.MasterQuotaRepository;
import com.wanshifu.master.order.push.service.LongTailStrategyService;
import com.wanshifu.master.order.push.service.QLExpressHandler;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LongTailStrategyServiceImpl implements LongTailStrategyService {

    private final LongTailStrategyRepository longTailStrategyRepository;

    private final MasterQuotaRepository masterQuotaRepository;
    /**
     * 策略管理-获取列表
     *
     * @param rqt
     * @return
     */
    @Override
    public SimplePageInfo<LongTailStrategy> list(ListRqt rqt) {
        Page<ListResp> startPage = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());

        final List<LongTailStrategy> longTailStrategies = longTailStrategyRepository.selectList(
                rqt.getLongTailStrategyIdList(),
                rqt.getBusinessLineId(),
                rqt.getLongTailStrategyName(),
                rqt.getIsActive(),
                rqt.getCreateStartTime(),
                rqt.getCreateEndTime()
        );

        SimplePageInfo<LongTailStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(longTailStrategies);
        return listRespSimplePageInfo;

    }

    /**
     * 策略管理-创建长尾单策略
     *
     * @param createRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "longTailStrategyName")
    public int create(CreateRqt createRqt) {
        final String longTailStrategyName = createRqt.getLongTailStrategyName();
        String pushType = createRqt.getPushType();
        final Integer businessLineId = createRqt.getBusinessLineId();
        final String longTailStrategyDesc = createRqt.getLongTailStrategyDesc();
        final String strategyJson = JSON.toJSONString(createRqt.getLongTailRule());
        final String strategyExpression =createRqt.getLongTailRule().getTrigger().triggerQLExpression();

        //过滤规则
        final List<FilterRuleItemVo> filterRuleList = createRqt.getLongTailRule().getFilterRuleItem();

        //过滤规则表达式
        List<FilterStrategyRuleExpressionDto> ruleExpressionJson = this.getRuleExpressions(filterRuleList, businessLineId);

        final Long updateAccountId = createRqt.getCreateAccountId();
        final Long createAccountId = createRqt.getCreateAccountId();

        checkStrategyName(longTailStrategyName,businessLineId,null);
        this.checkRuleParams(filterRuleList);

        AppointGroupExpressionDto appointGroupExpressionDto = getAppointGroupExpression(createRqt.getLongTailRule().getAppointGroup());
        String appointGroupExpression = Objects.nonNull(appointGroupExpressionDto) ? JSON.toJSONString(appointGroupExpressionDto) : "";


        longTailStrategyRepository.insert(
                longTailStrategyName,
                businessLineId,
                pushType,
                JSON.toJSONString(createRqt.getLongTailRule().getRangeSelect()),
                JSON.toJSONString(createRqt.getLongTailRule().getStatusSelect()),
                JSON.toJSONString(filterRuleList),
                JSON.toJSONString(ruleExpressionJson),
                JSON.toJSONString(createRqt.getLongTailRule().getPushRule()),
                longTailStrategyDesc,
                strategyJson,
                strategyExpression,
                appointGroupExpression,
                updateAccountId,
                createAccountId
        );
        return 0;
    }

    private List<FilterStrategyRuleExpressionDto> getRuleExpressions(List<FilterRuleItemVo> filterRuleList, Integer businessLineId) {

        List<String> allMasterQuotaCodes = filterRuleList.stream().flatMap(it -> it.getFilterRule().getItemList().stream()).map(FilterRuleItemVo.FilterRuleItem::getItemName).filter(it -> !StringUtils.equals("master_group", it)).distinct().filter(Objects::nonNull).collect(Collectors.toList());

        //<指标编码,指标条件表达式>   例如  <master_work_status,{"preCondition":{"ruleExpression":"is_high_quality == 1","ruleExpressionParams":"is_high_quality"},"calculateExpression":"allMatch(itemName,'term',value) "}>
        if (Objects.nonNull(businessLineId)) {
            if (businessLineId == 3) {
                businessLineId = 1;
            } else if (businessLineId == 999) {
                businessLineId = 2;
            }
        }
        Map<String, String> masterQuotaFeatureExpressionMap = masterQuotaRepository.selectByCodes(allMasterQuotaCodes, businessLineId)
                .stream().collect(Collectors.toMap(MasterQuota::getQuotaCode, MasterQuota::getFeatureExpression));

        return filterRuleList.stream().map(rule -> {

            FilterRuleItemVo.OpenCondition openCondition = rule.getOpenCondition();

            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user", "user_group");
                        if (specialTerms.contains(s.getItemName())) {
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<FilterRuleItemVo.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }

            FilterRuleItemVo.FilterRule filterRule = rule.getFilterRule();
            String condition = filterRule.getCondition();
            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(FilterRuleItemVo.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));
            //召回规则表达式参数
            List<String> filterRuleParamList = new ArrayList<>(Collections.emptyList());
            List<String> masterQuotaExpressions = filterRule.getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                filterRuleParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(), null);

                //指标是否包含前置条件
                String masterQuotaFeatureJson = masterQuotaFeatureExpressionMap.get(it.getItemName());
                if (StringUtils.isNotBlank(masterQuotaFeatureJson)) {
                    MasterQuotaFeatureExpressionDto masterQuotaFeatureExpressionDto = JSON.parseObject(masterQuotaFeatureJson, MasterQuotaFeatureExpressionDto.class);
                    String calculateExpression = masterQuotaFeatureExpressionDto.getCalculateExpression();
                    if (StringUtils.isNotBlank(calculateExpression)) {
                        //特殊的函数计算表达式
                        masterQuotaFilterRuleExpression = " " + calculateExpression
                                .replace("itemName", itemName)
                                .replace("term", term)
                                .replace("value", it.getItemValue());
                    }

                    if (masterQuotaFeatureExpressionDto.getPreCondition() != null) {
                        filterRuleParamList.addAll(Arrays.asList(masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpressionParams().split(",")));
                        masterQuotaOpenConditionRuleExpression = masterQuotaFeatureExpressionDto.getPreCondition().getRuleExpression();
                        //(规则1开启条件满足 ? 规则1 : true )
                        //(规则1开启条件满足 ? 规则1 : false )
                        masterQuotaFilterRuleExpression = StrUtil.format(" ({} ?{}: {}) ", masterQuotaOpenConditionRuleExpression, masterQuotaFilterRuleExpression, StringUtils.equals(condition, "and"));
                    }
                }
                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());
            // (规则1开启条件满足 ? 规则1 : true ) and 规则2 and 规则3
            // (规则1开启条件满足 ? 规则1 : false ) or 规则2 or 规则3
            String filterRuleExpression = QlExpressUtil.transitionQlExpressStr(condition, masterQuotaExpressions);

            String filterRuleParams = filterRuleParamList.stream().distinct().collect(Collectors.joining(","));
            return new FilterStrategyRuleExpressionDto(rule.getRuleName(), openConditionRuleExpression, openConditionRuleParams, filterRuleExpression, filterRuleParams);
        }).collect(Collectors.toList());
    }


    private void checkRuleParams(List<FilterRuleItemVo> filterRuleList) {
        List<RuleItem> ruleListCopy = BeanCopyUtil.copyListProperties(filterRuleList, RuleItem.class, null);
        //规则名称
        List<String> ruleNames = ruleListCopy.stream().map(RuleItem::getRuleName).distinct().collect(Collectors.toList());
        Assert.isTrue(ruleListCopy.size() == ruleNames.size(), "不可存在相同的规则名称");
        //不能存在开启条件一样的两条规则,仅顺序不同，也认为是相同的条件
        List<RuleItem.OpenCondition> distinctOpenConditions = ruleListCopy.stream().map(RuleItem::getOpenCondition).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> {
            List<RuleItem.OpenConditionItem> itemList = BeanCopyUtil.copyListProperties(o.getItemList(), RuleItem.OpenConditionItem.class, null);
            itemList.sort(Comparator.comparingInt(c -> c.getItemName().hashCode()));
            String itemListStr = itemList.stream().map(it -> {
                List<List<Long>> serveIdList = Optional.ofNullable(it.getServeIdList()).orElse(Collections.emptyList());
                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).sorted(Long::compare).collect(Collectors.toList());
                return it.getItemName() + "_" + it.getTerm() + "_" + it.getItemValue() + "_" + serveLevel1Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel2Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel3Ids.stream().map(Object::toString).collect(Collectors.joining(","));
            }).collect(Collectors.joining(";"));
            return o.getCondition() + ";" + itemListStr;
        }))), ArrayList::new));
        Assert.isTrue(distinctOpenConditions.size() == filterRuleList.stream().map(FilterRuleItemVo::getOpenCondition).count(), "不能存在开启条件一样的两条规则");
        boolean checkServe = filterRuleList.stream().allMatch(it -> it.getOpenCondition().getItemList().stream().allMatch(item -> !"serve".equals(item.getItemName()) || !(CollectionUtils.isEmpty(item.getServeIdList()))));
        Assert.isTrue(checkServe, "开启条件选择服务时,至少选择一个服务!");
    }


    private AppointGroupExpressionDto getAppointGroupExpression(LongTailRule.AppointGroup appointGroup){

        if(appointGroup.getIsAppointGroup() == 1){
            AppointGroupExpressionDto appointGroupExpressionDto = new AppointGroupExpressionDto();
            List<String> appointGroupParamList = new ArrayList<>(Collections.emptyList());
            List<String> appointGroupExpressions = appointGroup.getItemList().stream().map(it -> {
                String itemName = it.getItemName();
                //指标的开启条件
                String masterQuotaOpenConditionRuleExpression = "";
                appointGroupParamList.add(it.getItemName());
                String term;
                //师傅人群
                if ("master_group".equals(it.getItemName())) {
                    term = "in".equals(it.getTerm()) ? "contain" : "notContain";
                } else {
                    term = it.getTerm();
                }

                //单个指标本身的表达式
                String masterQuotaFilterRuleExpression = QlExpressUtil.transitionQlExpress(it.getItemName(), term, it.getItemValue(),null);

                return masterQuotaFilterRuleExpression;
            }).collect(Collectors.toList());


            String appointGroupExpression = QlExpressUtil.transitionQlExpressStr("and", appointGroupExpressions);

            String appointGroupParams = appointGroupParamList.stream().distinct().collect(Collectors.joining(","));

            appointGroupExpressionDto.setAppointGroupExpression(appointGroupExpression);
            appointGroupExpressionDto.setAppointGroupParams(appointGroupParams);
            return appointGroupExpressionDto;
        }

        return null;


    }




    public static void main(String[] args) throws Exception {
        String asd="{\n" +
                "      \"triggerIntervalTime\": {\n" +
                "          \"itemName\": \"push_minutes_interval\",\n" +
                "          \"term\": \"=\",\n" +
                "          \"itemValue\": 30\n" +
                "       },\n" +
                "      \"triggerCondition\": {\n" +
                "        \"condition\": \"and\",\n" +
                "        \"itemList\": [\n" +
                "          {\n" +
                "            \"itemName\": \"serve\",\n" +
                "            \"itemValue\": \"3,2\",\n" +
                "            \"term\": \"not_in\",\n" +
                "            \"serveIdList\": [\n" +
                "              [\n" +
                "                1\n" +
                "              ],\n" +
                "              [\n" +
                "                1,\n" +
                "                2\n" +
                "              ],\n" +
                "              [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3\n" +
                "              ]\n" +
                "            ]\n" +
                "          },\n" +
                "          {\n" +
                "            \"itemName\": \"appoint_type\",\n" +
                "            \"itemValue\": \"3\",\n" +
                "            \"term\": \"in\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }";
        final Trigger trigger = JSON.parseObject(asd, Trigger.class);

        String[] qlExpression=new String[1];
        final ArrayList<String> params = new ArrayList<>();
        trigger.parseTriggerQLExpression(qlExpression,params);

        System.out.println(qlExpression[0]);
        System.out.println(params);
        String triggerQLExpression=qlExpression[0];


        final QLExpressHandler qlExpressHandler = new QLExpressHandler();
        qlExpressHandler.initQueryClient();
        final ExpressRunner expressRunner = qlExpressHandler.getExpressRunner();
        System.out.println(triggerQLExpression);
        boolean aBoolean=Boolean.valueOf(expressRunner.execute(triggerQLExpression,new DefaultContext<String,Object>(){{
                    put("push_minutes_interval",30);
                    put("serve",4);
                    put("appoint_type",3);
                }},
                null,true,false).toString());


        System.out.println(aBoolean);
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Integer businessLineId, Long strategyId) {
        final LongTailStrategy longTailStrategy = longTailStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(longTailStrategy, "该业务线已存在相同策略名称!");
    }

    /**
     * 策略管理-修改长尾单策略
     *
     * @param updateRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略管理",
            tableName = "long_tail_strategy_base", mapperClass = LongTailStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyMapper", primaryKeyFieldName = "longTailStrategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyName")
    public int update(UpdateRqt updateRqt) {
        final Long longTailStrategyId = updateRqt.getLongTailStrategyId();
        final String longTailStrategyName = updateRqt.getLongTailStrategyName();
        final String longTailStrategyDesc = updateRqt.getLongTailStrategyDesc();
        final String pushType = updateRqt.getPushType();
        final Integer businessLineId = updateRqt.getBusinessLineId();
        final String strategyJson = JSON.toJSONString(updateRqt.getLongTailRule());
        final String strategyExpression =updateRqt.getLongTailRule().getTrigger().triggerQLExpression();

        //过滤规则
        final List<FilterRuleItemVo> filterRuleList = updateRqt.getLongTailRule().getFilterRuleItem();

        //过滤规则表达式
        List<FilterStrategyRuleExpressionDto> ruleExpressionJson = this.getRuleExpressions(filterRuleList, businessLineId);

        final Long updateAccountId = updateRqt.getUpdateAccountId();
        checkStrategyName(longTailStrategyName,businessLineId,longTailStrategyId);

        AppointGroupExpressionDto appointGroupExpressionDto = getAppointGroupExpression(updateRqt.getLongTailRule().getAppointGroup());
        String appointGroupExpression = Objects.nonNull(appointGroupExpressionDto) ? JSON.toJSONString(appointGroupExpressionDto) : "";

        longTailStrategyRepository.update(
                longTailStrategyId,
                pushType,
                longTailStrategyName,
                JSON.toJSONString(updateRqt.getLongTailRule().getRangeSelect()),
                JSON.toJSONString(updateRqt.getLongTailRule().getStatusSelect()),
                JSON.toJSONString(filterRuleList),
                JSON.toJSONString(ruleExpressionJson),
                JSON.toJSONString(updateRqt.getLongTailRule().getPushRule()),
                longTailStrategyDesc,
                strategyJson,
                strategyExpression,
                appointGroupExpression,
                updateAccountId
        );
        return 0;
    }

    /**
     * 策略管理-更新策略状态（启用/禁用）
     *
     * @param enableRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略管理",
            tableName = "long_tail_strategy_base", mapperClass = LongTailStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyMapper", primaryKeyFieldName = "longTailStrategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyName")
    public int updateStatus(EnableRqt enableRqt) {
        final Long longTailStrategyId = enableRqt.getLongTailStrategyId();
        final Integer isActive = enableRqt.getIsActive();
        longTailStrategyRepository.updateStatus(
                longTailStrategyId,
                isActive,
                enableRqt.getUpdateAccountId()
        );
        return 0;
    }

    /**
     * 策略管理-删除长尾单策略
     *
     * @param deleteRqt
     * @return
     */
    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "长尾单管理", level2MenuName = "长尾单策略管理",
            tableName = "long_tail_strategy_base", mapperClass = LongTailStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "longTailStrategyMapper", primaryKeyFieldName = "longTailStrategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "longTailStrategyName")
    public int delete(DeleteRqt deleteRqt) {
        final Long longTailStrategyId = deleteRqt.getLongTailStrategyId();
        longTailStrategyRepository.delete(
                longTailStrategyId
        );
        return 0;
    }

    /**
     * 策略管理-策略详情
     *
     * @param detailRqt
     * @return
     */
    @Override
    public LongTailStrategy detail(DetailRqt detailRqt) {
        Long strategyId = detailRqt.getLongTailStrategyId();
        final LongTailStrategy longTailStrategy = longTailStrategyRepository.selectByStrategyId(strategyId);
        return longTailStrategy;
    }

}
