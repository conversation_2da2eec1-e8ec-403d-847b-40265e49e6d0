package com.wanshifu.master.order.push.service.impl;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;

import com.wanshifu.master.order.push.mapper.NormalOrderDistributeStrategyMapper;
import com.wanshifu.master.order.push.repository.NormalOrderDistributeStrategyRepository;
import com.wanshifu.master.order.push.service.NormalOrderDistributeStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NormalOrderDistributeStrategyServiceImpl implements NormalOrderDistributeStrategyService {


    @Resource
    private NormalOrderDistributeStrategyRepository normalOrderDistributeStrategyRepository;


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "普通订单调度",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {

        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        checkStrategyName(strategyName, businessLineId, null);

        List<CreateRqt.CompensateDistributeStrategy> distributeStrategyList = rqt.getCompensateDistributeList();
        return normalOrderDistributeStrategyRepository.insert(rqt.getStrategyName(), rqt.getStrategyDesc(),
                rqt.getBusinessLineId(), rqt.getCategoryIds(),
                rqt.getCityIds(), JSON.toJSONString(distributeStrategyList),
                rqt.getCreateAccountId());
    }


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "普通订单调度",
            tableName = "normal_order_distribute_strategy", mapperClass = NormalOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "normalOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer update(UpdateRqt rqt) {
        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        Integer strategyId = rqt.getStrategyId();
        checkStrategyName(strategyName, businessLineId, strategyId);
        List<CreateRqt.CompensateDistributeStrategy> distributeStrategyList = rqt.getCompensateDistributeList();
        return normalOrderDistributeStrategyRepository.update(strategyId, rqt.getStrategyName(), rqt.getStrategyDesc(),
                rqt.getBusinessLineId(), rqt.getCategoryIds(),
                rqt.getCityIds(), JSON.toJSONString(distributeStrategyList),
                rqt.getUpdateAccountId());
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Integer businessLineId, Integer strategyId) {
        NormalOrderDistributeStrategy normalOrderDistributeStrategy = normalOrderDistributeStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(normalOrderDistributeStrategy, "该业务线已存在相同策略名称!");
    }


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "普通订单调度",
            tableName = "normal_order_distribute_strategy", mapperClass = NormalOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "normalOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt) {
        Condition condition = new Condition(NormalOrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId())
                .andEqualTo("strategyStatus", 0)
                .andEqualTo("isDelete", 0);

        NormalOrderDistributeStrategy normalOrderDistributeStrategy = new NormalOrderDistributeStrategy();
        normalOrderDistributeStrategy.setIsDelete(1);
        normalOrderDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = normalOrderDistributeStrategyRepository.updateByConditionSelective(normalOrderDistributeStrategy, condition);
        Assert.isTrue(result > 0, "删除失败");
        return 1;
    }

    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "调度管理", level2MenuName = "普通订单调度",
            tableName = "normal_order_distribute_strategy", mapperClass = NormalOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "normalOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt) {
        Condition condition = new Condition(NormalOrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId())
                .andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);
        NormalOrderDistributeStrategy agreementOrderDistributeStrategy = new NormalOrderDistributeStrategy();
        agreementOrderDistributeStrategy.setStrategyStatus(rqt.getStrategyStatus());
        agreementOrderDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = normalOrderDistributeStrategyRepository.updateByConditionSelective(agreementOrderDistributeStrategy, condition);

        if (rqt.getStrategyStatus() == 1) {
            Assert.isTrue(result > 0, "启用策略失败");
        } else {
            Assert.isTrue(result > 0, "禁用策略失败");
        }
        return 1;
    }

    @Override
    public NormalOrderDistributeStrategy detail(DetailRqt rqt) {
        return normalOrderDistributeStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());

    }

    @Override
    public SimplePageInfo<NormalOrderDistributeStrategy> list(ListRqt rqt) {
        Page<?> page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<NormalOrderDistributeStrategy> pushNoticeStrategyList = normalOrderDistributeStrategyRepository.selectList(rqt);
        SimplePageInfo<NormalOrderDistributeStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(pushNoticeStrategyList);
        return listRespSimplePageInfo;
    }

}
