package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 代理商订单调度表
 * @date 2025/2/26 18:27
 */
@Data
@ToString
@Table(name = "agent_order_distribute")
public class AgentOrderDistribute {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "distribute_id")
    private Long distributeId;


    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 代理商名称
     */
    @Column(name = "agent_id")
    private Long agentId;

    /**
     * 所在地城市id
     */
    @Column(name = "order_serve_ids")
    private String orderServeIds;

    /**
     * 所在地三级区域id(逗号隔开)
     */
    @Column(name = "third_division_id")
    private Long thirdDivisionId;

    /**
     * 强制推单服务(逗号隔开)
     */
    @Column(name = "strategy_id")
    private Integer strategyId;

    /**
     * 调度规则
     */
    @Column(name = "distribute_rule")
    private String distributeRule;

    /**
     * 调度优先级
     */
    @Column(name = "distribute_priority")
    private String distributePriority;

    /**
     * 直接指派失败原因
     */
    @Column(name = "direct_appoint_fail_reason")
    private String directAppointFailReason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
