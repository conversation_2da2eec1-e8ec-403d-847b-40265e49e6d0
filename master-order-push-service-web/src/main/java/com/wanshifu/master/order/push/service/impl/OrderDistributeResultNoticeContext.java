package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/25 19:02
 */
public class OrderDistributeResultNoticeContext {


    private static Map<Long, OrderDistributeResultNoticeService> maps = new HashMap<>();

    public static OrderDistributeResultNoticeService getInstance(Long code) {
        if (Objects.isNull(code) || !maps.containsKey(code)){
            throw new IllegalArgumentException(String.format("参数违法,node:%s", code));

        }
        return maps.get(code);
    }

    public static void register(Long code, OrderDistributeResultNoticeService orderDistributeResultNoticeService) {
        Assert.notNull(code, "type can't be null");
        maps.put(code, orderDistributeResultNoticeService);
    }
}
