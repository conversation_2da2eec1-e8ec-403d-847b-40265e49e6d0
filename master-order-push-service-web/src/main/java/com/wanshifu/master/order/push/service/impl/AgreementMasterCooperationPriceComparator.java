package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Objects;
import java.util.Random;

public class AgreementMasterCooperationPriceComparator  implements Comparator<ApplyOrderCalculateBatchResp>{

    @Override
    public int compare(ApplyOrderCalculateBatchResp o1, ApplyOrderCalculateBatchResp o2){

        int result = this.getCost(o1).compareTo(this.getCost(o2));
        if(result != 0){
            if(result > 0){
                return 1;
            }else{
                return -1;
            }
        }else{
            Random random = new Random();
            int randomNum = random.nextInt(2);
            if(randomNum == 0){
                return -1;
            }else{
                return 1;
            }
        }

    }

    private BigDecimal getCost(ApplyOrderCalculateBatchResp resp){
        BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
        BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
        if(Objects.isNull(basePrice)){
            return cost;
        }
        return cost.compareTo(basePrice) >= 0 ? cost : basePrice;
    }
}
