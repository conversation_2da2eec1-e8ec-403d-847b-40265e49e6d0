package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.mapper.PermissionSetMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
@Deprecated
public class PermissionSetRepository extends BaseRepository<PermissionSet> {

    @Resource
    private PermissionSetMapper permissionSetMapper;

    public PermissionSet selectByPermissionSetName(String permissionSetName, Integer permissionSetId) {
        Example example = new Example(PermissionSet.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("permissionSetName", permissionSetName)
                .andEqualTo("isDelete", 0);
        if (permissionSetId != null) {
            criteria.andNotEqualTo("permissionSetId", permissionSetId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public int insertPermissionSet(String permissionSetName,String permissionSetDesc,String permissionSetStr,String roleList,Long createAccountId){
        PermissionSet permissionSet = new PermissionSet();
        permissionSet.setPermissionSetName(permissionSetName);
        permissionSet.setPermissionSetDesc(permissionSetDesc);
        permissionSet.setPermissionSet(permissionSetStr);
        permissionSet.setRoleList(roleList);
        permissionSet.setCreateAccountId(createAccountId);
        permissionSet.setUpdateAccountId(createAccountId);
        this.insertSelective(permissionSet);
        return permissionSet.getPermissionSetId();
    }


    public int updatePermissionSet(Integer permissionSetId,String permissionSetName,String permissionSetDesc,String permissionSetStr,String roleList,Long updateAccountId){
        PermissionSet permissionSet = new PermissionSet();
        permissionSet.setPermissionSetId(permissionSetId);
        permissionSet.setPermissionSetName(permissionSetName);
        permissionSet.setPermissionSetDesc(permissionSetDesc);
        permissionSet.setRoleList(roleList);
        permissionSet.setPermissionSet(permissionSetStr);
        permissionSet.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(permissionSet);
    }


    public List<PermissionSet> selectList(String permissionSetName, Date createTimeStart, Date createTimeEnd,List<Integer> roleIdList){
        return permissionSetMapper.selectList(permissionSetName,createTimeStart,createTimeEnd,roleIdList);

    }


}