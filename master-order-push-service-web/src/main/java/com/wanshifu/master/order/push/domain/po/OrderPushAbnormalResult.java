package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * 订单推送异常结果表
 */
@Data
@ToString
@Table(name = "order_push_abnormal_result")
public class OrderPushAbnormalResult {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "global_order_id")
    private Long globalOrderId;

    @Column(name = "msg_id")
    private String msgId;

    @Column(name = "topic")
    private String topic;

    @Column(name = "tag")
    private String tag;

    @Column(name = "msg_body")
    private String msgBody;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

}
