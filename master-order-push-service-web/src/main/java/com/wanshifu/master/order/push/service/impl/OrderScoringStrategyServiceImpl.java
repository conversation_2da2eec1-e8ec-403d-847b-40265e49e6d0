package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyByIdListRqt;
import com.wanshifu.master.order.push.domain.dto.SortRuleExpressionDto;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;
import com.wanshifu.master.order.push.mapper.OrderScoringStrategyMapper;
import com.wanshifu.master.order.push.repository.OrderScoreItemRepository;
import com.wanshifu.master.order.push.repository.OrderScoreItemValueRepository;
import com.wanshifu.master.order.push.repository.OrderScoringStrategyRepository;
import com.wanshifu.master.order.push.repository.OrderStrategyRelateRepository;
import com.wanshifu.master.order.push.service.OrderScoringStrategyService;
import com.wanshifu.master.order.push.service.ScoreStrategyRuleExpressionService;
import com.wanshifu.util.BeanCopyUtil;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 16:19
 */
@Service
public class OrderScoringStrategyServiceImpl implements OrderScoringStrategyService {


    @Resource
    private OrderScoringStrategyRepository orderScoringStrategyRepository;

    @Resource
    private ScoreStrategyRuleExpressionService scoreStrategyRuleExpressionService;

    @Resource
    private OrderScoreItemRepository orderScoreItemRepository;


    @Resource
    private OrderScoreItemValueRepository orderScoreItemValueRepository;

    @Resource
    private OrderStrategyRelateRepository orderStrategyRelateRepository;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "非普通订单匹配", level2MenuName = "师傅评分模型",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public Integer create(CreateOrderScoringStrategyRqt rqt){

        String strategyName = rqt.getStrategyName();
        List<CreateOrderScoringStrategyRqt.SortRule> ruleList = rqt.getRuleList();
        Long businessLineId = rqt.getBusinessLineId();
        String masterResources = rqt.getMasterResources();
        this.checkStrategyName(strategyName, businessLineId, null);
        this.checkSortRuleParams(ruleList);
        String sortingRule = JSON.toJSONString(ruleList);
        //获取精排策略表达式集合
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = scoreStrategyRuleExpressionService.buildSortRuleExpressions(ruleList);
        return orderScoringStrategyRepository.insert(businessLineId,strategyName, masterResources,rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList), rqt.getCreateAccountId());

    }


    private void checkSortRuleParams(List<CreateOrderScoringStrategyRqt.SortRule> ruleList){
        if(CollectionUtils.isNotEmpty(ruleList)){
            ruleList.forEach(sortRule -> checkRuleParams(sortRule.getItemList()));
        }
    }


    /**
     * 校验匹配项参数
     *
     * @param itemList
     */
    private void checkRuleParams(List<CreateOrderScoringStrategyRqt.RuleItem> itemList) {
        itemList.forEach(it -> {
            //赋值方式为区间
            if (StringUtils.equals("range_value", it.getAssignMode())) {
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> scoreItem.getStartValue() != null && scoreItem.getEndValue() != null), StrUtil.format("请检查{}的区间参数", it.getItemTitle()));
            } else {
                //赋值方式为枚举
                Assert.isTrue(it.getScoreList().stream().allMatch(scoreItem -> StringUtils.isNotBlank(scoreItem.getValue())), StrUtil.format("请检查{}的枚举参数", it.getItemTitle()));
            }
        });
        Assert.isTrue(itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getItemTitle).distinct().count() == itemList.size(), "匹配项名称不可重复!");
        Assert.isTrue(itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getItemName).distinct().count() == itemList.size(), "不可存在两条相同的匹配项!");
        BigDecimal totalWeight = itemList.stream().map(CreateOrderScoringStrategyRqt.RuleItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalWeight.compareTo(BigDecimal.ONE) == 0, "所有匹配项权重之和必须等于1");
    }

    private void checkStrategyName(String strategyName, Long businessLineId, Integer strategyId) {
        OrderScoringStrategy orderScoringStrategy = orderScoringStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(orderScoringStrategy, "该业务线已存在相同策略名称!");
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "非普通订单匹配", level2MenuName = "师傅评分模型",
            tableName = "order_select_strategy", mapperClass = OrderScoringStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderScoringStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer update(UpdateOrderScoringStrategyRqt rqt){

        String masterResources = rqt.getMasterResources();

        Integer strategyId = rqt.getStrategyId();
        String strategyName = rqt.getStrategyName();
        List<CreateOrderScoringStrategyRqt.SortRule> ruleList = rqt.getRuleList();
        Long businessLineId = rqt.getBusinessLineId();

        this.checkStrategyName(strategyName, businessLineId, strategyId);
        this.checkSortRuleParams(ruleList);
        String sortingRule = JSON.toJSONString(ruleList);
        //获取精排策略表达式json集合
        List<SortRuleExpressionDto> sortRuleExpressionDtoList = scoreStrategyRuleExpressionService.buildSortRuleExpressions(ruleList);

        return orderScoringStrategyRepository.update(strategyId,businessLineId,strategyName,masterResources, rqt.getStrategyDesc(), rqt.getCategoryIds(), sortingRule, JSON.toJSONString(sortRuleExpressionDtoList),rqt.getUpdateAccountId() );



    }

    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "非普通订单匹配", level2MenuName = "师傅评分模型",
            tableName = "order_select_strategy", mapperClass = OrderScoringStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderScoringStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableOrderScoringStrategyRqt rqt){
        Condition condition = new Condition(AgentDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);
        OrderScoringStrategy orderScoringStrategy = new OrderScoringStrategy();
        orderScoringStrategy.setStrategyStatus(rqt.getStrategyStatus());
        orderScoringStrategy.setUpdateAccountId(rqt.getUpdateAccountId());

        if (rqt.getStrategyStatus() == 0) {
            OrderStrategyRelate strategyRelate = orderStrategyRelateRepository.selectByOrderScoringStrategyId(rqt.getStrategyId());
            Assert.isNull(strategyRelate, "当前记录已被应用，不可禁用");
        }

        int result = orderScoringStrategyRepository.updateByConditionSelective(orderScoringStrategy, condition);

        if(rqt.getStrategyStatus() == 1){
            org.springframework.util.Assert.isTrue(result > 0,"启用策略失败");
        }else{
            org.springframework.util.Assert.isTrue(result > 0,"禁用策略失败");
        }
        return 1;
    }

    @Override
    public OrderScoringStrategy detail(OrderScoringStrategyDetailRqt rqt){
        OrderScoringStrategy orderScoringStrategy = orderScoringStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
        return orderScoringStrategy;
    }

    @Override
    public List<OrderScoringStrategy> selectAvailableStrategyByIdList(OrderScoringStrategyByIdListRqt rqt) {
        return orderScoringStrategyRepository.selectAvailableStrategyByIdList(rqt.getStrategyIdList());
    }

    @Override
    public SimplePageInfo<OrderScoringStrategy> list(GetOrderScoringStrategyListRqt rqt){
        if(StringUtils.isNotBlank(rqt.getCategoryIds())){
            rqt.setCategoryIdList(Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                            .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toList()));
        }
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<OrderScoringStrategy> orderScoringStrategyList = orderScoringStrategyRepository.selectList(rqt);
        SimplePageInfo<OrderScoringStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(orderScoringStrategyList);
        return listRespSimplePageInfo;    }



    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "非普通订单匹配", level2MenuName = "师傅评分模型",
            tableName = "order_select_strategy", mapperClass = OrderScoringStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderScoringStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteOrderScoringStrategyRqt rqt){

        Condition condition = new Condition(AgentDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", 0).andEqualTo("isDelete", 0);

        OrderScoringStrategy orderScoringStrategy = new OrderScoringStrategy();
        orderScoringStrategy.setIsDelete(1);
        orderScoringStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = orderScoringStrategyRepository.updateByConditionSelective(orderScoringStrategy, condition);
        org.springframework.util.Assert.isTrue(result > 0,"删除失败");
        return 1;

    }

    @Override
    public List<OrderScoringItemListResp> orderScoreItemList(String itemName) {
        List<OrderScoreItem> scoreItems = orderScoreItemRepository.selectByItemName(itemName);
        List<Long> scoreItemIds = scoreItems.stream().filter(it -> StringUtils.equals("enum_value", it.getValueType()))
                .map(OrderScoreItem::getItemId).collect(Collectors.toList());
        List<OrderScoreItemValue> scoreItemValues = orderScoreItemValueRepository.selectByScoreItemIds(scoreItemIds);
        Map<Long, List<OrderScoreItemValue>> scoreItemValuesMap = scoreItemValues.stream().collect(Collectors.groupingBy(OrderScoreItemValue::getScoreItemId));
        return scoreItems.stream().map(it -> {
            OrderScoringItemListResp masterItemListResp = new OrderScoringItemListResp(it.getItemCode(), it.getItemName(),it.getItemDesc(),it.getValueType());
            if (StringUtils.equals(it.getValueType(), "enum_value")) {
                List<OrderScoreItemValue> values = scoreItemValuesMap.getOrDefault(it.getItemId(), Collections.emptyList());
                List<OrderScoringItemListResp.EnumValue> enumValues = BeanCopyUtil.copyListProperties(values, OrderScoringItemListResp.EnumValue.class, (s, t) -> {
                    t.setName(s.getName());
                    t.setValue(s.getCode());
                });
                masterItemListResp.setValueList(enumValues);
            }
            return masterItemListResp;
        }).collect(Collectors.toList());
    }


}
