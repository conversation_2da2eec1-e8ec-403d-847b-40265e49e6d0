package com.wanshifu.master.order.push.repository;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.PushStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.PushHandle;
import com.wanshifu.master.order.push.mapper.PushHandleMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;

/**
 * 推送处理记录Repository
 * <AUTHOR>
 */
@Repository
public class PushHandleRepository extends BaseRepository<PushHandle> {

    @Resource
    private PushHandleMapper pushHandleMapper;


    /**
     * 分表控制标记
     */
    private String TIME_FLAG = "0";


    @Async
    public int insertPushHanle(PushHandle pushHandle, PushStrategySnapshot pushStrategySnapshot){
        String timeMark = DateFormatterUtil.timeStampToTimedMonth(Long.valueOf(pushHandle.getOrderVersion()));
        checkAndCreateTable(pushHandle.getOrderVersion());
        pushHandle.setFilterMessage(JSON.toJSONString(pushHandle.getFilterMessageMap()));
        pushHandle.setScoreMessage(JSON.toJSONString(pushHandle.getScoreMessageObject()));
        pushHandle.setPushStrategy(JSON.toJSONString(pushStrategySnapshot));
        return pushHandleMapper.insertPushHandle(pushHandle,timeMark);
    }


    public void checkAndCreateTable(String orderVersion){
        //获取订单时间标记
        String timeMark = DateFormatterUtil.timeStampToTimedMonth(Long.valueOf(orderVersion));

        if (!TIME_FLAG.equals(timeMark)) {
            String tableName = "push_handle_" + DateFormatterUtil.getNowMonth();
            pushHandleMapper.createPushHandleTable(tableName);
            TIME_FLAG = timeMark;
        }
    }


    public PushHandle selectByOrderIdAndOrderVersion(Long orderId,String orderVersion){
        Condition condition = new Condition(PushHandle.class);
        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("orderVersion",orderVersion);
        condition.selectProperties("filterMessage");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }



}