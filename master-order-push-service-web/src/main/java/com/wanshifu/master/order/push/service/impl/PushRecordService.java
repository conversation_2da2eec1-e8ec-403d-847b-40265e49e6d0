package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.po.MasterOrderPackageIndex;
import com.wanshifu.master.order.push.domain.po.MasterOrderPushCount;
import com.wanshifu.master.order.push.domain.po.OrderRefund;
import com.wanshifu.master.order.push.repository.MasterOrderPackageRepository;
import com.wanshifu.master.order.push.repository.MasterOrderPushCountRepository;
import com.wanshifu.master.order.push.repository.OrderRefundRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PushRecordService {

	@Resource
	private MasterOrderPackageRepository masterOrderPackageRepository;

	@Resource
	private OrderRefundRepository orderRefundRepository;

	@Resource
	private MasterOrderPushCountRepository masterOrderPushCountRepository;


	@Resource
	private HBaseClient hBaseClient;





	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {

		MasterOrderPackageIndex masterOrderPackage = masterOrderPackageRepository.selectByPackageConfigIdAndMasterId(packageConfigId,masterId);

		final String pushCountTag = masterOrderPackage.getPushCountTag();
		MasterOrderPackageIndex updateMasterOrderPackage = new MasterOrderPackageIndex();
		updateMasterOrderPackage.setPackageId(masterOrderPackage.getPackageId());
		if (StringUtils.isEmpty(pushCountTag)) {
			updateMasterOrderPackage.setPushCountTag(miToDay);
			updateMasterOrderPackage.setMasterPackageOrderPushNumberDaily(1L);
		} else if (miToDay.compareTo(pushCountTag)>0) {
			updateMasterOrderPackage.setPushCountTag(miToDay);
			updateMasterOrderPackage.setMasterPackageOrderPushNumberDaily(1L);
		} else {
			updateMasterOrderPackage.setMasterPackageOrderPushNumberDaily(masterOrderPackage.getMasterPackageOrderPushNumberDaily() + 1L);
		}
		masterOrderPackageRepository.updateByPrimaryKeySelective(updateMasterOrderPackage);
	}


	@Async
	public int saveOrderRefund(Long userId, Long masterId, Integer orderServeType, Date refundTime,Long orderId) {

		OrderRefund orderRefund = orderRefundRepository.selectByUserIdAndMasterId(userId,masterId,orderServeType);
		if(Objects.nonNull(orderRefund)){
			return 0;
		}
		orderRefund = new OrderRefund();
		orderRefund.setUserId(userId);
		orderRefund.setMasterId(masterId);
		orderRefund.setOrderServeType(orderServeType);
		orderRefund.setRefundTime(refundTime);
		orderRefund.setOrderId(orderId);
		return orderRefundRepository.insertSelective(orderRefund);
	}

//	public static String ORDER_PUSH = "order_push";
//	public void saveOrderPush(String masterOrderId, String pushTime, String pushMaster,String pushMode) {
//		try{
//			final AsyncClient asyncClient = tableStoreClient.getAsyncClient();
//			PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(
//					FieldConstant.MASTER_ORDER_ID,masterOrderId,
//					FieldConstant.PUSH_TIME,pushTime
//			);
//			RowUpdateChange rowUpdateChange=new RowUpdateChange(ORDER_PUSH, primaryKey);
//			rowUpdateChange.put(FieldConstant.MASTER_ID_LIST, ColumnValue.fromString(pushMaster));
//			rowUpdateChange.put(FieldConstant.PUSH_MODE, ColumnValue.fromString(pushMode));
//
//			asyncClient.updateRow(new UpdateRowRequest(rowUpdateChange), new TableStoreCallback<UpdateRowRequest, UpdateRowResponse>() {
//				@Override
//				public void onCompleted(UpdateRowRequest updateRowRequest, UpdateRowResponse updateRowResponse) {
//				}
//				@Override
//				public void onFailed(UpdateRowRequest updateRowRequest, Exception e) {
//
//				}
//			});
//		}catch(Exception e){
//			log.error("保存推单记录失败",e);
//		}
//
//	}




	@Async
	public void orderPushCount(String pushTime, Set<String> masterSet) {

		try{
			final String dayNow = DateFormatterUtil.formatDateString(pushTime,"yyyyMMdd");

			List<MasterOrderPushCount> masterOrderPushCountList = masterOrderPushCountRepository.selectByMasterIdSet(masterSet);

			Map<String,MasterOrderPushCount> masterOrderPushCountMap = masterOrderPushCountList.stream().collect(Collectors.toMap(MasterOrderPushCount::getMasterId,MasterOrderPushCount->MasterOrderPushCount));

			masterSet.forEach(masterId -> {
				MasterOrderPushCount masterOrderPushCount = masterOrderPushCountMap != null ? masterOrderPushCountMap.get(masterId) : null;
				if(Objects.nonNull(masterOrderPushCount)){
					final String dt = masterOrderPushCount.getDt();
					MasterOrderPushCount updateMasterOrderPushCount = new MasterOrderPushCount();
					updateMasterOrderPushCount.setId(masterOrderPushCount.getId());
					if (StringUtils.isEmpty(dt)) {
						updateMasterOrderPushCount.setDt(dayNow);
						updateMasterOrderPushCount.setOrderCnt(1L);
					} else if (dayNow.compareTo(dt)>0) {
						updateMasterOrderPushCount.setDt(dayNow);
						updateMasterOrderPushCount.setOrderCnt(1L);
					} else {
						updateMasterOrderPushCount.setOrderCnt(masterOrderPushCount.getOrderCnt() + 1L);
					}
					masterOrderPushCountRepository.updateByPrimaryKeySelective(updateMasterOrderPushCount);
				}else{
					masterOrderPushCount = new MasterOrderPushCount();
					masterOrderPushCount.setMasterId(masterId);
					masterOrderPushCount.setDt(dayNow);
					masterOrderPushCount.setOrderCnt(1L);
					masterOrderPushCountRepository.insertSelective(masterOrderPushCount);
				}
			});
		}catch(Exception e){
			log.error("记录推单次数失败",e);
		}


	}


	@Async
	public void saveOrderPush(Long globalOrderTraceId,Set<String> masterIdSet,String pushMode){
		hBaseClient.updateData("order_push",String.valueOf(globalOrderTraceId),"0",pushMode,String.join(",",masterIdSet));
	}


	@Async
	public void increasePushMainNewModelMasterCount(Long globalOrderTraceId){
		String pushCntStr = hBaseClient.querySingle("order_push",String.valueOf(globalOrderTraceId),"new_model_single_cnt");
		if(StringUtils.isNotBlank(pushCntStr)){
			Integer pushCnt = Integer.valueOf(pushCntStr);
			pushCnt++;
			hBaseClient.putData("order_push",String.valueOf(globalOrderTraceId),"0","new_model_single_cnt",String.valueOf(pushCnt));
		}else{
			hBaseClient.putData("order_push",String.valueOf(globalOrderTraceId),"0","new_model_single_cnt","1");
		}
	}

	@Async
	public void increasePushOrderCountList(List<PushMaster> pushMasterList ,String day){


		for(PushMaster pushMaster : pushMasterList) {

			if(StringUtils.isBlank(pushMaster.getIdCardNumber())){
				continue ;
			}
			String rowKey = pushMaster.getIdCardNumber() + "_" + day;
			String pushCntStr = hBaseClient.querySingle("master_daily",rowKey,"push_order_cnt");
			if(StringUtils.isNotBlank(pushCntStr)){
				Integer pushCnt = Integer.valueOf(pushCntStr);
				pushCnt++;
				hBaseClient.putData("master_daily",rowKey,"0","push_order_cnt",String.valueOf(pushCnt));
			}else{
				hBaseClient.putData("master_daily",rowKey,"0","id_card_number",pushMaster.getIdCardNumber());
				hBaseClient.putData("master_daily",rowKey,"0","day",day);
				hBaseClient.putData("master_daily",rowKey,"0","push_order_cnt","1");

			}
		}

	}


	@Async
	public void increasePushOrderCountByBusinessLine(List<PushMaster> pushMasterList, String day, String businessLine){

		for(PushMaster pushMaster : pushMasterList){

			if(StringUtils.isBlank(pushMaster.getIdCardNumber())){
				continue;
			}
			String rowKey = pushMaster.getIdCardNumber() + "_" + businessLine + "_" + day;
			String pushCntStr = hBaseClient.querySingle("master_business_daily",rowKey,"push_order_cnt");
			if(StringUtils.isNotBlank(pushCntStr)){
				Integer pushCnt = Integer.valueOf(pushCntStr);
				pushCnt++;
				hBaseClient.putData("master_business_daily",rowKey,"0","push_order_cnt",String.valueOf(pushCnt));
			}else{
				hBaseClient.putData("master_business_daily",rowKey,"0","id_card_number",pushMaster.getIdCardNumber());
				hBaseClient.putData("master_business_daily",rowKey,"0","business_line",businessLine);
				hBaseClient.putData("master_business_daily",rowKey,"0","day",day);
				hBaseClient.putData("master_business_daily",rowKey,"0","push_order_cnt","1");
			}

		};

	}




	@Async
	public void increaseMasterRecruitDaily(Long masterId ,Long recruitId,String day){

		if(Objects.isNull(recruitId)){
			return ;
		}
		String rowKey = masterId + "_" + recruitId + "_" + day;
		String pushCntStr = hBaseClient.querySingle("master_recruit_daily",rowKey,"order_cnt");
		if(StringUtils.isNotBlank(pushCntStr)){
			Integer pushCnt = Integer.valueOf(pushCntStr);
			pushCnt++;
			hBaseClient.putData("master_recruit_daily",rowKey,"0","order_cnt",String.valueOf(pushCnt));
		}else{
			hBaseClient.putData("master_recruit_daily",rowKey,"0","master_id",String.valueOf(masterId));
			hBaseClient.putData("master_recruit_daily",rowKey,"0","recruit_id",String.valueOf(recruitId));
			hBaseClient.putData("master_recruit_daily",rowKey,"0","day",day);
			hBaseClient.putData("master_recruit_daily",rowKey,"0","order_cnt","1");

		}

	}

    /**
     * 更新  当日用户指派量（合作经营）
     * @param masterId
     * @param day
     */
    public void increaseCooperationBusinessMasterCntDaily(Long masterId, String day) {
        String rowKey = masterId + "_" + day;
        String assignCooperationCntStr = hBaseClient.querySingle("master_daily_data_cnt", rowKey, "assign_cooperation_cnt");
        if (StringUtils.isNotBlank(assignCooperationCntStr)) {
            Integer pushCnt = Integer.valueOf(assignCooperationCntStr);
            pushCnt++;
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "assign_cooperation_cnt", String.valueOf(pushCnt));
        } else {
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "assign_cooperation_cnt", "1");
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "master_id", String.valueOf(masterId));

        }

    }

    /**
     * 更新  当日有效抢单量（合作经营）
     * @param masterId
     * @param day
     */
    public void increaseCooperationBusinessMasterGrabCntDaily(Long masterId, String day) {
        String rowKey = masterId + "_" + day;
        String grabCooperationCntStr = hBaseClient.querySingle("master_daily_data_cnt", rowKey, "grab_cooperation_cnt");
        if (StringUtils.isNotBlank(grabCooperationCntStr)) {
            Integer pushCnt = Integer.valueOf(grabCooperationCntStr);
            pushCnt++;
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "grab_cooperation_cnt", String.valueOf(pushCnt));
        } else {
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "grab_cooperation_cnt", "1");
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "master_id", String.valueOf(masterId));

        }

    }


    /**
     * 更新  当日有效抢单量（技能验证后派单）
     * @param masterId
     * @param day
     */
    public void increaseAfterTechniqueVerifyMasterGrabCntDaily(Long masterId, String day) {
        String rowKey = masterId + "_" + day;
        String afterTechniqueVerifyGrabCntStr = hBaseClient.querySingle("master_daily_data_cnt", rowKey, "after_technique_verify_grab_cnt");
        if (StringUtils.isNotBlank(afterTechniqueVerifyGrabCntStr)) {
            Integer pushCnt = Integer.valueOf(afterTechniqueVerifyGrabCntStr);
            pushCnt++;
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "after_technique_verify_grab_cnt", String.valueOf(pushCnt));
        } else {
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "after_technique_verify_grab_cnt", "1");
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "master_id", String.valueOf(masterId));

        }

    }

    /**
     * 更新  当日有效抢单量（合作经营）
     * @param masterId
     * @param day
     */
    public void subCooperationBusinessMasterGrabCntDaily(Long masterId, String day) {
        String rowKey = masterId + "_" + day;
        String grabCooperationCntStr = hBaseClient.querySingle("master_daily_data_cnt", rowKey, "grab_cooperation_cnt");
        if (StringUtils.isNotBlank(grabCooperationCntStr)) {
            Integer pushCnt = Integer.valueOf(grabCooperationCntStr);
            if (pushCnt > 0) {
                pushCnt--;
            }
            hBaseClient.putData("master_daily_data_cnt", rowKey, "0", "grab_cooperation_cnt", String.valueOf(pushCnt));
        }

    }


	/**
	 * 记录合作经营指派时间
	 * @param masterId
	 * @param masterId
	 */
	public void saveCooperationBusinessGrabTime(Long masterId, Date grabTime) {
		String rowKey = String.valueOf(masterId);
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "master_id", String.valueOf(masterId));
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "cooperation_business_last_grab_time", DateUtils.formatDate(grabTime,"yyyy-MM-dd HH:mm:ss"));
	}


	/**
	 * 记录合作经营指派时间
	 * @param masterId
	 * @param masterId
	 */
	public void saveCooperationBusinessAppointTime(Long masterId, Date appointTime) {
		String rowKey = String.valueOf(masterId);
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "master_id", String.valueOf(masterId));
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "cooperation_business_last_appoint_time", DateUtils.formatDate(appointTime,"yyyy-MM-dd HH:mm:ss"));
	}



	/**
	 * 记录全时师傅最近指派时间
	 * @param masterId
	 * @param masterId
	 */
	public void saveFullTimeMasterAppointTime(Long masterId, Date appointTime) {
		String rowKey = String.valueOf(masterId);
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "master_id", String.valueOf(masterId));
		hBaseClient.putData("mst_buss_extra", rowKey, "0", "full_time_master_last_appoint_time", DateUtils.formatDate(appointTime,"yyyy-MM-dd HH:mm:ss"));
	}





}