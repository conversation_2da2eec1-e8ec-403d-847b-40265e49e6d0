package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMaster;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMasterList;
import com.wanshifu.master.order.push.domain.dto.PushRuleConfig;
import com.wanshifu.master.order.push.domain.dto.WheelRoundsPushMessage;
import com.wanshifu.master.order.push.domain.po.PushRule;

import java.util.List;

public interface DynamicRoundsPushService {

    DynamicRoundsMasterList getPushMasterList(List<PushMaster> pushMasterList, PushRuleConfig pushRuleConfig, Integer appointType);


    Integer wheelRoundsPush(WheelRoundsPushMessage message);




    }
