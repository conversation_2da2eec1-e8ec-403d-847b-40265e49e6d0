package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.PushOrderList;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.mapper.PushOrderListMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Repository
public class PushOrderListRepository extends BaseRepository<PushOrderList> {

    @Resource
    private PushOrderListMapper pushOrderListMapper;


    /**
     * 查询未推单列表
     * @param rqt
     * @return
     */
    public List<PushOrderList> selectNoPushedMasterOrderList(NoPushedMasterOrderListRqt rqt){
        return pushOrderListMapper.selectNoPushedMasterOrderList(rqt);
    }


}