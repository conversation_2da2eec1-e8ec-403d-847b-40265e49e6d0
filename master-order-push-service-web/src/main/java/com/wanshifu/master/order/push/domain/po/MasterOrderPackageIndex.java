package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name="t_master_order_package")
public class MasterOrderPackageIndex {

    @Column(name = "package_config_id")
    private String packageConfigId;

    /**
     * 師傅id
     */
    private String masterId;

    /**
     * 订单包id
     */
    @Id
    @Column(name = "package_id")
    private String packageId;

    private Integer remainAppointOrderNumber;

    private Integer remainPushOrderNumber;

    private Long masterPackageOrderPushNumberDaily;

    private String pushCountTag;

    private String statementStatus;

    private String hasRecycle;

    private String hasPausePush;
}
