package com.wanshifu.master.order.push.annotation;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import org.hibernate.validator.constraints.NotEmpty;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/10 15:55
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FeishuNotice {

    /**
     * 方法类型名(insert、update、delete、enable)
     */
    @NotEmpty(message = "飞书通知告警注解方法类型不能为空")
    @ValueIn(value = "insert,update,delete,enable", message = "飞书通知告警注解方法类型值非法")
    String methodTypeName();

    /**
     * 一级菜单名称
     */
    @NotEmpty(message = "飞书通知告警注解一级菜单名称不能为空")
    String level1MenuName() default "";

    /**
     * 二级菜单名称
     */
    @NotEmpty(message = "飞书通知告警注解二级菜单名称不能为空")
    String level2MenuName() default "";

    /**
     * 实体表名
     * update、enable、delete时必填
     */
    String tableName() default "";

    /**
     * Mapper类
     * update、enable、delete时必填
     */
    Class<?> mapperClass() default void.class;

    /**
     * 持久层spring的beanName
     * (保留此参数作为备选方案)
     * update、enable、delete时必填
     */
    String mapperBeanName() default "";

    /**
     * 入参中业务线id字段名
     * insert时必填,特殊配置无该字段可不填
     */
    String businessLineIdFieldName() default "";

    /**
     * 入参中规则配置名称字段名
     * insert时必填
     */
    String configNameFieldName() default "";

    /**
     * 创建人账号id字段名
     * insert时必填
     * @return
     */
    String createAccountIdFieldName() default "";

    /**
     * 修改人账号id字段名
     * update、enable、delete时必填
     * @return
     */
    String updateAccountIdFieldName() default "";

    /**
     * 入参中主键id字段名
     * update、enable、delete时必填
     */
    String primaryKeyFieldName() default "";

    /**
     * 实体类中业务线id字段名
     * update、enable、delete时必填，特殊配置无该字段可不填
     */
    String businessLineIdFieldNameFromEntity() default "";

    /**
     * 实体类中规则配置名称字段名
     * update、enable、delete时必填
     */
    String configNameFieldNameFromEntity() default "";
}
