package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import com.wanshifu.master.order.push.domain.po.MasterUser;
import com.wanshifu.master.order.push.mapper.MasterDailyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterUserRepository extends BaseRepository<MasterUser> {



    public List<MasterUser> selectByMasterIdAndUserId(String userId, Set<String> masterId){
        Example example = new Example(MasterUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andIn("masterId",masterId);
        return this.selectByExample(example);

    }



}