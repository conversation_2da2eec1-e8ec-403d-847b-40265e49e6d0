package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.ListRqt;
import com.wanshifu.master.order.push.mapper.NewMasterOrderDistributeStrategyMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class NewMasterOrderDistributeStrategyRepository extends BaseRepository<NewMasterOrderDistributeStrategy> {

    @Resource
    private NewMasterOrderDistributeStrategyMapper newMasterOrderDistributeStrategyMapper;



    public Integer insert(Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String distributeType,String categoryIds,String openCityMode,String cityIds,
                          String distributeStrategy,String distributeStrategyExpression,Long createAccountId,Long updateAccountId){
        NewMasterOrderDistributeStrategy  orderDistributeStrategy = new NewMasterOrderDistributeStrategy();
        orderDistributeStrategy.setBusinessLineId(businessLineId);
        orderDistributeStrategy.setStrategyName(strategyName);
        orderDistributeStrategy.setOrderFrom(orderFrom);
        orderDistributeStrategy.setStrategyDesc(strategyDesc);
        orderDistributeStrategy.setDistributeType(distributeType);
        orderDistributeStrategy.setCategoryIds(categoryIds);
        orderDistributeStrategy.setOpenCityMode(openCityMode);
        orderDistributeStrategy.setCityIds(cityIds);
        orderDistributeStrategy.setDistributeStrategy(distributeStrategy);
        orderDistributeStrategy.setDistributeStrategyExpression(distributeStrategyExpression);
        orderDistributeStrategy.setCreateAccountId(createAccountId);
        orderDistributeStrategy.setUpdateAccountId(updateAccountId);
        newMasterOrderDistributeStrategyMapper.insertSelective(orderDistributeStrategy);
        return orderDistributeStrategy.getStrategyId();
    }


    public int update(Integer strategyId,Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String distributeType,String categoryIds,
                      String openCityMode,String cityIds,String distributeStrategy,String distributeStrategyExpression,Long updateAccountId){
        NewMasterOrderDistributeStrategy orderDistributeStrategy = new NewMasterOrderDistributeStrategy();
        orderDistributeStrategy.setStrategyId(strategyId);
        orderDistributeStrategy.setBusinessLineId(businessLineId);
        orderDistributeStrategy.setStrategyName(strategyName);
        orderDistributeStrategy.setOrderFrom(orderFrom);
        orderDistributeStrategy.setStrategyDesc(strategyDesc);
        orderDistributeStrategy.setDistributeType(distributeType);
        orderDistributeStrategy.setCategoryIds(categoryIds);
        orderDistributeStrategy.setOpenCityMode(openCityMode);
        orderDistributeStrategy.setCityIds(cityIds);
        orderDistributeStrategy.setDistributeStrategy(distributeStrategy);
        orderDistributeStrategy.setDistributeStrategyExpression(distributeStrategyExpression);
        orderDistributeStrategy.setUpdateAccountId(updateAccountId);
        return newMasterOrderDistributeStrategyMapper.updateByPrimaryKeySelective(orderDistributeStrategy);
    }


//    public NewMasterOrderDistributeStrategy selectByCityAndCategoryId(@Param("distributeType")String distributeType, @Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Long strategyId, @Param("businessLineId") Long businessLineId){
//        return newMasterOrderDistributeStrategyMapper.selectByCityAndCategoryId(distributeType,cityIdList,categoryIdList,strategyId,businessLineId);
//    }


    public NewMasterOrderDistributeStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Long businessLineId, Integer strategyId) {
        Example example = new Example(OrderDistributeStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }




    public NewMasterOrderDistributeStrategy selectByCityAndCategoryId(@Param("distributeType")String distributeType,@Param("cityIdList") List<String> cityIdList,
                                                                      @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Long strategyId,
                                                                      @Param("businessLineId") Long businessLineId){
        return newMasterOrderDistributeStrategyMapper.selectByCityAndCategoryId(distributeType,cityIdList,categoryIdList,strategyId,businessLineId);
    }



    public List<NewMasterOrderDistributeStrategy> selectList(ListRqt rqt){
        return newMasterOrderDistributeStrategyMapper.selectList(rqt);
    }


    public NewMasterOrderDistributeStrategy selectByCategoryIdAndCityId(Integer businessLineId, String distributeType,Long categoryId, String cityId){
        return newMasterOrderDistributeStrategyMapper.selectByCategoryIdAndCityId(businessLineId,distributeType,categoryId,cityId);
    }


}