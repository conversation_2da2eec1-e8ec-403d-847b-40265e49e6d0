package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.mapper.SpecialGroupStrategyMapper;
import com.wanshifu.master.order.push.repository.SpecialGroupStrategyRepository;
import com.wanshifu.master.order.push.service.SpecialGroupStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 特殊人群策略服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SpecialGroupStrategyServiceImpl implements SpecialGroupStrategyService {

    private final SpecialGroupStrategyRepository specialGroupStrategyRepository;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "特殊人群策略",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {
        // 校验策略名称是否重复
        validateCreateParam(rqt);

        return specialGroupStrategyRepository.insert(
                rqt.getStrategyName(),
                rqt.getStrategyDesc(),
                JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {}),
                rqt.getRegionLevel(),
                rqt.getCityIds(),
                rqt.getPushGroups(),
                rqt.getServeModels(),
                rqt.getDelayMinutes(),
                rqt.getFilterGroups(),
                rqt.getCreateAccountId()
        );
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            updateAccountIdFieldName = "updateAccountId",
            configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt) {
        validateUpdateParam(rqt);
        
        return specialGroupStrategyRepository.update(
                rqt.getStrategyId(),
                rqt.getStrategyName(),
                rqt.getStrategyDesc(),
                JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {}),
                rqt.getRegionLevel(),
                rqt.getCityIds(),
                rqt.getPushGroups(),
                rqt.getServeModels(),
                rqt.getDelayMinutes(),
                rqt.getFilterGroups(),
                rqt.getUpdateAccountId()
        );
    }

    @Override
    public SpecialGroupStrategy detail(DetailRqt rqt) {
        return specialGroupStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
    }

    @Override
    public SimplePageInfo<SpecialGroupStrategy> list(ListRqt rqt) {
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        String strategyName = rqt.getStrategyName();
        Long cityId = rqt.getCityId();
        String serveIds = rqt.getServeIds();
        Integer strategyStatus = rqt.getStrategyStatus();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<SpecialGroupStrategy> strategyList = specialGroupStrategyRepository.selectList(
                strategyName, cityId, serveIds, rqt.getCreateStartTime(), rqt.getCreateEndTime(), strategyStatus);

        SimplePageInfo<SpecialGroupStrategy> pageInfo = new SimplePageInfo<>();
        pageInfo.setPages(startPage.getPages());
        pageInfo.setPageNum(startPage.getPageNum());
        pageInfo.setTotal(startPage.getTotal());
        pageInfo.setPageSize(startPage.getPageSize());
        pageInfo.setList(strategyList);
        return pageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "调度管理", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        Integer strategyStatus = rqt.getStrategyStatus();
        return specialGroupStrategyRepository.updateStatus(strategyId, strategyStatus);
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt) {
        Long strategyId = rqt.getStrategyId();
        if (strategyId == null || strategyId <= 0) {
            return 0;
        }
        return specialGroupStrategyRepository.deleteByPrimaryKey(strategyId);
    }


    private void validateCreateParam(CreateRqt rqt) {
        checkStrategyName(rqt.getStrategyName(), null);
        checkCityIds(rqt);
        fixCityIds(rqt);
    }


    private void validateUpdateParam(UpdateRqt rqt) {
        checkStrategyName(rqt.getStrategyName(), rqt.getStrategyId());
        checkCityIds(rqt);
        fixCityIds(rqt);
    }


    /**
     * 校验策略名称是否重复
     */
    private void checkStrategyName(String strategyName, Long strategyId) {
        SpecialGroupStrategy strategy = specialGroupStrategyRepository.selectByStrategyName(strategyName, strategyId);
        Assert.isNull(strategy, "已存在相同策略名称!");
    }

    private void checkCityIds(CreateRqt rqt) {
        String regionLevel = rqt.getRegionLevel();
        if ("city".equals(regionLevel) && StringUtils.isBlank(rqt.getCityIds())) {
            throw new IllegalArgumentException("城市范围必须选择城市!");
        }
    }

    public void fixCityIds(CreateRqt rqt) {
        String regionLevel = rqt.getRegionLevel();
        if (!"city".equals(regionLevel)) {
            rqt.setCityIds("");
        }
    }
}
