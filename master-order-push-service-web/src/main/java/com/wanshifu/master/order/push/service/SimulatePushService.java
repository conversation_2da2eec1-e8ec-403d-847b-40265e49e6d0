package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import com.wanshifu.master.order.push.domain.resp.simulatePush.GetSimulatePushResp;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.*;

import java.util.List;

public interface SimulatePushService {

    List<StrategyCombinationSimulate> getStrategyCombinationSimulate(GetStrategyCombinationSimulateRqt rqt);

    StrategyCombinationSimulate getCombinationLastSimulate(GetCombinationLastSimulateRqt rqt);

    Long addCombinationSimulate(AddCombinationSimulateRqt rqt);

    Integer updateOrderNum(UpdateOrderNumRqt rqt);

    Integer updateSimulateFinish(UpdateSimulateFinishRqt rqt);

    GetSimulatePushResp getSimulatePush(GetSimulatePushRqt rqt);

    Integer addSimulatedOrderNum(AddSimulateOrderNumRqt rqt);

    Long addSimulatePush(AddSimulatePushRqt rqt);

    Integer batchAddSimulatePushDetail(BatchAddSimulatePushDetailRqt rqt);

}
