package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27 15:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributeScoreRuleExpressionDto {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;

    /**
     * 匹配项分值表达式
     */
    private String scoreRuleExpression;

    /**
     * 匹配项分值表达式参数
     */
    private String scoreRuleParams;
}
