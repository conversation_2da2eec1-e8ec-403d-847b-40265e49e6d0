package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.api.resp.IopAccountResp;
import com.wanshifu.master.order.push.api.rqt.IopGetInfoListByAccountIdReq;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:05
 */
@FeignClient(value = "iop-account-service-api",
        url = "${wanshifu.iop-account-service.url}",
        configuration = {DefaultEncoder.class, DefaultErrorDecode.class})
public interface IopAccountApi {

    /**
     * 根据accountId获取用户信息
     *
     * @param getInfoListByAccountIdsReq
     * @return
     */
    @PostMapping("/account/getUserInfoByAccountId")
    IopAccountResp<IopAccountResp.IopAccount> getUserInfoByAccountId(@RequestBody IopGetInfoListByAccountIdReq getInfoListByAccountIdsReq);
}
