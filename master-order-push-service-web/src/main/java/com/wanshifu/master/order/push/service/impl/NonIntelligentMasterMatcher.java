package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.information.api.MasterFilterApi;
import com.wanshifu.master.information.domain.api.request.masterFilter.BaseFiltrateRqt;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 非智能推单匹配师傅（开发环境）
 * <AUTHOR>
 */
@Slf4j
@Component("non_intelligent")
public class NonIntelligentMasterMatcher extends AbstractOrderMasterMatcher{

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private MasterFilterApi masterFilterApi;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    public boolean checkPreCondition(OrderDetailData orderDetailData){
        return true;
    }


    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){

        BaseFiltrateRqt baseFiltrateRqt = new BaseFiltrateRqt();
        baseFiltrateRqt.setSecondDivisionId(orderDetailData.getSecondDivisionId());
        List<Address> subListByDivisionId = addressCommon.getSubListByDivisionId(orderDetailData.getSecondDivisionId());
        List<Long> thirdDivisionIdList = subListByDivisionId.stream().map(Address::getDivisionId).collect(Collectors.toList());
        baseFiltrateRqt.setThirdDivisionId(orderDetailData.getThirdDivisionId());
        baseFiltrateRqt.setThirdDivisionIdList(thirdDivisionIdList);
        baseFiltrateRqt.setTechnologyIds(orderDetailData.getOrderTechniques());
        List<Long> masterIds = masterFilterApi.baseFiltrate(baseFiltrateRqt);
        Set<String> masterIdSet = CollectionUtils.isNotEmpty(masterIds) ? masterIds.stream().map(masterId -> String.valueOf(masterId)).collect(Collectors.toSet()) : Collections.emptySet();
        MatchMasterResult matchMasterResult = new MatchMasterResult(masterIdSet);
        return matchMasterResult;
    }


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){

    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        try{
            if(matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
                return false;
            }
            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp), PushMode.NON_INTELLIGENT.code);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.NORMAL.code);
            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
            commonFeature.put(FieldConstant.PUSH_SCENARIO_TYPE,"direct_push");
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
            pushControllerFacade.directPush(orderDetailData,orderVersion,matchMasterResult.getMasterIdSet(),commonFeature);
            return true;
        }catch(Exception e){
            log.error(String.format("执行非智能推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;
    }


}
