package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import com.wanshifu.master.order.push.repository.CooperationBusinessMasterMatchLogRepository;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/12 11:21
 */
@Service
@Slf4j
public class CooperationBusinessOrderDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private PushRecordService pushRecordService;

    @Resource
    private CooperationBusinessMasterMatchLogRepository cooperationBusinessMasterMatchLogRepository;

    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage) {
        Long orderId = orderDistributeResultMessage.getOrderId();
        log.info("cooperationBusinessOrder distributeResultNotice,orderId:{},message:{}", orderId, JSONUtil.toJsonStr(orderDistributeResultMessage));


        int distributeResult;

        List<Long> successMasterList = orderDistributeResultMessage.getSuccessMasterList();
        if(CollectionUtils.isNotEmpty(successMasterList)){
            distributeResult = 1;

            Long masterId = successMasterList.get(0);
            //抢单成功，更新师傅 当日有效抢单量（合作经营） 指标
            pushRecordService.increaseCooperationBusinessMasterGrabCntDaily(masterId, DateFormatterUtil.getNow());

            this.updateCooperationBusinessMasterMatchLog(orderId, masterId, 1, "");


            List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
            List<JSONObject> failMasterVos = orderDistributeResultMessage.getMasterFailVos();
            if (CollectionUtil.isNotEmpty(failMasterList) && CollectionUtil.isNotEmpty(failMasterVos)) {
                Map<Long, JSONObject> failMasterMap = failMasterVos.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getLong("masterId"), jsonObject -> jsonObject));
                for (Long failMasterId : failMasterList) {
                    this.updateCooperationBusinessMasterMatchLog(orderId, failMasterId, 0, failMasterMap.getOrDefault(failMasterId, new JSONObject()).toJSONString());
                }

            }


            successMasterList.forEach(currentMasterId -> {
                pushRecordService.saveCooperationBusinessGrabTime(currentMasterId,new Date());
            });

        }else{
            distributeResult = 0;
            List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
            List<JSONObject> failMasterVos = orderDistributeResultMessage.getMasterFailVos();
            if (CollectionUtil.isNotEmpty(failMasterList) && CollectionUtil.isNotEmpty(failMasterVos)) {
                Map<Long, JSONObject> failMasterMap = failMasterVos.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getLong("masterId"), jsonObject -> jsonObject));
                for (Long masterId : failMasterList) {
                    this.updateCooperationBusinessMasterMatchLog(orderId, masterId, 0, failMasterMap.getOrDefault(masterId, new JSONObject()).toJSONString());
                }

            }

        }


        if(distributeResult == 0){
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderId);
            pushQueueService.sendDelayPushMessage(100L,JSON.toJSONString(rqt));

            return 0;
        }

        return 1;
    }

    @Override
    public void afterPropertiesSet() {
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_COOPERATION_BUSINESS.getCode(), this);
    }

    private void updateCooperationBusinessMasterMatchLog(Long orderId, Long masterId, Integer isAutoGrabSuccess, String autoGrabFailReason) {
        Condition condition = new Condition(CooperationBusinessMasterMatchLog.class);

        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);

        CooperationBusinessMasterMatchLog log = new CooperationBusinessMasterMatchLog();
        log.setOrderId(orderId);
        log.setMasterId(masterId);
        log.setIsAutoGrabSuccess(isAutoGrabSuccess);
        log.setAutoGrabFailReason(autoGrabFailReason);
        log.setUpdateTime(new Date());
        cooperationBusinessMasterMatchLogRepository.updateByConditionSelective(log, condition);
    }
}
