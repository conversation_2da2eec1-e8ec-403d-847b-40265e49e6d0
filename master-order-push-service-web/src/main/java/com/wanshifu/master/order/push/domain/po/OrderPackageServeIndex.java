package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import lombok.Data;

@Data
@Document(indexAlias = "order_package_serve", type = "order_package_serve")
public class OrderPackageServeIndex {

    private Long packageId;

    private Long packageConfigId;

    private Long maxPushNumberDaily;

    private Long createTime;

    private Long platformAutoMode;

    private String attributeKey;

    private String attributeValueMin;

    private String attributeValueMax;

}
