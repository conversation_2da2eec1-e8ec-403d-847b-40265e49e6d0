package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.MasterAutoReceiverRqt;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 全时师傅订单调度
 */
@Service
public class FullTimeMasterOrderDistributeServiceImpl implements OrderDistributeService, InitializingBean {


    @Resource
    private RocketMqSendService rocketMqSendService;


    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;




    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice){

        Long orderId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getOrderId();
        Long globalOrderTraceId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getGlobalOrderTraceId();
        OrderGrab orderGrab = orderPushedResultNotice.getOrderBaseComposite().getOrderGrab();
        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        rqt.setOrderId(orderId);
        rqt.setGlobalOrderTraceId(globalOrderTraceId);
        if(AppointType.DEFINITE_PRICE.value.equals(orderGrab.getAppointType())){
            rqt.setAppointType(AppointType.DEFINITE_PRICE.value);
        }
        rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_FULL_TIME_MASTER.getCode());
        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        orderPushedResultNotice.getMasterInfoList().forEach(masterInfo -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(masterInfo.getMasterId());
            masterList.add(masterPrice);
        });
        rqt.setMasterList(masterList);
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),1000L);
        return 1;

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeContext.register(PushMode.FULL_TIME_MASTER_DISPATCH.code, this);
    }

}
