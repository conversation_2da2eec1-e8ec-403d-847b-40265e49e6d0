package com.wanshifu.master.order.push.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import com.wanshifu.master.order.push.domain.rqt.common.*;
import com.wanshifu.master.order.push.repository.MasterQuotaRepository;
import com.wanshifu.master.order.push.repository.MasterQuotaValueRepository;
import com.wanshifu.master.order.push.repository.ScoreItemRepository;
import com.wanshifu.master.order.push.repository.ScoreItemValueRepository;
import com.wanshifu.master.order.push.service.CommonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private MasterQuotaRepository masterQuotaRepository;


    @Resource
    private MasterQuotaValueRepository masterQuotaValueRepository;

    @Resource
    private ScoreItemRepository scoreItemRepository;

    @Resource
    private ScoreItemValueRepository scoreItemValueRepository;

    @Override
    public SimplePageInfo<MasterQuota> masterQuota(MasterQuotaRqt rqt){
        Page<?> startPage = PageHelper.startPage(rqt.pageNum, rqt.pageSize);
        List<MasterQuota> masterQuotas = masterQuotaRepository.selectQuotaList(rqt.getQuotaName(), rqt.getBusinessLineId());
        SimplePageInfo<MasterQuota> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(masterQuotas);
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPages(startPage.getPages());
        return listRespSimplePageInfo;
    }


    @Override
    public List<ScoreItem> masterItemList(MasterItemRqt rqt){
       return scoreItemRepository.selectByItemName(rqt.getItemName());
    }


    @Override
    public List<MasterQuotaValue> getMasterQuotaValue(GetMasterQuotaValueRqt rqt){
        return masterQuotaValueRepository.selectByMasterQuotaIds(rqt.getMasterQuotaIdList());
    }

    @Override
    public   List<ScoreItemValue> getScoreItemValue(GetScoreItemValueRqt rqt){
        return scoreItemValueRepository.selectByScoreItemIds(rqt.getScoreItemIdList());
    }

    @Override
    public List<ScoreItem> getScoreItemByCodes(GetScoreItemByCodesRqt rqt){
        return scoreItemRepository.selectByItemCodes(rqt.getItemCodes());
    }


    @Override
    public List<MasterQuota> getMasterQuotaByCodes(GetMasterQuotaByCodesRqt rqt){
        return masterQuotaRepository.selectByCodes(rqt.getQuotaCodes(), rqt.getBusinessLineId());
    }


}
