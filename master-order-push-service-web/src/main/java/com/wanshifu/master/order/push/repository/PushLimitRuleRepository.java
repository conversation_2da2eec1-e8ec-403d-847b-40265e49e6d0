package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.mapper.AgentDistributeStrategyMapper;
import com.wanshifu.master.order.push.mapper.PushLimitRuleMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/08 14:36
 */
@Repository
public class PushLimitRuleRepository extends BaseRepository<PushLimitRule> {

    @Resource
    private PushLimitRuleMapper pushLimitRuleMapper;


    public int insertPushLimitRule(Integer businessLineId,String ruleName,String ruleDesc,String cityIds,String crowdType,String crowdLabel,String crowdGroup,
                                   String crowdGroupExpression,
                                   String limitRange,
                              String limitServeRule,String serveRuleExpression,String exclusiveRule,String limitRule,
                              Integer decreaseDays,Integer decreasePercent,Integer fixedDecreasePercent,Long createAccountId){
        PushLimitRule pushLimitRule = new PushLimitRule();
        pushLimitRule.setBusinessLineId(businessLineId);
        pushLimitRule.setRuleName(ruleName);
        pushLimitRule.setRuleDesc(ruleDesc);
        pushLimitRule.setCityIds(cityIds);
        pushLimitRule.setCrowdType(crowdType);
        pushLimitRule.setCrowdLabel(crowdLabel);
        pushLimitRule.setCrowdGroup(crowdGroup);
        pushLimitRule.setCrowdGroupExpression(crowdGroupExpression);
        pushLimitRule.setServeRuleExpression(serveRuleExpression);
        pushLimitRule.setLimitRange(limitRange);
        pushLimitRule.setLimitServeRule(limitServeRule);
        pushLimitRule.setExclusiveRule(exclusiveRule);
        pushLimitRule.setLimitRule(limitRule);
        pushLimitRule.setDecreaseDays(decreaseDays);
        pushLimitRule.setDecreasePercent(decreasePercent);
        pushLimitRule.setFixedDecreasePercent(fixedDecreasePercent);
        pushLimitRule.setCreateAccountId(createAccountId);
        pushLimitRule.setUpdateAccountId(createAccountId);
        return this.insertSelective(pushLimitRule);
    }


    public int updatePushLimitRule(Integer ruleId,Integer businessLineId,String ruleName,String ruleDesc,String cityIds,String crowdType,String crowdLabel,String crowdGroup,String crowdGroupExpression,
                                   String limitRange,
                                   String limitServeRule,String serveRuleExpression,String exclusiveRule,String limitRule,
                                   Integer decreaseDays,Integer decreasePercent,Integer fixedDecreasePercent,Long updateAccountId){
        PushLimitRule pushLimitRule = new PushLimitRule();
        pushLimitRule.setBusinessLineId(businessLineId);
        pushLimitRule.setRuleId(ruleId);
        pushLimitRule.setRuleName(ruleName);
        pushLimitRule.setRuleDesc(ruleDesc);
        pushLimitRule.setCityIds(cityIds);
        pushLimitRule.setCrowdType(crowdType);
        pushLimitRule.setCrowdLabel(crowdLabel);
        pushLimitRule.setCrowdGroup(crowdGroup);
        pushLimitRule.setCrowdGroupExpression(crowdGroupExpression);
        pushLimitRule.setServeRuleExpression(serveRuleExpression);
        pushLimitRule.setLimitRange(limitRange);
        pushLimitRule.setLimitServeRule(limitServeRule);
        pushLimitRule.setExclusiveRule(exclusiveRule);
        pushLimitRule.setLimitRule(limitRule);
        pushLimitRule.setDecreaseDays(decreaseDays);
        pushLimitRule.setDecreasePercent(decreasePercent);
        pushLimitRule.setFixedDecreasePercent(fixedDecreasePercent);
        pushLimitRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(pushLimitRule);
    }


    public PushLimitRule selectByRuleName(String ruleName,Integer ruleId) {
        Example example = new Example(PushRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleName", ruleName)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (ruleId != null) {
            criteria.andNotEqualTo("ruleId", ruleId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }



    public List<PushLimitRule> selectList(String cityId,Integer businessLineId,String ruleName, Date createStartTime, Date createEndTime,Integer ruleStatus){
        return pushLimitRuleMapper.selectList(cityId,businessLineId,ruleName,createStartTime,createEndTime,ruleStatus);
    }

    public int softDeleteByRuleId(Integer ruleId) {
        PushLimitRule pushLimitRule = new PushLimitRule();
        pushLimitRule.setRuleId(ruleId);
        pushLimitRule.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(pushLimitRule);
    }



    public int updateStatus(Integer ruleId, Integer ruleStatus,Long updateAccountId) {
        PushLimitRule pushLimitRule = new PushLimitRule();
        pushLimitRule.setRuleId(ruleId);
        pushLimitRule.setRuleStatus(ruleStatus);
        pushLimitRule.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(pushLimitRule);
    }



    public PushLimitRule selectByCityIdAndBusinessLineId(Integer ruleId,Integer businessLineId, List<String> cityIdList,String crowdLabel){
        return CollectionUtils.getFirstSafety(pushLimitRuleMapper.selectByCityIdAndBusinessLineId(ruleId,businessLineId,cityIdList,crowdLabel));
    }


}
