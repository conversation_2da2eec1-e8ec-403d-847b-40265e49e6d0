package com.wanshifu.master.order.push.service.impl;


import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.repository.AbstractElasticSearchRepository;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

@Repository
public class AgreementMasterEsRespository extends AbstractElasticSearchRepository<AgreementMaster> {


    public List<AgreementMaster> searchMainMasterByMasterId(Long masterId){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId",masterId));
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterCategory", Collections.singleton(1L)));
        EsResponse<AgreementMaster> esResponse = this.search(boolQueryBuilder);
        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList();
        }
        return null;
    }


    public List<AgreementMaster> searchByIdSet(Set<String> agreementMasterIdList){


        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("id", agreementMasterIdList));

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = this.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }
        return agreementMasterList;
    }

}
