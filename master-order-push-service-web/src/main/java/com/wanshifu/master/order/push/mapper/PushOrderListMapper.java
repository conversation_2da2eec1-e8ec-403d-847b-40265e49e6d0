package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.PushOrderList;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;

import java.util.List;

public interface PushOrderListMapper extends IBaseCommMapper<PushOrderList> {

    /**
     * 查询未推单列表
     * @param rqt
     * @return
     */
    List<PushOrderList> selectNoPushedMasterOrderList(NoPushedMasterOrderListRqt rqt);

}
