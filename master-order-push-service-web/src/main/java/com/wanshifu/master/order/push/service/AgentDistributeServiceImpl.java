package com.wanshifu.master.order.push.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.master.information.domain.api.response.common.BatchGetMasterBaseInfoResp;
import com.wanshifu.master.order.push.api.MasterOrderApi;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.RedisKeyConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AgentPushType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.rqt.GrabOrderValidRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.impl.AgentMasterMatcher;
import com.wanshifu.master.order.push.service.impl.OrderDataBuilder;
import com.wanshifu.master.order.push.service.impl.OrderDistributeContext;
import com.wanshifu.master.order.push.service.impl.Tools;
import com.wanshifu.master.order.push.util.DistanceUtil;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hbase.thirdparty.com.google.common.collect.Sets;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 代理商派单调度
 * @date 2025/8/7 17:56
 */
@Service
@Slf4j
public class AgentDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private Tools tools;

    @Resource
    private MasterOrderApi masterOrderApi;

    @Resource
    private MasterCommon masterCommon;

    @Resource
    private AgentMasterMatcher agentMasterMatcher;

    @Resource
    private OrderDataBuilder orderDataBuilder;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Autowired
    private RedisHelper redisHelper;

    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice) {
        log.info("agentDistribute,request:{}", JSONUtil.toJsonStr(orderPushedResultNotice));

        String agentPushType = orderPushedResultNotice.getAgentPushType();
        if (Strings.isNullOrEmpty(agentPushType)) {
            return 0;
        }

        OrderBaseComposite orderBaseComposite = orderPushedResultNotice.getOrderBaseComposite();
        OrderBase orderBase = orderBaseComposite.getOrderBase();
        Integer appointType = orderBaseComposite.getOrderGrab().getAppointType();
        Long orderId = orderBase.getOrderId();

        //避免推单派单死循环的兜底逻辑
        String distributeTimes = redisHelper.get(RedisKeyConstant.AGENT_PUSH_CONFIRM_SAFE_KEY.concat(orderId.toString()));
        if (Strings.isNullOrEmpty(distributeTimes)) {
            redisHelper.set(RedisKeyConstant.AGENT_PUSH_CONFIRM_SAFE_KEY.concat(orderId.toString()), "1", 2 * 24 * 60 * 60);
        } else {
            int times = Integer.parseInt(distributeTimes);
            if (times > 3) {
                log.error("agentDistribute confirmSafe error,orderId:{},times:{}", orderId, times);
                return 0;
            }
            redisHelper.set(RedisKeyConstant.AGENT_PUSH_CONFIRM_SAFE_KEY.concat(orderId.toString()), String.valueOf(times + 1), 2 * 24 * 60 * 60);
        }


        if (AgentPushType.PUSH.getType().equals(agentPushType)) {
            //定向推送
            Integer agentPushNoHiredRePushTime = orderPushedResultNotice.getAgentPushNoHiredRePushTime();
            log.info("agentDistribute byPush sendNormal afterPush,orderId:{},agentPushType:{},agentPushNoHiredRePushTime:{},pushTime:{}",
                    orderId, agentPushType, agentPushNoHiredRePushTime, orderPushedResultNotice.getPushTime());
            if (Objects.nonNull(agentPushNoHiredRePushTime)) {
                pushQueueService.sendAgentPushMessage(orderId, appointType, orderPushedResultNotice.getPushTime(), agentPushNoHiredRePushTime * 60 * 1000L);
            }
            return 1;

        } else {
            //直接指派 or 直接指派优先
            log.info("agentDistribute byDirectAppointOrPriority,orderId:{},agentPushType:{},agentMasterList:{},agentAfterDirectAppointPushInfo:{}",
                    orderId, agentPushType, orderPushedResultNotice.getAgentMasterList(), orderPushedResultNotice.getAgentAfterDirectAppointPushInfo());
            if (Strings.isNullOrEmpty(orderPushedResultNotice.getAgentMasterList())) {
                return 0;
            }
            List<AgentPushMaster> agentPushMasterList = JSON.parseArray(orderPushedResultNotice.getAgentMasterList(), AgentPushMaster.class);
            if (CollectionUtil.isEmpty(agentPushMasterList)) {
                return 0;
            }

            OrderExtraData orderExtraData = orderBaseComposite.getOrderExtraData();
            BigDecimal orderLatitude = Optional.ofNullable(orderExtraData).map(OrderExtraData::getBuyerAddressLatitude).orElse(BigDecimal.ZERO);
            BigDecimal orderLongitude = Optional.ofNullable(orderExtraData).map(OrderExtraData::getBuyerAddressLongitude).orElse(BigDecimal.ZERO);


            List<AgentPushMaster> tocMasterList = agentPushMasterList.stream().filter(agentPushMaster -> "toc".equals(agentPushMaster.getAgentType())).collect(Collectors.toList());

            List<AgentPushMaster> tobGroupMasterList = agentPushMasterList.stream().filter(agentPushMaster -> "tobGroup".equals(agentPushMaster.getAgentType())).collect(Collectors.toList());

            if (AgentPushType.DIRECT_APPOINT.getType().equals(agentPushType)) {
                log.info("agentDistribute byDirectAppoint,orderId:{},agentPushType:{},tocMasterList:{},tobGroupMasterList:{}",
                        orderId, agentPushType, tocMasterList, tobGroupMasterList);
                //直接指派
                boolean isDirectAppoint = doDirectAppoint(orderId, tocMasterList, tobGroupMasterList, orderLatitude, orderLongitude);
                if (isDirectAppoint){
                    log.info("agentDistribute byDirectAppoint success,orderId:{},agentPushType:{}", orderId, agentPushType);
                    return 1;
                }
                log.info("agentDistribute byDirectAppoint fail,than do nextPriorityPush!orderId:{},agentPushType:{}", orderId, agentPushType);
                sendNextPriorityPush(orderId);
                return 1;



            } else {
                log.info("agentDistribute byDirectAppointPriority,orderId:{},agentPushType:{},tocMasterList:{},tobGroupMasterList:{}",
                        orderId, agentPushType, tocMasterList, tobGroupMasterList);
                //直接指派优先
                boolean isDirectAppoint = doDirectAppoint(orderId, tocMasterList, tobGroupMasterList, orderLatitude, orderLongitude);
                if (isDirectAppoint) {
                    log.info("agentDistribute byDirectAppointPriority do directAppoint success,orderId:{},agentPushType:{}", orderId, agentPushType);
                    return 1;
                } else {
                    //走定向推送
                    log.info("agentDistribute byDirectAppointPriority do byPush,orderId:{},agentPushType:{}", orderId, agentPushType);
                    List<AgentAfterDirectAppointPushInfo> agentAfterDirectAppointPushInfo = orderPushedResultNotice.getAgentAfterDirectAppointPushInfo();
                    if (CollectionUtil.isEmpty(agentAfterDirectAppointPushInfo)) {
                        log.error("agentDistribute byDirectAppointPriority do byPush exception! agentAfterDirectAppointPushInfo is empty!,orderId:{},agentPushType:{}", orderId, agentPushType);
                        return 0;
                    }

                    Integer agentPushNoHiredRePushTime = agentAfterDirectAppointPushInfo.get(0).getAgentPushNoHiredRePushTime();
                    Integer agentFirstViewNoHiredRePushTime = agentAfterDirectAppointPushInfo.get(0).getAgentFirstViewNoHiredRePushTime();

                    OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
                    rqt.setMasterOrderId(orderId);
                    OrderDetailData orderDetailData = orderDataBuilder.build(rqt);
                    if(!Objects.nonNull(orderDetailData)){
                        return 0;
                    }
                    MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);


                    List<AgentAfterDirectAppointPushInfo> tocAgentAfterDirectAppointPushInfo = agentAfterDirectAppointPushInfo.stream().filter(item -> "toc".equals(item.getAgentType())).collect(Collectors.toList());
                    List<AgentAfterDirectAppointPushInfo> tobGroupAgentAfterDirectAppointPushInfo = agentAfterDirectAppointPushInfo.stream().filter(item -> "tobGroup".equals(item.getAgentType())).collect(Collectors.toList());
                    Set<Long> agentIdList = Sets.newHashSet();
                    if (CollectionUtil.isNotEmpty(tocAgentAfterDirectAppointPushInfo)) {
                        agentIdList.addAll(tocAgentAfterDirectAppointPushInfo.stream().map(AgentAfterDirectAppointPushInfo::getAgentId).collect(Collectors.toSet()));
                    }
                    Set<Long> tobGroupAgentIdList = Sets.newHashSet();
                    if (CollectionUtil.isNotEmpty(tobGroupAgentAfterDirectAppointPushInfo)) {
                        tobGroupAgentIdList.addAll(tobGroupAgentAfterDirectAppointPushInfo.stream().map(AgentAfterDirectAppointPushInfo::getAgentId).collect(Collectors.toSet()));
                    }

                    MatchMasterResult matchMasterResult = agentMasterMatcher.buildMatchMasterResult(agentIdList, tobGroupAgentIdList, null, orderDetailData, masterMatchCondition);

                    if (Objects.isNull(matchMasterResult) || matchMasterResult.getMasterIdSet().isEmpty()) {
                        log.info("agentDistribute byDirectAppointPriority do push no master,than do nextPriorityPush!orderId:{},agentPushType:{}", orderId, agentPushType);
                        sendNextPriorityPush(orderId);
                        return 1;
                    } else {
                        log.info("agentDistribute byDirectAppointPriority do push success,orderId:{},agentPushType:{}", orderId, agentPushType);
                        Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
                        Long timestamp = System.currentTimeMillis();
                        String orderVersion = String.valueOf(timestamp);
                        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, masterIdSet.size(), new Date(timestamp), PushMode.AGENT_MASTER.code);
                        JSONObject commonFeature = new JSONObject();
                        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
                        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
                        commonFeature.put(FieldConstant.PUSH_MODE, "agent");
                        commonFeature.put(AgentMasterMatcher.AGENT_MASTER_LIST, JSON.toJSONString(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_MASTER_LIST)));
                        commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
                        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
                        commonFeature.put(AgentMasterMatcher.AGENT_PUSH_TYPE, AgentPushType.PUSH.getType());
                        commonFeature.put(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME, agentPushNoHiredRePushTime);
                        commonFeature.put(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME, agentFirstViewNoHiredRePushTime);

                        pushControllerFacade.directPush(orderDetailData, orderVersion, matchMasterResult.getMasterIdSet(), commonFeature);
                        return 1;
                    }
                }

            }

        }

    }

    /**
     * 下一优先级推单
     * @param orderId
     */
    private void sendNextPriorityPush(Long orderId) {
        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(orderId);
        orderMatchMasterRqt.setExclusivePushModeList(Arrays.asList(PushMode.FULL_TIME_MASTER.code,
                PushMode.DIRECT_APPOINT_MASTER.code,
                PushMode.TECHNIQUE_VERIFY_MASTER.code,
                PushMode.COOPERATION_BUSINESS_MASTER.code,
                PushMode.COLLECT_CONTRACT_MASTER.code,
                PushMode.ORDER_PACKAGE_MASTER.code,
                PushMode.AGENT_MASTER.code));

        pushQueueService.sendDelayPushMessage(10L, JSONObject.toJSONString(orderMatchMasterRqt));
    }

    /**
     * 直接指派逻辑
     *
     * @param orderId
     * @param tocMasterList
     * @param tobGroupMasterList
     * @param orderLatitude
     * @param orderLongitude
     */
    private boolean doDirectAppoint(Long orderId, List<AgentPushMaster> tocMasterList, List<AgentPushMaster> tobGroupMasterList,
                                    BigDecimal orderLatitude, BigDecimal orderLongitude) {

        if (CollectionUtil.isEmpty(tocMasterList) && CollectionUtil.isEmpty(tobGroupMasterList)) {
            return false;
        }
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        if (orderGrabByIdResp == null || orderGrabByIdResp.getOrderBase() == null) {
            return false;
        }

        if (CollectionUtil.isEmpty(tocMasterList)) {
            //没有c端代理商，直接团队师傅
            return doTobGroupDirectAppoint(orderGrabByIdResp, tobGroupMasterList);
        } else {
            //先c端代理商，再团队师傅代理商
            boolean isTocDirectAppoint = doTocDirectAppoint(orderGrabByIdResp, tocMasterList, orderLatitude, orderLongitude);
            if (isTocDirectAppoint) {
                return true;
            } else {
                return doTobGroupDirectAppoint(orderGrabByIdResp, tobGroupMasterList);
            }
        }
    }

    /**
     * c端代理商指派
     * @param orderGrabByIdResp
     * @param tocMasterList
     * @param orderLatitude
     * @param orderLongitude
     * @return
     */
    private boolean doTocDirectAppoint(OrderGrabByIdResp orderGrabByIdResp, List<AgentPushMaster> tocMasterList,
                                       BigDecimal orderLatitude, BigDecimal orderLongitude) {
        if (CollectionUtil.isEmpty(tocMasterList)) {
            return false;
        }
        List<AgentPushMaster> remainingTocMasters = new ArrayList<>(tocMasterList);
        while (!remainingTocMasters.isEmpty()) {
            //先随机取一个c端代理商
            AgentPushMaster agentPushMaster = RandomUtil.randomEle(remainingTocMasters);
            if (!Strings.isNullOrEmpty(agentPushMaster.getMasterList())) {
                List<Long> masterList = Arrays.stream(agentPushMaster.getMasterList().split(",")).map(Long::parseLong).collect(Collectors.toList());

                if ("smart".equals(agentPushMaster.getAgentBelongsStrategyDistributePriority())) {
                    masterList = sortMaster(masterList, orderLongitude, orderLatitude);
                } else {
                    Collections.shuffle(masterList);
                }

                OrderBase orderBase = orderGrabByIdResp.getOrderBase();
                GrabOrderValidRqt rqt = new GrabOrderValidRqt();
                rqt.setOrderId(orderBase.getOrderId());
                rqt.setGrabType(4);
                rqt.setOrderModifyTime(orderBase.getOrderModifyTime());

                for (Long masterId : masterList) {
                    rqt.setMasterId(masterId);
                    JSONObject jsonObject = tools.catchNoLog(() -> masterOrderApi.autoGrabOrder(rqt));
                    boolean result = (jsonObject != null) && Objects.equals(jsonObject.get("grabOrderFlag"), true);
                    if (result) {
                        return true;
                    } else {
                        String failReason = "";
                        if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("tipContent"))) {
                            failReason = jsonObject.getString("tipContent");
                        }
                        log.error(String.format("代理商订单直接指派C端师傅失败,orderId:%d,masterId:%d，failReason:%s", orderBase.getOrderId(), masterId, failReason));
                    }
                }
            }

            remainingTocMasters.remove(agentPushMaster);
        }
        return false;
    }

    /**
     * 团队师傅直接指派
     *
     * @param orderGrabByIdResp
     * @param tobGroupMasterList
     * @return
     */
    private boolean doTobGroupDirectAppoint(OrderGrabByIdResp orderGrabByIdResp, List<AgentPushMaster> tobGroupMasterList) {

        if (CollectionUtil.isEmpty(tobGroupMasterList)) {
            return false;
        }
        //随机打乱
        Collections.shuffle(tobGroupMasterList);

        OrderBase orderBase = orderGrabByIdResp.getOrderBase();

        GrabOrderValidRqt rqt = new GrabOrderValidRqt();
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGrabType(4);
        rqt.setOrderModifyTime(orderBase.getOrderModifyTime());

        //团队师傅
        for (AgentPushMaster tobGroupMaster : tobGroupMasterList) {
            rqt.setMasterId(tobGroupMaster.getAgentId());
            JSONObject jsonObject = tools.catchNoLog(() -> masterOrderApi.autoGrabOrder(rqt));
            boolean result = (jsonObject != null) && Objects.equals(jsonObject.get("grabOrderFlag"), true);
            if (result) {
                return true;
            } else {
                String failReason = "";
                if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("tipContent"))) {
                    failReason  = jsonObject.getString("tipContent");
                }
                log.error(String.format("代理商订单直接指派B端团队师傅失败,orderId:%d,masterId:%d，failReason:%s", orderBase.getOrderId(), tobGroupMaster.getAgentId(), failReason));
            }
        }
        return false;

    }

    private List<Long> sortMaster(List<Long> masterList, BigDecimal orderLongitude, BigDecimal orderLatitude) {
        if (!BigDecimalUtil.isGreaterThan(orderLongitude, BigDecimal.ZERO) || !BigDecimalUtil.isGreaterThan(orderLatitude, BigDecimal.ZERO)) {
            //订单经纬度有误,将师傅打乱重新返回
            Collections.shuffle(masterList);
            return masterList;
        }
        Map<Long, BatchGetMasterBaseInfoResp> masterBaseInfoRespMap = masterCommon.getMasterByMasterIds(masterList)
                .stream().collect(Collectors.toMap(BatchGetMasterBaseInfoResp::getMasterId, Function.identity()));

        return masterList.stream()
                .sorted((m1, m2) -> {
                    BigDecimal m1lon = Optional.ofNullable(masterBaseInfoRespMap.get(m1)).map(BatchGetMasterBaseInfoResp::getLocationLongitude).orElse(BigDecimal.ZERO);
                    BigDecimal m1lat = Optional.ofNullable(masterBaseInfoRespMap.get(m1)).map(BatchGetMasterBaseInfoResp::getLocationLatitude).orElse(BigDecimal.ZERO);
                    BigDecimal m2lon = Optional.ofNullable(masterBaseInfoRespMap.get(m2)).map(BatchGetMasterBaseInfoResp::getLocationLongitude).orElse(BigDecimal.ZERO);
                    BigDecimal m2lat = Optional.ofNullable(masterBaseInfoRespMap.get(m2)).map(BatchGetMasterBaseInfoResp::getLocationLatitude).orElse(BigDecimal.ZERO);

                    double distance1 = DistanceUtil.distance(orderLongitude.doubleValue(), orderLongitude.longValue(), m1lon.doubleValue(), m1lat.doubleValue());
                    double distance2 = DistanceUtil.distance(orderLongitude.doubleValue(), orderLongitude.longValue(), m2lon.doubleValue(), m2lat.doubleValue());
                    return Double.compare(distance1, distance2);
                }).collect(Collectors.toList());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        OrderDistributeContext.register("agent", this);
    }
}
