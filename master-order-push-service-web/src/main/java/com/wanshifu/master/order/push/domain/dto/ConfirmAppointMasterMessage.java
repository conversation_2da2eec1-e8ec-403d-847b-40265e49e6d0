package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ConfirmAppointMasterMessage {

    /**
     * 全局订单ID
     */
    private Long globalOrderTraceId;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 下单人账号ID
     */
    private Long accountId;

    /**
     * 下单人账号类型
     */
    private String accountType;
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 雇佣师傅ID
     */
    private Long hireMasterId;


    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 指派类型(2-发布任务，3-直接雇佣,4-一口价,5-预付款)
     */
    private Integer appointType;

    /**
     * 订单支付状态,0:默认,1:一口价前置支付
     */
    private Integer orderPayStatus = 0;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 招募ID (总包直接指派需求)
     */
    private Long recruitId;

    /**
     * 招募类型，agreement_master: 协议师傅
     */
    private String recruitType;

    /**
     * 订单标签
     */
    private String orderLabel;


}
