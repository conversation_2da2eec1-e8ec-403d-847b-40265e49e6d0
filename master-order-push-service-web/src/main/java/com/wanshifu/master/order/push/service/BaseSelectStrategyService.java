package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface BaseSelectStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    BaseSelectStrategy detail(DetailRqt rqt);

    int enable(EnableRqt rqt);

    SimplePageInfo<BaseSelectStrategy> list(ListRqt rqt);

    int delete(DeleteRqt rqt);

    List<BaseSelectStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList);
}