package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.common.enums.AttributeKeyEnum;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.order.offer.api.OrderGoodsResourceApi;
import com.wanshifu.order.offer.api.OrderServiceAttributeApi;
import com.wanshifu.order.offer.domains.api.response.OrderGoodsDetailResp;
import com.wanshifu.order.offer.domains.bo.service.*;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGoods;
import com.wanshifu.order.offer.domains.po.OrderIkeaGoods;
import com.wanshifu.order.offer.domains.po.OrderServiceAttributeInfo;
import com.wanshifu.order.offer.domains.vo.publish.OrderGoodsWithAttachment;
import com.wanshifu.order.offer.domains.vo.publish.OrderServiceAttributeInfoVo;
import com.wanshifu.orderconfig.api.GoodsApi;
import com.wanshifu.orderconfig.domains.reqbean.ConfigGoodsCategoryReq;
import com.wanshifu.orderconfig.domains.respbean.ConfigGoodsCategoryResp;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderServiceAttributeService {

    @Resource
    OrderCommon orderCommon;

    @Resource
    OrderGoodsResourceApi orderGoodsResourceApi;
//
//    @Resource
//    Tools tools;

    @Resource
    GoodsApi goodsApi;

//    @Resource
//    IkeaOrderServiceApi ikeaOrderServiceApi;

    @Resource
    private Tools tools;

    @Resource
    private OrderServiceAttributeApi orderServiceAttributeApi;


    /**
     * 获取商品信息
     *
     * @return
     */
    public List<OrderGoods> orderGoodsList(List<OrderGoodsWithAttachment> orderGoodsComposite) {
        if (CollectionUtils.isEmpty(orderGoodsComposite)) {
            return Collections.emptyList();
        }

        return orderGoodsComposite.stream().map(
                e -> e.getOrderGoods().toPo()
        ).collect(Collectors.toList());
    }


    public long getLastLevelGoodsCategoryId(OrderBase orderBase) {
        if (orderCommon.isDynamicConfiguration(orderBase)) {
            List<OrderServiceAttributeInfo> orderServiceAttributeInfo = tools.catchLogThrow(() -> orderServiceAttributeApi.getOrderServiceAttributeInfo(orderBase.getOrderId()));
            if (CollectionUtils.isNotEmpty(orderServiceAttributeInfo)) {
                OrderServiceAttributeInfo attributeInfoResp = CollectionUtils.getFirstSafety(orderServiceAttributeInfo);
                ServiceInfo serviceInfo = getServiceInfo(attributeInfoResp);
                Long goodsCategoryChildId = serviceInfo.getGoodsCategoryChildId();
                Long goodsCategoryId = serviceInfo.getGoodsCategoryId();
                return goodsCategoryChildId == 0 ? goodsCategoryId : goodsCategoryChildId;
            }
        } else {
            List<OrderGoodsDetailResp> goodsDetailList = tools.catchLogThrow(() ->  orderGoodsResourceApi.getOrderGoodsDetail(orderBase.getOrderId()));
            if (CollectionUtils.isNotEmpty(goodsDetailList)) {
                int categoryChild = goodsDetailList.get(0).getOrderGoods().getCategoryChild();
                int goodsCategory = Integer.parseInt(goodsDetailList.get(0).getOrderGoods().getGoodsCategory());
                return categoryChild == 0 ? goodsCategory : categoryChild;
            }
        }

        return 0L;
    }


    /**
     * 订单报价服务对象>>
     * 获取服务信息
     *
     * @param orderServiceAttributeInfo
     * @return
     */
    public ServiceInfo getServiceInfoForOffer(OrderServiceAttributeInfo orderServiceAttributeInfo) {
        ServiceInfo serviceInfo = JSONObject.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(), ServiceInfo.class);
        return serviceInfo;
    }

    public ServiceInfo getServiceInfo(OrderServiceAttributeInfo orderServiceAttributeInfo) {
        ServiceInfo serviceInfo = JSONObject.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(), ServiceInfo.class);
        return serviceInfo;
    }

    public ServiceInfo getServiceInfo(String serviceAttributeString) {
        ServiceInfo serviceInfo = JSONObject.parseObject(serviceAttributeString, ServiceInfo.class);
        return serviceInfo;
    }


    public List<String> getOrderServiceInfoTagValues(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo, boolean includeUnit) {
        ServiceAttribute serviceAttribute = getOrderServiceAttribute(keyEnum, serviceInfo);

        if (serviceAttribute != null) {
            return getAttributeValues(Collections.singletonList(serviceAttribute), includeUnit);
        }
        return Collections.emptyList();
    }

    public List<String> getAttributeValues(List<ServiceAttribute> serviceAttributes, boolean includeUnit) {
        List<String> values = new ArrayList<>();
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {
                List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
                String value = Optional.ofNullable(serviceAttributeValue.getValue()).orElse("").trim();
                if (CollectionUtils.isNotEmpty(childList)) {
                    values.add(value + "(" + String.join("", getAttributeValues(childList)) + ")");
                } else if (StringUtils.isNotEmpty(value)) {
                    String unit = Optional.ofNullable(serviceAttribute.getUnit()).orElse("").trim();
                    String text = value + (includeUnit ? unit : "");
                    values.add(text);
                }
            }
        }
        return values;
    }

    public List<String> getAttributeValues(List<ServiceAttribute> serviceAttributes) {
        return getAttributeValues(serviceAttributes, true);
    }

    /**
     * 获取订单服务属性
     *
     * @param serviceInfo
     * @return
     */
    public ServiceAttribute getOrderServiceAttribute(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo) {
        List<ServiceAttribute> serviceAttributes = serviceInfo.getRootAttributeDetailList();
        return serviceAttributes.stream().filter(e -> Objects.equals(e.getAttributeKey(), keyEnum.toString())).findFirst().orElse(null);
    }


    public String getOrderServiceInfoKeySingleValue(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo, boolean includeUnit) {
        List<String> orderServiceInfoTagValues = this.getOrderServiceInfoTagValues(keyEnum, serviceInfo, includeUnit);
        String firstSafety = CollectionUtils.getFirstSafety(orderServiceInfoTagValues);
        return firstSafety != null ? firstSafety : "";
    }

    public String getOrderServiceInfoKeySingleValue(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo, boolean includeUnit, String defaultValue) {
        String value = getOrderServiceInfoKeySingleValue(keyEnum, serviceInfo, includeUnit);
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }
        return defaultValue;
    }

    /**
     * 获取商品数量
     *
     * @param serviceInfo
     * @return
     */
    public String getGoodsNum(ServiceInfo serviceInfo) {
        return this.getOrderServiceInfoKeySingleValue(AttributeKeyEnum.goods_number, serviceInfo, false, "1");
    }


    /**
     * 获取订单商品类别
     *
     * @param orderBase
     * @return
     */
    public String getOrderGoodsTypeName(OrderBase orderBase) {
        if (orderCommon.isIkeaOrder(orderBase)) {
//            GetOrderIkeaGoodsRqt ikeaGoodsRqt = new GetOrderIkeaGoodsRqt();
//            ikeaGoodsRqt.setOrderId(orderBase.getOrderId());
//            List<OrderIkeaGoods> orderIkeaGoods = ikeaOrderServiceApi.getOrderIkeaGoods(ikeaGoodsRqt);
            List<OrderIkeaGoods> orderIkeaGoods = orderCommon.queryOrderIkeaGoods(orderBase.getOrderId(), orderBase.getGlobalOrderTraceId());
            return orderIkeaGoods.get(0).getGoodsName();
        }
        long goodsCategoryId = getLastLevelGoodsCategoryId(orderBase);
        return this.goodsTypeName(goodsCategoryId);
    }


    private String goodsTypeName(Long categoryChild) {
        ConfigGoodsCategoryReq goodsCategoryReq = new ConfigGoodsCategoryReq();
        goodsCategoryReq.setGoodsCategoryId(categoryChild.intValue());
        Optional<ConfigGoodsCategoryReq> req = Optional.of(goodsCategoryReq);

        SimplePageInfo<ConfigGoodsCategoryResp> simplePageInfo = tools.catchLog(() -> goodsApi.listGoods(req), "listGoods", req);
        if (Objects.nonNull(simplePageInfo) && CollectionUtils.isNotEmpty(simplePageInfo.getList())) {
            ConfigGoodsCategoryResp configGoodsCategoryResp = simplePageInfo.getList().get(0);
            return configGoodsCategoryResp.getCnName();
        } else {
            return "";
        }
    }


    public String goodsTypeName(int categoryId, Long goodsCategoryId, Long goodsCategoryChildId) {
        return this.goodsTypeName(goodsCategoryChildId != 0L ? goodsCategoryChildId.intValue() : goodsCategoryId);
    }


    /**
     * 获取订单服务图片
     *
     * @param orderServiceAttributeInfoVos
     * @return
     */
    public List<String> getServiceImageList(List<OrderServiceAttributeInfoVo> orderServiceAttributeInfoVos) {

        List<String> imageList = new ArrayList<>();
        for (OrderServiceAttributeInfoVo orderServiceAttributeInfoVo : orderServiceAttributeInfoVos) {
            ServiceInfo serviceInfo = orderServiceAttributeInfoVo.getServiceInfos();
            List<ServiceAttribute> serviceAttributes = serviceInfo.getRootAttributeDetailList();
            List<AttributeExpand> attributeExpands = getAttributeExpands(serviceAttributes);
            attributeExpands.forEach(valueExpand -> {
                if ("image".equals(valueExpand.getType())) {
                    Long aid = valueExpand.getAid();
                    if (aid != null && valueExpand.getPath() != null) {
                        imageList.add(aid.toString());
                    }
                }
            });
        }
        return imageList;
    }

    public List<AttributeExpand> getAttributeExpands(List<ServiceAttribute> serviceAttributes) {
        List<AttributeExpand> expands = new ArrayList<>();
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            List<ServiceAttributeValue> attributeValues = serviceAttribute.getChildList();
            for (ServiceAttributeValue attributeValue : attributeValues) {
                List<ServiceAttribute> childServiceAttributeDetailDto = attributeValue.getChildList();
                AttributeExpand expand = attributeValue.getExpand();
                if (CollectionUtils.isNotEmpty(childServiceAttributeDetailDto)) {
                    expands.addAll(getAttributeExpands(childServiceAttributeDetailDto));
                } else if (expand != null && StringUtils.isNotEmpty(expand.getPath())) {
                    expands.add(expand);
                }
            }
        }

        return expands;
    }

    public List<AttributeExpand> getServiceExpands(AttributeKeyEnum keyEnum, OrderServiceAttributeInfo orderServiceAttributeInfo) {
        ServiceInfo serviceInfo = getServiceInfo(orderServiceAttributeInfo);
        return getServiceExpands(keyEnum, serviceInfo);
    }


    public List<AttributeExpand> getServiceExpands(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo) {
        ServiceAttribute orderServiceAttribute = getOrderServiceAttribute(keyEnum, serviceInfo);
        if (orderServiceAttribute != null) {
            return getAttributeExpands(Collections.singletonList(orderServiceAttribute));
        }
        return Collections.emptyList();
    }

    /**
     * 获取服务视频
     *
     * @param keyEnum
     * @param serviceInfo
     * @return
     */
    public List<AttributeExpand> getServiceExpandsVideo(AttributeKeyEnum keyEnum, ServiceInfo serviceInfo) {
        List<AttributeExpand> attributeExpands = getServiceExpands(keyEnum, serviceInfo);
        List<AttributeExpand> attributeExpandList = new ArrayList<>();
        attributeExpands.forEach(valueExpand -> {
            if ("video".equals(valueExpand.getType())) {
                if (valueExpand.getPath() != null) {
                    attributeExpandList.add(valueExpand);
                }
            }
        });
        return attributeExpandList;
    }

    /**
     * 获取服务数量信息
     *
     * @param serviceInfo
     * @return ImmutablePair<数量, 单位>
     */
    public ImmutablePair<String, String> getServiceNumInfo(ServiceInfo serviceInfo) {

        String goodsNum = "1";
        String unit = "";
        ServiceAttribute serviceAttribute = getOrderServiceAttribute(AttributeKeyEnum.goods_number, serviceInfo);

        if (serviceAttribute != null) {
            List<ServiceAttributeValue> attributeValues = serviceAttribute.getChildList();
            ServiceAttributeValue attributeValue = CollectionUtils.getFirstSafety(attributeValues);

            unit = serviceAttribute.getUnit();
            goodsNum = Optional.ofNullable(attributeValue).map(e -> String.valueOf(e.getValue())).orElse("");

        }

        return ImmutablePair.of(goodsNum, unit);
    }

    /**
     * 获取服务商品图片
     *
     * @param orderServiceAttributeInfos
     * @return
     */
    public Long getServiceGoodsImageAid(List<OrderServiceAttributeInfo> orderServiceAttributeInfos) {
        OrderServiceAttributeInfo orderServiceAttributeInfo = orderServiceAttributeInfos.get(0);
        List<AttributeExpand> serviceExpands = getServiceExpands(AttributeKeyEnum.goods_image, orderServiceAttributeInfo);
        return serviceExpands.stream().map(AttributeExpand::getAid).findAny().orElse(0L);
    }


    public List<OrderServiceAttributeInfoVo> transToOrderServiceAttributeInfoVo(List<OrderServiceAttributeInfo> orderServiceAttributeInfos) {

        if(CollectionUtils.isEmpty(orderServiceAttributeInfos)){
            return Collections.emptyList();
        }

        return orderServiceAttributeInfos.stream().map(e -> {
            OrderServiceAttributeInfoVo orderServiceAttributeInfoVo = new OrderServiceAttributeInfoVo();

            ServiceInfo serviceInfo = this.getServiceInfo(e);

            if (StringUtils.isNotEmpty(e.getServicePriceInfos())) {
                DefinitePrice definitePrice = JSONObject.parseObject(e.getServicePriceInfos(), DefinitePrice.class);
                orderServiceAttributeInfoVo.setServicePriceInfos(definitePrice);
            }

            orderServiceAttributeInfoVo.setServiceId(e.getServiceId());
            orderServiceAttributeInfoVo.setServiceInfos(serviceInfo);
            orderServiceAttributeInfoVo.setServiceVersion(e.getServiceVersion());
            orderServiceAttributeInfoVo.setThirdServiceId(e.getThirdServiceId());
            orderServiceAttributeInfoVo.setAfterSalesGoodsId(e.getAfterSalesGoodsId());

            return orderServiceAttributeInfoVo;
        }).collect(Collectors.toList());
    }

    /**
     * 订单报价服务对象>>转订单服务属性对象
     *
     * @param orderServiceAttributeInfos
     * @return
     */
    public List<OrderServiceAttributeInfoVo> transToOrderServiceAttributeInfoVoForOffer(List<OrderServiceAttributeInfo> orderServiceAttributeInfos) {

        if (CollectionUtils.isEmpty(orderServiceAttributeInfos)){
            return Collections.emptyList();
        }

        return orderServiceAttributeInfos.stream().map(e -> {
            OrderServiceAttributeInfoVo orderServiceAttributeInfoVo = new OrderServiceAttributeInfoVo();

            ServiceInfo serviceInfo = this.getServiceInfoForOffer(e);

            if (StringUtils.isNotEmpty(e.getServicePriceInfos())) {
                DefinitePrice definitePrice = JSONObject.parseObject(e.getServicePriceInfos(), DefinitePrice.class);
                orderServiceAttributeInfoVo.setServicePriceInfos(definitePrice);
            }

            orderServiceAttributeInfoVo.setServiceId(e.getServiceId());
            orderServiceAttributeInfoVo.setServiceInfos(serviceInfo);
            orderServiceAttributeInfoVo.setServiceVersion(e.getServiceVersion());
            orderServiceAttributeInfoVo.setThirdServiceId(e.getThirdServiceId());
            orderServiceAttributeInfoVo.setAfterSalesGoodsId(e.getAfterSalesGoodsId());

            return orderServiceAttributeInfoVo;
        }).collect(Collectors.toList());
    }


    /**
     * 获取商品数量（地毯为面积，有小数）
     *
     * @param orderBase
     * @return
     */
    public BigDecimal getGoodsNum(List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,List<OrderGoods> orderGoodsList,
                                  List<OrderIkeaGoods> orderIkeaGoodsList) {
        if (CollectionUtils.isNotEmpty(orderServiceAttributeInfoList)) {
            return orderServiceAttributeInfoList.stream().reduce(BigDecimal.ZERO, (bigDecimal, orderServiceAttributeInfoResp) -> {
                ServiceInfo serviceInfo = this.getServiceInfo(orderServiceAttributeInfoResp);
                String orderServiceInfoSingleTagValue = getGoodsNum(serviceInfo);
                return bigDecimal.add(new BigDecimal(orderServiceInfoSingleTagValue));
            }, (b1, b2) -> null);
        } else if (CollectionUtils.isNotEmpty(orderIkeaGoodsList)) {
            return orderIkeaGoodsList.stream().reduce(BigDecimal.ZERO, (bigDecimal, orderIkeaGoods) -> bigDecimal.add(new BigDecimal(orderIkeaGoods.getNumber())), (b1, b2) -> null);
        } else {
            return orderGoodsList.stream().reduce(BigDecimal.ZERO, (bigDecimal, orderGoods) -> bigDecimal.add(new BigDecimal(orderGoods.getNumber())), (b1, b2) -> null);
        }
    }


    public String getValueByAttributePathNo(ServiceInfo serviceInfo,String attributePathNoValue){
        List<ServiceAttribute> serviceAttributes = serviceInfo.getRootAttributeDetailList();
        for (ServiceAttribute serviceAttribute : serviceAttributes) {
            String value = getValueByAttributePathNo(serviceAttribute,attributePathNoValue);
            if(StringUtils.isNotBlank(value)){
                return value;
            }
        }
        return null;
    }


    public String getValueByAttributePathNo(ServiceAttribute serviceAttribute,String attributePathNoValue){

        for (ServiceAttributeValue serviceAttributeValue : serviceAttribute.getChildList()) {
            if(attributePathNoValue.equals(serviceAttributeValue.getAttributePathNo())){
                return Optional.ofNullable(serviceAttributeValue.getValue()).orElse("").trim();
            }
            List<ServiceAttribute> childList = serviceAttributeValue.getChildList();
            if (CollectionUtils.isNotEmpty(childList)) {
                for(ServiceAttribute childServiceAttribute : childList){
                    String value = getValueByAttributePathNo(childServiceAttribute,attributePathNoValue);
                    if(StringUtils.isNotBlank(value)){
                        return value;
                    }
                }
            }
        }
        return null;
    }

}
