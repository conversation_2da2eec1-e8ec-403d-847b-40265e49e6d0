package com.wanshifu.master.order.push.service.impl;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.*;
import com.wanshifu.master.order.push.mapper.OrderMatchRoutingMapper;
import com.wanshifu.master.order.push.repository.OrderMatchRoutingRepository;
import com.wanshifu.master.order.push.service.OrderMatchRoutingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OrderMatchRoutingServiceImpl implements OrderMatchRoutingService {

    @Resource
    private OrderMatchRoutingRepository orderMatchRoutingRepository;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "路由管理", level2MenuName = "匹配路由管理",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "routingName")
    public int create(CreateRqt rqt){
        Integer result = checkRoutingName(rqt.getRoutingName(),null);
        if(result != 1){
            return result;
        }
        orderMatchRoutingRepository.insertRouting(rqt.getRoutingName(),rqt.getRoutingDesc(),rqt.getOrderFrom(),rqt.getOrderTag(),rqt.getRoutingType(),rqt.getMatchRouting(),
                rqt.getLv1MasterType(),rqt.getLv2MasterType(),rqt.getLv3MasterType(),rqt.getCreateAccountId());
        return 1;
    }

    private Integer checkRoutingName(String routingName, Integer routingId) {
        OrderMatchRouting orderMatchRouting = orderMatchRoutingRepository.selectByRoutingName(routingName, routingId);
        return orderMatchRouting != null ? 10010 : 1;
    }
    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "路由管理", level2MenuName = "匹配路由管理",
            tableName = "order_match_routing", mapperClass = OrderMatchRoutingMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderMatchRoutingMapper", primaryKeyFieldName = "routingId",
            configNameFieldNameFromEntity = "routingName")
    public int update(UpdateRqt rqt){
        checkRoutingName(rqt.getRoutingName(),rqt.getRoutingId());
        orderMatchRoutingRepository.updateRouting(rqt.getRoutingId(),rqt.getRoutingName(),rqt.getRoutingDesc(),rqt.getOrderFrom(),rqt.getOrderTag(),rqt.getRoutingType(),rqt.getMatchRouting(),
                rqt.getLv1MasterType(),rqt.getLv2MasterType(),rqt.getLv3MasterType(),rqt.getUpdateAccountId());
        return 1;
    }

    @Override
    public OrderMatchRouting detail(DetailRqt rqt){
        return orderMatchRoutingRepository.selectByPrimaryKey(rqt.getRoutingId());
    }

    @Override
    public SimplePageInfo<OrderMatchRouting> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<OrderMatchRouting> orderMatchRoutingList = orderMatchRoutingRepository.selectList(rqt.getRoutingName(),rqt.getCreateStartTime(),rqt.getCreateEndTime());

        SimplePageInfo<OrderMatchRouting> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(orderMatchRoutingList);
        return listRespSimplePageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "路由管理", level2MenuName = "匹配路由管理",
            tableName = "order_match_routing", mapperClass = OrderMatchRoutingMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderMatchRoutingMapper", primaryKeyFieldName = "routingId",
            configNameFieldNameFromEntity = "routingName")
    public Integer delete(DeleteRqt rqt){
        orderMatchRoutingRepository.deleteByPrimaryKey(rqt.getRoutingId());
        return 1;
    }

}
