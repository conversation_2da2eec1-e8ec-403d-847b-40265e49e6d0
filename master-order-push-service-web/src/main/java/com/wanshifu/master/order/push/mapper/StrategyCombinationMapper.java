package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 策略组合Mapper
 * <AUTHOR>
 */
public interface StrategyCombinationMapper extends IBaseCommMapper<StrategyCombination> {

    List<StrategyCombination> selectByCategoryIdAndCityId(@Param("orderFlag") String orderFlag,@Param("businessLineId") Integer businessLineId, @Param("categoryId") String categoryId, @Param("cityId") String cityId);


    StrategyCombination selectByCityAndCategory(@Param("orderFlag") String orderFlag,@Param("cityIdStr") String cityIdStr, @Param("categoryIdList") List<String> categoryIdList, @Param("combinationId") Long combinationId,@Param("businessLineId") Integer businessLineId);


}