package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AgreementMasterMatch;
import com.wanshifu.master.order.push.domain.po.OrderFullTimeMasterMasterMatchLog;
import com.wanshifu.master.order.push.domain.po.OrderTechniqueVerifyMasterMatch;
import com.wanshifu.master.order.push.mapper.OrderFullTimeMasterMatchLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class OrderFullTimeMasterMatchLogRepository extends BaseRepository<OrderFullTimeMasterMasterMatchLog> {

    @Resource
    private OrderFullTimeMasterMatchLogMapper orderFullTimeMasterMatchLogMapper;


    public List<OrderFullTimeMasterMasterMatchLog> selectList(String orderNo, Long masterId, Date orderCreateTimeStart, Date orderCreateTimeEnd) {
        return orderFullTimeMasterMatchLogMapper.selectList(orderNo, masterId, orderCreateTimeStart, orderCreateTimeEnd);
    }





}