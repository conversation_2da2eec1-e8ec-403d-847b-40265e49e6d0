package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.message.EnterpriseAppointMessage;
import com.wanshifu.master.order.push.domain.message.EnterpriseMatchAgreementMasterResultMessage;

public interface EnterpriseAppointMasterService {

//    Long matchAgreementMaster(EnterpriseAppointMessage message);

    Integer matchTechniqueVerifyMaster(MatchSupportMasterRqt rqt) ;


    EnterpriseMatchAgreementMasterResultMessage matchAppointMaster(EnterpriseAppointMessage message);

    }
