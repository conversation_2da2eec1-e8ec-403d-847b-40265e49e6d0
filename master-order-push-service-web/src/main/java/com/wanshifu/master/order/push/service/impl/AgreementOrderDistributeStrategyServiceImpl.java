package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.DistributeStrategyExpressionDto;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.mapper.AgreementOrderDistributeStrategyMapper;

import com.wanshifu.master.order.push.repository.AgreementOrderDistributeStrategyRepository;
import com.wanshifu.master.order.push.service.AgreementOrderDistributeStrategyService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementOrderDistributeStrategyServiceImpl implements AgreementOrderDistributeStrategyService {

    @Resource
    private AgreementOrderDistributeStrategyRepository agreementOrderDistributeStrategyRepository;
//
//    @Resource
//    private OrderSelectStrategyRepository orderSelectStrategyRepository;
//
//    @Resource
//    private OrderScoringStrategyRepository orderScoringStrategyRepository;

    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "协议订单调度",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {

        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        checkStrategyName(strategyName, businessLineId, null);

        List<CreateRqt.DistributeStrategyItem> distributeStrategyList = rqt.getDistributeStrategyList();
        List<DistributeStrategyExpressionDto> distributeStrategyExpressionDtos = this.buildStrategyExpression(distributeStrategyList);
        return agreementOrderDistributeStrategyRepository.insert(rqt.getStrategyName(), rqt.getStrategyDesc(),
                rqt.getBusinessLineId(), rqt.getCategoryIds(),
                rqt.getCityIds(), JSON.toJSONString(distributeStrategyList),
                JSON.toJSONString(distributeStrategyExpressionDtos),
                JSON.toJSONString(rqt.getCompensateDistributeList()),
                rqt.getCreateAccountId());
    }


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "协议订单调度",
            tableName = "agreement_order_distribute_strategy", mapperClass = AgreementOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agreementOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer update(UpdateRqt rqt) {
        String strategyName = rqt.getStrategyName();
        Integer businessLineId = rqt.getBusinessLineId();
        Integer strategyId = rqt.getStrategyId();
        checkStrategyName(strategyName, businessLineId, strategyId);
        List<CreateRqt.DistributeStrategyItem> distributeStrategyList = rqt.getDistributeStrategyList();
        List<DistributeStrategyExpressionDto> distributeStrategyExpressionDtoList = this.buildStrategyExpression(distributeStrategyList);
        return agreementOrderDistributeStrategyRepository.update(strategyId, rqt.getStrategyName(), rqt.getStrategyDesc(),
                rqt.getBusinessLineId(), rqt.getCategoryIds(),
                rqt.getCityIds(), JSON.toJSONString(distributeStrategyList),
                JSON.toJSONString(distributeStrategyExpressionDtoList),
                JSON.toJSONString(rqt.getCompensateDistributeStrategyList()),
                rqt.getUpdateAccountId());
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Integer businessLineId, Integer strategyId) {
        AgreementOrderDistributeStrategy agreementOrderDistributeStrategy = agreementOrderDistributeStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        org.springframework.util.Assert.isNull(agreementOrderDistributeStrategy, "该业务线已存在相同策略名称!");
    }

    private List<DistributeStrategyExpressionDto> buildStrategyExpression(List<CreateRqt.DistributeStrategyItem> ruleList) {
        return ruleList.stream().map(ruleItem -> {
            CreateRqt.OpenCondition openCondition = ruleItem.getOpenCondition();
            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user");
                        if (specialTerms.contains(s.getItemName())) {
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<CreateRqt.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","), Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","), Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }

            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(CreateRqt.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));
            return new DistributeStrategyExpressionDto(openConditionRuleParams, openConditionRuleExpression, ruleItem.getSelectStrategyId(),ruleItem.getScoreStrategyId(),ruleItem.getDistributeRule(),ruleItem.getDistributeNum());

        }).collect(Collectors.toList());


    }

    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "协议订单调度",
            tableName = "agreement_order_distribute_strategy", mapperClass = AgreementOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agreementOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt) {
        Condition condition = new Condition(AgreementOrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId())
                .andEqualTo("strategyStatus", 0)
                .andEqualTo("isDelete", 0);

        AgreementOrderDistributeStrategy pushNoticeStrategy = new AgreementOrderDistributeStrategy();
        pushNoticeStrategy.setIsDelete(1);
        pushNoticeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = agreementOrderDistributeStrategyRepository.updateByConditionSelective(pushNoticeStrategy, condition);
        org.springframework.util.Assert.isTrue(result > 0, "删除失败");
        return 1;
    }

    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "调度管理", level2MenuName = "协议订单调度",
            tableName = "agreement_order_distribute_strategy", mapperClass = AgreementOrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agreementOrderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt) {
        Condition condition = new Condition(AgreementOrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId())
                .andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);
        AgreementOrderDistributeStrategy agreementOrderDistributeStrategy = new AgreementOrderDistributeStrategy();
        agreementOrderDistributeStrategy.setStrategyStatus(rqt.getStrategyStatus());
        agreementOrderDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = agreementOrderDistributeStrategyRepository.updateByConditionSelective(agreementOrderDistributeStrategy, condition);

        if (rqt.getStrategyStatus() == 1) {
            org.springframework.util.Assert.isTrue(result > 0, "启用策略失败");
        } else {
            Assert.isTrue(result > 0, "禁用策略失败");
        }
        return 1;
    }

    @Override
    public AgreementOrderDistributeStrategy detail(DetailRqt rqt) {

//        DetailResp detailResp = new DetailResp();

        AgreementOrderDistributeStrategy agreementOrderDistributeStrategy = agreementOrderDistributeStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
//        if (agreementOrderDistributeStrategy != null) {
//            BeanUtils.copyProperties(agreementOrderDistributeStrategy, detailResp);
//
//            List<CreateRqt.DistributeStrategyItem> strategyItemList = JSON.parseArray(agreementOrderDistributeStrategy.getDistributeStrategyList(), CreateRqt.DistributeStrategyItem.class);
//            List<Integer> selectStrategyIds = strategyItemList.stream().map(CreateRqt.DistributeStrategyItem::getSelectStrategyId).collect(Collectors.toList());
//            List<Integer> scoreStrategyIds = strategyItemList.stream().map(CreateRqt.DistributeStrategyItem::getScoreStrategyId).collect(Collectors.toList());
//
//            List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(selectStrategyIds);
//            List<OrderScoringStrategy> orderScoringStrategyList = orderScoringStrategyRepository.selectByStrategyIdList(scoreStrategyIds);
//
//            detailResp.setOrderSelectStrategyList(orderSelectStrategyList);
//            detailResp.setOrderScoringStrategyList(orderScoringStrategyList);
//
//        }
        return agreementOrderDistributeStrategy;
    }

    @Override
    public SimplePageInfo<AgreementOrderDistributeStrategy> list(ListRqt rqt) {
        Page<?> page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<AgreementOrderDistributeStrategy> pushNoticeStrategyList = agreementOrderDistributeStrategyRepository.selectList(rqt);
        SimplePageInfo<AgreementOrderDistributeStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(pushNoticeStrategyList);
        return listRespSimplePageInfo;
    }

}
