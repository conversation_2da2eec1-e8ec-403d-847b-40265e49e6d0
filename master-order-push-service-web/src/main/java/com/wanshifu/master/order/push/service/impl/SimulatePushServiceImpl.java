package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import com.wanshifu.master.order.push.domain.resp.simulatePush.GetSimulatePushResp;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.*;
import com.wanshifu.master.order.push.repository.SimulatePushDetailRepository;
import com.wanshifu.master.order.push.repository.SimulatePushRepository;
import com.wanshifu.master.order.push.repository.StrategyCombinationSimulateRepository;
import com.wanshifu.master.order.push.service.SimulatePushService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class SimulatePushServiceImpl implements SimulatePushService {

    @Resource
    private StrategyCombinationSimulateRepository strategyCombinationSimulateRepository;

    @Resource
    private SimulatePushRepository simulatePushRepository;

    @Resource
    private SimulatePushDetailRepository simulatePushDetailRepository;

    @Override
    public List<StrategyCombinationSimulate> getStrategyCombinationSimulate(GetStrategyCombinationSimulateRqt rqt){
        return strategyCombinationSimulateRepository.selectByStrategyCombinationIds(rqt.getCombinationIds());
    }

    @Override
    public StrategyCombinationSimulate getCombinationLastSimulate(GetCombinationLastSimulateRqt rqt){
        return strategyCombinationSimulateRepository.selectLatestSimulate(rqt.getStrategyCombinationId());
    }

    @Override
    public Long addCombinationSimulate(AddCombinationSimulateRqt rqt){
        return strategyCombinationSimulateRepository.insertSimulate(rqt.getStrategyCombinationId(),rqt.getSimulateTime());
    }

    @Override
    public Integer updateOrderNum(UpdateOrderNumRqt rqt){
        return strategyCombinationSimulateRepository.updateOrderNum(rqt.getSimulateId(),rqt.getOrderNum());
    }


    @Override
    public Integer updateSimulateFinish(UpdateSimulateFinishRqt rqt){
        return strategyCombinationSimulateRepository.finishSimulateStatus(rqt.getSimulateId());
    }

    @Override
    public GetSimulatePushResp getSimulatePush(GetSimulatePushRqt rqt){

        GetSimulatePushResp getSimulatePushResp = new GetSimulatePushResp();

        getSimulatePushResp.setSimulatePush(simulatePushRepository.selectBySimulateIdAndOrderId(rqt.getSimulateId(),rqt.getOrderId()));
        getSimulatePushResp.setStrategyCombinationSimulate(strategyCombinationSimulateRepository.selectByPrimaryKey(rqt.getSimulateId()));

        return getSimulatePushResp;
    }

    @Override
    public Integer addSimulatedOrderNum(AddSimulateOrderNumRqt rqt){
        return strategyCombinationSimulateRepository.addSimulatedOrderNum(rqt.getSimulateId());
    }


    @Override
    public Long addSimulatePush(AddSimulatePushRqt rqt){
        SimulatePush simulatePush = new SimulatePush();
        simulatePush.setOrderId(rqt.getOrderId());
        simulatePush.setSimulateId(rqt.getSimulateId());
        simulatePush.setPushMasterNum(rqt.getPushMasterNum());
        simulatePush.setPushRounds(rqt.getPushRounds());
        simulatePush.setIsAlternateStrategy(rqt.getIsAlternateStrategy());
        simulatePush.setStrategyListName(rqt.getStrategyListName());
        simulatePush.setCreateTime(new Date());
        simulatePush.setUpdateTime(new Date());
        simulatePushRepository.insertSelective(simulatePush);
        return simulatePush.getSimulatePushId();
    }

    @Override
    public Integer batchAddSimulatePushDetail(BatchAddSimulatePushDetailRqt rqt){
        return simulatePushDetailRepository.insertList(rqt.getSimulatePushDetailList());
    }



}
