package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.mapper.OrderMatchRoutingMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class OrderMatchRoutingRepository extends BaseRepository<OrderMatchRouting> {

    @Resource
    private OrderMatchRoutingMapper orderMatchRoutingMapper;


    public Integer insertRouting(String routingName,String routingDesc,String orderFrom,String orderTag,String routingType,String matchRouting,
                         String lv1MasterType,String lv2MasterType,String lv3MasterType,Long createAccountId){
        OrderMatchRouting orderMatchRouting = new OrderMatchRouting();
        orderMatchRouting.setRoutingName(routingName);
        orderMatchRouting.setRoutingDesc(routingDesc);
        orderMatchRouting.setOrderFrom(orderFrom);
        orderMatchRouting.setOrderTag(orderTag);
        orderMatchRouting.setRoutingType(routingType);
        orderMatchRouting.setMatchRouting(matchRouting);
        orderMatchRouting.setLv1MasterType(lv1MasterType);
        orderMatchRouting.setLv2MasterType(lv2MasterType);
        orderMatchRouting.setLv3MasterType(lv3MasterType);
        orderMatchRouting.setCreateAccountId(createAccountId);
        orderMatchRouting.setUpdateAccountId(createAccountId);
        return this.insertSelective(orderMatchRouting);
    }


    public Integer updateRouting(Integer routingId,String routingName,String routingDesc,String orderFrom,String orderTag,String routingType,String matchRouting,
                                 String lv1MasterType,String lv2MasterType,String lv3MasterType,Long updateAccountId){
        OrderMatchRouting orderMatchRouting = new OrderMatchRouting();
        orderMatchRouting.setRoutingId(routingId);
        orderMatchRouting.setRoutingName(routingName);
        orderMatchRouting.setRoutingDesc(routingDesc);
        orderMatchRouting.setOrderFrom(orderFrom);
        orderMatchRouting.setOrderTag(orderTag);
        orderMatchRouting.setRoutingType(routingType);
        orderMatchRouting.setMatchRouting(matchRouting);
        orderMatchRouting.setLv1MasterType(lv1MasterType);
        if(StringUtils.isBlank(lv2MasterType)){
            orderMatchRouting.setLv2MasterType("");
        }else{
            orderMatchRouting.setLv2MasterType(lv2MasterType);
        }
        if(StringUtils.isBlank(lv3MasterType)){
            orderMatchRouting.setLv3MasterType("");
        }else{
            orderMatchRouting.setLv3MasterType(lv3MasterType);
        }
        orderMatchRouting.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(orderMatchRouting);
    }


    public List<OrderMatchRouting> selectList(String routingName, Date createStartTime, Date createEndTime){
        return orderMatchRoutingMapper.selectList(routingName,createStartTime,createEndTime);
    }

    public OrderMatchRouting selectByRoutingName(String routingName,Integer routingId) {
        Example example = new Example(OrderMatchRouting.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("routingName", routingName)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (routingId != null) {
            criteria.andNotEqualTo("routingId", routingId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }




}
