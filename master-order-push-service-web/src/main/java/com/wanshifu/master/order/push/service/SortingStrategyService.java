package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.po.SortingStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface SortingStrategyService {


    int enable(EnableRqt rqt);

    SimplePageInfo<SortingStrategy> list(ListRqt rqt);

    int delete(DeleteRqt rqt);

    int createV2(CreateV2Rqt rqt);

    int updateV2(UpdateV2Rqt rqt);

    SortingStrategy detailV2(DetailRqt rqt);

    List<SortingStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList);




}