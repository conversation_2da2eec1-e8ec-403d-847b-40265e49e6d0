package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 协议订单调度结果通知
 * <AUTHOR>
 * @description
 * @date 2025/2/25 18:36
 */
@Service
@Slf4j
public class NewModelOrderDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {


    @Resource
    private AgreementMasterMatchRepository agreementMasterMatchRepository;


    @Resource
    private AgreementMasterPushRepository agreementMasterPushRepository;

    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;


    @Value("${agreementOrderDistribute.move.switch:on}")
    private String agreementOrderDistributeMoveSwitch;


    @Resource
    private OrderDataBuilder orderDataBuilder;


    @Resource
    private NewModelMatchDetailRepository newModelMatchDetailRepository;

    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage){

        if(!"on".equals(agreementOrderDistributeMoveSwitch)){
            return 0;
        }

        String pushMode;
        if("0".equals(orderDistributeResultMessage.getExtraId())){
            pushMode = "afresh_new_model_single";
        }else{
            pushMode = "new_model_single";
        }

        Integer distributeResult = 1;
        if(CollectionUtils.isNotEmpty(orderDistributeResultMessage.getSuccessMasterList())){
            distributeResult = 1;
        }else{
            distributeResult = 0;
        }

        List<Long> successMasterList = orderDistributeResultMessage.getSuccessMasterList();
        List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
        Map<Long,JSONObject> failMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(failMasterList)){
            List<JSONObject> jsonObjectList = orderDistributeResultMessage.getMasterFailVos();
            if(CollectionUtils.isNotEmpty(jsonObjectList)){
                jsonObjectList.forEach(jsonObject -> {
                    failMap.put( jsonObject.getLong("masterId"),jsonObject.getJSONObject("object"));
                });
            }
        }
        Long orderId = orderDistributeResultMessage.getOrderId();
        Long masterId = distributeResult == 1 ? successMasterList.get(0) : failMasterList.get(0);

        if(distributeResult == 0){

            if("new_model_single".equals(pushMode)){
                OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NEW_MODEL_MASTER);
                OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                orderMatchMasterRqt.setMasterOrderId(orderId);
                OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
                MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
                orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
            }

        }


        NewModelMatchDetail newModelMatchDetail = newModelMatchDetailRepository.selectByOrderIdAndMasterId(orderId,masterId);
        if(Objects.nonNull(newModelMatchDetail)){
            String distributeResultRemark = failMap.containsKey(masterId) ? failMap.get(masterId).getString("tipContent") : "";
            NewModelMatchDetail matchDetail = new NewModelMatchDetail();
            matchDetail.setId(newModelMatchDetail.getId());
            matchDetail.setDistributeResult(distributeResult);
            matchDetail.setDistributeFailRemark(distributeResultRemark);
            matchDetail.setIsDistribute(null);
            matchDetail.setDistributeRemark(null);
            newModelMatchDetailRepository.updateByPrimaryKeySelective(matchDetail);
        }

        return 1;
    }


    private void updateAgreementMasterMatch(Long orderId,Long masterId,Long recruitId,Integer isDistributeSucc,String distributeFailReason){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterMatch agreementMasterMatch = agreementMasterMatchRepository.selectByAgreementMasterId(orderId,agreementMasterId);

        if(Objects.isNull(agreementMasterMatch)){
            return ;
        }

        AgreementMasterMatch updateAgreementMasterMatch = new AgreementMasterMatch();
        updateAgreementMasterMatch.setId(agreementMasterMatch.getId());
        updateAgreementMasterMatch.setIsAutoReceiveSucc(isDistributeSucc);
        updateAgreementMasterMatch.setAutoReceiveFailReason(distributeFailReason);
        agreementMasterMatchRepository.updateByPrimaryKeySelective(updateAgreementMasterMatch);
    }

    private void updateAgreementMasterPush(String today,Long masterId,Long recruitId){

        String agreementMasterId = recruitId + ":" + masterId;
        AgreementMasterPush agreementMasterPush = agreementMasterPushRepository.selectByAgreementMasterId(agreementMasterId);

        if(Objects.isNull(agreementMasterPush)){
            agreementMasterPush = new AgreementMasterPush();
            agreementMasterPush.setAgreementMasterId(recruitId + ":" + masterId);
            agreementMasterPush.setMasterId(masterId);
            agreementMasterPush.setRecruitId(recruitId);
            agreementMasterPush.setDt(today);
            agreementMasterPush.setPushCountDaily(1L);
            agreementMasterPushRepository.insertSelective(agreementMasterPush);
            return ;
        }


        final String dt = agreementMasterPush.getDt();
        AgreementMasterPush updateAgreementMasterPush = new AgreementMasterPush();
        updateAgreementMasterPush.setId(agreementMasterPush.getId());
        if (org.apache.commons.lang.StringUtils.isEmpty(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else if (!today.equals(dt)) {
            updateAgreementMasterPush.setDt(today);
            updateAgreementMasterPush.setPushCountDaily(1L);
        } else {
            updateAgreementMasterPush.setPushCountDaily(agreementMasterPush.getPushCountDaily() + 1L);
        }
        agreementMasterPushRepository.updateByPrimaryKeySelective(updateAgreementMasterPush);
    }

    private List<CompensateDistribute> getCompensateDistributeList(Integer businessLineId,Long categoryId,Integer appointType,Integer hasPrice,Integer hasCooperationUser){

        List<CompensateDistribute> compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId,"agreement",categoryId,appointType,hasPrice,hasCooperationUser);

        if(CollectionUtils.isEmpty(compensateDistributeList)){
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId,"agreement",categoryId,appointType,2,hasCooperationUser);
        }


        if(CollectionUtils.isEmpty(compensateDistributeList)){
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId,"agreement",categoryId,appointType,hasPrice,2);
        }

        if(CollectionUtils.isEmpty(compensateDistributeList)){
            compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId,"agreement",categoryId,appointType,2,2);
        }

        return compensateDistributeList;

    }


    @Override
    public void afterPropertiesSet(){
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_NEW_MODEL.getCode(), this);
    }

}
