package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.sql.Order;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.AgentAfterDirectAppointPushInfo;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.AgentPushType;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.agent.AddAgentDistributeStrategyRqt;
import com.wanshifu.master.order.push.repository.AgentDistributeStrategyRepository;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商师傅匹配器
 *
 * <AUTHOR>
 * @date 2023-09-13 14:04:00
 */
@Slf4j
@Component("agent_master")
public class AgentMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private AgentInfoEsRepository agentInfoEsRepository;

    @Resource
    private AgentMasterEsRepository agentMasterEsRepository;

    @Resource
    private NewModelMatchService newModelMatchService;


    @Resource
    private PushHandler pushHandler;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private AgentDistributeStrategyRepository agentDistributeStrategyRepository;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    /**
     * 匹配代理商师傅
     *
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {
        if (apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())) {
            log.info("推送合作商师傅，该城市订单不推单");
            return false;
        }

        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();

        if(CollectionUtils.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.AGENT_MASTER.code)){
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if (StringUtils.isNotBlank(pushMode) && (!"agent".equals(pushMode))) {
            return Boolean.FALSE;
        }

        if (orderDetailData.getFourthDivisionId() == 0) {
            //订单无四级地址不走代理商派单
            return Boolean.FALSE;
        }
        if (CollectionUtil.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return Boolean.FALSE;
        }

        return orderDetailData.getBusinessLineId() == 2;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition condition) {
        try {
            log.info("agentMasterMatcher agentInfo orderId:{}", orderDetailData.getMasterOrderId());

            //[代理商][取消指派]
            boolean agentCancelMaster = FieldConstant.AGENT.equals(orderDetailData.getTagName())
                    && RePushSource.orderCancelMaster.toString().equals(orderDetailData.getRePushSource());

            //[代理商][取消指派],推送普通师傅
            if (agentCancelMaster) {
                log.info("agentMasterMatcher {},[代理商][取消指派]直接推普通师傅,getBusinessLineId:{}", orderDetailData.getMasterOrderId(),
                        orderDetailData.getBusinessLineId());
                OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
                orderMasterMatcher.executeMatch(orderDetailData, condition);

                MatchMasterResult masterResult = new MatchMasterResult();
                masterResult.putExtraData("agentDoCancel", true);

                return masterResult;
            }

            //根据订单服务、四级地址匹配代理商

            List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeStrategyRepository.selectByServeIdsAndDivisionId(orderDetailData.getLv3ServeIdList(), orderDetailData.getFourthDivisionId());
            log.info("agentMasterMatcher agentInfo matchAgentStrategy,orderId:{},fourthDivisionId:{},lv3ServeId:{},strategyList:{}",
                    orderDetailData.getMasterOrderId(), orderDetailData.getFourthDivisionId(), JSONUtil.toJsonStr(orderDetailData.getLv3ServeIdList()),
                    JSONUtil.toJsonStr(agentDistributeStrategyList)
            );

            if (CollectionUtil.isEmpty(agentDistributeStrategyList)) {
                log.warn("agentMasterMatcher agentInfo matchAgentStrategy is empty,orderId:{},fourthDivisionId:{},lv3ServeId:{}",
                        orderDetailData.getMasterOrderId(), orderDetailData.getFourthDivisionId(), orderDetailData.getLv3ServeIdList());
                return new MatchMasterResult(new HashSet<>());
            }
            agentDistributeStrategyList = filterByNonEffectiveTime(agentDistributeStrategyList);
            if (CollectionUtil.isEmpty(agentDistributeStrategyList)) {
                log.warn("agentMasterMatcher agentInfo after filterByNonEffectiveTime is empty,orderId:{},fourthDivisionId:{},lv3ServeId:{}",
                        orderDetailData.getMasterOrderId(), orderDetailData.getFourthDivisionId(), orderDetailData.getLv3ServeIdList());
                return new MatchMasterResult(new HashSet<>());
            }

            List<AgentDistributeStrategy> tocStrategyList = agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> "toc".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());
            List<AgentDistributeStrategy> tobGroupStrategyList = agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> "tobGroup".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());

            tocStrategyList = filterByTocAgentUserStatus(tocStrategyList);
            log.info("agentMasterMatcher agentInfo matchTocAgentStrategy,orderId:{},tocStrategyList:{}",
                    orderDetailData.getMasterOrderId(),JSONUtil.toJsonStr(tocStrategyList));

            tobGroupStrategyList = filterByTobGroupAccountStatus(tobGroupStrategyList);
            log.info("agentMasterMatcher agentInfo matchTobGroupAgentStrategy,orderId:{},tobGroupStrategyList:{}",
                    orderDetailData.getMasterOrderId(),JSONUtil.toJsonStr(tobGroupStrategyList));

            List<AgentDistributeStrategy> strategyList = new ArrayList<>(tocStrategyList);
            strategyList.addAll(tobGroupStrategyList);
            if (CollectionUtil.isEmpty(strategyList)) {
                log.warn("agentMasterMatcher agentInfo after filterByStatus is empty,orderId:{},fourthDivisionId:{},lv3ServeId:{}",
                        orderDetailData.getMasterOrderId(), orderDetailData.getFourthDivisionId(), orderDetailData.getLv3ServeIdList());
                return new MatchMasterResult(new HashSet<>());
            }

            String agentPushType;
            boolean isFixedPrice = orderDetailData.getAppointType() == 4;
            boolean hasDirectPush = strategyList.stream().anyMatch(s -> "direct_push".equals(s.getDistributeRule()));
            boolean hasDirectAppoint = strategyList.stream().anyMatch(s -> "direct_appoint".equals(s.getDistributeRule()));

            if (!isFixedPrice) {
                //报价、预付款订单
                if (hasDirectPush) {
                    agentPushType = AgentPushType.PUSH.getType();
                } else {
                    log.info("agentMasterMatcher agentInfo offerOrder no direct push strategy,orderId:{},appointType:{},strategyList:{}",
                            orderDetailData.getMasterOrderId(), orderDetailData.getAppointType(), JSONUtil.toJsonStr(strategyList));
                    return new MatchMasterResult(new HashSet<>());
                }
            } else {
                //一口价订单
                if (!hasDirectAppoint) {
                    //不存在直接指派策略
                    if (hasDirectPush) {
                        agentPushType = AgentPushType.PUSH.getType();
                    } else {
                        log.info("agentMasterMatcher agentInfo fixedPriceOrder no strategy,orderId:{},appointType:{},strategyList:{}",
                                orderDetailData.getMasterOrderId(), orderDetailData.getAppointType(), JSONUtil.toJsonStr(strategyList));
                        return new MatchMasterResult(new HashSet<>());
                    }
                } else {
                    //存在直接指派策略
                    if (!hasDirectPush) {
                        agentPushType = AgentPushType.DIRECT_APPOINT.getType();
                    } else {
                        agentPushType = AgentPushType.DIRECT_APPOINT_PRIORITY.getType();
                    }
                }
            }
            log.info("agentMasterMatcher agentInfo orderId:{},agentPushType:{},strategyList:{}",
                    orderDetailData.getMasterOrderId(), agentPushType, JSONUtil.toJsonStr(strategyList));

            if (AgentPushType.PUSH.getType().equals(agentPushType)) {
                List<AgentDistributeStrategy> directPushStrategyList = strategyList.stream().filter(agentDistributeStrategy -> "direct_push".equals(agentDistributeStrategy.getDistributeRule())).collect(Collectors.toList());
                log.info("agentMasterMatcher agentInfo orderId:{},agentPushType:{},pushStrategyList:{}",
                        orderDetailData.getMasterOrderId(), agentPushType, JSONUtil.toJsonStr(directPushStrategyList));

                MatchMasterResult result = pushMatch(directPushStrategyList, orderDetailData, condition);
                if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getMasterIdSet())) {
                    log.info("agentMasterMatcher agentInfo pushMatch is empty,orderId:{},agentPushType:{}",
                            orderDetailData.getMasterOrderId(), agentPushType);
                    return new MatchMasterResult(new HashSet<>());
                }
                result.putExtraData(AGENT_PUSH_TYPE, agentPushType);
                return result;

            } else if (AgentPushType.DIRECT_APPOINT.getType().equals(agentPushType)) {

                MatchMasterResult result = directAppointMatch(strategyList, orderDetailData, condition);
                if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getMasterIdSet())) {
                    log.info("agentMasterMatcher agentInfo directAppointMatch is empty,orderId:{},agentPushType:{}",
                            orderDetailData.getMasterOrderId(), agentPushType);
                    return new MatchMasterResult(new HashSet<>());
                }
                result.putExtraData(AGENT_PUSH_TYPE, agentPushType);
                return result;

            } else {
                List<AgentDistributeStrategy> directPushStrategyList = strategyList.stream().filter(agentDistributeStrategy -> "direct_push".equals(agentDistributeStrategy.getDistributeRule())).collect(Collectors.toList());
                List<AgentDistributeStrategy> directAppointStrategyList = strategyList.stream().filter(agentDistributeStrategy -> "direct_appoint".equals(agentDistributeStrategy.getDistributeRule())).collect(Collectors.toList());

                //先匹配直接指派策略师傅
                MatchMasterResult result = directAppointMatch(directAppointStrategyList, orderDetailData, condition);
                if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getMasterIdSet())) {
                    //没有直接指派师傅，再匹配定向推送策略师傅
                    log.info("agentMasterMatcher agentInfo orderId:{},agentPushType:{},pushStrategyList:{}",
                            orderDetailData.getMasterOrderId(), agentPushType, JSONUtil.toJsonStr(directPushStrategyList));

                    MatchMasterResult pushMatchResult = pushMatch(directPushStrategyList, orderDetailData, condition);
                    if (Objects.isNull(pushMatchResult) || CollectionUtil.isEmpty(pushMatchResult.getMasterIdSet())) {
                        log.info("agentMasterMatcher agentInfo directAppointPriority is empty,orderId:{},agentPushType:{}",
                                orderDetailData.getMasterOrderId(), agentPushType);
                        return new MatchMasterResult(new HashSet<>());
                    }
                    pushMatchResult.putExtraData(AGENT_PUSH_TYPE, AgentPushType.PUSH.getType());
                    return pushMatchResult;
                }

                //塞入兜底定向推送代理商信息
                result.putExtraData(AGENT_PUSH_TYPE, AgentPushType.DIRECT_APPOINT_PRIORITY.getType());

                List<String> pushStrategyStringList = directPushStrategyList.stream().map(AgentDistributeStrategy::getPushStrategy).collect(Collectors.toList());

                //获取所有策略推单？分钟配置最小值、查看？分钟配置最小值
                List<AddAgentDistributeStrategyRqt.PushStrategy> pushStrategyList = pushStrategyStringList.stream().map(e -> JSON.parseObject(e, AddAgentDistributeStrategyRqt.PushStrategy.class)).collect(Collectors.toList());
                Integer agentPushNoHiredRePushTime = pushStrategyList.stream().map(AddAgentDistributeStrategyRqt.PushStrategy::getAgentPushNoHiredRePushTime).min(Integer::compareTo).orElse(0);
                Integer agentFirstViewNoHiredRePushTime = pushStrategyList.stream().map(AddAgentDistributeStrategyRqt.PushStrategy::getAgentFirstViewNoHiredRePushTime).min(Integer::compareTo).orElse(0);


                List<AgentAfterDirectAppointPushInfo> agentAfterDirectAppointPushInfoList = new ArrayList<>();
                for (AgentDistributeStrategy agentDistributeStrategy : directPushStrategyList) {
                    AgentAfterDirectAppointPushInfo agentAfterDirectAppointPushInfo = new AgentAfterDirectAppointPushInfo();
                    agentAfterDirectAppointPushInfo.setAgentId(agentDistributeStrategy.getAgentId());
                    if ("toc".equals(agentDistributeStrategy.getMasterSourceType())) {
                        agentAfterDirectAppointPushInfo.setAgentType("toc");
                    } else if ("tobGroup".equals(agentDistributeStrategy.getMasterSourceType())) {
                        agentAfterDirectAppointPushInfo.setAgentType("tobGroup");
                    }
                    agentAfterDirectAppointPushInfo.setAgentPushNoHiredRePushTime(agentPushNoHiredRePushTime);
                    agentAfterDirectAppointPushInfo.setAgentFirstViewNoHiredRePushTime(agentFirstViewNoHiredRePushTime);
                    agentAfterDirectAppointPushInfoList.add(agentAfterDirectAppointPushInfo);
                }
                if (CollectionUtil.isNotEmpty(agentAfterDirectAppointPushInfoList)) {
                    result.putExtraData(AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO, agentAfterDirectAppointPushInfoList);
                }
                return result;
            }


        } catch (Exception e) {
            e.printStackTrace();
            log.error(String.format("订单匹配代理商师傅失败,orderDetailData:%s", JSON.toJSONString(orderDetailData)));
        }
        return new MatchMasterResult(new HashSet<>());
    }

    public static final String AGENT_MASTER_LIST = "agentMasterList";
    private static final String AGENT_ID = "agentId";
    private static final String MASTER_LIST = "masterList";
    private static final String AGENT_TYPE = "agentType";
    private static final String AGENT_BELONGS_STRATEGY_ID = "agentBelongsStrategyId";
    private static final String AGENT_BELONGS_STRATEGY_DISTRIBUTE_PRIORITY = "agentBelongsStrategyDistributePriority";


    /**
     *directAppointPriority:直接指派优先
     * directAppoint:直接指派
     * push:定向推送
     */
    public static final String AGENT_PUSH_TYPE = "agentPushType";
    /**
     * 代理商推送类型为push时才有数据，定向推送推单后无人接单重推时间配置
     */
    public static final String AGENT_PUSH_NO_HIRED_RE_PUSH_TIME = "agentPushNoHiredRePushTime";
    /**
     * 代理商推送类型为push时才有数据，定向推送推单首次查看后无人接单重推时间配置
     */
    public static final String AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME = "agentFirstViewNoHiredRePushTime";

    /**
     * 直接指派优先的定向推送代理商信息
     */
    public static final String AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO = "agentAfterDirectAppointPushInfo";



    /**
     * 直接指派匹配
     *
     * @param agentPushStrategyList
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private MatchMasterResult directAppointMatch(List<AgentDistributeStrategy> agentPushStrategyList,
                                                 OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        if (CollectionUtil.isEmpty(agentPushStrategyList)) {
            return null;
        }

        Map<Long, AgentDistributeStrategy> agentStrategyMap = agentPushStrategyList.stream().collect(Collectors.toMap(AgentDistributeStrategy::getAgentId, each -> each, (value1, value2) -> value1));

        List<AgentDistributeStrategy> tocStrategyList = agentPushStrategyList.stream().filter(agentDistributeStrategy -> "toc".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());
        List<AgentDistributeStrategy> tobGroupStrategyList = agentPushStrategyList.stream().filter(agentDistributeStrategy -> "tobGroup".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());


        Set<Long> agentIdList = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(tocStrategyList)) {
            agentIdList = tocStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toSet());
        }
        Set<Long> tobGroupAgentIdList = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(tobGroupStrategyList)) {
            tobGroupAgentIdList = tobGroupStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toSet());
        }

        return buildMatchMasterResult(agentIdList, tobGroupAgentIdList, agentStrategyMap, orderDetailData, masterMatchCondition);
    }


    /**
     * 定向推送匹配
     *
     * @return
     */
    private MatchMasterResult pushMatch(List<AgentDistributeStrategy> directPushStrategyList,
                                        OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        if (CollectionUtil.isEmpty(directPushStrategyList)) {
            return null;
        }
        List<String> pushStrategyStringList = directPushStrategyList.stream().map(AgentDistributeStrategy::getPushStrategy).collect(Collectors.toList());

        //获取所有策略推单？分钟配置最小值、查看？分钟配置最小值
        List<AddAgentDistributeStrategyRqt.PushStrategy> pushStrategyList = pushStrategyStringList.stream().map(e -> JSON.parseObject(e, AddAgentDistributeStrategyRqt.PushStrategy.class)).collect(Collectors.toList());
        Integer agentPushNoHiredRePushTime = pushStrategyList.stream().map(AddAgentDistributeStrategyRqt.PushStrategy::getAgentPushNoHiredRePushTime).min(Integer::compareTo).orElse(0);
        Integer agentFirstViewNoHiredRePushTime = pushStrategyList.stream().map(AddAgentDistributeStrategyRqt.PushStrategy::getAgentFirstViewNoHiredRePushTime).min(Integer::compareTo).orElse(0);


        Map<Long, AgentDistributeStrategy> agentStrategyMap = directPushStrategyList.stream().collect(Collectors.toMap(AgentDistributeStrategy::getAgentId, each -> each, (value1, value2) -> value1));

        List<AgentDistributeStrategy> tocStrategyList = directPushStrategyList.stream().filter(agentDistributeStrategy -> "toc".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());
        List<AgentDistributeStrategy> tobGroupStrategyList = directPushStrategyList.stream().filter(agentDistributeStrategy -> "tobGroup".equals(agentDistributeStrategy.getMasterSourceType())).collect(Collectors.toList());


        Set<Long> agentIdList = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(tocStrategyList)) {
            agentIdList = tocStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toSet());
        }
        Set<Long> tobGroupAgentIdList = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(tobGroupStrategyList)) {
            tobGroupAgentIdList = tobGroupStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toSet());
        }

        MatchMasterResult matchMasterResult = buildMatchMasterResult(agentIdList, tobGroupAgentIdList, agentStrategyMap, orderDetailData, masterMatchCondition);
        if (Objects.isNull(matchMasterResult)) {
            return null;
        }
        matchMasterResult.putExtraData(AGENT_PUSH_NO_HIRED_RE_PUSH_TIME, agentPushNoHiredRePushTime);
        matchMasterResult.putExtraData(AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME, agentFirstViewNoHiredRePushTime);
        return matchMasterResult;
    }


    public MatchMasterResult buildMatchMasterResult(Set<Long> agentIdList, Set<Long> tobGroupAgentIdList,
                                                     Map<Long, AgentDistributeStrategy> agentStrategyMap,
                                                     OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition) {

        if (CollectionUtil.isEmpty(agentIdList) && CollectionUtil.isEmpty(tobGroupAgentIdList)) {
            return null;
        }

        Set<String> masterIds = new HashSet<>();
        JSONArray agentMatchArray = new JSONArray();

        for (Long agentId : agentIdList) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("agentId", agentId));
            boolQueryBuilder.must(QueryBuilders.termQuery("cooperationStatus", 0));
            boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

            log.info("match agentMaster request:" + boolQueryBuilder.toString());
            EsResponse<AgentMasterIndex> esResponse = agentMasterEsRepository.search(boolQueryBuilder, new Pageable(1, 200), null);
            if (Objects.nonNull(esResponse) || CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                Set<String> masterIdSet = esResponse.getDataList().stream().map(agentInfo -> String.valueOf(agentInfo.getMasterId())).collect(Collectors.toSet());

                masterIds.addAll(masterIdSet);
                //添加代理商匹配信息
                final JSONObject agentMatchValue = new JSONObject();
                agentMatchValue.put(AGENT_ID, agentId.toString());
                agentMatchValue.put(AGENT_TYPE, "toc");
                if (Objects.nonNull(agentStrategyMap) && agentStrategyMap.containsKey(agentId)) {
                    agentMatchValue.put(AGENT_BELONGS_STRATEGY_ID, agentStrategyMap.get(agentId).getStrategyId());
                    agentMatchValue.put(AGENT_BELONGS_STRATEGY_DISTRIBUTE_PRIORITY, agentStrategyMap.get(agentId).getDistributePriority());
                }
                agentMatchValue.put(MASTER_LIST, StringUtils.join(masterIdSet, ","));
                agentMatchArray.add(agentMatchValue);
            }

        }

        for (Long tobGroupMasterId : tobGroupAgentIdList) {
            masterIds.add(tobGroupMasterId.toString());
            //添加B端团队代理商匹配信息
            final JSONObject agentMatchValue = new JSONObject();
            agentMatchValue.put(AGENT_ID, tobGroupMasterId.toString());
            agentMatchValue.put(AGENT_TYPE, "tobGroup");
            if (Objects.nonNull(agentStrategyMap) && agentStrategyMap.containsKey(tobGroupMasterId)) {
                agentMatchValue.put(AGENT_BELONGS_STRATEGY_ID, agentStrategyMap.get(tobGroupMasterId).getStrategyId());
                agentMatchValue.put(AGENT_BELONGS_STRATEGY_DISTRIBUTE_PRIORITY, agentStrategyMap.get(tobGroupMasterId).getDistributePriority());
            }
            agentMatchValue.put(MASTER_LIST, tobGroupMasterId.toString());
            agentMatchArray.add(agentMatchValue);
        }

        if (CollectionUtils.isNotEmpty(masterIds)) {
            masterIds = pushHandler.filterByPushLimitRule(orderDetailData, null, masterIds, "agent", null);
        }

        if (CollectionUtils.isNotEmpty(masterIds) && CollectionUtils.isNotEmpty(masterMatchCondition.getOrderPushEliminateMasterIds())) {
            masterIds.removeAll(masterMatchCondition.getOrderPushEliminateMasterIds());
        }

        if (CollectionUtils.isNotEmpty(masterIds)) {
            List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
            if (CollectionUtil.isNotEmpty(agreementMasterList)) {
                List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
                masterIds.removeAll(agreementMasterIdList);
            }

        }
        if (CollectionUtil.isEmpty(masterIds)) {
            return null;
        }


        MatchMasterResult matchMasterResult = new MatchMasterResult(masterIds);
        matchMasterResult.putExtraData(AGENT_MASTER_LIST, agentMatchArray);

        return matchMasterResult;
    }


    /**
     * 过滤B端团队师傅账号状态
     *
     * @param agentDistributeStrategyList
     * @return
     */
    private List<AgentDistributeStrategy> filterByTobGroupAccountStatus(List<AgentDistributeStrategy> agentDistributeStrategyList) {
        if (CollectionUtil.isEmpty(agentDistributeStrategyList)) {
            return new ArrayList<>();
        }
        List<Long> tobGroupAccountIdList = agentDistributeStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toList());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.filter(QueryBuilders.termsQuery("masterId", tobGroupAccountIdList));

        boolQueryBuilder.filter(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));

        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(1, 200), null);

        List<String> masterIdList = Lists.newArrayList();
        if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
            masterIdList = esResponse.getDataList().stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toList());
        }

        List<String> finalMasterIdList = masterIdList;
        return agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> finalMasterIdList.contains(String.valueOf(agentDistributeStrategy.getAgentId()))).collect(Collectors.toList());
    }

    /**
     * c端代理商根据代理商用户状态过滤
     *
     * @param agentDistributeStrategyList
     */
    private List<AgentDistributeStrategy> filterByTocAgentUserStatus(List<AgentDistributeStrategy> agentDistributeStrategyList) {

        if (CollectionUtil.isEmpty(agentDistributeStrategyList)) {
            return new ArrayList<>();
        }


        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("useStatus", 0));
        boolQueryBuilder.must(QueryBuilders.termsQuery("agentId", agentDistributeStrategyList.stream().map(AgentDistributeStrategy::getAgentId).collect(Collectors.toList())));

        log.info("match agentInfo request:" + boolQueryBuilder.toString());
        EsResponse<AgentInfoIndex> esResponse = agentInfoEsRepository.search(boolQueryBuilder, new Pageable(1, 200), null);

        List<String> agentIdList = Lists.newArrayList();
        if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
            agentIdList = esResponse.getDataList().stream().map(agentInfo -> String.valueOf(agentInfo.getAgentId())).collect(Collectors.toList());
        }
        List<String> finalAgentIdList = agentIdList;
        return agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> finalAgentIdList.contains(String.valueOf(agentDistributeStrategy.getAgentId()))).collect(Collectors.toList());
    }


    /**
     * 过滤不在生效时间段的代理商
     *
     * @param agentDistributeStrategyList
     * @return
     */
    private List<AgentDistributeStrategy> filterByNonEffectiveTime(List<AgentDistributeStrategy> agentDistributeStrategyList) {

        if (CollectionUtils.isEmpty(agentDistributeStrategyList)) {
            return agentDistributeStrategyList;
        }

        List<AgentDistributeStrategy> nonEffectiveTimeStrategyList = agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> StringUtils.isNotBlank(agentDistributeStrategy.getNonEffectiveTime())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nonEffectiveTimeStrategyList)) {
            return agentDistributeStrategyList;
        }

        List<AgentDistributeStrategy> agentDistributeStrategies = nonEffectiveTimeStrategyList.stream().filter(agentDistributeStrategy -> {

            List<AddAgentDistributeStrategyRqt.NonEffectiveTime> nonEffectiveTimeList = JSON.parseArray(agentDistributeStrategy.getNonEffectiveTime(), AddAgentDistributeStrategyRqt.NonEffectiveTime.class);

            for (AddAgentDistributeStrategyRqt.NonEffectiveTime nonEffectiveTime : nonEffectiveTimeList) {
                if (DateFormatterUtil.isBetweenPeriodTime(nonEffectiveTime.getNonEffectiveStartTime(), nonEffectiveTime.getNonEffectiveEndTime())) {
                    return true;
                }
            }

            return false;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(agentDistributeStrategies)) {
            return agentDistributeStrategyList;
        }

        Set<String> filterAgentIdSet = agentDistributeStrategies.stream().map(AgentDistributeStrategy::getAgentId).map(String::valueOf).collect(Collectors.toSet());

        return agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> !filterAgentIdSet.contains(String.valueOf(agentDistributeStrategy.getAgentId()))).collect(Collectors.toList());
    }


    @Deprecated
    public MatchQueryBuilder stringMatchQueryBuilderWithMinim(String fieldName, String value, Operator operator, Integer minimumShouldMatch) {
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName, value);
        //设置要匹配的列。
        matchQueryBuilder.operator(operator);
        matchQueryBuilder.minimumShouldMatch(String.valueOf(minimumShouldMatch));
        return matchQueryBuilder;
    }


    @Deprecated
    private MatchMasterResult getAgentMasterIds(OrderDetailData orderDetailData, List<String> agentSet, MasterMatchCondition masterMatchCondition) {
        Set<String> masterIds = new HashSet<>();
        JSONArray agentMatchArray = new JSONArray();
        for (String agentString : agentSet) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("agentId", agentString));
            boolQueryBuilder.must(QueryBuilders.termQuery("cooperationStatus", 0));
            boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

            log.info("match agentMaster request:" + boolQueryBuilder.toString());
            EsResponse<AgentMasterIndex> esResponse = agentMasterEsRepository.search(boolQueryBuilder);
            // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
            if (Objects.nonNull(esResponse) || CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                Set<String> masterIdSet = esResponse.getDataList().stream().map(agentInfo -> String.valueOf(agentInfo.getMasterId())).collect(Collectors.toSet());

                masterIds.addAll(masterIdSet);
                //添加代理商匹配信息
                final JSONObject agentMatchValue = new JSONObject();
                agentMatchValue.put(AGENT_ID, agentString);
                agentMatchValue.put(MASTER_LIST, StringUtils.join(masterIdSet, ","));
                agentMatchArray.add(agentMatchValue);
            }

        }

        if (CollectionUtils.isNotEmpty(masterIds)) {
            masterIds = pushHandler.filterByPushLimitRule(orderDetailData, null, masterIds, "agent", null);
        }

        for (Object obj : agentMatchArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String masterList = jsonObject.getString(MASTER_LIST);
            if (StringUtils.isNotBlank(masterList)) {
                List<String> masterIdList = Arrays.asList(masterList.split(",")).stream().filter(masterId -> masterList.contains(masterId)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(masterIdList)) {
                    jsonObject.put(MASTER_LIST, StringUtils.join(masterIdList, ","));
                } else {
                    jsonObject.put(MASTER_LIST, "");
                }
            }
        }
        MatchMasterResult matchMasterResult = new MatchMasterResult(masterIds);
        matchMasterResult.putExtraData(AGENT_MASTER_LIST, agentMatchArray);

        if (CollectionUtils.isNotEmpty(matchMasterResult.getMasterIdSet()) && CollectionUtils.isNotEmpty(masterMatchCondition.getOrderPushEliminateMasterIds())) {
            matchMasterResult.getMasterIdSet().removeAll(masterMatchCondition.getOrderPushEliminateMasterIds());
        }


        if (CollectionUtils.isNotEmpty(matchMasterResult.getMasterIdSet())) {
            List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
            if (CollectionUtils.isEmpty(agreementMasterList)) {
                return matchMasterResult;
            }
            List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
            matchMasterResult.getMasterIdSet().removeAll(agreementMasterIdList);
        }

        return matchMasterResult;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {


    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        try {
            if (Objects.nonNull(matchMasterResult)
                    && Objects.nonNull(matchMasterResult.getExtraData())
                    && Objects.nonNull(matchMasterResult.getExtraData().getBoolean("agentDoCancel"))
                    && matchMasterResult.getExtraData().getBoolean("agentDoCancel")) {
                //代理商取消指派，match时已直接走普通推单，跳过后续推单
                return true;
            }
            if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
                return false;
            }

            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, masterIdSet.size(), new Date(timestamp), PushMode.AGENT_MASTER.code);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
            commonFeature.put(FieldConstant.PUSH_MODE, "agent");
            commonFeature.put(AgentMasterMatcher.AGENT_MASTER_LIST, JSON.toJSONString(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_MASTER_LIST)));
            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
            commonFeature.put(AgentMasterMatcher.AGENT_PUSH_TYPE, matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_PUSH_TYPE));
            if (Objects.nonNull(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME))) {
                commonFeature.put(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME, matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_PUSH_NO_HIRED_RE_PUSH_TIME));
            }
            if (Objects.nonNull(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME))) {
                commonFeature.put(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME, matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_FIRST_VIEW_NO_HIRED_RE_PUSH_TIME));
            }
            if (Objects.nonNull(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO))) {
                commonFeature.put(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO, matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_AFTER_DIRECT_APPOINT_PUSH_INFO));
            }
            pushControllerFacade.directPush(orderDetailData, orderVersion, matchMasterResult.getMasterIdSet(), commonFeature);


            return true;
        } catch (Exception e) {
            log.error(String.format("执行代理商推单失败,orderDetailData:%s,matchMasterResult:%s", JSON.toJSONString(orderDetailData), JSON.toJSONString(matchMasterResult)), e);
        }
        return false;

    }


}
