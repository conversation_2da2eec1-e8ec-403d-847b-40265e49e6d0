package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.AgreementMasterMatch;
import com.wanshifu.master.order.push.mapper.AgreementMasterMatchMapper;
import org.springframework.stereotype.Repository;


import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class AgreementMasterMatchRepository extends BaseRepository<AgreementMasterMatch> {


    @Resource
    private AgreementMasterMatchMapper agreementMasterMatchMapper;

    public AgreementMasterMatch selectByAgreementMasterId(Long orderId,String agreementMasterId){
        AgreementMasterMatch agreementMasterMatch = new AgreementMasterMatch();
        agreementMasterMatch.setAgreementMasterId(agreementMasterId);
        agreementMasterMatch.setOrderId(orderId);
        return CollectionUtils.getFirstSafety(this.select(agreementMasterMatch));
    }


    public AgreementMasterMatch selectByOrderIdAndOrderVersion(Long orderId,String orderVersion){
        AgreementMasterMatch agreementMasterMatch = new AgreementMasterMatch();
        agreementMasterMatch.setOrderId(orderId);
        agreementMasterMatch.setOrderVersion(orderVersion);
        return CollectionUtils.getFirstSafety(this.select(agreementMasterMatch));
    }


    public AgreementMasterMatch selectByOrderIdAndMasterId(Long orderId,Long masterId){
        AgreementMasterMatch agreementMasterMatch = new AgreementMasterMatch();
        agreementMasterMatch.setOrderId(orderId);
        agreementMasterMatch.setMasterId(masterId);
        agreementMasterMatch.setIsAutoReceiveSucc(1);
        return CollectionUtils.getFirstSafety(this.select(agreementMasterMatch));
    }

    public List<AgreementMasterMatch> selectList(String orderNo, Long masterId, Date orderCreateTimeStart, Date orderCreateTimeEnd) {
        return agreementMasterMatchMapper.selectList(orderNo, masterId, orderCreateTimeStart, orderCreateTimeEnd);
    }

}
