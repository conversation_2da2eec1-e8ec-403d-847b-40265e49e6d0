package com.wanshifu.master.order.push.mapper;


import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;

import java.util.List;

public interface OrderMatchRouteMapper extends IBaseCommMapper<OrderMatchRoute> {

    List<OrderMatchRoute> selectList(OrderMatchRouteListRqt rqt);

}
