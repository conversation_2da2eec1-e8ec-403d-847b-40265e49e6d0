package com.wanshifu.master.order.push.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.CreatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.ListPushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.UpdatePushExportTaskRqt;
import com.wanshifu.master.order.push.repository.PushExportTaskRepository;
import com.wanshifu.master.order.push.service.PushExportTaskService;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 21:45
 */
@Service
public class PushExportTaskServiceImpl implements PushExportTaskService {

    @Resource
    private PushExportTaskRepository pushExportTaskRepository;


    @Override
    public Long createPushExportTask(CreatePushExportTaskRqt createPushExportTaskRqt) {
        if (Objects.isNull(createPushExportTaskRqt)) {
            throw new BusException("创建导出任务失败，参数有误！");
        }
        PushExportTask pushExportTask = new PushExportTask();
        pushExportTask.setExportDataTypeName(createPushExportTaskRqt.getExportDataTypeName());
        pushExportTask.setExportDataName(createPushExportTaskRqt.getExportDataName());
        pushExportTask.setFileName(createPushExportTaskRqt.getFileName());
        pushExportTask.setCreateTime(new Date());
        pushExportTask.setStatus(createPushExportTaskRqt.getStatus());


        pushExportTaskRepository.insertSelective(pushExportTask);

        return pushExportTask.getId();
    }

    @Override
    public void updatePushExportTask(UpdatePushExportTaskRqt updatePushExportTaskRqt) {
        if (Objects.isNull(updatePushExportTaskRqt)
                || Objects.isNull(updatePushExportTaskRqt.getId())) {
            throw new BusException("更新导出任务失败，参数有误！");
        }
        PushExportTask pushExportTask = new PushExportTask();
        pushExportTask.setId(updatePushExportTaskRqt.getId());
        pushExportTask.setUpdateTime(new Date());

        if (!Strings.isNullOrEmpty(updatePushExportTaskRqt.getStatus())) {
            pushExportTask.setStatus(updatePushExportTaskRqt.getStatus());
        }

        if (!Strings.isNullOrEmpty(updatePushExportTaskRqt.getUrl())) {
            pushExportTask.setUrl(updatePushExportTaskRqt.getUrl());

        }

        if (!Strings.isNullOrEmpty(updatePushExportTaskRqt.getFailReason())) {
            pushExportTask.setFailReason(updatePushExportTaskRqt.getFailReason());
        }

        pushExportTaskRepository.updateByPrimaryKeySelective(pushExportTask);
    }

    @Override
    public SimplePageInfo<PushExportTask> listPushExportTask(ListPushExportTaskRqt listPushExportTaskRqt) {
        SimplePageInfo<PushExportTask> result = new SimplePageInfo<>();

        Page page = PageHelper.startPage(listPushExportTaskRqt.getPageNum(), listPushExportTaskRqt.getPageSize());

        List<PushExportTask> pushExportTaskList = pushExportTaskRepository.selectList(listPushExportTaskRqt.getCreateTimeStart(), listPushExportTaskRqt.getCreateTimeEnd());
        result.setPages(page.getPages());
        result.setPageNum(page.getPageNum());
        result.setTotal(page.getTotal());
        result.setPageSize(page.getPageSize());
        result.setList(pushExportTaskList);
        return result;
    }
}
