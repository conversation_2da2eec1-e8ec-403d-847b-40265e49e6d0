package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.common.enums.AttributeKeyEnum;
import com.wanshifu.fee.center.api.BizRuleMappingApi;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.api.FeeTemplateApi;
import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import com.wanshifu.fee.center.domain.document.CalculateRuleData;
import com.wanshifu.fee.center.domain.document.FeeRule;
import com.wanshifu.fee.center.domain.document.FeeTemplate;
import com.wanshifu.fee.center.domain.dto.AccountInfo;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.*;
import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import com.wanshifu.fee.center.domain.response.ApplyCalculateResp;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateResp;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.activity.domain.api.request.MasterActivityRewardRqt;
import com.wanshifu.iop.activity.domain.api.response.MasterActivityRewardResp;
import com.wanshifu.iop.activity.service.api.OutBusinessServiceApi;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.api.MasterOrderApi;
import com.wanshifu.master.order.push.domain.api.response.GetPersonaGroupIdsByUserIdResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetPersonaGroupIdsByUserIdRqt;
import com.wanshifu.master.order.push.domain.dto.AutoReceiveMaster;
import com.wanshifu.master.order.push.domain.dto.MasterAutoReceiverRqt;
import com.wanshifu.master.order.push.domain.dto.ScorerMaster;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.po.OrderLock;
import com.wanshifu.master.order.push.domain.rqt.GrabOrderValidRqt;
import com.wanshifu.master.order.push.domain.rqt.MasterOfferPriceRqt;
import com.wanshifu.master.order.push.repository.MasterAutoReceiveOrderRepository;
import com.wanshifu.master.order.push.repository.OrderLockRepository;
import com.wanshifu.master.order.push.service.MasterAutoReceiveOrderService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.api.grabdefinite.GrabDefiniteResourceApi;
import com.wanshifu.order.offer.api.offer.OfferModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.offer.OfferPriceListResp;
import com.wanshifu.order.offer.domains.bo.service.ServiceInfo;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.push.response.BatchOrderPushResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.ThreadFactoryImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 师傅自动接单
 * <AUTHOR>
 * @date 2023-08-14
 */
@Service
@Slf4j
public class MasterAutoReceiveOrderServiceImpl implements MasterAutoReceiveOrderService {

//    @Resource
//    private DataCenterRepository dataCenterRepository;

    @Resource
    private FeeRuleApi feeRuleApi;

    @Resource
    private BizRuleMappingApi bizRuleMappingApi;

    @Resource
    private MasterOrderApi masterOrderApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Value("${master.auto.grab.order.enable}")
    private Boolean masterAutoGrabOrderEnable;

    @Value("${master.auto.offer.price.enable}")
    private Boolean masterAutoOfferPriceEnable;

    @Resource
    private OrderServiceAttributeService orderServiceAttributeService;

    @Resource
    private Tools tools;


    @Resource
    private FeeTemplateApi feeTemplateApi;

    @Resource
    private MasterAutoReceiveOrderRepository masterAutoReceiveOrderRepository;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private FeatureRepository featureRepository;

    /**
     * 计费场景编码
     */
    private final String SCENE_CODE = "master_auto_offer_price";

    /**
     * 师傅ID业务字段
     */
    private final String SERVICE_ID = "serviceId";

    private final Integer MASTER_SCORE_TOP = 50;


    @Resource
    private OrderLockRepository orderLockRepository;

    @Resource
    private RocketMqSendService rocketMqSendService;


    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private GrabDefiniteResourceApi grabDefiniteResourceApi;

    @Resource
    private AddressApi addressApi;

    @Value("${master.auto.price.switch:on}")
    private String masterAutoPriceSwitch;

    @Value("${auto.receive.user.blacklist:0}")
    private String autoReceiveUserBlacklist;

    @Resource
    private OutBusinessServiceApi outBusinessServiceApi;
    
    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;


    @Value("${guidePrice.lowerLimit.percent:20}")
    private Long guidePriceLowerLimitPercent;

    @Value("${guidePrice.upperLimit.percent:20}")
    private Long guidePriceUpperLimitPercent;

    @Resource
    private OfferModuleResourceApi offerModuleResourceApi;


    @Override
    public Integer autoReceive(OrderBase orderBase, OrderGrab orderGrab, List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,
                               List<ScorerMaster> scorerMasterList, String orderDistributeRule, Long cityDivisionId, List<BatchOrderPushResp> batchOrderPushRespList){


//        if(!checkAutoReceiveCondition(orderBase,orderGrab,orderServiceAttributeInfoList)){
//            log.info(String.format("自动接单校验订单条件不通过，orderId:%d",orderBase.getOrderId()));
//            return 0;
//        }

        Long orderId = orderBase.getOrderId();

        OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderId, "");
        OrderInitFee orderInitFee = orderBaseComposite.getOrderInitFee();

        if(orderGrab.getAppointType().equals(AppointType.DEFINITE_PRICE.value) && (orderInitFee == null || orderInitFee.getDefiniteServeFee().compareTo(BigDecimal.ZERO) <= 0)){
            return 0;
        }

        List<AutoReceiveMaster> autoReceiveMasterList = "on".equals(masterAutoPriceSwitch) ? getAutoReceivePrice(orderBase,orderGrab,orderServiceAttributeInfoList,scorerMasterList) :
                getAutoReceiveMasterList(orderBase,orderServiceAttributeInfoList,scorerMasterList);

        log.info(String.format("orderId:%d,autoReceiveMasterList:%s",orderBase.getOrderId(),JSON.toJSONString(autoReceiveMasterList)));

        masterAutoReceiveOrderRepository.insertAutoReceiveOrder(orderId,scorerMasterList,autoReceiveMasterList,null);

        this.autoReceiveOrder(orderBase,orderGrab,orderInitFee,orderServiceAttributeInfoList,autoReceiveMasterList,orderDistributeRule,cityDivisionId,
                batchOrderPushRespList);

        return 1;
    }


    /**
     * 校验自动接单开启条件
     * @param orderBase
     * @param orderGrab
     * @return
     */
    @Override
    public boolean checkAutoReceiveCondition(OrderBase orderBase, OrderGrab orderGrab, List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,
                                             OrderExtraData orderExtraData){
        /**     工作项:22072039
         *      远康:仅报价招标的订单过滤掉这几条
         *      * 符合以下条件订单才参与自动报价
         *      * 1、平台报价招标B端订单-成品家具:1 家庭业务:2 创新业务:3
         *      * 2、订单信息中无描述特殊要求、订单备注无描述内容
         *      * 3、订单无好评返现、加急、带配件、代付运费
         */


        if(!checkAutoReceiveUserBlacklist(orderBase)){
            return false;
        }

        if(!OrderStatus.TRADING.code.equals(orderBase.getOrderStatus())){
            return false;
        }

        if(Objects.isNull(orderGrab)){
            return false;
        }


        Integer confirmServeStatus = orderGrab.getConfirmServeStatus();
        Long hireMasterId = orderGrab.getHireMasterId();


        if(Objects.nonNull(confirmServeStatus) && confirmServeStatus == 1 && Objects.nonNull(hireMasterId) && hireMasterId > 0L){
            return false;
        }

        Integer appointType = orderGrab.getAppointType();
        if (AppointType.OPEN.value.equals(appointType)) {
            final Integer businessLineId = orderBase.getBusinessLineId();
            //B端
            if (1!=businessLineId) {
                return false;
            }

            if (CollectionUtils.isNotEmpty(orderServiceAttributeInfoList)) {
                final boolean match = orderServiceAttributeInfoList.stream().anyMatch(row -> {
                            final ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(row);
                            final String specialRequire = orderServiceAttributeService.getOrderServiceInfoKeySingleValue(
                                    AttributeKeyEnum.special_require, serviceInfo, false, null);
                            return StringUtils.isNotEmpty(specialRequire);
                        }
                );
                //有特殊要求
                if (match) {
                    return false;
                }
            }
            //有备注
            final String buyerNote = orderExtraData.getBuyerNote();
            if (StringUtils.isNotEmpty(buyerNote)) {
                return false;
            }

            //加急
            final Integer emergencyOrderFlag = orderExtraData.getEmergencyOrderFlag();
            if (1==emergencyOrderFlag) {
                return false;
            }

            //带配件
            final Integer isParts = orderExtraData.getIsParts();
            if (1==isParts) {
                return false;
            }

            final OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderBase.getOrderId(), orderBase.getOrderNo());
            //好评返现
            final OrderRateAward orderRateAward = orderBaseComposite.getOrderRateAward();
            if (orderRateAward!=null&&orderRateAward.getRateAwardFee()!=null
                    &&orderRateAward.getRateAwardFee().compareTo(BigDecimal.ZERO)>0) {
                return false;
            }

            //代付运费
            final OrderInitFee orderInitFee = orderBaseComposite.getOrderInitFee();
            if (orderInitFee!=null&&orderInitFee.getInitLogisticsFee()!=null
                    &&orderInitFee.getInitLogisticsFee().compareTo(BigDecimal.ZERO)>0) {
                return false;
            }


        }



        if((!AppointType.OPEN.value.equals(appointType)) && (!AppointType.DEFINITE_PRICE.value.equals(appointType))){
            return Boolean.FALSE;
        }


        if((appointType.equals(AppointType.OPEN) && (!masterAutoOfferPriceEnable)) || (appointType.equals(AppointType.DEFINITE_PRICE) && (!masterAutoGrabOrderEnable)) ){
            return Boolean.FALSE;
        }




        if(!this.isDynamicConfiguration(orderBase)){
            return Boolean.FALSE;
        }

        if(orderBase.getFourthDivisionId() == null || orderBase.getFourthDivisionId() <= 0){
            return Boolean.FALSE;
        }

        if(CollectionUtils.isEmpty(orderServiceAttributeInfoList)){
            return Boolean.FALSE;
        }

        //校验订单锁单次数是否达到上限
        OrderLock orderLock = orderLockRepository.selectByCityDivisionId(addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        if(Objects.nonNull(orderLock)){
            Integer lockedGrabCount = grabDefiniteResourceApi.orderLockedGrabCount(orderBase.getOrderId());
            if(Objects.nonNull(lockedGrabCount) && lockedGrabCount >= orderLock.getOrderGrabLockCnt()){
                return false;
            }
        }
        return Boolean.TRUE;
    }
    private boolean checkAutoReceiveUserBlacklist(OrderBase orderBase){

        if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            return true;
        }

        if("0".equals(autoReceiveUserBlacklist)){
            return true;
        }

        if("all".equals(autoReceiveUserBlacklist)){
            return false;
        }


        final List<String> forbiddenUserList =
                new ArrayList<>(Arrays.asList(autoReceiveUserBlacklist.split(",")));
        if (forbiddenUserList.contains(String.valueOf(orderBase.getAccountId()))) {
            return false;
        }


        return true;


    }

    private String getSkuValue(Set<String> skuList,OrderServiceAttributeInfo orderServiceAttributeInfo){
        ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(orderServiceAttributeInfo);
        for(String sku : skuList){
            String skuValue = orderServiceAttributeService.getValueByAttributePathNo(serviceInfo,sku);
            if(StringUtils.isNotBlank(skuValue)){
                return skuValue;
            }
        }
        return null;
    }

    private List<AutoReceiveMaster>  getAutoReceivePrice(OrderBase orderBase,OrderGrab orderGrab,
                                                 List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,List<ScorerMaster> scorerMasterList){

        ApplyOrderCalculateBatchReq batchReq = new ApplyOrderCalculateBatchReq();
        ApplyOrderCalculateReq calculateReq = new ApplyOrderCalculateReq();
        Long orderId = orderBase.getOrderId();
        List<String> sceneCode = Collections.singletonList("master_auto_offer_price");
        calculateReq.setSceneCode(sceneCode);
        com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
        orderBaseParam.setOrderId(orderId);
        orderBaseParam.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        orderBaseParam.setCreateTime(orderBase.getCreateTime());
        calculateReq.setOrderBase(orderBaseParam);
        List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(com.alibaba.fastjson.JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(),CalculateServiceInfo.class)));
        calculateReq.setServiceInfos(serviceInfos);
        AccountInfo from = new AccountInfo();
        from.setAccountId(orderBase.getAccountId());
        from.setAccountType(orderBase.getAccountType());
        calculateReq.setFrom(from);

        AddressInfo addressInfo = new AddressInfo();
        Long divisionId = orderBase.getFourthDivisionId();
        addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);
        Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
        BeanUtils.copyProperties(address,addressInfo);
        calculateReq.setAddressInfo(addressInfo);

        Integer appointType = orderGrab.getAppointType();

        if(AccountType.USER.code.equals(orderBase.getAccountType())){
            if(AppointType.OPEN.value.equals(appointType)){
                calculateReq.setFromSceneCode("master_auto_offer_price");
            }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
                calculateReq.setIsMappingPricing(false);
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            calculateReq.setFromSceneCode("master_auto_offer_price");
        }

        calculateReq.setNeedIntegrityValidation(false);


        batchReq.setApplyOrderCalculateReq(calculateReq);

        List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();

        scorerMasterList.forEach(scorerMaster -> {
            BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
            bizRuleBatchReq.setBizId(String.valueOf(scorerMaster.getMasterId()));
            bizRuleBatchReqList.add(bizRuleBatchReq);

        });

        batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);
        log.info("applyOrderCalculateBatchReq:" + com.alibaba.fastjson.JSON.toJSONString(batchReq));
        List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
        log.info("applyOrderCalculateBatchResp:" + com.alibaba.fastjson.JSON.toJSONString(applyOrderCalculateBatchRespList));
        List<ApplyOrderCalculateBatchResp> successList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess()).collect(Collectors.toList());
        List<AutoReceiveMaster> autoReceiveMasterList = new ArrayList<>();


        if(CollectionUtils.isNotEmpty(successList)){

            Map<String, ScorerMaster> scorerMasterMap = scorerMasterList.stream()
                    .collect(Collectors.toMap(ScorerMaster::getMasterId, scorerMaster -> scorerMaster));

            successList.forEach(resp -> {
                String masterId = resp.getBizId();
                AutoReceiveMaster autoReceiveMaster = new AutoReceiveMaster();
                autoReceiveMaster.setMasterId(Long.valueOf(masterId));
                BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
                BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
                if(Objects.isNull(basePrice)){
                    basePrice = BigDecimal.ZERO;
                }
                autoReceiveMaster.setMasterPrice(cost.compareTo(basePrice) >= 0 ? cost : basePrice);
                autoReceiveMaster.setMasterScore(scorerMasterMap.get(masterId).getScore());
                autoReceiveMasterList.add(autoReceiveMaster);

            });
        }

        return autoReceiveMasterList;
    }



    private List<AutoReceiveMaster>  getAutoReceiveMasterList(OrderBase orderBase,
                                                 List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,List<ScorerMaster> scorerMasterList){

        Set<String> orderServiceIdList = orderServiceAttributeInfoList.stream().map(OrderServiceAttributeInfo::getServiceId).map(String::valueOf).collect(Collectors.toSet());

        Map<String,String> serviceIdMap = new HashMap<>();
        Map<String,Map<String,String>> skuNumberPathNoMap = new HashMap<>();
        Map<String,Map<String,Set<String>>> skuNoMap = new HashMap<>();
        List<BizRuleMapping>  bizRuleMappingList = getBizRuleMapping(orderServiceIdList);

        if(CollectionUtils.isNotEmpty(bizRuleMappingList)){
            bizRuleMappingList.forEach(bizRuleMapping -> {
                String serviceId = bizRuleMapping.getFromBizRule().get("serviceId");
                serviceIdMap.put(bizRuleMapping.getToBizRule().get("serviceId"),bizRuleMapping.getFromBizRule().get("serviceId"));
                if(!skuNumberPathNoMap.containsKey(serviceId)){
                    skuNumberPathNoMap.put(serviceId,new HashMap<>());
                }
                skuNumberPathNoMap.get(serviceId).put(bizRuleMapping.getFromBizRule().get("skuNumberPathNo"),bizRuleMapping.getToBizRule().get("skuNumberPathNo"));

                if(!skuNoMap.containsKey(serviceId)){
                    skuNoMap.put(serviceId,new HashMap<>());
                }

                if(!skuNoMap.get(serviceId).containsKey(bizRuleMapping.getFromBizRule().get("skuNo"))){
                    skuNoMap.get(serviceId).put(bizRuleMapping.getFromBizRule().get("skuNo"),new HashSet<>());

                }
                skuNoMap.get(serviceId).get(bizRuleMapping.getFromBizRule().get("skuNo")).add(bizRuleMapping.getToBizRule().get("skuNo"));
            });
        }

       log.info("skuNoMap:" + skuNoMap.toString());

        Set<String> serviceIdList = new HashSet<>();
        orderServiceIdList.forEach(serviceId -> serviceIdList.add(serviceIdMap.getOrDefault(String.valueOf(serviceId),serviceId)));
        List<FeeTemplate> feeTemplateList = getFeeTemplateList(serviceIdList);

        Map<String,Set<String>> templateSkuNoMap = getFeeTemplateSkuList(feeTemplateList);

        log.info("templateSkuNoMap:" + templateSkuNoMap.toString());

        if(CollectionUtils.isEmpty(templateSkuNoMap.keySet()) || (!templateSkuNoMap.keySet().containsAll(serviceIdList))){
            log.info(String.format("master_auto_offer_price计价模板不存在,serviceIdList:%s",serviceIdList));
            return null;
        }

        List<AutoReceiveMaster> autoReceiveMasterList = new ArrayList<>();


        int batchSize = 100;
        int totals = scorerMasterList.size();
        int pages = totals / batchSize;
        int leftNum = totals % batchSize;
        int totalPage = leftNum > 0 ? (pages + 1) : pages;
        for(int index = 0;index < totalPage;index++){
            int endIndex = (index + 1) * batchSize;
            if(endIndex > totals){
                endIndex = totals;
            }
            List<ScorerMaster> scorerMasterPage = scorerMasterList.subList(index * batchSize,endIndex);
            Set<String> masterIdSet = scorerMasterPage.stream().map(ScorerMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());


            List<FeeRule> masterFeeRuleList = this.getFeeRuleList(masterIdSet,orderBase.getFourthDivisionId(),serviceIdList);
            Map<String,Map<String,List<FeeRule>>> masterFeeRuleMap = getMasterFeeRuleMap(masterFeeRuleList);

            for(ScorerMaster scorerMaster : scorerMasterPage){
                boolean isSkipMaster = false;
                String masterId = scorerMaster.getMasterId();
                Map<String,List<FeeRule>> tempMasterFeeRuleMap = masterFeeRuleMap.get(masterId);
                if(tempMasterFeeRuleMap == null || tempMasterFeeRuleMap.isEmpty() || (!tempMasterFeeRuleMap.keySet().containsAll(serviceIdList))){
                    log.info(String.format("师傅没有配置订单全部服务的接单价格，masterId:%s,serviceIdList:%s,masterFeeRuleMap:%s",masterId,serviceIdList,masterFeeRuleMap));
                    continue;
                }
                Map<Long,Set<FeeRule>> orderFeeRuleMap = new HashMap<>();
                for(OrderServiceAttributeInfo orderServiceAttributeInfo : orderServiceAttributeInfoList){
                    Long attributeInfoId = orderServiceAttributeInfo.getId();
                    String serviceId = String.valueOf(orderServiceAttributeInfo.getServiceId());
                    serviceId = serviceIdMap.getOrDefault(serviceId,serviceId);
                    Set<String> templateSkuNoSet = templateSkuNoMap.get(serviceId);
                    List<FeeRule> feeRuleList = tempMasterFeeRuleMap.get(serviceId);
                    Map<String,Set<String>> skuNoMapping = skuNoMap.getOrDefault(serviceId,new HashMap<>());
                    feeRuleList = filterDuplicateFeeRule(feeRuleList);
                    for(String sku : templateSkuNoSet){
                        String skuValue = getSkuValue(skuNoMapping.getOrDefault(sku,Collections.singleton(sku)),orderServiceAttributeInfo);
                        if(StringUtils.isNotBlank(skuValue)){
                            if(!orderFeeRuleMap.containsKey(attributeInfoId)){
                                orderFeeRuleMap.put(attributeInfoId,new HashSet<>());
                            }
                            Set<FeeRule> orderFeeRuleSet = feeRuleList.stream().filter(feeRule -> feeRule.getBizRule().get("skuNo").equals(sku)).collect(Collectors.toSet());
                            if(CollectionUtils.isEmpty(orderFeeRuleSet) || orderFeeRuleSet.size() > 1){
                                log.info(String.format("师傅同一个服务同一个skuNo存在多个接单价格配置,masterId:%s,skuNo:%s,feeRuleList:%s",masterId,sku,JSON.toJSONString(orderFeeRuleSet)));
                                isSkipMaster = true;
                                break;
                            }
                            FeeRule feeRule = orderFeeRuleSet.iterator().next();
                            String attributeValueMin = feeRule.getBizRule().get("attributeValueMin");
                            String attributeValueMax = feeRule.getBizRule().get("attributeValueMax");
                            if(CollectionUtils.isNotEmpty(skuNoMapping.get(sku)) && StringUtils.isNotBlank(attributeValueMin) && StringUtils.isNotBlank(attributeValueMax)){
                                Long minAttributeValue = Long.valueOf(attributeValueMin);
                                Long maxAttributeValue = Long.valueOf(attributeValueMax);
                                if(StringUtils.isBlank(skuValue)){
                                    log.info(String.format("自定义skuNo属性值为空,orderId:%d,skuNo:%s,orderServiceAttributeInfo:%s",orderBase.getOrderId(),sku,orderServiceAttributeInfo.getMasterServiceInfos()));
                                    isSkipMaster = true;
                                    break;
                                }
                                Long attributeValue = Long.valueOf(skuValue);
                                if(attributeValue >= minAttributeValue && attributeValue <= maxAttributeValue){
                                    orderFeeRuleMap.get(attributeInfoId).addAll(orderFeeRuleSet);
                                }
                            }else{
                                orderFeeRuleMap.get(attributeInfoId).addAll(orderFeeRuleSet);
                            }
                        }else if("AP48014959".equals(sku)){
                            //订单上下文没有该SKUAP48014959，需要特殊处理SKU，如果师傅没有配置该SKU接单价格，则不计算，否则需要计算
                            Set<FeeRule> orderFeeRuleSet = feeRuleList.stream().filter(feeRule -> feeRule.getBizRule().get("skuNo").equals(sku)).collect(Collectors.toSet());
                            if(CollectionUtils.isNotEmpty(orderFeeRuleSet)){

                                if(!orderFeeRuleMap.containsKey(attributeInfoId)){
                                    orderFeeRuleMap.put(attributeInfoId,new HashSet<>());
                                }

                                orderFeeRuleMap.get(attributeInfoId).addAll(orderFeeRuleSet);
                            }
                        }
                    }
                    if(isSkipMaster){
                        break;
                    }
                }
                if(isSkipMaster){
                    log.info(String.format("自动接单跳过师傅,orderId:%d,masterId:%s",orderBase.getOrderId(),masterId));
                    continue;
                }
                BigDecimal masterPrice = calculatePrice(masterId,orderBase,orderFeeRuleMap,skuNumberPathNoMap,orderServiceAttributeInfoList,serviceIdMap);
                if(masterPrice != null){
                    AutoReceiveMaster autoReceiveMaster = new AutoReceiveMaster(Long.valueOf(masterId),masterPrice,scorerMaster.getScore());
                    autoReceiveMasterList.add(autoReceiveMaster);
                }
            }
        }

        return autoReceiveMasterList;
    }

    private Map<String,Map<String,List<FeeRule>>> getMasterFeeRuleMap(List<FeeRule> feeRuleList){

        Map<String,Map<String,List<FeeRule>>> masterFeeRuleMap = new HashMap<>();
        feeRuleList.forEach(feeRule -> {
            Map<String, String> bizRule = feeRule.getBizRule();
            String masterId = bizRule.get("masterId");
            String serviceId = bizRule.get("serviceId");
            if(!masterFeeRuleMap.containsKey(masterId)){
                masterFeeRuleMap.put(masterId,new HashMap<>());
            }
            if(!masterFeeRuleMap.get(masterId).containsKey(serviceId)){
                masterFeeRuleMap.get(masterId).put(serviceId,new ArrayList<>());
            }
            masterFeeRuleMap.get(masterId).get(serviceId).add(feeRule);

        });

        return masterFeeRuleMap;

    }



    private BigDecimal calculatePrice(String masterId,OrderBase orderBase, Map<Long,Set<FeeRule>> orderFeeRuleMap,Map<String,Map<String,String>> skuNumberPathNoMap,
                                                 List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,Map<String,String> serviceIdMap){
        ApplyCalculateReq applyCalculateReq = new ApplyCalculateReq();
        applyCalculateReq.setSceneCode(SCENE_CODE);
        Map<String,String> bizRuleMap = new HashMap<>();
        bizRuleMap.put("masterOrderId",String.valueOf(orderBase.getOrderId()));
        bizRuleMap.put("masterId",String.valueOf(masterId));
        applyCalculateReq.setBizRule(bizRuleMap);
        List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataList = buildCalculateDataUnitList(orderServiceAttributeInfoList,skuNumberPathNoMap,orderFeeRuleMap,serviceIdMap);
        if(CollectionUtils.isEmpty(calculateRuleDataList)){
            log.info(String.format("自动接单无可计费项,orderId:%d,masterId:%s",orderBase.getOrderId(),masterId));
            return null;
        }
        log.info(String.format("calculateRuleDataList:%s",JSON.toJSONString(calculateRuleDataList)));
        applyCalculateReq.setCalculateRuleDataList(calculateRuleDataList);
        ApplyCalculateResp applyCalculateResp = feeRuleApi.applyCalculate(applyCalculateReq);
        return applyCalculateResp.getCost();
    }


    private List<FeeRule> filterDuplicateFeeRule(List<FeeRule> feeRuleList){
        Map<Long,FeeRule> feeRuleMap = new HashMap<>();
        feeRuleList.forEach(feeRule -> {
            Long templateId = feeRule.getTemplateId();
            if(feeRuleMap.containsKey(templateId)){
                if(feeRule.getCreateTime().compareTo(feeRuleMap.get(templateId).getCreateTime()) > 0){
                    feeRuleMap.put(templateId,feeRule);
                }
            }else{
                feeRuleMap.put(templateId,feeRule);
            }
        });
        return new ArrayList<>(feeRuleMap.values());
    }


    /**
     * 获取业务规则映射
     * @param serviceIdList
     * @return ap值映射
     */
    private List<BizRuleMapping> getBizRuleMapping(Set<String> serviceIdList){
        BizRuleMappingBatchQueryReq req = new BizRuleMappingBatchQueryReq();
        req.setSceneCode(SCENE_CODE);
        Map<String, List<String>> toBizRule = new HashMap<>();
        toBizRule.put("serviceId",new ArrayList<>(serviceIdList));
        req.setToBizRule(toBizRule);
        req.setPageSize(200);
        List<BizRuleMapping> bizRuleMappingList = new ArrayList<>();
        SimplePageInfo<BizRuleMapping> simplePageInfo = bizRuleMappingApi.batchQuery(req);
        if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){
            bizRuleMappingList.addAll(simplePageInfo.getList());
        }
        while(simplePageInfo.getPageNum() < (simplePageInfo.getPages() - 1) ){
            req.setPageNum(req.getPageNum() + 1);
            simplePageInfo = bizRuleMappingApi.batchQuery(req);
            if(CollectionUtils.isNotEmpty(simplePageInfo.getList())){
                bizRuleMappingList.addAll(simplePageInfo.getList());
            }
        }
        return bizRuleMappingList;
    }


    private List<FeeTemplate> getFeeTemplateList(Set<String> serviceIdList){
        FeeTemplateBatchQueryReq req = new FeeTemplateBatchQueryReq();
        req.setSceneCode(SCENE_CODE);
        req.setPageSize(200);
        Map<String, List<String>> bizRule = new HashMap<>();
        bizRule.put("serviceId",new ArrayList<>(serviceIdList));
        req.setBizRule(bizRule);

        List<FeeTemplate> feeTemplateList = new ArrayList<>();
        SimplePageInfo<FeeTemplate> templateSimplePageInfo;
        do{
           templateSimplePageInfo = feeTemplateApi.batchQuery(req);
           if(CollectionUtils.isNotEmpty(templateSimplePageInfo.getList())){
               feeTemplateList.addAll(templateSimplePageInfo.getList());
           }
        }while(templateSimplePageInfo.getPageNum() < (templateSimplePageInfo.getPages() - 1));

        return feeTemplateList;
    }

    private Map<String,Set<String>> getFeeTemplateSkuList(List<FeeTemplate> templateList){
        Map<String,Set<String>> feeTemplateSkuNoMap = new HashMap<>();
        List<Map<String,String>> bizRuleList = templateList.stream().map(FeeTemplate::getBizRule).collect(Collectors.toList());
        bizRuleList.stream().forEach(templateBizRule -> {
            String serviceId = templateBizRule.get("serviceId");
            if(!feeTemplateSkuNoMap.containsKey(serviceId)){
                feeTemplateSkuNoMap.put(serviceId,new HashSet<>());
            }
            feeTemplateSkuNoMap.get(serviceId).add(templateBizRule.get("skuNo"));
        });
        return feeTemplateSkuNoMap;
    }


    private BigDecimal queryUserOfferGuidePrice(OrderBase orderBase,OrderGrab orderGrab,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList){


        Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());
        if(Objects.isNull(cityDivisionId) || cityDivisionId == 0L){
            return null;
        }

        ApplyOrderCalculateReq applyOrderCalculateReq = new ApplyOrderCalculateReq();
        Long orderId = orderBase.getOrderId();
        List<String> sceneCode = Collections.singletonList("user_order_offer_guide_price");
        applyOrderCalculateReq.setSceneCode(sceneCode);
        com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
        orderBaseParam.setOrderId(orderId);
        orderBaseParam.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        orderBaseParam.setCreateTime(orderBase.getCreateTime());
        applyOrderCalculateReq.setOrderBase(orderBaseParam);
        List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(com.alibaba.fastjson.JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(),CalculateServiceInfo.class)));
        applyOrderCalculateReq.setServiceInfos(serviceInfos);
        AccountInfo from = new AccountInfo();
        from.setAccountId(orderBase.getAccountId());
        from.setAccountType(orderBase.getAccountType());
        applyOrderCalculateReq.setFrom(from);

        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setDivisionType(DivisionTypeEnum.CITY.code);
        Address address = addressApi.getDivisionInfoByDivisionId(cityDivisionId);
        BeanUtils.copyProperties(address,addressInfo);
        applyOrderCalculateReq.setAddressInfo(addressInfo);
        applyOrderCalculateReq.setDynamicCalculateTime(new Date());

        applyOrderCalculateReq.setNeedIntegrityValidation(false);


        log.info("applyOrderCalculateBatchReq:" + com.alibaba.fastjson.JSON.toJSONString(applyOrderCalculateReq));
        ApplyOrderCalculateResp resp = feeRuleApi.applyOrderCalculate(applyOrderCalculateReq);
        log.info("applyOrderCalculateBatchResp:" + com.alibaba.fastjson.JSON.toJSONString(resp));
        if(Objects.nonNull(resp) && Objects.nonNull(resp.getCost())){
            return resp.getCost();
        }


        return null;

    }

    private boolean autoReceiveOrder(OrderBase orderBase, OrderGrab orderGrab, OrderInitFee orderInitFee,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,
                                     List<AutoReceiveMaster> autoReceiveMasterList,
                                     String orderDistributeRule, Long cityDivisionId, List<BatchOrderPushResp> batchOrderPushRespList){


        Set<Long> autoReceiveMasterSet = autoReceiveMasterList.stream().map(AutoReceiveMaster::getMasterId).collect(Collectors.toSet());


        if(CollectionUtils.isEmpty(autoReceiveMasterList)){
            return false;
        }

        List<AutoReceiveMaster> finalAutoReceiveMasterList;


        if(orderGrab.getAppointType().equals(AppointType.DEFINITE_PRICE.value)){


            MasterActivityRewardRqt rqt = new MasterActivityRewardRqt();
            rqt.setAppointType(orderGrab.getAppointType());
            rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
            rqt.setOrderId(orderBase.getOrderId());
            rqt.setServeIds(orderBase.getServeIds());
            rqt.setCreateOrderTime(orderBase.getOrderCreateTime());
            rqt.setFourthDivisionId(orderBase.getFourthDivisionId());
            rqt.setServeLevel1Ids(orderBase.getServeLevel1Ids());
            rqt.setOrderNo(orderBase.getOrderNo());
            rqt.setCreateOrderUserId(orderBase.getAccountId());
            rqt.setThirdDivisionId(orderBase.getThirdDivisionId());
            rqt.setPushOrderTime(batchOrderPushRespList.get(0).getPushTime());
            rqt.setOfferNum(Long.valueOf(orderGrab.getOfferNumber()));
            rqt.setIsOrderContract(0);
            rqt.setOrderFrom(orderBase.getOrderFrom());
            rqt.setFromAccountType(orderBase.getAccountType());
            rqt.setGroupGoodNum(orderServiceAttributeInfoList.size());
            List<MasterActivityRewardRqt.MasterInfo> masterInfoList = new ArrayList<>();

            batchOrderPushRespList = batchOrderPushRespList.stream().filter(batchOrderPushResp -> autoReceiveMasterSet.contains(batchOrderPushResp.getMasterId())).collect(Collectors.toList());

            batchOrderPushRespList.forEach(batchOrderPushResp -> {
                MasterActivityRewardRqt.MasterInfo masterInfo = new MasterActivityRewardRqt.MasterInfo();
                masterInfo.setMasterId(batchOrderPushResp.getMasterId());
                masterInfo.setDistanceValue(batchOrderPushResp.getPushDistance().intValue());
                masterInfo.setSkillRelatedState(1);
                masterInfoList.add(masterInfo);
            });

            rqt.setMasterInfoList(masterInfoList);
            rqt.setIsOrderPackage(0);


            if(AccountType.USER.code.equals(orderBase.getAccountType()) && OrderFrom.SITE.valueEn.equals(orderBase.getOrderFrom())){
                GetPersonaGroupIdsByUserIdRqt getPersonaGroupIdsByUserIdRqt = new GetPersonaGroupIdsByUserIdRqt();
                getPersonaGroupIdsByUserIdRqt.setUserId(orderBase.getAccountId());
                getPersonaGroupIdsByUserIdRqt.setAppId(1);
                GetPersonaGroupIdsByUserIdResp resp = bigdataOpenServiceApi.getPersonaGroupIdsByUserId(getPersonaGroupIdsByUserIdRqt);
                if(Objects.nonNull(resp) && StringUtils.isNotBlank(resp.getGroupIds())){
                    List<Long> userCrowIdList = Arrays.asList(resp.getGroupIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
                    rqt.setUserCrowIds(userCrowIdList);
                }
            }

            List<MasterActivityRewardResp> respList = outBusinessServiceApi.getMasterActivityReward(rqt);

            log.info("getMasterActivityReward rqt: " + com.alibaba.fastjson.JSON.toJSONString(rqt) + "，resp:" + com.alibaba.fastjson.JSON.toJSONString(respList));



            if(CollectionUtils.isNotEmpty(respList)){
                Map<Long,BigDecimal> rewardMap = new HashMap<>();
                for(MasterActivityRewardResp resp : respList){
                    if(CollectionUtils.isEmpty(resp.getRewardList())){
                       continue;
                    }
                    Set<MasterActivityRewardResp.ActivityRewardInfo> activityRewardInfoList = resp.getRewardList().stream().filter(activityRewardInfo -> "bonus".equals(activityRewardInfo.getRewardType()) && "fixed_value".equals(activityRewardInfo.getRewardGiveType())
                            && "quoted".equals(activityRewardInfo.getTaskSymbol())).collect(Collectors.toSet());
                    if(CollectionUtils.isEmpty(activityRewardInfoList)){
                        continue;
                    }
                    BigDecimal maxRewardValue = BigDecimal.ZERO;
                    for(MasterActivityRewardResp.ActivityRewardInfo activityRewardInfo : activityRewardInfoList){
                        if(activityRewardInfo.getRewardValue().compareTo(maxRewardValue) > 0){
                            maxRewardValue = activityRewardInfo.getRewardValue();
                        }
                    }
                    rewardMap.put(resp.getMasterId(),maxRewardValue);
                }

                finalAutoReceiveMasterList = autoReceiveMasterList.stream().filter(autoReceiveMaster -> autoReceiveMaster.getMasterPrice().compareTo(orderInitFee.getDefiniteServeFee().add(rewardMap.getOrDefault(autoReceiveMaster.getMasterId(),BigDecimal.ZERO))) <= 0).collect(Collectors.toList());
            }else{
                finalAutoReceiveMasterList = autoReceiveMasterList.stream().filter(autoReceiveMaster -> autoReceiveMaster.getMasterPrice().compareTo(orderInitFee.getDefiniteServeFee()) <= 0).collect(Collectors.toList());
            }

        }else if(orderGrab.getAppointType().equals(AppointType.OPEN.value) && AccountType.USER.code.equals(orderBase.getAccountType()) &&
                OrderFrom.SITE.valueEn.equals(orderBase.getOrderFrom())){

            BigDecimal guidePrice = queryUserOfferGuidePrice(orderBase,orderGrab,orderServiceAttributeInfoList);
            if(Objects.nonNull(guidePrice) && guidePrice.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal guidPriceLower = guidePrice.subtract( guidePrice.multiply( BigDecimal.valueOf(guidePriceLowerLimitPercent)).divide(BigDecimal.valueOf(100L)));
                BigDecimal guidPriceUpper = guidePrice.add(guidePrice.multiply( BigDecimal.valueOf(guidePriceUpperLimitPercent)).divide(BigDecimal.valueOf(100L)));
                finalAutoReceiveMasterList = autoReceiveMasterList.stream().filter(autoReceiveMaster -> autoReceiveMaster.getMasterPrice().compareTo(guidPriceLower) >= 0 && autoReceiveMaster.getMasterPrice().compareTo(guidPriceUpper) <=0).collect(Collectors.toList());
            }else{
                OfferPriceListResp offerPriceListResp = offerModuleResourceApi.orderOfferPriceList(orderBase.getGlobalOrderTraceId(),orderBase.getAccountId(),orderBase.getAccountType());

                if(Objects.isNull(offerPriceListResp) || CollectionUtils.isEmpty(offerPriceListResp.getOrderOfferPriceInfoList())){
                    return false;
                }
                List<OrderOfferPrice> orderOfferPriceList = offerPriceListResp.getOrderOfferPriceInfoList().stream().map(OfferPriceListResp.OrderOfferPriceInfo::getOrderOfferPrice).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(orderOfferPriceList)){
                    return false;
                }
                orderOfferPriceList = orderOfferPriceList.stream().filter(orderOfferPrice -> "master".equals(orderOfferPrice.getOfferLabel())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(orderOfferPriceList)){
                    return false;
                }
                AtomicReference<BigDecimal> offerPriceReference = new AtomicReference<>(BigDecimal.ZERO);
                orderOfferPriceList.stream()
                        .min(Comparator.comparing(OrderOfferPrice::getOfferPrice)).ifPresent(e -> offerPriceReference.set(e.getOfferPrice()));
                BigDecimal minOfferPrice = offerPriceReference.get();
                if(minOfferPrice.compareTo(BigDecimal.ZERO) > 0){
                    BigDecimal guidPriceLower = minOfferPrice.subtract( minOfferPrice.multiply( BigDecimal.valueOf(guidePriceLowerLimitPercent)).divide(BigDecimal.valueOf(100L)));
                    BigDecimal guidPriceUpper = minOfferPrice.add(minOfferPrice.multiply( BigDecimal.valueOf(guidePriceUpperLimitPercent)).divide(BigDecimal.valueOf(100L)));
                    finalAutoReceiveMasterList = autoReceiveMasterList.stream().filter(autoReceiveMaster -> autoReceiveMaster.getMasterPrice().compareTo(guidPriceLower) >= 0 && autoReceiveMaster.getMasterPrice().compareTo(guidPriceUpper) <=0).collect(Collectors.toList());

                }else{
                    return false;
                }

            }

        }else{
            finalAutoReceiveMasterList = autoReceiveMasterList;
        }


        if(CollectionUtils.isEmpty(finalAutoReceiveMasterList)){
            return false;
        }

        if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(orderDistributeRule) || OrderDistributeRule.SCORING_ORDER_TOP50_LOW_PRICE_PRIORITY.getCode().equals(orderDistributeRule)){
            finalAutoReceiveMasterList = finalAutoReceiveMasterList.stream().sorted(new AutoReceiveMasterScoreComparator()).collect(Collectors.toList());
            if(finalAutoReceiveMasterList.size() > MASTER_SCORE_TOP){
                finalAutoReceiveMasterList = finalAutoReceiveMasterList.subList(0,MASTER_SCORE_TOP);
            }
        }

        Integer appointType = orderGrab.getAppointType();
        Comparator<AutoReceiveMaster> masterComparator = (OrderDistributeRule.SCORING_ORDER.getCode().equals(orderDistributeRule) ||
                OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(orderDistributeRule)) ? new AutoReceiveMasterScoreComparator() : new AutoReceiveMasterPriceComparator();

        List<AutoReceiveMaster> lastAutoReceiveMasterList = finalAutoReceiveMasterList.stream().sorted(masterComparator).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(lastAutoReceiveMasterList)){
            return false;
        }


        if(appointType.equals(AppointType.OPEN.value)){
            //TODO 改成mq模式（基础中台不能调用业务http接口）
            this.autoReceiveOrder(orderBase,2,lastAutoReceiveMasterList,false);
        }else if(appointType.equals(AppointType.DEFINITE_PRICE.value)){
            OrderLock orderLock = orderLockRepository.selectByCityDivisionId(cityDivisionId);
            if(Objects.nonNull(orderLock) && Objects.nonNull(orderLock.getOrderGrabLockCnt()) && orderLock.getOrderGrabLockCnt() > 0){
                this.autoReceiveOrder(orderBase,4,lastAutoReceiveMasterList,true);
            }else{
                this.autoReceiveOrder(orderBase,4,lastAutoReceiveMasterList,false);
            }
        }
        return false;
    }



    public List<ApplyCalculateReq.CalculateRuleDataUnit> buildCalculateDataUnitList(List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,
                                                                                   Map<String,Map<String,String>> skuNumberPathNoMap,
                                                                                    Map<Long,Set<FeeRule>> feeRuleMap,Map<String,String> serviceIdMap){
        List<ApplyCalculateReq.CalculateRuleDataUnit> calculateRuleDataUnitList = new ArrayList<>();
        for(OrderServiceAttributeInfo orderServiceAttributeInfo : orderServiceAttributeInfoList){
            String serviceId = String.valueOf(orderServiceAttributeInfo.getServiceId());
            serviceId = serviceIdMap.getOrDefault(serviceId,serviceId);
            Map<String,String> skuNumberPathNoMapping = skuNumberPathNoMap.getOrDefault(serviceId,new HashMap<>());
            Set<FeeRule> tempFeeRuleList = feeRuleMap.get(orderServiceAttributeInfo.getId());
            for(FeeRule feeRule : tempFeeRuleList){
                Map<String,String> bizRuleMap = feeRule.getBizRule();
                ApplyCalculateReq.CalculateRuleDataUnit calculateRuleDataUnit = new ApplyCalculateReq.CalculateRuleDataUnit();
                calculateRuleDataUnit.setFeeRuleId(feeRule.getFeeRuleId());
                CalculateRuleData calculateRuleData = feeRule.getCalculateRuleData();
                List<String> expressionParamList = calculateRuleData.getExpressionParamList();
                calculateRuleDataUnit.setExpressionParamList(expressionParamList);
                calculateRuleDataUnit.setTemplateId(feeRule.getTemplateId());
                calculateRuleDataUnit.setExpress(calculateRuleData.getExpress());
                Map<String,String> expressionParamMap = new HashMap<>();
                for(String expressionParam : expressionParamList){
                    String tempParam = expressionParam;
                    if(bizRuleMap.containsKey(expressionParam)){
                        expressionParamMap.put(expressionParam,bizRuleMap.get(expressionParam));
                    }else{
                        expressionParam = skuNumberPathNoMapping.getOrDefault(expressionParam,expressionParam);
                        ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(orderServiceAttributeInfo.getMasterServiceInfos());
                        String paramValue = orderServiceAttributeService.getValueByAttributePathNo(serviceInfo,expressionParam);
                        if(StringUtils.isBlank(paramValue)){
                            log.info("计费表达式参数取值为空,跳过该师傅计费,orderServiceAttributeInfo：%s,feeRule:%s",JSON.toJSONString(orderServiceAttributeInfo),JSON.toJSONString(feeRule));
                            return null;
                        }
                        expressionParamMap.put(tempParam,paramValue);
                    }
                }
                calculateRuleDataUnit.setExpressionParamMap(expressionParamMap);
                calculateRuleDataUnitList.add(calculateRuleDataUnit);

            }
        }
        return calculateRuleDataUnitList;
    }


    private List<FeeRule> getFeeRuleList(Set<String> masterIdList,Long fourthDivisionId,Set<String> serviceIdList){

        FeeRuleBatchQueryReq req = new FeeRuleBatchQueryReq();
        req.setSceneCode(SCENE_CODE);
        Map<String, List<String>> bizRuleMap = new HashMap<>();
        bizRuleMap.put("masterId",new ArrayList<>(masterIdList));
        bizRuleMap.put("serviceId",new ArrayList<>(serviceIdList));
        bizRuleMap.put("level4DivisionId",Collections.singletonList(String.valueOf(fourthDivisionId)));
        req.setBizRule(bizRuleMap);
        List<FeeRule> feeRuleList = feeRuleApi.batchQuery(req);
        return feeRuleList;
    }




    private boolean isDynamicConfiguration(OrderBase orderBase){
        return orderBase.getOrderServeVersion() == 1;
    }


    /**
     * 锁单
     * @param
     * @param autoReceiveMasterList
     */
    private void autoReceiveOrder(OrderBase orderBase,Integer appointType,List<AutoReceiveMaster> autoReceiveMasterList,boolean isOrderLock){
        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setAppointType(appointType);
        if (AppointType.OPEN.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_OFFER_MASTER.getCode());
        } else if (AppointType.DEFINITE_PRICE.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_MASTER.getCode());
        }
        rqt.setOfferStrategy(isOrderLock?"auto_receive_grab" : "default_grab");
        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        autoReceiveMasterList.forEach(autoReceiveMaster -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(autoReceiveMaster.getMasterId());
            masterPrice.setPrice(autoReceiveMaster.getMasterPrice());
            masterList.add(masterPrice);
        });
        rqt.setMasterList(masterList);
        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer",JSON.toJSONString(rqt),1L);
    }


    @Data
    public class AutoGrabOrderTask implements Runnable {

        private OrderBase orderBase;
        private List<AutoReceiveMaster> autoReceiveMasterList;

        public AutoGrabOrderTask(OrderBase orderBase, List<AutoReceiveMaster> autoReceiveMasterList) {
            this.orderBase = orderBase;
            this.autoReceiveMasterList = autoReceiveMasterList;
        }

        @Override
        public void run() {
            Long orderId = orderBase.getOrderId();
            //一口价
            if(CollectionUtils.isEmpty(autoReceiveMasterList)){
                return ;
            }
            Set<String> grabSuccessMaster=new HashSet<>();
            for(AutoReceiveMaster autoReceiveMaster : autoReceiveMasterList){
                if(autoReceiveMaster.getMasterPrice() == null || autoReceiveMaster.getMasterPrice().compareTo(BigDecimal.ZERO) <= 0){
                    continue;
                }
                GrabOrderValidRqt rqt = new GrabOrderValidRqt();
                rqt.setOrderId(orderId);
                rqt.setMasterId(autoReceiveMaster.getMasterId());
                rqt.setGrabType(3);
                rqt.setOrderModifyTime(orderBase.getOrderModifyTime());
                JSONObject jsonObject = tools.catchNoLog(() -> masterOrderApi.autoGrabOrder(rqt));;
                boolean grabOrderResult = Objects.nonNull(jsonObject) && Objects.equals(jsonObject.get("grabOrderFlag"), true);
                if(grabOrderResult){
                    grabSuccessMaster.add(String.valueOf(autoReceiveMaster.getMasterId()));
                    log.info(String.format("autoReceiveOrder success,rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSONString(jsonObject)));
//                    dataCenterRepository.orderDistributeCount(orderId,
//                            DateFormatterUtil.getNow(),grabSuccessMaster, DataCenterRepository.CountTag.AUTO_GRAB);
                    //TODO 改成Hbase统计
//                    featureRepository.orderDistributeCount(orderId, DateFormatterUtil.getNow(),grabSuccessMaster,FeatureRepository.CountTag.AUTO_GRAB);
                    return ;
                }else{
                    log.error(String.format("autoReceiveOrder error,rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSONString(jsonObject)));
                }
            }
            return ;
        }
    }



    @Data
    public class AutoOfferPriceTask implements Runnable {

        private OrderBase orderBase;
        private List<AutoReceiveMaster> autoReceiveMasterList;

        public AutoOfferPriceTask(OrderBase orderBase, List<AutoReceiveMaster> autoReceiveMasterList) {
            this.orderBase = orderBase;
            this.autoReceiveMasterList = autoReceiveMasterList;
        }

        @Override
        public void run() {

            Long orderId = orderBase.getOrderId();
            Date orderModifyTime = orderBase.getOrderModifyTime();
            Set<String> offerSuccessMaster=new HashSet<>();
            //公开报价
            autoReceiveMasterList.forEach(masterPrice -> {
                MasterOfferPriceRqt rqt = new MasterOfferPriceRqt();
                rqt.setOrderId(orderId);
                rqt.setOfferPrice(masterPrice.getMasterPrice());
                rqt.setOrderModifyTime(String.valueOf(orderModifyTime));
                rqt.setMasterId(masterPrice.getMasterId());
                rqt.setIncludeFitting(false);
                rqt.setBelongUserShare(0);
                rqt.setIsFurnishMesaInstallation(0);
                rqt.setOfferPriceType(3);
                JSONObject offerResponse=tools.catchNoLog(() -> masterOrderApi.autoOfferPrice(rqt));
                if (offerResponse==null) {
                    offerSuccessMaster.add(String.valueOf(masterPrice.getMasterId()));
                }
            });
//            dataCenterRepository.orderDistributeCount(orderId,
//                    DateFormatterUtil.getNow(),offerSuccessMaster, DataCenterRepository.CountTag.AUTO_OFFER);


            //改成Hbase统计
//            featureRepository.orderDistributeCount(orderId,DateFormatterUtil.getNow(),offerSuccessMaster,FeatureRepository.CountTag.AUTO_OFFER);
        }
    }


    private final ExecutorService autoReceiveOrderExecutor = new ThreadPoolExecutor(16,
            64,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(32),
            new ThreadFactoryImpl("autoReceiveOrderThread_"));


}
