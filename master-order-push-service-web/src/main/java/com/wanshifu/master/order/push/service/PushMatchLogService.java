package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;

/**
 * <AUTHOR>
 * @date 2025/4/25 20:49
 */
public interface PushMatchLogService {

    @Deprecated
    SimplePageInfo<PushMatchLogDto> pushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);

    SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(PushMatchLogRqt pushMatchLogRqt);


    SimplePageInfo<OrderFullTimeMasterMatchLogDto> orderFullMasterMatchLogList(PushMatchLogRqt pushMatchLogRqt);

}
