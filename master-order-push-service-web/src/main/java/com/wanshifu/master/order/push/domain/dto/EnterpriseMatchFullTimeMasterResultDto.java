package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.master.order.push.domain.message.EnterpriseMatchAgreementMasterResultMessage;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/4/16 19:39
 */
@Data
public class EnterpriseMatchFullTimeMasterResultDto {

    /**
     * 能够指派的全时师傅
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMaster;

    /**
     * 过滤掉的全时师傅
     *
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMaster;
}
