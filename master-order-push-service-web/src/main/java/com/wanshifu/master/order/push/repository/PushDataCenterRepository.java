//package com.wanshifu.master.order.push.repository;
//
//
//import com.alicloud.openservices.tablestore.model.BatchGetRowResponse;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Collection;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//@Component
//@Slf4j
//public class PushDataCenterRepository {
//
//    @Resource
//    TableStoreClient tableStoreClient;
//    private static final String T_MASTER_BASE = "t_master_base";
//
//    public Map<String,String> getMasterBaseFeature(Collection<String> masterList, String columnName){
//        final HashMap<String, String> resultMap = new HashMap<>();
//        try {
//            final List<BatchGetRowResponse.RowResult> rowResults =
//                    tableStoreClient.batchQueryByCollection(T_MASTER_BASE
//                            , masterList, FieldConstant.MASTER_ID,
//                            columnName);
//            for (BatchGetRowResponse.RowResult rowResult : rowResults) {
//                final Row row = rowResult.getRow();
//                if (row!=null) {
//                    final String masterId = row.getPrimaryKey()
//                            .getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//                    final String columnValue = tableStoreClient.getValue(row, columnName);
//                    resultMap.put(masterId,columnValue);
//                }
//            }
//        }catch (Exception e){
//            log.warn("getMasterBaseFeature warn,{}",e);
//        }
//        return resultMap;
//    }
//
//    private static final String USR_GROUPS_STAT = "usr_groups_stat";
//    public String getUserBaseFeature(Long userId, String columnName){
//        String result=null;
//        try {
//            final Row withColumns = tableStoreClient
//                    .getRowWithOnePKWithColumns(USR_GROUPS_STAT, FieldConstant.USER_ID, userId, columnName);
//                if (withColumns!=null) {
//                    final String columnValue = tableStoreClient.getValue(withColumns, columnName);
//                    result=columnValue;
//                }
//        }catch (Exception e){
//            log.warn("getUserBaseFeature warn,{}",e);
//        }
//        return result;
//    }
//
//    public String getOrderBaseFeature(String globalOrderId, String columnName){
//        String result=null;
//        try {
//            final Row withColumns = tableStoreClient
//                    .getRowWithOnePKWithColumns(FieldConstant.SMC_GLOBAL_ORDER_FEATURE,
//                            FieldConstant.GLOBAL_ORDER_ID, globalOrderId, columnName);
//            if (withColumns!=null) {
//                final String columnValue = tableStoreClient.getValue(withColumns, columnName);
//                result=columnValue;
//            }
//        }catch (Exception e){
//            log.warn("getOrderBaseFeature warn,{}",e);
//        }
//        return result;
//    }
//}
