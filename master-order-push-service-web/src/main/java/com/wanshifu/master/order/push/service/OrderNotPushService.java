package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushOrderList;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;

import java.util.List;

/**
 * 推单监控Service
 * <AUTHOR>
 */
public interface OrderNotPushService {

    SimplePageInfo<PushOrderList> noPushedMasterOrderList(NoPushedMasterOrderListRqt rqt);

    List<GetFilterDataResp> filterData(GetFilterDataRqt rqt);

}
