package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Data
@ToString
@Table(name = "order_distribute")
public class OrderDistribute {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    private Long orderId;

    private String pushMode;

    private String masterList;

    private String distributeResult;

    private Date createTime;

    private Date updateTime;



}
