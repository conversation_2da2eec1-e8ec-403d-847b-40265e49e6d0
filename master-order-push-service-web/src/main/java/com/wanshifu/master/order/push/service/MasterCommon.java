package com.wanshifu.master.order.push.service;

import cn.hutool.core.collection.CollUtil;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.BatchGetMasterBaseInfoRqt;
import com.wanshifu.master.information.domain.api.response.common.BatchGetMasterBaseInfoResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 16:42
 */
@Service
public class MasterCommon {

    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    /**
     * 根据masterIds获取师傅信息
     *
     * @param masterIds
     * @return
     */
    public List<BatchGetMasterBaseInfoResp> getMasterByMasterIds(List<Long> masterIds) {

        List<List<Long>> masterIdsList = CollUtil.split(masterIds, 20);
        return masterIdsList.stream().flatMap(it -> {
            BatchGetMasterBaseInfoRqt batchGetMasterBaseInfoRqt = new BatchGetMasterBaseInfoRqt();
            batchGetMasterBaseInfoRqt.setMasterIds(masterIds);
            batchGetMasterBaseInfoRqt.setPageSize(20);
            return commonQueryServiceApi.batchGetMasterBaseInfo(batchGetMasterBaseInfoRqt).stream();
        }).collect(Collectors.toList());
    }
}
