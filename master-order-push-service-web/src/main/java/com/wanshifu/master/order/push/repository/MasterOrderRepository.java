package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.MasterOrder;
import com.wanshifu.master.order.push.domain.po.MasterOrderPackageIndex;
import com.wanshifu.master.order.push.domain.po.OrderRefund;
import com.wanshifu.master.order.push.mapper.MasterOrderMapper;
import com.wanshifu.master.order.push.mapper.MasterOrderPackageMapper;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 初筛策略Repository
 * <AUTHOR>
 */
@Repository
public class MasterOrderRepository extends BaseRepository<MasterOrder> {

    @Resource
    private MasterOrderMapper masterOrderMapper;

    public List<MasterOrder> selectByMasterIdAndOrderId(List<String> masterIdSet,String orderId){
        Condition condition = new Condition(MasterOrder.class);
        condition.selectProperties("masterId");
        condition.createCriteria().andEqualTo("masterOrderId", orderId)
                .andIn("masterId",masterIdSet);
        return masterOrderMapper.selectByCondition(condition);
    }



}