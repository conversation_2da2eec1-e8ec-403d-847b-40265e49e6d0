package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.enterprise.master.api.CooperaMasterApi;
import com.wanshifu.enterprise.master.domain.cooperaMaster.request.CooMasterInfoReq;
import com.wanshifu.enterprise.master.domain.cooperaMaster.response.CooMasterInfoResp;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.domain.dto.AccountInfo;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateBatchReq;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.api.MasterBigDataOpenApi;
import com.wanshifu.master.order.push.api.OrderOperationApi;
import com.wanshifu.master.order.push.domain.api.response.GetMasterJiedanInfoResp;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.CompensateType;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistribute;
import com.wanshifu.master.order.push.domain.rqt.NotifyOrderComeInRqt;
import com.wanshifu.master.order.push.repository.NormalOrderDistributeRepository;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.recruit.api.RecruitMasterApi;
import com.wanshifu.master.recruit.domain.api.request.recruitMaster.GetMasterCurrentLeaveRequestRqt;
import com.wanshifu.master.recruit.domain.api.response.recruitMaster.GetMasterCurrentLeaveRequestResp;
import com.wanshifu.master.recruit.domain.enums.RecruitBusinessEnum;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.GetLevel2IdsByServeIdsReq;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.order.offer.domains.po.OrderInitFee;
import com.wanshifu.order.offer.domains.po.OrderServiceAttributeInfo;
import com.wanshifu.order.push.api.OrderPushListApi;
import com.wanshifu.order.push.response.BatchOrderPushResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NormalOrderAgreementAutoGrabService {


    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private NormalOrderDistributeRepository normalOrderDistributeRepository;


    @Resource
    private OrderPushListApi orderPushListApi;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;


    @Resource
    private CooperaMasterApi cooperaMasterApi;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;



    @Value("${agreementAutoOfferPrice.cityList}")
    private String agreementAutoOfferPriceCityList;


    @Value("${agreementAutoGrab.cityList}")
    private String agreementAutoGrabCityList;


    @Value("${agreementAutoOfferPrice.serveCompleteNumber}")
    private int agreementAutoOfferPriceServeCompleteNumber;


    @Value("${agreementAutoGrab.serveCompleteNumber}")
    private int agreementAutoGrabServeCompleteNumber;


    @Value("${agreementAutoGrab.lv2Serve.serveCompleteNumber}")
    private int agreementAutoGrabLv2ServeServeCompleteNumber;


    @Resource
    private OrderOperationApi orderOperationApi;


    @Resource
    private FeeRuleApi feeRuleApi;

    @Resource
    private AddressApi addressApi;


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;


    @Resource
    private MasterBigDataOpenApi masterBigDataOpenApi;


    @Resource
    private ServeServiceApi serveServiceApi;


    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Resource
    private RocketMqSendService rocketMqSendService;

    public Integer agreementAutoReceive(CompensateDistributeMessage message){

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(message.getOrderId());
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGrabByIdResp.getOrderServiceAttributeInfos();


        if(!checkAgreementMasterAutoReceiveCondition(orderBase,orderGrab,orderServiceAttributeInfoList)){
            return 0;
        }


        CompensateDistribute compensateDistribute  = message.getCompensateDistribute();
        Integer num = 0;
        if(CompensateType.NONE_APPOINT.getCode().equals(compensateDistribute.getCompensateType())){
            num = (orderGrab.getHireMasterId() > 0 && orderGrab.getConfirmServeStatus() == 1) ? 1 : 0;
        }else if(CompensateType.NONE_RECEIVE.getCode().equals(compensateDistribute.getCompensateType())){
            num = orderGrab.getOfferNumber();
        }
        if(num < compensateDistribute.getTriggerNum()){
            //满足条件使用协议师傅兜底报价或抢单
            return this.agreementMasterReceiveOrder(orderBase,orderGrab
                    ,orderGrabByIdResp.getOrderServiceAttributeInfos(),message.getDistributeType());
        }
        return 1;
    }


    private Integer agreementMasterReceiveOrder(OrderBase orderBase, OrderGrab orderGrab,
                                                List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,String distributeType){

        Long orderId = orderBase.getOrderId();

        NormalOrderDistribute normalOrderDistribute = new NormalOrderDistribute();
        normalOrderDistribute.setOrderNo(orderBase.getOrderNo());
        normalOrderDistribute.setOrderId(orderBase.getOrderId());
        normalOrderDistribute.setDistributeType(distributeType);


        List<AgreementMaster> agreementMasterList = matchEnterpriseAgreementMaster(orderBase);
        if(CollectionUtils.isEmpty(agreementMasterList)){
            normalOrderDistribute.setRemark("'无满足条件的总包协议师傅'");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            return 0;
        }


        agreementMasterList = filterByDailyOrderCnt(agreementMasterList);

        agreementMasterList = filterByNewContractRestStatus(agreementMasterList);



        Set<Long> agreementMasterIdSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).collect(Collectors.toSet());

        List<BatchOrderPushResp> batchOrderPushRespList = orderPushListApi.getOrderPushList(orderId, orderBase.getThirdDivisionId());
        if(CollectionUtils.isEmpty(batchOrderPushRespList)){
            return 1;
        }


        Set<Long> pushedMasterList = batchOrderPushRespList.stream().map(BatchOrderPushResp::getMasterId).collect(Collectors.toSet());
        agreementMasterIdSet.retainAll(pushedMasterList);


        if(CollectionUtils.isEmpty(agreementMasterIdSet)){
            normalOrderDistribute.setRemark("'无满足条件的总包协议师傅'");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            return 0;
        }

        List<Long> noticeMasterSet = new ArrayList<>(agreementMasterIdSet);

        Set<String> matchAgreementMasterSet = agreementMasterIdSet.stream().map(String::valueOf).collect(Collectors.toSet());
        normalOrderDistribute.setAgreementMasterList(String.join(",",matchAgreementMasterSet));

        agreementMasterIdSet = this.filterEnterpriseSuspendCooperationMaster(agreementMasterIdSet,Long.valueOf(orderBase.getServeIds()),orderBase.getCategoryId());

        BigDecimal definiteServeFee = this.getDefiniteServeFee(orderId);

        if(CollectionUtils.isEmpty(agreementMasterIdSet)){
            normalOrderDistribute.setRemark("师傅已被总包暂停合作");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            if(orderGrab.getAppointType() == 4){
                noticeMasterGrab(orderId,noticeMasterSet,definiteServeFee);
            }
            return 0;
        }

        if(orderGrab.getAppointType() == 2){
            agreementMasterIdSet = this.filterByCompleteOrderNum(agreementMasterIdSet,orderBase.getCategoryId());
        }else if(orderGrab.getAppointType() == 4){
            agreementMasterIdSet = this.filterByCompleteOrderNum(agreementMasterIdSet,orderBase.getCategoryId(),orderBase.getServeIds());
        }


        if(CollectionUtils.isEmpty(agreementMasterIdSet)){
            normalOrderDistribute.setRemark("师傅完工单量不满足条件");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            if(orderGrab.getAppointType() == 4){
                noticeMasterGrab(orderId,noticeMasterSet,definiteServeFee);
            }
            return 0;
        }

        final Set<Long> finalAgreementMasterIdSet = agreementMasterIdSet;

        agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> finalAgreementMasterIdSet.contains(agreementMaster.getMasterId())).collect(Collectors.toList());

        Map<String, AgreementMaster> agreementMasterListMap = agreementMasterList.stream().collect(Collectors.toMap(AgreementMaster::getId, Function.identity()));

        List<AgreementMasterBase> agreementMasterBaseList = this.getAgreementMasterCooperationPrice(agreementMasterList,orderBase,orderGrab,orderServiceAttributeInfoList);

        if(CollectionUtils.isEmpty(agreementMasterBaseList)){
            normalOrderDistribute.setRemark("师傅无协议合作价");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            return 0;
        }

        if(orderGrab.getAppointType() == 4){
            agreementMasterBaseList = filterByDefiniteServeFee(orderId,agreementMasterBaseList);
        }


        if(CollectionUtils.isEmpty(agreementMasterBaseList)){
            normalOrderDistribute.setRemark("师傅协议价低于订单一口价");
            normalOrderDistributeRepository.insertSelective(normalOrderDistribute);
            return 0;
        }

        Set<String> afterFilterMasterList = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());
        normalOrderDistribute.setAfterFilterMasterList(String.join(",",afterFilterMasterList));
        normalOrderDistributeRepository.insertSelective(normalOrderDistribute);


        if("receive_notice".equals(distributeType)){
            List<Long> masterList = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).map(Long::valueOf).collect(Collectors.toList());
            noticeMasterOffer(orderId,masterList);
            return 1;
        }


        Long extraId = normalOrderDistribute.getId();


        Integer appointType = orderGrab.getAppointType();

        MasterAutoReceiverRqt rqt = new MasterAutoReceiverRqt();
        List<MasterAutoReceiverRqt.MasterSubsidiaryInfo> masterSubsidiaryInfoList = Lists.newArrayList();

        rqt.setOrderId(orderBase.getOrderId());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setAppointType(appointType);
        rqt.setExtraId(String.valueOf(extraId));
        if (AppointType.OPEN.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_OFFER_AGREEMENT_UNDERTAKE.getCode());
        } else if (AppointType.DEFINITE_PRICE.value.equals(appointType)) {
            rqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_AGREEMENT_UNDERTAKE.getCode());
        }

        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        agreementMasterBaseList.forEach(agreementMasterBase -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterPrice.setPrice(agreementMasterBase.getCooperationPrice());

            String agreementMasterId = agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId();
            AgreementMaster agreementMaster = agreementMasterListMap.get(agreementMasterId);
            Integer sceneId = agreementMaster.getSceneId();
            String tagName = agreementMaster.getTagName();

            if (Objects.nonNull(sceneId)
                    && !Strings.isNullOrEmpty(tagName)) {

                MasterAutoReceiverRqt.MasterScene masterScene = new MasterAutoReceiverRqt.MasterScene();
                masterScene.setSceneId(sceneId);
                masterScene.setTagName(tagName);

                masterPrice.setMasterScene(masterScene);
            }

            masterList.add(masterPrice);

            MasterAutoReceiverRqt.MasterSubsidiaryInfo masterSubsidiaryInfo = new MasterAutoReceiverRqt.MasterSubsidiaryInfo();
            masterSubsidiaryInfo.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterSubsidiaryInfo.setExtraType("master_recruit_id");
            masterSubsidiaryInfo.setExtraId(Objects.isNull(agreementMasterBase.getRecruitId()) ? 0L : Long.parseLong(agreementMasterBase.getRecruitId()));
            masterSubsidiaryInfoList.add(masterSubsidiaryInfo);
        });
        rqt.setMasterList(masterList);

        MasterAutoReceiverRqt.SubsidiaryParam subsidiaryParam = new MasterAutoReceiverRqt.SubsidiaryParam();
        subsidiaryParam.setMasterSubsidiaryInfoList(masterSubsidiaryInfoList);
        rqt.setSubsidiaryParamJSON(JSON.toJSONString(subsidiaryParam));

        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(rqt),1L);


        return 1;
    }


    private List<AgreementMasterBase> getAgreementMasterCooperationPrice(List<AgreementMaster> agreementMasterList, OrderBase orderBase,
                                                                         OrderGrab orderGrab,
                                                                         List<OrderServiceAttributeInfo> orderServiceAttributeInfoList){
        ApplyOrderCalculateBatchReq batchReq = new ApplyOrderCalculateBatchReq();
        ApplyOrderCalculateReq calculateReq = new ApplyOrderCalculateReq();
        Long orderId = orderBase.getOrderId();
        //TODO 补充场景编码
        List<String> sceneCode = Collections.singletonList("contract_master");
        calculateReq.setSceneCode(sceneCode);
        com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
        orderBaseParam.setOrderId(orderId);
        orderBaseParam.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        orderBaseParam.setCreateTime(orderBase.getCreateTime());
        calculateReq.setOrderBase(orderBaseParam);
        List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(),CalculateServiceInfo.class)));
        calculateReq.setServiceInfos(serviceInfos);
        AccountInfo from = new AccountInfo();
        from.setAccountId(orderBase.getAccountId());
        from.setAccountType(orderBase.getAccountType());
        calculateReq.setFrom(from);

        AddressInfo addressInfo = new AddressInfo();
        Long divisionId;
        if(orderBase.getFourthDivisionId() > 0){
            divisionId = orderBase.getFourthDivisionId();
            addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);

        }else{
            divisionId = orderBase.getThirdDivisionId();
            addressInfo.setDivisionType(DivisionTypeEnum.DISTRICT.code);
        }
        Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
        BeanUtils.copyProperties(address,addressInfo);
        calculateReq.setAddressInfo(addressInfo);


        Integer appointType = orderGrab.getAppointType();
        if(AppointType.OPEN.value.equals(appointType)){
            calculateReq.setIsMappingPricing(false);
        }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
            calculateReq.setIsMappingPricing(true);
            calculateReq.setFromSceneCode("contract_master");
        }

        batchReq.setApplyOrderCalculateReq(calculateReq);

        List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();

        agreementMasterList.forEach(masterInfo -> {
            BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
            bizRuleBatchReq.setBizId(String.valueOf(masterInfo.getMasterId()));
            bizRuleBatchReq.setBizTag(String.valueOf(masterInfo.getRecruitId()));
            bizRuleBatchReqList.add(bizRuleBatchReq);

        });


        batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);
        log.info("applyOrderCalculateBatchReq:" + JSON.toJSONString(batchReq));
        List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
        log.info("applyOrderCalculateBatchResp:" + JSON.toJSONString(applyOrderCalculateBatchRespList));
        List<ApplyOrderCalculateBatchResp> successList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess()).collect(Collectors.toList());
        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(successList)){

            Map<String, AgreementMaster> agreementMasterMap = agreementMasterList.stream()
                    .collect(Collectors.toMap(AgreementMaster::getId, agreementMaster -> agreementMaster));

            successList.forEach(resp -> {
                String masterId = resp.getBizId();



                AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                agreementMasterBase.setMasterId(masterId);
                agreementMasterBase.setRecruitId(resp.getBizTag());

                String agreementMasterId = agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId();
                AgreementMaster agreementMaster = agreementMasterMap.get(agreementMasterId);

                agreementMasterBase.setDirectAppointMethod(agreementMaster.getDirectAppointMethod());
                BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
                BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
                if(Objects.isNull(basePrice)){
                    basePrice = BigDecimal.ZERO;
                }

                if(orderGrab.getAppointType() == 2){
                    Long offerPriceIncreasePercent = agreementMaster.getOfferPriceIncreasePercent();
                    if(Objects.nonNull(offerPriceIncreasePercent) && offerPriceIncreasePercent > 0){
                        cost = cost.add(cost.multiply( BigDecimal.valueOf(offerPriceIncreasePercent)).divide(BigDecimal.valueOf(100L)));
                    }
                }

                agreementMasterBase.setCooperationPrice(cost.compareTo(basePrice) >= 0 ? cost : basePrice);
                agreementMasterBaseList.add(agreementMasterBase);

            });
        }
        return agreementMasterBaseList;
    }



    public List<AgreementMasterBase> filterByDefiniteServeFee(Long orderId,List<AgreementMasterBase> agreementMasterBaseList){
        BigDecimal definiteServeFee = this.getDefiniteServeFee(orderId);
        return agreementMasterBaseList.stream().filter(agreementMasterBase -> definiteServeFee.compareTo(agreementMasterBase.getCooperationPrice()) >= 0).collect(Collectors.toList());
    }



    public BigDecimal getDefiniteServeFee(Long orderId){
        //一口价金额
        BigDecimal definiteServeFee = new BigDecimal(0);
        if(orderId > 0){
            OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderId, null);
            if (orderBaseComposite != null && orderBaseComposite.getOrderInitFee() != null) {
                definiteServeFee = Optional.ofNullable(orderBaseComposite.getOrderInitFee()).map(OrderInitFee::getDefiniteServeFee).orElse(BigDecimal.ZERO);
            }
        }
        return definiteServeFee;
    }


    private List<Long> getServeIds(String serveIds) {
        String[] split = serveIds.split(",");
        List<String> strings = Arrays.asList(split);
        return strings.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
    }


    private void noticeMasterGrab(Long orderId, List<Long> masterIds, BigDecimal price) {
        if (CollUtil.isEmpty(masterIds)) {
            log.warn("the orderId is {}", orderId);
            return;
        }

        //发送push给师傅
        NotifyOrderComeInRqt notifyOrderComeInRqt = new NotifyOrderComeInRqt();
        notifyOrderComeInRqt.setMasterIds(StrUtil.join(",", masterIds));
        notifyOrderComeInRqt.setOrderId(orderId);
        notifyOrderComeInRqt.setContent("来新单啦 | %s有笔" + price + "元的%s订单已推送给您，点击前往抢单>>");
        String result = orderOperationApi.notifyOrderComeIn(notifyOrderComeInRqt);
        log.info("notifyOrderComeIn result:" + result);
    }


    private void noticeMasterOffer(Long orderId, List<Long> masterIds) {
        if (CollUtil.isEmpty(masterIds)) {
            log.warn("the orderId is {}", orderId);
            return;
        }

        //发送push给师傅
        NotifyOrderComeInRqt notifyOrderComeInRqt = new NotifyOrderComeInRqt();
        notifyOrderComeInRqt.setMasterIds(StrUtil.join(",", masterIds));
        notifyOrderComeInRqt.setOrderId(orderId);
        notifyOrderComeInRqt.setContent("来新单啦 | %s刚发布了一笔%s订单，立即报价>>");
        String result = orderOperationApi.notifyOrderComeIn(notifyOrderComeInRqt);
        log.info("notifyOrderComeIn result:" + result);
    }


    private Set<Long> filterEnterpriseSuspendCooperationMaster(Set<Long> agreementMasterIdSet, Long serveId, Integer categoryId) {

        CooMasterInfoReq cooMasterInfoReq = new CooMasterInfoReq();
        cooMasterInfoReq.setMasterIds(new ArrayList<>(agreementMasterIdSet));
        cooMasterInfoReq.setCategory(categoryId);
        cooMasterInfoReq.setServeId(serveId);
        List<CooMasterInfoResp> cooMasterInfoList = cooperaMasterApi.getCooMasterInfo(cooMasterInfoReq);

        log.info("======getCooMasterInfo===rqt={}, response={}", cooMasterInfoReq, JSON.toJSONString(cooMasterInfoList));
        if (CollectionUtils.isNotEmpty(cooMasterInfoList)) {
            List<CooMasterInfoResp> suspendCooMasterList = cooMasterInfoList.stream().filter(cooMasterInfoResp -> Objects.nonNull(cooMasterInfoResp.getIsSuspendAppoint()) && cooMasterInfoResp.getIsSuspendAppoint() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(suspendCooMasterList)) {
                List<Long> suspendCooMasterIdList = suspendCooMasterList.stream().map(CooMasterInfoResp::getMasterId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(suspendCooMasterIdList)) {
                    return agreementMasterIdSet.stream().filter(masterId -> (!suspendCooMasterIdList.contains(masterId))).collect(Collectors.toSet());
                }
            }
        }
        return agreementMasterIdSet;
    }


    @Resource
    private RecruitMasterApi recruitMasterApi;

    public List<AgreementMaster> filterByNewContractRestStatus(List<AgreementMaster> agreementMasterList){
        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }
        if(agreementMasterList.get(0).getSceneId() != 4){
            return agreementMasterList;
        }

        return agreementMasterList.stream().filter(agreementMaster -> {
            GetMasterCurrentLeaveRequestRqt rqt = new GetMasterCurrentLeaveRequestRqt();
            rqt.setMasterId(Long.valueOf(agreementMaster.getMasterId()));
            rqt.setNowTime(new Date());
            rqt.setSceneId(4L);
            GetMasterCurrentLeaveRequestResp resp = recruitMasterApi.getMasterCurrentLeaveRequest(rqt);
            return Objects.isNull(resp) ? true : Objects.isNull(resp.getLeaveRequestStartTime()) ? true : false;
        }).collect(Collectors.toList());

    }


    private Set<Long> filterByCompleteOrderNum(Set<Long> agreementMasterIdSet, Integer categoryId) {

        GetMstIndexValueVo getMstIndexValueVo = new GetMstIndexValueVo();
        GetMstIndexValueVo.ParamRqt paramRqt = new GetMstIndexValueVo.ParamRqt();
        paramRqt.setIndexName("last180d_serve_complete_order_num");
        paramRqt.setDimensionInfo(new GetMstIndexValueVo.DimensionInfo(String.valueOf(categoryId)));
        getMstIndexValueVo.setIndexNames(Collections.singletonList(paramRqt));
        getMstIndexValueVo.setAccountIds(agreementMasterIdSet.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList()));
        JSONObject mstIndexValue = bigdataOpenServiceApi.getMstIndexValue(getMstIndexValueVo);
        log.info("======getMstIndexValue===rqt={}, response={}", JSON.toJSONString(getMstIndexValueVo), mstIndexValue);

        List<Long> masterIdList = new ArrayList<>();

        if (mstIndexValue != null) {
            mstIndexValue.forEach((k, v) -> {
                JSONArray jsonArray = (JSONArray) v;
                String value = (String) ((JSONObject) jsonArray.get(0)).get("value");

                int serveCompleteOrderNum = 0;
                if (com.wanshifu.framework.utils.StringUtils.isNotBlank(value) && com.wanshifu.framework.utils.StringUtils.isNotBlank(k)) {
                    serveCompleteOrderNum = Integer.parseInt(value);
                }
                if (serveCompleteOrderNum >= agreementAutoOfferPriceServeCompleteNumber) {
                    masterIdList.add(Long.parseLong(k));
                }
            });
        }

        if (CollectionUtils.isNotEmpty(masterIdList)) {
            return agreementMasterIdSet.stream().filter(masterId -> (masterIdList.contains(masterId))).collect(Collectors.toSet());
        } else {
            return Collections.emptySet();
        }

    }


    public Set<Long> filterByCompleteOrderNum(Set<Long> agreementMasterIdSet, Integer categoryId,String serveIds){

        Set<Long> bigdataConditionMasterIds = new HashSet<>();
        //师傅近180天完工过该类目（一级类目）总包直接指派+平台指派订单
        Set<Long> bigdataLevel1ConditionMasterIds = new HashSet<>();

        if (agreementAutoGrabServeCompleteNumber > 0) {
            GetMstIndexValueVo getMstIndexValueVo = new GetMstIndexValueVo();
            GetMstIndexValueVo.ParamRqt paramRqt = new GetMstIndexValueVo.ParamRqt();
            paramRqt.setIndexName("last180d_serve_complete_order_num");
            paramRqt.setDimensionInfo(new GetMstIndexValueVo.DimensionInfo(String.valueOf(categoryId)));
            getMstIndexValueVo.setIndexNames(Collections.singletonList(paramRqt));
            getMstIndexValueVo.setAccountIds(agreementMasterIdSet.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList()));
            JSONObject mstIndexValue = bigdataOpenServiceApi.getMstIndexValue(getMstIndexValueVo);
            log.info("======getMstIndexValue===rqt={}, response={}", JSON.toJSONString(getMstIndexValueVo), mstIndexValue);
            if (mstIndexValue != null) {
                mstIndexValue.forEach((k, v) -> {
                    JSONArray jsonArray = (JSONArray) v;
                    String value = (String) ((JSONObject) jsonArray.get(0)).get("value");
                    if (com.wanshifu.framework.utils.StringUtils.isNotBlank(value)
                            && Integer.parseInt(value) >= agreementAutoGrabServeCompleteNumber
                            && com.wanshifu.framework.utils.StringUtils.isNotBlank(k)) {
                        bigdataLevel1ConditionMasterIds.add(Long.parseLong(k));
                    }
                });
            }
        } else {
            bigdataLevel1ConditionMasterIds.addAll(agreementMasterIdSet);
        }

        //获取师傅对应指标:师傅近半年对应二级服务完工量
        if (!CollUtil.isEmpty(bigdataLevel1ConditionMasterIds)) {

            if (agreementAutoGrabLv2ServeServeCompleteNumber > 0) {
                GetLevel2IdsByServeIdsReq getLevel2IdsByServeIdsReq = new GetLevel2IdsByServeIdsReq();
                getLevel2IdsByServeIdsReq.setServeIds(getServeIds(serveIds));
                //查询基础平台,根据订单子集服务id集,获取对应二级服务
                List<Long> level2IdsByServeIds = serveServiceApi.getLevel2IdsByServeIds(getLevel2IdsByServeIdsReq);
                if ((!CollUtil.isEmpty(level2IdsByServeIds)) && level2IdsByServeIds.size() > 0) {
                    //查询订单二级服务,批量师傅对应180天的完工量
                    log.info("======getMstServeLv2IndexPram===masterId={}, goodsLv2={}", JSON.toJSONString(bigdataLevel1ConditionMasterIds), JSON.toJSONString(level2IdsByServeIds));
                    List<GetMasterJiedanInfoResp> masterJiedanInfo = masterBigDataOpenApi.getMstServeLv2Index(StrUtil.join(",", bigdataLevel1ConditionMasterIds), StrUtil.join(",", CollUtil.distinct(level2IdsByServeIds)));
                    log.info("======getMstServeLv2Index===masterId={}, goodsLv2={}, response={}", bigdataLevel1ConditionMasterIds, level2IdsByServeIds, JSON.toJSONString(masterJiedanInfo));
                    //师傅近180天完工订单二级服务 总包直接指派+平台指派订单
                    List<Long> getMstServeLv2Index = new ArrayList<>();
                    if (CollUtil.isNotEmpty(masterJiedanInfo) && masterJiedanInfo.size() > 0) {
                        masterJiedanInfo.forEach(info -> {
                            if (info.getLast180dTobCompCnt() >= agreementAutoGrabLv2ServeServeCompleteNumber) {
                                getMstServeLv2Index.add(info.getMasterId());
                                bigdataConditionMasterIds.addAll(CollUtil.distinct(getMstServeLv2Index));
                            }
                        });
                    }
                }
            } else {
                bigdataConditionMasterIds.addAll(bigdataLevel1ConditionMasterIds);
            }
        }
        return bigdataConditionMasterIds;
    }



    /**
     * 匹配协议师傅
     * @param orderBase
     * @return
     */
    private List<AgreementMaster> matchEnterpriseAgreementMaster(OrderBase orderBase){

        List<String> cooperationBusinessList = getCooperationBusiness(orderBase);
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();


        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));

        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));


        boolQueryBuilder.must(QueryBuilders.termQuery("isOrderBackstop",1));



        boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderBase.getFourthDivisionId())));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", "0"));
        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if(StringUtils.isBlank(orderBase.getServeIds())){
            return null;
        }
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));



        Arrays.asList(orderBase.getServeIds().split(",")).stream().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType","noCooperationPrice"));

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", "geo_fence"));


        //TODO 添加指定用户查询条件


        log.info("search agreementmaster request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return agreementMasterList;
    }


    /**
     * 获取业务范围
     * @param orderBase
     * @return
     */
    public List<String> getCooperationBusiness(OrderBase orderBase) {
        return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.ENTERPRISE.code);
    }


    @Resource
    private HBaseClient hBaseClient;

    private List<AgreementMaster> filterByDailyOrderCnt(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = agreementMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("recruit_id");
        fieldColumnList.add("order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList,fieldColumnList,"master_recruit_daily");
        Map<String,Integer> orderCntMap = new HashMap<>();

        if(Objects.nonNull(resultArray) && resultArray.size() > 0){
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String)jsonObject.get("master_id");
                String recruitId = (String)jsonObject.get("recruit_id");
                if(jsonObject.containsKey("order_cnt")){
                    String orderCnt = (String)jsonObject.get("order_cnt");
                    if(org.apache.commons.lang.StringUtils.isNotBlank(orderCnt)){
                        orderCntMap.put(masterId + "_" + recruitId,Integer.valueOf(orderCnt));
                    }
                }
            }
        }


        return agreementMasterList.stream().filter(agreementMaster -> agreementMaster.getMaxDailyOrder() == 0 || orderCntMap.getOrDefault(agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId(),0) < agreementMaster.getMaxDailyOrder()).collect(Collectors.toList());
    }


    private boolean checkAgreementMasterAutoReceiveCondition(OrderBase orderBase,OrderGrab orderGrab,
                                                             List<OrderServiceAttributeInfo> orderServiceAttributeInfoList){


        if(Objects.isNull(orderGrab)){
            return false;
        }

        if(CollectionUtils.isEmpty(orderServiceAttributeInfoList)){
            return false;
        }

        if(orderServiceAttributeInfoList.size() != 1){
            return false;
        }

        if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            return false;
        }

        Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());

        if(Objects.isNull(cityDivisionId) || cityDivisionId == 0){
            return false;
        }

        Integer appointType = orderGrab.getAppointType();

        boolean isAgreementAutoReceive = false;

        if(AppointType.OPEN.value.equals(appointType)){
            isAgreementAutoReceive = this.isAgreementAutoOfferPrice(cityDivisionId);
        }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
            isAgreementAutoReceive = this.isAgreementAutoGrab(cityDivisionId);
        }

        if(!isAgreementAutoReceive){
            return false;
        }


        return orderGrab != null && AccountType.USER.code.equals(orderBase.getAccountType()) &&
                orderBase.getBusinessLineId() == 1 && "site".equals(orderBase.getOrderFrom()) &&
                OrderStatus.TRADING.code.equals(orderBase.getOrderStatus()) &&
                Objects.nonNull(orderBase.getFourthDivisionId()) &&
                orderGrab.getConfirmServeStatus() != 1;
    }


    public boolean isAgreementAutoOfferPrice(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(agreementAutoOfferPriceCityList)){
                return false;
            }

            if ("0".equals(agreementAutoOfferPriceCityList)) {
                return false;
            }

            if ("all".equals(agreementAutoOfferPriceCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(agreementAutoOfferPriceCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }
        return false;
    }



    public boolean isAgreementAutoGrab(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(agreementAutoOfferPriceCityList)){
                return false;
            }

            if ("0".equals(agreementAutoOfferPriceCityList)) {
                return false;
            }

            if ("all".equals(agreementAutoOfferPriceCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(agreementAutoOfferPriceCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }
        return false;
    }
}
