package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.message.LongTailBatchPushMessage;
import com.wanshifu.master.order.push.domain.rqt.LongTailOrderPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;

/**
 * 长尾单推单
 */
public interface LongTailPushService {


    /**
     * 匹配长尾单策略
     * @param orderDetailData
     * @param orderVersion
     * @param maxOfferNum
     */
    void matchLongTailStrategy(Integer businessLineId,OrderDetailData orderDetailData, String orderVersion, Integer maxOfferNum);

    /**
     * 长尾单推单
     * @param longTailOrderPushRqt
     * @return
     */
    int longTailPush(LongTailOrderPushRqt longTailOrderPushRqt);


    void longTailBatchPush(LongTailBatchPushMessage message);



    }
