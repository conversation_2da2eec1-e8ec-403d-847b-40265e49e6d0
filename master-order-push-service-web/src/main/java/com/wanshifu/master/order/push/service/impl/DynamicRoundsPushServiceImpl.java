package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.common.PushMaster;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.po.OrderMasterPush;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.OrderMasterPushRepository;
import com.wanshifu.master.order.push.service.DynamicRoundsPushService;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DynamicRoundsPushServiceImpl implements DynamicRoundsPushService {

    @Resource
    private OrderMasterPushRepository orderMasterPushRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private OrderDataBuilder orderDataBuilder;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Override
    public DynamicRoundsMasterList getPushMasterList(List<PushMaster> pushMasterList, PushRuleConfig pushRuleConfig, Integer appointType){

        int bestOfferNum = pushRuleConfig.getBestOfferNum();
        List<PushRuleConfig.DynamicRoundsRule> dynamicRoundsRuleList = pushRuleConfig.getDynamicRoundsRuleList();
        final List<DynamicRoundsMaster> dynamicRoundsMasterList = new ArrayList<>();
        dynamicRoundsRuleList.forEach(dynamicRoundsRule -> {
            DynamicRoundsMaster dynamicRoundsMaster = new DynamicRoundsMaster();
            BeanUtils.copyProperties(dynamicRoundsRule,dynamicRoundsMaster);
            dynamicRoundsMasterList.add(dynamicRoundsMaster);
        });

        for(PushMaster pushMaster :  pushMasterList){
            for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList){
                if(dynamicRoundsMaster.getPushScoreStartValue().compareTo(pushMaster.getScore()) <= 0 && dynamicRoundsMaster.getPushScoreEndValue().compareTo(pushMaster.getScore()) > 0){
                    pushMaster.setRounds(dynamicRoundsMaster.getRounds());
                    dynamicRoundsMaster.getMasterList().add(pushMaster);
                    break;
                }
            }
        }

        Map<Integer, List<DynamicRoundsMaster>> dynamicRoundsMasterMap = dynamicRoundsMasterList.stream().collect(Collectors.groupingBy(item -> item.getBatchNum()));

        List<Integer> batchNumList = new ArrayList(dynamicRoundsMasterMap.keySet());

        Collections.sort(batchNumList);

        Integer totalOfferNum = 0;

        for(Integer batchNum : batchNumList){

            List<DynamicRoundsMaster> roundsMasterList = dynamicRoundsMasterMap.get(batchNum);

            //剩余应报价人数
            int diffOfferNum = bestOfferNum - totalOfferNum;
            if(diffOfferNum <= 0){
                break;
            }

            for(DynamicRoundsMaster dynamicRoundsMaster : roundsMasterList){

                if(CollectionUtils.isEmpty(dynamicRoundsMaster.getMasterList())){
                    continue ;
                }

                dynamicRoundsMaster.setIsFirstDynamicRoundsPush(1);


                //本轮需要报价人数（按切量分配）
                int roundsOfferNum = (diffOfferNum * dynamicRoundsMaster.getDeliveryPercent()) / 100;

                if((diffOfferNum * dynamicRoundsMaster.getDeliveryPercent()) % 100 > 0){
                    roundsOfferNum = roundsOfferNum + 1;
                }


                //本轮次能够最大报价人数
                int roundsMaxOfferNum = (int)Math.floor((dynamicRoundsMaster.getMasterList().size()  * dynamicRoundsMaster.getOfferRate().doubleValue()) / (100));

                if(roundsMaxOfferNum >= roundsOfferNum){
                    //本轮次能够最大报价人数大于本轮次需要报价人数，人数足够则足量的师傅人数
                    int pushMasterNum = (int)Math.floor((roundsOfferNum * 100) / dynamicRoundsMaster.getOfferRate().doubleValue());
                    dynamicRoundsMaster.setAlternativeMasterList(dynamicRoundsMaster.getMasterList().subList(pushMasterNum,dynamicRoundsMaster.getMasterList().size()));
                    dynamicRoundsMaster.setMasterList(dynamicRoundsMaster.getMasterList().subList(0,pushMasterNum));
                    totalOfferNum = totalOfferNum + roundsOfferNum;
                }else{
                    //本轮次能够最大报价人数小于本轮次需要报价人数，人数不足则推送本轮次全部师傅
                    totalOfferNum = totalOfferNum + roundsMaxOfferNum;
                }

            }

            if(totalOfferNum >= bestOfferNum){
                break;
            }
        }

        if(totalOfferNum < bestOfferNum){
           for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList){
               if(!CollectionUtils.isEmpty(dynamicRoundsMaster.getAlternativeMasterList())){
                   int diffOfferNum = bestOfferNum - totalOfferNum;
                   int roundsMaxOfferNum = (int)Math.floor((dynamicRoundsMaster.getAlternativeMasterList().size()  * dynamicRoundsMaster.getOfferRate().doubleValue()) / (100));
                   if(roundsMaxOfferNum >= diffOfferNum){
                       int pushMasterNum = (int)Math.floor((diffOfferNum * 100) / dynamicRoundsMaster.getOfferRate().doubleValue());
                       dynamicRoundsMaster.setMasterList(Stream.of(dynamicRoundsMaster.getAlternativeMasterList().subList(0,pushMasterNum),dynamicRoundsMaster.getMasterList()).
                               flatMap(Collection::stream).collect(Collectors.toList()));
                       dynamicRoundsMaster.setAlternativeMasterList(dynamicRoundsMaster.getAlternativeMasterList().subList(pushMasterNum,dynamicRoundsMaster.getAlternativeMasterList().size()));
                       totalOfferNum = totalOfferNum + diffOfferNum;
                       break;

                   }else{
                       dynamicRoundsMaster.setMasterList(Stream.of(dynamicRoundsMaster.getAlternativeMasterList(),dynamicRoundsMaster.getMasterList()).
                               flatMap(Collection::stream).collect(Collectors.toList()));
                       totalOfferNum = totalOfferNum + roundsMaxOfferNum;
                   }
               }
           }
        }


        for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList){


            if(CollectionUtils.isEmpty(dynamicRoundsMaster.getAlternativeMasterList())){
                continue;
            }

            int count = 0;
            if(!CollectionUtils.isEmpty(dynamicRoundsMaster.getMasterList())){
                count = (int)dynamicRoundsMaster.getMasterList().stream().filter(master -> master.isNewMaster()).count();
            }

            if(count < dynamicRoundsMaster.getMasterNum()){
                int diffMasterNum = dynamicRoundsMaster.getMasterNum() - count;
                List<PushMaster> addMasterList = new ArrayList<>();
                for(PushMaster pushMaster : dynamicRoundsMaster.getAlternativeMasterList()){
                    if(diffMasterNum > 0){
                        if(pushMaster.isNewMaster()){
                            addMasterList.add(pushMaster);
                            diffMasterNum = diffMasterNum - 1;
                        }
                    }else{
                        break;
                    }
                }
                if(!CollectionUtils.isEmpty(addMasterList)){
                    Set<String> addMasterSet = addMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
                    List<PushMaster> leftMasterList = dynamicRoundsMaster.getAlternativeMasterList().stream().filter(pushMaster -> !addMasterSet.contains(pushMaster.getMasterId())).collect(Collectors.toList());
                    dynamicRoundsMaster.setAlternativeMasterList(leftMasterList);
                    dynamicRoundsMaster.getMasterList().addAll(addMasterList);
                }
            }
        }

        getPriorityPushMasterList(dynamicRoundsMasterList);

        DynamicRoundsMasterList list = new DynamicRoundsMasterList();
        list.setDynamicRoundsMasterList(dynamicRoundsMasterList);
        if(totalOfferNum < bestOfferNum){
            list.setIsMasterEnough(false);
        }
        return list;
    }


    private void getPriorityPushMasterList(List<DynamicRoundsMaster> dynamicRoundsMasterList){
        for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList){
            if(CollectionUtils.isEmpty(dynamicRoundsMaster.getAlternativeMasterList())){
                continue;
            }
            List<PushMaster> priorityPushMasterList = dynamicRoundsMaster.getAlternativeMasterList().stream().filter(pushMaster -> pushMaster.isPriorityPush()).collect(Collectors.toList());
            List<PushMaster> alternativePushMasterList = dynamicRoundsMaster.getAlternativeMasterList().stream().filter(pushMaster -> !pushMaster.isPriorityPush()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(priorityPushMasterList)){
                dynamicRoundsMaster.getMasterList().addAll(priorityPushMasterList);
            }
            dynamicRoundsMaster.setAlternativeMasterList(alternativePushMasterList);
        }
    }



    @Override
    public Integer wheelRoundsPush(WheelRoundsPushMessage message){
        String orderVersion = message.getOrderVersion();
        Long globalOrderId = message.getGlobalOrderId();
        Long orderId = message.getOrderId();

        OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
        rqt.setMasterOrderId(orderId);
        OrderDetailData orderDetailData = orderDataBuilder.build(rqt);
        if(!Objects.nonNull(orderDetailData)){
            return 1;
        }
        PushRuleConfig pushRuleConfig = message.getPushRuleConfig();
        PushRuleConfig.DynamicRoundsRule dynamicRoundsRule = message.getDynamicRoundsRule();
        PushRuleConfig.WheelRoundRule wheelRoundRule = message.getWheelRoundRule();


        List<OrderMasterPush> roundsPushMasterList = null;

        if(Objects.isNull(wheelRoundRule)){
            roundsPushMasterList = orderMasterPushRepository.getOrderMasterPushList(globalOrderId,orderVersion,null,null);
        }else{
            if("alternate_personnel".equals(wheelRoundRule.getPushTarget())){
                roundsPushMasterList = orderMasterPushRepository.getOrderMasterPushList(globalOrderId,orderVersion,Collections.singleton(dynamicRoundsRule.getRounds()),1);
                //候补师傅推送
            }else  if("next_batch".equals(wheelRoundRule.getPushTarget())){
                List<PushRuleConfig.DynamicRoundsRule> dynamicRoundsRuleList = pushRuleConfig.getDynamicRoundsRuleList();
                dynamicRoundsRuleList = dynamicRoundsRuleList.stream().filter(dynamicRoundsRule1 -> dynamicRoundsRule1.getBatchNum() > dynamicRoundsRule.getBatchNum()).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(dynamicRoundsRuleList)){
                    return 1;
                }
                Set<Integer> batchNumSet = dynamicRoundsRuleList.stream().map(PushRuleConfig.DynamicRoundsRule::getBatchNum).collect(Collectors.toSet());
                Integer minBatchNum = Collections.min(batchNumSet);
                Set<String> roundsSet = dynamicRoundsRuleList.stream().filter(dynamicRoundsRule1 -> dynamicRoundsRule1.getBatchNum().equals(minBatchNum)).map(PushRuleConfig.DynamicRoundsRule::getRounds).collect(Collectors.toSet());
                roundsPushMasterList = orderMasterPushRepository.getOrderMasterPushList(globalOrderId,orderVersion,roundsSet,null);

            }
        }

        if(CollectionUtils.isEmpty(roundsPushMasterList)){
            return 1;
        }
        Set<String> masterSet = roundsPushMasterList.stream().map(OrderMasterPush::getMasterId).collect(Collectors.toSet());
        pushControllerFacade.wheelRoundsPush(orderDetailData,orderVersion,masterSet,message.getCommonFeature());
        return 1;
    }

}
