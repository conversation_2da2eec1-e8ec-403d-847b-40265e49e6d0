package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.filterStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.filterStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface FilterStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    FilterStrategy detail(DetailRqt rqt);

    SimplePageInfo<FilterStrategy> list(ListRqt rqt);

    int enable(EnableRqt rqt);

    int delete(DeleteRqt rqt);

    List<FilterStrategySnapshot> selectBySnapshotIdList(@Valid List<Long> snapshotIdList);

}