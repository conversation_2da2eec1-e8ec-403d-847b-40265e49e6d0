package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AccountRole;
import com.wanshifu.master.order.push.domain.po.OrderTechniqueVerifyMasterMatch;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class OrderTechniqueVerifyMasterMatchRepository extends BaseRepository<OrderTechniqueVerifyMasterMatch> {




}