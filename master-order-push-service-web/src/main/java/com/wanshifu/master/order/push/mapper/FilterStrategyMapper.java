package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 召回策略Mapper
 * <AUTHOR>
 */
public interface FilterStrategyMapper extends IBaseCommMapper<FilterStrategy> {

    List<FilterStrategy> selectList(@Param("businessLineId") Long businessLineId, @Param("strategyName") String strategyName, @Param("strategyStatus") Integer strategyStatus, @Param("createStartTime") Date createStartTime, @Param("createEndTime") Date createEndTime, @Param("categoryIds") List<Long> categoryIds,
                                    @Param("orderFlag") String orderFlag);

}