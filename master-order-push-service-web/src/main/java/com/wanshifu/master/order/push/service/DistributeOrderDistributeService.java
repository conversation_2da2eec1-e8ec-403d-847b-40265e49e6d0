package com.wanshifu.master.order.push.service;

import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.domain.dto.SupportMasterPushResultMessage;
import com.wanshifu.master.order.push.domain.rqt.UpdateDirectAppointFailReasonRqt;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;

/**
 * <AUTHOR>
 * @date 2025/2/28 15:18
 */
@Deprecated
public interface DistributeOrderDistributeService {

    Integer supportMasterOrderDistribute(SupportMasterPushResultMessage message);

    Integer updateDirectAppointFailReason(UpdateDirectAppointFailReasonRqt rqt);

    DefaultContext<String, Object> initOrderFeatures(OrderBase orderBase, OrderExtraData orderExtraData, OrderGrab orderGrab);
}
