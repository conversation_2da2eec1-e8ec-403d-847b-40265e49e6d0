package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.wanshifu.framework.core.page.SimplePageInfo;

import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;

import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;

import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;

import com.wanshifu.master.order.push.mapper.OrderRoutingStrategyMapper;
import com.wanshifu.master.order.push.repository.OrderRoutingStrategyRepository;
import com.wanshifu.master.order.push.service.OrderRoutingStrategyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OrderRoutingStrategyServiceImpl implements OrderRoutingStrategyService {

    @Resource
    private OrderRoutingStrategyRepository orderRoutingStrategyRepository;


    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "路由管理", level2MenuName = "路由应用管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt){
        checkCityCategoryUniq(rqt.getCityIds(),rqt.getCategoryIds(),rqt.getBusinessLineId(),null);
        orderRoutingStrategyRepository.insertStrategy(rqt.getBusinessLineId(),rqt.getStrategyName(),rqt.getStrategyDesc(),rqt.getOrderFrom(),rqt.getOrderTag(),rqt.getCategoryIds(),
                rqt.getCityIds(),rqt.getMatchRoutingId(),rqt.getCreateAccountId());
        return 1;
    }


    private void checkCityCategoryUniq(String cityIds, String categoryIds, Integer businessLineId, Integer strategyId) {
        List<String> cityIdList = Arrays.asList(cityIds.split(","));
        List<String> categoryIdList = Arrays.asList(categoryIds.split(","));
        OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectByCityAndCategory(cityIdList,categoryIdList,strategyId,businessLineId);
        if (orderRoutingStrategy != null) {
            Assert.isNull(true, StrUtil.format("城市及类目与策略【{}】存在重复!",orderRoutingStrategy.getStrategyName()));
        }
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "路由管理", level2MenuName = "路由应用管理",
            tableName = "order_routing_strategy", mapperClass = OrderRoutingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderRoutingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt){
        checkCityCategoryUniq(rqt.getCityIds(),rqt.getCategoryIds(),rqt.getBusinessLineId(),rqt.getStrategyId());
        orderRoutingStrategyRepository.updateStrategy(rqt.getStrategyId(),rqt.getBusinessLineId(),rqt.getStrategyName(),rqt.getStrategyDesc(),rqt.getOrderFrom(),rqt.getOrderTag(),rqt.getCategoryIds(),
                rqt.getCityIds(),rqt.getMatchRoutingId(),rqt.getUpdateAccountId());
        return 1;
    }

    @Override
    public OrderRoutingStrategy detail(DetailRqt rqt){
        return orderRoutingStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "路由管理", level2MenuName = "路由应用管理",
            tableName = "order_routing_strategy", mapperClass = OrderRoutingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderRoutingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt) {
        Integer strategyId = rqt.getStrategyId();
        orderRoutingStrategyRepository.selectByPrimaryKey(strategyId);
        return orderRoutingStrategyRepository.updateStatus(strategyId, rqt.getStrategyStatus(),rqt.getUpdateAccountId());
    }


    @Override
    public SimplePageInfo<OrderRoutingStrategy> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);


        List<Long> categoryIdList = null;
        if(StringUtils.isNotEmpty(rqt.getCategoryIds())){
            categoryIdList = Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        List<OrderRoutingStrategy> orderRoutingStrategyList = orderRoutingStrategyRepository.selectList(rqt.getBusinessLineId(),rqt.getStrategyStatus(),rqt.getStrategyName(),rqt.getCreateStartTime(),rqt.getCreateEndTime(),
                categoryIdList,rqt.getCityId());

        SimplePageInfo<OrderRoutingStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(orderRoutingStrategyList);
        return listRespSimplePageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "路由管理", level2MenuName = "路由应用管理",
            tableName = "order_routing_strategy", mapperClass = OrderRoutingStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderRoutingStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt){
        OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
        Assert.isTrue(orderRoutingStrategy.getStrategyStatus() == 0, "非禁用状态不可删除!");
        return orderRoutingStrategyRepository.softDeleteByStrategyId(rqt.getStrategyId());
    }
}
