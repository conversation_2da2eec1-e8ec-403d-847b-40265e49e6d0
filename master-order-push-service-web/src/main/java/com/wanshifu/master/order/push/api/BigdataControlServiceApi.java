package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.domain.api.response.GetGroupAccountsResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetGroupAccountsRqt;
import com.wanshifu.master.order.push.domain.dto.BigDataOpenApiDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(value = "bigdata-control-service",
        url = "${wshifu.bigdata-control-service.url}",
        configuration = {DefaultEncoder.class, BigDataOpenApiDecoder.class, BigDataOpenApiDecoder.ApiErrorDecoder.class}
)
public interface BigdataControlServiceApi {

    /**
     * 获取人群包-pool版本
     * <br/>
     * <a href="http://dev-api-manage.wanshifu.com:3000/project/1190/interface/api/82764"></a>
     *
     * @param rqt 人群信息
     * @return 人群成员ids
     */
    @FeignTimeout(connectTimeoutMillis = 2000, readTimeoutMillis = 1000)
    @PostMapping("/persona/getGroupAccountListFromPool")
    GetGroupAccountsResp getGroupAccounts(@RequestBody GetGroupAccountsRqt rqt);

}
