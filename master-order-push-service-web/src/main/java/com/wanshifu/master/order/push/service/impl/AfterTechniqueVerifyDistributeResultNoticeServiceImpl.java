package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.po.AfterTechniqueVerifyMasterMatchLog;
import com.wanshifu.master.order.push.repository.AfterTechniqueVerifyMasterMatchLogRepository;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/2 10:10
 */
@Service
@Slf4j
public class AfterTechniqueVerifyDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private PushRecordService pushRecordService;

    @Resource
    private AfterTechniqueVerifyMasterMatchLogRepository afterTechniqueVerifyMasterMatchLogRepository;

    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage) {
        Long orderId = orderDistributeResultMessage.getOrderId();
        log.info("afterTechniqueVerifyOrder distributeResultNotice,orderId:{},message:{}", orderId, JSONUtil.toJsonStr(orderDistributeResultMessage));


        int distributeResult;

        List<Long> successMasterList = orderDistributeResultMessage.getSuccessMasterList();
        if(CollectionUtils.isNotEmpty(successMasterList)){
            distributeResult = 1;

            Long masterId = successMasterList.get(0);
            //抢单成功，更新师傅 当日有效抢单量（技能验证后派单） 指标
            pushRecordService.increaseAfterTechniqueVerifyMasterGrabCntDaily(masterId, DateFormatterUtil.getNow());

            this.updateAfterTechniqueVerifyMasterMatchLog(orderId, masterId, 1, "");


            List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
            List<JSONObject> failMasterVos = orderDistributeResultMessage.getMasterFailVos();
            if (CollectionUtil.isNotEmpty(failMasterList) && CollectionUtil.isNotEmpty(failMasterVos)) {
                Map<Long, JSONObject> failMasterMap = failMasterVos.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getLong("masterId"), jsonObject -> jsonObject));
                for (Long failMasterId : failMasterList) {
                    this.updateAfterTechniqueVerifyMasterMatchLog(orderId, failMasterId, 0, failMasterMap.getOrDefault(failMasterId, new JSONObject()).toJSONString());
                }

            }

        }else{
            distributeResult = 0;
            List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
            List<JSONObject> failMasterVos = orderDistributeResultMessage.getMasterFailVos();
            if (CollectionUtil.isNotEmpty(failMasterList) && CollectionUtil.isNotEmpty(failMasterVos)) {
                Map<Long, JSONObject> failMasterMap = failMasterVos.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getLong("masterId"), jsonObject -> jsonObject));
                for (Long masterId : failMasterList) {
                    this.updateAfterTechniqueVerifyMasterMatchLog(orderId, masterId, 0, failMasterMap.getOrDefault(masterId, new JSONObject()).toJSONString());
                }

            }

        }


        if(distributeResult == 0){
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderId);
            pushQueueService.sendDelayPushMessage(100L, JSON.toJSONString(rqt));

            return 0;
        }

        return 1;
    }


    private void updateAfterTechniqueVerifyMasterMatchLog(Long orderId, Long masterId, Integer isAutoGrabSuccess, String autoGrabFailReason) {
        Condition condition = new Condition(AfterTechniqueVerifyMasterMatchLog.class);

        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);

        AfterTechniqueVerifyMasterMatchLog log = new AfterTechniqueVerifyMasterMatchLog();
        log.setOrderId(orderId);
        log.setMasterId(masterId);
        log.setIsAutoGrabSuccess(isAutoGrabSuccess);
        log.setAutoGrabFailReason(autoGrabFailReason);
        log.setUpdateTime(new Date());
        afterTechniqueVerifyMasterMatchLogRepository.updateByConditionSelective(log, condition);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_AFTER_TECHNIQUE_VERIFY.getCode(), this);
    }
}
