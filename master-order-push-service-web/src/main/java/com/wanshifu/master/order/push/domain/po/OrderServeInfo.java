package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;

@Data
@Document(indexAlias = "order_serve_info", type = "order_serve_info")
public class OrderServeInfo {

    /**
     * 订单服务id
     */
    private String orderServeId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 服务状态
     */
    private String serveStatus;

    /**
     * 预约开始时间
     */
    private Long reserveStartTime;

    /**
     * 预约结束时间
     */
    private Long reserveEndTime;

    /**
     * 期望上门时间
     */
    private String expectedDoorInDate;

    /**
     * 忙碌开始时间
     */
    private Long busyStartTime;

    /**
     * 忙碌结束时间
     */
    private Long busyEndTime;

    /**
     * 订单客户地址经纬度
     */
    private String orderLatLng;

    /**
     * 当前服务节点
     */
    private String currentServeNode;

    /**
     * 下一个服务节点
     */
    private String nextServeNode;

    /**
     * 遗留订单标记
     */
    private String historyMark;

    /**
     * 预约上门时间
     */
    private String reserveDoorInDate;

}
