//package com.wanshifu.master.order.push.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.ql.util.express.DefaultContext;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.dto.*;
//import com.wanshifu.master.order.push.domain.enums.AgreementMasterLabel;
//import com.wanshifu.master.order.push.domain.enums.DistributeType;
//import com.wanshifu.master.order.push.domain.enums.RecruitDistrictMethod;
//import com.wanshifu.master.order.push.domain.enums.RecruitPricingType;
//import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
//import com.wanshifu.master.order.push.domain.po.AgreementMaster;
//import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Service
//@Slf4j
//public class EnterpriseTechniqueVerifyMasterServiceImpl {
//
//    @Resource
//    private OrderDataBuilder orderDataBuilder;
//
//
//    /**
//     * 匹配协议师傅
//     * @param orderDetailData
//     * @param masterMatchCondition
//     * @return
//     */
//    private List<MasterBaseSearch> matchTechniqueVerifyMaster(OrderDetailData orderDetailData){
//
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(orderDetailData.getFourthDivisionId())));
//        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1L));
//        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
//        boolQueryBuilder.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
//        boolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
//        boolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));
//
//
//
////        boolQueryBuilder.must(QueryBuilders.termQuery("pushOrderStatus", 1));
//
//        Set<String> techniqueIdSet = Arrays.stream(orderDetailData.getOrderTechniques().split("\\|")).collect(Collectors.toSet());
//
//        //过滤非宜家师傅时，技能是隐藏条件，必须符合
//        BoolQueryBuilder shouldQuery = this.boolQueryBuilder("masterTechniqueIds",techniqueIdSet, 1);
//        boolQueryBuilder.must(shouldQuery);
//
//        log.info("search supportMaster request:" + boolQueryBuilder.toString());
//
//        List<MasterBaseSearch> supportMasterList = new ArrayList<>();
//        int pageNum = 1;
//        int pageSize = 200;
//        while(true){
//            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
//            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
//                supportMasterList.addAll(esResponse.getDataList());
//                pageNum++;
//            }else{
//                break;
//            }
//        }
//
//        return supportMasterList;
//    }
//
//
//        private List<AgreementMaster> matchTechniqueVerifyMaster(Long globalOrderTraceId){
//
//
//            OrderDetailData orderDetailData = orderDataBuilder.buildEnterpriseOrder(globalOrderTraceId);
//
//            List<MasterBaseSearch> masterBaseSearchList = matchTechniqueVerifyMaster(orderDetailData);
//
//
//
//            return agreementMasterList;
//        }
//    }
//
//
//    public Integer supportMasterOrderDistribute(SupportMasterPushResultMessage message){
//
//        Long globalOrderId = message.getGlobalOrderId();
//
//
//        try{
//            DefaultContext<String, Object> orderFeatureContext = initOrderFeatures(message.getGlobalOrderId());
//
//            Set<String> masterSet = message.getMasterSet();
//            OrderDistributeRqt orderDistributeRqt = new OrderDistributeRqt();
//            orderDistributeRqt.setMasterOrderId(globalOrderId);
//            orderDistributeRqt.setMasterSet(message.getMasterSet());
//
//            //获取策略
//            final OrderDistributor orderDistributor = distributeFactory
//                    .matchDistributor(orderDataBuilder.buildEnterpriseOrder(globalOrderId),orderFeatureContext, DistributeType.NEW_MASTER_SUPPORT.getCode());
//            if (!orderDistributor.isMatched()) {
//                this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
//                log.info("no matchDistributor matched!:{}",globalOrderId);
//                return 0;
//            }
//
//            //获取特征
//            featureRepository.orderFeatureReplenish(orderFeatureContext,orderDistributor.getOrderFeatureSet());
//            DefaultContext<String, DefaultContext<String, Object>> masterFeatures = featureRepository.masterFeatures(masterSet, orderDistributor.getMasterFeatureSet(),orderFeatureContext);
//            log.info("master_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]",globalOrderId,orderFeatureContext,masterFeatures);
//
//
//            //过滤排序
//            //过滤排序
//            final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
//                    .withOrderId(String.valueOf(globalOrderId))
//                    .withType(FieldConstant.RANK_DETAIL)
//                    .build();
//
//            final List<ScorerMaster> scorerMasterList = orderDistributor
//                    .rank(masterSet,orderFeatureContext,masterFeatures,rankDetail);
//
//
//            log.info("rankDetail: " + JSON.toJSONString(rankDetail));
//
//
//            if(CollectionUtils.isEmpty(scorerMasterList)){
//                this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
//                return 0;
//            }
//
//            String distributeRule = orderDistributor.getDistributeRule();
//
//            ScorerMaster scorerMaster = distribute(scorerMasterList,distributeRule);
//
//            log.info("scorerMasterList:" + JSON.toJSONString(scorerMaster));
//
//            this.sendSupportMasterMatchMessage(globalOrderId,scorerMaster,1L);
//
//            return 1;
//        }catch(Exception e){
//            log.error("supportMasterOrderDistribute error,message:" + JSON.toJSONString(message) );
//            this.sendSupportMasterMatchMessage(globalOrderId,null,1L);
//        }
//
//        return 0;
//
//    }
//
//
//}
