package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.mapper.StrategyCombinationMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 策略组合Repository
 * <AUTHOR>
 */
@Repository
public class StrategyCombinationRepository extends BaseRepository<StrategyCombination> {

    @Resource
    private StrategyCombinationMapper strategyCombinationMapper;


    public StrategyCombination selectByStrategyId(Long combinationId) {
        StrategyCombination strategyCombination = this.selectByPrimaryKey(combinationId);
        Assert.isTrue(strategyCombination != null && Objects.equals(strategyCombination.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return strategyCombination;
    }


    /**
     * 根据类目和城市查询策略组合
     * @param businessLineId
     * @param categoryId
     * @param cityId
     * @return
     */
    public StrategyCombination selectByCategoryIdAndCityId(String orderFlag,Integer businessLineId, String categoryId, String cityId){
        return CollectionUtils.getFirstSafety(strategyCombinationMapper.selectByCategoryIdAndCityId(orderFlag,businessLineId,categoryId,cityId));
    }

    /**
     * 创建组合策略
     */

    public Long create(String orderFlag,String combinationName, Long snapshotId, String combinationDesc, String categoryIds, String cityIds, String priorityStrategyJson, String alternateStrategyJson, Integer businessLineId, Long loginUserId) {

        StrategyCombination strategyCombination = new StrategyCombination();
        strategyCombination.setOrderFlag(orderFlag);
        strategyCombination.setCombinationName(combinationName);
        strategyCombination.setSnapshotId(snapshotId);
        strategyCombination.setCombinationDesc(combinationDesc);
        strategyCombination.setCategoryIds(categoryIds);
        strategyCombination.setCityIds(cityIds);
        strategyCombination.setPriorityStrategyCombination(priorityStrategyJson);
        strategyCombination.setAlternateStrategyCombination(alternateStrategyJson);
        strategyCombination.setBusinessLineId(businessLineId);
        strategyCombination.setUpdateAccountId(loginUserId);
        strategyCombination.setCreateAccountId(loginUserId);
        strategyCombination.setCombinationStatus(CommonConstant.STRATEGY_STATUS_0);
        this.insertSelective(strategyCombination);
        return strategyCombination.getCombinationId();
    }

    /**
     * 修改组合策略
     * @return
     */
    public int update(String orderFlag,Long combinationId, Long snapshotId, String combinationName, String combinationDesc, String categoryIds, String cityIds, String priorityStrategyJson, String alternateStrategyJson, Integer businessLineId, Long loginUserId) {
        StrategyCombination strategyCombination = new StrategyCombination();
        strategyCombination.setOrderFlag(orderFlag);
        strategyCombination.setCombinationId(combinationId);
        strategyCombination.setSnapshotId(snapshotId);
        strategyCombination.setCombinationName(combinationName);
        strategyCombination.setCombinationDesc(combinationDesc);
        strategyCombination.setCategoryIds(categoryIds);
        strategyCombination.setCityIds(cityIds);
        strategyCombination.setPriorityStrategyCombination(priorityStrategyJson);
        strategyCombination.setAlternateStrategyCombination(alternateStrategyJson);
        strategyCombination.setBusinessLineId(businessLineId);
        strategyCombination.setUpdateAccountId(loginUserId);
        return this.updateByPrimaryKeySelective(strategyCombination);
    }

    public int updateStatus(Long combinationId, Integer combinationStatus,Long updateAccountId) {
        StrategyCombination strategyCombination = new StrategyCombination();
        strategyCombination.setCombinationId(combinationId);
        strategyCombination.setCombinationStatus(combinationStatus);
        strategyCombination.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(strategyCombination);
    }

    public List<StrategyCombination> selectList(String orderFlag,Long businessLineId, List<Long> cityIdList, List<Long> categoryIdList, String combinationName, Integer combinationStatus, Date createStartTime, Date createEndTime) {
        Example example = new Example(StrategyCombination.class);
        Example.Criteria criteria = example.createCriteria();
        if (businessLineId != null) {
            criteria.andEqualTo("businessLineId", businessLineId);
        }
        if (StringUtils.isNotBlank(orderFlag)) {
            criteria.andEqualTo("orderFlag", orderFlag);
        }
        if (CollectionUtils.isNotEmpty(cityIdList)) {
            String cityIdListSql = cityIdList.stream().map(it -> StrUtil.format(" FIND_IN_SET({},city_ids) ", it))
                    .collect(Collectors.joining("OR"));
            criteria.andCondition(StrUtil.format("({})",cityIdListSql));
        }
        if (CollectionUtils.isNotEmpty(categoryIdList)) {
            String categoryIdListSql = categoryIdList.stream().map(it -> StrUtil.format(" FIND_IN_SET({},category_ids) ", it))
                    .collect(Collectors.joining("OR"));
            criteria.andCondition(StrUtil.format("({})",categoryIdListSql));
        }
        if (StringUtils.isNotBlank(combinationName)) {
            criteria.andLike("combinationName",  StrUtil.format("%{}%",combinationName));
        }
        if (combinationStatus != null) {
            criteria.andEqualTo("combinationStatus", combinationStatus);
        }
        if (createStartTime != null) {
            criteria.andGreaterThanOrEqualTo("createTime", createStartTime);
        }
        if (createEndTime != null) {
            criteria.andLessThanOrEqualTo("createTime", createEndTime);
        }
        criteria.andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        example.orderBy("updateTime").desc();
        return this.selectByExample(example);
    }

    public int softDeleteByStrategyId(Long combinationId) {
        StrategyCombination strategyCombination = new StrategyCombination();
        strategyCombination.setCombinationId(combinationId);
        strategyCombination.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(strategyCombination);
    }

    public StrategyCombination selectByCityAndCategory(String orderFlag, List<String> cityIdList, List<String> categoryIdList, Long combinationId, Integer businessLineId) {
        return strategyCombinationMapper.selectByCityAndCategory(orderFlag,CollectionUtils.isEmpty(cityIdList) ? "" : String.join(",", cityIdList), categoryIdList, combinationId,businessLineId);
    }


}