package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.*;


public interface NewMasterOrderDistributeStrategyService {

    Integer create(CreateRqt rqt);


    Integer update(UpdateRqt rqt);

    Integer enable(EnableRqt rqt);

    DetailResp detail(DetailRqt rqt);

    SimplePageInfo<NewMasterOrderDistributeStrategy> list(ListRqt rqt);


    Integer delete(DeleteRqt rqt);
}
