package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "order_push_master")
public class OrderPushMaster {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    private String id;

    /**
     * 主键ID
     */
    @Column(name = "push_id")
    private Long pushId;

    /**
     * 订单ID
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 师傅ID
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 指派类型(2:发布任务,3:直接雇佣,4:一口价)
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 来源客户端(site:网站,backend:后台,weixin:微信,ikea:宜家)
     */
    @Column(name = "order_from")
    private String orderFrom;


    /**
     * 报价数量
     */
    @Deprecated
    @Column(name = "offer_number")
    private Integer offerNumber;


    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 报价时间
     */
    @Column(name = "offer_time")
    private Date offerTime;


    /**
     * 师傅第一次拉取待报价订单时间
     */
    @Column(name = "first_pull_time")
    private Date firstPullTime;

    /**
     * 师傅第一次查看待报价订单详情时间
     */
    @Column(name = "first_view_time")
    private Date firstViewTime;

    /**
     * 截止报价时间
     */
    @Column(name = "stop_offer_time")
    private Date stopOfferTime;


    /**
     * 是否按照师傅技能推送 0:否,1:是
     */
    @Column(name = "according_technology_push_flag")
    private Integer accordingTechnologyPushFlag;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;


    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;




}