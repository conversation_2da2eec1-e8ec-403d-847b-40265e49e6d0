package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.OrderPushMaster;
import com.wanshifu.master.order.push.mapper.OrderPushMasterMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 推单明细Repsitory
 * <AUTHOR>
 */
@Repository
public class OrderPushMasterRepository extends BaseRepository<OrderPushMaster> {

    @Resource
    private OrderPushMasterMapper orderPushMasterMapper;


    /**
     * 根据orderId查询推单明细
     * @param tableName
     * @param orderId
     * @return
     */
    public List<OrderPushMaster> getOrderPushMasterList(@Param("tableName")String tableName,@Param("orderId") Long orderId){
        return orderPushMasterMapper.getOrderPushMasterList(tableName,orderId);
    }


    /**
     * 查询最小id
     * @param tableName
     * @return
     */
    public  Long getMinId(@Param("tableName")String tableName){
        return orderPushMasterMapper.getMinId(tableName);
    }


    /**
     * 查询最大id
     * @param tableName
     * @return
     */
    public Long getMaxId(@Param("tableName")String tableName,@Param("pushTimeEnd")Date pushTimeEnd){
        return orderPushMasterMapper.getMaxId(tableName,pushTimeEnd);
    }


    /**
     * 通过主键ID范围删除记录
     *
     * @param minId
     * @param maxId
     */
    public Integer rangeDeleteById(String tableName,Long minId, Long maxId) {
        return orderPushMasterMapper.rangeDeleteById(tableName,minId,maxId);

    }


}