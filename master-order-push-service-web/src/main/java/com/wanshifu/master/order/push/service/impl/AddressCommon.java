package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.api.PositionConvertServiceApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.base.address.domain.po.PositionConvert;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class AddressCommon {


    @Resource
    private AddressApi addressApi;

    @Resource
    private PositionConvertServiceApi positionConvertServiceApi;


    /**
     * 根据divisionId获取城市id
     *
     * @param divisionId
     * @return
     */
    public Long getCityDivisionIdByDivisionId(Long divisionId) {
        Long cityId = 0L;
        Address addressResp = this.getDivisionInfoByDivisionId(divisionId);
        if (addressResp != null) {
            if (LongUtil.notEmpty(addressResp.getLv3DivisionId()) && LongUtil.notEmpty(addressResp.getLv4DivisionId())) {
                cityId = addressResp.getLv3DivisionId();
            } else if (LongUtil.isEmpty(addressResp.getLv3DivisionId())&& LongUtil.notEmpty(addressResp.getLv4DivisionId())) {
                cityId = addressResp.getLv4DivisionId();
            } else {
                cityId = addressResp.getLv3DivisionId();
            }
        }
        return cityId;
    }


    /**
     * 获取divisionId下的子级地区信息
     *
     * @param divisionId
     * @return
     */
    public List<Address> getSubListByDivisionId(Long divisionId) {
        try {
            return addressApi.getSubListByDivisionId(divisionId);
        } catch (Exception e) {
            log.error("=====addressApi:getSubListByDivisionId=====divisionId={},e={}", divisionId, e);
            throw new BusException("获取子级地区信息失败");
        }
    }


    /**
     * 根据divisionId获取地区信息
     *
     * @param divisionId
     * @return
     */
    public Address getDivisionInfoByDivisionId(Long divisionId) {
        try {
            return addressApi.getDivisionInfoByDivisionId(divisionId);
        } catch (Exception e) {
//            logger.error("=====addressApi:getDivisionInfoByDivisionId=====divisionId={},e={}", divisionId, e);
            throw new BusException("获取地区信息失败");
        }
    }



    /**
     * 根据divisionId获取地区信息
     *
     * @param divisionId
     * @return
     */
    public PositionConvert getPositionConvertByDivisionId(Long divisionId) {
        try {
            Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
            StringBuilder streetAddress = new StringBuilder();
            streetAddress.append(address.getLv2DivisionName()).append(address.getLv3DivisionName()).append(address.getLv4DivisionName()).append(address.getLv5DivisionName());

            return positionConvertServiceApi.getLocationInfo(streetAddress.toString(),null);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoByDivisionId=====divisionId={},e={}", divisionId, e);
            throw new BusException("获取街道地址经纬度失败");
        }
    }

    /**
     * 根据divisionId获取地区信息
     * @param divisionId
     * @return
     */
    public Address getAddressByDivisionId(Long divisionId) {
        try {
            return addressApi.getDivisionInfoByDivisionId(divisionId);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoByDivisionId=====divisionId={},e={}", divisionId, e);
            throw new BusException("获取地区信息失败");
        }
    }

    /**
     * 根据divisionIds获取地区信息
     * @param divisionIds
     * @return
     */
    public List<Address> getAddressByDivisionIds(String divisionIds) {
        try {
            return addressApi.getDivisionInfoListByDivisionIds(divisionIds);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoListByDivisionIds=====divisionIds={},e={}", divisionIds, e);
            throw new BusException("获取地区信息失败");
        }
    }

    /**
     * 根据divisionId获取地区信息
     *
     * @param divisionId
     * @return
     */
    public PositionConvert getPositionConvertByDivisionId(Long divisionId,String detailAddress) {
        try {
            Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
            StringBuilder streetAddress = new StringBuilder();
            streetAddress.append(address.getLv2DivisionName()).append(address.getLv3DivisionName()).append(address.getLv4DivisionName()).append(detailAddress);

            return positionConvertServiceApi.getLocationInfo(streetAddress.toString(),null);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoByDivisionId=====divisionId={},e={}", divisionId, e);
            throw new BusException("获取街道地址经纬度失败");
        }
    }


    /**
     * 根据divisionId获取地区信息
     *
     * @param address
     * @return
     */
    public PositionConvert getPositionConvertByAddress(String address) {
        try {
            return positionConvertServiceApi.getLocationInfo(address,null);
        } catch (Exception e) {
            log.error("=====addressApi:getDivisionInfoByDivisionId=====divisionId={},e={}", address, e);
            throw new BusException("获取街道地址经纬度失败");
        }
    }

}
