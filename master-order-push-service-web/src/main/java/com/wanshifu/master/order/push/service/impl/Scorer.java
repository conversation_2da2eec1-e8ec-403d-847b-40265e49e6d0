package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.domain.constant.ScorerItem;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.util.QlExpressStatic;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评分器
 * <AUTHOR>
 */
public class Scorer {
    String scorerId;
    String admittance;
    List<ScorerItem> scorerItemList;

    public String getScorerId() {
        return scorerId;
    }


    public BigDecimal mark(DefaultContext<String, Object> orderFeatures,
                           DefaultContext<String, Object> masterContext,
                           RankDetail rankDetail, String masterId){
        BigDecimal result=BigDecimal.ZERO;

        if (!SymbolConstant.EMPTY_STRING.equals(admittance)) {
            final boolean admittanceResult = QlExpressStatic.QlExpressBoolean(admittance, orderFeatures);
            if (!admittanceResult) {
                return result;
            }
        }

        for (ScorerItem scorerItem : scorerItemList) {
            final BigDecimal score = scorerItem.mark(orderFeatures, masterContext);
            result=result.add(score);
            rankDetail.addScorerDetail(scorerItem.getRuleName(),masterId,score);
        }

        return result;
    }



    public static final class ScorerBuilder {
        String scorerId;
        String admittance;
        List<ScorerItem> scorerItemList;

        private ScorerBuilder() {
        }

        public static ScorerBuilder aScorer() {
            return new ScorerBuilder();
        }

        public ScorerBuilder withAdmittance(String admittance) {
            this.admittance = admittance;
            return this;
        }

        public ScorerBuilder withScorerId(String scorerId) {
            this.scorerId = scorerId;
            return this;
        }

        public ScorerBuilder withScorerItemList(List<ScorerItem> scorerItemList) {
            this.scorerItemList = scorerItemList;
            return this;
        }

        public Scorer build() {
            Scorer scorer = new Scorer();
            scorer.scorerId = this.scorerId;
            scorer.scorerItemList = this.scorerItemList;
            scorer.admittance = this.admittance;
            return scorer;
        }
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
