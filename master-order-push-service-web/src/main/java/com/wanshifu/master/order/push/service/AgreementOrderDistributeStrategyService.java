package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 17:11
 */
public interface AgreementOrderDistributeStrategyService {


    int create(CreateRqt rqt);

    Integer update(UpdateRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);

    AgreementOrderDistributeStrategy detail(DetailRqt rqt);

    SimplePageInfo<AgreementOrderDistributeStrategy> list(ListRqt rqt);


}