package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeStrategyExpressionDto;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.domain.enums.OrderDistributeRule;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.*;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;
import com.wanshifu.master.order.push.mapper.OrderDistributeStrategyMapper;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.NewMasterOrderDistributeStrategyService;
import com.wanshifu.master.order.push.service.OrderDistributeStrategyService;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.QlExpressUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class NewMasterOrderDistributeStrategyServiceImpl implements NewMasterOrderDistributeStrategyService {

    @Resource
    private NewMasterOrderDistributeStrategyRepository newMasterOrderDistributeStrategyRepository;

    @Resource
    private OrderScoringStrategyRepository orderScoringStrategyRepository;

    @Resource
    private OrderSelectStrategyRepository orderSelectStrategyRepository;

    @Resource
    private OrderStrategyRelateRepository orderStrategyRelateRepository;




    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "智能接单调度",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public  Integer create(CreateRqt rqt){
        List<CreateRqt.DistributeStrategy> distributeStrategyList = rqt.getDistributeStrategyList();
        String strategyName = rqt.getStrategyName();
        Long businessLineId = rqt.getBusinessLineId();
        String strategyDesc = rqt.getStrategyDesc();
        String categoryIds = rqt.getCategoryIds();
        String cityIds = rqt.getCityIds();
        String openCityMode = rqt.getOpenCityMode();
        String distributeType = rqt.getDistributeType();



        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), null);
        this.checkDistributeStrategy(businessLineId, distributeType, distributeStrategyList);
        this.checkCityCategoryUniq(distributeType,cityIds,categoryIds,businessLineId,openCityMode,null);

        //召回规则表达式
        List<OrderDistributeStrategyExpressionDto> expressionDtoList = this.getOrderDistributeStrategyExpression(distributeStrategyList);

        Integer strategyId = newMasterOrderDistributeStrategyRepository.insert(businessLineId,strategyName, rqt.getOrderFrom(), strategyDesc, distributeType,categoryIds,
                rqt.getOpenCityMode(),rqt.getCityIds(), JSON.toJSONString(distributeStrategyList), JSON.toJSONString(expressionDtoList),
                rqt.getCreateAccountId(),rqt.getUpdateAccountId());

        distributeStrategyList.forEach(distributeStrategy -> {
            //TODO  ORDER_DISTRIBUTE
            orderStrategyRelateRepository.insert(strategyId, "order_distribute", distributeStrategy.getOrderSelectStrategyId(), distributeStrategy.getOrderScoringStrategyId());
        });

        return 1;


    }


    private void checkCityCategoryUniq(String distributeType,String cityIds, String categoryIds,Long businessLineId, String openCityMode,Long strategyId) {
        List<String> cityIdList = Lists.newArrayList();
        List<String> categoryIdList = Lists.newArrayList();
        Assert.isTrue(("all".equals(openCityMode) && StringUtils.isBlank(cityIds)) || ("city".equals(openCityMode) && StringUtils.isNotBlank(cityIds)),"开放城市参数错误" );
        if (StringUtils.equals("city", openCityMode)) {
            cityIdList.addAll(Arrays.asList(cityIds.split(",")));
        }
        categoryIdList.addAll(Arrays.asList(categoryIds.split(",")));

        NewMasterOrderDistributeStrategy orderDistributeStrategy = newMasterOrderDistributeStrategyRepository.selectByCityAndCategoryId(distributeType,cityIdList, categoryIdList, strategyId,businessLineId);
        if (orderDistributeStrategy != null) {
            cn.hutool.core.lang.Assert.isNull(true, StrUtil.format("城市及类目与配置【{}】存在重复!", orderDistributeStrategy.getStrategyName()));
        }
    }

    /**
     * 校验策略名称
     *
     * @param strategyName
     * @param businessLineId
     * @param strategyId
     */
    private void checkStrategyName(String strategyName, Long businessLineId, Integer strategyId) {
        NewMasterOrderDistributeStrategy orderDistributeStrategy = newMasterOrderDistributeStrategyRepository.selectByStrategyNameAndBusinessLineId(strategyName, businessLineId, strategyId);
        Assert.isNull(orderDistributeStrategy, "该业务线已存在相同策略名称!");
    }


    private void checkDistributeStrategy(Long businessLineId, String distributeType, List<CreateRqt.DistributeStrategy> distributeStrategyList) {
        List<CreateOrderDistributeStrategyRqt.DistributeStrategy> distributeStrategyListCopy = BeanCopyUtil.copyListProperties(distributeStrategyList, CreateOrderDistributeStrategyRqt.DistributeStrategy.class, null);
        //不能存在开启条件一样的两条规则,仅顺序不同，也认为是相同的条件
        List<CreateOrderDistributeStrategyRqt.OpenCondition> distinctOpenConditions = distributeStrategyListCopy.stream().map(CreateOrderDistributeStrategyRqt.DistributeStrategy::getOpenCondition).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> {
            List<CreateOrderDistributeStrategyRqt.OpenConditionItem> itemList = BeanCopyUtil.copyListProperties(o.getItemList(), CreateOrderDistributeStrategyRqt.OpenConditionItem.class, null);
            itemList.sort(Comparator.comparingInt(c -> c.getItemName().hashCode()));
            String itemListStr = itemList.stream().map(it -> {
                List<List<Long>> serveIdList = Optional.ofNullable(it.getServeIdList()).orElse(Collections.emptyList());
                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).sorted(Long::compare).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3).flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).sorted(Long::compare).collect(Collectors.toList());
                return it.getItemName() + "_" + it.getTerm() + "_" + it.getItemValue() + "_" + serveLevel1Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel2Ids.stream().map(Object::toString).collect(Collectors.joining(",")) + "_" + serveLevel3Ids.stream().map(Object::toString).collect(Collectors.joining(","));
            }).collect(Collectors.joining(";"));
            return o.getCondition() + ";" + itemListStr;
        }))), ArrayList::new));
        cn.hutool.core.lang.Assert.isTrue(distinctOpenConditions.size() == distributeStrategyList.stream().map(CreateRqt.DistributeStrategy::getOpenCondition).count(), "不能存在开启条件一样的两条规则");
        boolean checkServe = distributeStrategyList.stream().allMatch(it -> it.getOpenCondition().getItemList().stream().allMatch(item -> !"serve".equals(item.getItemName()) || !(CollectionUtils.isEmpty(item.getServeIdList()))));
        cn.hutool.core.lang.Assert.isTrue(checkServe, "开启条件选择服务时,至少选择一个服务!");

        //企业合作经营师傅类型时，校验调度规则
        List<String> cooperationBusinessDistributeTypeHaveScoringList = Arrays.asList(OrderDistributeRule.SCORING_ORDER.getCode(), OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode());

        if (businessLineId == 1
                && PushMode.COOPERATION_BUSINESS_MASTER.getCode().equals(distributeType)
                && CollectionUtils.isNotEmpty(distributeStrategyList)) {
            distributeStrategyList.forEach(distributeStrategy -> {
                if (Objects.isNull(distributeStrategy.getCooperationBusinessServeDivisionAtLast())) {
                    throw new IllegalArgumentException("合作经营师傅类型,是否允许服务区域兜底字段未填！");
                }
                if (Objects.nonNull(distributeStrategy.getOrderScoringStrategyId()) && distributeStrategy.getOrderScoringStrategyId() > 0) {

                    if (!cooperationBusinessDistributeTypeHaveScoringList.contains(distributeStrategy.getDistributeRule())) {
                        throw new IllegalArgumentException("合作经营师傅类型且选择了评分模型时，调度规则选择有误！");
                    }
                } else {
                    if (!OrderDistributeRule.RANDOM.getCode().equals(distributeStrategy.getDistributeRule())) {
                        throw new IllegalArgumentException("合作经营师傅类型且未选评分模型时，调度规则选择有误！");
                    }
                }
            });
        }
    }


    private List<OrderDistributeStrategyExpressionDto> getOrderDistributeStrategyExpression(List<CreateRqt.DistributeStrategy> distributeStrategyList) {


        //<指标编码,指标条件表达式>   例如  <master_work_status,{"preCondition":{"ruleExpression":"is_high_quality == 1","ruleExpressionParams":"is_high_quality"},"calculateExpression":"allMatch(itemName,'term',value) "}>


        return distributeStrategyList.stream().map(strategy -> {

            CreateRqt.OpenCondition openCondition = strategy.getOpenCondition();

            //开启条件表达式
            String openConditionRuleExpression = QlExpressUtil.transitionQlExpress(openCondition.getCondition(),
                    BeanCopyUtil.copyListProperties(openCondition.getItemList().stream()
                            .filter(it -> !StringUtils.equals(it.getItemName(), "serve"))
                            .collect(Collectors.toList()), QlExpressDto.class, (s, t) -> {
                        //时效标签 和 用户人群 操作符符号转换
                        List<String> specialTerms = Lists.newArrayList("time_liness_tag", "appoint_user");
                        if(specialTerms.contains(s.getItemName())){
                            t.setTerm(StringUtils.equals("in", s.getTerm()) ? "containsAny" : "notContainsAny");
                        }
                    }));
            //服务 的开启条件特殊处理
            List<CreateRqt.OpenConditionItem> serveItems = openCondition.getItemList().stream().filter(it -> StringUtils.equals(it.getItemName(), "serve")).collect(Collectors.toList());

            List<String> serveExpression = serveItems.stream().map(it -> {
                List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
                String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
                String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

                List<List<Long>> serveIdList = it.getServeIdList();

                List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

                List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

                List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                        .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","),Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","),Long.class));
                }
                if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                    qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","),Long.class));
                }
                return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
            }).collect(Collectors.toList());
            String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);
            if (StringUtils.isNotBlank(openConditionRuleExpression) && StringUtils.isNotBlank(serveExpressions)) {
                openConditionRuleExpression = StrUtil.format("{} {} {}", openConditionRuleExpression, openCondition.getCondition(), serveExpressions);
            } else {
                openConditionRuleExpression = StringUtils.isNotBlank(openConditionRuleExpression) ? openConditionRuleExpression : serveExpressions;
            }

            //开启条件表达式参数
            List<String> openConditionRuleParamsList = openCondition.getItemList().stream().map(CreateRqt.OpenConditionItem::getItemName)
                    .filter(itemName -> !StringUtils.equals(itemName, "serve")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serveItems)) {
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)))
                    openConditionRuleParamsList.add("lv1_serve_id");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2)))
                    openConditionRuleParamsList.add("lv2_serve_ids");
                if (serveItems.stream().anyMatch(it -> CollectionUtils.isNotEmpty(it.getServeIdList()) && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3)))
                    openConditionRuleParamsList.add("lv3_serve_ids");
            }
            //开启条件表达式参数
            String openConditionRuleParams = openConditionRuleParamsList.stream().distinct().collect(Collectors.joining(","));

            OrderDistributeStrategyExpressionDto expressionDto = new OrderDistributeStrategyExpressionDto();
            expressionDto.setOpenConditionRuleExpression(openConditionRuleExpression);
            expressionDto.setOpenConditionRuleParams(openConditionRuleParams);
            expressionDto.setOrderSelectStrategyId(strategy.getOrderSelectStrategyId());
            expressionDto.setOrderScoringStrategyId(strategy.getOrderScoringStrategyId());
            expressionDto.setDistributeRule(strategy.getDistributeRule());
            expressionDto.setCooperationBusinessServeDivisionAtLast(strategy.getCooperationBusinessServeDivisionAtLast());
            return expressionDto;
        }).collect(Collectors.toList());
    }





    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "智能接单调度",
            tableName = "order_distribute_strategy", mapperClass = OrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public  Integer update(UpdateRqt rqt){
        Integer strategyId = rqt.getStrategyId();
        String strategyName = rqt.getStrategyName();
        Long businessLineId = rqt.getBusinessLineId();
        String strategyDesc = rqt.getStrategyDesc();
        String categoryIds = rqt.getCategoryIds();
        String distributeType = rqt.getDistributeType();
        List<CreateRqt.DistributeStrategy> distributeStrategyList = rqt.getDistributeStrategyList();
        this.checkStrategyName(rqt.getStrategyName(), rqt.getBusinessLineId(), strategyId);
        this.checkDistributeStrategy(businessLineId, distributeType, distributeStrategyList);
        //召回规则表达式
        List<OrderDistributeStrategyExpressionDto> expressionDtoList = this.getOrderDistributeStrategyExpression(distributeStrategyList);
        newMasterOrderDistributeStrategyRepository.update(strategyId,businessLineId,strategyName,rqt.getOrderFrom(),strategyDesc, distributeType,categoryIds,rqt.getOpenCityMode(),
                rqt.getCityIds(), JSON.toJSONString(distributeStrategyList), JSON.toJSONString(expressionDtoList),
                rqt.getUpdateAccountId());

        orderStrategyRelateRepository.deleteByRelateId(strategyId);
        distributeStrategyList.forEach(distributeStrategy -> {
            orderStrategyRelateRepository.insert(strategyId, "order_distribute", distributeStrategy.getOrderSelectStrategyId(), distributeStrategy.getOrderScoringStrategyId());
        });
        return 1;
    }


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "调度管理", level2MenuName = "智能接单调度",
            tableName = "order_distribute_strategy", mapperClass = OrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt){
        Condition condition = new Condition(OrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);
        NewMasterOrderDistributeStrategy orderDistributeStrategy = new NewMasterOrderDistributeStrategy();
        orderDistributeStrategy.setStrategyStatus(rqt.getStrategyStatus());
        orderDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = newMasterOrderDistributeStrategyRepository.updateByConditionSelective(orderDistributeStrategy, condition);

        if(rqt.getStrategyStatus() == 1){
            Assert.isTrue(result > 0,"启用策略失败");
        }else{
            Assert.isTrue(result > 0,"禁用策略失败");
        }
        return 1;
    }


    @Override
    @Transactional
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "智能接单调度",
            tableName = "order_distribute_strategy", mapperClass = OrderDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "orderDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt){
        Condition condition = new Condition(OrderDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", 0).andEqualTo("isDelete", 0);

        NewMasterOrderDistributeStrategy orderDistributeStrategy = new NewMasterOrderDistributeStrategy();
        orderDistributeStrategy.setIsDelete(1);
        orderDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        orderStrategyRelateRepository.deleteByRelateId(rqt.getStrategyId());
        int result = newMasterOrderDistributeStrategyRepository.updateByConditionSelective(orderDistributeStrategy, condition);
        Assert.isTrue(result > 0,"删除失败");
        return 1;
    }


    @Override
    public DetailResp detail(DetailRqt rqt){
        NewMasterOrderDistributeStrategy orderDistributeStrategy = newMasterOrderDistributeStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
        if(orderDistributeStrategy == null){
            return null;
        }

        DetailResp detailResp = new DetailResp();
        detailResp.setStrategyId(orderDistributeStrategy.getStrategyId());
        detailResp.setStrategyName(orderDistributeStrategy.getStrategyName());
        detailResp.setBusinessLineId(orderDistributeStrategy.getBusinessLineId());
        detailResp.setOpenCityMode(orderDistributeStrategy.getOpenCityMode());
        detailResp.setStrategyDesc(orderDistributeStrategy.getStrategyDesc());
        detailResp.setCategoryIds(orderDistributeStrategy.getCategoryIds());
        detailResp.setCityIds(orderDistributeStrategy.getCityIds());
        detailResp.setDistributeType(orderDistributeStrategy.getDistributeType());
        detailResp.setOrderFrom(orderDistributeStrategy.getOrderFrom());
        List<DetailResp.DistributeStrategyVo> distributeStrategyVoList = JSON.parseArray(orderDistributeStrategy.getDistributeStrategy(),DetailResp.DistributeStrategyVo.class);


        List<Integer> orderScoringStrategyIdList = distributeStrategyVoList .stream().map(DetailResp.DistributeStrategyVo::getOrderScoringStrategyId).distinct().collect(Collectors.toList());

        List<Integer> orderSelectStrategyIdList = distributeStrategyVoList .stream().map(DetailResp.DistributeStrategyVo::getOrderSelectStrategyId).distinct().collect(Collectors.toList());




        List<OrderScoringStrategy> orderScoringStrategyList = orderScoringStrategyRepository.selectByStrategyIdList(orderScoringStrategyIdList);

        Map<Integer,OrderScoringStrategy> orderScoringStrategyMap = orderScoringStrategyList.stream().collect(Collectors.toMap(OrderScoringStrategy::getStrategyId,each->each,(value1, value2) -> value1));


        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(orderSelectStrategyIdList);

        Map<Integer,OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream().collect(Collectors.toMap(OrderSelectStrategy::getStrategyId,each->each,(value1, value2) -> value1));




        distributeStrategyVoList.forEach(distributeStrategyVo -> {
            distributeStrategyVo.setOrderSelectStrategyName(orderSelectStrategyMap.get(distributeStrategyVo.getOrderSelectStrategyId()).getStrategyName());
            if(distributeStrategyVo.getOrderScoringStrategyId() != null){
                distributeStrategyVo.setOrderScoringStrategyName(orderScoringStrategyMap.get(distributeStrategyVo.getOrderScoringStrategyId()).getStrategyName());
            }
        });


        detailResp.setDistributeStrategyList(distributeStrategyVoList);
        return detailResp;
    }




    @Override
    public SimplePageInfo<NewMasterOrderDistributeStrategy> list(ListRqt rqt){
        if(StringUtils.isNotBlank(rqt.getCategoryIds())){
            rqt.setCategoryIdList(Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toList()));
        }
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());
        List<NewMasterOrderDistributeStrategy> orderDistributeStrategyList = newMasterOrderDistributeStrategyRepository.selectList(rqt);
        SimplePageInfo<NewMasterOrderDistributeStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(orderDistributeStrategyList);
        return listRespSimplePageInfo;
    }





}
