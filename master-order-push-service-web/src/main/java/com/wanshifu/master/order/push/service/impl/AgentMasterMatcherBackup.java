//package com.wanshifu.master.order.push.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.alicloud.openservices.tablestore.model.search.SearchQuery;
//import com.alicloud.openservices.tablestore.model.search.SearchRequest;
//import com.alicloud.openservices.tablestore.model.search.SearchResponse;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.alicloud.openservices.tablestore.model.search.query.QueryOperator;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
//import com.wanshifu.master.order.push.domain.po.RePushSource;
//import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
//import com.wanshifu.master.order.push.repository.PushProgressRepository;
//import com.wanshifu.master.order.push.service.OrderMatchRouteService;
//import com.wanshifu.master.order.push.service.PushControllerFacade;
//import com.wanshifu.master.order.push.service.PushQueueService;
//import com.wanshifu.master.order.push.service.TableStoreClient;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 代理商师傅匹配器
// * <AUTHOR>
// * @date 2023-09-13 14:04:00
// */
//@Slf4j
//@Component("agent")
//public class AgentMasterMatcher extends AbstractOrderMasterMatcher {
//
//    @Resource
//    private TableStoreClient tableStoreClient;
//
//    @Resource
//    private PushProgressRepository pushProgressRepository;
//
//    @Resource
//    private PushControllerFacade pushControllerFacade;
//
//    @Resource
//    private PushQueueService pushQueueService;
//
//    private static final String agentTableName = "t_agent_info";
//    private static final String agentMasterTableName = "t_agent_master";
//    private static final String agentIndexName = "t_agent_info_index";
//    private static final String agentMasterIndexName = "t_agent_master_index";
//
//    @Resource
//    private OrderMatchRouteService orderMatchRouteService;
//
//    /**
//     * 匹配代理商师傅
//     * @param orderDetailData
//     * @return
//     */
//    @Override
//    protected boolean checkPreCondition(OrderDetailData orderDetailData){
//        return (!"normal".equals(orderDetailData.getPushExtraData().getPushMode())) && orderDetailData.getBusinessLineId() == 2;
//    }
//
//    @Override
//    protected MatchMasterResult match(OrderDetailData orderDetailData,MasterMatchCondition condition) {
//        try{
//            SearchQuery searchQuery = new SearchQuery();
//            BoolQuery boolQuery = new BoolQuery();
//            List<Query> mustQuerys=new ArrayList<Query>();
//            setAgentQuery(mustQuerys,condition);
//            boolQuery.setMustQueries(mustQuerys);
//            searchQuery.setQuery(boolQuery);
//            SearchRequest searchRequest = new SearchRequest(agentTableName, agentIndexName, searchQuery);
//            SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//            // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//            List<Row> rows = resp.getRows();
//            while(resp.getNextToken() != null){
//                //把token设置到下一次请求中
//                searchRequest.getSearchQuery().setToken(resp.getNextToken());
//                resp = tableStoreClient.getSyncClient().search(searchRequest);
//                if (!resp.isAllSuccess()){
//                    throw new RuntimeException("not all success");
//                }
//                rows.addAll(resp.getRows());
//            }
//            ArrayList<String> agentIds=new ArrayList<>();
//            for (Row row : rows) {
//                if (row!=null) {
//                    agentIds.add(row.getPrimaryKey().getPrimaryKeyColumn("agent_id").getValue().asString());
//                }
//            }
//            if (agentIds.size()!=0) {
//                return getAgentMasterIds(agentIds);
//            }
//        }catch(Exception e){
//            log.error(String.format("订单匹配代理商师傅失败,orderDetailData:%s",JSON.toJSONString(orderDetailData)));
//        }
//        return new MatchMasterResult(new HashSet<>());
//    }
//
//
//    private void setAgentQuery(List<Query> mustQuerys,MasterMatchCondition masterCondition) {
//        mustQuerys.add(tableStoreClient.stringTermQuery("division_id", String.valueOf(masterCondition.getThirdDivisionId())));
//        mustQuerys.add(tableStoreClient.stringMatchQueryWithMinim("serve_ids",
//                masterCondition.getServeIds(), QueryOperator.OR,1));
//        mustQuerys.add(tableStoreClient.longTermQuery("use_status", 0));
//    }
//
//
//    public static final String AGENT_MASTER_LIST = "agentMasterList";
//    private static final String AGENT_ID = "agentId";
//    private static final String MASTER_LIST = "masterList";
//    private MatchMasterResult getAgentMasterIds(ArrayList<String> agentSet){
//        HashSet<String> masterIds=new HashSet<>();
//        JSONArray agentMatchArray=new JSONArray();
//        for (String agentString : agentSet) {
//            SearchQuery searchQuery = new SearchQuery();
//            BoolQuery boolQuery = new BoolQuery();
//            List<Query> mustQuerys=new ArrayList<Query>();
//            mustQuerys.add(tableStoreClient.stringTermQuery("agent_id", agentString));
//            mustQuerys.add(tableStoreClient.longTermQuery("cooperation_status", 0));
//            boolQuery.setMustQueries(mustQuerys);
//            searchQuery.setQuery(boolQuery);
//            SearchRequest searchRequest = new SearchRequest(agentMasterTableName, agentMasterIndexName, searchQuery);
//            SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//            // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//            List<Row> rows = resp.getRows();
//            while(resp.getNextToken() != null){
//                //把token设置到下一次请求中
//                searchRequest.getSearchQuery().setToken(resp.getNextToken());
//                resp = tableStoreClient.getSyncClient().search(searchRequest);
//                if (!resp.isAllSuccess()){
//                    throw new RuntimeException("not all success");
//                }
//                rows.addAll(resp.getRows());
//            }
//            HashSet<String> currentAgentSet = new HashSet<>();
//            for (Row row : rows) {
//                if (row!=null) {
//                    final String masterId = row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString();
//                    masterIds.add(masterId);
//                    currentAgentSet.add(masterId);
//                }
//            }
//            //添加代理商匹配信息
//            final JSONObject agentMatchValue = new JSONObject();
//            agentMatchValue.put(AGENT_ID,agentString);
//            agentMatchValue.put(MASTER_LIST, StringUtils.join(currentAgentSet,","));
//            agentMatchArray.add(agentMatchValue);
//        }
//
//
//        MatchMasterResult matchMasterResult = new MatchMasterResult(masterIds);
//        matchMasterResult.putExtraData(AGENT_MASTER_LIST,agentMatchArray);
//        return matchMasterResult;
//    }
//
//
//    @Override
//    protected void afterMatch(MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
//        if(CollectionUtils.isNotEmpty(matchMasterResult.getMasterIdSet()) && CollectionUtils.isNotEmpty(masterCondition.getOrderPushEliminateMasterIds())){
//            matchMasterResult.getMasterIdSet().removeAll(masterCondition.getOrderPushEliminateMasterIds());
//        }
//    }
//
//    @Override
//    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
//        try{
//            if(matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
//                return false;
//            }
//            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();
//            Long timestamp = System.currentTimeMillis();
//            String orderVersion = String.valueOf(timestamp);
//            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp),"agent_push");
//            JSONObject commonFeature = new JSONObject();
//            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
//            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
//            commonFeature.put(FieldConstant.PUSH_MODE, "agent");
//            commonFeature.put(AgentMasterMatcher.AGENT_MASTER_LIST, JSON.toJSONString(matchMasterResult.getExtraData().get(AgentMasterMatcher.AGENT_MASTER_LIST)));
//            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
//            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
//            pushControllerFacade.directPush(orderDetailData,orderVersion,matchMasterResult.getMasterIdSet(),commonFeature);
//
//            //[代理商][取消指派]
//            boolean agentCancelMaster=FieldConstant.AGENT.equals(orderDetailData.getTagName())
//                    &&RePushSource.orderCancelMaster.toString().equals(orderDetailData.getRePushSource());
//
//            //[代理商][取消指派],同时推送普通师傅
//            if (agentCancelMaster) {
//                log.info("{},[代理商][取消指派]推送更多普通师傅转发消息,getBusinessLineId:{},{},{}",orderDetailData.getMasterOrderId(),
//                        orderDetailData.getBusinessLineId());
//                pushQueueService.sendNormalPushMessage(
//                        orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),
//                        matchMasterResult.getMasterIdSet().stream().map(Long::valueOf).collect(Collectors.toList()),
//                        orderDetailData.getPushExtraData().getHandoffTag()
//                );
//            }
//
////            orderMatchRouteService.orderMatchRoute("agent",orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
//
//            return true;
//        }catch(Exception e){
//            log.error(String.format("执行代理商推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
//        }
//        return false;
//    }
//
//
//
//}
