package com.wanshifu.master.order.push.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.common.PushStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import com.wanshifu.master.order.push.repository.PushHandleRepository;
import com.wanshifu.master.order.push.repository.PushOrderListRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.OrderPushMonitorService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Deprecated
public class OrderPushMonitorServiceImpl implements OrderPushMonitorService {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushHandleRepository pushHandleRepository;

    private  static Map<Long,String> businessLineMap = new HashMap<>();


    static {
        businessLineMap.put(1L,"成品业务线");
        businessLineMap.put(2L,"家庭业务线");
        businessLineMap.put(3L,"创新业务线");
    }


    @Resource
    private PushOrderListRepository pushOrderListRepository;

    @Override
    public SimplePageInfo<PushOrderList> noPushedMasterOrderList(NoPushedMasterOrderListRqt rqt){

        parseParams(rqt);

        Page page = PageHelper.startPage(rqt.getPageNum(),rqt.getPageSize());

        List<PushOrderList> pushOrderListList = pushOrderListRepository.selectNoPushedMasterOrderList(rqt);


        SimplePageInfo<PushOrderList> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setList(pushOrderListList);
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPages(page.getPages());
        return listRespSimplePageInfo;

    }

    private void parseParams(NoPushedMasterOrderListRqt rqt){

        if(StringUtils.isNotBlank(rqt.getOrderFrom())){
            rqt.setOrderFromList(
                    Arrays.stream(Optional.ofNullable(rqt.getOrderFrom())
                            .orElse("0").split(",")).map(String::valueOf)
                            .collect(Collectors.toList()));
        }


        if(StringUtils.isNotBlank(rqt.getAppointType())){
            rqt.setAppointTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getAppointType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getServeType())){
            rqt.setServeTypeList(
                    Arrays.stream(Optional.ofNullable(rqt.getServeType())
                            .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

        if(StringUtils.isNotBlank(rqt.getCategoryIds())){
            rqt.setCategoryList(
                    Arrays.stream(Optional.ofNullable(rqt.getCategoryIds())
                                    .orElse("0").split(",")).map(Integer::parseInt)
                            .collect(Collectors.toList()));
        }

    }



    private String getPushStrategy(PushOrderList pushOrderList,Map<Long,PushStrategySnapshot> strategySnapshotMap,Map<Long, BaseSelectStrategySnapshot> baseSelectStrategySnapshotMap,
                                   Map<Long, FilterStrategySnapshot> filterStrategySnapshotMap,Map<Long, SortingStrategySnapshot> sortingStrategySnapshotMap){

        if(StringUtils.isBlank(pushOrderList.getPushStrategy())){
            return "";
        }

        String pushStrategy;
        String baseSelectStrategyName = baseSelectStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getBaseSelectStrategySnapshotId()).getStrategyName();
        pushStrategy = baseSelectStrategyName;
        if(strategySnapshotMap.get(pushOrderList.getId()).getFilterStrategySnapshotId() != null){
            String filterStrategyName = filterStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getFilterStrategySnapshotId()).getStrategyName();
            pushStrategy = pushStrategy + "-" + filterStrategyName;
        }

        if(strategySnapshotMap.get(pushOrderList.getId()).getSortingStrategySnapshotId() != null){
            String sortingStrategyName = sortingStrategySnapshotMap.get(strategySnapshotMap.get(pushOrderList.getId()).getSortingStrategySnapshotId()).getStrategyName();
            pushStrategy = pushStrategy + "-" + sortingStrategyName;

        }
        return pushStrategy;
    }


    private String getGoodsName(String goodsIds,Map<Long,String> goodsMap){
        if(StringUtils.isBlank(goodsIds)){
            return "";
        }
        Set<Long> goodsIdSet = Arrays.stream(Optional.ofNullable(goodsIds)
                .orElse("0").split(",")).map(Long::parseLong)
                .collect(Collectors.toSet());
        List<String> goodsNameList = new ArrayList<>();
        goodsIdSet.forEach(goodsId -> {
            goodsNameList.add(goodsMap.get(goodsId));
        });
        return StringUtils.join(goodsNameList,",");

    }


    @Override
    public List<GetFilterDataResp> filterData(GetFilterDataRqt rqt){
        PushProgress pushProgress = pushProgressRepository.selectByPrimaryKey(rqt.getPushId());
        if(pushProgress != null){
            PushHandle pushHandle = pushHandleRepository.selectByOrderIdAndOrderVersion(pushProgress.getOrderId(),pushProgress.getOrderVersion());
            if(pushHandle != null && StringUtils.isNotBlank(pushHandle.getFilterMessage())){
                List<GetFilterDataResp> getFilterDataRespList = new ArrayList<>();
                try{
                    Map<String, JSONArray> map = JSON.parseObject(pushHandle.getFilterMessage(),Map.class);
                    for(Map.Entry<String,JSONArray> entry : map.entrySet()){
                        GetFilterDataResp getFilterDataResp = new GetFilterDataResp();
                        getFilterDataResp.setStageName("召回");
                        getFilterDataResp.setRuleName(entry.getKey());
                        getFilterDataResp.setFilterMasterNum(entry.getValue().size());
                        getFilterDataRespList.add(getFilterDataResp);
                    }
                }catch(Exception e){
                    e.printStackTrace();
                }
                return getFilterDataRespList;
            }
        }
        return null;
    }


}
