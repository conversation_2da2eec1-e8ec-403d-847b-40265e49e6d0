package com.wanshifu.master.order.push.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.BaseSelectMaster;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMaster;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.MasterType;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.po.PushProgress;
import com.wanshifu.master.order.push.domain.rqt.DelayPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSONObject;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 推送控制器
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PushController {

	@Resource
	private PushProgressRepository pushProgressRepository;

	@Resource
	private PushQueueService pushQueueService;

	@Resource
	private MasterPushRepository masterPushRepository;

	@Resource
	private BaseSelector baseSelector;

	@Value("${master.push.insert.batchSize}")
	private int masterPushInsertBatchSize;


	@Resource
	private OrderMasterPushRepository orderMasterPushRepository;

	@Resource
	private MasterBaseEsRepository masterBaseEsRepository;


	@Value("${auto.punishment.push.restrict.switch:on}")
	private String autoPunishmentPushRestrictSwitch;




	public PushController() {
	}

    /**
     * 首轮推送
     *
     * @param orderVersion
     * @param toBePushedMasterList
     * @return
     */
    public Integer push(String timeMark, String orderVersion, PushParameter pushParameter,
                        PushMasterList toBePushedMasterList, JSONObject commonFeature, PushCommonObject pushCommonObject,
                        int firstTimeValidPush, int normalFirstTimeValidPush, Integer normalFirstMatchMasterNum) {
        // 新建待推送列表
        List<PushMaster> finalList = toBePushedMasterList.getFirstPushMasterList(pushCommonObject.getPushConfig());

        final String masterOrderId = String.valueOf(pushParameter.getMasterOrderId());
        String toBePush = masterJoin(masterOrderId, orderVersion, finalList);

        pushQueueService.pushByQueue(pushParameter.getMasterOrderId(), toBePush, finalList.size(),
                "1", commonFeature.getString(FieldConstant.DIVISION_MATCH_LEVEL),
                commonFeature.getString(FieldConstant.BUSINESS_LINE_ID), commonFeature,
                finalList, firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,
                pushCommonObject.getOrderDetailData().getPushExtraData().getMasterSourceType(),pushCommonObject.getOrderDetailData());

        PushProgress pushProgress = toBePushedMasterList.getPushProgress();
        pushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + finalList.size());
        pushProgress.setPushedRound(pushProgress.getPushedRound() + 1);


        if (MasterType.ALL.code.equals(pushCommonObject.getPushConfig().getDelayPushMasterType())) {
            masterPushRepository.insertMasterPushList(orderVersion, pushParameter.getGlobalOrderId(), toBePushedMasterList.getMasterList(), masterPushInsertBatchSize);
        } else {
            masterPushRepository.insertMasterPushList(orderVersion, pushParameter.getGlobalOrderId(), toBePushedMasterList.getNewMasterList(), toBePushedMasterList.getOldMasterList(), masterPushInsertBatchSize);
        }

        // 更新offset version 过滤掉的师傅数
        pushProgressRepository.updatePushProgress(pushParameter.getGlobalOrderId(), orderVersion, toBePushedMasterList.getPushProgress());


        return finalList.size();
    }


    @Resource
	private PushRecordFacade pushRecordFacade;

    public Integer dynamicRoundsPush(String timeMark, String orderVersion, PushParameter pushParameter,
                                     List<DynamicRoundsMaster> masterList, JSONObject commonFeature,
                                     PushCommonObject pushCommonObject,
                                     int firstTimeValidPush, int normalFirstTimeValidPush, Integer normalFirstMatchMasterNum) {

        // 新建待推送列表

        List<PushMaster> finalList = new ArrayList<>();
        masterList.forEach(dynamicRoundsMaster -> {
            if (dynamicRoundsMaster.getIsFirstDynamicRoundsPush() == 1) {
                finalList.addAll(dynamicRoundsMaster.getMasterList());
            }
        });

        final String masterOrderId = String.valueOf(pushParameter.getMasterOrderId());
        String toBePush = masterJoin(masterOrderId, orderVersion, finalList);

        pushQueueService.pushByQueue(pushParameter.getMasterOrderId(), toBePush, finalList.size(),
                "1", commonFeature.getString(FieldConstant.DIVISION_MATCH_LEVEL),
                commonFeature.getString(FieldConstant.BUSINESS_LINE_ID), commonFeature, finalList,
                firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,
                pushCommonObject.getOrderDetailData().getPushExtraData().getMasterSourceType(),pushCommonObject.getOrderDetailData());

        int masterSize = orderMasterPushRepository.insertOrderMasterPushList(pushParameter.getGlobalOrderId(), orderVersion, masterList, masterPushInsertBatchSize);

        //TODO
        PushProgress pushProgress = new PushProgress();
        pushProgress.setPushedMasterNum(finalList.size());
        pushProgress.setPushedRound(1);
        pushProgress.setListLength(masterSize);


        // 更新offset version 过滤掉的师傅数
        pushProgressRepository.updatePushProgress(pushParameter.getGlobalOrderId(), orderVersion, pushProgress);



        if("on".equals(autoPunishmentPushRestrictSwitch)){
			String miToDay = DateFormatterUtil.miToDay(timeMark);
			String businessLine = "";
			if(pushCommonObject.getOrderDetailData().getBusinessLineId() == 1){
				businessLine = MasterSourceType.TOB.code;
			}else if(pushCommonObject.getOrderDetailData().getBusinessLineId() == 2){
				businessLine = MasterSourceType.TOC.code;
			}

			if(StringUtils.isNotBlank(businessLine)){
				pushRecordFacade.increasePushOrderCount(finalList,miToDay);
				pushRecordFacade.increasePushOrderCountByBusinessLine(finalList,miToDay,businessLine);
			}


		}


        return finalList.size();
    }


	/**
	 * 非首轮推送
	 * 
	 * @param orderVersion
	 * @param toBePushedMasterList
	 * @return
	 */
	public boolean delayPush(String orderVersion, PushParameter pushParameter,
							 PushMasterList toBePushedMasterList, JSONObject commonFeature, DelayPushRqt delayPushRqt) {

		PushConfig pushConfig = delayPushRqt.getPushConfig();
		// 新建待推送列表
		List<PushMaster> finalList = new ArrayList<>();

		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getNewMasterList())){
			// 添加本轮推送新师傅
			finalList.addAll(toBePushedMasterList.getNewMasterList());
		}


		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getOldMasterList())){
			// 添加本轮推送老师傅
			finalList.addAll(toBePushedMasterList.getOldMasterList());
		}


		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getMasterList())){
			finalList.addAll(toBePushedMasterList.getMasterList());
		}

		
		int tmpFilterNum=finalList.size();
		
		//推送前后兼容
		Long businessLineId=commonFeature.getLong(FieldConstant.BUSINESS_LINE_ID);
		if (businessLineId==null) {
			businessLineId=1L;
		}


		List<BaseSelectMaster> baseSelectMasterList = baseSelector.delayPushBaseSelect(delayPushRqt.getOrderDetailData(),delayPushRqt.getBaseSelect(),commonFeature.getString(FieldConstant.PUSH_MODE),commonFeature.getIntValue(FieldConstant.DIVISION_MATCH_LEVEL));

		Set<String> masterSet = baseSelectMasterList.stream().map(BaseSelectMaster::getMasterId).collect(Collectors.toSet());

		finalList.retainAll(new ArrayList<>(masterSet));


		tmpFilterNum=tmpFilterNum-finalList.size();
		
		
		// 推送-控制（夜间或停服）-----------------
		final String masterOrderId = String.valueOf(pushParameter.getMasterOrderId());
		String toBePush = masterJoin(masterOrderId,orderVersion,finalList);

		commonFeature.put(FieldConstant.ORDER_LNG_LAT,delayPushRqt.getOrderDetailData().getOrderLngLat());

		handleCrossCityPush(finalList,delayPushRqt.getOrderDetailData());

        pushQueueService.pushByQueue(pushParameter.getMasterOrderId(), toBePush,
                toBePushedMasterList.getPushProgress().getPushedMasterNum() + finalList.size(),
                "0", commonFeature.getString(FieldConstant.DIVISION_MATCH_LEVEL),
                businessLineId.toString(), commonFeature, finalList, null, null, null,
                delayPushRqt.getOrderDetailData().getPushExtraData().getMasterSourceType(),delayPushRqt.getOrderDetailData());

		// 推送-控制（夜间或停服）-----------------
		// 更新推送进度
		PushProgress pushProgress = toBePushedMasterList.getPushProgress();
		pushProgress.setFilteredMasterNum(pushProgress.getFilteredMasterNum() + tmpFilterNum);
		pushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + finalList.size());
		pushProgress.setPushedRound(pushProgress.getPushedRound() + 1);
		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getNewMasterList())){
			pushProgress.setNewMasterOffset(pushProgress.getNewMasterOffset() + toBePushedMasterList.getNewMasterList().size());
		}

		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getOldMasterList())){
			pushProgress.setOldMasterOffset(pushProgress.getOldMasterOffset() + toBePushedMasterList.getOldMasterList().size());
		}

		if(CollectionUtils.isNotEmpty(toBePushedMasterList.getMasterList())){
			pushProgress.setMasterOffset(pushProgress.getMasterOffset() + toBePushedMasterList.getMasterList().size());
		}


		// 入库新进度
		pushProgressRepository.updatePushProgress(pushParameter.getGlobalOrderId(),orderVersion,pushProgress);
		return true;
	}


	private void handleCrossCityPush(List<PushMaster> pushMasterList, OrderDetailData orderDetailData){

		try{
			Set<String> masterSet = pushMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
			List<List<String>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterSet,100);

			List<MasterBaseSearch> masterBaseList = new ArrayList<>();

			for(List<String> batch : masterBatchList){
				BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
				boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", batch));
				EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder);
				if( CollectionUtils.isNotEmpty(esResponse.getDataList())){
					masterBaseList.addAll(esResponse.getDataList());
				}
			}

			Map<String, MasterBaseSearch> masterBaseMap = masterBaseList.stream()
					.collect(Collectors.toMap(MasterBaseSearch::getMasterId, masterBase -> masterBase));

			pushMasterList.forEach(pushMaster -> {
				if(masterBaseMap.containsKey(pushMaster.getMasterId())){
					pushMaster.setIsCrossCityPush(masterBaseMap.get(pushMaster.getMasterId()).getCityDivisionId().equals(orderDetailData.getSecondDivisionId()) ? 0 : 1);
					pushMaster.setMasterTimeType(masterBaseMap.get(pushMaster.getMasterId()).getMasterTimeType());
				}else{
					pushMaster.setIsCrossCityPush(0);
				}
			});
		}catch(Exception e){
			log.error("handleCrossCityPush error",e);
		}

	}

    /**
     * 开关关闭直接推送
     *
     * @param listToBeHandle
     * @return
     */
    public boolean directPush(String orderVersion, Long masterOrderId,
                              List<PushMaster> listToBeHandle, JSONObject commonFeature, boolean longTailPushFlag, String firstPush,
                              String masterSourceType,OrderDetailData orderDetailData) {
        // 未开启直接推送

        String listToBePush = masterJoin(String.valueOf(masterOrderId), orderVersion, listToBeHandle);
        // 推送

        pushQueueService.pushByQueue(masterOrderId, listToBePush, listToBeHandle.size(),
                firstPush, commonFeature.getString(FieldConstant.DIVISION_MATCH_LEVEL),
                commonFeature.getString(FieldConstant.BUSINESS_LINE_ID), commonFeature,
                listToBeHandle, null, null, null, masterSourceType,orderDetailData);
        return true;
    }


	/**
	 * 开关关闭直接推送
	 *
	 * @param listToBeHandle
	 * @return
	 */
	public boolean pushOrderToMaster(String orderVersion, List<OrderDetailData> orderDetailDataList,
							  PushMaster pushMaster, JSONObject commonFeature, Integer firstTimeValidPush,
							  String masterSourceType) {

		pushQueueService.pushByQueue(orderDetailDataList, commonFeature.getString(FieldConstant.DIVISION_MATCH_LEVEL),
				commonFeature.getString(FieldConstant.BUSINESS_LINE_ID), commonFeature,
				pushMaster, firstTimeValidPush, masterSourceType);
		return true;
	}


		/**
         * 推送前,师傅列表转换为字符串
         * @param finalList
         * @return
         * orderId
         * masterIds
         * pushNumber
         * pushTime
         * firstPush
         * pushAddress
         * businessLineId
         */
	public String masterJoin(String masterOrderId,String pushTime,List<PushMaster> finalList) {
		final String join = StringUtils.join(finalList, SymbolConstant.COMMA);
		return join;
	}


	public static void main(String[] args){
		System.out.println(BigDecimal.valueOf(1) .divide(BigDecimal.valueOf( 100)));
	}

}
