package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.fee.center.api.BizRuleMappingApi;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.domain.document.BizRuleMapping;
import com.wanshifu.fee.center.domain.dto.AccountInfo;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateBatchReq;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.request.BizRuleMappingBatchQueryReq;
import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.activity.domain.api.request.MasterActivityRewardRqt;
import com.wanshifu.iop.activity.domain.api.response.MasterActivityRewardResp;
import com.wanshifu.iop.activity.service.api.OutBusinessServiceApi;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoListByIdsRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoListByIdsResp;
import com.wanshifu.master.manage.config.domain.api.request.autoQuoted.BatchGetMasterAutoQuotedByMasterIdsRqt;
import com.wanshifu.master.manage.config.domain.api.request.autoQuoted.BatchGetMasterAutoQuotedGoodsNotContainJSONRqt;
import com.wanshifu.master.manage.config.domain.po.MasterAutoQuotedBase;
import com.wanshifu.master.manage.config.domain.po.MasterAutoQuotedGoodsPriceSet;
import com.wanshifu.master.manage.config.service.api.autoQuoted.MasterAutoQuotedApi;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.message.DynamicAutoReceiveMessage;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.CreateRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.MasterAutoReceiveOrderService;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import com.wanshifu.master.order.push.service.PushQueueService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.master.recruit.api.RecruitMasterApi;
import com.wanshifu.master.recruit.domain.api.request.recruitMaster.GetMasterCurrentLeaveRequestRqt;
import com.wanshifu.master.recruit.domain.api.response.recruitMaster.GetMasterCurrentLeaveRequestResp;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.api.grabdefinite.GrabDefiniteResourceApi;
import com.wanshifu.order.offer.domains.api.request.graborder.BranchGetMaterLockedGrabTodayCountReq;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.api.response.graborder.MaterLockedGrabTodayCountResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.enums.OrderStatus;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.order.push.api.OrderPushListApi;
import com.wanshifu.order.push.response.BatchOrderPushResp;
import com.wanshifu.util.LoCollectionsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 协议订单调度
 */
@Service
@Slf4j
public class NormalOrderDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private AddressCommon addressCommon;


    @Resource
    private OrderConfigCommon orderConfigCommon;


    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;


    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private MasterAutoReceiveOrderService masterAutoReceiveOrderService;

    @Resource
    private OrderPushListApi orderPushListApi;

    @Resource
    private Tools tools;

    @Resource
    private InfoQueryApi infoQueryApi;


    @Resource
    private MasterAutoQuotedApi masterAutoQuotedApi;

    @Resource
    private GrabDefiniteResourceApi grabDefiniteResourceApi;


    @Resource
    private PushQueueService pushQueueService;



    @Resource
    private RocketMqSendService rocketMqSendService;

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private NormalOrderDistributeStrategyRepository normalOrderDistributeStrategyRepository;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private HBaseClient hBaseClient;

    @Resource
    private AddressApi addressApi;

    @Resource
    private FeeRuleApi feeRuleApi;

    @Resource
    private OutBusinessServiceApi outBusinessServiceApi;

    @Value("${wanshifu.rocketMQ.order-distribute-topic}")
    private String orderDistributeTopic;

    @Value("${autoReceive.move.switch:on}")
    private String autoReceiveMoveSwitch;


    @Value("${dynamic.auto.receive.delayTime:30}")
    private Integer dynamicAutoReceiveDelayTime;

    /**
     * 一口价订单使用红包自动抢单功能迁移开关
     */
    @Value("${definitePriceByRedPack.autoReceive.move.switch:on}")
    private String definitePriceByRedPackAutoReceiveMoveSwitch;


    @Value("${agreement.master.auto.grab.serveIds:0}")
    private String agreementMasterAutoGrabServeIds;

    @Value("${agreement.master.auto.grab.delayTime:30}")
    private Integer agreementMasterAutoGrabDelayTime;


    @Value("${agreementAutoOfferPrice.cityList}")
    private String agreementAutoOfferPriceCityList;


    @Value("${agreementAutoGrab.cityList}")
    private String agreementAutoGrabCityList;


    @Value("${agreementAutoOfferPrice.serveCompleteNumber}")
    private int agreementAutoOfferPriceServeCompleteNumber;


    @Value("${agreementAutoGrab.serveCompleteNumber}")
    private int agreementAutoGrabServeCompleteNumber;


    @Value("${agreementAutoGrab.lv2Serve.serveCompleteNumber}")
    private int agreementAutoGrabLv2ServeServeCompleteNumber;


    @Value("${agreementReceiveNotice.delayTime}")
    private int agreementReceiveNoticeDelayTime;


    @Value("${agreement.offerPriceNotice.categoryList}")
    private String agreementOfferPriceNoticeCategoryList;

    @Value("${none.offerPrice.push.goldMedalMaster.minutes:5}")
    private Integer pushGoldMedalMasterMinutes;



    @Resource
    private OrderDataBuilder orderDataBuilder;


    @Resource
    private NormalOrderDistributeRepository normalOrderDistributeRepository;


    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;

    @Resource
    private PushProgressRepository pushProgressRepository;


    private boolean isEnterpriseOrder(OrderBase orderBase) {
        return AccountType.ENTERPRISE.code.equals(orderBase.getAccountType());
    }


    public DefaultContext<String, Object> initOrderFeatures(OrderBase orderBase,OrderExtraData orderExtraData,OrderGrab orderGrab){

        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put(FieldConstant.ACCOUNT_ID,orderBase.getAccountId());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_ID,orderBase.getBusinessLineId());
        orderFeatures.put(FieldConstant.CATEGORY_ID,orderBase.getCategoryId());
        orderFeatures.put(FieldConstant.APPOINT_TYPE,orderGrab.getAppointType());

        JSONObject orderPosition = new JSONObject()
                .fluentPut("buyerAddressLatitude", orderExtraData.getBuyerAddressLatitude())
                .fluentPut("buyerAddressLongitude", orderExtraData.getBuyerAddressLongitude());

        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

        orderFeatures.put(FieldConstant.ORDER_LNG_LAT, orderExtraData.getBuyerAddressLongitude()+","+orderExtraData.getBuyerAddressLatitude());
        orderFeatures.put(FieldConstant.SECOND_DIVISION_ID, addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        orderFeatures.put(FieldConstant.THIRD_DIVISION_ID, orderBase.getThirdDivisionId());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());
        orderFeatures.put(FieldConstant.LV1_SERVE_ID,orderBase.getServeLevel1Ids());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderFeatures.put(FieldConstant.LV2_SERVE_IDS, Joiner.on(",").join(lv2ServeIdList));
                }

            }

        }
        orderFeatures.put(FieldConstant.LV3_SERVE_IDS,orderBase.getServeIds());

        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
        if (this.isEnterpriseOrder(orderBase)) {

            GetOrderBaseByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
            getOrderByGlobalIdRqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
            GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchNoLog(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderByGlobalIdRqt));
            if(Objects.isNull(getOrderBaseByGlobalIdRsp) || getOrderBaseByGlobalIdRsp.getOrderBase() == null){
                orderFeatures.put(FieldConstant.USER_ID, 0L);
            }else{
                orderFeatures.put(FieldConstant.USER_ID,AccountType.USER.code.equals(getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountType()) ? getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountId() : 0L);
            }
        }else {
            orderFeatures.put(FieldConstant.USER_ID,orderBase.getAccountId());
        }

        orderFeatures.put(FieldConstant.ORDER_FROM,getOrderFrom(orderBase));
        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderExtraData));

        return orderFeatures;
    }


    private String getOrderFrom(OrderBase orderBase){
        String orderFrom = orderBase.getOrderFrom();
        if(AccountType.USER.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom) || OrderFrom.THIRDPART.valueEn.equals(orderFrom)){
                return "site";
            }else  if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family";
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom)){
                return "enterprise_inside";
            }else if("applet".equals(orderFrom)){
                return "family_enterprise";
            }else if("enterprise_system".equals(orderFrom)){
                return "family_outside";
            }
        }

        return "";


    }


    private List<String> getTimeLinessTag(OrderExtraData orderExtraData){

        List<String> timeLinessTagList = new ArrayList<>();


//        if(orderExtraData.getTimerFlag() != null && orderExtraData.getTimerFlag() == 1){
//            timeLinessTagList.add("regular_time_order");
//        }
//
//        if(orderExtraData.getEmergencyOrderFlag() != null && orderExtraData.getEmergencyOrderFlag() == 1){
//            timeLinessTagList.add("emergency_order");
//        }
//
//        if(orderExtraData.getOnTimeOrderFlag() != null && orderExtraData.getOnTimeOrderFlag() == 1){
//            timeLinessTagList.add("on_time_order");
//        }
//
//        if(orderExtraData.getExpectDoorInStartDate() != null){
//            timeLinessTagList.add("expect_time_order");
//        }

        return timeLinessTagList;
    }


    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private BizRuleMappingApi bizRuleMappingApi;

    private final String SCENE_CODE = "master_auto_offer_price";


    private List<BizRuleMapping> getBizRuleMapping(Set<String> serviceIdList){
        BizRuleMappingBatchQueryReq req = new BizRuleMappingBatchQueryReq();
        req.setSceneCode(SCENE_CODE);
        Map<String, List<String>> toBizRule = new HashMap<>();
        toBizRule.put("serviceId",new ArrayList<>(serviceIdList));
        req.setToBizRule(toBizRule);
        req.setPageSize(200);
        List<BizRuleMapping> bizRuleMappingList = new ArrayList<>();
        SimplePageInfo<BizRuleMapping> simplePageInfo = bizRuleMappingApi.batchQuery(req);
        if(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(simplePageInfo.getList())){
            bizRuleMappingList.addAll(simplePageInfo.getList());
        }
        while(simplePageInfo.getPageNum() < (simplePageInfo.getPages() - 1) ){
            req.setPageNum(req.getPageNum() + 1);
            simplePageInfo = bizRuleMappingApi.batchQuery(req);
            if(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(simplePageInfo.getList())){
                bizRuleMappingList.addAll(simplePageInfo.getList());
            }
        }
        return bizRuleMappingList;
    }


    /**
     * 全局开关批量查询
     * @return
     */
    private List<MasterAutoQuotedBase> batchGetMasterAutoQuotedBase(List<Long> masterList,int batchSize){
        final ArrayList<MasterAutoQuotedBase> result = new ArrayList<>();

        List<Long> batchMaster = new ArrayList<>();
        int i=0;
        for (Long masterInset : masterList) {
            batchMaster.add(masterInset);
            i++;
            if (i>=batchSize) {
                final BatchGetMasterAutoQuotedByMasterIdsRqt batchGetMasterAutoQuotedByMasterIdsRqt
                        = new BatchGetMasterAutoQuotedByMasterIdsRqt();
                batchGetMasterAutoQuotedByMasterIdsRqt.setMasterIds(batchMaster);
                batchGetMasterAutoQuotedByMasterIdsRqt.setQueryStatus(1);//字段已废弃
                final List<MasterAutoQuotedBase> masterAutoQuotedBases = masterAutoQuotedApi.batchGetMasterAutoQuotedByMasterIds(batchGetMasterAutoQuotedByMasterIdsRqt);
                log.info("batchGetMasterAutoQuotedByMasterIds:rqt:" + JSON.toJSONString(batchGetMasterAutoQuotedByMasterIdsRqt)+ ",resp:" + JSON.toJSONString(masterAutoQuotedBases));
                result.addAll(masterAutoQuotedBases);
                i=0;
                batchMaster=new ArrayList<>();
            }
        }
        if (batchMaster.size()!=0) {
            final BatchGetMasterAutoQuotedByMasterIdsRqt batchGetMasterAutoQuotedByMasterIdsRqt
                    = new BatchGetMasterAutoQuotedByMasterIdsRqt();
            batchGetMasterAutoQuotedByMasterIdsRqt.setMasterIds(batchMaster);
            batchGetMasterAutoQuotedByMasterIdsRqt.setQueryStatus(1);
            final List<MasterAutoQuotedBase> masterAutoQuotedBases = masterAutoQuotedApi.batchGetMasterAutoQuotedByMasterIds(batchGetMasterAutoQuotedByMasterIdsRqt);
            log.info("batchGetMasterAutoQuotedByMasterIds:rqt:" + JSON.toJSONString(batchGetMasterAutoQuotedByMasterIdsRqt)+ ",resp:" + JSON.toJSONString(masterAutoQuotedBases));

            result.addAll(masterAutoQuotedBases);
        }
        return result;
    }

    /**
     * 批量查询师傅详细配置
     * @param autoListMaster
     * @param lv4DivisionId
     * @param serveIdList
     * @param batchSize
     * @return
     */
    private List<MasterAutoQuotedGoodsPriceSet> batchGetMasterAutoQuotedBase(
            List<Long> autoListMaster,
            Long lv4DivisionId,
            List<Long> serveIdList,
            int batchSize
    ){
        final ArrayList<MasterAutoQuotedGoodsPriceSet> result = new ArrayList<>();

        List<Long> batchMaster = new ArrayList<>();
        int i=0;
        for (Long masterInset : autoListMaster) {
            batchMaster.add(masterInset);
            i++;
            if (i>=batchSize) {
                final BatchGetMasterAutoQuotedGoodsNotContainJSONRqt masterAutoQuotedRqt = new BatchGetMasterAutoQuotedGoodsNotContainJSONRqt();
                masterAutoQuotedRqt.setMasterIds(batchMaster);
                masterAutoQuotedRqt.setFourthDivisionId(lv4DivisionId);
                masterAutoQuotedRqt.setServeIds(serveIdList);
                masterAutoQuotedRqt.setQueryStatus(1);

                final List<MasterAutoQuotedGoodsPriceSet> masterAutoQuotedGoodsPriceSets = masterAutoQuotedApi.batchGetMasterAutoQuotedGoodsNotContainJSON(masterAutoQuotedRqt);
                log.info("batchGetMasterAutoQuotedGoodsNotContainJSON:rqt:" + JSON.toJSONString(masterAutoQuotedRqt)+ ",resp:" + JSON.toJSONString(masterAutoQuotedGoodsPriceSets));

                result.addAll(masterAutoQuotedGoodsPriceSets);
                i=0;
                batchMaster=new ArrayList<>();
            }
        }
        if (batchMaster.size()!=0) {
            final BatchGetMasterAutoQuotedGoodsNotContainJSONRqt masterAutoQuotedRqt = new BatchGetMasterAutoQuotedGoodsNotContainJSONRqt();
            masterAutoQuotedRqt.setMasterIds(batchMaster);
            masterAutoQuotedRqt.setFourthDivisionId(lv4DivisionId);
            masterAutoQuotedRqt.setServeIds(serveIdList);
            masterAutoQuotedRqt.setQueryStatus(1);
            final List<MasterAutoQuotedGoodsPriceSet> masterAutoQuotedGoodsPriceSets = masterAutoQuotedApi.batchGetMasterAutoQuotedGoodsNotContainJSON(masterAutoQuotedRqt);
            log.info("batchGetMasterAutoQuotedGoodsNotContainJSON:rqt:" + JSON.toJSONString(masterAutoQuotedRqt)+ ",resp:" + JSON.toJSONString(masterAutoQuotedGoodsPriceSets));

            result.addAll(masterAutoQuotedGoodsPriceSets);
        }
        return result;
    }


    private boolean filterMasterAutoQuotedBases(MasterAutoQuotedBase masterAutoQuotedBase,Integer appointType){
        if(appointType.equals(AppointType.OPEN.value)){
            return 1==masterAutoQuotedBase.getGlobalAutoOfferOrderStatus();
        }else if(appointType.equals(AppointType.DEFINITE_PRICE.value)){
            return 1==masterAutoQuotedBase.getGlobalAutoLootOrderStatus();
        }
        return false;
    }

    private boolean filterMasterAutoQuotedGoods(MasterAutoQuotedGoodsPriceSet masterAutoRow,Integer appointType){
        if(appointType.equals(AppointType.OPEN.value)){
            return 1==masterAutoRow.getAutoOfferOrderStatus();
        }else if(appointType.equals(AppointType.DEFINITE_PRICE.value)){
            return 1==masterAutoRow.getAutoLootOrderStatus();
        }
        return false;
    }

    /**
     * 获取开启自动报价的师傅
     * @param masterSet
     * @param lv4DivisionId
     * @param serveIds
     * @return
     */
    public Set<String> masterAutoPriceCheck(
            Long orderId,
            Long cityDivisionId,
            Set<String> masterSet,
            Long lv4DivisionId,String serveIds,
            String serviceIds,
            Integer appointType,OrderExtraData orderExtraData){
        Set<String> result = new HashSet<>();

        Set<String> serviceIdSet = Arrays.stream(serviceIds.split(",")).collect(Collectors.toSet());
        Set<String> serveIdSet = Arrays.stream(serveIds.split(",")).collect(Collectors.toSet());


        List<BizRuleMapping> bizRuleMappingList = getBizRuleMapping(serviceIdSet);
        Map<String,String> serveIdMapping = new HashMap<>();
        bizRuleMappingList.forEach(bizRuleMapping -> {
            String fromServeId = bizRuleMapping.getFromBizRule().get("serviceCategoryId");
            String toServeId = bizRuleMapping.getToBizRule().get("serviceCategoryId");
            serveIdMapping.put(toServeId,fromServeId);
        });

        Set<String> finalServeIdSet = serveIdSet.stream().map(e -> serveIdMapping.getOrDefault(e,e)).collect(Collectors.toSet());

        final List<Long> serveIdList = finalServeIdSet.stream().map(row -> Long.parseLong(row)).collect(Collectors.toList());


        //开启了自动接单的师傅
        final List<MasterAutoQuotedBase> masterAutoQuotedBases
                = batchGetMasterAutoQuotedBase(
                masterSet.stream().map(row->Long.valueOf(row)).collect(Collectors.toList()), 200
        );
        log.info("开启了自动接单的师傅pre,{},{},{},  masterAutoQuotedBases:{}",orderId,masterSet,appointType,JSON.toJSONString(masterAutoQuotedBases));
        if (masterAutoQuotedBases!=null&&masterAutoQuotedBases.size()!=0) {
            final List<Long> autoListMaster = new ArrayList<>();
            final HashMap<Long, MasterAutoQuotedBase> masterAutoQuotedBaseMap = new HashMap<>();
            masterAutoQuotedBases.stream()
                    .filter(row->filterMasterAutoQuotedBases(row,appointType))
                    .forEach(row -> {
                        autoListMaster.add(row.getMasterId());
                        masterAutoQuotedBaseMap.put(row.getMasterId(),row);
                    });

            log.info("{} 开启了自动接单的师傅 - filter {} masterAutoQuotedBases:{}",orderId,autoListMaster,JSON.toJSONString(masterAutoQuotedBaseMap));
            //配置了对应订单维度自动接单的师傅
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(autoListMaster)) {
                final List<MasterAutoQuotedGoodsPriceSet> masterAutoQuotedConfig =batchGetMasterAutoQuotedBase(
                        autoListMaster,lv4DivisionId,serveIdList,200
                );
                if (masterAutoQuotedConfig!=null&&masterAutoQuotedConfig.size()!=0) {
                    final HashMap<Long, Set<Long>> mapMasterServe = new HashMap<>(50);
                    final Map<String, Long> masterPushCount = getMasterPushCount(cityDivisionId,masterSet, appointType);
                    log.info("{} masterPushCount -  masterPushCount:{}",orderId,JSON.toJSONString(masterPushCount));
                    for (MasterAutoQuotedGoodsPriceSet masterAutoRow : masterAutoQuotedConfig) {
                        //状态正常的记录
//                        if (masterAutoRow.getStatus()==1) {
                        final Long masterId = masterAutoRow.getMasterId();
                        final Long serviceLastId = masterAutoRow.getServiceLastId();
                        if (filterMasterAutoQuotedGoods(masterAutoRow,appointType)) {
                            LoCollectionsUtil.putToSet(mapMasterServe,masterId,serviceLastId);
                        }
//                        }
                    }
                    log.info("{} filterMasterAutoQuotedGoods -  masterAutoQuotedConfig:{}  -  mapMasterServe:{}"
                            ,orderId,JSON.toJSONString(masterAutoQuotedConfig),JSON.toJSONString(mapMasterServe));
                    master:for (Map.Entry<Long, Set<Long>> entry : mapMasterServe.entrySet()) {
                        final Long masterId = entry.getKey();
                        final Set<Long> masterServeSet = entry.getValue();
                        //包含所有服务
                        if (!masterServeSet.containsAll(serveIdList)) {
                            continue master;
                        }
                        //自动接单报价上限
                        final boolean isReachLimit = pushCountLimit(
                                masterAutoQuotedBaseMap.get(masterId),
                                appointType,
                                masterPushCount.get(String.valueOf(masterId))
                        );
                        if (isReachLimit) {
                            continue master;
                        }

                        result.add(String.valueOf(masterId));
                    }
                }
            }

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(result)) {
                result = this.checkOfferOrderDistance(result,orderExtraData,appointType,masterAutoQuotedBaseMap);
            }
            log.info("{} final自动接单的师傅 - filter masterAutoQuotedBases:{}",orderId,autoListMaster);
        }
        return result;
    }


    /**
     * 自动接单报价上限
     * true:已达到上限
     * @return
     */
    private boolean pushCountLimit(MasterAutoQuotedBase masterAutoQuotedBase,Integer appointType,Long masterCount){
        Integer pushCountLimit=0;
        if (masterAutoQuotedBase!=null) {
            if(appointType.equals(AppointType.OPEN.value)){
                pushCountLimit=masterAutoQuotedBase.getEveryDayOfferOrderCount();
            }else if(appointType.equals(AppointType.DEFINITE_PRICE.value)){
                pushCountLimit=masterAutoQuotedBase.getEveryDayLootOrderCount();
            }
        }
        if (masterCount!=null&&masterCount>=pushCountLimit) {
            return true;
        }
        return false;
    }


    private Map<String,Map<String,Object>> getMasterFeatures(Set<String> masterSet){
        Map<String,Map<String,Object>> resultMap = new HashMap<>();
        GetMasterInfoListByIdsRqt rqt = new GetMasterInfoListByIdsRqt();
        List<Long> masterIdList = masterSet.stream().map(Long::parseLong).collect(Collectors.toList());
        rqt.setMasterIds(masterIdList);
        List<GetMasterInfoListByIdsResp> respList = commonQueryServiceApi.getMasterInfoListByIds(rqt);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(respList)){
            respList.forEach(resp -> {
                Map<String,Object> map = new HashMap<>();
                map.put("master_id",String.valueOf(resp.getMasterId()));
                map.put("rest_state",resp.getRestState());
                map.put("lng_lat",resp.getLocationLongitude() + "," + resp.getLocationLatitude());
                map.put("master_current_location",resp.getCurrentLongitude() + "," + resp.getCurrentLatitude());
                map.put("account_status","on".equals(resp.getStatus()) ? 1 : 0);
                map.put("account_freeze_status", (resp.getFreezeTime() != null && resp.getFreezeTime() > 0 && resp.getFreezeTime() < DateFormatterUtil.getNowTimeStampMil()) ? 1 : 0);
                resultMap.put(String.valueOf(resp.getMasterId()),map);
            });
        }
        return resultMap;
    }

    public Set<String> checkOfferOrderDistance(Set<String> masterIdSet, OrderExtraData orderExtraData, Integer appointType,HashMap<Long, MasterAutoQuotedBase> masterAutoQuotedBaseHashMap){

        Map<String,Map<String,Object>> masterFeatures = getMasterFeatures(masterIdSet);

        BigDecimal orderLat = orderExtraData.getBuyerAddressLatitude();
        BigDecimal orderLng = orderExtraData.getBuyerAddressLongitude();

        if(Objects.nonNull(orderLat) && orderLat.compareTo(BigDecimal.ZERO) > 0 &&
                Objects.nonNull(orderLng) && orderLng.compareTo(BigDecimal.ZERO) > 0){



            Set<String> resultSet = new HashSet<>();
            masterIdSet.forEach(masterId -> {
                Map<String,Object> masterFeatureMap = masterFeatures.get(masterId);
                if(masterFeatureMap == null){
                    return ;
                }

                MasterAutoQuotedBase masterAutoQuotedBase = masterAutoQuotedBaseHashMap.get(Long.valueOf(masterId));
                if(Objects.isNull(masterAutoQuotedBase) || Objects.isNull(masterAutoQuotedBase.getOfferOrderDistance()) || Objects.isNull(masterAutoQuotedBase.getLootOrderDistance())){
                    return ;
                }


                Integer orderDistance = 0;
                if(AppointType.OPEN.value .equals( appointType)){
                    orderDistance = masterAutoQuotedBase.getOfferOrderDistance();
                }else if(AppointType.DEFINITE_PRICE.value .equals( appointType)){
                    orderDistance = masterAutoQuotedBase.getLootOrderDistance();
                }


                if(orderDistance <= 0){
                    resultSet.add(masterId);
                    return ;
                }

                String masterLngLat = (String)masterFeatureMap.get("lng_lat");
                double lng = Double.valueOf(masterLngLat.split(",")[0]);
                double lat = Double.valueOf(masterLngLat.split(",")[1]);
                if(lng > 0 && lat > 0 ){
                    if(orderDistance  > distance(lat,lng,orderLat.doubleValue(),orderLng.doubleValue())){
                        resultSet.add(masterId);
                    }
                }
            });
            return resultSet;
        }
        return masterIdSet;
    }


    /**
     * km 地球半径 平均值，千米
     */
    static double EARTH_RADIUS = 6371.0;



    /**
     * latitude:维度 longitude:经度  （第一个点经纬度,第二个点经纬度） 用haversine公式计算球面两点间的距离。
     *
     * @return 距离:千米
     */
    private static Long distance(double lat1, double lon1, double lat2,
                                 double lon2) {
        // 经纬度转换成弧度
        lat1 = convertDegreesToRadians(lat1);
        lon1 = convertDegreesToRadians(lon1);
        lat2 = convertDegreesToRadians(lat2);
        lon2 = convertDegreesToRadians(lon2);

        // 差值
        double vLon = Math.abs(lon1 - lon2);
        double vLat = Math.abs(lat1 - lat2);

        // h is the great circle distance in radians, great
        // circle就是一个球体上的切面，它的圆心即是球心的一个周长最大的圆。
        double h = haverSin(vLat) + Math.cos(lat1) * Math.cos(lat2) * haverSin(vLon);

        Double distance = 2 * EARTH_RADIUS * Math.asin(Math.sqrt(h));

        return distance.longValue();
    }


    /**
     * 将角度换算为弧度。
     *
     * @param degrees
     * @return
     */
    private static double convertDegreesToRadians(double degrees) {
        return degrees * Math.PI / 180;
    }

    // private static double ConvertRadiansToDegrees(double radian) {
    // return radian * 180.0 / Math.PI;
    // }

    private static double haverSin(double theta) {
        double v = Math.sin(theta / 2);
        return v * v;
    }


    @Resource
    private MasterOrderPushCountRepository masterOrderPushCountRepository;


    @Resource
    private OrderLockRepository orderLockRepository;


    public enum CountTag{
        AUTO_OFFER,AUTO_GRAB
    }


    public Map<String, Long> getMasterPushCount(Long cityDivisionId,Set<String> masterSet,Integer appointType){
        final String now = DateFormatterUtil.getNow();
        if(appointType.equals(AppointType.OPEN.value)){
            return getMasterPushCount(now, masterSet, CountTag.AUTO_OFFER);
        }else if(appointType.equals(AppointType.DEFINITE_PRICE.value)){
            OrderLock orderLock = orderLockRepository.selectByCityDivisionId(cityDivisionId);
            if(Objects.nonNull(orderLock) && Objects.nonNull(orderLock.getOrderGrabLockCnt()) && orderLock.getOrderGrabLockCnt() > 0){
                //TODO 调用订单中台接口查询师傅锁单次数
                BranchGetMaterLockedGrabTodayCountReq req = new BranchGetMaterLockedGrabTodayCountReq();
                final List<List<Long>> batch = LocalCollectionsUtil.groupByBatchLong(masterSet, 10);
                final Map<String, Long> masterPushCount=new HashMap<>();
                for (List<Long> batchRequest : batch) {
                    req.setMasterIds(batchRequest);
                    List<MaterLockedGrabTodayCountResp> countRespList = grabDefiniteResourceApi.branchGetMaterLockedGrabTodayCount(req);
                    countRespList.forEach(countResp -> masterPushCount.put(String.valueOf(countResp.getMasterId()),Long.valueOf(countResp.getLockedNumber())));
                }
                return masterPushCount;
            }else{
                return getMasterPushCount(now,masterSet, CountTag.AUTO_GRAB);
            }
        }
        return new HashMap<>();
    }

    public Map<String,Long> getMasterPushCount(String dayTimeString,Set<String> masterIdSet,CountTag tag){
        final Map<String,Long> result = new HashMap<>(50);
        List<MasterOrderPushCount> masterOrderPushCountList = masterOrderPushCountRepository.selectByMasterIdSetAndDt(masterIdSet,dayTimeString);
        for (MasterOrderPushCount masterOrderPushCount : masterOrderPushCountList) {
            //TODO 改成Hbase统计
//            result.put(masterOrderPushCount.getMasterId(),tag == CountTag.AUTO_GRAB ? masterOrderPushCount.getGrabOrderCnt() : masterOrderPushCount.getOfferOrderCnt());
        }
        return result;
    }


    @Resource
    private FeatureRepository featureRepository;



    public boolean orderDistribute(Long orderId){

        if(!"on".equals(autoReceiveMoveSwitch)){
            return false;
        }

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderExtraData orderExtraData = orderGrabByIdResp.getOrderExtraData();
        final OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        boolean checkResult = masterAutoReceiveOrderService.checkAutoReceiveCondition(orderBase,orderGrabByIdResp.getOrderGrab(),orderGrabByIdResp.getOrderServiceAttributeInfos(),orderExtraData);
        if(!checkResult){
            return false;
        }
        //订单特征初始化
        final DefaultContext<String, Object> orderFeatures = initOrderFeatures(orderBase,orderExtraData,orderGrab);


        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                .matchDistributor(orderDataBuilder.build(orderId),orderFeatures, DistributeType.AUTO_RECEIVE.getCode());
        if (!orderDistributor.isMatched()) {
            log.info("no matchDistributor matched!:{}",orderId);
            return false;
        }
        log.info("order:{} orderDistributor:[{}]",orderDistributor);


        List<BatchOrderPushResp> batchOrderPushRespList = orderPushListApi.getOrderPushList(orderId, orderBase.getThirdDivisionId());
        if(CollectionUtils.isEmpty(batchOrderPushRespList)){
            return false;
        }

        Set<Long> masterStrSet = batchOrderPushRespList.stream().map(BatchOrderPushResp::getMasterId).collect(Collectors.toSet());
        Set<String> masterSet = masterStrSet.stream().map(i -> String.valueOf(i)).collect(Collectors.toSet());

        //过滤已退款师傅
        if(StringUtils.isNotBlank(orderExtraData.getOrderPushExcludeMasterIds())){
            List<String> excludeMasterList = Arrays.asList(orderExtraData.getOrderPushExcludeMasterIds().split(","));
            masterSet.removeAll(excludeMasterList);
        }

        log.info("master_order_id:{}-master from order_push masterSet:[{}]",orderId,masterSet);
        //过滤自动接单开关
        masterSet=this.masterAutoPriceCheck(orderId,(Long)orderFeatures.get("second_division_id"),masterSet,orderBase.getFourthDivisionId(),orderBase.getServeIds(),orderBase.getServiceIds(),orderGrab.getAppointType(),orderExtraData);


        log.info("master_order_id:{}-autoPrice masterSet:[{}]",orderId,masterSet);
        if (CollectionUtils.isEmpty(masterSet)) {
            log.info("order:{} not master after autoSelect:[{}]",orderId,masterSet);
            return true;
        }

        DefaultContext<String, DefaultContext<String, Object>> masterFeatures = null;
        //获取特征
        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getOrderFeatureSet());
        masterFeatures = featureRepository.masterFeatures(masterSet, orderDistributor.getMasterFeatureSet(),orderFeatures);
        log.info("master_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]",orderId,orderFeatures,masterFeatures);



        //过滤排序
        final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                .withOrderId(String.valueOf(orderId))
                .withType(FieldConstant.RANK_DETAIL)
                .build();
        final List<ScorerMaster> scorerMasterList = orderDistributor
                .rank(masterSet,orderFeatures,masterFeatures,rankDetail);
        //存储处理详情
        //TODO
//        featureRepository.saveOrderProgress(rankDetail);
        log.info("order distribute rankDetail:" + JSON.toJSONString(rankDetail));
        Collections.sort(scorerMasterList, Comparator.comparing(ScorerMaster::getScore));

        if(CollectionUtils.isNotEmpty(scorerMasterList)){
            //自动接单
            masterAutoReceiveOrderService.autoReceive(orderBase,orderGrabByIdResp.getOrderGrab(),orderGrabByIdResp.getOrderServiceAttributeInfos(),
                    scorerMasterList,orderDistributor.getDistributeRule(),(Long)orderFeatures.get("second_division_id"),batchOrderPushRespList);
        }else{
            log.info("没有可自动接单师傅");
        }



        return true;

    }

    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice){
        Long orderId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getOrderId();
        Integer firstPush = orderPushedResultNotice.getFirstPush();
        String masterSourceType = orderPushedResultNotice.getMasterSourceType();

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        this.agreementMasterAutoGrab(orderBase, firstPush);
        this.orderDistribute(orderId);


        if(firstPush != 1){
            return 0;
        }

        DynamicAutoReceiveMessage message = new DynamicAutoReceiveMessage();
        message.setOrderId(orderId);
        pushQueueService.sendDynamicAutoReceiveMessage(message,dynamicAutoReceiveDelayTime * 60 * 1000L);


        matchNormalOrderDistributeStrategy(orderId, firstPush, masterSourceType);

        sendPushGoldMedalMasterMessage(orderBase,orderGrab,orderPushedResultNotice.getFirstTimeValidPush());


        return 1;

    }


    private void sendPushGoldMedalMasterMessage(OrderBase orderBase,OrderGrab orderGrab,Integer firstTimeValidPush){

        if(Objects.isNull(firstTimeValidPush) || firstTimeValidPush != 1){
            return ;
        }

        if(!(AppointType.OPEN.value.equals(orderGrab.getAppointType()) && OrderFrom.SITE.valueEn.equals(orderBase.getOrderFrom()) && AccountType.USER.code.equals(orderBase.getAccountType()) &&
                "5".equals(orderBase.getServeLevel1Ids()))){
            return ;
        }

        PushGoldMedalMasterMessage message = new PushGoldMedalMasterMessage();
        message.setOrderId(orderBase.getOrderId());
        pushQueueService.sendPushGoldMedalMasterMessage(message,pushGoldMedalMasterMinutes * 60 * 1000L);

    }





    @Override
    public void afterPropertiesSet(){
        OrderDistributeContext.register(PushMode.NORMAL.code, this);

    }

    /**
     * 一口价订单协议师傅动态抢单（根据智能运营红包奖励）
     * @param message
     */
    public void userAgreementMasterAutoGrab(AgreementMasterAutoGrabMessage message){

        Long orderId = message.getOrderId();


        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGrabByIdResp.getOrderServiceAttributeInfos();

        if(CollectionUtils.isEmpty(orderServiceAttributeInfoList)){
            return ;
        }

        if(!checkUserAgreementMasterAutoGrab(orderBase,orderGrab)){
            return ;
        }


        BigDecimal definiteServeFee = null;

        OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(orderBase.getGlobalOrderTraceId());
        if (orderBaseComposite != null && orderBaseComposite.getOrderInitFee() != null) {
            definiteServeFee = Optional.ofNullable(orderBaseComposite.getOrderInitFee()).map(OrderInitFee::getDefiniteServeFee).orElse(BigDecimal.ZERO);
        }

        List<AgreementMaster> agreementMasterList = matchUserAgreementMaster(orderBase);
        agreementMasterList = filterByDailyOrderCnt(agreementMasterList);
        agreementMasterList = filterByNewContractRestStatus(agreementMasterList);
        Set<Long> agreementMasterIdSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).collect(Collectors.toSet());
        List<BatchOrderPushResp> batchOrderPushRespList = orderPushListApi.getOrderPushList(orderId, orderBase.getThirdDivisionId());
        if(CollectionUtils.isEmpty(batchOrderPushRespList)){
            return ;
        }


        Set<Long> pushedMasterList = batchOrderPushRespList.stream().map(BatchOrderPushResp::getMasterId).collect(Collectors.toSet());
        agreementMasterIdSet.retainAll(pushedMasterList);


        if(CollectionUtils.isEmpty(agreementMasterIdSet)){
            return ;
        }

        List<AgreementMasterBase> agreementMasterBaseList = getAgreementMasterCooperationPrice(agreementMasterList,orderBase,orderGrab,orderServiceAttributeInfoList);

        if(CollectionUtils.isEmpty(agreementMasterBaseList)){
            return ;
        }


        Map<String, AgreementMasterBase> agreementMasterBaseMap = agreementMasterBaseList.stream()
                .collect(Collectors.toMap(AgreementMasterBase::getMasterId, agreementMasterBase -> agreementMasterBase));

        Set<String> agreementMasterSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());


        MasterActivityRewardRqt rqt = new MasterActivityRewardRqt();
        rqt.setAppointType(orderGrab.getAppointType());
        rqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        rqt.setOrderId(orderBase.getOrderId());
        rqt.setServeIds(orderBase.getServeIds());
        rqt.setCreateOrderTime(orderBase.getOrderCreateTime());
        rqt.setFourthDivisionId(orderBase.getFourthDivisionId());
        rqt.setServeLevel1Ids(orderBase.getServeLevel1Ids());
        rqt.setOrderNo(orderBase.getOrderNo());
        rqt.setCreateOrderUserId(orderBase.getAccountId());
        rqt.setThirdDivisionId(orderBase.getThirdDivisionId());
        rqt.setPushOrderTime(batchOrderPushRespList.get(0).getPushTime());
        rqt.setOfferNum(Long.valueOf(orderGrab.getOfferNumber()));
        rqt.setIsOrderContract(0);
        rqt.setOrderFrom(orderBase.getOrderFrom());
        rqt.setFromAccountType(orderBase.getAccountType());
        rqt.setGroupGoodNum(orderServiceAttributeInfoList.size());
        List<MasterActivityRewardRqt.MasterInfo> masterInfoList = new ArrayList<>();

        batchOrderPushRespList = batchOrderPushRespList.stream().filter(batchOrderPushResp -> agreementMasterSet.contains(String.valueOf(batchOrderPushResp.getMasterId()))).collect(Collectors.toList());

        batchOrderPushRespList.forEach(batchOrderPushResp -> {
            MasterActivityRewardRqt.MasterInfo masterInfo = new MasterActivityRewardRqt.MasterInfo();
            masterInfo.setMasterId(batchOrderPushResp.getMasterId());
            masterInfo.setDistanceValue(batchOrderPushResp.getPushDistance().intValue());
            masterInfo.setSkillRelatedState(1);
            masterInfoList.add(masterInfo);
        });

        rqt.setMasterInfoList(masterInfoList);
        rqt.setIsOrderPackage(0);


        //查询智能运营红包奖励，如果奖励金额+订单一口价金额由于师傅合作价则自动抢单
        List<MasterActivityRewardResp> respList = outBusinessServiceApi.getMasterActivityReward(rqt);

        log.info("getMasterActivityReward rqt: " + JSON.toJSONString(rqt) + "，resp:" + JSON.toJSONString(respList));


        if(CollectionUtils.isEmpty(respList)){
            return ;
        }

        List<AgreementMasterBase> finalAgreementMasterBaseList = new ArrayList<>();
        for(MasterActivityRewardResp resp : respList){
            if(CollectionUtils.isEmpty(resp.getRewardList())){
                continue;
            }
            Set<MasterActivityRewardResp.ActivityRewardInfo> activityRewardInfoList = resp.getRewardList().stream().filter(activityRewardInfo -> "bonus".equals(activityRewardInfo.getRewardType()) && "fixed_value".equals(activityRewardInfo.getRewardGiveType())
                    && "quoted".equals(activityRewardInfo.getTaskSymbol())).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(activityRewardInfoList)){
                continue;
            }
            BigDecimal maxRewardValue = BigDecimal.ZERO;
            for(MasterActivityRewardResp.ActivityRewardInfo activityRewardInfo : activityRewardInfoList){
                if(activityRewardInfo.getRewardValue().compareTo(maxRewardValue) > 0){
                    maxRewardValue = activityRewardInfo.getRewardValue();
                }
            }

            if(maxRewardValue.add(definiteServeFee).compareTo(agreementMasterBaseMap.get(String.valueOf(resp.getMasterId())).getCooperationPrice()) >= 0){
                finalAgreementMasterBaseList.add(agreementMasterBaseMap.get(String.valueOf(resp.getMasterId())));
            }
        }


        if(CollectionUtils.isEmpty(finalAgreementMasterBaseList)){
            return ;
        }

        MasterAutoReceiverRqt masterAutoReceiverRqt = new MasterAutoReceiverRqt();

        List<MasterAutoReceiverRqt.MasterSubsidiaryInfo> masterSubsidiaryInfoList = Lists.newArrayList();

        masterAutoReceiverRqt.setOrderId(orderBase.getOrderId());
        masterAutoReceiverRqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        masterAutoReceiverRqt.setAppointType(4);
        //不需要做新合约师傅兜底奖励
        masterAutoReceiverRqt.setAppointDetailType(AppointDetailType.AUTO_GRAB_AGREEMENT.getCode());
        List<MasterAutoReceiverRqt.MasterPrice> masterList = new ArrayList<>();
        finalAgreementMasterBaseList.forEach(agreementMasterBase -> {
            MasterAutoReceiverRqt.MasterPrice masterPrice = new MasterAutoReceiverRqt.MasterPrice();
            masterPrice.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterPrice.setPrice(agreementMasterBase.getCooperationPrice());
            masterList.add(masterPrice);

            MasterAutoReceiverRqt.MasterSubsidiaryInfo masterSubsidiaryInfo = new MasterAutoReceiverRqt.MasterSubsidiaryInfo();
            masterSubsidiaryInfo.setMasterId(Long.valueOf(agreementMasterBase.getMasterId()));
            masterSubsidiaryInfo.setExtraType("master_recruit_id");
            masterSubsidiaryInfo.setExtraId(Objects.isNull(agreementMasterBase.getRecruitId()) ? 0L : Long.parseLong(agreementMasterBase.getRecruitId()));
            masterSubsidiaryInfoList.add(masterSubsidiaryInfo);
        });
        masterAutoReceiverRqt.setMasterList(masterList);

        MasterAutoReceiverRqt.SubsidiaryParam subsidiaryParam = new MasterAutoReceiverRqt.SubsidiaryParam();
        subsidiaryParam.setMasterSubsidiaryInfoList(masterSubsidiaryInfoList);
        masterAutoReceiverRqt.setSubsidiaryParamJSON(JSON.toJSONString(subsidiaryParam));

        rocketMqSendService.sendDelayMessage(orderDistributeTopic,"order_batch_master_auto_offer", JSON.toJSONString(masterAutoReceiverRqt),1L);

        log.info("userAgreementMasterAutoGrab message: " + JSON.toJSONString(masterAutoReceiverRqt));
        return ;

    }


    /**
     * 一口价订单协议师傅动态抢单（根据智能运营红包奖励）dv
     *
     * @param orderBase
     * @param firstPush
     */
    public void agreementMasterAutoGrab(OrderBase orderBase, Integer firstPush) {

        if (!"on".equals(definitePriceByRedPackAutoReceiveMoveSwitch)) {
            return;
        }


        if (firstPush != 1) {
            return;
        }

        if (!checkServeIds(orderBase)) {
            return;
        }


        AgreementMasterAutoGrabMessage message = new AgreementMasterAutoGrabMessage();
        message.setOrderId(orderBase.getOrderId());
        rocketMqSendService.sendDelayMessage(orderDistributeTopic, "agreement_master_auto_grab", JSON.toJSONString(message), agreementMasterAutoGrabDelayTime * 60 * 1000L);
    }


    public int dynamicAutoReceive(Long orderId){
        boolean result = this.orderDistribute(orderId);
        if(result){
            DynamicAutoReceiveMessage message = new DynamicAutoReceiveMessage();
            message.setOrderId(orderId);
            pushQueueService.sendDynamicAutoReceiveMessage(message,dynamicAutoReceiveDelayTime * 60 * 1000L);
        }
        return 1;

    }

    private boolean checkServeIds(OrderBase orderBase){

        if("all".equals(agreementMasterAutoGrabServeIds)){
            return true;
        }

        if("0".equals(agreementMasterAutoGrabServeIds)){
            return false;
        }

        List<String> orderLv1ServeIdList = Arrays.asList(orderBase.getServeLevel1Ids().split(",")).stream().collect(Collectors.toList());

        List<String> orderServeIdList = Arrays.asList(orderBase.getServeIds().split(",")).stream().collect(Collectors.toList());

        orderLv1ServeIdList.addAll(orderServeIdList);

        orderLv1ServeIdList.retainAll(Arrays.asList(agreementMasterAutoGrabServeIds.split(",")).stream().collect(Collectors.toList()));


        if(CollectionUtils.isNotEmpty(orderLv1ServeIdList)){
            return true;
        }

        return false;

    }

    private boolean checkUserAgreementMasterAutoGrab(OrderBase orderBase,OrderGrab orderGrab){

        if(!AccountType.USER.code.equals(orderBase.getAccountType())){
            return false;
        }

        if(orderGrab.getAppointType() != 4){
            return false;
        }

        if(Objects.isNull(orderBase.getFourthDivisionId()) || orderBase.getFourthDivisionId() == 0){
            return false;
        }

        return true;
    }

    private List<AgreementMaster> matchUserAgreementMaster(OrderBase orderBase){

        List<String> cooperationBusinessList = getCooperationBusiness(orderBase);
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();


        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));

        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));

        boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderBase.getFourthDivisionId())));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", "0"));

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if(StringUtils.isBlank(orderBase.getServeIds())){
            return null;
        }


        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", "geo_fence"));

//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));



        Arrays.asList(orderBase.getServeIds().split(",")).stream().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType","noCooperationPrice"));

        //TODO 添加指定用户查询条件


        log.info("search agreementmaster request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> {
            if(StringUtils.isNotBlank(agreementMaster.getUserIds())){
                Set<Long> userIdSet = Arrays.stream(agreementMaster.getUserIds().split(",")).map(Long::valueOf).collect(Collectors.toSet());
                return userIdSet.contains(orderBase.getAccountId());
            }else{
                return true;
            }
        }).collect(Collectors.toList());

        return agreementMasterList;
    }

    /**
     * 获取业务范围
     * @param orderBase
     * @return
     */
    public List<String> getCooperationBusiness(OrderBase orderBase) {
        return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FINISHED_PRODUCT.code);
    }


    private List<AgreementMaster> filterByDailyOrderCnt(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = agreementMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("recruit_id");
        fieldColumnList.add("order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList,fieldColumnList,"master_recruit_daily");
        Map<String,Integer> orderCntMap = new HashMap<>();

        if(Objects.nonNull(resultArray) && resultArray.size() > 0){
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String)jsonObject.get("master_id");
                String recruitId = (String)jsonObject.get("recruit_id");
                if(jsonObject.containsKey("order_cnt")){
                    String orderCnt = (String)jsonObject.get("order_cnt");
                    if(org.apache.commons.lang.StringUtils.isNotBlank(orderCnt)){
                        orderCntMap.put(masterId + "_" + recruitId,Integer.valueOf(orderCnt));
                    }
                }
            }
        }


        return agreementMasterList.stream().filter(agreementMaster -> agreementMaster.getMaxDailyOrder() == 0 || orderCntMap.getOrDefault(agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId(),0) < agreementMaster.getMaxDailyOrder()).collect(Collectors.toList());
    }

    @Resource
    private RecruitMasterApi recruitMasterApi;
    public List<AgreementMaster> filterByNewContractRestStatus(List<AgreementMaster> agreementMasterList){
        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }
        if(agreementMasterList.get(0).getSceneId() != 4){
            return agreementMasterList;
        }

        return agreementMasterList.stream().filter(agreementMaster -> {
            GetMasterCurrentLeaveRequestRqt rqt = new GetMasterCurrentLeaveRequestRqt();
            rqt.setMasterId(Long.valueOf(agreementMaster.getMasterId()));
            rqt.setNowTime(new Date());
            rqt.setSceneId(4L);
            GetMasterCurrentLeaveRequestResp resp = recruitMasterApi.getMasterCurrentLeaveRequest(rqt);
            return Objects.isNull(resp) ? true : Objects.isNull(resp.getLeaveRequestStartTime()) ? true : false;
        }).collect(Collectors.toList());

    }

    private List<AgreementMasterBase> getAgreementMasterCooperationPrice(List<AgreementMaster> agreementMasterList, OrderBase orderBase,
                                                                         OrderGrab orderGrab,
                                                                         List<OrderServiceAttributeInfo> orderServiceAttributeInfoList){
        ApplyOrderCalculateBatchReq batchReq = new ApplyOrderCalculateBatchReq();
        ApplyOrderCalculateReq calculateReq = new ApplyOrderCalculateReq();
        Long orderId = orderBase.getOrderId();
        //TODO 补充场景编码
        List<String> sceneCode = Collections.singletonList("contract_master");
        calculateReq.setSceneCode(sceneCode);
        com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
        orderBaseParam.setOrderId(orderId);
        orderBaseParam.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        orderBaseParam.setCreateTime(orderBase.getCreateTime());
        calculateReq.setOrderBase(orderBaseParam);
        List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(),CalculateServiceInfo.class)));
        calculateReq.setServiceInfos(serviceInfos);
        AccountInfo from = new AccountInfo();
        from.setAccountId(orderBase.getAccountId());
        from.setAccountType(orderBase.getAccountType());
        calculateReq.setFrom(from);

        AddressInfo addressInfo = new AddressInfo();
        Long divisionId;
        if(orderBase.getFourthDivisionId() > 0){
            divisionId = orderBase.getFourthDivisionId();
            addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);

        }else{
            divisionId = orderBase.getThirdDivisionId();
            addressInfo.setDivisionType(DivisionTypeEnum.DISTRICT.code);
        }
        Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
        BeanUtils.copyProperties(address,addressInfo);
        calculateReq.setAddressInfo(addressInfo);


        Integer appointType = orderGrab.getAppointType();
        if(AppointType.OPEN.value.equals(appointType)){
            calculateReq.setIsMappingPricing(false);
        }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
            calculateReq.setIsMappingPricing(true);
            calculateReq.setFromSceneCode("contract_master");
        }

        batchReq.setApplyOrderCalculateReq(calculateReq);

        List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();

        agreementMasterList.forEach(masterInfo -> {
            BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
            bizRuleBatchReq.setBizId(String.valueOf(masterInfo.getMasterId()));
            bizRuleBatchReq.setBizTag(String.valueOf(masterInfo.getRecruitId()));
            bizRuleBatchReqList.add(bizRuleBatchReq);

        });


        batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);
        log.info("applyOrderCalculateBatchReq:" + JSON.toJSONString(batchReq));
        List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
        log.info("applyOrderCalculateBatchResp:" + JSON.toJSONString(applyOrderCalculateBatchRespList));
        List<ApplyOrderCalculateBatchResp> successList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess()).collect(Collectors.toList());
        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(successList)){

            Map<String, AgreementMaster> agreementMasterMap = agreementMasterList.stream()
                    .collect(Collectors.toMap(AgreementMaster::getId, agreementMaster -> agreementMaster));

            successList.forEach(resp -> {
                String masterId = resp.getBizId();



                AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                agreementMasterBase.setMasterId(masterId);
                agreementMasterBase.setRecruitId(resp.getBizTag());

                String agreementMasterId = agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId();
                AgreementMaster agreementMaster = agreementMasterMap.get(agreementMasterId);

                agreementMasterBase.setDirectAppointMethod(agreementMaster.getDirectAppointMethod());
                BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
                BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
                if(Objects.isNull(basePrice)){
                    basePrice = BigDecimal.ZERO;
                }

                if(orderGrab.getAppointType() == 2){
                    Long offerPriceIncreasePercent = agreementMaster.getOfferPriceIncreasePercent();
                    if(Objects.nonNull(offerPriceIncreasePercent) && offerPriceIncreasePercent > 0){
                        cost = cost.add(cost.multiply( BigDecimal.valueOf(offerPriceIncreasePercent)).divide(BigDecimal.valueOf(100L)));
                    }
                }

                agreementMasterBase.setCooperationPrice(cost.compareTo(basePrice) >= 0 ? cost : basePrice);
                agreementMasterBaseList.add(agreementMasterBase);

            });
        }
        return agreementMasterBaseList;
    }


    /**
     * 普通订单使用总包协议师傅兜底报价或抢单
     *
     * @param orderId
     * @param isFirstPush
     * @param masterSourceType
     * @return
     */
    public Integer matchNormalOrderDistributeStrategy(Long orderId, Integer isFirstPush, String masterSourceType) {

        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGrabByIdResp.getOrderServiceAttributeInfos();


        if (!checkAgreementMasterAutoReceiveCondition(orderBase, orderGrab, orderServiceAttributeInfoList)) {
            return 0;
        }


        Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());
        if (Objects.isNull(cityDivisionId) || cityDivisionId == 0) {
            return 0;
        }


        if (orderGrab.getAppointType() == 2 && isFirstPush == 1) {
            List<Integer> categoryIdList = Arrays.stream(agreementOfferPriceNoticeCategoryList.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            if (categoryIdList.contains(orderBase.getCategoryId())) {
                CompensateDistributeMessage distributeMessage = new CompensateDistributeMessage();
                distributeMessage.setDistributeType("receive_notice");
                distributeMessage.setOrderId(orderBase.getOrderId());
                CompensateDistribute compensateDistribute = new CompensateDistribute();
                compensateDistribute.setCompensateType(CompensateType.NONE_RECEIVE.getCode());
                compensateDistribute.setTriggerNum(1);
                distributeMessage.setCompensateDistribute(compensateDistribute);
                pushQueueService.sendNormalOrderDistribute(distributeMessage, agreementReceiveNoticeDelayTime * 60 * 1000L);
            }

        }
        Integer businessLineId = orderBase.getBusinessLineId();
        if (MasterSourceType.TOC.code.equals(masterSourceType) && businessLineId == 2) {
            businessLineId = 999;
        }
        NormalOrderDistributeStrategy orderDistributeStrategy = matchNormalOrderDistributeStrategy(businessLineId, orderBase.getCategoryId(), cityDivisionId);

        if (Objects.isNull(orderDistributeStrategy)) {
            return 0;
        }

        //根据补偿调度条件进行自动报价或抢单
        if (StringUtils.isNotBlank(orderDistributeStrategy.getCompensateDistributeList())) {
            List<CreateRqt.CompensateDistributeStrategy> compensateDistributeStrategyList = JSON.parseArray(orderDistributeStrategy.getCompensateDistributeList(), CreateRqt.CompensateDistributeStrategy.class);
            compensateDistributeStrategyList.forEach(compensateDistributeStrategy -> {
                Integer compensateDistributeStrategyId = compensateDistributeStrategy.getCompensateDistributeId();
                CompensateDistribute compensateDistribute = compensateDistributeRepository.selectByPrimaryKey(compensateDistributeStrategyId);

                CompensateDistributeMessage message = new CompensateDistributeMessage();
                message.setDistributeType("auto_receive");
                message.setOrderId(orderBase.getOrderId());
                message.setCompensateDistribute(compensateDistribute);
                int intervalTime = compensateDistribute.getIntervalTime();
                long delayTime;
                if (isFirstPush == 1) {
                    if (intervalTime == 0) {
                        delayTime = 5L;
                    } else {
                        delayTime = intervalTime * 60 * 1000L;
                    }
                } else {
                    delayTime = 1;
                }

                pushQueueService.sendNormalOrderDistribute(message, delayTime);

            });


            return 1;
        }


        //兼容旧的补偿调度
        List<CreateRqt.MatchRoutingRule> matchRoutingRuleList = JSON.parseArray(orderDistributeStrategy.getCompensateDistributeStrategyList(), CreateRqt.MatchRoutingRule.class);


        List<CompensateDistribute> compensateDistributeList = compensateDistributeRepository.selectByCategoryIdAndAppointType(businessLineId,
                PushMode.NORMAL.code, Long.valueOf(orderBase.getCategoryId()), orderGrab.getAppointType(), 2, 2);

        if (CollectionUtils.isEmpty(compensateDistributeList)) {
            return 0;
        }


        List<NormalOrderDistribute> normalOrderDistributeList = normalOrderDistributeRepository.selectByOrderId(orderId);


        Map<String, CompensateDistribute> compensateDistributeMap = compensateDistributeList.stream().collect(Collectors.toMap(CompensateDistribute::getCompensateType, each -> each, (value1, value2) -> value1));

        matchRoutingRuleList.forEach(matchRoutingRule -> {
            CompensateDistribute compensateDistribute = compensateDistributeMap.get(matchRoutingRule.getItem().getItemName());
            CompensateDistributeMessage message = new CompensateDistributeMessage();
            message.setDistributeType("auto_receive");
            message.setOrderId(orderBase.getOrderId());
            message.setCompensateDistribute(compensateDistribute);
            int intervalTime = compensateDistribute.getIntervalTime();
            long delayTime;
            if (isFirstPush == 1) {
                if (intervalTime == 0) {
                    delayTime = 5L;
                } else {
                    delayTime = intervalTime * 60 * 1000L;
                }
            } else {
                if (CollectionUtils.isNotEmpty(normalOrderDistributeList)) {
                    delayTime = 1;
                } else {
                    return;
                }

            }

            pushQueueService.sendNormalOrderDistribute(message, delayTime);
        });


        return 0;
    }


    private NormalOrderDistributeStrategy matchNormalOrderDistributeStrategy(Integer businessLineId, Integer categoryId, Long cityDivisionId){

        NormalOrderDistributeStrategy normalOrderDistributeStrategy = normalOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,String.valueOf(categoryId),String.valueOf(cityDivisionId));

        if(Objects.isNull(normalOrderDistributeStrategy)){
            normalOrderDistributeStrategy = normalOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,String.valueOf(categoryId),"all");
        }


        if(Objects.isNull(normalOrderDistributeStrategy)){
            normalOrderDistributeStrategy = normalOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,"all",String.valueOf(cityDivisionId));
        }

        if(Objects.isNull(normalOrderDistributeStrategy)){
            normalOrderDistributeStrategy = normalOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,"all","all");
        }

        return normalOrderDistributeStrategy;

    }



    @Value("${agreement.master.city:all}")
    private String agreementMasterCity;


    @Value("${agreement.master.blackList.city:0}")
    private String agreementMasterBlackListCity;

    private boolean checkCityAndServe(OrderGrab orderGrab,OrderBase orderBase){
        
        if(orderGrab.getSecondDivisionId() == null || orderGrab.getSecondDivisionId() == 0){
            return false;
        }

        if(StringUtils.isNotBlank(agreementMasterBlackListCity)){
            if("all".equals(agreementMasterBlackListCity)){
                return false;
            }else{
                List<Long> blackListCityList = Arrays.stream(agreementMasterBlackListCity.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(blackListCityList.contains(orderGrab.getSecondDivisionId())){
                    return false;
                }
            }
        }



        //不限制订单服务的城市
        if(StringUtils.isNotBlank(agreementMasterCity)){
            if("all".equals(agreementMasterCity)){
                return true;
            }else{
                List<Long> cityList = Arrays.stream(agreementMasterCity.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(cityList.contains(orderGrab.getSecondDivisionId())){
                    return true;
                }
            }
        }


        //其他城市限制服务模式和服务
        if(orderBase.getBusinessLineId() == 1 && orderGrab.getAppointType() == 4){
            List<Long> orderServeIdList = Arrays.stream(orderBase.getServeIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            if(orderServeIdList.contains(400353L) || orderServeIdList.contains(400354L) || orderServeIdList.contains(400349L)){
                //如果订单是平台一口价，服务包含板式床安装或实木床安装或衣柜安装则不推送协议师傅
                return false;
            }

        }
        return true;
    }

    private boolean checkAgreementMasterAutoReceiveCondition(OrderBase orderBase,OrderGrab orderGrab,
                                                             List<OrderServiceAttributeInfo> orderServiceAttributeInfoList){


        if(Objects.isNull(orderGrab)){
            return false;
        }

        if(CollectionUtils.isEmpty(orderServiceAttributeInfoList)){
            return false;
        }

        if(orderServiceAttributeInfoList.size() != 1){
            return false;
        }

        if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            return false;
        }

        Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());

        if(Objects.isNull(cityDivisionId) || cityDivisionId == 0){
            return false;
        }

        Integer appointType = orderGrab.getAppointType();

        boolean isAgreementAutoReceive = false;

        if(AppointType.OPEN.value.equals(appointType)){
            isAgreementAutoReceive = this.isAgreementAutoOfferPrice(cityDivisionId);
        }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
            isAgreementAutoReceive = this.isAgreementAutoGrab(cityDivisionId);
        }

        if(!isAgreementAutoReceive){
            return false;
        }


        if(!checkCityAndServe(orderGrab,orderBase)){
            return false;
        }


        return orderGrab != null && AccountType.USER.code.equals(orderBase.getAccountType()) &&
                orderBase.getBusinessLineId() == 1 && "site".equals(orderBase.getOrderFrom()) &&
                OrderStatus.TRADING.code.equals(orderBase.getOrderStatus()) &&
                Objects.nonNull(orderBase.getFourthDivisionId()) &&
                orderGrab.getConfirmServeStatus() != 1;
    }


    public boolean isAgreementAutoOfferPrice(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(agreementAutoOfferPriceCityList)){
                return false;
            }

            if ("0".equals(agreementAutoOfferPriceCityList)) {
                return false;
            }

            if ("all".equals(agreementAutoOfferPriceCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(agreementAutoOfferPriceCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }
        return false;
    }



    public boolean isAgreementAutoGrab(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(agreementAutoOfferPriceCityList)){
                return false;
            }

            if ("0".equals(agreementAutoOfferPriceCityList)) {
                return false;
            }

            if ("all".equals(agreementAutoOfferPriceCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(agreementAutoOfferPriceCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }
        return false;
    }

}
