package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;
import com.wanshifu.master.order.push.mapper.CompensateDistributeMapper;

import com.wanshifu.master.order.push.repository.CompensateDistributeRepository;
import com.wanshifu.master.order.push.service.CompensateDistributeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

import java.util.List;

@Service
public class CompensateDistributeServiceImpl implements CompensateDistributeService {

    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "补偿调度管理",
            createAccountIdFieldName = "createAccountId",
            businessLineIdFieldName = "businessLineId", configNameFieldName = "strategyName")
    public int create(CreateRqt rqt){
        Integer businessLineId = rqt.getBusinessLineId();
        String orderPushFlag = rqt.getOrderPushFlag();
        List<String> categoryIdList = Arrays.asList(rqt.getCategoryIds().split(","));
        List<String> appointTypeList = Arrays.asList(rqt.getAppointTypes().split(","));
        String compensateType = rqt.getCompensateType();
        Integer hasPrice = rqt.getHasPrice();
        Integer hasCooperationUser = rqt.getHasCooperationUser();
        checkUniq(businessLineId,orderPushFlag,categoryIdList,appointTypeList,
                null, hasPrice, hasCooperationUser, compensateType);
        return compensateDistributeRepository.insertDistribute(rqt.getBusinessLineId(),rqt.getStrategyName(),rqt.getStrategyDesc(),rqt.getCategoryIds(),rqt.getAppointTypes(),rqt.getOrderPushFlag(),
                rqt.getHasPrice(),rqt.getHasCooperationUser(),rqt.getCompensateType(),rqt.getIntervalTime(),rqt.getTriggerNum(),rqt.getCreateAccountId());
    }


    private void checkUniq(Integer businessLineId,String orderPushFlag,List<String> categoryIdList, List<String> appointTypeList,
                           Integer distributeId,Integer hasPrice,Integer hasCooperationUser,String compensateType) {
        CompensateDistribute compensateDistribute = compensateDistributeRepository.selectByCategoryIdAndCompensateType(businessLineId,orderPushFlag,categoryIdList,appointTypeList,distributeId,hasPrice,hasCooperationUser,compensateType);
        Assert.isNull(compensateDistribute, "该业务线已存在相同调度!");
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "补偿调度管理",
            tableName = "compensate_distribute", mapperClass = CompensateDistributeMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "compensateDistributeMapper", primaryKeyFieldName = "distributeId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt){
        Integer businessLineId = rqt.getBusinessLineId();
        String orderPushFlag = rqt.getOrderPushFlag();
        List<String> categoryIdList = Arrays.asList(rqt.getCategoryIds().split(","));
        List<String> appointTypeList = Arrays.asList(rqt.getAppointTypes().split(","));
        String compensateType = rqt.getCompensateType();
        Integer hasPrice = rqt.getHasPrice();
        Integer hasCooperationUser = rqt.getHasCooperationUser();
        Integer distributeId = rqt.getDistributeId();
        checkUniq(businessLineId,orderPushFlag,categoryIdList,appointTypeList,
                distributeId, hasPrice, hasCooperationUser, compensateType);
        return compensateDistributeRepository.updateDistribute(rqt.getDistributeId(),rqt.getBusinessLineId(),rqt.getStrategyName(),rqt.getStrategyDesc(),rqt.getCategoryIds(),rqt.getAppointTypes(),rqt.getOrderPushFlag(),
                rqt.getHasPrice(),rqt.getHasCooperationUser(),rqt.getCompensateType(),rqt.getIntervalTime(),rqt.getTriggerNum(),rqt.getUpdateAccountId());
    }

    @Override
    public CompensateDistribute detail(DetailRqt rqt){
        Integer distributeId = rqt.getDistributeId();
        return compensateDistributeRepository.selectByPrimaryKey(distributeId);
    }

    @Override
    public SimplePageInfo<CompensateDistribute> list(ListRqt rqt){
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<CompensateDistribute> compensateDistributeList = compensateDistributeRepository.selectList(rqt.getBusinessLineId(),rqt.getCategoryId(),rqt.getCompensateType(),rqt.getOrderPushFlag());

        SimplePageInfo<CompensateDistribute> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(startPage.getPages());
        listRespSimplePageInfo.setPageNum(startPage.getPageNum());
        listRespSimplePageInfo.setTotal(startPage.getTotal());
        listRespSimplePageInfo.setPageSize(startPage.getPageSize());
        listRespSimplePageInfo.setList(compensateDistributeList);
        return listRespSimplePageInfo;

    }


    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "补偿调度管理",
            tableName = "compensate_distribute", mapperClass = CompensateDistributeMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "compensateDistributeMapper", primaryKeyFieldName = "distributeId",
            businessLineIdFieldNameFromEntity = "businessLineId", configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt){
        return compensateDistributeRepository.softDeleteByStrategyId(rqt.getDistributeId());

    }

    @Override
    public List<CompensateDistribute> match(MatchRqt rqt){
        return compensateDistributeRepository.match(rqt);
    }


}
