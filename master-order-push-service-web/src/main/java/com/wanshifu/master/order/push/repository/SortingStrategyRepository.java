package com.wanshifu.master.order.push.repository;

import cn.hutool.core.lang.Assert;
import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.CommonConstant;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.mapper.SortingStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 精排策略Repository
 * <AUTHOR>
 */
@Repository
public class SortingStrategyRepository extends BaseRepository<SortingStrategy> {

    @Resource
    private SortingStrategyMapper sortingStrategyMapper;

    public SortingStrategy selectStrategyByStrategyId(Long strategyId){
        Condition condition = new Condition(SortingStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", strategyId);
        condition.selectProperties("ruleExpression","snapshotId","strategyName","strategyVersion");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }


    public SortingStrategy selectByStrategyId(Long strategyId) {
        SortingStrategy sortingStrategy = this.selectByPrimaryKey(strategyId);
        Assert.isTrue(sortingStrategy != null && Objects.equals(sortingStrategy.getIsDelete(), CommonConstant.DELETE_STATUS_0), "该策略不存在!");
        return sortingStrategy;
    }

    public SortingStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Integer businessLineId, Long strategyId) {
        Example example = new Example(SortingStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", CommonConstant.DELETE_STATUS_0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

    public int insert(String orderFlag,String strategyName, Long snapshotId,String strategyDesc, String categoryIds, String sortingRule, String ruleExpression, Integer businessLineId, Long loginUserId,Integer strategyVersion) {
        SortingStrategy sortingStrategy = new SortingStrategy();
        sortingStrategy.setOrderFlag(orderFlag);
        sortingStrategy.setStrategyName(strategyName);
        sortingStrategy.setSnapshotId(snapshotId);
        sortingStrategy.setStrategyDesc(strategyDesc);
        sortingStrategy.setCategoryIds(categoryIds);
        sortingStrategy.setSortingRule(sortingRule);
        sortingStrategy.setRuleExpression(ruleExpression);
        sortingStrategy.setStrategyStatus(CommonConstant.STRATEGY_STATUS_0);
        sortingStrategy.setBusinessLineId(businessLineId);
        sortingStrategy.setStrategyVersion(strategyVersion);
        sortingStrategy.setCreateAccountId(loginUserId);
        sortingStrategy.setUpdateAccountId(loginUserId);
        return super.insertSelective(sortingStrategy);
    }

    public int update(String orderFlag,Long strategyId,Long snapshotId, String strategyName, String strategyDesc, String categoryIds, String sortingRule, String ruleExpression, Integer businessLineId, Long loginUserId) {
        SortingStrategy sortingStrategy = new SortingStrategy();
        sortingStrategy.setOrderFlag(orderFlag);
        sortingStrategy.setStrategyId(strategyId);
        sortingStrategy.setSnapshotId(snapshotId);
        sortingStrategy.setStrategyName(strategyName);
        sortingStrategy.setStrategyDesc(strategyDesc);
        sortingStrategy.setCategoryIds(categoryIds);
        sortingStrategy.setSortingRule(sortingRule);
        sortingStrategy.setRuleExpression(ruleExpression);
        sortingStrategy.setBusinessLineId(businessLineId);
        sortingStrategy.setUpdateAccountId(loginUserId);
        return this.updateByPrimaryKeySelective(sortingStrategy);
    }

    public int updateStatus(Long strategyId, Integer strategyStatus,Long updateAccountId) {
        SortingStrategy sortingStrategy = new SortingStrategy();
        sortingStrategy.setStrategyId(strategyId);
        sortingStrategy.setStrategyStatus(strategyStatus);
        sortingStrategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(sortingStrategy);
    }

    public List<SortingStrategy> selectList(String orderFlag, Long businessLineId, String strategyName, Integer strategyStatus, Date createStartTime, Date createEndTime, List<Long> categoryIdList) {
        return sortingStrategyMapper.selectList(orderFlag,businessLineId,strategyName,strategyStatus,createStartTime,createEndTime,categoryIdList);
    }




    public int softDeleteByStrategyId(Long strategyId) {
        SortingStrategy sortingStrategy = new SortingStrategy();
        sortingStrategy.setStrategyId(strategyId);
        sortingStrategy.setIsDelete(CommonConstant.DELETE_STATUS_1);
        return updateByPrimaryKeySelective(sortingStrategy);
    }

    public List<SortingStrategy> selectByStrategyIds(List<Long> filterStrategyIds) {
        Example example = new Example(SortingStrategy.class);
        example.createCriteria().andIn("strategyId",filterStrategyIds);
        return this.selectByExample(example);
    }


}