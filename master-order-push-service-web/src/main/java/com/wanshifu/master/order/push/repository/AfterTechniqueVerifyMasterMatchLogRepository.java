package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AfterTechniqueVerifyMasterMatchLog;
import com.wanshifu.master.order.push.mapper.AfterTechniqueVerifyMasterMatchLogMapper;

import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/1 16:28
 */
@Repository
public class AfterTechniqueVerifyMasterMatchLogRepository extends BaseRepository<AfterTechniqueVerifyMasterMatchLog> {

    @Resource
    private AfterTechniqueVerifyMasterMatchLogMapper afterTechniqueVerifyMasterMatchLogMapper;
}
