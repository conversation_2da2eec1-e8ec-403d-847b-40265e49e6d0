package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AgreementMasterBase implements Comparable<AgreementMasterBase>{

    /**
     * 协议师傅id
     */
    private String id;

    /**
     * 师傅id
     */
    private String masterId;

    /**
     * 招募id
     */
    private String recruitId;

    /**
     * 合作价
     */
    private BigDecimal cooperationPrice;

    /**
     * 协议师傅标签
     */
    private String recruitTagName;

    /**
     * 报价顺序
     */
    private Integer offerSort;


    /**
     * 派单模式
     */
    private String directAppointMethod;


    /**
     * 计价模式
     */
    private String pricingMethod;

    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    private String masterSourceType;



    @Override
    public int compareTo(AgreementMasterBase o) {
        return o.cooperationPrice.compareTo(this.cooperationPrice);
    }

}
