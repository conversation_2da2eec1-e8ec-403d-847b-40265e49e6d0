package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.Enterprise;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 初筛策略Mapper
 * <AUTHOR>
 */
public interface OrderRoutingStrategyMapper extends IBaseCommMapper<OrderRoutingStrategy> {

    List<OrderRoutingStrategy> selectList(@Param("businessLineId")Integer businessLineId,@Param("strategyStatus") Integer strategyStatus,@Param("strategyName") String strategyName,@Param("createStartTime")  Date createStartTime,@Param("createEndTime")  Date createEndTime,
                                       @Param("categoryIdList") List<Long> categoryIdList,@Param("cityId")  Long cityId);


    OrderRoutingStrategy selectByCityAndCategory( @Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("strategyId") Integer strategyId, @Param("businessLineId") Integer businessLineId);

    OrderRoutingStrategy selectStrategy(@Param("orderTag") String orderTag,@Param("cityId") String cityId, @Param("categoryId") String categoryId,@Param("businessLineId")Integer businessLineId);

    OrderRoutingStrategy selectRoutingStrategy(@Param("orderTag")String orderTag, @Param("cityIdList") List<String> cityIdList, @Param("categoryIdList") List<String> categoryIdList, @Param("businessLineId") Integer businessLineId);

}