package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.AccountRole;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class AccountRoleRepository extends BaseRepository<AccountRole> {


    public List<AccountRole> selectByRoleIdList(List<Integer> roleIdList){
        Condition condition = new Condition(AccountRole.class);
        condition.createCriteria()
                .andIn("roleId", roleIdList)
                .andEqualTo("isDelete", 0);
        return this.selectByCondition(condition);
    }


    public int insertAccountRoleList(Integer roleId,List<Long> accountIdList){
        List<AccountRole> accountRoleList = new ArrayList<>();
        accountIdList.forEach(accountId -> {
            AccountRole accountRole = new AccountRole();
            accountRole.setAccountId(accountId);
            accountRole.setRoleId(roleId);
            accountRole.setIsDelete(0);
            accountRole.setCreateTime(new Date());
            accountRole.setUpdateTime(new Date());
            accountRoleList.add(accountRole);
        });
        return this.insertList(accountRoleList);
    }


    public int deleteByRoleId(Integer roleId){
        AccountRole accountRole = new AccountRole();
        accountRole.setRoleId(roleId);
        return this.delete(accountRole);
    }

    public List<AccountRole> selectByAccountId(Long accountId){
        AccountRole accountRole = new AccountRole();
        accountRole.setAccountId(accountId);
        return this.select(accountRole);
    }


}