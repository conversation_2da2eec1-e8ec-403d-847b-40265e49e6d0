package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "t_enterprise_limit_user")
public class EnterpriseLimitUser {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "enterprise_id")
    private String enterpriseId;


    @Column(name = "user_id")
    private String userId;


    @Column(name = "limit_type")
    private String limitType;


    @Column(name = "is_delete")
    private String isDelete;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
