package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.dto.MasterSettleInRqt;
import com.wanshifu.master.order.push.domain.message.*;

/**
 * <AUTHOR>
 * 师傅操作通知service
 */
public interface MasterOperationNotifyService {

    Integer masterReserveCustomer(MasterReserveCustomerMessage message);

    Integer masterHangOrder(MasterHangOrderMessage message);

    Integer masterServeFinish(MasterServeFinishMessage message);

    Integer masterServeReturnFinish(MasterServeReturnFinishMessage message);


    Integer masterAgreeRefund(MasterAgreeRefundMessage message);

    Integer masterRestState(MasterRestStateMessage message);


    Integer addTechnique(MasterAddTechniqueMessage message);

    Integer addServeFourthDivision(MasterAddFourthDivisionMessage message);


    void masterSettleIn(MasterSettleInRqt message);


    Integer masterOfferPrice(MasterOfferPriceMessage message);


    Integer masterModifyOfferPrice(MasterModifyOfferPriceMessage message);


    Integer tocMasterAtTobAppOfferLimit(TocMasterAtTobAppOfferLimitMessage message);


    }
