package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderMatchRoutingMapper extends IBaseCommMapper<OrderMatchRouting> {

    List<OrderMatchRouting> selectList(@Param("routingName") String routingName,@Param("createStartTime") Date createStartTime,@Param("createEndTime") Date createEndTime);

}