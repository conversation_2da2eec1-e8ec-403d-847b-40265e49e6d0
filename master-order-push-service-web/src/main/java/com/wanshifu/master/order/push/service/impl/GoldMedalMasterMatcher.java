package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金牌维修师傅匹配器
 * <AUTHOR>
 * @date 2023-09-13 14:04:00
 */
@Slf4j
@Component("gold_medal_master")
public class GoldMedalMasterMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;
    
    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;


    @Value("${goldMedalMaster.special.city.list:441900}")
    private String specialCityList;


    /**
     * 匹配代理商师傅
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){


        if(!apolloConfigUtils.isPushGoldMedalMaster()){
            return Boolean.FALSE;
        }

        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            log.info("推送金牌维修师傅，该城市订单不推单");
            return Boolean.FALSE;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();

        if(StringUtils.isNotBlank(pushMode) && (!PushMode.GOLD_MEDAL_MASTER.code.equals(pushMode))){
            return Boolean.FALSE;
        }

        if(!(AppointType.OPEN.value.equals(orderDetailData.getAppointType()) && AccountType.USER.code.equals(orderDetailData.getAccountType()))){
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition condition) {

        List<MasterBaseSearch> masterBaseSearchList = searchGoldMedalMaster(orderDetailData,condition);

        Set<String> masterIdSet = null;
        if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
            masterIdSet = masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
        }
        return new MatchMasterResult(masterIdSet);
    }


    private List<MasterBaseSearch> searchGoldMedalMaster(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("goldMedalMasterStatus", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isAccountNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", MasterSourceType.TOB.code));


        BoolQueryBuilder divisionBoolQueryBuilder = new BoolQueryBuilder();

        //1. 手动设置金牌维修师傅服务区域（城市）
        BoolQueryBuilder boolQueryBuilder1 = new BoolQueryBuilder();
        boolQueryBuilder1.must(QueryBuilders.termQuery("goldMedalMasterDivisionSetType", String.valueOf(1)));
        boolQueryBuilder1.must(QueryBuilders.termQuery("goldMedalMasterCityDivisionIds", String.valueOf(orderDetailData.getSecondDivisionId())));
        divisionBoolQueryBuilder.should(boolQueryBuilder1);


        //2. 自动获取金牌维修师傅的服务区域
        BoolQueryBuilder boolQueryBuilder2 = new BoolQueryBuilder();
        boolQueryBuilder2.must(QueryBuilders.termQuery("goldMedalMasterDivisionSetType", String.valueOf(2)));
        boolQueryBuilder2.must(QueryBuilders.termQuery("serveDivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
        divisionBoolQueryBuilder.should(boolQueryBuilder2);


        List<Long> goldMedalMasterSpecialCityList = org.apache.commons.lang.StringUtils.isNotBlank(specialCityList) ? Arrays.stream(specialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();

        //3. 手动设置金牌维修师傅服务区域（街道）
        if(CollectionUtils.isNotEmpty(goldMedalMasterSpecialCityList) && goldMedalMasterSpecialCityList.contains(orderDetailData.getSecondDivisionId())){
            if(Objects.nonNull(orderDetailData.getThirdDivisionId()) && orderDetailData.getThirdDivisionId() > 0){
                BoolQueryBuilder boolQueryBuilder3 = new BoolQueryBuilder();
                boolQueryBuilder3.must(QueryBuilders.termQuery("goldMedalMasterDivisionSetType", String.valueOf(1)));
                boolQueryBuilder3.must(QueryBuilders.termQuery("goldMedalMasterFourthDivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
                divisionBoolQueryBuilder.should(boolQueryBuilder3);
            }
        }else{
            if(Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0){
                BoolQueryBuilder boolQueryBuilder3 = new BoolQueryBuilder();
                boolQueryBuilder3.must(QueryBuilders.termQuery("goldMedalMasterDivisionSetType", String.valueOf(1)));
                boolQueryBuilder3.must(QueryBuilders.termQuery("goldMedalMasterFourthDivisionIds", String.valueOf(orderDetailData.getFourthDivisionId())));
                divisionBoolQueryBuilder.should(boolQueryBuilder3);
            }
        }




        divisionBoolQueryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.filter(divisionBoolQueryBuilder);


        //服务限制
        boolQueryBuilder.mustNot(this.stringMatchQueryBuilderWithMinim(
                "masterForbiddenServeIds",
                orderDetailData.getLv3ServeIds(),
                Operator.OR,1));


        //业务线推单限制
        boolQueryBuilder.mustNot(this.stringMatchQueryBuilderWithMinim(
                "masterForbiddenBusinessIds",
                String.valueOf(1),
                Operator.OR,1));




        log.info("boolQueryBuilder:" + boolQueryBuilder.toString());
        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }
        return masterBaseSearchList;
    }



    public MatchQueryBuilder stringMatchQueryBuilderWithMinim(String fieldName, String value, Operator operator, Integer minimumShouldMatch) {
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        //设置要匹配的列。
        matchQueryBuilder.operator(operator);
        matchQueryBuilder.minimumShouldMatch(String.valueOf(minimumShouldMatch));
        return matchQueryBuilder;
    }




    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult){

    }

    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){

        try{
            if(Objects.isNull(matchMasterResult) || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())){
                return false;
            }

            Set<String> masterIdSet = matchMasterResult.getMasterIdSet();

            Long timestamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timestamp);
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(),orderVersion,masterIdSet.size(),new Date(timestamp),PushMode.GOLD_MEDAL_MASTER.code);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.GOLD_MEDAL_MASTER.code);
            commonFeature.put(FieldConstant.MATCH_SCENE_CODE, orderDetailData.getPushExtraData().getMatchSceneCode());
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
            pushControllerFacade.directPush(orderDetailData, orderVersion, matchMasterResult.getMasterIdSet(), commonFeature);

            return true;
        }catch(Exception e){
            log.error(String.format("执行金牌师傅推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;

    }



}
