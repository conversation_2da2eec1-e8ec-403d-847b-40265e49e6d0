package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouting;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRouting.*;

public interface OrderMatchRoutingService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    OrderMatchRouting detail(DetailRqt rqt);


    SimplePageInfo<OrderMatchRouting> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);


    }
