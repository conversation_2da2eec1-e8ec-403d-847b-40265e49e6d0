package com.wanshifu.master.order.push.service;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PriorityPushRule;
import com.wanshifu.master.order.push.domain.rqt.priorityPushRule.*;

/**
 * 描述 :  .
 *
 * <AUTHOR> xinze<PERSON>@wshifu.com
 * @date : 2023-02-01 17:11
 */
public interface PriorityPushRuleService {


    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    PriorityPushRule detail(DetailRqt rqt);

    SimplePageInfo<PriorityPushRule> list(ListRqt rqt);

    Integer enable(EnableRqt rqt);

    Integer delete(DeleteRqt rqt);





}