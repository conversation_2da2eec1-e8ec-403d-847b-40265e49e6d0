package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetMenuListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionListResp;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.PermissionSetService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Deprecated
public class PermissionSetServiceImpl implements PermissionSetService {

    @Resource
    private PermissionSetRepository permissionSetRepository;

    @Resource
    private PermissionRepository permissionRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private RolePermissionRepository rolePermissionRepository;

    @Resource
    private AccountRoleRepository accountRoleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(AddPermissionSetRqt rqt){
        checkPermissionSetName(rqt.getPermissionSetName(),null);
        String roleList = rqt.getRoleList().stream().map(String::valueOf).collect(Collectors.joining(","));
        int permissionSetId = permissionSetRepository.insertPermissionSet(rqt.getPermissionSetName(),rqt.getPermissionSetDesc(), JSON.toJSONString(rqt.getMenuList()),roleList,rqt.getCreateAccountId());
        rolePermissionRepository.batchInsertRolePermission(permissionSetId,rqt.getMenuList(),rqt.getRoleList());
        return 1;
    }


    private void checkPermissionSetName(String permissionSetName,Integer permissionSetId) {
        PermissionSet permissionSet = permissionSetRepository.selectByPermissionSetName(permissionSetName, permissionSetId);
        Assert.isNull(permissionSet, "权限名称重复");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UpdatePermissionSetRqt rqt){
        checkPermissionSetName(rqt.getPermissionSetName(),rqt.getPermissionSetId());
        String roleList = rqt.getRoleList().stream().map(String::valueOf).collect(Collectors.joining(","));
        permissionSetRepository.updatePermissionSet(rqt.getPermissionSetId(),rqt.getPermissionSetName(),rqt.getPermissionSetDesc(), JSON.toJSONString(rqt.getMenuList()),roleList,rqt.getCreateAccountId());
        rolePermissionRepository.deleteByPermissionSetId(rqt.getPermissionSetId());
        rolePermissionRepository.batchInsertRolePermission(rqt.getPermissionSetId(),rqt.getMenuList(),rqt.getRoleList());
        return 1;
    }



    @Override
    public SimplePageInfo<PermissionSet> list(GetPermissionSetListRqt rqt){

        List<Role> roleList = null;
        if(StringUtils.isNotBlank(rqt.getRoleName())){
            roleList = roleRepository.selectList(rqt.getRoleName(),null,null);
            if(CollectionUtils.isEmpty(roleList)){
                SimplePageInfo<PermissionSet> listRespSimplePageInfo = new SimplePageInfo<>();
                listRespSimplePageInfo.setPages(0);
                listRespSimplePageInfo.setPageNum(rqt.getPageNum());
                listRespSimplePageInfo.setTotal(0);
                listRespSimplePageInfo.setPageSize(rqt.getPageSize());
                return listRespSimplePageInfo;
            }
        }

        List<Integer> roleIdList = CollectionUtils.isNotEmpty(roleList) ? roleList.stream().map(Role::getRoleId).collect(Collectors.toList()) : null;
        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.pageSize);

        List<PermissionSet> permissionSetList = permissionSetRepository.selectList(rqt.getPermissionSetName(),rqt.getCreateTimeStart(),rqt.getCreateTimeEnd(),roleIdList);

        SimplePageInfo<PermissionSet> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(permissionSetList);
        return listRespSimplePageInfo;
    }


    @Override
    public List<Permission> menuList(GetMenuListRqt rqt){

//        List<Permission> permissionList = permissionRepository.selectList(rqt.getMenuName());
//        if(StringUtils.isNotBlank(rqt.getMenuName())){
//            List<Permission> currentPermissionList = new ArrayList<>();
//            List<Permission> allPermissionList = permissionRepository.selectAllPermission();
//            permissionList.forEach(permission -> currentPermissionList.addAll(getPermissionList(allPermissionList,permission)));
//            List<Permission> lastPermissionList = currentPermissionList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Permission::getPermissionId))), ArrayList::new));
//            return getMenuListResp(lastPermissionList);
//        }else{
//            return getMenuListResp(permissionList);
//        }


        return  permissionRepository.selectList(rqt.getMenuName());


    }


    @Override
    public PermissionSet detail(GetPermissionSetDetailRqt rqt){
        return permissionSetRepository.selectByPrimaryKey(rqt.getPermissionSetId());
    }

    @Override
    public List<Permission> permissionList(GetPermissionListRqt rqt){
        Long accountId = rqt.getAccountId();
        List<AccountRole> roleList = accountRoleRepository.selectByAccountId(accountId);
        if(CollectionUtils.isEmpty(roleList)){
            return Collections.emptyList();
        }
        List<Integer> roleIdList = roleList.stream().map(AccountRole::getRoleId).collect(Collectors.toList());

        List<RolePermission> rolePermissionList = rolePermissionRepository.selectByRoleIdList(roleIdList);

        if(CollectionUtils.isEmpty(rolePermissionList)){
            return Collections.emptyList();
        }

        List<Integer> permissionIdList = rolePermissionList.stream().map(RolePermission::getPermissionId).collect(Collectors.toList());

        List<Permission> permissionList = permissionRepository.selectByPermissionIdList(permissionIdList);

        return permissionList;

//        List<Permission> currentPermissionList = new ArrayList<>();
//        List<Permission> allPermissionList = permissionRepository.selectAllPermission();
//        permissionList.forEach(permission -> currentPermissionList.addAll(getPermissionList(allPermissionList,permission)));
//
//        List<Permission> lastPermissionList = currentPermissionList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Permission::getPermissionId))), ArrayList::new));
//
//        return getPermissionListResp(lastPermissionList);
    }


    private List<GetPermissionListResp> getPermissionListResp(List<Permission> permissionList){
        List<Permission> menuList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId() == 0)).sorted((permission1,permission2) ->{
            return permission1.getOrderNum() - permission2.getOrderNum();
        }).collect(Collectors.toList());

        List<GetPermissionListResp> getPermissionListRespList = new ArrayList<>();
        menuList.forEach(menuPermission -> getPermissionListRespList.add(getPermissionListResp(permissionList,menuPermission)));
        return getPermissionListRespList;
    }


    private GetPermissionListResp getPermissionListResp(List<Permission> permissionList,Permission menuPermission){
        GetPermissionListResp getPermissionListResp = new GetPermissionListResp();
        getPermissionListResp.setName(menuPermission.getPermissionName());
        getPermissionListResp.setPath(menuPermission.getUrl());
        List<Permission> subMenuPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(subMenuPermissionList)){
            List<GetPermissionListResp> subMenuList = new ArrayList<>();
            subMenuPermissionList.forEach(subMenuPermission -> {
                subMenuList.add(getPermissionListResp(permissionList,subMenuPermission));
            });
            getPermissionListResp.setRoutes(subMenuList);
        }else{
        }

        List<Permission> buttonPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 2 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(buttonPermissionList)){
            List<GetPermissionListResp.Button> buttonList = new ArrayList<>();
            buttonPermissionList.forEach(buttonPermssion -> buttonList.add(new GetPermissionListResp.Button(buttonPermssion.getPermissionCode(),buttonPermssion.getPermissionName())));
            getPermissionListResp.setButtonList(buttonList);
        }

        List<Permission> tabPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 3 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(tabPermissionList)){
            List<GetPermissionListResp.Tab> tabList = new ArrayList<>();
            tabPermissionList.forEach(tabPermission -> tabList.add(new GetPermissionListResp.Tab(tabPermission.getPermissionCode(),tabPermission.getPermissionName())));
            getPermissionListResp.setTabList(tabList);
        }

        return getPermissionListResp;
    }




    private List<GetMenuListResp> getMenuListResp(List<Permission> permissionList){
        List<Permission> menuList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId() == 0)).sorted((permission1,permission2) ->{
            return permission1.getOrderNum() - permission2.getOrderNum();
        }).collect(Collectors.toList());

        List<GetMenuListResp> getMenuListRespList = new ArrayList<>();
        menuList.forEach(menuPermission -> getMenuListRespList.add(getMenuListResp(permissionList,menuPermission)));
        return getMenuListRespList;
    }

    private List<Permission> getPermissionList(List<Permission> allPermissionList,Permission permission){
        List<Permission> permissionList = new ArrayList<>();
        permissionList.add(permission);
        getParentPermission(allPermissionList,permissionList,permission);
        getChildPermission(allPermissionList,permissionList,permission);
        return permissionList;
    }

    private void getParentPermission(List<Permission> allPermissionList,List<Permission> permissionList,Permission permission){
        if(permission.getParentPermissionId() != null && permission.getParentPermissionId() > 0){
            Permission parentPermission = allPermissionList.stream().filter(permissionTemp -> permissionTemp.getPermissionId().equals(permission.getParentPermissionId())).findFirst().orElse(null);
            if(parentPermission != null){
                permissionList.add(parentPermission);
                getParentPermission(allPermissionList,permissionList,parentPermission);
            }
        }
    }

    public void getChildPermission(List<Permission> allPermissionList,List<Permission> permissionList,Permission permission){
        List<Permission> childPermissionList = allPermissionList.stream().filter(permissionTemp -> permissionTemp.getParentPermissionId().equals(permission.getPermissionId())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(childPermissionList)){
            permissionList.addAll(childPermissionList);
            childPermissionList.forEach(childPermission -> getChildPermission(allPermissionList,permissionList,childPermission));
        }

    }


    private GetMenuListResp getMenuListResp(List<Permission> permissionList,Permission menuPermission){
        GetMenuListResp getMenuListResp = new GetMenuListResp();
        getMenuListResp.setMenuId(menuPermission.getPermissionId());
        getMenuListResp.setMenuName(menuPermission.getPermissionName());
        getMenuListResp.setMenuLevel(menuPermission.getPermissionLevel());
        List<Permission> subMenuPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 1 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(subMenuPermissionList)){
            List<GetMenuListResp> subMenuList = new ArrayList<>();
            subMenuPermissionList.forEach(subMenuPermission -> {
                subMenuList.add(getMenuListResp(permissionList,subMenuPermission));
            });
            getMenuListResp.setSubMenu(subMenuList);
            getMenuListResp.setIsLeaf(false);
        }else{
            getMenuListResp.setIsLeaf(true);
        }

        List<Permission> buttonPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 2 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(buttonPermissionList)){
            List<GetMenuListResp.Button> buttonList = new ArrayList<>();
            buttonPermissionList.forEach(buttonPermssion -> buttonList.add(new GetMenuListResp.Button(buttonPermssion.getPermissionId(),buttonPermssion.getPermissionName())));
            getMenuListResp.setButtonList(buttonList);
        }

        List<Permission> tabPermissionList = permissionList.stream().filter(permission -> (permission.getPermissionType() == 3 && permission.getParentPermissionId().equals(menuPermission.getPermissionId()))).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(tabPermissionList)){
            List<GetMenuListResp.Tab> tabList = new ArrayList<>();
            tabPermissionList.forEach(tabPermission -> tabList.add(new GetMenuListResp.Tab(tabPermission.getPermissionId(),tabPermission.getPermissionName())));
            getMenuListResp.setTabList(tabList);
        }

        return getMenuListResp;
    }


    @Override
    public List<Permission> allPermissionList(){
        return permissionRepository.selectAllPermission();
    }


    @Override
    @Transactional
    public  Integer delete(DeletePermissionSetRqt rqt){
        Condition condition = new Condition(PermissionSet.class);
        condition.createCriteria().andEqualTo("permissionSetId", rqt.getPermissionSetId()).andEqualTo("isDelete", 0);
        PermissionSet permissionSet = new PermissionSet();
        permissionSet.setIsDelete(1);
        permissionSet.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = permissionSetRepository.updateByConditionSelective(permissionSet, condition);
        Assert.isTrue(result > 0,"删除失败");
        rolePermissionRepository.deleteByPermissionSetId(rqt.getPermissionSetId());
        return 1;
    }







}
