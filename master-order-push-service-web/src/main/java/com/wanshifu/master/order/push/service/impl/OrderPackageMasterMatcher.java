package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.api.PackageOrderApi;
import com.wanshifu.master.order.push.domain.api.response.GetPersonaGroupIdsByUserIdResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetPersonaGroupIdsByUserIdRqt;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.OrderPackageMaster;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.orderPackage.PackageOrderOfferPriceResp;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.orderPackage.PackageOrderMasterOfferPriceRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component("order_package_master")
public class OrderPackageMasterMatcher extends AbstractOrderMasterMatcher{

//
//    @Resource
//    private PushDataCenterRepository pushDataCenterRepository;


    @Resource
    private PackageOrderApi packageOrderApi;

    @Resource
    private AddressApi addressApi;


    @Value("${night.push.switch}")
    private String nightPushSwitch;

    @Value("${night.push.start.time}")
    private String nightPushStartTime;


    @Value("${night.push.end.time}")
    private String nightPushEndTime;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushQueueService pushQueueService;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private OrderPackageBaseEsRepository orderPackageBaseEsRepository;

    @Resource
    private OrderPackageServeEsRepository orderPackageServeEsRepository;

//    @Resource
//    private MasterOrderPackageEsRespository masterOrderPackageEsRespository;
    
    @Resource
    private EnterpriseRepository enterpriseRepository;

    @Resource
    private EnterpriseLimitUserRepository enterpriseLimitUserRepository;

    @Resource
    private MasterOrderPackageAppointEsRespository masterOrderPackageAppointEsRespository;

    @Resource
    private OrderRefundRepository orderRefundRepository;

    @Resource
    private MasterRepository masterRepository;


    @Resource
    private MasterEnterpriseRepository masterEnterpriseRepository;

    @Resource
    private MasterUserRepository masterUserRepository;

    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private MasterOrderPackageRepository masterOrderPackageRepository;

    @Resource
    private OrderMatchRouteService orderMatchRouteService;


    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private HBaseClient hBaseClient;


    @Resource
    private Tools tools;


    @Value("${package.order.forbidden.user}")
    private String forbiddenUser;


    @Resource
    private NewModelMatchService newModelMatchService;

    @Resource
    private PushHandler pushHandler;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;





    private boolean nightPush(){
        if("on".equals(nightPushSwitch)){
            return true;
        }else{
            return false;
        }
    }


    @Override
    public boolean checkPreCondition(OrderDetailData orderDetailData){

        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            log.info("该城市订单不推订单包师傅");
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(org.apache.commons.lang.StringUtils.isNotBlank(pushMode) && (!PushMode.PACKAGE_ORDER.code.equals(pushMode))){
            return Boolean.FALSE;
        }

        return (!PushMode.NORMAL.code.equals(orderDetailData.getPushExtraData().getPushMode()))
                && orderDetailData.getBusinessLineId() == 1
                && CollectionUtils.isNotEmpty(orderDetailData.getLv3ServeIdList())
                && orderDetailData.getLv3ServeIdList().size() == 1
                && orderDetailData.getAppointType() != 3
                && orderDetailData.getFourthDivisionId() != null && orderDetailData.getFourthDivisionId() > 0;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){

        try{
            log.info(String.format("orderPackageMasterMatcher orderDetailData:%s,masterMatchCondition:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(masterMatchCondition)));
            Long masterOrderId = orderDetailData.getMasterOrderId();
            String accountType=orderDetailData.getAccountType();
            String orderUserId = "none";
            String orderEnterpriseId = "none";
            Long accountId = orderDetailData.getAccountId();
            if(AccountType.USER.code.equals(accountType)&&accountId!=null) {
                orderUserId=String.valueOf(accountId);
            }else if (AccountType.ENTERPRISE.code.equals(accountType)) {
                orderUserId=String.valueOf(orderDetailData.getUserId());
                orderEnterpriseId=String.valueOf(accountId);
            }


                        //用户黑名单 ***********
            if (StringUtils.isNotEmpty(forbiddenUser)) {
                final List<String> forbiddenUserList =
                        new ArrayList<>(Arrays.asList(forbiddenUser.split(",")));
                if (forbiddenUserList.contains(orderUserId)) {
                    log.info("debug::[订单包禁止用户]break package_mode::{},{},orderEnterpriseId:{},:orderUserId{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId,orderUserId);
                    return null;
                }

            }

            //总包
            if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
                //非总包直接指派确认订单包,才需要做总包开关判断
                if (!FieldConstant.CONFIRM_ORDER_PACKAGE.equals(orderDetailData.getPushExtraData().getOperateFlag())) {
                    if (!this.enterprisePackageSwitch(orderEnterpriseId)) {
                        log.info("debug::[总包订单包开关]break package_mode::{},{},orderEnterpriseId:{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId);
                        return null;
                    }
                }

                //总包指派黑名单过滤
                if (this.enterpriseUserLimit(orderEnterpriseId,orderUserId)) {
                    log.info("debug::[总包指派黑名单]break package_mode::{},{},orderEnterpriseId:{},:orderUserId{}",masterOrderId,orderDetailData.getAccountType(),orderEnterpriseId,orderUserId);
                    return null;
                }
            }


            //拆旧服务,nouse不需要拆旧,useall拆整体,usepart拆部件
            String demolishType = orderDetailData.getDemolishType();
            //是否需要寄回旧件,1:是,2:否
            final Integer isSendBackOld = orderDetailData.getIsSendBackOld();
            if (FieldConstant.USEALL.equals(demolishType)||
                    FieldConstant.USEPART.equals(demolishType)||
                    SymbolConstant.ONE.equals(String.valueOf(isSendBackOld))) {
//            logger.info("debug::break package_mode::{},{},{}",masterOrderId,demolishType,isSendBackOld);
                return null;
            }
            //加急单不走订单包-工作项:********-2023-12-11
            if (orderDetailData.getEmergencyOrderFlag()!=null&&1==orderDetailData.getEmergencyOrderFlag()) {
                return null;
            }


            final BigDecimal definiteServeFee = orderDetailData.getDefiniteServeFee();
            final MatchMasterResult matchMasterResult = this.searchOrderPackageMaster(orderDetailData,
                    masterMatchCondition,
                    orderUserId, orderEnterpriseId,
                    orderDetailData.getAccountType(), orderDetailData.getAccountId(),
                    orderDetailData.getExpectDoorInStartTimeString(),
                    orderDetailData.getExpectDoorInEndTimeString(),
                    orderDetailData.getAttributeKeys(),
                    orderDetailData.getAttributeSet(),
                    orderDetailData.getAppointType(),
                    orderDetailData.getAttributeValue(),
                    orderDetailData.getGlobalOrderId(),
                    definiteServeFee, orderDetailData.getMasterOrderId(), orderDetailData.getOrderPushExcludeMasterIds()
            );


            if(matchMasterResult != null && CollectionUtils.isNotEmpty(matchMasterResult.getMasterIdSet())){
                List<AgreementMaster> agreementMasterList = newModelMatchService.matchNewModelMaster(orderDetailData);
                if(CollectionUtils.isEmpty(agreementMasterList)){
                    return matchMasterResult;
                }
                List<String> agreementMasterIdList = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toList());
                matchMasterResult.getMasterIdSet().removeAll(agreementMasterIdList);
            }

            return matchMasterResult;
        }catch(Exception e){
            log.error(String.format("订单匹配订单包师傅失败,orderDetailData:%s",JSON.toJSONString(orderDetailData)),e);
        }
        return new MatchMasterResult(new HashSet<>());
    }


    /**
     * 总包订单包开关
     * @param enterpriseId
     * @return
     */


    private static final String RULE="order_package_rule";

    //TODO 改为查询MySQl
    public boolean enterprisePackageSwitch(String enterpriseId){

        Enterprise enterprise = enterpriseRepository.selectByPrimaryKey(enterpriseId);
        if(enterprise == null){
            return true;
        }

        final String rule = enterprise.getOrderPackageRule();

        if (rule!=null&&rule.contains("2")) {
            return false;
        }
        return true;
    }


    /**
     * 总包商家限制过滤
     * @return
     */
    //TODO 改为查询MySQL

    public boolean enterpriseUserLimit(String enterpriseId,String userId){
        List<EnterpriseLimitUser> enterpriseLimitUserList = enterpriseLimitUserRepository.selectByEnterpriseIdAndUserId(enterpriseId,userId);
        return CollectionUtils.isNotEmpty(enterpriseLimitUserList);
    }




    /**
     * 获取订单包信息
     * @return
     */
    //改为查询MySQL
    public MatchMasterResult searchOrderPackageMaster(OrderDetailData orderDetailData,
            MasterMatchCondition masterCondition,
            String orderUserId,String orderEnterpriseId,
            String accountType,Long accountId,
            String expectDoorInStartTimeString,
            String expectDoorInEndTimeString,
            String attributeKey,
            Set<String> attributeSet,
            Integer appointType,
            JSONObject AttributeValue,
            Long getGlobalOrderTraceId,
            BigDecimal definiteServeFee,Long orderId, String orderPushExcludeMasterIds) {

        MatchMasterResult matchMasterResult = new MatchMasterResult();
        final HashSet<String> packageConfigIdSet = new HashSet<>();
        final HashMap<String, PackageBase> packageBaseMap = new HashMap<>();
        getOrderPackageInfo(
                orderUserId,
                orderEnterpriseId,
                masterCondition,
                packageConfigIdSet,
                packageBaseMap,
                appointType,
                getGlobalOrderTraceId,
                definiteServeFee);
        Set<String> masterIds = new HashSet<>();
        log.info("master_order_id:{}::package_list::debug::accountId:{},result package:{}",
                masterCondition.getMasterOrderId(),accountId,packageConfigIdSet);

        if (packageConfigIdSet.size()==0) {
            return matchMasterResult;
        }
        /**
         * 子属性判断 WORKFLOW
         */
        lowAttributeFilter(masterCondition.getServeIds(),attributeKey,attributeSet,packageConfigIdSet,packageBaseMap,AttributeValue);
        log.info("master_order_id:{}::package_attr_list::accountId:{},result package:{},attributeKey:{}::AttributeValue:{}," +
                        "packageBaseMap:{}",
                masterCondition.getMasterOrderId(),accountId,packageConfigIdSet,attributeKey,AttributeValue,packageBaseMap);

        if (packageConfigIdSet.size()==0) {
            return matchMasterResult;
        }

        /**
         * 师傅列表
         */
        final HashMap<String, OrderPackageMaster> packageMasterIds
                = getOrderPackageMasterIds(masterCondition.getMasterOrderId(),masterCondition.getOrderVersion()
                ,packageBaseMap, packageConfigIdSet, masterIds,
                masterCondition.getServeIds(),
                attributeSet);
        log.info("master_order_id:{}::master_list::package::::accountId:{},masterIds:{},attributeKey{}",
                masterCondition.getMasterOrderId(),accountId,masterIds,attributeKey);
//		------------------------------------师傅过滤-----------------------------------
        //四级地址过滤
        if (masterCondition.getDivisionMatchLevel()==3&&masterIds.size() != 0) {
            levelThreeDivisionFilter(masterIds,masterCondition);
        }

        //退款限制
        if (CollectionUtils.isNotEmpty(masterIds)) {
            //TODO
            filterRefundMaster(masterCondition,orderUserId,masterIds);
        }else {
            return matchMasterResult;
        }
        log.info("{}::after 退款::package::debug::::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
        //服务中限制
        if (CollectionUtils.isNotEmpty(masterIds)) {
            reserveMasterFilter(expectDoorInStartTimeString,expectDoorInEndTimeString,masterIds);
        }
        log.info("{}::after 服务中::package::debug::::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
        if (CollectionUtils.isNotEmpty(masterIds)) {
            //拉黑过滤
            this.blackListFilter(masterIds,accountType,accountId);
        }

        //过滤休息中的师傅
        if (CollectionUtils.isNotEmpty(masterIds)) {
            filterRestMaster(masterIds,orderDetailData.getBusinessLineId());
        }

        log.info("{}::after 拉黑::package::debug::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);
        //功能限制过滤
        if (CollectionUtils.isNotEmpty(masterIds)) {
            List<String> restrictList = new ArrayList<>();
            restrictList.add(RestrictAction.NO_QUOTATION.getActionString());
            restrictList.add(RestrictAction.NO_APPOINT.getActionString());
            final Set<String> limitedMasterSet = searchRestrictMasterByIndex(masterIds, restrictList);
            if (limitedMasterSet.size()!=0) {
                masterIds.removeAll(limitedMasterSet);
            }
        }
        log.info("{}::after 功能限制::package::debug::{},{}",masterCondition.getMasterOrderId(),accountId,masterIds);


        if (CollectionUtils.isNotEmpty(masterIds)) {
            //拉黑过滤
            this.filterPushedMaster(masterIds,getGlobalOrderTraceId);
        }


        if (CollectionUtils.isNotEmpty(masterIds)) {
            //拉黑过滤
            masterIds = this.filterCooperationBusinessEndMaster(orderDetailData,masterIds);
        }

        if (CollectionUtils.isEmpty(masterIds)) {
            return matchMasterResult;
        }

        final String excludeMasterIds =
                org.apache.commons.lang.StringUtils.trimToNull(orderPushExcludeMasterIds);
        outerMasterFilter(masterIds, excludeMasterIds);
        log.info("debug::orderPackageMasterMatcher orderPushExcludeMasterIds:{}:{}:{}", orderId, masterIds, excludeMasterIds);

        if(CollectionUtils.isEmpty(masterIds)){
            return matchMasterResult;
        }

        //		------------------------------------师傅过滤-----------------------------------
        String masterId=null;
        if (CollectionUtils.isEmpty(masterIds)) {
            return matchMasterResult;
        }else if(masterIds.size()==1) {
            masterId=masterIds.iterator().next();
        }else {
            masterId= minMasterSelect(masterIds,packageMasterIds,masterCondition);
        }




        if (masterId!=null) {
            String finalMasterId = masterId;
            final OrderPackageMaster orderPackageMaster = packageMasterIds.get(finalMasterId);
            matchMasterResult.putExtraData(FieldConstant.PACKAGE_ID,orderPackageMaster.getBindPackageId());
            final String bindPackageConfigId = orderPackageMaster.getBindPackageConfigId();
            matchMasterResult.putExtraData(FieldConstant.PACKAGE_CONFIG_ID,bindPackageConfigId);
            final PackageBase packageBase = packageBaseMap.get(bindPackageConfigId);
            matchMasterResult.putExtraData(FieldConstant.PACKAGE_ATTRIBUTE_ID,packageBase.getMatchedAttributeId());
            matchMasterResult.putExtraData(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE,packageBase.getMatchedAttributeValue());
            matchMasterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
            matchMasterResult.putExtraData(FieldConstant.PLATFORM_AUTO_MODE,packageBase.getPlatformAutoMode());


            log.info("{}::{},package::finalMasterId:[{}],getBindPackageId:[{}]," +
                    "[{}]",masterCondition.getMasterOrderId(),accountId,finalMasterId,orderPackageMaster.getBindPackageId(),packageMasterIds);

            matchMasterResult.setMasterIdSet(new HashSet<String>(){{add(finalMasterId);}});
        }else {
            return matchMasterResult;
        }
        return matchMasterResult;

    }

    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }


    private void filterRestMaster(Set<String> masterSet,Integer businessLineId){

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        Set<String> restMasterSet = new HashSet<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));

            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.termQuery("restState", 0L));
            boolQueryBuilder.should(QueryBuilders.termsQuery("violationPunishLimitBusinessLineIds", Lists.newArrayList(businessLineId)));
            boolQueryBuilder.minimumShouldMatch(1);

            boolQuery.must(boolQueryBuilder);


            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                System.out.println("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                restMasterSet.addAll(masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet()));
            }

        }

        if(CollectionUtils.isEmpty(restMasterSet)){
            return ;
        }

        masterSet.removeAll(restMasterSet);

    }


    public Set<String> filterCooperationBusinessEndMaster(OrderDetailData orderDetailData,Set<String> masterSet){
        return pushHandler.filterByPushLimitRule(orderDetailData,null,masterSet,"order_package",null);
    }


    public void filterPushedMaster(Set<String> masterIdSet,Long getGlobalOrderTraceId){
        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),"order_package");
        if(StringUtils.isNotBlank(masterIdStr)){
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            masterIdSet.removeAll(pushedMasterIdSet);
        }
    }

    private void levelThreeDivisionFilter(Set<String> masterIds,MasterMatchCondition masterMatchCondition){
        final Set<String> subDivisionIdList = getSubListByDivisionId(masterMatchCondition.getThirdDivisionId());
        if (CollectionUtils.isEmpty(subDivisionIdList)) {
            log.info("{},subDivision is empty:{},lv3:{},getDivisionMatchLevel:{}"
                    ,masterMatchCondition.getMasterOrderId()
                    ,subDivisionIdList
                    ,masterMatchCondition.getThirdDivisionId()
                    ,masterMatchCondition.getDivisionMatchLevel());
            return;
        }
        log.info("{},subDivision:{},lv3:{},getDivisionMatchLevel:{}"
                ,masterMatchCondition.getMasterOrderId()
                ,subDivisionIdList
                ,masterMatchCondition.getThirdDivisionId()
                ,masterMatchCondition.getDivisionMatchLevel());



        List<Master> masterList = masterRepository.selectByMasterIdSet(masterIds);
        if(CollectionUtils.isEmpty(masterList)){
            return ;
        }
        final Map<String, Master> masterMap = masterList.stream()
                .filter(entity -> entity.getMasterId() != null)
                .collect(Collectors.toMap(Master::getMasterId, Function.identity()));

        final HashSet<String> tmpMasterSet = new HashSet<>(masterIds);
        //按3级地址扩展时,过滤[不匹配全区域]的师傅
        for (String currentMasterId : tmpMasterSet) {
            final String masterLevelFourDivision = masterMap.get(currentMasterId).getCoreFourthDivisionIds();
            final boolean fullMatch = thirdLevelDivisionFullMatch(subDivisionIdList, masterLevelFourDivision);
            if (!fullMatch) {
                masterIds.remove(currentMasterId);
            }
        }
    }


//    public String getServeFourthDivisionIds(Master master){
//        List<String> serveFourthDivisionIdList = new ArrayList<>();
//        String coreFourthDivisionIds = master.getCoreFourthDivisionId();
//        if (!org.apache.commons.lang.StringUtils.isBlank(coreFourthDivisionIds)) {
//            serveFourthDivisionIdList.add(coreFourthDivisionIds);
//        }
//
//        if (!org.apache.commons.lang.StringUtils.isBlank(master.getImportantFourthDivisionId())) {
//            serveFourthDivisionIdList.add(master.getImportantFourthDivisionId());
//        }
//
//        if (!org.apache.commons.lang.StringUtils.isBlank(master.getOtherFourthDivisionId())) {
//            serveFourthDivisionIdList.add(master.getOtherFourthDivisionId());
//        }
//
//
//        String serveFourthDivisionIds = "";
//        if(serveFourthDivisionIdList.size() > 0){
//            serveFourthDivisionIds = String.join(",", serveFourthDivisionIdList);
//        }
//
//        return serveFourthDivisionIds;
//    }


    /**
     * 用户/总包 拉黑过滤
     */
    public Set<String> blackListFilter(Set<String> result,String accountType,Long accountId){
        Set<String> blackMasterList=new HashSet<>();
        try {
            String accountIdString=String.valueOf(accountId);
            if (FieldConstant.ENTERPRISE.equals(accountType)) {
                List<MasterEnterprise> masterEnterpriseList = masterEnterpriseRepository.selectByMasterIdAndEnterpriseId(accountIdString,result);
                blackMasterList = masterEnterpriseList.stream().filter(masterUser -> isBlackListEffective(masterUser.getEnterpriseLastBlacklistTime())).map(MasterEnterprise::getMasterId).collect(Collectors.toSet());
            } else if(FieldConstant.USER.equals(accountType)) {
                List<MasterUser> masterUserList = masterUserRepository.selectByMasterIdAndUserId(accountIdString,result);
                blackMasterList = masterUserList.stream().filter(masterUser -> isBlackListEffective(masterUser.getUserLastBlacklistTime())).map(MasterUser::getMasterId).collect(Collectors.toSet());
            }
            if (blackMasterList!=null) {
                result.removeAll(blackMasterList);
            }
        } catch (Exception e) {
            log.warn("blackListFilter failed :: accountId:{},e:{}",accountId,e);
        }
        return blackMasterList;
    }


    private boolean isBlackListEffective(String time){
        if (com.wanshifu.framework.utils.StringUtils.isBlank(time) || "unspecified".equals(time)) {
            return false;
        }
        return true;
    };


    /**
     * 限制枚举
     */
    private enum RestrictAction {
        PUSH{
            @Override
            public String getActionString(){
                return "1";
            };
        },LOGIN{
            @Override
            public String getActionString(){
                return "2";
            };
        },EXCLUSIVE{
            @Override
            public String getActionString(){
                return "14";
            };
        },NO_QUOTATION{
            @Override
            public String getActionString(){
                return "3";
            };
        },NO_APPOINT{
            @Override
            public String getActionString(){
                return "4";
            };
        };
        public String getActionString(){
            return null;
        };
    };



    /**
     * 过滤功能限制师傅
     * @param RestrictAction 1：禁推单，2：禁登录，3：禁专属
     * @param orderGoodsLv1Id
     * @param inputMasterIdsSet
     * @return
     */
    private static final String MASTER_INDEX = "master_index_v1";



//改为查询MySQL
    private Set<String> searchRestrictMasterByIndex(Set<String> inputMasterIdsSet,List<String> restrictActions) {
        Set<String> masterIds=new HashSet<>();
        if (CollectionUtils.isNotEmpty(restrictActions)) {
            return masterIds;
        }

        List<Master> masterList = masterRepository.selectByMasterIdSet(inputMasterIdsSet);

        List<Master> masterResultList = masterList.stream().filter(master -> Arrays.asList(master.getRestrictAction().split(",")).stream().anyMatch(restrictActions::contains)).collect(Collectors.toList());
        masterIds = masterResultList.stream().map(Master::getMasterId).collect(Collectors.toSet());
        return masterIds;
    }




    private void getOrderPackageInfo(String orderUserId,String orderEnterpriseId,
                                     MasterMatchCondition masterCondition,
                                     HashSet<String> packageConfigIdSet,
                                     HashMap<String, PackageBase> packageBaseMap,
                                     Integer appointType,
                                     Long globalOrderId,
                                     BigDecimal definiteServeFee){

        searchOrderPackage(orderUserId,orderEnterpriseId,masterCondition,packageConfigIdSet,packageBaseMap,appointType);

        if (definiteServeFee!=null
                &&definiteServeFee.compareTo(BigDecimal.ZERO)!=0) {
            log.info("{},debug definiteServeFee before::{}",masterCondition.getMasterOrderId(),
                    packageConfigIdSet);
            checkPackageOrderPrice(definiteServeFee,globalOrderId,packageConfigIdSet);
            log.info("{},debug definiteServeFee after::{}",masterCondition.getMasterOrderId(),
                    packageConfigIdSet);
        }
    }


    /**
     * 检查订单包价格
     * @param definiteServeFee
     * @param globalOrderId
     * @param packageConfigIdSet
     */
    private void checkPackageOrderPrice(
            BigDecimal definiteServeFee,
            Long globalOrderId,
            HashSet<String> packageConfigIdSet
    ){
        if (packageConfigIdSet.size()==0) {
            return;
        }

        final HashSet<String> before = new HashSet<>();
        before.addAll(packageConfigIdSet);

        PackageOrderMasterOfferPriceRqt rqt = new PackageOrderMasterOfferPriceRqt();
        rqt.setGlobalOrderTraceId(globalOrderId);
        rqt.setPackageIds(String.join(",",packageConfigIdSet));
        final PackageOrderOfferPriceResp resp = packageOrderApi.getOfferPrice(rqt);
        Map<String, BigDecimal> orderPackagePriceMap = resp.getOfferPrice();
        //订单有价格,订单包【无价格】,【价格为0】,【大于订单价格】则过滤
        for (String packageConfigId : before) {
            final BigDecimal packagePrice = orderPackagePriceMap.get(packageConfigId);
            if (packagePrice.compareTo(BigDecimal.ZERO)==0) {
                packageConfigIdSet.remove(packageConfigId);
                log.info("{},debug::Zero{},{},{}",globalOrderId,
                        packageConfigId,packagePrice,definiteServeFee);
                continue;
            }else if (packagePrice.compareTo(definiteServeFee) > 0) {
                packageConfigIdSet.remove(packageConfigId);
                log.info("{},debug::remove{},{},{}",globalOrderId,
                        packageConfigId,packagePrice,definiteServeFee);
            }
        }


    }


    private void searchOrderPackage(String orderUserId,String orderEnterpriseId,
                                    MasterMatchCondition masterCondition,
                                    HashSet<String> packageConfigIdSet,
                                    HashMap<String, PackageBase> packageBaseMap,
                                    Integer appointType){

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        /**
         * 有效范围(白名单)
         * 耀辉:用户下给总包的订单 mq 新增一个 userId 用户字段
         */
        setOrderPackageSourceQuery(boolQueryBuilder,orderUserId,orderEnterpriseId);

        /**
         * 人群包过滤
         */
//        setOrderPackageUserGroupQuery(mustQueryList,orderUserId,orderEnterpriseId);


        /**
         * 服务地区
         */
        setOrderPackageServeDivisionQuery(boolQueryBuilder,masterCondition);
        /**
         * 进行中
         */
        setOrderPackageStatusQuery(boolQueryBuilder,masterCondition,appointType);





        List<OrderPackageBaseIndex> orderPackageBaseList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<OrderPackageBaseIndex> esResponse = orderPackageBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            System.out.println("boolQueryBuilder:"+boolQueryBuilder.toString());
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                orderPackageBaseList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }


        //TODO 分页

        System.out.println("boolQueryBuilder:" + boolQueryBuilder.toString());

        if(CollectionUtils.isNotEmpty(orderPackageBaseList)){
            orderPackageBaseList.forEach(orderPackageBaseIndex -> {
                final String packageConfigId = orderPackageBaseIndex.getPackageConfigId().toString();
                packageConfigIdSet.add(packageConfigId);
                packageBaseMap.put(packageConfigId,PackageBase.PackageBaseBuilder.aPackageBase()
                        .withPackageConfigId(packageConfigId)
                        .withMaxPushNumberDaily(orderPackageBaseIndex.getMaxPushNumberDaily())
                        .withCreateTime(orderPackageBaseIndex.getCreateTime())
                        .withPlatformAutoMode(orderPackageBaseIndex.getPlatformAutoMode())
                        .build());
            });

        }

    }

    /**
     * 获取地址子集集合
     * @return
     */
    private Set<String> getSubListByDivisionId(Long divisionId){
        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
        final Set<String> divisionList =
                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
        return divisionList;
    }
    /**
     * 三级地址全匹配
     */
    private boolean thirdLevelDivisionFullMatch(
            Set<String> orderFullFourthDivisions,
            String packageFourthLevelDivisions){
        if (StringUtils.isEmpty(packageFourthLevelDivisions)) {
            return false;
        }
        final Set<String> packageFourthDivisionSet
                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());

        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
    }


    /**
     * 订单包子属性查询
     * @param packageConfigIdSet
     * @param packageBaseMap
     */
    private void lowAttributeFilter(String serveIds,String attributeKey,Set<String> attributeSet,
                                    HashSet<String> packageConfigIdSet,
                                    HashMap<String, PackageBase> packageBaseMap,
                                    JSONObject attributeValue){
        //查询服务子属性列表
        final HashSet<String> matchedAttributePackage = packageMatch(serveIds,packageConfigIdSet);
        log.info("serveIds: " + serveIds + ",packageConfigIdSet:"+ packageConfigIdSet + ",matchedAttributePackage:" + matchedAttributePackage);

        if (attributeKey==null) {
            packageConfigIdSet.removeAll(matchedAttributePackage);
        }else {
            //订单有子属性,服务无子属性,不做属性筛选
            if (0==matchedAttributePackage.size()) {
                return;
            }
            final HashSet<String> matchedPackage = packageServeMatch(serveIds,attributeSet, packageConfigIdSet,packageBaseMap,attributeValue);
            packageConfigIdSet.retainAll(matchedPackage);
            packageConfigIdSet.forEach(packageConfigId -> packageBaseMap.get(packageConfigId).setIsMatchedAttribute(1));
        }
    }


    //	private static final String SMC_PACKAGE_SERVE_INFO = "smc_package_serve_info";
//	private static final String SMC_PACKAGE_SERVE_INFO_INDEX = "smc_package_serve_info_index";
    private static final String ORDER_PACKAGE_SERVE = "t_order_package_serve";
    private static final String ORDER_PACKAGE_SERVE_INDEX = "order_package_serve_index_v1";
    private HashSet<String> packageServeMatch(String serveIds,Set<String> attributeSet,HashSet<String> packageConfigIdSet
            ,HashMap<String, PackageBase> packageBaseMap,JSONObject attributeValue){
        HashSet<String> matchedPackage=new HashSet<>();

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("packageConfigId",
                packageConfigIdSet));
        setPackageServeQuery(serveIds,attributeSet,boolQueryBuilder,attributeValue);




        List<OrderPackageServeIndex> orderPackageServeList = new ArrayList<>();

        List<SortBuilder> sortBuilderList = new ArrayList<>();
        sortBuilderList.add(new FieldSortBuilder(SortBuilders.fieldSort("packageConfigId").order(SortOrder.ASC)));

        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<OrderPackageServeIndex> esResponse = orderPackageServeEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),sortBuilderList);
            System.out.println("boolQueryBuilder:"+boolQueryBuilder.toString());
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                orderPackageServeList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        if(CollectionUtils.isNotEmpty(orderPackageServeList)){
            final HashMap<String, HashSet<String>> packageAttributesMap = new HashMap<>();
            final HashMap<String, Map<String,String>> packageServeDataMap = new HashMap<>();

            orderPackageServeList.stream().forEach(orderPackageServe -> {
                final String packageConfigId = String.valueOf(orderPackageServe.getPackageConfigId());
                final String packageAttributesId = orderPackageServe.getAttributeKey();

                final String attributeValueMin = orderPackageServe.getAttributeValueMin();
                final String attributeValueMax = orderPackageServe.getAttributeValueMax();
                //获取订单包的子属性集合
                LocalCollectionsUtil.addHashSetValueToGenericMap(packageAttributesMap,packageConfigId,packageAttributesId);
                packageServeDataMap.put(packageServeDataMapKey(packageConfigId,packageAttributesId),new HashMap<String,String>(){
                    {
                        put(FieldConstant.ATTRIBUTE_VALUE_MIN,attributeValueMin);
                        put(FieldConstant.ATTRIBUTE_VALUE_MAX,attributeValueMax);
                    }
                });
            });


            for (Map.Entry<String, HashSet<String>> entry : packageAttributesMap.entrySet()) {
                if (entry.getValue().size()==1) {
                    final String packageConfigId = entry.getKey();
                    matchedPackage.add(packageConfigId);
                    final PackageBase packageBase = packageBaseMap.get(packageConfigId);
                    final HashSet<String> matchedAttributeSet = entry.getValue();
                    for (String matchedAttributeId : matchedAttributeSet) {
                        packageBase.setMatchedAttributeId(matchedAttributeId);
                    }
                    final Map<String, String> packageAttributeData =
                            packageServeDataMap.get(packageServeDataMapKey(packageConfigId, packageBase.getMatchedAttributeId()));
                    final JSONObject matchedAttributeValue = new JSONObject();

                    final String finalMin = packageAttributeData.get(FieldConstant.ATTRIBUTE_VALUE_MIN);
                    final String finalMax = packageAttributeData.get(FieldConstant.ATTRIBUTE_VALUE_MAX);
                    //记录订单包尺寸匹配数据
                    if (finalMin!=null&&finalMax!=null) {
                        matchedAttributeValue.put(packageBase.getMatchedAttributeId(),new JSONArray(){{
                            add(Double.valueOf(finalMin));
                            add(Double.valueOf(finalMax));
                        }});
                        packageBase.setMatchedAttributeValue(matchedAttributeValue);
                        packageBase.setMatchedAttributeSizeMin(finalMin);
                        packageBase.setMatchedAttributeSizeMax(finalMax);
                    }
                }
            }
        }

        return matchedPackage;
    }

    private String packageServeDataMapKey(String packageConfigId,String packageAttributesId){
        return packageConfigId+"_"+packageAttributesId;
    }

    /**
     * 返回包含子属性的订单包
     * @param packageConfigIdSet
     * @return
     */
    private HashSet<String> packageMatch(String serveIds,HashSet<String> packageConfigIdSet){
        HashSet<String> matchedPackage=new HashSet<>();

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        boolQueryBuilder.must(QueryBuilders.termsQuery("packageConfigId",
                packageConfigIdSet));
        boolQueryBuilder.must(QueryBuilders.termQuery("serveId",serveIds));
        log.info("boolQueryBuilder:"+boolQueryBuilder.toString());


        //列表排序
        List<SortBuilder> sortBuilderList = new ArrayList<>();
        sortBuilderList.add(new FieldSortBuilder(SortBuilders.fieldSort("packageConfigId").order(SortOrder.ASC)));

        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse esResponse = orderPackageServeEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),sortBuilderList);
            log.info("packageMatch result:"+ JSON.toJSONString(esResponse.getDataList()));
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                List<OrderPackageServeIndex> orderPackageServeIndexList = esResponse.getDataList();
                orderPackageServeIndexList.forEach(orderPackageServeIndex -> {
                    final String packageConfigId = String.valueOf(orderPackageServeIndex.getPackageConfigId());
                    final String attributeKey = orderPackageServeIndex.getAttributeKey();
                    if (attributeKey!=null&&!SymbolConstant.EMPTY_STRING.equals(attributeKey)) {
                        matchedPackage.add(packageConfigId);
                    }
                });
                pageNum++;
            }else{
                break;
            }
        }
        return matchedPackage;
    }



    private static final String ATTRIBUTE_KEY="attribute_key";
    private void setPackageServeQuery(String serveIds,Set<String> attributeSet,
                                      BoolQueryBuilder boolQueryBuilder,
                                      JSONObject attributeValue){

        BoolQueryBuilder attributeShouldQuery = new BoolQueryBuilder();
        if (attributeSet!=null) {
            for (String attributeId : attributeSet) {
                BoolQueryBuilder attributeBool = new BoolQueryBuilder();
                attributeBool.must(QueryBuilders.termQuery("attributeKey",attributeId));
                try {
                    final Double attributeValueDouble = attributeValue.getDouble(attributeId);
                    //如果有商品尺寸,则判断尺寸
                    if (attributeValueDouble!=null) {
                        this.doubleRangeBetweenQuery(
                                attributeBool,
                                attributeValueDouble,
                                FieldConstant.ATTRIBUTE_VALUE_MIN,
                                FieldConstant.ATTRIBUTE_VALUE_MAX);
                    }
                }catch (Exception e){
                    log.warn("warn - setPackageServeQuery attributeValue:{},{},{}",attributeValue,attributeSet,e);
                }
                attributeShouldQuery.should(attributeBool);
            }
        }
        attributeShouldQuery.minimumShouldMatch(1);
        boolQueryBuilder.must(attributeShouldQuery);
    }


    public void doubleRangeBetweenQuery(BoolQueryBuilder boolQueryBuilder,
                                        Double aimValue,String startField,String endField) {
        RangeQueryBuilder rangeQueryStart = new RangeQueryBuilder(startField);
        rangeQueryStart.lte(aimValue);
        boolQueryBuilder.must(rangeQueryStart);

        RangeQueryBuilder rangeQueryEnd = new RangeQueryBuilder(endField);
        rangeQueryEnd.gt(aimValue);
        boolQueryBuilder.must(rangeQueryEnd);
    }

    /** 排序
     *1.批次，创建时间
     *2.待指派订单
     *3.核心区域
     *4.随机
     */
    private String minMasterSelect(Set<String> masterIds,
                                   HashMap<String, OrderPackageMaster> packageMasterIds,
                                   MasterMatchCondition masterCondition){
        final ArrayList<String> masterSortList = new ArrayList<>(masterIds);
        /** 排序
         * 1.批次，创建时间
         * 2.待指派订单
         */
        sortPackageMaster(masterSortList,packageMasterIds);
        /**
         * 取最大值相同
         */
        final String firstMaster = masterSortList.get(0);
        final OrderPackageMaster orderPackageMaster = packageMasterIds.get(firstMaster);
        long maxPackageCreateTime = orderPackageMaster.getBindPackageCreateTime();
        int maxRemainPushOrderNumber = orderPackageMaster.getRemainPushOrderNumber();
        final ArrayList<String> moreSortMasterList = new ArrayList<>();
        for (int i = 1; i < masterSortList.size(); i++) {
            final String dMasterId = masterSortList.get(i);
            final OrderPackageMaster dMaster = packageMasterIds.get(dMasterId);
            if (dMaster.getBindPackageCreateTime()==maxPackageCreateTime
                    &&dMaster.getRemainPushOrderNumber()==maxRemainPushOrderNumber) {
                moreSortMasterList.add(dMasterId);
            }
        }
        if (moreSortMasterList.size()==0) {
            return firstMaster;
        }else {
            //核心区域
            final String thirdDivisionId = String.valueOf(masterCondition.getThirdDivisionId());
            final Map<String, String> tMasterFeature =
                    getMasterFeature(moreSortMasterList, FieldConstant.CORE_DIVISION_ID);
            final String coreMaster = getSortedPackageMasterDivision(thirdDivisionId, moreSortMasterList, tMasterFeature);
            return coreMaster;
        }
    }


    //改为查询MySQL
    private Map<String,String> getMasterFeature(List<String> masterIdList,String columnName){

        List<Master> masterList = masterRepository.selectByMasterIdSet(new HashSet<>(masterIdList));
        final HashMap<String, String> resultMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(masterList)){
            masterList.forEach(master -> resultMap.put(master.getMasterId(),master.getCoreDivisionId()));
        }
        return resultMap;
    }

    /**
     * 订单包师傅排序
     * @param masterSortList
     * @param packageMasterIds
     */
    private void sortPackageMaster(ArrayList<String> masterSortList,HashMap<String, OrderPackageMaster> packageMasterIds){
        Collections.sort(masterSortList, (o1, o2) -> {
            final OrderPackageMaster groupMaster1 = packageMasterIds.get(o1);
            final OrderPackageMaster groupMaster2 = packageMasterIds.get(o2);
            final long bindPackageCreateTime1 = groupMaster1.getBindPackageCreateTime();
            final long bindPackageCreateTime2 = groupMaster2.getBindPackageCreateTime();
            if (bindPackageCreateTime1>bindPackageCreateTime1) {
                return 1;
            }else if (bindPackageCreateTime1==bindPackageCreateTime2) {
                final int remainPushOrderNumber1 = groupMaster1.getRemainPushOrderNumber();
                final int remainPushOrderNumber2 = groupMaster2.getRemainPushOrderNumber();
                if (remainPushOrderNumber1>remainPushOrderNumber2) {
                    return -1;
                }else if (remainPushOrderNumber1==remainPushOrderNumber2) {
                    return 0;
                }else {
                    return 1;
                }
            }else {
                return -1;
            }
        });
    }

    /**
     * 订单包核心区域排序
     * @param thirdDivisionId
     * @param masterSortList
     * @param tMasterFeature
     */
    private String getSortedPackageMasterDivision(String thirdDivisionId,
                                                  ArrayList<String> masterSortList,
                                                  Map<String, String> tMasterFeature){
        final ArrayList<String> coreMasterList = new ArrayList<>();
        for (String master : masterSortList) {
            final String coreDivisionId = tMasterFeature.get(master);
            if (thirdDivisionId.equals(coreDivisionId)) {
                coreMasterList.add(master);
            }
        }

        if (coreMasterList.size()>1) {
            Collections.shuffle(coreMasterList);
            return coreMasterList.get(0);
        }else if (coreMasterList.size()==1) {
            return coreMasterList.get(0);
        }else {
            Collections.shuffle(masterSortList);
            return masterSortList.get(0);
        }

    }


    /**
     *预约冲突过滤
     * @return
     */
    //TODO 改为查询MySQL
    private Set<String> reserveMasterFilter(String startTime,String endTime,Set<String> masterIds){
//        if (startTime==null||endTime==null) {
//            return masterIds;
//        }
//        Long startTimestamp= DateFormatterUtil.timeToTimestamp(startTime);
//        Long endTimestamp=DateFormatterUtil.timeToTimestamp(endTime);
//        List<OrderServeInfo> orderServeInfoList = E.selectByReserveTime(masterIds,startTimestamp,endTimestamp);
//        return orderServeInfoList.stream().map(OrderServeInfo::getMasterId).collect(Collectors.toSet());
        return masterIds;
    }


    /**
     * 【推单组】订单包支持配置到商品属性级别
     * 1.订单数量到子属性级别
     * 3.配置到子属性的订单包只接收配置到子属性级别的订单->筛选订单包时 TODO ↑能不能合并?
     */
    private HashMap<String, OrderPackageMaster> getOrderPackageMasterIds(Long masterOrderId, String orderVersion,
                                                                         HashMap<String, PackageBase> packageBaseMap,
                                                                         Set<String> packageSet, Set<String> masterIds,
                                                                         String serveIds,
                                                                         Set<String> attributeSet) {

        HashMap<String, OrderPackageMaster> orderPackageMasterMap=new HashMap<>();
//        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.must(QueryBuilders.termsQuery("packageConfigId",
//                packageSet));
//        setOrderPackageMasterQuery(boolQueryBuilder);
//
//        EsResponse<MasterOrderPackageIndex> esResponse = masterOrderPackageEsRespository.search(boolQueryBuilder);


        List<MasterOrderPackageIndex> masterOrderPackageIndexList = masterOrderPackageRepository.selectByPackageConfigIds(packageSet);

        if(CollectionUtils.isNotEmpty(masterOrderPackageIndexList)){
            for(MasterOrderPackageIndex masterOrderPackageIndex : masterOrderPackageIndexList){
                final String packageConfigId = masterOrderPackageIndex.getPackageConfigId();
                final String masterId = masterOrderPackageIndex.getMasterId();
                final PackageBase packageBase = packageBaseMap.get(packageConfigId);
                /**
                 * 每日推送上限
                 */
                final Long maxPushNumberDaily = packageBase.getMaxPushNumberDaily();
                //TODO
                Long orderNum=getPackageOrderDailyNum(masterOrderPackageIndex,orderVersion);
                final Long pushPackageOrderNumToday = (orderNum==null?0:orderNum);
                if (maxPushNumberDaily!=null&&pushPackageOrderNumToday>=maxPushNumberDaily) {
                    continue;
                }

                masterIds.add(masterId);
                final Long createTime = packageBase.getCreateTime();
                if (orderPackageMasterMap.containsKey(masterId)) {
                    final OrderPackageMaster orderPackageMaster = orderPackageMasterMap.get(masterId);
                    final Long bindPackageCreateTime = orderPackageMaster.getBindPackageCreateTime();
                    if (bindPackageCreateTime>createTime) {
                        orderPackageMaster.setBindPackageConfigId(packageConfigId);
                        orderPackageMaster.setBindPackageCreateTime(createTime);
                        orderPackageMaster.setBindPackageId(masterOrderPackageIndex.getPackageId());
                    }
                }else {
                    orderPackageMasterMap
                            .put(masterId, OrderPackageMaster.GroupMasterBuilder.aGroupMaster()
                                    .withMasterId(masterId)
                                    .withRemainPushOrderNumber(
                                            Integer.valueOf(masterOrderPackageIndex.getRemainPushOrderNumber())
                                    )
                                    .withBindPackageId(masterOrderPackageIndex.getPackageId())
                                    .withBindPackageConfigId(packageConfigId)
                                    .withBindPackageCreateTime(createTime)
                                    .build());
                }
            }
        }


        /**
         * 订单包订单指派数量限制 WORKFLOW
         */
        log.info("master_order_id:{}::before appoint limit:{}",masterOrderId,masterIds);
        if (masterIds.size()!=0) {
            appointOrderLimit(serveIds,attributeSet,masterIds,packageSet,orderPackageMasterMap,packageBaseMap);
            log.info("master_order_id:{}::after appoint limit:{}",masterOrderId,masterIds);
        }
        return orderPackageMasterMap;
    }

    /**
     * 指派上限过滤
     */
    private void appointOrderLimit(String serveIds, Set<String> attributeSet, Set<String> masterIds,
                                   Set<String> packageSet, HashMap<String, OrderPackageMaster> orderPackageMasterMap, HashMap<String, PackageBase> packageBaseMap){
        final HashMap<String, HashSet<String>> packageKeyMaster = new HashMap<>();
        //获取订单包与师傅的对应关系
        for (Map.Entry<String, OrderPackageMaster> row : orderPackageMasterMap.entrySet()) {
            String masterId=row.getKey();
            final String bindPackageConfigId = row.getValue().getBindPackageConfigId();
            LocalCollectionsUtil.addHashSetValueToGenericMap(packageKeyMaster,bindPackageConfigId,masterId);
        }
        final HashSet<String>  availableMasterIds= new HashSet<>();
        for (Map.Entry<String, HashSet<String>> entry : packageKeyMaster.entrySet()) {
            final String bindPackageConfigId = entry.getKey();
            final HashSet<String> masterIdSet = entry.getValue();
            final PackageBase packageBase = packageBaseMap.get(bindPackageConfigId);
            availableMasterIds.addAll(appointOrderLimitOTS(serveIds, attributeSet, masterIdSet,bindPackageConfigId,packageBase));
        }
        masterIds.retainAll(availableMasterIds);
    }

    /**
     * 查出来可以推的
     * @param serveIds
     * @param attributeSet
     * @param boolQueryBuilder
     */
    private void setAppointOrderQuery(PackageBase packageBase,String serveIds,Set<String> attributeSet,BoolQueryBuilder boolQueryBuilder){
        //指派数量过滤
        boolQueryBuilder.must(QueryBuilders.termQuery("serveId",
                serveIds));
        if (packageBase.getIsMatchedAttribute() == 1 && attributeSet!=null&&attributeSet.size()>0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("attributeKey",
                    attributeSet));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("isDelete",
                "0"));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("remainAppointOrderNumber").gt(0L));
        //尺寸
        final String matchedAttributeSizeMin = packageBase.getMatchedAttributeSizeMin();
        final String matchedAttributeSizeMax = packageBase.getMatchedAttributeSizeMax();
        if (matchedAttributeSizeMin!=null&&matchedAttributeSizeMax!=null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("attributeValueMin",
                    matchedAttributeSizeMin));
            boolQueryBuilder.must(QueryBuilders.termQuery("attributeValueMax",
                    matchedAttributeSizeMax));
        }
    }

    //	public static final String SMC_PACKAGE_REMAIN_APPOINT = "smc_package_remain_appoint";
//	public static final String SMC_PACKAGE_REMAIN_APPOINT_INDEX = "smc_package_remain_appoint_index";
    public static final String MASTER_ORDER_PACKAGE_APPOINT = "t_master_order_package_appoint";
    public static final String MASTER_ORDER_PACKAGE_APPOINT_INDEX = "master_order_package_appoint_index_v1";
    private Set<String> appointOrderLimitOTS(String serveIds,Set<String> attributeSet,HashSet<String> masterIds,
                                             String packageConfigId,PackageBase packageBase){
        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
        boolQuery.must(QueryBuilders.termsQuery("packageConfigId",
                packageConfigId));
        boolQuery.must(QueryBuilders.termsQuery("masterId",
                masterIds));
        setAppointOrderQuery(packageBase,serveIds, attributeSet,boolQuery);


        EsResponse<MasterOrderPackageAppoint> esResponse = masterOrderPackageAppointEsRespository.search(boolQuery);
        Set<String> availableMasterIds = new HashSet<>();

        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            availableMasterIds = esResponse.getDataList().stream().map(MasterOrderPackageAppoint::getMasterId).collect(Collectors.toSet());
        }

        log.info("appointOrderLimitOTS debug::availableMasterIds{}::packageConfigId:{}" +
                        ",masterIds:{},serveIds:{},attributeSet:{},packageBase:{}",availableMasterIds,
                packageConfigId,masterIds,serveIds,attributeSet,packageBase);


        return availableMasterIds;
    }



    private Long getPackageOrderDailyNum(MasterOrderPackageIndex masterOrderPackageIndex,String orderVersion){
        Long orderNum = masterOrderPackageIndex.getMasterPackageOrderPushNumberDaily();
        try {
            final String pushCountTag = masterOrderPackageIndex.getPushCountTag();
            final String currentDate = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));
            if (!currentDate.equals(pushCountTag)) {
                orderNum=0L;
            }
        }catch (Exception e){
            log.error("getPackageOrderDailyNum error");
            orderNum=0L;
        }
        return orderNum;
    }

    //改为查询MySQL
    private Set<String> filterRefundMaster(
            MasterMatchCondition masterCondition,
            String orderUserId,
            Set<String> masterIds) {

        Set<String> removedIds=new HashSet<>();
        List<OrderRefund> orderRefundList = orderRefundRepository.selectByUserIdAndMasterId(Long.valueOf(orderUserId),masterIds,masterCondition.getServeType());
        if(CollectionUtils.isNotEmpty(orderRefundList)){
            removedIds = orderRefundList.stream().map(OrderRefund::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        }
        return removedIds;
    }

////改为查询MySQL
//    private static final String REFUND_TIME="refund_time";
//    private void setOrderRefundQuery(MasterMatchCondition masterCondition,String orderUserId,List<Query> mustQueryList){
//        //用户过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.USER_ID,
//                orderUserId));
//        //服务过滤
//        mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.ORDER_SERVE_TYPE,
//                String.valueOf(masterCondition.getServeType())));
//        //时间过滤
//        mustQueryList.add(tableStoreClient.longCompareQuery(
//                REFUND_TIME,
//                RangeQueryType.GREATER_THAN,
////				DateFormatterUtil.getAssignDayTimeStampMils(-2)
//                DateFormatterUtil.getAssignMinutesTimeStampMils(-120)
//        ));
//    }





    /*
     */
    private static final String HAS_PAUSE_PUSH="has_pause_push";
    private static final String REMAIN_PUSH_ORDER_NUMBER ="remain_push_order_number";
    private static final String REMAIN_APPOINT_ORDER_NUMBER ="remain_appoint_order_number";
    private static final String STATEMENT_STATUS="statement_status";
    private static final String HAS_RECYCLE="has_recycle";
    private void setOrderPackageMasterQuery(BoolQueryBuilder boolQueryBuilder){

        boolQueryBuilder.must(QueryBuilders.rangeQuery("remainAppointOrderNumber").gt(0L));
        boolQueryBuilder.must(QueryBuilders.termQuery("statementStatus","pre"));
        boolQueryBuilder.must(QueryBuilders.termQuery("hasPausePush","0"));
        boolQueryBuilder.must(QueryBuilders.termQuery("hasRecycle","0"));
    }


    /**
     * account_type-user查询
     * - platform_special_user_flag
     * - platform_special_user_ids
     * account_type-enterprise查询
     * - enterprise_special_user_flag
     * - enterprise_special_user_ids
     * - special_enterprise_ids

     */

    private void setOrderPackageSourceQuery(BoolQueryBuilder boolQueryBuilder
            , String orderUserId, String orderEnterpriseId){
        if (FieldConstant.NONE.equals(orderEnterpriseId)) {
            //用户订单
            BoolQueryBuilder userFlagQuery = new BoolQueryBuilder();
            userFlagQuery.should(QueryBuilders.termQuery("platformSpecialUserFlag", "0"));
            userFlagQuery.should(QueryBuilders.termQuery("platformSpecialUserIds", orderUserId));

            //TODO
            GetPersonaGroupIdsByUserIdRqt rqt = new GetPersonaGroupIdsByUserIdRqt();
            rqt.setUserId(Long.valueOf(orderUserId));
            rqt.setAppId(5);
            GetPersonaGroupIdsByUserIdResp resp = tools.catchLogThrow(() -> bigdataOpenServiceApi.getPersonaGroupIdsByUserId(rqt));

            //匹配订单包绑定人群 TODO
            if(Objects.nonNull(resp) && StringUtils.isNotBlank(resp.getGroupIds())){
                String groupIds = resp.getGroupIds();
                userFlagQuery.should(QueryBuilders.termsQuery("platformSpecialUserGroupIds",Arrays.asList(groupIds.split(","))));
            }
            userFlagQuery.minimumShouldMatch(1);


            BoolQueryBuilder userBool = new BoolQueryBuilder();
            userBool.must(QueryBuilders.termQuery("source","platform"));
            userBool.must(userFlagQuery);
            boolQueryBuilder.must(userBool);


        }else {

            //总包订单
            BoolQueryBuilder userBool = new BoolQueryBuilder();
            userBool.should(QueryBuilders.termQuery("enterpriseSpecialUserFlag","0"));
            userBool.should(QueryBuilders.termQuery("enterpriseSpecialUserIds", orderUserId));
            userBool.minimumShouldMatch(1);

            BoolQueryBuilder enterpriseBool = new BoolQueryBuilder();
            enterpriseBool.should(QueryBuilders.termQuery("source","enterprise"));
            enterpriseBool.should(QueryBuilders.termQuery("specialEnterpriseIds",orderEnterpriseId));
            enterpriseBool.should(userBool);

            boolQueryBuilder.must(enterpriseBool);
        }
    }

    /**
     * 人群包过滤
     * @param mustQueryList
     * @param orderUserId
     * @param orderEnterpriseId
     */
//    private void setOrderPackageUserGroupQuery(List<Query> mustQueryList
//            ,String orderUserId,String orderEnterpriseId) {
//        //用户人群
//        final String groupIds = pushDataCenterRepository.getUserBaseFeature(
//                Long.valueOf(orderUserId), "group_ids");
//        //匹配订单包绑定人群 TODO
//        mustQueryList.add(tableStoreClient.stringTermsQuery("TODO",groupIds));
//    }

    /**
     * 服务,地区 过滤
     * @param masterCondition
     */
    private static final String SERVE_IDS="serve_ids";
    private static final String LV3_DIVISION_IDS="lv3_division_ids";
    private static final String LV4_DIVISION_IDS="lv4_division_ids";
    private void setOrderPackageServeDivisionQuery(BoolQueryBuilder boolQueryBuilder,MasterMatchCondition masterCondition) {
        /**
         * 服务表结构改造,不再支持分词索引同步
         */
        boolQueryBuilder.must(QueryBuilders.termQuery("serveIds",
                masterCondition.getServeIds()));
//--------------------------------------------

        /**
         * 		mustQueryList.add(OTSUtil.stringMatchQuery("serve_ids",
         * 		masterCondition.getServeIds(), QueryOperator.AND));
         */
        //由于门,定制/存在特殊处理,且订单包不做区域特殊处理,需再次判断区域处理等级
        if (masterCondition.getFourthDivisionId()!=null
                &&!SymbolConstant.ZERO.equals(String.valueOf(masterCondition.getFourthDivisionId()))) {
            //lv 4
            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds",
                    String.valueOf(masterCondition.getFourthDivisionId())));
            masterCondition.setDivisionMatchLevel(4);
        }else{
            //lv 3
            boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds",
                    String.valueOf(masterCondition.getThirdDivisionId())));
            masterCondition.setDivisionMatchLevel(3);
        }
    }



    /**
     * 进行中 过滤
     * 开始推单时间
     * 待指派未满
     * 结算
     * 订单包未被回收或暂停
     * 是否可用
     * 审核
     * 结算时间
     * @param masterCondition
     */
    private static final String AUDIT_STATUS="audit_status";
    private static final String ENABLE_STATUS="enable_status";
    private static final String ORDER_PUSH_START_TIME="order_push_start_time";
    private static final String SETTLEMENT_TIME="settlement_time";
    private static final String APPOINT_TYPE="appoint_type";
    //TODO 指派类型 合作价格
    private void setOrderPackageStatusQuery(BoolQueryBuilder boolQueryBuilder,MasterMatchCondition masterCondition,Integer appointType) {
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("orderPushStartTime");
        rangeQueryBuilder.lte(DateFormatterUtil.getNowTimeStampMil());
        boolQueryBuilder.must(rangeQueryBuilder);
        boolQueryBuilder.must(QueryBuilders.termQuery("auditStatus","pass"));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType",String.valueOf(appointType)));
//		mustQueryList.add(OTSUtil.stringTermQuery(ENABLE_STATUS,"1"));

        RangeQueryBuilder settlementTimeueryBuilder = QueryBuilders.rangeQuery("settlementTime");
        settlementTimeueryBuilder.gte(DateFormatterUtil.getNowTimeStampMil());
        boolQueryBuilder.must(settlementTimeueryBuilder);
    }

    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){




    }


    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        try {
            if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
                return false;
            }
            // 订单版本号
            String orderVersion = orderDetailData.getOrderVersion();
            Long timeStamp = Long.parseLong(orderVersion);
            String operateFlag = orderDetailData.getPushExtraData().getOperateFlag();
            String pushType = FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag) ? "order_package_match" : "order_package_push";
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), pushType);
            //TODO 夜间推送
            if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {

                if(FieldConstant.CONFIRM_ORDER_PACKAGE.equals(operateFlag)){
                    //TODO 总包直接指派匹配订单包
//                    pushQueueService.sendDelayMatchOrderPackageMasterMesasge(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
                    return false;
                }else{
                    OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
                    rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
                    rqt.setBusinessLineId(orderDetailData.getBusinessLineId());
                    rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
                    rqt.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
                    pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
                }
                return true;
            }

            // 获取智能推单订单全局时间标记 yyyy-MM-dd HH:mm:ss
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.OPERATE_FLAG, orderDetailData.getPushExtraData().getOperateFlag());
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            //TODO 判断地址级别
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
            commonFeature.put(FieldConstant.PUSH_MODE, PushMode.PACKAGE_ORDER.code);
            commonFeature.put(FieldConstant.PACKAGE_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_ID));
            commonFeature.put(FieldConstant.PACKAGE_CONFIG_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_CONFIG_ID));
            commonFeature.put(FieldConstant.PACKAGE_ATTRIBUTE_ID, matchMasterResult.getExtraData().getString(FieldConstant.PACKAGE_ATTRIBUTE_ID));
            commonFeature.put(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE, matchMasterResult.getExtraData().getString(FieldConstant.ORDER_PACKAGE_ATTRIBUTE_RANGE));
            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
            pushControllerFacade.packageOrderPush(orderDetailData, orderVersion, timeMark, matchMasterResult.getMasterIdSet(), commonFeature);


            Long platformAutoMode = matchMasterResult.getExtraData().getLongValue(FieldConstant.PLATFORM_AUTO_MODE);
            if(AppointType.OPEN.value.equals(orderDetailData.getAppointType()) && Objects.nonNull(platformAutoMode) && platformAutoMode == 1){
                OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
                rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
                rqt.setBusinessLineId(orderDetailData.getBusinessLineId());
                rqt.setPushMode(PushMode.NORMAL.code);
                rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
                rqt.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
                pushQueueService.sendDelayPushMessage(1000L,JSON.toJSONString(rqt));
            }else{
                if(!AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())){
                    OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
                    orderMatchMasterRqt.setMasterOrderId(orderDetailData.getMasterOrderId());
                    orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
                    orderDetailData.getPushExtraData().setMatchSceneCode(MatchSceneCode.INTERFERE_ORDER_PUSH.getCode());
                    MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
                    OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
                    orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
                }
            }

//            orderMatchRouteService.orderMatchRoute("order_package",orderDetailData.getMasterOrderId(),orderDetailData.getBusinessLineId(),orderDetailData.getOrderCategoryId(),orderDetailData.getAppointType());
            return true;
        }catch(Exception e){
            log.error(String.format("执行代理商推单失败,orderDetailData:%s,matchMasterResult:%s",JSON.toJSONString(orderDetailData),JSON.toJSONString(matchMasterResult)),e);
        }
        return false;
    }

    public Long getSecondDayTimestamp(){
        Calendar calendar = Calendar.getInstance();
        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
            calendar.add(Calendar.DATE,1);
        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
        }
        calendar.set(Calendar.HOUR_OF_DAY,8);

        Random random = new Random();
        int minute = random.nextInt(5);
        calendar.set(Calendar.MINUTE,minute);

        return calendar.getTimeInMillis();
    }



}
