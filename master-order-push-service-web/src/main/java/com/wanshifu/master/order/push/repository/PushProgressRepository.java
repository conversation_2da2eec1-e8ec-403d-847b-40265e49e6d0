package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.enums.PushStatus;
import com.wanshifu.master.order.push.domain.po.PushProgress;
import com.wanshifu.master.order.push.mapper.PushProgressMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 推送进度Repository
 * <AUTHOR>
 */
@Repository
public class PushProgressRepository extends BaseRepository<PushProgress> {

    @Resource
    private PushProgressMapper pushProgressMapper;


    public String getOrderLatestVersion(Long orderId){
        return pushProgressMapper.getOrderLatestVersion(orderId);
    }


    public int updatePushProgress(Long orderId, String orderVersion,
                                           PushProgress pushProgress) {
        PushProgress updatePushProgress = new PushProgress();
        updatePushProgress.setNewMasterOffset(pushProgress.getNewMasterOffset());
        updatePushProgress.setOldMasterOffset(pushProgress.getOldMasterOffset());
        updatePushProgress.setListLength(pushProgress.getListLength());
        updatePushProgress.setFilteredMasterNum(pushProgress.getFilteredMasterNum());
        updatePushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum());
        updatePushProgress.setPushedRound(pushProgress.getPushedRound());
        updatePushProgress.setMasterOffset(pushProgress.getMasterOffset());

        Condition condition = new Condition(PushProgress.class);
        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("orderVersion",orderVersion);
        return this.updateByConditionSelective(updatePushProgress,condition);
    }




    public PushProgress getPushProgress(Long orderId,String orderVersion) {
        PushProgress pushProgress = new PushProgress();
        pushProgress.setOrderId(orderId);
        pushProgress.setOrderVersion(orderVersion);
        return CollectionUtils.getFirstSafety(this.select(pushProgress));
    }

    public PushProgress getFirstTimeValidPushProgress(Long orderId) {
        Condition condition = new Condition(PushProgress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andGreaterThan("baseSelectMasterNum", 0)
                .andNotEqualTo("currentStopReason", "ENABLE_ALTERNATIVE_STRATEGY");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }

    public PushProgress getNormalFirstTimeValidPushProgress(Long orderId) {
        Condition condition = new Condition(PushProgress.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("orderId", orderId)
                .andEqualTo("pushType", "normal_push")
                .andGreaterThan("baseSelectMasterNum", 0)
                .andNotEqualTo("currentStopReason", "ENABLE_ALTERNATIVE_STRATEGY");
        return CollectionUtils.getFirstSafety(this.selectByCondition(condition));
    }




    public int updatePushProgressWithStatus(String orderVersion, Long orderId,
                                                     PushProgress pushProgress) {
        PushProgress updatePushProgress = new PushProgress();
        updatePushProgress.setNewMasterOffset(pushProgress.getNewMasterOffset());
        updatePushProgress.setOldMasterOffset(pushProgress.getOldMasterOffset());
        updatePushProgress.setFilteredMasterNum(pushProgress.getFilteredMasterNum());
        updatePushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum());
        updatePushProgress.setPushedRound(pushProgress.getPushedRound());
        updatePushProgress.setCurrentStopReason(pushProgress.getCurrentStopReason());
        updatePushProgress.setPushStatus(pushProgress.getPushStatus());

        Condition condition = new Condition(PushProgress.class);
        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("orderVersion",orderVersion);
        return this.updateByConditionSelective(updatePushProgress,condition);

    }


    public int updatePushOverMessage(Long globalOrderId,String orderVersion, String pushStatus,String stopReason) {
        PushProgress updatePushProgress = new PushProgress();
        updatePushProgress.setPushStatus(pushStatus);
        updatePushProgress.setCurrentStopReason(stopReason);

        Condition condition = new Condition(PushProgress.class);
        condition.createCriteria().andEqualTo("orderId", globalOrderId).andEqualTo("orderVersion",orderVersion);
        return this.updateByConditionSelective(updatePushProgress,condition);

    }

    public int updatePushProgress( String orderVersion, int directPushNum, Long orderId,PushProgress pushProgress) {
        PushProgress updatePushProgress = new PushProgress();
        updatePushProgress.setNewMasterOffset(pushProgress.getNewMasterOffset());
        updatePushProgress.setOldMasterOffset(pushProgress.getOldMasterOffset());
        updatePushProgress.setListLength(pushProgress.getListLength());
        updatePushProgress.setFilteredMasterNum(pushProgress.getFilteredMasterNum());
        updatePushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum());
        updatePushProgress.setPushedRound(pushProgress.getPushedRound());
        updatePushProgress.setDirectPushNum(directPushNum);

        Condition condition = new Condition(PushProgress.class);
        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("orderVersion",orderVersion);
        return this.updateByConditionSelective(updatePushProgress,condition);
    }


    public int insertBasePushZeroProgress(Long globalOrderId, String orderVersion,Date firstPushTime,String pushType){
        PushProgress pushProgress = new PushProgress();
        pushProgress.setOrderId(globalOrderId);
        pushProgress.setNewMasterOffset(0);
        pushProgress.setOldMasterOffset(0);
        pushProgress.setBaseSelectMasterNum(0);
        pushProgress.setListLength(0);
        pushProgress.setFilteredMasterNum(0);
        pushProgress.setPushedMasterNum(0);
        pushProgress.setPushedRound(0);
        pushProgress.setCurrentStopReason("BASE_SELECT_ZERO");
        pushProgress.setPushStatus(PushStatus.STOP.code);
        pushProgress.setOrderVersion(orderVersion);
        pushProgress.setDirectPushNum(0);
        pushProgress.setFirstPushTime(firstPushTime);
        pushProgress.setPushType(pushType);
        pushProgress.setCreateTime(new Date());
        pushProgress.setUpdateTime(new Date());
        return this.pushProgressMapper.insertSelective(pushProgress);
    }


    public int insertBasePushZeroProgress(Long globalOrderId, String orderVersion,Date firstPushTime,String pushType,String currentStopReason){
        PushProgress pushProgress = new PushProgress();
        pushProgress.setOrderId(globalOrderId);
        pushProgress.setNewMasterOffset(0);
        pushProgress.setOldMasterOffset(0);
        pushProgress.setBaseSelectMasterNum(0);
        pushProgress.setListLength(0);
        pushProgress.setFilteredMasterNum(0);
        pushProgress.setPushedMasterNum(0);
        pushProgress.setPushedRound(0);
        pushProgress.setCurrentStopReason(currentStopReason);
        pushProgress.setPushStatus(PushStatus.STOP.code);
        pushProgress.setOrderVersion(orderVersion);
        pushProgress.setDirectPushNum(0);
        pushProgress.setFirstPushTime(firstPushTime);
        pushProgress.setPushType(pushType);
        pushProgress.setCreateTime(new Date());
        pushProgress.setUpdateTime(new Date());
        return this.pushProgressMapper.insertSelective(pushProgress);
    }


    public int insertBasePushProgress(Long orderId,String orderVersion, int baseSelectNum,Date firstPushTime,String pushMode) {
        PushProgress pushProgress = new PushProgress();
        pushProgress.setOrderId(orderId);
        pushProgress.setNewMasterOffset(0);
        pushProgress.setOldMasterOffset(0);
        pushProgress.setBaseSelectMasterNum(baseSelectNum);
        pushProgress.setListLength(0);
        pushProgress.setFilteredMasterNum(0);
        pushProgress.setPushedMasterNum(0);
        pushProgress.setPushedRound(0);
        pushProgress.setPushStatus(PushStatus.PUSHING.code);
        pushProgress.setOrderVersion(orderVersion);
        pushProgress.setDirectPushNum(0);
        pushProgress.setFirstPushTime(firstPushTime);
        pushProgress.setPushType(pushMode);
        pushProgress.setCreateTime(new Date());
        pushProgress.setUpdateTime(new Date());
        return pushProgressMapper.insertSelective(pushProgress);
    }


    public PushProgress getOrderLatestPushProgress(Long orderId){
        return this.pushProgressMapper.getOrderLatestPushProgress(orderId);
    }


    public Integer countByPushType(@Param("orderId") Long orderId,@Param("pushType") String pushType){
        return this.pushProgressMapper.countByPushType(orderId,pushType);
    }



}