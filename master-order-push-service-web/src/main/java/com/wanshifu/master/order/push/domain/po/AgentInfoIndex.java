package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.annotation.Field;
import com.wanshifu.master.order.push.domain.annotation.FieldType;
import lombok.Data;

@Data
@Document(indexAlias = "agent_info", type = "agent_info")
public class AgentInfoIndex {

    /**
     * 合作商id
     */
    @Field(type = FieldType.Long)
    private Long agentId;

    @Field(type = FieldType.Text)
    private String operateTime;

    /**
     * 合作商名称
     */
    @Field(type = FieldType.Text)
    private String agentName;

    /**
     * 合作区域id集合
     */
    @Field(type = FieldType.Text)
    private String divisionId;

    /**
     * 合作服务id集合
     */
    @Field(type = FieldType.Text)
    private String serveIds;

    /**
     * 合作商城市id
     */
    @Field(type = FieldType.Long)
    private Long cityDivisionId;

    /**
     * 合作商开启状态
     */
    @Field(type = FieldType.Integer)
    private Integer useStatus;

}
