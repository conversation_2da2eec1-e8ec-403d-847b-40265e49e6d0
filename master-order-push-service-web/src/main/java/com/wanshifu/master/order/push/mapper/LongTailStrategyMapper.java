package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 策略Mapper
 * <AUTHOR>
 */
public interface LongTailStrategyMapper extends IBaseCommMapper<LongTailStrategy> {

    /**
     * 获取列表
     * @param longTailStrategyName
     * @param isActive
     * @param createStartTime
     * @param createEndTime
     * @return
     */
    public List<LongTailStrategy> selectList(
            @Param("longTailStrategyIdList") List<Long> longTailStrategyIdList,
            @Param("businessLineId") Long businessLineId,
            @Param("longTailStrategyName")String longTailStrategyName,
            @Param("isActive")Integer isActive,
            @Param("createStartTime") Date createStartTime,
            @Param("createEndTime") Date createEndTime);
}