package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.enums.MatchSceneCode;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.enums.PushType;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.OrderPushService;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 策略推单
 * <AUTHOR>
 */
@Component("normal")
@Slf4j
public class StrategyMasterMatcher extends AbstractOrderMasterMatcher{


    @Resource
    private OrderPushService orderPushService;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;

    @Resource
    private PushProgressRepository pushProgressRepository;



    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){


        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            log.info("该城市订单不推单");
            pushProgressRepository.insertBasePushZeroProgress(orderDetailData.getGlobalOrderId(),orderDetailData.getOrderVersion(),new Date(),"normal_push","该城市订单不推单");
            return false;
        }


//        String pushMode = orderDetailData.getPushExtraData().getPushMode();
//        if(org.apache.commons.lang.StringUtils.isNotBlank(pushMode) && (!PushMode.NORMAL.code.equals(pushMode))){
//            return Boolean.FALSE;
//        }

        return true;
    }


    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){
        orderPushService.push(orderDetailData,masterCondition);
        return null;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){

        if(AccountType.USER.code.equals(orderDetailData.getAccountType()) &&
                OrderFrom.SITE.valueEn.equals(orderDetailData.getOrderFrom()) &&
                AppointType.OPEN.value.equals(orderDetailData.getAppointType()) &&
                "5".equals(orderDetailData.getLv1ServeIds()) &&
                MatchSceneCode.ORDER_CREATE.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode()) &&
                CollectionUtils.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("special_repair_attribute")){
            //下单场景，订单特殊维修属性标签，额外推送金牌维修师傅
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.GOLD_MEDAL_MASTER);
            orderMasterMatcher.executeMatch(orderDetailData,masterCondition);
        }

        OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.TECHNIQUE_VERIFY_MASTER);
        orderDetailData.getPushExtraData().setPushMode(PushMode.TECHNIQUE_VERIFY_MASTER.code);
        orderMasterMatcher.executeMatch(orderDetailData,masterCondition);

    }



    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        return true;
    }




}
