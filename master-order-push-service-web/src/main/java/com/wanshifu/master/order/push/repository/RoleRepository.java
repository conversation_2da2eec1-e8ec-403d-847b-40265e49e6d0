package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.mapper.RoleMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Repository
public class RoleRepository extends BaseRepository<Role> {

    @Resource
    private RoleMapper roleMapper;




    public int insertRole(String roleName,String roleDesc,Long createAccountId){
        Role role = new Role();
        role.setRoleName(roleName);
        role.setRoleDesc(roleDesc);
        role.setCreateAccountId(createAccountId);
        role.setUpdateAccountId(createAccountId);
        return this.insertSelective(role);
    }


    public int updateRole(Integer roleId,String roleName,String roleDesc,Long updateAccountId){
        Role role = new Role();
        role.setRoleId(roleId);
        role.setRoleName(roleName);
        role.setRoleDesc(roleDesc);
        return this.updateByPrimaryKeySelective(role);
    }


    public List<Role> selectList(String roleName, Date createTimeStart, Date createTimeEnd){
        return roleMapper.selectList(roleName,createTimeStart,createTimeEnd);
    }


    public Role selectByRoleName(String roleName, Integer roleId) {
        Example example = new Example(Role.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("roleName", roleName)
                .andEqualTo("isDelete", 0);
        if (roleId != null) {
            criteria.andNotEqualTo("roleId", roleId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public List<Role> selectByRoleIdList(List<Integer> roleIdList) {
        if(CollectionUtils.isEmpty(roleIdList)){
            return Collections.emptyList();
        }
        Example example = new Example(Role.class);
        example.createCriteria().andIn("roleId",roleIdList);
        return this.selectByExample(example);
    }




}