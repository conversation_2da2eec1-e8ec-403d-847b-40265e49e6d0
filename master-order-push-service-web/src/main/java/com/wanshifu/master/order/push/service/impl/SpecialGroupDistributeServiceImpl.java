package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.rocketmq.autoconfigure.component.RocketMqSendService;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.OrderDistributeService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR> Yong
 * @description 特殊人群调度逻辑
 * @date 2025/8/6 19:45
 */
@Service
public class SpecialGroupDistributeServiceImpl implements OrderDistributeService, InitializingBean {

    @Resource
    private RedisHelper redisHelper;

    @Resource
    private RocketMqSendService rocketMqSendService;

    @Value("${wanshifu.rocketMQ.order-match-master-topic}")
    private String orderMatchMasterTopic;

    @Override
    public int orderDistribute(OrderPushedResultNotice orderPushedResultNotice) {
        Long orderId = orderPushedResultNotice.getOrderBaseComposite().getOrderBase().getOrderId();
        String delayMinutesStr = redisHelper.get(ApolloConfigUtils.APPLICATION_NAME + ":special_group_" + orderId);
        if (delayMinutesStr == null) {
            return 1;
        }
        int delayMinutes = Integer.parseInt(delayMinutesStr);
        OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
        rqt.setMasterOrderId(orderId);
        rocketMqSendService.sendDelayMessage(orderMatchMasterTopic, PushMode.SPECIAL_GROUP.code, JSON.toJSONString(rqt), delayMinutes * 60 * 1000L);
        return 1;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        OrderDistributeContext.register(PushMode.SPECIAL_GROUP.code, this);
    }
}
