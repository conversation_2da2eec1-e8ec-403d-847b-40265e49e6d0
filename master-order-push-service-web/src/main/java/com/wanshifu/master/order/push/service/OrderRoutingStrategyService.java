package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;

public interface OrderRoutingStrategyService {

    int create(CreateRqt rqt);

    int update(UpdateRqt rqt);

    OrderRoutingStrategy detail(DetailRqt rqt);


    SimplePageInfo<OrderRoutingStrategy> list(ListRqt rqt);

    Integer delete(DeleteRqt rqt);

    Integer enable(EnableRqt rqt);



}
