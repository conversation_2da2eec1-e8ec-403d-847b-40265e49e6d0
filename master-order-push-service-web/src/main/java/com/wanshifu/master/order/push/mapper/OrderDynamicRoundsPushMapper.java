package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;

import java.util.List;

public interface OrderDynamicRoundsPushMapper extends IBaseCommMapper<OrderDynamicRoundsPush> {

    List<OrderDynamicRoundsPush> list(ListRqt rqt);

}