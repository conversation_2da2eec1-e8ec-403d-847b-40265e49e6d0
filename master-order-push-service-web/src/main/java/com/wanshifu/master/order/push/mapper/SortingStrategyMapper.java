package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 精排策略Mapper
 * <AUTHOR>
 */
public interface SortingStrategyMapper extends IBaseCommMapper<SortingStrategy> {
    List<SortingStrategy> selectList(@Param("orderFlag") String orderFlag,@Param("businessLineId") Long businessLineId, @Param("strategyName") String strategyName, @Param("strategyStatus") Integer strategyStatus, @Param("createStartTime") Date createStartTime, @Param("createEndTime") Date createEndTime, @Param("categoryIds") List<Long> categoryIds);

}