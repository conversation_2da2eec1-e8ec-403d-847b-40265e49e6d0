package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.information.api.MasterTeamApi;
import com.wanshifu.master.information.domain.pojo.CaptainInformationVo;
import com.wanshifu.master.information.domain.pojo.MasterTeamPojo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;

import com.wanshifu.master.order.push.domain.enums.AgentDistributePriority;
import com.wanshifu.master.order.push.domain.enums.AgentDistributeRule;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;

import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.resp.agent.TobGroupAgentInfoResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyDetailRqt;
import com.wanshifu.master.order.push.mapper.AgentDistributeStrategyMapper;

import com.wanshifu.master.order.push.repository.AgentDistributeStrategyRepository;
import com.wanshifu.master.order.push.repository.AgentInfoRepository;
import com.wanshifu.master.order.push.service.AgentDistributeStrategyService;
import com.wanshifu.master.order.push.service.OrderScoringStrategyService;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.config.api.ServeServiceApi;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:48
 */
@Service
public class AgentDistributeStrategyServiceImpl implements AgentDistributeStrategyService {

    @Resource
    private AgentDistributeStrategyRepository agentDistributeStrategyRepository;

    @Resource
    private AgentInfoRepository agentInfoRepository;

    @Resource
    private MasterTeamApi masterTeamApi;

    @Resource
    private AddressCommon addressCommon;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "调度管理", level2MenuName = "合作商订单调度",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "strategyName")
    public  Integer add(AddAgentDistributeStrategyRqt rqt){


        if ("toc".equals(rqt.getMasterSourceType())
                && AgentDistributeRule.DIRECT_APPOINT.code.equals(rqt.getDistributeRule())) {

            Assert.isTrue(StringUtils.isNotBlank(rqt.getDistributePriority()), "C端代理商选择直接指派时调度规则不能为空");
        }

        if (AgentDistributeRule.DIRECT_PUSH.code.equals(rqt.getDistributeRule())) {

            if (Objects.isNull(rqt.getPushStrategy())
                    || Objects.isNull(rqt.getPushStrategy().getAgentFirstViewNoHiredRePushTime())
                    || Objects.isNull(rqt.getPushStrategy().getAgentPushNoHiredRePushTime())) {
                throw new IllegalArgumentException("定向推送时推送策略配置不能为空！");
            }
        }
        /*if(AgentDistributeRule.DIRECT_APPOINT.code.equals(rqt.getDistributeRule()) && AgentDistributePriority.SMART.code.equals(rqt.getDistributePriority())){
            Assert.isTrue(Objects.nonNull(rqt.getScoringStrategyId()),"智能直接指派时选择的评分模型不能为空");
        }*/

        checkNonEffectiveTime(rqt);

//        checkServeAndDivisionId(rqt.getMasterSourceType(), rqt.getAgentId(), rqt.getServeIds(), rqt.getCityDivisionId().toString(), null);

        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setStrategyName(rqt.getStrategyName());
        agentDistributeStrategy.setStrategyDesc(rqt.getStrategyDesc());
        agentDistributeStrategy.setAgentId(rqt.getAgentId());

        /*if ("tobGroup".equals(rqt.getMasterSourceType())) {
            List<List<Long>> serveIds = JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {
            });
            agentDistributeStrategy.setServeIdArray(serveIds.toString());

            agentDistributeStrategy.setServeIds(getServeIds(serveIds));
        } else {
            agentDistributeStrategy.setServeIds(rqt.getServeIds());
        }*/
        agentDistributeStrategy.setServeIds(rqt.getServeIds());
        agentDistributeStrategy.setServeIdArray(rqt.getServeIdArray());
        agentDistributeStrategy.setFourthDivisionIds(rqt.getFourthDivisionIds());
        if (Objects.nonNull(rqt.getPushStrategy())) {
            agentDistributeStrategy.setPushStrategy(JSONUtil.toJsonStr(rqt.getPushStrategy()));
        }
        agentDistributeStrategy.setCityDivisionId(rqt.getCityDivisionId());
        agentDistributeStrategy.setDistributeRule(rqt.getDistributeRule());
        agentDistributeStrategy.setDistributePriority(rqt.getDistributePriority());
        agentDistributeStrategy.setScoringStrategyId(rqt.getScoringStrategyId());
        if(CollectionUtils.isNotEmpty(rqt.getNonEffectiveTimeList())){
            agentDistributeStrategy.setNonEffectiveTime(JSON.toJSONString(rqt.getNonEffectiveTimeList()));
        }
        agentDistributeStrategy.setServeNames(rqt.getServeNames());
        agentDistributeStrategy.setAgentName(rqt.getAgentName());
        agentDistributeStrategy.setCreateAccountId(rqt.getCreateAccountId());
        agentDistributeStrategy.setUpdateAccountId(rqt.getCreateAccountId());
        agentDistributeStrategy.setMasterSourceType(rqt.getMasterSourceType());
        agentDistributeStrategyRepository.insertSelective(agentDistributeStrategy);
        return 0;
    }

    /*private String getServeIds(List<List<Long>> serveIds) {
        Set<Long> serveIdList = Sets.newHashSet();
        for (List<Long> sub : serveIds) {
            if (CollectionUtils.isEmpty(sub)) {
                continue;
            }
            Long last = sub.get(sub.size() - 1);
            serveIdList.add(last);
        }
        if (CollectionUtil.isEmpty(serveIdList)) {
            return  "";
        }
        Set<Long> serveIdSet = serveServiceApi.getLeafServeIdsByServeIds(serveIdList);
        if (CollectionUtil.isEmpty(serveIdSet)) {
            return "";
        } else {
            return serveIdSet.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
    }*/

    private void checkNonEffectiveTime(AddAgentDistributeStrategyRqt rqt){

        if(CollectionUtils.isEmpty(rqt.getNonEffectiveTimeList())){
            return ;
        }

        if(rqt.getNonEffectiveTimeList().size() > 2){
            throw new IllegalArgumentException("不生效时间段数量超过2个!");

        }
        AddAgentDistributeStrategyRqt.NonEffectiveTime nonEffectiveTimeRange = rqt.getNonEffectiveTimeList().stream().filter(nonEffectiveTime -> nonEffectiveTime.getNonEffectiveEndTime().equals(nonEffectiveTime)).findFirst().orElse(null);
        if(Objects.nonNull(nonEffectiveTimeRange)){
            throw new IllegalArgumentException("不生效时间段,开始和结束时间相同!");
        }

        if(rqt.getNonEffectiveTimeList().size() != 2){
            return ;
        }



        if(DateFormatterUtil.isTimeRangeOverlap(rqt.getNonEffectiveTimeList().get(0).getNonEffectiveStartTime(),rqt.getNonEffectiveTimeList().get(0).getNonEffectiveEndTime()
                ,rqt.getNonEffectiveTimeList().get(1).getNonEffectiveStartTime(),rqt.getNonEffectiveTimeList().get(1).getNonEffectiveEndTime())){
            throw new RuntimeException("不生效时间段重复");
        }

    }


    private void checkServeAndDivisionId(String masterSourceType, Long agentId,
                                         String serveIds, String secondDivisionId, Integer strategyId) {

        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeStrategyRepository.selectByAgentIdAndMasterSourceType(agentId, masterSourceType);
        if (CollectionUtils.isNotEmpty(agentDistributeStrategyList)) {

            if (strategyId != null) {
                agentDistributeStrategyList = agentDistributeStrategyList.stream().filter(agentDistributeStrategy -> (!agentDistributeStrategy.getStrategyId().equals(strategyId))).collect(Collectors.toList());
            }

            List<Long> serveIdList = Arrays.stream(serveIds.split(",")).map(Long::parseLong).collect(Collectors.toList());

            agentDistributeStrategyList.forEach(agentDistributeStrategy -> {
                List<Long> agentServeIdList = Arrays.stream(agentDistributeStrategy.getServeIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                Long existingSecondDivisionId = agentDistributeStrategy.getCityDivisionId();
                if ((!Collections.disjoint(serveIdList, agentServeIdList)) && (existingSecondDivisionId.toString().equals(secondDivisionId))) {
                    throw new IllegalArgumentException("服务、城市和其他调度策略重合，请检查");
                }
            });
        }
    }


    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "调度管理", level2MenuName = "合作商订单调度",
            tableName = "agent_distribute_strategy", mapperClass = AgentDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agentDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public  Integer update(UpdateAgentDistributeStrategyRqt rqt){

        if ("toc".equals(rqt.getMasterSourceType())
                && AgentDistributeRule.DIRECT_APPOINT.code.equals(rqt.getDistributeRule())) {

            Assert.isTrue(StringUtils.isNotBlank(rqt.getDistributePriority()), "C端代理商选择直接指派时调度规则不能为空");
        }

        if (AgentDistributeRule.DIRECT_PUSH.code.equals(rqt.getDistributeRule())) {

            if (Objects.isNull(rqt.getPushStrategy())
                    || Objects.isNull(rqt.getPushStrategy().getAgentFirstViewNoHiredRePushTime())
                    || Objects.isNull(rqt.getPushStrategy().getAgentPushNoHiredRePushTime())) {
                throw new IllegalArgumentException("定向推送时推送策略配置不能为空！");
            }
        }

        AgentDistributeStrategy strategy = agentDistributeStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
        Assert.notNull(strategy,"策略不存在,更新失败");

        checkNonEffectiveTime(rqt);


//        checkServeAndDivisionId(rqt.getMasterSourceType(),rqt.getAgentId(),rqt.getServeIds(),rqt.getCityDivisionId().toString(),strategy.getStrategyId());



        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setStrategyId(rqt.getStrategyId());
        agentDistributeStrategy.setStrategyName(rqt.getStrategyName());
        agentDistributeStrategy.setStrategyDesc(rqt.getStrategyDesc());
        agentDistributeStrategy.setAgentId(rqt.getAgentId());
        agentDistributeStrategy.setAgentName(rqt.getAgentName());

        /*if ("tobGroup".equals(rqt.getMasterSourceType())) {
            List<List<Long>> serveIds = JSON.parseObject(rqt.getServeIds(), new TypeReference<List<List<Long>>>() {
            });
            agentDistributeStrategy.setServeIdArray(serveIds.toString());

            agentDistributeStrategy.setServeIds(getServeIds(serveIds));
        } else {
            agentDistributeStrategy.setServeIdArray("");
            agentDistributeStrategy.setServeIds(rqt.getServeIds());
        }*/
        agentDistributeStrategy.setServeIds(rqt.getServeIds());
        agentDistributeStrategy.setServeIdArray(rqt.getServeIdArray());
        agentDistributeStrategy.setFourthDivisionIds(rqt.getFourthDivisionIds());
        if (Objects.nonNull(rqt.getPushStrategy())) {
            agentDistributeStrategy.setPushStrategy(JSONUtil.toJsonStr(rqt.getPushStrategy()));
        } else {
            agentDistributeStrategy.setPushStrategy("");
        }
        agentDistributeStrategy.setScoringStrategyId(rqt.getScoringStrategyId());
        agentDistributeStrategy.setCityDivisionId(rqt.getCityDivisionId());
        agentDistributeStrategy.setDistributeRule(rqt.getDistributeRule());
        agentDistributeStrategy.setDistributePriority(rqt.getDistributePriority());
        agentDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        agentDistributeStrategy.setServeNames(rqt.getServeNames());
        agentDistributeStrategy.setMasterSourceType(rqt.getMasterSourceType());

        if(CollectionUtils.isNotEmpty(rqt.getNonEffectiveTimeList())){
            agentDistributeStrategy.setNonEffectiveTime(JSON.toJSONString(rqt.getNonEffectiveTimeList()));
        }else{
            agentDistributeStrategy.setNonEffectiveTime("");
        }
        int result = agentDistributeStrategyRepository.updateByPrimaryKeySelective(agentDistributeStrategy);

        Assert.isTrue(result > 0,"更新合作商订单调度策略失败");

        return rqt.getStrategyId();
    }


    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "调度管理", level2MenuName = "合作商订单调度",
            tableName = "agent_distribute_strategy", mapperClass = AgentDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agentDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableAgentDistributeStrategyRqt rqt){

        Condition condition = new Condition(AgentDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", rqt.getStrategyStatus() == 1 ? 0 : 1);

        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setStrategyStatus(rqt.getStrategyStatus());
        agentDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = agentDistributeStrategyRepository.updateByConditionSelective(agentDistributeStrategy, condition);

        if(rqt.getStrategyStatus() == 1){
            Assert.isTrue(result > 0,"启用策略失败");
        }else{
            Assert.isTrue(result > 0,"禁用策略失败");
        }
        return 1;
    }


    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "调度管理", level2MenuName = "合作商订单调度",
            tableName = "agent_distribute_strategy", mapperClass = AgentDistributeStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "agentDistributeStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteAgentDistributeStrategyRqt rqt){
        Condition condition = new Condition(AgentDistributeStrategy.class);
        condition.createCriteria().andEqualTo("strategyId", rqt.getStrategyId()).andEqualTo("strategyStatus", 0).andEqualTo("isDelete", 0);

        AgentDistributeStrategy agentDistributeStrategy = new AgentDistributeStrategy();
        agentDistributeStrategy.setIsDelete(1);
        agentDistributeStrategy.setUpdateAccountId(rqt.getUpdateAccountId());
        int result = agentDistributeStrategyRepository.updateByConditionSelective(agentDistributeStrategy, condition);
        Assert.isTrue(result > 0,"删除失败");
        return 1;
    }



    @Override
    public AgentDistributeDetailResp detail(AgentDistributeStrategyDetailRqt rqt) {
        AgentDistributeStrategy agentDistributeStrategy = agentDistributeStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
        if (agentDistributeStrategy == null) {
            return null;
        }

        AgentDistributeDetailResp resp = new AgentDistributeDetailResp();
        BeanUtils.copyProperties(agentDistributeStrategy, resp);


        if(StringUtils.isNotBlank(agentDistributeStrategy.getNonEffectiveTime())){
            resp.setNonEffectiveTimeList(JSON.parseArray(agentDistributeStrategy.getNonEffectiveTime(),AddAgentDistributeStrategyRqt.NonEffectiveTime.class));
        }

        if (!Strings.isNullOrEmpty(agentDistributeStrategy.getPushStrategy())) {
            resp.setPushStrategy(JSON.parseObject(agentDistributeStrategy.getPushStrategy(), AddAgentDistributeStrategyRqt.PushStrategy.class));
        }

        if (StringUtils.isNotBlank(agentDistributeStrategy.getServeIdArray())) {
            resp.setServeIdArray(agentDistributeStrategy.getServeIdArray());
        }

        return resp;
    }


    @Override
    public SimplePageInfo<AgentInfo> getAgentList(@Valid @RequestBody GetAgentListRqt rqt){

        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());

        List<AgentInfo> agentInfoList = agentInfoRepository.selectList(rqt.getAgentName(),rqt.getMasterSourceType());

        if(CollectionUtils.isNotEmpty(agentInfoList)){
            agentInfoList.forEach(agentInfo -> {
                if(StringUtils.isBlank(agentInfo.getMasterSourceType())){
                    agentInfo.setMasterSourceType(MasterSourceType.TOB.code);
                }
            });
        }

        SimplePageInfo<AgentInfo> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(agentInfoList);
        return listRespSimplePageInfo;

    }

    @Override
    public SimplePageInfo<TobGroupAgentInfoResp> getTobGroupAgentList(TobGroupAgentInfoRqt rqt) {
        SimplePageInfo<TobGroupAgentInfoResp> result = new SimplePageInfo<>();
        try {
            MasterTeamPojo params = new MasterTeamPojo();
            params.setPageNum(rqt.getPageNum());
            params.setPageSize(rqt.getPageSize());
            params.setMasterSourceType("tob");
            params.setPhone(rqt.getPhone());
            params.setMasterName(rqt.getGroupMasterName());
            SimplePageInfo<CaptainInformationVo> captainInformationVoSimplePageInfo = masterTeamApi.pageQueryMasterTeamInfo(params);
            if (Objects.isNull(captainInformationVoSimplePageInfo)
                    || CollectionUtil.isEmpty(captainInformationVoSimplePageInfo.getList())) {
                return result;
            }
            List<TobGroupAgentInfoResp> tobGroupAgentInfoRespList = captainInformationVoSimplePageInfo.getList().stream().map(captainInformationVo -> {
                TobGroupAgentInfoResp tobGroupAgentInfoResp = new TobGroupAgentInfoResp();
                tobGroupAgentInfoResp.setMasterId(captainInformationVo.getMasterId());
                tobGroupAgentInfoResp.setMasterName(captainInformationVo.getTeamName());
                tobGroupAgentInfoResp.setPhone(captainInformationVo.getPhone());
                tobGroupAgentInfoResp.setUseStatus("on".equals(captainInformationVo.getStatus()) ? 0 : 1);
                tobGroupAgentInfoResp.setServiceFourthDivisionIds(captainInformationVo.getServiceArea());
                return tobGroupAgentInfoResp;
            }).collect(Collectors.toList());

            result.setList(tobGroupAgentInfoRespList);
            result.setPages(captainInformationVoSimplePageInfo.getPages());
            result.setPageNum(captainInformationVoSimplePageInfo.getPageNum());
            result.setTotal(captainInformationVoSimplePageInfo.getTotal());
            result.setPageSize(captainInformationVoSimplePageInfo.getPageSize());
            return result;

        } catch (Exception e) {

            throw new BusException("获取团队师傅信息异常！");
        }
    }


    @Override
    public SimplePageInfo<AgentDistributeStrategy> list(GetAgentDistributeStrategyListRqt rqt){

        Page page = PageHelper.startPage(rqt.getPageNum(), rqt.getPageSize());

        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeStrategyRepository.selectList(rqt.getStrategyName(),rqt.getAgentName(),rqt.getServeName(),
                rqt.getCityDivisionId(),rqt.getCreateTimeStart(),rqt.getCreateTimeEnd(),rqt.getMasterSourceType());

        SimplePageInfo<AgentDistributeStrategy> listRespSimplePageInfo = new SimplePageInfo<>();
        listRespSimplePageInfo.setPages(page.getPages());
        listRespSimplePageInfo.setPageNum(page.getPageNum());
        listRespSimplePageInfo.setTotal(page.getTotal());
        listRespSimplePageInfo.setPageSize(page.getPageSize());
        listRespSimplePageInfo.setList(agentDistributeStrategyList);
        return listRespSimplePageInfo;


    }


    @Override
    public void flushAgentStrategyData() {
        //刷四级地址,定向推送策略
        List<AgentDistributeStrategy> agentDistributeStrategyList = agentDistributeStrategyRepository.select4FlushData();
        if (CollectionUtil.isEmpty(agentDistributeStrategyList)) {
            return;
        }

        agentDistributeStrategyList.forEach(agentDistributeStrategy -> {
            String thirdDivisionIds = agentDistributeStrategy.getThirdDivisionIds();
            if (StringUtils.isBlank(thirdDivisionIds)) {
                return;
            }
            List<Address> addressList = addressCommon.getAddressByDivisionIds(thirdDivisionIds);
            List<Long> fourthDivisionIds = Lists.newArrayList();
            for (Address address : addressList) {
                if (address.getDivisionLevel() == 5) {
                    fourthDivisionIds.add(address.getDivisionId());
                } else if (address.getDivisionLevel() == 4) {
                    List<Address> subList = addressCommon.getSubListByDivisionId(address.getDivisionId());
                    if (CollectionUtil.isNotEmpty(subList)) {
                        subList.forEach(subAddress -> {
                            fourthDivisionIds.add(subAddress.getDivisionId());
                        });
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(fourthDivisionIds)
                    || "direct_push".equals(agentDistributeStrategy.getDistributeRule())) {

                AgentDistributeStrategy temp = new AgentDistributeStrategy();
                temp.setStrategyId(agentDistributeStrategy.getStrategyId());
                if (CollectionUtil.isNotEmpty(fourthDivisionIds)) {
                    temp.setFourthDivisionIds(com.wanshifu.framework.utils.StringUtils.join(fourthDivisionIds, ","));
                }
                if ("direct_push".equals(agentDistributeStrategy.getDistributeRule())) {
                    temp.setPushStrategy("{\n" +
                            "    \"agentPushNoHiredRePushTime\":60,\n" +
                            "    \"agentFirstViewNoHiredRePushTime\":5\n" +
                            "}");
                }

                agentDistributeStrategyRepository.updateByPrimaryKeySelective(temp);
            }


        });

    }

    @Override
    public AgentInfo getAgentById(Long agentId) {
        AgentInfo agentInfo = agentInfoRepository.selectByPrimaryKey(agentId);
        if (agentInfo == null) {
            return null;
        }
        if(StringUtils.isBlank(agentInfo.getMasterSourceType())){
            agentInfo.setMasterSourceType(MasterSourceType.TOB.code);
        }
        return agentInfo;
    }

    @Override
    public TobGroupAgentInfoResp getTobGroupAgentById(Long agentId) {
        try {
            MasterTeamPojo params = new MasterTeamPojo();
            params.setMasterId(agentId);
            CaptainInformationVo captainInformationVo = masterTeamApi.getCaptainInfo(params);
            if (Objects.isNull(captainInformationVo)) {
                return null;
            }
            TobGroupAgentInfoResp tobGroupAgentInfoResp = new TobGroupAgentInfoResp();
            tobGroupAgentInfoResp.setMasterId(captainInformationVo.getMasterId());
            tobGroupAgentInfoResp.setMasterName(captainInformationVo.getTeamName());
            tobGroupAgentInfoResp.setPhone(captainInformationVo.getPhone());
            tobGroupAgentInfoResp.setUseStatus("on".equals(captainInformationVo.getStatus()) ? 0 : 1);
            tobGroupAgentInfoResp.setServiceFourthDivisionIds(captainInformationVo.getServiceArea());
            return tobGroupAgentInfoResp;
        } catch (Exception e) {
            throw new BusException("获取团队师傅信息异常！");
        }
    }
}
