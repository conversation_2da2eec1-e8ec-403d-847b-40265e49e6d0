package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.GetOrderSelectStrategyListRqt;
import com.wanshifu.master.order.push.mapper.OrderSelectStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 11:24
 */
@Repository
public class OrderSelectStrategyRepository extends BaseRepository<OrderSelectStrategy> {

    @Resource
    private OrderSelectStrategyMapper orderSelectStrategyMapper;

    public List<OrderSelectStrategy> selectList(GetOrderSelectStrategyListRqt rqt){
        return orderSelectStrategyMapper.selectList(rqt);
    }

    public List<OrderSelectStrategy> selectByStrategyIdList(List<Integer> strategyIdList) {
        Condition condition = new Condition(OrderSelectStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andIn("strategyId", strategyIdList);
        return this.selectByCondition(condition);
    }

    public List<OrderSelectStrategy> selectAvailableStrategyByIdList(List<Integer> strategyIdList) {
        Condition condition = new Condition(OrderSelectStrategy.class);
        Condition.Criteria criteria = condition.createCriteria();
        criteria.andIn("strategyId", strategyIdList);
        criteria.andEqualTo("strategyStatus",1);
        criteria.andEqualTo("isDelete",0);
        return this.selectByCondition(condition);
    }


    public OrderSelectStrategy selectByStrategyNameAndBusinessLineId(String strategyName, Long businessLineId, Integer strategyId) {
        Example example = new Example(OrderSelectStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("businessLineId", businessLineId)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }


    public int insert(Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String masterResources,
                      String selectRule,String selectRuleExpression,Long createAccountId){
        OrderSelectStrategy orderSelectStrategy = new OrderSelectStrategy();
        orderSelectStrategy.setBusinessLineId(businessLineId);
        orderSelectStrategy.setOrderFrom(orderFrom);
        orderSelectStrategy.setStrategyName(strategyName);
        orderSelectStrategy.setStrategyDesc(strategyDesc);
        orderSelectStrategy.setMasterResources(masterResources);
        orderSelectStrategy.setSelectStrategy("");
        orderSelectStrategy.setSelectStrategyExpression("");
        orderSelectStrategy.setSelectRule(selectRule);
        orderSelectStrategy.setSelectRuleExpression(selectRuleExpression);
        orderSelectStrategy.setCreateAccountId(createAccountId);
        orderSelectStrategy.setUpdateAccountId(createAccountId);
        return orderSelectStrategyMapper.insertSelective(orderSelectStrategy);
    }


    public int update(Integer strategyId,Long businessLineId,String strategyName,String orderFrom,String strategyDesc,String masterResources,
                      String selectRule,String selectRuleExpression,Long updateAccountId){
        OrderSelectStrategy orderSelectStrategy = new OrderSelectStrategy();
        orderSelectStrategy.setStrategyId(strategyId);
        orderSelectStrategy.setBusinessLineId(businessLineId);
        orderSelectStrategy.setOrderFrom(orderFrom);
        orderSelectStrategy.setStrategyName(strategyName);
        orderSelectStrategy.setStrategyDesc(strategyDesc);
        orderSelectStrategy.setMasterResources(masterResources);
        orderSelectStrategy.setSelectRule(selectRule);
        orderSelectStrategy.setSelectRuleExpression(selectRuleExpression);
        orderSelectStrategy.setUpdateAccountId(updateAccountId);
        return orderSelectStrategyMapper.updateByPrimaryKeySelective(orderSelectStrategy);
    }
}
