package com.wanshifu.master.order.push.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.CooperationBusinessMasterMatchLog;
import com.wanshifu.master.order.push.domain.po.OrderFullTimeMasterMasterMatchLog;
import com.wanshifu.master.order.push.domain.po.OrderTechniqueVerifyMasterMatch;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.OrderFullTimeMasterMatchLogRepository;
import com.wanshifu.master.order.push.repository.OrderTechniqueVerifyMasterMatchRepository;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import com.wanshifu.master.order.push.service.PushQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/12 11:21
 */
@Service
@Slf4j
public class FullTimeMasterOrderDistributeResultNoticeServiceImpl implements OrderDistributeResultNoticeService, InitializingBean {

    @Resource
    private OrderDataBuilder orderDataBuilder;

    @Resource
    private OrderFullTimeMasterMatchLogRepository orderFullTimeMasterMatchLogRepository;

    @Resource
    private PushQueueService pushQueueService;


    @Override
    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage) {
        Long orderId = orderDistributeResultMessage.getOrderId();
        log.info("techniqueVerifyOrder distributeResultNotice,orderId:{},message:{}", orderId, JSONUtil.toJsonStr(orderDistributeResultMessage));

        List<Long> successMasterList = orderDistributeResultMessage.getSuccessMasterList();

        if(CollectionUtils.isEmpty(successMasterList) ){
            //
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderId);
            orderMatchMasterRqt.setExclusivePushModeList(Arrays.asList(PushMode.FULL_TIME_MASTER.code));

            pushQueueService.sendDelayPushMessage(10L, JSONObject.toJSONString(orderMatchMasterRqt));

//            OrderDetailData orderDetailData = orderDataBuilder.build(orderMatchMasterRqt);
//            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
//            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
//            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }


        if(CollectionUtils.isNotEmpty(successMasterList)){
            Long successMasterId = successMasterList.get(0);
            this.updateMatchLog(orderId, successMasterId, 1, "");

        }

        List<Long> failMasterList = orderDistributeResultMessage.getFailMasterList();
        if(CollectionUtils.isNotEmpty(failMasterList)){
            Map<Long, JSONObject> failMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(failMasterList)){
                List<JSONObject> jsonObjectList = orderDistributeResultMessage.getMasterFailVos();
                if(CollectionUtils.isNotEmpty(jsonObjectList)){
                    jsonObjectList.forEach(jsonObject -> {
                        failMap.put( jsonObject.getLong("masterId"),jsonObject.getJSONObject("object"));
                    });
                }
            }


            for (Long masterId : failMasterList) {
                String autoReceiveFailReason = failMap.containsKey(masterId) ? failMap.get(masterId).getString("tipContent") : "";
                this.updateMatchLog(orderId, masterId, 0, autoReceiveFailReason);
            }
        }

        return 1;
    }



    private void updateMatchLog(Long orderId, Long masterId, Integer isAutoReceiveSucc, String autoReceiveFailReason) {
        Condition condition = new Condition(OrderFullTimeMasterMasterMatchLog.class);
        condition.createCriteria().andEqualTo("orderId", orderId).andEqualTo("masterId", masterId);
        OrderFullTimeMasterMasterMatchLog matchLog = new OrderFullTimeMasterMasterMatchLog();
        matchLog.setOrderId(orderId);
        matchLog.setMasterId(masterId);
        matchLog.setIsAutoReceiveSucc(isAutoReceiveSucc);
        matchLog.setAutoReceiveFailReason(autoReceiveFailReason);
        matchLog.setUpdateTime(new Date());
        orderFullTimeMasterMatchLogRepository.updateByConditionSelective(matchLog, condition);
    }

    @Override
    public void afterPropertiesSet() {
        OrderDistributeResultNoticeContext.register(AppointDetailType.AUTO_GRAB_FULL_TIME_MASTER.getCode(), this);
    }

}
