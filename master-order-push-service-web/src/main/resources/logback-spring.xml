<?xml version="1.0" encoding="UTF-8"?>
<!-- 属性描述 scan：性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true
scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
当scan为true时，此属性生效。默认的时间间隔为1分钟。
debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property resource="config/application.properties" />
    <contextName>${spring.application.name}</contextName>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder默认配置为PartternLayoutEncoder    -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[${spring.application.name}][%5p] %d{yyyy-MM-dd HH:mm:ss SSS} [%X{RequestID}]- %c >>> %m %n</pattern>
        </encoder>
    </appender>

    <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
    <appender name="appLogFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>GMT+8</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        {
                        "severity": "%level",
                        "service": "${spring.application.name}",
                        "trace": "%X{X-B3-TraceId:-}",
                        "span": "%X{X-B3-SpanId:-}",
                        "parent": "%X{X-B3-ParentSpanId:-}",
                        "exportable": "%X{X-Span-Export:-}",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "class": "%logger{40}",
                        "rest": "%m",
                        "exception": "%ex{full}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <!-- 指定日志文件的名称 -->
        <file>${logging.path}/app/app.log</file>
        <!-- 当发生滚动时，决定 RollingFileAppender 的行为，涉及文件移动和重命名 TimeBasedRollingPolicy： 最常用的滚动策略，它根据时间来制定滚动策略，既负责滚动也负责出发滚动。 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 滚动时产生的文件的存放位置及文件名称 %d{yyyy-MM-dd}：按天进行日志滚动 %i：当文件大小超过maxFileSize时，按照i进行文件滚动 -->
            <fileNamePattern>${logging.path}/app/app-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <!-- 可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件。假设设置每天滚动， 且maxHistory是365，则只保存最近365天的文件，删除之前的旧文件。注意，删除旧文件是， 那些为了归档而创建的目录也会被删除。 -->
            <MaxHistory>365</MaxHistory>
            <!-- 当日志文件超过maxFileSize指定的大小是，根据上面提到的%i进行日志文件滚动 注意此处配置SizeBasedTriggeringPolicy是无法实现按文件大小进行滚动的，必须配置timeBasedFileNamingAndTriggeringPolicy -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="errorLogFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!-- 日志输出格式：%d表示日期时间，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %logger{50} 表示logger名字最长50个字符，否则按照句点分割。 %msg：日志消息，%n是换行符 -->
            <pattern>[%5p] %d{yyyy-MM-dd HH:mm:ss SSS} [%X{RequestID}]- %c >>> %m %n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <file>${logging.path}/error/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/error/error-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <MaxHistory>365</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><!-- 只打warn以上的日志 -->
            <level>WARN</level>
        </filter>
    </appender>

    <springProfile name="local">
        <logger name="com.wanshifu.mapper" level="DEBUG"/>
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
    <springProfile name="dev">
        <logger name="com.wanshifu.mapper" level="DEBUG"/>
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="appLogFileAppender"/>
        </root>
    </springProfile>
    <springProfile name="test">
        <logger name="com.wanshifu.mapper" level="DEBUG"/>
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="appLogFileAppender"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="appLogFileAppender"/>
            <appender-ref ref="errorLogFileAppender"/>
        </root>
    </springProfile>
</configuration>