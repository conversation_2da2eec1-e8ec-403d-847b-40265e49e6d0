<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushProgressMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushProgress">
        <id column="push_progress_id" jdbcType="BIGINT" property="pushProgressId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="push_status" jdbcType="VARCHAR" property="pushStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getOrderLatestVersion" resultType="String">
        SELECT max(order_version) AS order_version FROM push_progress WHERE order_id=#{orderId} and push_type = 'normal_push'
    </select>


    <select id="getOrderLatestPushProgress" resultMap="BaseResultMap">
        SELECT push_progress_id,push_status  FROM push_progress WHERE order_id=#{orderId} and push_type = 'normal_push' ORDER BY push_progress_id desc limit 1
    </select>


    <select id="countByPushType" resultType="Integer">
        SELECT count(*)  FROM push_progress WHERE order_id=#{orderId} and push_type = #{pushType}
    </select>


</mapper>