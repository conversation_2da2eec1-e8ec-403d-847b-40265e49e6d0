<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushOrderListMapper">

    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushOrderList">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="business_line_id" jdbcType="BIGINT" property="businessLineId"/>
        <result column="second_division_id" jdbcType="BIGINT" property="secondDivisionId"/>
        <result column="third_division_id" jdbcType="BIGINT" property="thirdDivisionId"/>
        <result column="fourth_division_id" jdbcType="BIGINT" property="fourthDivisionId"/>
        <result column="child_goods_category_ids" jdbcType="VARCHAR" property="childGoodsCategoryIds"/>
        <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="order_from_type" jdbcType="VARCHAR" property="orderFromType"/>
        <result column="serve_type_id" jdbcType="INTEGER" property="serveTypeId"/>
        <result column="appoint_type" jdbcType="INTEGER" property="appointType"/>
        <result column="push_strategy" jdbcType="VARCHAR" property="pushStrategy"/>
        <result column="base_select_master_num" jdbcType="INTEGER" property="baseSelectMasterNum"/>
        <result column="master_num_after_filter" jdbcType="INTEGER" property="masterNumAfterFilter"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
   </resultMap>


    <select id="selectNoPushedMasterOrderList" resultMap="BaseResultMap">

        select * from push_order_list
        where is_delete = 0

        <if test="businessLineId != null ">
          <choose>
            <when test="businessLineId == 1">
                and business_line_id in( 1,3)
            </when>
              <when test="businessLineId == 2">
                  and business_line_id = 2
              </when>
              <when test="businessLineId == 3">
                  and business_line_id = 3
              </when>
            <otherwise>

            </otherwise>
          </choose>
        </if>

        <if test="pushTimeStart != null ">
            AND push_time >= #{pushTimeStart}
        </if>
        <if test="pushTimeEnd != null ">
            AND push_time <![CDATA[ <= ]]> #{pushTimeEnd}
        </if>

        <if test="thirdDivisionId != null ">
            AND third_division_id = #{thirdDivisionId}
        </if>

        <if test="fourthDivisionId != null ">
            AND fourth_division_id = #{fourthDivisionId}
        </if>

        <if test="childGoodsId != null ">
            AND FIND_IN_SET(#{childGoodsId},child_goods_category_ids)
        </if>

        <if test="serveTypeList != null">
            AND `serve_type_id` in
            <foreach collection="serveTypeList" item="serveTypeTemp" open="(" separator="," close=")">
                #{serveTypeTemp}
            </foreach>
        </if>

        <if test="categoryList != null">
            AND `category_id` in
            <foreach collection="categoryList" item="categoryTemp" open="(" separator="," close=")">
                #{categoryTemp}
            </foreach>
        </if>

        <if test="appointTypeList != null">
            AND `appoint_type` in
            <foreach collection="appointTypeList" item="appointTypeTemp" open="(" separator="," close=")">
                #{appointTypeTemp}
            </foreach>
        </if>

        <if test="orderFromList != null">
            AND `order_from_type` in
            <foreach collection="orderFromList" item="orderFromTemp" open="(" separator="," close=")">
                #{orderFromTemp}
            </foreach>
        </if>


        <if test="divisionId != null ">
            AND (second_division_id = #{divisionId} OR third_division_id = #{divisionId} OR fourth_division_id = #{divisionId})
        </if>


        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>

        order by push_time desc

    </select>

</mapper>