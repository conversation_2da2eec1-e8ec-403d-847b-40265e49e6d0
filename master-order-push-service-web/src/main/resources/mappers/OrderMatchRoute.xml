<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderMatchRouteMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderMatchRoute">
        <id column="route_id" jdbcType="INTEGER" property="routeId"/>
        <id column="route_name" jdbcType="VARCHAR" property="routeName"/>
        <result column="route_desc" jdbcType="VARCHAR" property="routeDesc"/>
        <result column="order_push_flag" jdbcType="VARCHAR" property="orderPushFlag"/>
        <result column="order_priority_match_rule" jdbcType="VARCHAR" property="orderPriorityMatchRule"/>
        <result column="order_standby_match_rule" jdbcType="VARCHAR" property="orderStandbyMatchRule"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>


    <select id="selectList" resultMap="BaseResultMap">
        select * from order_match_route where is_delete = 0

            <if test="routeName != null and routeName !=''">
                AND route_name like CONCAT( '%', #{routeName},'%')
            </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            order by update_time desc
    </select>



</mapper>