<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.NewMasterOrderDistributeStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy">
        <id column="strategy_id" jdbcType="INTEGER" property="strategyId"/>
        <id column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="distribute_type" jdbcType="VARCHAR" property="distributeType"/>
        <result column="open_city_mode" jdbcType="VARCHAR" property="openCityMode"/>
        <result column="city_ids" jdbcType="VARCHAR" property="cityIds"/>
        <result column="distribute_strategy" jdbcType="VARCHAR" property="distributeStrategy"/>
        <result column="distribute_strategy_expression" jdbcType="VARCHAR" property="distributeStrategyExpression"/>
        <result column="strategy_status" jdbcType="INTEGER" property="strategyStatus"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        select * from new_master_order_distribute_strategy where business_line_id = #{businessLineId}

        <if test="strategyName != null and strategyName !=''">
            AND strategy_name like CONCAT( '%', #{strategyName},'%')
        </if>

        <if test="createStartTime != null ">
            AND create_time >= #{createStartTime}
        </if>
        <if test="createEndTime != null ">
            AND create_time <![CDATA[ <= ]]> #{createEndTime}
        </if>

        <if test="strategyStatus != null ">
            AND strategy_status= #{strategyStatus}
        </if>

        <if test="categoryIdList!= null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>

        <if test="cityId != null">
            AND (open_city_mode = 'all' OR FIND_IN_SET(#{cityId},city_ids))
        </if>

        and is_delete = 0
        order by update_time desc
    </select>






        <select id="selectByCategoryIdAndCityId" resultMap="BaseResultMap">

            select * from new_master_order_distribute_strategy
            where business_line_id = #{businessLineId}

            <if test="distributeType != null and distributeType != '' ">
                AND distribute_type = #{distributeType}
            </if>

            <if test="categoryId != null ">
                AND FIND_IN_SET(#{categoryId},category_ids)
            </if>

            <if test="cityId != null and cityId != '' and cityId != 'all' ">
                AND FIND_IN_SET(#{cityId},city_ids)
            </if>


            <if test="cityId != null and cityId == 'all' ">
                AND open_city_mode = 'all'
            </if>


            AND strategy_status= 1
            and is_delete = 0

        </select>



        <select id="selectByCityAndCategoryId" resultMap="BaseResultMap">

            select * from new_master_order_distribute_strategy
            where is_delete = 0

            <if test="cityIdList != null and cityIdList.size() > 0">
                AND (
                <foreach collection="cityIdList" item="cityId" separator="OR" index="index">
                    FIND_IN_SET(#{cityId},city_ids)
                </foreach>
                )
            </if>

            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND (
                <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                    FIND_IN_SET(#{categoryId},category_ids)
                </foreach>
                )
            </if>
            <if test="strategyId != null ">
                AND strategy_id != #{strategyId}
            </if>
            <if test="businessLineId != null ">
                AND business_line_id = #{businessLineId}
            </if>
            <if test="distributeType != null and distributeType != ''">
                AND distribute_type = #{distributeType}
            </if>
            limit 1
        </select>

</mapper>