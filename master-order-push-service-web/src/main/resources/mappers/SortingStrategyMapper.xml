<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.SortingStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.SortingStrategy">
        <id column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="sorting_rule" jdbcType="LONGVARCHAR" property="sortingRule"/>
        <result column="rule_expression" jdbcType="LONGVARCHAR" property="ruleExpression"/>
        <result column="strategy_status" jdbcType="TINYINT" property="strategyStatus"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="order_flag" jdbcType="VARCHAR" property="orderFlag"/>

    </resultMap>
    <sql id="Base_Column_List">strategyId
    , strategyName, strategyDesc, categoryIds, sortingRule, ruleExpression, strategyStatus, businessLineId, isDelete, createTime, updateTime </sql>
    <select id="selectList" resultMap="BaseResultMap">
        select * from sorting_strategy
        <where>
            <if test="businessLineId != null">
                AND business_line_id= #{businessLineId}
            </if>
            <if test="orderFlag != null and orderFlag !=''">
                AND order_flag= #{orderFlag}
            </if>
            <if test="strategyName != null and strategyName !=''">
                AND strategy_name like CONCAT( '%', #{strategyName},'%')
            </if>
            <if test="strategyStatus != null ">
                AND strategy_status= #{strategyStatus}
            </if>
            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                AND (
                <foreach collection="categoryIds" item="categoryId" separator="OR" index="index">
                    FIND_IN_SET(#{categoryId},category_ids)
                </foreach>
                )
            </if>
            and is_delete = 0
            order by update_time desc
        </where>
    </select>
</mapper>