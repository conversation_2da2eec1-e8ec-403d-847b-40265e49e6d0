<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushPortRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushPortRule">
        <id column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc"/>
        <result column="appoint_type" jdbcType="VARCHAR" property="appointType"/>
        <result column="interval_time" jdbcType="INTEGER" property="intervalTime"/>
        <result column="offer_num" jdbcType="INTEGER" property="offerNum"/>
        <result column="order_tag" jdbcType="VARCHAR" property="orderTag"/>
        <result column="lv_1_serve_ids" jdbcType="LONGVARCHAR" property="lv1ServeIds"/>
        <result column="city_ids" jdbcType="LONGVARCHAR" property="cityIds"/>
        <result column="rule_status" jdbcType="TINYINT" property="ruleStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="push_rule" jdbcType="LONGVARCHAR" property="pushRule"/>
    </resultMap>


    <select id="selectList" resultMap="BaseResultMap">
        select * from push_port_rule
        <where>

            is_delete = 0


            <if test="appointType != null ">
                AND  appoint_type = #{appointType}
            </if>


            <if test="orderTag != null ">
                AND  order_tag = #{orderTag}
            </if>

            <if test="lv1ServeId != null and lv1ServeId > 0">
                AND FIND_IN_SET(#{lv1ServeId},lv_1_serve_ids)
            </if>



            order by update_time desc
        </where>
    </select>


    <select id="selectByCityAndLv1ServeId" resultMap="BaseResultMap">

        select * from push_port_rule
        where is_delete = 0


        <if test="null != orderTagList and orderTagList.size() > 0">
            AND `order_tag` in
            <foreach collection="orderTagList" item="orderTag" open="(" separator="," close=")">
                #{orderTag}
            </foreach>
        </if>

        <if test="cityIdList != null and cityIdList.size() > 0">
            AND (
            <foreach collection="cityIdList" item="cityId" separator="OR" index="index">
                FIND_IN_SET(#{cityId},city_ids)
            </foreach>
            )
        </if>
        <if test="lv1ServeIdList != null and lv1ServeIdList.size() > 0">
            AND (
            <foreach collection="lv1ServeIdList" item="lv1ServeId" separator="OR" index="index">
                FIND_IN_SET(#{lv1ServeId},lv_1_serve_ids)
            </foreach>
            )
        </if>

        <if test="appointTypeList != null and appointTypeList.size() > 0">
            AND (
            <foreach collection="appointTypeList" item="appointType" separator="OR" index="index">
                FIND_IN_SET(#{appointType},appoint_type)
            </foreach>
            )
        </if>

        <if test="ruleId != null ">
            AND rule_id != #{ruleId}
        </if>

        <if test="orderTag != null and orderTag !='' ">
            AND order_tag = #{orderTag}
        </if>

        limit 1
    </select>



</mapper>