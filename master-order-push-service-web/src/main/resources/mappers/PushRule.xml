<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushRule">
        <id column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc"/>
        <result column="push_rule_list" jdbcType="LONGVARCHAR" property="pushRuleList"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="new_push_rule_list" jdbcType="LONGVARCHAR" property="newPushRuleList"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>


    <select id="selectList" resultMap="BaseResultMap">

        select * from push_rule where is_delete = 0

        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>

        <if test="ruleName != null and ruleName !=''">
            AND rule_name like CONCAT( '%', #{ruleName},'%')
        </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            order by update_time desc
    </select>



</mapper>