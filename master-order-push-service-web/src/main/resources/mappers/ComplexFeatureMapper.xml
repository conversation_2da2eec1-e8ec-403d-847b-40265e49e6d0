<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.ComplexFeatureMapper">

    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.ComplexFeature">
        <id column="feature_id" jdbcType="INTEGER" property="featureId"/>
        <result column="feature_code" jdbcType="VARCHAR" property="featureCode"/>
        <result column="feature_name" jdbcType="VARCHAR" property="featureName"/>
        <result column="feature_for" jdbcType="VARCHAR" property="featureFor"/>
        <result column="calculate_expression" jdbcType="VARCHAR" property="calculateExpression"/>
        <result column="feature_dependency" jdbcType="VARCHAR" property="featureDependency"/>
        <result column="dependency_check" jdbcType="VARCHAR" property="dependencyCheck"/>
        <result column="field_type" jdbcType="VARCHAR" property="fieldType"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>



</mapper>