<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderPushMasterMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderPushMaster">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="push_id" jdbcType="BIGINT" property="pushId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="appoint_type" jdbcType="INTEGER" property="appointType"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="offer_number" jdbcType="INTEGER" property="offerNumber"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="offer_time" jdbcType="TIMESTAMP" property="offerTime"/>
        <result column="first_pull_time" jdbcType="TIMESTAMP" property="firstPullTime"/>
        <result column="first_view_time" jdbcType="TIMESTAMP" property="firstViewTime"/>
        <result column="stop_offer_time" jdbcType="TIMESTAMP" property="stopOfferTime"/>
        <result column="according_technology_push_flag" jdbcType="INTEGER" property="accordingTechnologyPushFlag"/>

    </resultMap>

    <select id="getOrderPushMasterList" resultMap="BaseResultMap">
            SELECT * FROM ${tableName} WHERE order_id = #{orderId}
    </select>


    <select id="getMinId" resultType="Long">
        SELECT push_id FROM ${tableName} order by push_time asc limit 1
    </select>


    <select id="getMaxId" resultType="Long">
        SELECT push_id FROM ${tableName} where push_time &lt; #{pushTimeEnd} order by push_time desc limit 1
    </select>




    <select id="rangeDeleteById" resultType="Integer">
        DELETE FROM ${tableName} where push_id BETWEEN #{minPushId} AND #{maxPushId}
    </select>






</mapper>