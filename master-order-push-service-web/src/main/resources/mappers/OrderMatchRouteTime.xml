<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderMatchRouteTimeMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime">
        <id column="route_time_id" jdbcType="INTEGER" property="routeTimeId"/>
        <id column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="appoint_types" jdbcType="VARCHAR" property="appointTypes"/>
        <result column="setting_type" jdbcType="VARCHAR" property="settingType"/>
        <result column="setting_time" jdbcType="INTEGER" property="settingTime"/>
        <result column="setting_num" jdbcType="INTEGER" property="settingNum"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>


    <select id="selectList" resultMap="BaseResultMap">
        select * from order_match_route_time where business_line_id = #{businessLineId}

        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>

        <if test="settingType != null and settingType !=''">
            AND setting_type = #{settingType}
        </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            and is_delete = 0
            order by update_time desc
    </select>



    <select id="selectByCategoryIdAndAppointType" resultMap="BaseResultMap">

        select * from order_match_route_time where is_delete = 0
                                               AND business_line_id = #{businessLineId}

            AND FIND_IN_SET(#{categoryId},category_ids) AND

            FIND_IN_SET(#{appointType},appoint_types)

        order by update_time desc
    </select>



</mapper>