<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.AgentInfoMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.AgentInfo">
        <id column="agent_id" jdbcType="BIGINT" property="agentId"/>
        <id column="agent_name" jdbcType="VARCHAR" property="agentName"/>
        <result column="city_division_id" jdbcType="BIGINT" property="cityDivisionId"/>
        <result column="division_id" jdbcType="BIGINT" property="divisionId"/>
        <result column="serve_ids" jdbcType="BIGINT" property="serveIds"/>
        <result column="use_status" jdbcType="INTEGER" property="useStatus"/>
        <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        select * from t_agent_info
        <where>
            1 = 1
            <if test="agentName != null and agentName !=''">
                AND agent_name like CONCAT( '%', #{agentName},'%')
            </if>

            <if test="masterSourceType != null and masterSourceType =='toc'">
                AND master_source_type = #{masterSourceType}
            </if>

            <if test="masterSourceType != null and masterSourceType =='tob'">
                AND (master_source_type = #{masterSourceType} OR master_source_type is null)
            </if>

            order by update_time desc
        </where>
    </select>


</mapper>