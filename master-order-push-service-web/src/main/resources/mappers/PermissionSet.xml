<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PermissionSetMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PermissionSet">
        <id column="permission_set_id" jdbcType="INTEGER" property="permissionSetId"/>
        <id column="permission_set_name" jdbcType="VARCHAR" property="permissionSetName"/>
        <result column="permission_set_desc" jdbcType="VARCHAR" property="permissionSetDesc"/>
        <result column="permission_set" jdbcType="VARCHAR" property="permissionSet"/>
        <result column="role_list" jdbcType="VARCHAR" property="roleList"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        select * from permission_set where  is_delete = 0
            <if test="permissionSetName != null and permissionSetName !=''">
                AND permission_set_name like CONCAT( '%', #{permissionSetName},'%')
            </if>

            <if test="createTimeStart != null ">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null ">
                AND create_time <![CDATA[ <= ]]> #{createTimeEnd}
            </if>

        <if test="roleIdList!= null and roleIdList.size() > 0">
            AND (
            <foreach collection="roleIdList" item="roleId" separator="OR" index="index">
                FIND_IN_SET(#{roleId},role_list)
            </foreach>
            )
        </if>

            order by update_time desc
    </select>


</mapper>