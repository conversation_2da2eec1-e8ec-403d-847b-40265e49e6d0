<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderRoutingStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy">
        <id column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="order_tag" jdbcType="BIGINT" property="orderTag"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="city_ids" jdbcType="VARCHAR" property="cityIds"/>
        <result column="match_routing_id" jdbcType="INTEGER" property="matchRoutingId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="strategy_status" jdbcType="TINYINT" property="strategyStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from order_routing_strategy
        <where>

            <if test="businessLineId != null">
                AND business_line_id = #{businessLineId}
            </if>

            <if test="strategyStatus != null">
                AND strategy_status = #{strategyStatus}
            </if>

            <if test="strategyName != null and strategyName !=''">
                AND strategy_name like CONCAT( '%', #{strategyName},'%')
            </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND (
                <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                    FIND_IN_SET(#{categoryId},category_ids)
                </foreach>
                )
            </if>
            <if test="cityId != null and cityId !=''">
                AND FIND_IN_SET(#{cityId},city_ids)
            </if>
            and is_delete = 0
            order by update_time desc
        </where>
    </select>


    <select id="selectByCityAndCategory" resultMap="BaseResultMap">

        select * from order_routing_strategy
        where is_delete = 0
        <if test="cityIdList != null and cityIdList.size() > 0">
            AND (
            <foreach collection="cityIdList" item="cityId" separator="OR" index="index">
                FIND_IN_SET(#{cityId},city_ids)
            </foreach>
            )
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="strategyId != null ">
            AND strategy_id != #{strategyId}
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>

        limit 1
    </select>


    <select id="selectRoutingStrategy" resultMap="BaseResultMap">

        select * from order_routing_strategy
        where is_delete = 0
        <if test="cityIdList != null and cityIdList.size() > 0">
            AND (
            <foreach collection="cityIdList" item="cityId" separator="OR" index="index">
                FIND_IN_SET(#{cityId},city_ids)
            </foreach>
            )
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="orderTag != null ">
            AND order_tag = #{orderTag}
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>

        limit 1
    </select>



    <select id="selectStrategy" resultMap="BaseResultMap">
        select * from order_routing_strategy
        where is_delete = 0
        <if test="cityId != null and cityId != ''">
            AND FIND_IN_SET(#{cityId},city_ids)
        </if>
        <if test="categoryId != null and categoryId != ''">
            AND FIND_IN_SET(#{categoryId},category_ids)
        </if>

        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>

        <if test="orderTag != null and orderTag != ''">
            AND order_tag = #{orderTag}
        </if>
        limit 1
    </select>



</mapper>