<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.RepushPolicyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.RepushPolicy">
        <id column="policy_id" jdbcType="BIGINT" property="policyId"/>
        <result column="policy_name" jdbcType="VARCHAR" property="policyName"/>
        <result column="policy_desc" jdbcType="VARCHAR" property="policyDesc"/>
        <result column="city_ids" jdbcType="VARCHAR" property="cityIds"/>
        <result column="snapshot_id" jdbcType="BIGINT" property="snapshotId"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="strategy_combination" jdbcType="LONGVARCHAR" property="strategyCombination"/>
        <result column="policy_status" jdbcType="TINYINT" property="policyStatus"/>
        <result column="business_line_id" jdbcType="TINYINT" property="businessLineId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order_flag" jdbcType="VARCHAR" property="orderFlag"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
    </resultMap>
    <sql id="Base_Column_List">policyId, policyName, policyDesc, cityIds, strategyCombination, policyStatus, businessLineId, isDelete, createTime, updateTime </sql>
    <select id="selectByCityAndCategory" resultMap="BaseResultMap">

        select * from repush_policy
        where is_delete = 0
        <if test="cityIdStr != null and cityIdStr !=''">
            and MATCH(city_ids) against(#{cityIdStr})
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="policyId != null ">
            AND policy_id != #{policyId}
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="orderFlag != null and orderFlag !='' ">
            AND order_flag = #{orderFlag}
        </if>
        limit 1
    </select>

    <select id="selectByCategoryIdAndCityId" resultMap="BaseResultMap">
        select snapshot_id,strategy_combination,version
        from repush_policy
        where MATCH(city_ids) against(#{cityId}) and
        FIND_IN_SET(#{categoryId},category_ids)
        and policy_status = 1 AND is_delete = 0
        <choose>
            <when test="businessLineId == 1 or businessLineId == 3">
                and business_line_id = 1 and order_flag = #{orderFlag}
            </when>
            <otherwise>
                and business_line_id = #{businessLineId}
            </otherwise>
        </choose>
    </select>
</mapper>