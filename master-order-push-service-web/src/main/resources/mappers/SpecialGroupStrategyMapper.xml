<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.SpecialGroupStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy">
        <id column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="serve_ids" jdbcType="LONGVARCHAR" property="serveIds"/>
        <result column="region_level" jdbcType="VARCHAR" property="regionLevel"/>
        <result column="city_ids" jdbcType="LONGVARCHAR" property="cityIds"/>
        <result column="push_groups" jdbcType="LONGVARCHAR" property="pushGroups"/>
        <result column="serve_models" jdbcType="VARCHAR" property="serveModels"/>
        <result column="delay_minutes" jdbcType="INTEGER" property="delayMinutes"/>
        <result column="filter_groups" jdbcType="LONGVARCHAR" property="filterGroups"/>
        <result column="strategy_status" jdbcType="INTEGER" property="strategyStatus"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM special_group_strategy
        <where>
            is_delete = 0
            <if test="strategyName != null and strategyName != ''">
                AND strategy_name LIKE CONCAT('%', #{strategyName}, '%')
            </if>
            <if test="cityId != null and cityId > 0">
                AND FIND_IN_SET(#{cityId}, city_ids)
            </if>
            <if test="serveIdList != null and serveIdList.size() > 0">
                AND (
                <foreach collection="serveIdList" item="serveId" separator="OR" index="index">
                    FIND_IN_SET(#{serveId}, serve_ids)
                </foreach>
                )
            </if>
            <if test="strategyStatus != null">
                AND strategy_status = #{strategyStatus}
            </if>
            <if test="createStartTime != null">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null">
                AND create_time &lt;= #{createEndTime}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectByCityAndServe" resultMap="BaseResultMap">
        SELECT * FROM special_group_strategy
        WHERE is_delete = 0
        AND strategy_status = 1
        AND (
            region_level = 'country'
            OR
            (region_level = 'city'
                <if test="cityId != null and cityId != ''">
                    AND FIND_IN_SET(#{cityId}, city_ids) > 0
                </if>
                <if test="cityId == null or cityId == ''">
                    AND 1 = 0  <!-- 如果region_level为city但没有传cityId，则不返回记录 -->
                </if>
            )
        )
        <if test="serveModel != null and serveModel != ''">
            AND FIND_IN_SET(#{serveModel}, serve_models) > 0
        </if>
        <if test="serveIdList != null and serveIdList.size() > 0">
            AND (
            <foreach collection="serveIdList" item="serveId" separator="OR" index="index">
                FIND_IN_SET(#{serveId}, serve_ids) > 0
            </foreach>
            )
        </if>
    </select>
</mapper>
