<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.RoleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.Role">
        <id column="role_id" jdbcType="INTEGER" property="roleId"/>
        <id column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_desc" jdbcType="VARCHAR" property="roleDesc"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        select * from role where  is_delete = 0
            <if test="roleName != null and roleName !=''">
                AND role_name like CONCAT( '%', #{roleName},'%')
            </if>

            <if test="createTimeStart != null ">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null ">
                AND create_time <![CDATA[ <= ]]> #{createTimeEnd}
            </if>
            order by update_time desc
    </select>


</mapper>