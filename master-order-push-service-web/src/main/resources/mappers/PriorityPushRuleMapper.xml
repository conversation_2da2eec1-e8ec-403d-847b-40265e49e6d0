<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PriorityPushRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PriorityPushRule">
        <id column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc"/>
<!--        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>-->
<!--        <result column="appoint_type" jdbcType="VARCHAR" property="appointType"/>-->
        <result column="category_ids" jdbcType="LONGVARCHAR" property="categoryIds"/>
        <result column="city_ids" jdbcType="LONGVARCHAR" property="cityIds"/>
        <result column="push_groups" jdbcType="LONGVARCHAR" property="pushGroups"/>
        <result column="push_groups_expression" jdbcType="LONGVARCHAR" property="pushGroupsExpression"/>
        <result column="push_rule" jdbcType="LONGVARCHAR" property="pushRule"/>
        <result column="push_rule_expression" jdbcType="LONGVARCHAR" property="pushRuleExpression"/>
        <result column="rule_status" jdbcType="TINYINT" property="ruleStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
    </resultMap>


    <select id="selectList" resultMap="BaseResultMap">
        select * from priority_push_rule
        <where>

            is_delete = 0

            <if test="businessLineId != null">
                AND business_line_id = #{businessLineId}
            </if>

            <if test="ruleName != null and ruleName !=''">
                AND rule_name like CONCAT( '%', #{ruleName},'%')
            </if>


            <if test="cityId != null ">
                AND  FIND_IN_SET(#{cityId},city_ids)
            </if>

<!--            <if test="orderFrom != null and orderFrom !=''">-->
<!--                AND  FIND_IN_SET(#{orderFrom},order_from)-->
<!--            </if>-->

            <if test="cityId != null ">
                AND  FIND_IN_SET(#{cityId},city_ids)
            </if>


<!--            <if test="appointType != null ">-->
<!--                AND  FIND_IN_SET(#{appointType},appoint_type)-->
<!--            </if>-->
--

            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND (
                <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                    FIND_IN_SET(#{categoryId},category_ids)
                </foreach>
                )
            </if>


            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>

            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>


            order by update_time desc
        </where>
    </select>


    <select id="selectByCategoryIdAndCityId" resultMap="BaseResultMap">
        select rule_id,push_groups_expression,push_rule_expression
        from priority_push_rule
        where FIND_IN_SET(#{cityId},city_ids) and
        FIND_IN_SET(#{categoryId},category_ids)
        and rule_status = 1 AND is_delete = 0
        <choose>
            <when test="businessLineId == 1 or businessLineId == 3">
                and business_line_id = 1
            </when>
            <otherwise>
                and business_line_id = #{businessLineId}
            </otherwise>
        </choose>

    </select>

</mapper>