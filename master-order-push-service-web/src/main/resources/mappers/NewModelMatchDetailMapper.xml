<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.NewModelMatchDetailMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.NewModelMatchDetail">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="master_id" jdbcType="INTEGER" property="masterId"/>
        <result column="is_main_master" jdbcType="INTEGER" property="isMainMaster"/>
        <result column="is_filter_out" jdbcType="INTEGER" property="isFilterOut"/>
        <result column="filter_remark" jdbcType="VARCHAR" property="filterRemark"/>
        <result column="is_distribute" jdbcType="INTEGER" property="isDistribute"/>
        <result column="distribute_rule" jdbcType="VARCHAR" property="distributeRemark"/>
        <result column="distribute_result" jdbcType="INTEGER" property="distributeResult"/>
        <result column="distribute_fail_remark" jdbcType="VARCHAR" property="distributeFailRemark"/>
        <result column="abandon_time" jdbcType="TIMESTAMP" property="abandonTime"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from new_model_match_detail
        <where>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="masterId != null">
                and master_id = #{masterId}
            </if>
            <if test="orderCreateTimeStart != null">
                AND order_create_time >= #{orderCreateTimeStart}
            </if>
            <if test="orderCreateTimeEnd != null">
                AND order_create_time <![CDATA[ <= ]]> #{orderCreateTimeEnd}
            </if>
        </where>
        order by order_create_time desc, id desc
    </select>


</mapper>