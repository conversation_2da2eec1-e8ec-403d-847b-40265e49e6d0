<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.CompensateDistributeMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.CompensateDistribute">
        <id column="distribute_id" jdbcType="INTEGER" property="distributeId"/>
        <result column="business_line_id" jdbcType="VARCHAR" property="businessLineId"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="appoint_types" jdbcType="VARCHAR" property="appointTypes"/>
        <result column="order_push_flag" jdbcType="VARCHAR" property="orderPushFlag"/>
        <result column="has_price" jdbcType="VARCHAR" property="hasPrice"/>
        <result column="has_cooperation_user" jdbcType="LONGVARCHAR" property="hasCooperationUser"/>
        <result column="compensate_type" jdbcType="VARCHAR" property="compensateType"/>
        <result column="interval_time" jdbcType="INTEGER" property="intervalTime"/>
        <result column="trigger_num" jdbcType="INTEGER" property="triggerNum"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">

        select * from compensate_distribute where is_delete = 0

        <if test="businessLineId != null">
            AND business_line_id = #{businessLineId}
        </if>
                                              
        <if test="categoryId != null">
            AND FIND_IN_SET(#{categoryId},category_ids)
        </if>

        <if test=" compensateType != null and compensateType != ''">
            AND compensate_type = #{compensateType}
        </if>

        <if test=" orderPushFlag != null and orderPushFlag != ''">
            AND order_push_flag = #{orderPushFlag}
        </if>
        order by update_time desc
    </select>


    <select id="selectByCategoryIdAndAppointType" resultMap="BaseResultMap">

        select * from compensate_distribute where is_delete = 0
        <if test="businessLineId != null">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="orderPushFlag != null and orderPushFlag != ''">
            AND order_push_flag = #{orderPushFlag}
        </if>
        <if test="categoryId != null">
            AND FIND_IN_SET(#{categoryId},category_ids)
        </if>

        <if test=" appointType != null">
            AND FIND_IN_SET(#{appointType},appoint_types)
        </if>

        <if test=" hasPrice != null">
            AND has_price = #{hasPrice}
        </if>
        <if test=" hasCooperationUser != null">
            AND has_cooperation_user = #{hasCooperationUser}
        </if>

    </select>



    <select id="selectByCategoryIdAndCompensateType" resultMap="BaseResultMap">

        select * from compensate_distribute where is_delete = 0
        <if test="businessLineId != null">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="orderPushFlag != null and orderPushFlag != ''">
            AND order_push_flag = #{orderPushFlag}
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>

        <if test="appointTypeList != null and appointTypeList.size() > 0">
            AND (
            <foreach collection="appointTypeList" item="appointType" separator="OR" index="index">
                FIND_IN_SET(#{appointType},appoint_types)
            </foreach>
            )
        </if>


        <if test=" hasPrice != null">
            AND has_price = #{hasPrice}
        </if>
        <if test=" hasCooperationUser != null">
            AND has_cooperation_user = #{hasCooperationUser}
        </if>

        <if test=" compensateType != null">
            AND compensate_type = #{compensateType}
        </if>



        <if test="distributeId != null ">
            AND distribute_id != #{distributeId}
        </if>

    </select>


</mapper>