<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.LongTailStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.LongTailStrategy">
        <id column="long_tail_strategy_id" jdbcType="BIGINT" property="longTailStrategyId"/>
        <result column="long_tail_strategy_name" jdbcType="VARCHAR" property="longTailStrategyName"/>
        <result column="long_tail_strategy_desc" jdbcType="VARCHAR" property="longTailStrategyDesc"/>
        <result column="push_type" jdbcType="VARCHAR" property="pushType"/>
        <result column="range_select" jdbcType="VARCHAR" property="rangeSelect"/>
        <result column="status_select" jdbcType="VARCHAR" property="statusSelect"/>
        <result column="strategy_json" jdbcType="LONGVARCHAR" property="strategyJson"/>
        <result column="strategy_expression" jdbcType="LONGVARCHAR" property="strategyExpression"/>
        <result column="filter_rule_expression" jdbcType="LONGVARCHAR" property="filterRuleExpression"/>
        <result column="filter_rule" jdbcType="LONGVARCHAR" property="filterRule"/>
        <result column="push_rule" jdbcType="LONGVARCHAR" property="pushRule"/>
        <result column="appoint_group_expression" jdbcType="LONGVARCHAR" property="appointGroupExpression"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="is_active" jdbcType="TINYINT" property="isActive"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from long_tail_strategy_base
        <where>
            <if test="longTailStrategyIdList != null and longTailStrategyIdList.size() > 0">
                AND `long_tail_strategy_id` in
                <foreach collection="longTailStrategyIdList" item="longTailStrategyIdTemp" open="(" separator="," close=")">
                    #{longTailStrategyIdTemp}
                </foreach>
            </if>
            <if test="businessLineId != null">
                AND business_line_id= #{businessLineId}
            </if>
            <if test="longTailStrategyName != null and longTailStrategyName !=''">
                AND long_tail_strategy_name like CONCAT( '%', #{longTailStrategyName},'%')
            </if>
            <if test="isActive != null ">
                AND is_active= #{isActive}
            </if>
            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
            and is_delete = 0
            order by update_time desc
        </where>
    </select>
</mapper>