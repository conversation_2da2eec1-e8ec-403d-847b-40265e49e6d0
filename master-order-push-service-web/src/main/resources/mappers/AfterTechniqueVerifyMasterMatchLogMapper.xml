<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.AfterTechniqueVerifyMasterMatchLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.AfterTechniqueVerifyMasterMatchLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="master_id" jdbcType="INTEGER" property="masterId"/>
        <result column="is_match_success" jdbcType="INTEGER" property="isMatchSuccess"/>
        <result column="match_fail_reason" jdbcType="VARCHAR" property="matchFailReason"/>
        <result column="is_filter" jdbcType="INTEGER" property="isFilter"/>
        <result column="filter_reason" jdbcType="VARCHAR" property="filterReason"/>
        <result column="is_distribute" jdbcType="INTEGER" property="isDistribute"/>
        <result column="distribute_rule" jdbcType="VARCHAR" property="distributeRule"/>
        <result column="is_auto_grab_success" jdbcType="INTEGER" property="isAutoGrabSuccess"/>
        <result column="auto_grab_fail_reason" jdbcType="VARCHAR" property="autoGrabFailReason"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


</mapper>