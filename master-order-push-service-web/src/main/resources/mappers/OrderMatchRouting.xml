<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderMatchRoutingMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderMatchRouting">
        <id column="routing_id" jdbcType="INTEGER" property="routingId"/>
        <result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
        <result column="routing_desc" jdbcType="VARCHAR" property="routingDesc"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="order_tag" jdbcType="VARCHAR" property="orderTag"/>
        <result column="routing_type" jdbcType="VARCHAR" property="routingType"/>
        <result column="match_routing" jdbcType="VARCHAR" property="matchRouting"/>
        <result column="lv1_master_type" jdbcType="VARCHAR" property="lv1MasterType"/>
        <result column="lv2_master_type" jdbcType="VARCHAR" property="lv2MasterType"/>
        <result column="lv3_master_type" jdbcType="VARCHAR" property="lv3MasterType"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>


    <select id="selectList" resultMap="BaseResultMap">
        select * from order_match_routing where is_delete = 0

            <if test="routingName != null and routingName !=''">
                AND routing_name like CONCAT( '%', #{routingName},'%')
            </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            order by update_time desc
    </select>



</mapper>