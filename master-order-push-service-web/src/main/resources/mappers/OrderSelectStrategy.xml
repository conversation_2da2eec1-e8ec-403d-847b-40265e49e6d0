<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderSelectStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderSelectStrategy">
        <id column="strategy_id" jdbcType="INTEGER" property="strategyId"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="order_from" jdbcType="VARCHAR" property="orderFrom"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="select_strategy" jdbcType="VARCHAR" property="selectStrategy"/>
        <result column="master_resources" jdbcType="VARCHAR" property="masterResources"/>
        <result column="select_strategy_expression" jdbcType="VARCHAR" property="selectStrategyExpression"/>
        <result column="select_rule" jdbcType="VARCHAR" property="selectRule"/>
        <result column="select_rule_expression" jdbcType="VARCHAR" property="selectRuleExpression"/>
        <result column="strategy_status" jdbcType="INTEGER" property="strategyStatus"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        select * from order_select_strategy where business_line_id = #{businessLineId}
            <if test="strategyName != null and strategyName !=''">
                AND strategy_name like CONCAT( '%', #{strategyName},'%')
            </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

            <if test="strategyStatus != null ">
               AND strategy_status= #{strategyStatus}
           </if>

        <if test="masterResources != null ">
            AND master_resources= #{masterResources}
        </if>

            and is_delete = 0
            order by update_time desc
    </select>


</mapper>