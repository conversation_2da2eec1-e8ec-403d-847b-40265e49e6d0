<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.StrategyCombinationSimulateMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate">
        <id column="simulate_id" jdbcType="BIGINT" property="simulateId"/>
        <result column="strategy_combination_id" jdbcType="BIGINT" property="strategyCombinationId"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="simulated_order_num" jdbcType="INTEGER" property="simulatedOrderNum"/>
        <result column="simulate_status" jdbcType="INTEGER" property="simulateStatus"/>
        <result column="simulate_time" jdbcType="TIMESTAMP" property="simulateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">simulateId, strategyCombinationId, orderNum, simulatedOrderNum, simulateStatus, simulateTime, createTime, updateTime </sql>


    <select id="selectLatestSimulate" resultMap="BaseResultMap">
        SELECT *  FROM strategy_combination_simulate WHERE strategy_combination_id=#{strategyCombinationId} order by simulate_time desc limit 1
    </select>

    <update id="addSimulatedOrderNum">
 UPDATE strategy_combination_simulate
SET simulate_status =
IF (
	simulated_order_num + 1 = order_num,
	2,
	simulate_status
),
 simulated_order_num = simulated_order_num + 1
WHERE
	simulate_id = #{simulateId}
    </update>

</mapper>