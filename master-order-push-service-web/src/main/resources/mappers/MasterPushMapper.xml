<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.MasterPushMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.MasterPush">
        <id column="push_id" jdbcType="BIGINT" property="pushId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="master_type" jdbcType="VARCHAR" property="masterType"/>
        <result column="list_offset" jdbcType="INTEGER" property="listOffset"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="score" jdbcType="DOUBLE" property="score"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getMasterPushList" resultMap="BaseResultMap">
            SELECT * FROM master_push_${timeMark} WHERE order_id = #{orderId} AND order_version = #{orderVersion} AND master_type = #{masterType} AND list_offset &gt;= #{beginOffset} AND list_offset &lt; #{endOffset}
    </select>


    <select id="getMasterPushListByPushOffset" resultMap="BaseResultMap">
        SELECT * FROM master_push_${timeMark} WHERE order_id = #{orderId} AND order_version = #{orderVersion} AND push_offset &gt;= #{beginOffset} AND push_offset &lt; #{endOffset}
    </select>


    <insert id="insertMasterPushList">
        insert into master_push_${timeMark}(order_id, order_version, master_type, list_offset,push_offset,master_id,score)
        values
        <foreach collection="masterPushList" item="masterPush" separator=",">
            (#{masterPush.orderId}, #{masterPush.orderVersion}, #{masterPush.masterType}, #{masterPush.listOffset},#{masterPush.pushOffset},#{masterPush.masterId},#{masterPush.score})
        </foreach>
    </insert>


    <select id="selectByMasterIdList" resultType="String">
        SELECT master_id FROM master_push_${timeMark} WHERE order_id = #{orderId} AND order_version = #{orderVersion} AND master_type = #{masterType} AND list_offset &lt; #{offset} AND master_id IN
        <foreach collection="masterIdSet" index="index" item="masterId" open="(" separator="," close=")">
            #{masterId}
        </foreach>
    </select>
    <select id="selectByOrderIdAndMasterIds" resultType="java.lang.Long">
        SELECT master_id FROM master_push_${timeMark}
        WHERE order_id = #{orderId}
        AND master_id IN
        <foreach collection="masterIdSet" index="index" item="masterId" open="(" separator="," close=")">
            #{masterId}
        </foreach>
    </select>


    <update id="createMasterPushTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
            `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
            `order_version` varchar(20) NOT NULL COMMENT '订单版本',
            `master_type` varchar(10) NOT NULL COMMENT '师傅类型',
            `list_offset` bigint(20) DEFAULT '0' COMMENT '推送进度位点',
            `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
            `score` decimal(10,2) NOT NULL COMMENT '分数',
            `push_offset` bigint(20) DEFAULT '0',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`push_id`),
            KEY `master_list` (`order_id`,`order_version`,`master_type`,`list_offset`),
            KEY `push_master_list` (`order_id`,`order_version`,`push_offset`),
            KEY `master_id` (`master_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
    </update>

    <update id="dropHistoryPushTable" parameterType="String">
        DROP TABLE IF EXISTS ${tableName};
    </update>


    <select id="selectPushScore" resultMap="BaseResultMap">
        SELECT master_id,score FROM master_push_${timeMark}
        WHERE order_id = #{orderId} AND order_version = #{orderVersion}
        AND master_id IN
        <foreach collection="masterIdList" index="index" item="masterId" open="(" separator="," close=")">
            #{masterId}
        </foreach>
    </select>


</mapper>