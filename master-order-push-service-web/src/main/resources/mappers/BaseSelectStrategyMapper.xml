<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.BaseSelectStrategyMapper">
<!--    <resultMap id="BaseResultMap" type="com.wanshifu.master.information.domain.po.MasterOrderServeRelation">-->
<!--        <id column="id" jdbcType="INTEGER" property="id"/>-->
<!--        <result column="server_category_id" jdbcType="INTEGER" property="serverCategoryId"/>-->
<!--        <result column="master_server_id" jdbcType="VARCHAR" property="masterServerId"/>-->
<!--        <result column="order_server_id" jdbcType="INTEGER" property="orderServerId"/>-->
<!--        <result column="rela_status" jdbcType="TINYINT" property="relaStatus"/>-->
<!--        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>-->
</mapper>