<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.AgreementOrderDistributeStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.AgreementOrderDistributeStrategy">
        <id column="strategy_id" jdbcType="INTEGER" property="strategyId"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="strategy_desc" jdbcType="VARCHAR" property="strategyDesc"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="city_ids" jdbcType="LONGVARCHAR" property="cityIds"/>
        <result column="distribute_strategy_list" jdbcType="LONGVARCHAR" property="distributeStrategyList"/>
        <result column="distribute_strategy_expression_list" jdbcType="LONGVARCHAR" property="distributeStrategyExpressionList"/>
        <result column="compensate_distribute_strategy_list" jdbcType="LONGVARCHAR" property="compensateDistributeStrategyList"/>
        <result column="compensate_distribute_list" jdbcType="LONGVARCHAR" property="compensateDistributeList"/>
        <result column="strategy_status" jdbcType="TINYINT" property="strategyStatus"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>



    <select id="selectList" resultMap="BaseResultMap">

        select * from agreement_order_distribute_strategy
        where business_line_id = #{businessLineId}
        <if test="strategyName != null and strategyName !=''">
            AND strategy_name like CONCAT( '%', #{strategyName},'%')
        </if>

        <if test="categoryIds != null ">
            AND FIND_IN_SET(#{categoryIds},category_ids)
        </if>

        <if test="cityIds != null ">
            AND FIND_IN_SET(#{cityIds},city_ids)
        </if>

        <if test="createStartTime != null ">
            AND create_time >= #{createStartTime}
        </if>
        <if test="createEndTime != null ">
            AND create_time <![CDATA[ <= ]]> #{createEndTime}
        </if>

        <if test="strategyStatus != null ">
            AND strategy_status= #{strategyStatus}
        </if>

        and is_delete = 0
        order by update_time desc

    </select>


    <select id="selectByCategoryIdAndCityId" resultMap="BaseResultMap">

        select * from agreement_order_distribute_strategy
        where business_line_id = #{businessLineId}

        <if test="categoryId != null ">
            AND FIND_IN_SET(#{categoryId},category_ids)
        </if>

        <if test="cityId != null ">
            AND FIND_IN_SET(#{cityId},city_ids)
        </if>


        AND strategy_status= 1
        and is_delete = 0
        order by update_time desc

    </select>



</mapper>