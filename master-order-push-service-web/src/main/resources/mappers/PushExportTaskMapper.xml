<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushExportTaskMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushExportTask">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="export_data_type_name" jdbcType="VARCHAR" property="exportDataTypeName"/>
        <result column="export_data_name" jdbcType="VARCHAR" property="exportDataName"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from push_export_task
        <where>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time <![CDATA[ <= ]]> #{createTimeEnd}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>