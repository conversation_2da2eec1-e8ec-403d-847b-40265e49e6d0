<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushHandleMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushHandle">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="base_select_master_num" jdbcType="INTEGER" property="baseSelectMasterNum"/>
        <result column="direct_push_num" jdbcType="INTEGER" property="directPushNum"/>
        <result column="filtered_master_num" jdbcType="INTEGER" property="filteredMasterNum"/>
        <result column="filter_message" jdbcType="VARCHAR" property="filterMessage"/>
        <result column="score_message" jdbcType="VARCHAR" property="scoreMessage"/>
        <result column="push_strategy" jdbcType="VARCHAR" property="pushStrategy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <update id="createPushHandleTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName} (
                                       `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                       `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
                                       `order_version` varchar(100) NOT NULL DEFAULT '' COMMENT '订单版本号',
                                       `base_select_master_num` int(11) NOT NULL DEFAULT '0' COMMENT '初筛师傅数',
                                       `direct_push_num` int(11) NOT NULL DEFAULT '0' COMMENT '强推师傅数',
                                       `filtered_master_num` int(11) NOT NULL DEFAULT '0' COMMENT '过滤师傅数',
                                       `filter_message` mediumtext NOT NULL COMMENT '过滤详情',
                                       `score_message` mediumtext NOT NULL COMMENT '评分详情',
                                       `push_strategy` varchar(200) DEFAULT '' COMMENT '推送策略',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_order_version` (`order_id`,`order_version`),
                                       KEY `idx_create_time` (`create_time`),
                                       KEY `idx_update_time` (`update_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    </update>


    <insert id="insertPushHandle">
        insert into push_handle_${timeMark}(order_id,order_version, base_select_master_num, direct_push_num,filtered_master_num,filter_message,score_message,push_strategy)
        values (#{pushHandle.orderId}, #{pushHandle.orderVersion}, #{pushHandle.baseSelectMasterNum}, #{pushHandle.directPushNum},#{pushHandle.filteredMasterNum},#{pushHandle.filterMessage},#{pushHandle.scoreMessage},#{pushHandle.pushStrategy})
    </insert>

    <update id="dropHistoryPushHandleTable" parameterType="String">
        DROP TABLE IF EXISTS ${tableName};
    </update>


</mapper>