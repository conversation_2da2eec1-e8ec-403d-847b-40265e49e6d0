<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.StrategyCombinationMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.StrategyCombination">
        <id column="combination_id" jdbcType="BIGINT" property="combinationId"/>
        <id column="snapshot_id" jdbcType="BIGINT" property="snapshotId"/>
        <result column="combination_name" jdbcType="VARCHAR" property="combinationName"/>
        <result column="combination_desc" jdbcType="VARCHAR" property="combinationDesc"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="city_ids" jdbcType="VARCHAR" property="cityIds"/>
        <result column="priority_strategy_combination" jdbcType="LONGVARCHAR" property="priorityStrategyCombination"/>
        <result column="alternate_strategy_combination" jdbcType="LONGVARCHAR" property="alternateStrategyCombination"/>
        <result column="combination_status" jdbcType="TINYINT" property="combinationStatus"/>
        <result column="business_line_id" jdbcType="TINYINT" property="businessLineId"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order_flag" jdbcType="VARCHAR" property="orderFlag"/>

    </resultMap>

    <select id="selectByCategoryIdAndCityId" resultMap="BaseResultMap">
        select snapshot_id,priority_strategy_combination,alternate_strategy_combination
        from strategy_combination
        where MATCH(city_ids) against(#{cityId}) and
        FIND_IN_SET(#{categoryId},category_ids)
        and combination_status = 1 AND is_delete = 0
        <choose>
            <when test="businessLineId == 1 or businessLineId == 3">
                and business_line_id = 1 and order_flag = #{orderFlag}
            </when>
            <otherwise>
                and business_line_id = #{businessLineId}
            </otherwise>
        </choose>

    </select>


    <select id="selectByCityAndCategory" resultMap="BaseResultMap">

        select * from strategy_combination
        where is_delete = 0
        <if test="cityIdStr != null and cityIdStr !=''">
            and MATCH(city_ids) against(#{cityIdStr})
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="combinationId != null ">
            AND combination_id != #{combinationId}
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="orderFlag != null and orderFlag !='' ">
            AND order_flag = #{orderFlag}
        </if>
        limit 1
    </select>

</mapper>