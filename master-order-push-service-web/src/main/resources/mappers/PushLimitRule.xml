<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushLimitRuleMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushLimitRule">
        <id column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="rule_desc" jdbcType="VARCHAR" property="ruleDesc"/>
        <result column="city_ids" jdbcType="LONGVARCHAR" property="cityIds"/>
        <result column="crowd_type" jdbcType="VARCHAR" property="crowdType"/>
        <result column="crowd_label" jdbcType="VARCHAR" property="crowdLabel"/>
        <result column="crowd_group" jdbcType="VARCHAR" property="crowdGroup"/>
        <result column="crowd_group_expression" jdbcType="LONGVARCHAR" property="crowdGroupExpression"/>
        <result column="limit_range" jdbcType="VARCHAR" property="limitRange"/>
        <result column="limit_serve_rule" jdbcType="LONGVARCHAR" property="limitServeRule"/>
        <result column="serve_rule_expression" jdbcType="LONGVARCHAR" property="serveRuleExpression"/>
        <result column="exclusive_rule" jdbcType="LONGVARCHAR" property="exclusiveRule"/>
        <result column="limit_rule" jdbcType="VARCHAR" property="limitRule"/>
        <result column="decrease_days" jdbcType="INTEGER" property="decreaseDays"/>
        <result column="decrease_percent" jdbcType="INTEGER" property="decreasePercent"/>
        <result column="fixed_decrease_percent" jdbcType="INTEGER" property="fixedDecreasePercent"/>
        <result column="rule_status" jdbcType="INTEGER" property="ruleStatus"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">

        select * from push_limit_rule where is_delete = 0


        <if test="cityId != null and cityId != ''">
            AND FIND_IN_SET(#{cityId},city_ids)
        </if>

        <if test="ruleName != null and ruleName !=''">
            AND rule_name like CONCAT( '%', #{ruleName},'%')
        </if>

        <if test="ruleStatus != null">
            AND rule_status =  #{ruleStatus}
        </if>

            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>

            order by update_time desc
    </select>


    <select id="selectByCityIdAndBusinessLineId" resultMap="BaseResultMap">

        select * from push_limit_rule where is_delete = 0

        <if test="ruleId != null and ruleId > 0">
            AND rule_id != #{ruleId}
        </if>

        <if test="cityIdList != null and cityIdList.size() > 0">
            AND (
            <foreach collection="cityIdList" item="cityId" separator="OR" index="index">
                FIND_IN_SET(#{cityId},city_ids)
            </foreach>
            )
        </if>

        <if test="businessLineId != null">
            AND business_line_id = #{businessLineId}
        </if>

        <if test="crowdLabel != null">
            AND crowd_label =  #{crowdLabel}
        </if>

        </select>






</mapper>