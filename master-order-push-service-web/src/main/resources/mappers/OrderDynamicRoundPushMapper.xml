<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderDynamicRoundsPushMapper">

    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="global_order_id" jdbcType="BIGINT" property="globalOrderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="business_line_id" jdbcType="INTEGER" property="businessLineId"/>
        <result column="category_id" jdbcType="BIGINT" property="categoryId"/>
        <result column="second_division_id" jdbcType="BIGINT" property="secondDivisionId"/>
        <result column="third_division_id" jdbcType="BIGINT" property="thirdDivisionId"/>
        <result column="fourth_division_id" jdbcType="BIGINT" property="fourthDivisionId"/>
        <result column="order_from_type" jdbcType="VARCHAR" property="orderFromType"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="serve_type_id" jdbcType="INTEGER" property="serveTypeId"/>
        <result column="appoint_type" jdbcType="INTEGER" property="appointType"/>
        <result column="lack_type" jdbcType="VARCHAR" property="lackType"/>
        <result column="push_master_detail_list" jdbcType="VARCHAR" property="pushMasterDetailList"/>
        <result column="serve_level_1_ids" jdbcType="VARCHAR" property="serveLevel1Ids"/>
        <result column="customer_address" jdbcType="VARCHAR" property="customerAddress"/>
        <result column="push_master_num" jdbcType="INTEGER" property="pushMasterNum"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
   </resultMap>


    <select id="list" resultMap="BaseResultMap">

        select * from order_dynamic_rounds_push where

        <if test="businessLineId != null ">
          <choose>
              <when test="businessLineId == 1">
                  business_line_id in(1,3)
              </when>
              <when test="businessLineId == 2">
                  business_line_id = 2
              </when>
              <when test="businessLineId == 3">
                  business_line_id = 3
              </when>
              <when test="businessLineId == 999">
                  business_line_id = 999
              </when>
              <otherwise>

              </otherwise>
          </choose>
        </if>

        <if test="pushTimeStart != null ">
            AND push_time >= #{pushTimeStart}
        </if>
        <if test="pushTimeEnd != null ">
            AND push_time <![CDATA[ <= ]]> #{pushTimeEnd}
        </if>


        <if test="firstRoundsNumStart != null ">
            AND push_master_num >= #{firstRoundsNumStart}
        </if>
        <if test="firstRoundsNumEnd != null ">
            AND push_master_num <![CDATA[ <= ]]> #{firstRoundsNumEnd}
        </if>


        <if test="categoryId != null ">
            AND category_id = #{categoryId}
        </if>

        <if test="serveTypeList != null">
            AND `serve_type_id` in
            <foreach collection="serveTypeList" item="serveTypeTemp" open="(" separator="," close=")">
                #{serveTypeTemp}
            </foreach>
        </if>

        <if test="categoryList != null">
            AND `category_id` in
            <foreach collection="categoryList" item="categoryTemp" open="(" separator="," close=")">
                #{categoryTemp}
            </foreach>
        </if>

        <if test="appointTypeList != null">
            AND `appoint_type` in
            <foreach collection="appointTypeList" item="appointTypeTemp" open="(" separator="," close=")">
                #{appointTypeTemp}
            </foreach>
        </if>

        <if test="orderFromList != null">
            AND `order_from_type` in
            <foreach collection="orderFromList" item="orderFromTemp" open="(" separator="," close=")">
                #{orderFromTemp}
            </foreach>
        </if>


        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>


        <if test="divisionId != null ">
            AND (second_division_id = #{divisionId} OR third_division_id = #{divisionId} OR fourth_division_id = #{divisionId})
        </if>

        order by push_time desc

    </select>

</mapper>