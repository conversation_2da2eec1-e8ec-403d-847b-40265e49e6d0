<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderFullTimeMasterMatchLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderFullTimeMasterMasterMatchLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_create_time" jdbcType="TIMESTAMP" property="orderCreateTime"/>
        <result column="master_id" jdbcType="INTEGER" property="masterId"/>
        <result column="is_match_succ" jdbcType="INTEGER" property="isMatchSucc"/>
        <result column="match_fail_reason" jdbcType="VARCHAR" property="matchFailReason"/>
        <result column="is_filter" jdbcType="INTEGER" property="isFilter"/>
        <result column="filter_reason" jdbcType="VARCHAR" property="filterReason"/>
        <result column="is_distribute" jdbcType="INTEGER" property="isDistribute"/>
        <result column="distribute_rule" jdbcType="VARCHAR" property="distributeRule"/>
        <result column="is_auto_receive_succ" jdbcType="INTEGER" property="isAutoReceiveSucc"/>
        <result column="auto_receive_fail_reason" jdbcType="VARCHAR" property="autoReceiveFailReason"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from order_full_time_master_match_log
        <where>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="masterId != null">
                and master_id = #{masterId}
            </if>
            <if test="orderCreateTimeStart != null">
                AND order_create_time >= #{orderCreateTimeStart}
            </if>
            <if test="orderCreateTimeEnd != null">
                AND order_create_time <![CDATA[ <= ]]> #{orderCreateTimeEnd}
            </if>
        </where>
        order by order_create_time desc
    </select>


</mapper>