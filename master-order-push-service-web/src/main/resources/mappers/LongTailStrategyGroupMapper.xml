<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.LongTailStrategyGroupMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup">
        <id column="long_tail_strategy_group_id" jdbcType="BIGINT" property="longTailStrategyGroupId"/>
<!--        <result column="long_tail_strategy_id" jdbcType="BIGINT" property="longTailStrategyId"/>-->
        <result column="long_tail_strategy_group_name" jdbcType="VARCHAR" property="longTailStrategyGroupName"/>
        <result column="long_tail_strategy_group_desc" jdbcType="VARCHAR" property="longTailStrategyGroupDesc"/>
        <result column="business_line_id" jdbcType="BIGINT" property="businessLineId"/>
        <result column="push_type" jdbcType="VARCHAR" property="pushType"/>
        <result column="category_ids" jdbcType="VARCHAR" property="categoryIds"/>
        <result column="open_city_mode" jdbcType="VARCHAR" property="openCityMode"/>
        <result column="city_ids" jdbcType="VARCHAR" property="cityIds"/>
        <result column="strategy_json" jdbcType="LONGVARCHAR" property="strategyJson"/>
        <result column="strategy_expression" jdbcType="LONGVARCHAR" property="strategyExpression"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="is_active" jdbcType="TINYINT" property="isActive"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        select * from long_tail_strategy_group
        <where>
            <if test="businessLineId != null">
                AND business_line_id= #{businessLineId}
            </if>
            <if test="longTailStrategyGroupName != null and longTailStrategyGroupName !=''">
                AND long_tail_strategy_group_name like CONCAT( '%', #{longTailStrategyGroupName},'%')
            </if>
            <if test="isActive != null ">
                AND is_active= #{isActive}
            </if>
            <if test="createStartTime != null ">
                AND create_time >= #{createStartTime}
            </if>
            <if test="createEndTime != null ">
                AND create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND (
                <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                    FIND_IN_SET(#{categoryId},category_ids)
                </foreach>
                )
            </if>
            <if test="cityId != null and cityId !=''">
                and MATCH(city_ids) against(#{cityId})
            </if>
            and is_delete = 0
            order by update_time desc
        </where>
    </select>


    <select id="selectByCityAndCategory" resultMap="BaseResultMap">
        select * from long_tail_strategy_group
        where is_delete = 0
        <if test="cityIdStr != null and cityIdStr !=''">
            and MATCH(city_ids) against(#{cityIdStr})
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="longTailStrategyGroupId != null ">
            AND long_tail_strategy_group_id != #{longTailStrategyGroupId}
        </if>
        limit 1
    </select>


    <select id="selectByCityAndCategoryActive" resultMap="BaseResultMap">
        select * from long_tail_strategy_group
        where is_delete = 0 and is_active = 1
        <if test="cityIdStr != null and cityIdStr !=''">
            and MATCH(city_ids) against(#{cityIdStr})
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            AND (
            <foreach collection="categoryIdList" item="categoryId" separator="OR" index="index">
                FIND_IN_SET(#{categoryId},category_ids)
            </foreach>
            )
        </if>
        <if test="businessLineId != null ">
            AND business_line_id = #{businessLineId}
        </if>
        <if test="longTailStrategyGroupId != null ">
            AND long_tail_strategy_group_id != #{longTailStrategyGroupId}
        </if>
        limit 4
    </select>
</mapper>