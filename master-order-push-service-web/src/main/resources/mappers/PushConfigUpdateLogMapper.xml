<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.PushConfigUpdateLogMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.PushConfigUpdateLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="primary_key_id" jdbcType="INTEGER" property="primaryKeyId"/>
        <result column="config_name" jdbcType="VARCHAR" property="configName"/>
        <result column="table_name" jdbcType="VARCHAR" property="tableName"/>
        <result column="config_detail" jdbcType="VARCHAR" property="configDetail"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>


</mapper>