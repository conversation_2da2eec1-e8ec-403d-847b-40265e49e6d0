<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.OrderMasterPushMapper">


    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.OrderMasterPush">
        <id column="push_id" jdbcType="BIGINT" property="pushId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="master_id" jdbcType="BIGINT" property="masterId"/>
        <result column="order_version" jdbcType="VARCHAR" property="orderVersion"/>
        <result column="score" jdbcType="VARCHAR" property="score"/>
        <result column="rounds" jdbcType="INTEGER" property="rounds"/>
        <result column="is_alternative" jdbcType="BIGINT" property="isAlternative"/>
        <result column="master_type" jdbcType="BIGINT" property="masterType"/>
        <result column="is_priority_push" jdbcType="INTEGER" property="isPriorityPush"/>
        <result column="push_time" jdbcType="TIMESTAMP" property="pushTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getOrderMasterPushList" resultMap="BaseResultMap">
            SELECT * FROM order_master_push_${timeMark} WHERE order_id = #{orderId} AND order_version = #{orderVersion} AND push_time is null

        <if test="isAlternative != null">
            AND is_alternative = #{isAlternative}
        </if>

        <if test="roundsSet != null and roundsSet.size() > 0">
            AND rounds in
            <foreach collection="roundsSet" index="index" item="rounds" open="(" separator="," close=")">
                #{rounds}
            </foreach>

        </if>
    </select>


    <insert id="insertOrderMasterPushList">
        insert into order_master_push_${timeMark}(order_id, master_id,order_version,score,rounds, is_alternative,master_type,is_priority_push,push_time)
        values
        <foreach collection="orderMasterPushList" item="orderMasterPush" separator=",">
            (#{orderMasterPush.orderId},#{orderMasterPush.masterId}, #{orderMasterPush.orderVersion}, #{orderMasterPush.score}, #{orderMasterPush.rounds},#{orderMasterPush.isAlternative},#{orderMasterPush.masterType},#{orderMasterPush.isPriorityPush},#{orderMasterPush.pushTime})
        </foreach>
    </insert>


    <update id="createOrderMasterPushTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `push_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
            `order_id` bigint(20) unsigned NOT NULL COMMENT '订单ID',
            `master_id` bigint(20) NOT NULL COMMENT '师傅ID',
            `order_version` varchar(20) NOT NULL COMMENT '订单版本',
            `score` decimal(10,2) NOT NULL COMMENT '分数',
            `rounds` varchar(100) NOT NULL COMMENT '轮数',
            `is_alternative` tinyint(3) NOT NULL COMMENT '是否候补，1：候补，0：非候补',
            `master_type` varchar(20) NOT NULL COMMENT '师傅类型',
            `is_priority_push` TINYINT(3) DEFAULT '0' COMMENT '是否优先推送',
            `push_time` datetime NULL COMMENT '推单时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`push_id`),
            KEY `master_list` (`order_id`,`order_version`,`rounds`,`master_type`),
            KEY `master_id` (`master_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
    </update>

    <update id="dropHistoryOrderMasterPushTable" parameterType="String">
        DROP TABLE IF EXISTS ${tableName};
    </update>


    <select id="selectPushScore" resultMap="BaseResultMap">
        SELECT master_id,score FROM order_master_push_${timeMark}
        WHERE order_id = #{orderId} AND order_version = #{orderVersion}
        AND master_id IN
        <foreach collection="masterIdList" index="index" item="masterId" open="(" separator="," close=")">
            #{masterId}
        </foreach>
    </select>


</mapper>