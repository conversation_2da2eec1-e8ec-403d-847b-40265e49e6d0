#spring.application.name=master-order-push-service
##############
##\u65E5\u5FD7\u914D\u7F6E
##############
#logging.config=classpath:logback-spring.xml
######################################
##profile\u914D\u7F6E\u5BF9\u5E94\u73AF\u5883\u7684properties\u6587\u4EF6
######################################
#spring.profiles.active=dev
#spring.profiles.active=test
#
######################################
##          \u63A5\u53E3\u914D\u7F6E
######################################
##\u6253\u5F00actuator\u76D1\u63A7
#management.security.enabled=false
###########################################################
##log\u8DEF\u5F84
###########################################################
#logging.path=/data/servicesLog/tomcat-master-order-push-service/logs
#javamelody.advisor-auto-proxy-creator-enabled=false
#
#
#mybatis.mapper-locations=classpath*:**/mappers/*.xml
#mybatis.type-aliases-package=com.wanshifu.master.order.push.domain.po
#mapper.mappers=com.wanshifu.framework.persistence.base.IBaseCommMapper
#mapper.not-empty=false
#mapper.identity=MYSQL
##########################################################
##pagehelper\u5206\u9875\u914D\u7F6E#
##########################################################
#pagehelper.helperDialect=mysql
#pagehelper.reasonable=true
#pagehelper.supportMethodsArguments=true
#pagehelper.params=count=countSql
#
#wanshifu.dataSource.enable=true
#
#
##mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

#app.id=master-order-push-service
#apollo.bootstrap.enabled=true
#apollo.bootstrap.eagerLoad.enabled=true
#apollo.cluster=default
#apollo.bootstrap.namespaces=application,developer-pub-config,yunwei-pub-config