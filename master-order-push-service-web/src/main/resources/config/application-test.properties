#
#
#########################################################
##\u6D88\u606F\u961F\u5217\u914D\u7F6E#
#########################################################
#wanshifu.rocketMQ.enable=true
#wanshifu.rocketMQ.consumer-id=CID_test_pre-release_wanshifu_master_order_push_consumer
#wanshifu.rocketMQ.producer-id=PID_test_pre-release_wanshifu_master_order_push_producer
#wanshifu.rocketMQ.o-n-s-addr=http://onsaddr-internet.aliyun.com/rocketmq/nsaddr4client-internet
#wanshifu.rocketMQ.consumer.enable=true
#wanshifu.rocketMQ.producer.enable=true
#wanshifu.rocketMQ.namesrv=test-rocketmq.wanshifu.com:9876
#wanshifu.rocketMQ.send-service.enable=true
#
#wanshifu.rocketMQ.order-push-topic=topic_common_order_general
#
#wanshifu.mindpy.redis.enable=true
#wanshifu.mindpy.redis.redisPassword=tRedisW135
#wanshifu.mindpy.redis.database=15
#wanshifu.mindpy.redis.redisHost=multi-lane-redis.wanshifu.com
#wanshifu.mindpy.redis.redisPort=6379
#
#
#
#########################################################
##redis\u7F13\u5B58\u914D\u7F6E#
#########################################################
#wanshifu.redis.enable=false
#wanshifu.redis.redisHost=default-test-redis.wanshifu.com
#wanshifu.redis.redisPort=6379
#wanshifu.redis.redisPassword=tRedisW135
#wanshifu.health.jedis-enable=false
#
#
#wanshifu.dataSource.url=*************************************************************************************
#wanshifu.dataSource.username=user_test
#wanshifu.dataSource.password=UserTest1246
#wanshifu.dataSource.driver-class-name=com.mysql.jdbc.Driver
#


#apollo.meta=http://test-apollo-config-service.wanshifu.com:8091
