package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述 :  范围类型：枚举.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 16:44
 */
@AllArgsConstructor
@Getter
public enum RangeTypeEnum {

    ORDER_MASTER_DISTANCE("order_master_distance", "服务地址与师傅常住地的距离"),
    ORDER_MASTER_SERVE_DIVISION("order_master_serve_division", "服务地址所处师傅服务区域范围(三级地址)"),
    ORDER_MASTER_SERVE_DIVISION_LEVEL4("order_master_serve_division_level4", "服务地址所处师傅服务区域范围(四级地址)"),
    ORDER_MASTER_DISTANCE_AND_ORDER_MASTER_SERVE_DIVISION("order_master_distance_and_order_master_serve_division", "服务地址与师傅常住地的距离所匹配的师傅服务范围"),
    ORDER_MASTER_DISTANCE_OR_ORDER_MASTER_SERVE_DIVISION("order_master_distance_or_order_master_serve_division", "服务地址所处师傅服务区域范围内或与师傅常住地的距离范围"),
    ;
    private final String code;

    private final String desc;
}