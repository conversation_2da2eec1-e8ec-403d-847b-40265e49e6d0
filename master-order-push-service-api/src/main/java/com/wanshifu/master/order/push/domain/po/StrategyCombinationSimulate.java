package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 
 */
@Data
@ToString
@Table(name = "strategy_combination_simulate")
public class StrategyCombinationSimulate {

    /**
     * 测算id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "simulate_id")
    private Long simulateId;

    /**
     * 策略组合id
     */
    @Column(name = "strategy_combination_id")
    private Long strategyCombinationId;

    /**
     * 订单数量
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 已测试订单量
     */
    @Column(name = "simulated_order_num")
    private Integer simulatedOrderNum;

    /**
     * 测算状态
     */
    @Column(name = "simulate_status")
    private Integer simulateStatus;

    /**
     * 测算时间
     */
    @Column(name = "simulate_time")
    private Date simulateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}