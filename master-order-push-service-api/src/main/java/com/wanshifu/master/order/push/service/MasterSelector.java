package com.wanshifu.master.order.push.service;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.common.BaseSelect;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.order.config.api.TechniqueServiceApi;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.es.OrderBaseInfo;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.OrderBaseInfoEsRepository;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.config.domains.dto.technique.ServeTypeAndGoodsResp;
import com.wanshifu.order.config.domains.dto.technique.TechniqueIdWithLevel1GoodsIdDto;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * tablestore师傅初筛
 * <AUTHOR>
 */
@Slf4j
@Component
public class MasterSelector {


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;


    @Resource
    private OrderBaseInfoEsRepository orderBaseInfoEsRepository;


    @Value("${master.settleIn.search.count}")
    private Integer masterSettleInSearchCount;


    @Value("${master.settleIn.push.count}")
    private Integer masterSettleInPushCount;


    @Resource
    private HBaseClient hBaseClient;


    @Value("${es.searchAfter.switch:off}")
    private String esSearchAfterSwitch;


    @Value("${base.select.limit.city:off}")
    private String baseSelectLimitCity;


    @Value("${out.district.push.master.count:5000}")
    private Integer outDistrictPushMasterCount;

    @Value("${baseSelect.search.es.time:3000}")
    private int baseSelectSearchEsTime;


    /**
     * es慢查询优化开关，默认开启on
     * 非on则关闭
     */
    @Value("${es.searchGradeSwitch:on}")
    private String esSearchGradeSwitch;


    @Resource
    private TechniqueServiceApi techniqueServiceApi;

    /**
     * 推送新师傅开关
     */
    @Value("${push.newMaster.switch:on}")
    private String pushNewMasterSwitch;

    /**
     * 获取订单指派师傅
     * @param globalOrderId
     * @return
     */


    /**
     * 功能限制-禁止推单||禁止登录
     * @param
     */
    private void setPushRestrictQuery(BoolQueryBuilder boolQueryBuilder){
        boolQueryBuilder.filter(QueryBuilders.termQuery("isPushRestrictNormal", 1));
    }

    public List<BaseSelectMaster> searchMasterByBaseSelectStrategy(MasterMatchCondition masterMatchCondition, BaseSelect baseSelect) {
        RangeSelect rangeSelect = baseSelect.getRangeSelect();
        StatusSelect statusSelect = baseSelect.getStatusSelect();
        TechniqueSelect techniqueSelect = baseSelect.getTechniqueSelect();
        ServeDataSelect serveDataSelect = baseSelect.getServeDataSelect();
        masterMatchCondition.setDivisionMatchLevel(3);
        return selectMasterByStrategy(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect,serveDataSelect);
    }


    public List<BaseSelectMaster> searchMasterByBaseSelectStrategy(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect, StatusSelect statusSelect) {
        return selectMasterByStrategy(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect,null);


    }


    /**
     * 限制枚举
     */
    private static enum RestrictAction {
        PUSH{
            @Override
            public String getActionString(){
                return "1";
            };
        },LOGIN{
            @Override
            public String getActionString(){
                return "2";
            };
        },EXCLUSIVE{
            @Override
            public String getActionString(){
                return "14";
            };
        },NO_QUOTATION{
            @Override
            public String getActionString(){
                return "3";
            };
        },NO_APPOINT{
            @Override
            public String getActionString(){
                return "4";
            };
        };
        public String getActionString(){
            return null;
        };
    };


    /**
     * 专属订单全额退款限制推送
     * @param orderGoodsLv1Id
     * @param masterIdsSet
     */
    public void exclusiveRefundBlackList(String orderGoodsLv1Id,Set<String> masterIdsSet){
        if (masterIdsSet != null) {
            try {
                final Set<String> restrictMasters = searchRestrictMasterByIndex(
                        RestrictAction.EXCLUSIVE.getActionString(),
                        orderGoodsLv1Id, masterIdsSet);
                if (restrictMasters.size()!=0) {
                    masterIdsSet.removeAll(restrictMasters);
                }
            } catch (Exception e) {
                log.warn("exclusiveRefundBlackList error:{},{}",e,masterIdsSet);
            }
        }
    }


    /**
     * 过滤功能限制师傅
     * @param RestrictAction 1：禁推单，2：禁登录，3：禁专属
     * @param orderGoodsLv1Id
     * @param inputMasterIdsSet
     * @return
     */

    private static final String MASTER = "t_master";
    private static final String MASTER_INDEX = "master_index_v1";





    private Set<String> searchRestrictMasterByIndex(String RestrictAction,String orderGoodsLv1Id,Set<String> inputMasterIdsSet) {

        if (RestrictAction==null) {
            return Collections.emptySet();
        }

        QueryBuilder queryBuilder= QueryBuilders.boolQuery().filter(QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("masterId", inputMasterIdsSet))
                .must(QueryBuilders.termQuery("restrictAction", RestrictAction))
                .must(QueryBuilders.termQuery("limitLv1GoodsIds", orderGoodsLv1Id))
        );

//        SearchQuery searchQuery = new SearchQuery();
//        BoolQuery boolQuery = new BoolQuery();
//        List<Query> mustQueryList=new ArrayList<Query>();
//        mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID, inputMasterIdsSet));
//        setRestrictMasterQuery(mustQueryList,RestrictAction,orderGoodsLv1Id);
//        boolQuery.setMustQueries(mustQueryList);
//        searchQuery.setQuery(boolQuery);
//        SearchRequest searchRequest = new SearchRequest(MASTER, MASTER_INDEX, searchQuery);
//        SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
        // 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。

        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();

        int pageNum = 1;
        int pageSize = 100;
        while(true){
            EsResponse esResponse = masterBaseEsRepository.search(queryBuilder,new Pageable(pageNum,pageSize),null);
            if(CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }
        if(CollectionUtils.isEmpty(masterBaseSearchList)){
            return Collections.emptySet();
        }
        Set<String> masterIds = masterBaseSearchList.stream().map(masterBaseSearch -> String.valueOf(masterBaseSearch.getMasterId())).collect(Collectors.toSet());
        return masterIds;
    }



    private List<BaseSelectMaster> selectMasterByStrategy(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect,
                                                          StatusSelect statusSelect,ServeDataSelect serveDataSelect) {
        Long cityDivisionId = masterMatchCondition.getSecondDivisionId();
        List<BaseSelectMaster> baseSelectMasterList = new ArrayList<>();
        if(serveDataSelect == null){
            List<MasterBaseSearch> masterBaseList = selectMaster(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect);
            log.info("masterBaseList:" + JSON.toJSONString(masterBaseList));
            if(CollectionUtils.isNotEmpty(masterBaseList)){
                if(cityDivisionId != null){
                    masterBaseList.forEach(masterBase -> {
                        BaseSelectMaster baseSelectMaster = new BaseSelectMaster(masterBase.getMasterId(),"technique",
                                cityDivisionId.equals(masterBase.getCityDivisionId()) ? 0 : 1,masterBase.getIdCardNumber());
                        BeanUtils.copyProperties(masterBase,baseSelectMaster);
                        baseSelectMasterList.add(baseSelectMaster);
                    });
                }else{
                    masterBaseList.forEach(masterBase -> {
                        BaseSelectMaster baseSelectMaster = new BaseSelectMaster(masterBase.getMasterId(),"technique", 0,masterBase.getIdCardNumber());
                        BeanUtils.copyProperties(masterBase,baseSelectMaster);
                        baseSelectMasterList.add(baseSelectMaster);
                    });
                }

            }
            return baseSelectMasterList;
        }else{
            List<MasterBaseSearch> techniqueMasterBaseList = selectMaster(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect);
            List<MasterBaseSearch> masterBaseList = selectMaster(masterMatchCondition,rangeSelect,null,statusSelect);

            log.info("techniqueMasterBaseList:" + JSON.toJSONString(techniqueMasterBaseList));
            log.info("masterBaseList:" + JSON.toJSONString(masterBaseList));


            Set<String> techniqueMasterSet = techniqueMasterBaseList.stream().map(masterBaseSearch -> String.valueOf(masterBaseSearch.getMasterId())).collect(Collectors.toSet());
            Set<String> masterSet = masterBaseList.stream().map(masterBaseSearch -> String.valueOf(masterBaseSearch.getMasterId())).collect(Collectors.toSet());


            Set<String> serveMasterSet = filterByServeData(masterMatchCondition.getServeIds(),masterSet,serveDataSelect);

            if(CollectionUtils.isNotEmpty(serveMasterSet)){
                techniqueMasterSet.addAll(serveMasterSet);
                Map<String, MasterBaseSearch> masterBaseMap = masterBaseList.stream()
                        .collect(Collectors.toMap(MasterBaseSearch::getMasterId, masterBase -> masterBase));

                serveMasterSet.forEach(masterId -> {
                    BaseSelectMaster baseSelectMaster = new BaseSelectMaster(masterId,"serveData",cityDivisionId.equals(masterBaseMap.get(masterId).getCityDivisionId()) ? 0 : 1,masterBaseMap.get(masterId).getIdCardNumber());
                    BeanUtils.copyProperties(masterBaseMap.get(masterId),baseSelectMaster);

                    baseSelectMasterList.add(baseSelectMaster);
                });
            }

            Set<String> onlyTechniqueMasterSet = techniqueMasterSet.stream().filter(masterId -> (!serveMasterSet.contains(masterId))).collect(Collectors.toSet());
            Map<String, MasterBaseSearch> techniqueMasterBaseMap = techniqueMasterBaseList.stream()
                    .collect(Collectors.toMap(MasterBaseSearch::getMasterId, masterBase -> masterBase));
            onlyTechniqueMasterSet.forEach(masterId -> {
                BaseSelectMaster baseSelectMaster = new BaseSelectMaster(masterId,"technique",
                        cityDivisionId.equals(techniqueMasterBaseMap.get(masterId).getCityDivisionId()) ? 0 : 1,techniqueMasterBaseMap.get(masterId).getIdCardNumber());
                BeanUtils.copyProperties(techniqueMasterBaseMap.get(masterId),baseSelectMaster);

                baseSelectMasterList.add(baseSelectMaster);
            });
            return baseSelectMasterList;
        }
    }

    private Set<String> filterByServeData(String serveIds,Set<String> masterSet,
                                          ServeDataSelect serveDataSelect){

        Set<String> resultSet = new HashSet<>();

        if(StringUtils.isBlank(serveIds)){
            return resultSet;
        }
        List<String> serveIdList = Stream.of(serveIds.split(",")).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(serveIdList)){
            return Collections.emptySet();
        }

        String serveDataType = serveDataSelect.getServeDataType();
        String column = null;
        if("last_1y_serve_appoint".equals(serveDataType)){
            column = "serve_lv3_last_365d_offer_cnt";
        }else if("last_1y_serve_complete".equals(serveDataType)){
            column = "serve_lv3_last_365d_complete_cnt";
        }
        final String finalColumn = column;
        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,200);
        List<String> columnSet = Collections.singletonList(column);
        batchList.forEach(batch -> {
            List<String> rowKeyList = new ArrayList<>();
            batch.forEach(masterId -> rowKeyList.add(masterId+"_"+serveIdList.get(0)));
//            rowKeyList.add("10001_4003104");
            Map<String,Map<String,String>> resultMap = hBaseClient.batchQuery("mst_serve_lv3_stat","master_id",rowKeyList,columnSet);
            batch.forEach(masterId -> {
                Map<String,String> map = resultMap.get(masterId);
                if(map != null && StringUtils.isNotBlank(map.get(finalColumn)) && Integer.parseInt(map.get(finalColumn)) > 0){
                    resultSet.add(masterId);
                }
            });
        });
        log.info("after filterByServeData masterIds:{}", resultSet);
        return resultSet;
    }

    /**
     * 组装技能初筛query（优化前）
     * @param boolQueryBuilder
     * @param masterMatchCondition
     * @param techniqueSelect
     */
//    private void oldBuildTechniqueSelectBoolQuery(BoolQueryBuilder boolQueryBuilder, MasterMatchCondition masterMatchCondition, TechniqueSelect techniqueSelect) {
//
//        if (TechniqueSelectType.FILTER_OUT_NO_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
//            BoolQueryBuilder shouldQuery = this
//                    .boolQueryBuilder(
//                            "masterTechniqueIds",
//                            masterMatchCondition.getTechnologysInDemandSet(),
//                            1);
//            boolQueryBuilder.filter(shouldQuery);
//        } else if (TechniqueSelectType.FILTER_OUT_NO_MATCH_IKEA.code.equals(techniqueSelect.getTechniqueType())) {
//            //过滤非宜家师傅时，技能是隐藏条件，必须符合
//            BoolQueryBuilder shouldQuery = this
//                    .boolQueryBuilder(
//                            "masterTechniqueIds",
//                            masterMatchCondition.getTechnologysInDemandSet(),
//                            1);
//            boolQueryBuilder.filter(shouldQuery);
//            boolQueryBuilder.filter(QueryBuilders.termQuery("masterBrand", masterMatchCondition.getMasterBrand()));
//        }
//        if (TechniqueSelectType.FILTER_OUT_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
//            BoolQueryBuilder shouldQuery = this
//                    .boolQueryBuilder(
//                            "masterTechniqueIds",
//                            masterMatchCondition.getTechnologysInDemandSet(),
//                            1);
//            boolQueryBuilder.mustNot(shouldQuery);
//        }
//    }


    /**
     * 组装技能初筛query（优化后）
     * @param boolQueryBuilder
     * @param masterMatchCondition
     * @param techniqueSelect
     */
    private void newBuildTechniqueSelectBoolQuery(BoolQueryBuilder boolQueryBuilder, MasterMatchCondition masterMatchCondition, TechniqueSelect techniqueSelect) {
        List<String> shouldConditionList = new ArrayList<>(masterMatchCondition.getTechnologysInDemandSet());
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }

        if (TechniqueSelectType.FILTER_OUT_NO_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {

            boolQueryBuilder.filter(QueryBuilders.termsQuery("masterTechniqueIds", shouldConditionList));

        } else if (TechniqueSelectType.FILTER_OUT_NO_MATCH_IKEA.code.equals(techniqueSelect.getTechniqueType())) {
            //过滤非宜家师傅时，技能是隐藏条件，必须符合
            boolQueryBuilder.filter(QueryBuilders.termsQuery("masterTechniqueIds", shouldConditionList));
            boolQueryBuilder.filter(QueryBuilders.termQuery("masterBrand", masterMatchCondition.getMasterBrand()));
        }
        if (TechniqueSelectType.FILTER_OUT_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {

            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("masterTechniqueIds", shouldConditionList));
        }
    }

    /**
     * 根据初筛策略筛选师傅
     * @param
     * @return
     */
    private List<MasterBaseSearch> selectMaster(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect,
                                                StatusSelect statusSelect) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//                .must(QueryBuilders.termsQuery("masterId", inputMasterIdsSet))
//                .must(QueryBuilders.termQuery("restrict_action", RestrictAction))
//                .must(QueryBuilders.termQuery("limit_lv1_goods_ids", orderGoodsLv1Id))

        String masterSourceType = StringUtils.isBlank(masterMatchCondition.getMasterSourceType()) ? MasterSourceType.TOB.code : masterMatchCondition.getMasterSourceType();


        //范围初筛
        if (RangeSelectType.ORDER_MASTER_DISTANCE.code.equals(rangeSelect.getRangeType())) {
            if (StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())) {
                //匹配订单与师傅所在地距离
                Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(it -> {
                    boolQueryBuilder.filter(it);
                    //是否根据距离推送
                    masterMatchCondition.setAccordingDistancePush(true);
                });
            } else {
                boolQueryBuilder.filter(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
            }

        } else if (RangeSelectType.ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
            //匹配订单与师傅服务区域（三级地址）
            boolQueryBuilder.filter(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
        } else if (RangeSelectType.ORDER_MASTER_DISTANCE_AND_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
            //匹配订单与师傅所在地距离 and 匹配订单与师傅服务区域
            Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(boolQueryBuilder::should);
            boolQueryBuilder.filter(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
        } else if (RangeSelectType.ORDER_MASTER_DISTANCE_OR_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
            //匹配订单与师傅所在地距离 or 匹配订单与师傅服务区域（三级地址）
            BoolQueryBuilder boolQueryBuilder1 = new BoolQueryBuilder();
            boolQueryBuilder1.should(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
            Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(boolQueryBuilder1::should);
            boolQueryBuilder1.minimumShouldMatch(1);
            boolQueryBuilder.filter(boolQueryBuilder1);
            //是否根据距离推送
            masterMatchCondition.setAccordingDistancePush(true);
        } else if (RangeSelectType.ORDER_MASTER_SERVE_DIVISION_LEVEL4.code.equals(rangeSelect.getRangeType())) {
            //匹配订单与师傅服务区域（四级地址） 订单无四级地址时,按照三级地址匹配
            if (masterMatchCondition.getFourthDivisionId() != null && masterMatchCondition.getFourthDivisionId() > 0L) {
                masterMatchCondition.setDivisionMatchLevel(4);
            }
            boolQueryBuilder.filter(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
        } else if (RangeSelectType.ORDER_MASTER_ORT_DISTRICT.code.equals(rangeSelect.getRangeType())) {
            //区县外师傅（三级地址）
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(masterMatchCondition.getThirdDivisionId())));
        }


//        if (techniqueSelect != null && CollectionUtil.isNotEmpty(masterMatchCondition.getTechnologysInDemandSet())) {
//
//            boolean doShouldQuery = false;
//            for (String technique : masterMatchCondition.getTechnologysInDemandSet()) {
//                if (technique.contains(",")) {
//                    doShouldQuery = true;
//                    break;
//                }
//            }
//
//            //技能初筛
//            if ("on".equals(esSearchGradeSwitch)
//                    && !doShouldQuery) {
//                newBuildTechniqueSelectBoolQuery(boolQueryBuilder, masterMatchCondition, techniqueSelect);
//            } else {
//                oldBuildTechniqueSelectBoolQuery(boolQueryBuilder, masterMatchCondition, techniqueSelect);
//            }
//        }

        if(techniqueSelect != null){
            //技能初筛
            if (TechniqueSelectType.FILTER_OUT_NO_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
                BoolQueryBuilder shouldQuery = this
                        .boolQueryBuilder(
                                "masterTechniqueIds",
                                masterMatchCondition.getTechnologysInDemandSet(),
                                1,masterSourceType,masterMatchCondition.getBusinessLineId());
                boolQueryBuilder.filter(shouldQuery);
            } else if (TechniqueSelectType.FILTER_OUT_NO_MATCH_IKEA.code.equals(techniqueSelect.getTechniqueType())) {
                //过滤非宜家师傅时，技能是隐藏条件，必须符合
                BoolQueryBuilder shouldQuery = this
                        .boolQueryBuilder(
                                "masterTechniqueIds",
                                masterMatchCondition.getTechnologysInDemandSet(),
                                1,masterSourceType,masterMatchCondition.getBusinessLineId());
                boolQueryBuilder.filter(shouldQuery);
                boolQueryBuilder.filter(QueryBuilders.termQuery("masterBrand",masterMatchCondition.getMasterBrand()));
            }if (TechniqueSelectType.FILTER_OUT_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
                BoolQueryBuilder shouldQuery = this
                        .oldBoolQueryBuilder(
                                "masterTechniqueIds",
                                masterMatchCondition.getTechnologysInDemandSet(),
                                1);
                boolQueryBuilder.mustNot(shouldQuery);

                Integer businessLineId = masterMatchCondition.getBusinessLineId();
                if("on".equals(pushNewMasterSwitch) && Objects.nonNull(businessLineId) && businessLineId == 1){
                    BoolQueryBuilder boolQueryBuilder1 = new BoolQueryBuilder();
                    //待确认技能验证的类目
                    boolQueryBuilder1.should(QueryBuilders.termsQuery("waitConfirmTechniqueVerifyCategoryIds", Lists.newArrayList(masterMatchCondition.getCategoryId())));
                    //待技能验证派单的类目
                    boolQueryBuilder1.should(QueryBuilders.termsQuery("toTechniqueVerifyCategoryIds", Lists.newArrayList(masterMatchCondition.getCategoryId())));
                    //技能验证中的类目
                    boolQueryBuilder1.should(QueryBuilders.termsQuery("techniqueVerifyingCategoryIds", Lists.newArrayList(masterMatchCondition.getCategoryId())));
                    //技能验证失败的类目
                    boolQueryBuilder1.should(QueryBuilders.termsQuery("techniqueVerifyFailedCategoryIds", Lists.newArrayList(masterMatchCondition.getCategoryId())));
                    boolQueryBuilder1.minimumShouldMatch(1);
                    boolQueryBuilder.mustNot(boolQueryBuilder1);
                }

            }
        }

//        String masterSourceType = StringUtils.isBlank(masterMatchCondition.getMasterSourceType()) ? MasterSourceType.TOB.code : masterMatchCondition.getMasterSourceType();

        if(!(MatchSceneCode.NEW_MODEL_RECOMMEND_MASTER_LIST.getCode().equals(masterMatchCondition.getMatchSceneCode()) && MasterSourceType.TOC.code.equals(masterSourceType))){
            boolQueryBuilder.filter(QueryBuilders.termQuery("masterSourceType", masterSourceType));
        }


        if(MatchSceneCode.EXTRA_CONTEST_OFFER_NUMBER.getCode().equals(masterMatchCondition.getMatchSceneCode())){
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("isDistributeOrder", 1));
        }



        //状态初筛
        Optional.ofNullable(statusSelect).ifPresent(it->it.getItemList().forEach(statusSelectItem -> {
            if(StatusSelectType.MASTER_STATUS.code.equals(statusSelectItem.getItemName()) && StringUtils.isNotBlank(statusSelectItem.getItemValue())){

                String[] statusList = statusSelectItem.getItemValue().split(",");

                Arrays.stream(statusList).distinct().forEach(e -> {
                    if(MasterStatusType.BLACK_LIST.code.equals(e)){
                        boolQueryBuilder.filter(QueryBuilders.termQuery("isBlackListStatusNormal", "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));
                    }else if(MasterStatusType.ACCOUNT_STATUS_ABNORMAL.code.equals(e)){

                        boolQueryBuilder.filter(QueryBuilders.termQuery("isAccountNormal", "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));

                    }else if(MasterStatusType.ACCOUNT_FREEZE.code.equals(e)){
                        if("in".equals(statusSelectItem.getTerm())){
                            long freezingRecoverTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                            boolQueryBuilder.filter(QueryBuilders.rangeQuery("freezingTime").gte(0L).lte(freezingRecoverTime));
                        }
                    }else if(MasterStatusType.NOT_SETTLED.code.equals(e)){
                        boolQueryBuilder.filter(QueryBuilders.termQuery("isSettleStatusNormal", "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));
                    }else if(MasterStatusType.NOT_ACTIVE_GT_30.code.equals(e)){
                        if("in".equals(statusSelectItem.getTerm())){
                            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastActiveTime").gte(DateFormatterUtil.getAssignDayTimeStamp(-30)));
                        }
                    }else if(MasterStatusType.NOT_WORKING.code.equals(e)){
                        boolQueryBuilder.filter(QueryBuilders.termQuery("restState", "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));
                    }else if(MasterStatusType.EXAM_NOT_PASS.code.equals(e)){
                        boolQueryBuilder.filter(QueryBuilders.termQuery("isRuleExamStatusNormal", "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));
                    }
                });
            }
        }));


        if (
                "on".equals(baseSelectLimitCity) && (
                        RangeSelectType.ORDER_MASTER_DISTANCE.code.equals(rangeSelect.getRangeType()) ||
                                RangeSelectType.ORDER_MASTER_DISTANCE_AND_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType()) ||
                                RangeSelectType.ORDER_MASTER_DISTANCE_OR_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType()))) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("cityDivisionId",masterMatchCondition.getSecondDivisionId()));
        }



//		if (masterMatchCondition.needBrandMaster()) {
//            boolQueryBuilder.must(QueryBuilders.termQuery("masterBrand",masterMatchCondition.getMasterBrand()));
//		}
        //功能限制-禁止推单||禁止登录
        setPushRestrictQuery(boolQueryBuilder);

//        List<Query> mustNotQueryList=new ArrayList<Query>();
//
        if(StringUtils.isNotBlank(masterMatchCondition.getServeIds())){
            setBaseNotQuery(boolQueryBuilder,masterMatchCondition);
        }

        //禁止推单的业务线
        this.setNotQueryBusinessLine(boolQueryBuilder,masterMatchCondition.getBusinessLineId());

        Integer isExclusivePartTimeMaster = masterMatchCondition.getIsExclusivePartTimeMaster();
        if(Objects.nonNull(isExclusivePartTimeMaster) && isExclusivePartTimeMaster == 1){
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("masterTimeType",2));
        }


        if(MatchSceneCode.COOPERATION_BUSINESS.getCode().equals(masterMatchCondition.getMatchSceneCode())){
            //查询合作经营师傅且开启派单
            boolQueryBuilder.filter(QueryBuilders.termQuery("isDistributeOrder", 1));
        }


        if(MatchSceneCode.FULL_TIME_MASTER.getCode().equals(masterMatchCondition.getMatchSceneCode())){
            //报价人数已满订单推送全时师傅
            boolQueryBuilder.filter(QueryBuilders.termQuery("masterTimeType", 1));
        }

        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
        long beginTime = System.currentTimeMillis();
        if("on".equals(esSearchAfterSwitch)){
            log.info("masterBaseEsRepository boolQueryBuilder:"+boolQueryBuilder.toString());
            SortBuilder sortBuilder = null;
            if (RangeSelectType.ORDER_MASTER_ORT_DISTRICT.code.equals(rangeSelect.getRangeType()) &&
                    StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())) {
                String[] latLngArray = masterMatchCondition.getOrderLngLat().split(",");
                if (latLngArray.length == 2) {
                    GeoPoint geoPoint = new GeoPoint(Double.valueOf(latLngArray[1]), Double.valueOf(latLngArray[0]));
                    sortBuilder = SortBuilders.geoDistanceSort("latLng", geoPoint);

                }
            }
            if(Objects.isNull(sortBuilder)){
                sortBuilder = SortBuilders.fieldSort("masterId");
            }
            Integer limitCount = RangeSelectType.ORDER_MASTER_ORT_DISTRICT.code.equals(rangeSelect.getRangeType()) ? outDistrictPushMasterCount : Integer.MAX_VALUE;
            EsResponse esResponse = masterBaseEsRepository.searchAfter(masterMatchCondition, boolQueryBuilder, sortBuilder, 100, limitCount);
            masterBaseSearchList = esResponse.getDataList();
        }else{
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                log.info("masterBaseEsRepository boolQueryBuilder:"+boolQueryBuilder.toString());
                EsResponse esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
                if(CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
        }
        long endTime = System.currentTimeMillis();
        long searchTime = endTime - beginTime;
        log.info("master_base baseSelect searchTime: {} ms,订单id:{}，查询师傅数：{}", searchTime, masterMatchCondition.getMasterOrderId(), masterBaseSearchList.size());
        if (searchTime > baseSelectSearchEsTime) {
            log.info("master_base baseSelect searchTime greater than,actual take: {} ms,订单id:{}，查询师傅数：{},技能信息：{}，boolQueryBuilder：{}",
                    searchTime, masterMatchCondition.getMasterOrderId(), masterBaseSearchList.size(),
                    Objects.isNull(techniqueSelect) ? "" : JSONUtil.toJsonStr(masterMatchCondition.getTechnologysInDemandSet()),
                    boolQueryBuilder.toString());
        }
        return masterBaseSearchList;

//        if(CollectionUtils.isEmpty(masterBaseSearchList)){
//            return Collections.emptySet();
//        }
//        Set<String> masterIds = masterBaseSearchList.stream().map(masterBaseSearch -> String.valueOf(masterBaseSearch.getMasterId())).collect(Collectors.toSet());



//        return masterIds;
    }

    private void setBaseNotQuery(BoolQueryBuilder boolQueryBuilder,MasterMatchCondition masterMatchCondition) {

        //服务限制
        if ("on".equals(esSearchGradeSwitch)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("masterForbiddenServeIds", Arrays.stream(masterMatchCondition.getServeIds().split(",")).map(Long::parseLong).collect(Collectors.toSet())));
        } else {
            boolQueryBuilder.mustNot(this.stringMatchQueryBuilderWithMinim(
                    "masterForbiddenServeIds",
                    masterMatchCondition.getServeIds(),
                    Operator.OR,1));
        }


    }


    private void setNotQueryBusinessLine(BoolQueryBuilder boolQueryBuilder, Integer businessLineId){
        //业务线推单限制
        if ("on".equals(esSearchGradeSwitch)) {

            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("masterForbiddenBusinessIds", Lists.newArrayList(businessLineId)));
        } else {

            boolQueryBuilder.mustNot(this.stringMatchQueryBuilderWithMinim(
                    "masterForbiddenBusinessIds",
                    String.valueOf(businessLineId),
                    Operator.OR,1));
        }
    }


    public MatchQueryBuilder stringMatchQueryBuilderWithMinim(String fieldName, String value, Operator operator, Integer minimumShouldMatch) {
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        //设置要匹配的列。
        matchQueryBuilder.operator(operator);
        matchQueryBuilder.minimumShouldMatch(String.valueOf(minimumShouldMatch));
        return matchQueryBuilder;
    }



    /**
     * 索引API
     */
    public BoolQueryBuilder oldBoolQueryBuilder(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch) {

        List<String> shouldConditionList = new ArrayList<>(shouldConditions);
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        shouldConditionList.stream()
                .map(shouldCondition->shouldQuery.should(stringMatchQueryBuilder(fieldName,shouldCondition,Operator.AND)))
                .collect(Collectors.toList());

        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }


    /**
     * 索引API
     */
    public BoolQueryBuilder boolQueryBuilder(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch,String masterSourceType,Integer businessLineId) {

        List<String> shouldConditionList = new ArrayList<>(shouldConditions);
        if(CollectionUtils.isNotEmpty(shouldConditionList) && shouldConditionList.size() > 1024){
            shouldConditionList = shouldConditionList.subList(0,1000);
        }
        BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
        if((!"on".equals(pushNewMasterSwitch)) || Objects.isNull(businessLineId) || businessLineId == 2 || businessLineId == 3){
            shouldConditionList.stream()
                    .map(shouldCondition->shouldQuery.should(stringMatchQueryBuilder(fieldName,shouldCondition,Operator.AND)))
                    .collect(Collectors.toList());
        }else{

            Set<String> techniqueSet = new HashSet<>();
            shouldConditions.forEach(techniques -> techniqueSet.addAll(Arrays.asList(techniques.split(","))));

            Set<Long> finalTechniqueIdSet = techniqueSet.stream().map(Long::valueOf).collect(Collectors.toSet());
            List<TechniqueIdWithLevel1GoodsIdDto> techniqueIdWithLevel1GoodsIdDtos = techniqueServiceApi.getTechniqueIdWithLevel1GoodsId(finalTechniqueIdSet);

            Map<Long, TechniqueIdWithLevel1GoodsIdDto> techniqueIdWithLevel1GoodsIdDtoMap = techniqueIdWithLevel1GoodsIdDtos.stream()
                    .collect(Collectors.toMap(TechniqueIdWithLevel1GoodsIdDto::getTechniqueId, Function.identity()));

            shouldConditionList.stream()
                    .forEach(techniqueIds-> {
                        Set<Long> categoryIds = new HashSet<>();
                        Set<Long> techniqueIdSet = Arrays.asList(techniqueIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
                        for(Long techniqueId : techniqueIdSet){
                            TechniqueIdWithLevel1GoodsIdDto dto = techniqueIdWithLevel1GoodsIdDtoMap.get(techniqueId);
                            if(Objects.nonNull(dto) && Objects.nonNull(dto.getLevel1GoodsId()) && dto.getLevel1GoodsId() > 0){
                                categoryIds.add(dto.getLevel1GoodsId());
                            }else{
                                log.error("getTechniqueIdWithLevel1GoodsId error");
                            }
                        }
                        if(CollectionUtils.isNotEmpty(categoryIds)){
                            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                            boolQueryBuilder.must(stringMatchQueryBuilder(fieldName,techniqueIds,Operator.AND));
                            categoryIds.forEach(categoryId -> {
                                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("waitConfirmTechniqueVerifyCategoryIds", Lists.newArrayList(categoryId)));
                                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("toTechniqueVerifyCategoryIds", Lists.newArrayList(categoryId)));
                                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("techniqueVerifyingCategoryIds", Lists.newArrayList(categoryId)));
                                boolQueryBuilder.mustNot(QueryBuilders.termsQuery("techniqueVerifyFailedCategoryIds", Lists.newArrayList(categoryId)));

                            });
                            shouldQuery.should(boolQueryBuilder);
                        }
                    });
        }

        shouldQuery.minimumShouldMatch(minimumShouldMatch);
        return shouldQuery;
    }





    public MatchQueryBuilder stringMatchQueryBuilder(String fieldName, String value, Operator operator) {
        //设置查询类型为MatchQuery。
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder(fieldName,value);
        matchQueryBuilder.operator(operator);
        return matchQueryBuilder;
    }


    private QueryBuilder getMatchOrderMasterServeDivisionQuery(MasterMatchCondition masterMatchCondition) {

        QueryBuilder queryBuilder;

        //区域
        switch (masterMatchCondition.getDivisionMatchLevel()) {
            case 3:
                queryBuilder = QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(masterMatchCondition.getThirdDivisionId()));
                break;
            case 4:
                queryBuilder = QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(masterMatchCondition.getFourthDivisionId()));
                break;
            default:
                //never 没有区域的订单不会执行到查询
                queryBuilder = QueryBuilders.termsQuery("serveDivisionIds", Lists.newArrayList(masterMatchCondition.getThirdDivisionId()));
                break;
        }
        return queryBuilder;
    }

    private BoolQueryBuilder getMatchOrderMasterDistanceQuery(MasterMatchCondition masterMatchCondition, Double distance) {
        if (StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())) {
            String[] latLngArry = masterMatchCondition.getOrderLngLat().split(",");
            if (latLngArry.length == 2) {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                //按照距离取有经纬度的师傅
                GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
                //设置中心点
                geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArry[1]),Double.valueOf(latLngArry[0])));
                //设置条件为到中心点的距离不超过10000米
                geoDistanceQueryBuilder.distance(distance * 1000, DistanceUnit.METERS);

                //取没有经纬度的师傅 &&三级服务地址相匹配的师傅
                BoolQueryBuilder noGeoDistanceQueryBuilder = new BoolQueryBuilder();

                //三级服务地址相匹配的师傅
                TermQueryBuilder serveDivisionLv3Query = QueryBuilders.termQuery("serveDivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId()));
                //无经纬度的师傅
                GeoBoundingBoxQueryBuilder geoBoundingBoxQueryBuilder = new GeoBoundingBoxQueryBuilder("latLng");
                GeoPoint topLeft = new GeoPoint(0.1,0.0);
                GeoPoint bottomRight = new GeoPoint(0.0,0.1);
                geoBoundingBoxQueryBuilder.setCorners(topLeft,bottomRight);
                noGeoDistanceQueryBuilder.must(serveDivisionLv3Query).must(geoBoundingBoxQueryBuilder);


                if(ServeType.isDeliveryTypeNonReturn(masterMatchCondition.getServeType()) && StringUtils.isNotBlank(masterMatchCondition.getPickupAddressLngLat())){
                    String[] destLatLngArray = masterMatchCondition.getPickupAddressLngLat().split(",");
                    if (destLatLngArray.length == 2) {
                        //按照距离取有经纬度的师傅
                        GeoDistanceQueryBuilder destGeoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
                        //设置中心点
                        destGeoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(destLatLngArray[1]),Double.valueOf(destLatLngArray[0])));
                        //设置条件为到中心点的距离不超过10000米
                        destGeoDistanceQueryBuilder.distance(distance * 1000, DistanceUnit.METERS);
                        boolQueryBuilder.should(destGeoDistanceQueryBuilder);
                    }
                }

                boolQueryBuilder.should(geoDistanceQueryBuilder);
                boolQueryBuilder.should(noGeoDistanceQueryBuilder);
                boolQueryBuilder.minimumShouldMatch(1);
                return boolQueryBuilder;
            }
        }
        return null;
    }


    public Set<Long> searchOrderList(Long masterId, Set<Long> fourthDivisionIdList, List<Long> addFourthDivisionIdList,
                                     List<Long> addTechniqueIdList, String masterTechniqueIds,
                                     String masterForbiddenServeIds, String masterForbiddenBusinessIds,
                                     MasterBaseSearch masterBaseSearch) {

        if (StringUtils.isBlank(masterTechniqueIds)) {
            return null;
        }


        //限制推单的业务线过滤
        Set<Long> forbiddenBusinessLineIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenBusinessIds)) {
            forbiddenBusinessLineIdSet = Arrays.stream(masterForbiddenBusinessIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        //限制推单的服务
        Set<Long> forbiddenServeIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenServeIds)) {
            forbiddenServeIdSet = Arrays.stream(masterForbiddenServeIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        Set<Long> masterTechniqueIdSet = Arrays.stream(masterTechniqueIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());


        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder boolQueryBuilder1 = new BoolQueryBuilder();

        if (CollectionUtils.isNotEmpty(fourthDivisionIdList)) {
            fourthDivisionIdList.forEach(fourthDivisionId -> boolQueryBuilder1.should(QueryBuilders.termQuery("fourthDivisionId", fourthDivisionId)));
        } else if (CollectionUtils.isNotEmpty(addFourthDivisionIdList)) {
            addFourthDivisionIdList.forEach(fourthDivisionId -> boolQueryBuilder1.should(QueryBuilders.termQuery("fourthDivisionId", fourthDivisionId)));
        } else {
            fourthDivisionIdList.forEach(fourthDivisionId -> boolQueryBuilder1.should(QueryBuilders.termQuery("fourthDivisionId", fourthDivisionId)));
        }

        boolQueryBuilder1.minimumShouldMatch(1);
        boolQueryBuilder.must(boolQueryBuilder1);
        boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", "trading"));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", 2));
        boolQueryBuilder.must(QueryBuilders.termQuery("confirmServeStatus", 0));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("offerEndTime").gte(DateUtils.getCurrentTimeStamp()));

        if (CollectionUtils.isNotEmpty(forbiddenBusinessLineIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("businessLineId", forbiddenBusinessLineIdSet));
        }
        if (CollectionUtils.isNotEmpty(forbiddenServeIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serveIdsAnalyzed", forbiddenServeIdSet));
        }


        if(StringUtils.isNotBlank(masterBaseSearch.getWaitConfirmTechniqueVerifyCategoryIds())){
            Set<Long> waitConfirmTechniqueVerifyCategoryIdSet = Arrays.stream(masterBaseSearch.getWaitConfirmTechniqueVerifyCategoryIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("categoryId", waitConfirmTechniqueVerifyCategoryIdSet));
        }


        if(StringUtils.isNotBlank(masterBaseSearch.getToTechniqueVerifyCategoryIds())){
            Set<Long> toTechniqueVerifyingCategoryIdSet = Arrays.stream(masterBaseSearch.getToTechniqueVerifyCategoryIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("categoryId", toTechniqueVerifyingCategoryIdSet));
        }


        if(StringUtils.isNotBlank(masterBaseSearch.getTechniqueVerifyingCategoryIds())){
            Set<Long> techniqueVerifyingCategoryIdSet = Arrays.stream(masterBaseSearch.getTechniqueVerifyingCategoryIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("categoryId", techniqueVerifyingCategoryIdSet));
        }

        if(StringUtils.isNotBlank(masterBaseSearch.getTechniqueVerifyFailedCategoryIds())){
            Set<Long> techniqueVerifyFailedCategoryIdSet = Arrays.stream(masterBaseSearch.getTechniqueVerifyFailedCategoryIds().split(",")).map(Long::parseLong).collect(Collectors.toSet());
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("categoryId", techniqueVerifyFailedCategoryIdSet));
        }


        log.info("master search orderList push,searchOrderList boolQueryBuilder:{}", boolQueryBuilder.toString());
        List<OrderBaseInfo> orderBaseInfoList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        int searchCount = 0;
        while (true) {
            if (orderBaseInfoList.size() >= masterSettleInPushCount || searchCount >= masterSettleInSearchCount) {
                break;
            }
            EsResponse<OrderBaseInfo> esResponse = orderBaseInfoEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                searchCount = searchCount + esResponse.getDataList().size();

                esResponse.getDataList().forEach(orderBaseInfo -> {
                    if (StringUtils.isBlank(orderBaseInfo.getOrderTechniqueIds())) {
                        return;
                    }

                    Set<String> orderTechniqueSet = Arrays.stream(orderBaseInfo.getOrderTechniqueIds().split("\\|")).collect(Collectors.toSet());
                    for (String orderTechnique : orderTechniqueSet) {
                        Set<Long> orderTechniqueIdSet = Arrays.stream(orderTechnique.split(",")).map(Long::parseLong).collect(Collectors.toSet());
                        if (CollectionUtils.isNotEmpty(addTechniqueIdList)) {
                            boolean disJoint = Collections.disjoint(addTechniqueIdList, orderTechniqueIdSet);
                            if (disJoint) {
                                continue;
                            }
                        }
                        if (masterTechniqueIdSet.containsAll(orderTechniqueIdSet)) {
                            orderBaseInfoList.add(orderBaseInfo);
                            break;
                        }

                    }

                });

                pageNum++;
            } else {
                break;
            }
        }
        if (CollectionUtils.isEmpty(orderBaseInfoList)) {
            return Collections.emptySet();
        }
        Set<Long> orderIdSet = orderBaseInfoList.stream().map(orderBaseInfo -> orderBaseInfo.getOrderId()).collect(Collectors.toSet());
        return orderIdSet;
    }


    public Set<Long> searchOrderListByDivision(Set<Long> fourthDivisionIdList, String masterTechniqueIds,
                                               String masterForbiddenServeIds, String masterForbiddenBusinessIds) {

        if (StringUtils.isBlank(masterTechniqueIds)) {
            return null;
        }

        //限制推单的业务线过滤
        Set<Long> forbiddenBusinessLineIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenBusinessIds)) {
            forbiddenBusinessLineIdSet = Arrays.stream(masterForbiddenBusinessIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        //限制推单的服务
        Set<Long> forbiddenServeIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenServeIds)) {
            forbiddenServeIdSet = Arrays.stream(masterForbiddenServeIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        Set<Long> masterTechniqueIdSet = Arrays.stream(masterTechniqueIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());


        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder divisionQueryBuilder = new BoolQueryBuilder();

        fourthDivisionIdList.forEach(fourthDivisionId -> divisionQueryBuilder.should(QueryBuilders.termQuery("fourthDivisionId", fourthDivisionId)));

        divisionQueryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.must(divisionQueryBuilder);
        boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", "trading"));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", 2));
        boolQueryBuilder.must(QueryBuilders.termQuery("confirmServeStatus", 0));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("offerEndTime").gte(DateUtils.getCurrentTimeStamp()));

        if (CollectionUtils.isNotEmpty(forbiddenBusinessLineIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("businessLineId", forbiddenBusinessLineIdSet));
        }
        if (CollectionUtils.isNotEmpty(forbiddenServeIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serveIdsAnalyzed", forbiddenServeIdSet));
        }


        log.info("master search orderList push,searchOrderListByDivision boolQueryBuilder:{}", boolQueryBuilder.toString());

        List<OrderBaseInfo> orderBaseInfoList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        int searchCount = 0;
        while (true) {
            if (orderBaseInfoList.size() >= masterSettleInPushCount || searchCount >= masterSettleInSearchCount) {
                break;
            }
            EsResponse<OrderBaseInfo> esResponse = orderBaseInfoEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);

            if (CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                searchCount = searchCount + esResponse.getDataList().size();
                esResponse.getDataList().forEach(orderBaseInfo -> {
                    if (StringUtils.isBlank(orderBaseInfo.getOrderTechniqueIds())) {
                        return;
                    }

                    Set<String> orderTechniqueSet = Arrays.stream(orderBaseInfo.getOrderTechniqueIds().split("\\|")).collect(Collectors.toSet());
                    for (String orderTechnique : orderTechniqueSet) {
                        Set<Long> orderTechniqueIdSet = Arrays.stream(orderTechnique.split(",")).map(Long::parseLong).collect(Collectors.toSet());
                        if (masterTechniqueIdSet.containsAll(orderTechniqueIdSet)) {
                            orderBaseInfoList.add(orderBaseInfo);
                            break;
                        }

                    }

                });

                pageNum++;
            } else {
                break;
            }
        }
        if (CollectionUtils.isEmpty(orderBaseInfoList)) {
            return Collections.emptySet();
        }
        Set<Long> orderIdSet = orderBaseInfoList.stream().map(orderBaseInfo -> orderBaseInfo.getOrderId()).collect(Collectors.toSet());
        return orderIdSet;
    }


    public Set<Long> searchOrderListByDistance(double masterLat, double masterLng, String masterTechniqueIds,
                                               String masterForbiddenServeIds, String masterForbiddenBusinessIds) {

        if (StringUtils.isBlank(masterTechniqueIds)) {
            return null;
        }

        //限制推单的业务线过滤
        Set<Long> forbiddenBusinessLineIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenBusinessIds)) {
            forbiddenBusinessLineIdSet = Arrays.stream(masterForbiddenBusinessIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        //限制推单的服务
        Set<Long> forbiddenServeIdSet = new HashSet<>();
        if (!Strings.isNullOrEmpty(masterForbiddenServeIds)) {
            forbiddenServeIdSet = Arrays.stream(masterForbiddenServeIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }

        Set<Long> masterTechniqueIdSet = Arrays.asList(masterTechniqueIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());

        Integer masterSettleInPushDistance = 10;
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", "trading"));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", 2));
        boolQueryBuilder.must(QueryBuilders.termQuery("confirmServeStatus", 0));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("offerEndTime").gte(DateUtils.getCurrentTimeStamp()));


        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
        //设置中心点
        geoDistanceQueryBuilder.point(new GeoPoint(masterLat, masterLng));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(masterSettleInPushDistance * 1000, DistanceUnit.METERS);
        boolQueryBuilder.must(geoDistanceQueryBuilder);

        if (CollectionUtils.isNotEmpty(forbiddenBusinessLineIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("businessLineId", forbiddenBusinessLineIdSet));
        }
        if (CollectionUtils.isNotEmpty(forbiddenServeIdSet)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serveIdsAnalyzed", forbiddenServeIdSet));
        }


        log.info("master search orderList push,searchOrderListByDistance boolQueryBuilder:{}", boolQueryBuilder.toString());

        List<OrderBaseInfo> orderBaseInfoList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        int searchCount = 0;
        while (true) {
            if (orderBaseInfoList.size() >= masterSettleInPushCount || searchCount >= masterSettleInSearchCount) {
                break;
            }
            EsResponse<OrderBaseInfo> esResponse = orderBaseInfoEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);

            if (CollectionUtils.isNotEmpty(esResponse.getDataList())) {

                if (CollectionUtils.isEmpty(masterTechniqueIdSet)) {
                    orderBaseInfoList.addAll(esResponse.getDataList());
                    searchCount = searchCount + esResponse.getDataList().size();
                } else {
                    for (OrderBaseInfo orderBaseInfo : esResponse.getDataList()) {
                        if (StringUtils.isBlank(orderBaseInfo.getOrderTechniqueIds())) {
                            break;
                        }
                        Set<String> orderTechniqueSet = Arrays.stream(orderBaseInfo.getOrderTechniqueIds().split("\\|")).collect(Collectors.toSet());
                        for (String orderTechnique : orderTechniqueSet) {
                            Set<Long> orderTechniqueIdSet = Arrays.stream(orderTechnique.split(",")).map(Long::parseLong).collect(Collectors.toSet());
                            if (masterTechniqueIdSet.containsAll(orderTechniqueIdSet)) {
                                orderBaseInfoList.add(orderBaseInfo);
                                searchCount = searchCount + 1;
                                break;
                            }

                        }
                    }
                }
                pageNum++;
            } else {
                break;
            }
        }
        if (CollectionUtils.isEmpty(orderBaseInfoList)) {
            return Collections.emptySet();
        }
        Set<Long> orderIdSet = orderBaseInfoList.stream().map(orderBaseInfo -> orderBaseInfo.getOrderId()).collect(Collectors.toSet());
        return orderIdSet;
    }



}
