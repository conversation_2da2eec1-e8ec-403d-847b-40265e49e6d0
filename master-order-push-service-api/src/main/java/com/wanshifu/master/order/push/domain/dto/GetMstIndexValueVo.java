package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
public class GetMstIndexValueVo {

    private List<ParamRqt> indexNames;
    private List<String> accountIds;

    @Data
    public static class ParamRqt {
        private String indexName;
        private DimensionInfo dimensionInfo;
    }

    @Data
    @AllArgsConstructor
    public static class DimensionInfo {
        private String goods_lv1_id;
    }
}
