package com.wanshifu.master.order.push.domain.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 15:19
 */
@Data
@NoArgsConstructor
public class MasterQuotaResp {


    private String quotaCode;
    private String quotaName;
    private String quotaDesc;
    private String valueType;

    public List<EnumValue> enumValueList;

    @Data
    public static class EnumValue {
        private String code;
        private String name;
    }

    public MasterQuotaResp(String quotaCode, String quotaName, String quotaDesc, String valueType) {
        this.quotaCode = quotaCode;
        this.quotaName = quotaName;
        this.quotaDesc = quotaDesc;
        this.valueType = valueType;
    }

}
