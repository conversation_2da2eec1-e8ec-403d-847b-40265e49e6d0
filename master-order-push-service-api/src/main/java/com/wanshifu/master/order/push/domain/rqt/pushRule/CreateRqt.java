package com.wanshifu.master.order.push.domain.rqt.pushRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.repushPolicy.PushStrategyVo;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  创建重推机制.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 15:21
 */
@Data
public class CreateRqt {


    @NotNull
    private Integer businessLineId;

    /**
     * 机制名称
     */
    @NotEmpty
    private String ruleName;

    /**
     * 机制描述
     */
    private String ruleDesc;

    /**
     * 适用类目，多个用逗号拼接 all:所有类目
     */
    @NotEmpty
    @Valid
    private List<PushRuleEntity> pushRuleList;


    @Data
    public static class PushRuleEntity{

        @NotNull
        private Integer appointType;

        @NotNull
        private String pushRuleType;

        /**
         * 大数据ab实验编号
         */
        private Integer testId;

        /**
         * 是否ab实验，1：是，0：否
         */
        private Integer testFlag;

        /**
         * 大数据实验组别id
         */
        private Integer testGroupId;

        /**
         * 大数据实验组别名称
         */
        private String testGroupName;

        /**
         * 最佳报价数
         */
        private Integer bestOfferNum;

        private Integer bestOfferIntervalTime;

        @Valid
        private FixedRoundsRule fixedRoundsRule;

        @Valid
        private List<DynamicRoundsRule> dynamicRoundsRuleList;

    }


    @Data
    public static class FixedRoundsRule{

        /**
         * 最佳报价数
         */
        @NotNull
        private Integer bestOfferNum;

        /**
         * 推送时间间隔
         */
        @NotNull
        private Integer delayMinutesBetweenRounds;

        /**
         * 首轮推送人数
         */
        @NotNull
        private Integer firstPushMasterNumPerRound;

        /**
         * 非首轮推送人数
         */
        @NotNull
        private Integer delayPushMasterNumPerRound;

        /**
         * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String firstPushMasterFlag;

        /**
         * 首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal firstPushMasterPercent;

        /**
         * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String delayPushMasterFlag;

        /**
         * 非首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal delayPushMasterPercent;    }


    @Data
    public static class DynamicRoundsRule{

        @NotNull
        private String rounds;

        @NotNull
        private Integer pushScoreStartValue;

        @NotNull
        private Integer pushScoreEndValue;

        @NotNull
        private BigDecimal offerRate;

        @NotNull
        private Integer masterNum;

        @NotNull
        private Integer batchNum;

        @NotNull
        private Integer deliveryPercent;

        @Valid
        private List<WheelRoundRule> wheelRoundRuleList;

    }


    @Data
    public static class WheelRoundRule{

        @Valid
        private TriggerParam triggerParam;

        @NotNull
        private String pushTarget;

        private String pushRate;

        private Integer batchTime;

        private Integer batchNum;

    }

    @Data
    public static class TriggerParam{

        @NotBlank
        @ValueIn("and,or")
        private String condition;

        @NotEmpty
        @Valid
        private List<Item> itemList;

    }

    @Data
    public static class Item{

        @NotBlank
        private String itemName;

        @NotBlank
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        private String itemValue;
    }

    private Long createAccountId;

}