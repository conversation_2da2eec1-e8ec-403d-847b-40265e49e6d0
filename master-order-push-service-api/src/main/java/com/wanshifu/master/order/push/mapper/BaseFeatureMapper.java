package com.wanshifu.master.order.push.mapper;

import com.wanshifu.framework.persistence.base.IBaseCommMapper;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 基础特征Mapper
 * <AUTHOR>
 */
public interface BaseFeatureMapper extends IBaseCommMapper<BaseFeature> {

    List<Map<String,Object>> selectFeature(@Param("sql") String sql);


}