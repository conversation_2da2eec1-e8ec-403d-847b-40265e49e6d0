package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.ExclusiveOrderScheduler;
import com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "exclusiveOrderScheduler",
        configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface ExclusiveOrderSchedulerApi {

    /**
     * 列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<ExclusiveOrderScheduler> list(@RequestBody @Valid ListRqt rqt);
    /**
     * 创建
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    ExclusiveOrderScheduler detail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 启用/禁用
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);


}
