package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 策略组合快照表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "strategy_combination_snapshot")
public class StrategyCombinationSnapshot {

    /**
     * 快照id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "snapshot_id")
    private Long snapshotId;

    /**
     * 组合名称
     */
    @Column(name = "combination_name")
    private String combinationName;

    /**
     * 组合描述
     */
    @Column(name = "combination_desc")
    private String combinationDesc;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 城市id，多个以逗号拼接
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 优先推荐路由(JSON格式)
     */
    @Column(name = "priority_strategy_combination")
    private String priorityStrategyCombination;

    /**
     * 备用推荐路由(JSON格式)
     */
    @Column(name = "alternate_strategy_combination")
    private String alternateStrategyCombination;

    /**
     * 组合状态，1：启用，0：禁用
     */
    @Column(name = "combination_status")
    private Integer combinationStatus;

    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 订单标识,normal:普通订单 ikea:宜家订单
     */
    @Column(name = "order_flag")
    private String orderFlag;
}