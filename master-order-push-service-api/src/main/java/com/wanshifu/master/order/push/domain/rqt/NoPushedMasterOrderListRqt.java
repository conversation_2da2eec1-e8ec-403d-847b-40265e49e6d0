package com.wanshifu.master.order.push.domain.rqt;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 未推单列表查询请求实体类
 * <AUTHOR>
 * @date 2023-07-12 11:26:00
 */
@Data
public class NoPushedMasterOrderListRqt extends Pager {

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 推单开始时间
     */
    @NotNull
    private Date pushTimeStart;

    /**
     * 推单结束时间
     */
    @NotNull
    private Date pushTimeEnd;

    /**
     * 区县id
     */
    private Long thirdDivisionId;

    /**
     * 街道id
     */
    private Long fourthDivisionId;

    /**
     * 最子级商品id
     */
    private Long childGoodsId;

    /**
     * 服务类型id
     */
    private String serveType;

    /**
     * 指派模式
     */
    private String appointType;

    /**
     * 类目
     */
    private String categoryIds;

    /**
     * 订单来源
     */
    private String orderFrom;


    private List<Integer> serveTypeList;

    private List<Integer> appointTypeList;

    private List<String> orderFromList;

    private Long divisionId;
    
    private String orderNo;

    private List<Integer> categoryList;

}
