package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilder;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * Title
 * Author <EMAIL>
 * Time 2019/4/12
 */
public interface ElasticsearchRepository<T> {

    default SearchResponse search(QueryBuilder queryBuilder, RestHighLevelClient client) throws IOException {
        return search(queryBuilder, client, null);
    }

    default SearchResponse search(QueryBuilder queryBuilder, AggregationBuilder aggregationBuilder,RestHighLevelClient client) throws IOException {
        return search(queryBuilder, client, null,null,aggregationBuilder);
    }

    default SearchResponse search(QueryBuilder queryBuilder, RestHighLevelClient client, Pageable pageable) throws IOException {
        return search(queryBuilder, client, pageable,null);
    }

    default SearchResponse search(QueryBuilder queryBuilder, RestHighLevelClient client, Pageable pageable, List<SortBuilder> sortBuilderList) throws IOException {
        Class<T> clazz = getElasticEntityClass();
        Document annotation = clazz.getAnnotation(Document.class);
        String indexAlias = annotation.indexAlias();
        String type = annotation.type();
        SearchRequest searchRequest = new SearchRequest(indexAlias);
        searchRequest.types(type);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);

        if(CollectionUtils.isNotEmpty(sortBuilderList)) {
            sortBuilderList.forEach(searchSourceBuilder::sort);
        }

        if (pageable != null) {
            searchSourceBuilder.from((pageable.getPageNum() - 1) * pageable.getPageSize());
            searchSourceBuilder.size(pageable.getPageSize());
        }
        searchRequest.source(searchSourceBuilder);

        //closeClient(client);

        return client.search(searchRequest);
    }

    default SearchResponse search(QueryBuilder queryBuilder, RestHighLevelClient client, Pageable pageable,
                                  List<SortBuilder> sortBuilderList, AggregationBuilder aggregationBuilder) throws IOException {
        Class<T> clazz = getElasticEntityClass();
        Document annotation = clazz.getAnnotation(Document.class);
        String indexAlias = annotation.indexAlias();
        String type = annotation.type();
        SearchRequest searchRequest = new SearchRequest(indexAlias);
        searchRequest.types(type);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        if(Objects.nonNull(aggregationBuilder)){
            searchSourceBuilder.aggregation(aggregationBuilder);
        }

        if(CollectionUtils.isNotEmpty(sortBuilderList)) {
            sortBuilderList.forEach(searchSourceBuilder::sort);
        }

        if (pageable != null) {
            searchSourceBuilder.from((pageable.getPageNum() - 1) * pageable.getPageSize());
            searchSourceBuilder.size(pageable.getPageSize());
        }
        searchRequest.source(searchSourceBuilder);

        //closeClient(client);

        return client.search(searchRequest);
    }

    Class<T> getElasticEntityClass();

    void closeClient(RestHighLevelClient client);

}
