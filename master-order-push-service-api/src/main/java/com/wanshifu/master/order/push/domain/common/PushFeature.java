package com.wanshifu.master.order.push.domain.common;

import java.util.*;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单推送特征实体类
 * <AUTHOR>
 */
@Data
@Slf4j
public class PushFeature {
	private Long globalOrderId;
	private DefaultContext<String, Object> orderFeature = new DefaultContext<>();
	private DefaultContext<String, Object> dimensionFeature = new DefaultContext<>();
	private DefaultContext<String, DefaultContext<String, Object>> masterFeature = new DefaultContext<>();
	private List<List<String>> multiValueFeatureList= new ArrayList<>();

	/**
	 * 获取订单特征集合
	 *
	 * @param additionFeatureList
	 * @return
	 */
	public Map<String, Object> getOrderFeatureByList(Set<String> additionFeatureList) {
		Map<String, Object> tmpOrderFeatureMap = new HashMap<>();
		additionFeatureList.stream().forEach(featureName -> {
			Object tmpFeatureValue = getSingleOrderFeature(featureName);
			if (tmpFeatureValue != null) {
				tmpOrderFeatureMap.put(featureName, tmpFeatureValue);
			}
		});
		return tmpOrderFeatureMap;
	}


	public PushFeature(Long globalOrderId,Set<String> masterSet) {
		masterSet.stream().forEach(masterId -> masterFeature.put(masterId, new DefaultContext<>()));
		this.globalOrderId = globalOrderId;
	}


	public PushFeature(){

	}


	/**
	 * 添加订单特征
	 *
	 * @param featureName
	 * @param featureValue
	 */
	public void addOrderFeature(String featureName, Object featureValue) {
		orderFeature.put(featureName, featureValue);
	}


	/**
	 * 尝试获取订单特征
	 * @param key
	 * @return
	 */
	public String getOrderFeatureByName(String key) {
		String result=null;
		try {
			result=orderFeature.get(key).toString();
		} catch (Exception e) {
		}
		return result;
	}

	/**
	 * @param manyFeature
	 */
	public void addDimensionFeature(Map<String, Object> manyFeature) {
		dimensionFeature.putAll(manyFeature);
		orderFeature.putAll(manyFeature);
	}


	/**
	 * 添加师傅特征
	 *
	 * @param featureName
	 * @param featureValue
	 */
	public void addMasterFeature(String masterId, DefaultContext<String, Object> currentMasterFeatureMap) {
		if (masterFeature.containsKey(masterId)) {
			masterFeature.get(masterId).putAll(currentMasterFeatureMap);
		} else {
			masterFeature.put(masterId, currentMasterFeatureMap);
		}
	}


	public Object getSingleOrderFeature(String featureName) {
		return orderFeature.get(featureName);
	}


	public void addMultiValueFeature(List<String> featureList){
		multiValueFeatureList.add(featureList);
	}


}
