package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 创建特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@ApiModel("创建特殊人群策略请求类")
@Data
public class CreateRqt {

    @ApiModelProperty(value = "业务线id", required = true)
    @NotNull(message = "业务线id不能为空")
    private Integer businessLineId;

    @Pattern(
            regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9\\-_]*$",
            message = "仅支持数字、中文、字母、-、_"
    )
    @ApiModelProperty(value = "策略名称", required = true)
    @NotEmpty(message = "策略名称不能为空")
    protected String strategyName;

    @ApiModelProperty(value = "策略描述")
    @Length(max = 500, message = "策略描述长度不能超过500字符")
    protected String strategyDesc;

    @ApiModelProperty(value = "服务id集合", required = true)
    @NotBlank(message = "服务id不能为空")
    protected String serveIds;

    @ApiModelProperty(value = "区域级别,如city,country", required = true)
    @NotBlank(message = "区域级别不能为空")
    protected String regionLevel;

    @ApiModelProperty(value = "城市id集合，多个用逗号分隔")
    protected String cityIds;

    @ApiModelProperty(value = "下单模式，2:报价招标，4:一口价，5:预付款", required = true)
    @NotBlank(message = "下单模式不能为空")
    protected String serveModels;

    @ApiModelProperty(value = "调度人群，多个用逗号分隔", required = true)
    @NotBlank(message = "调度人群不能为空")
    protected String pushGroups;

    @ApiModelProperty(value = "延迟推送下一优先级的时间（分钟）", required = true)
    @Range(min = 1, max = 720, message = "延迟推送时间必须在1-720分钟之间")
    @NotNull(message = "延迟推送时间不能为空")
    protected Integer delayMinutes;

    @ApiModelProperty(value = "过滤人群")
    protected String filterGroups;

    @ApiModelProperty(value = "更新人id")
    private Long createAccountId;
}
