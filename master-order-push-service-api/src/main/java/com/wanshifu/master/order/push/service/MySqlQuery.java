package com.wanshifu.master.order.push.service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.mapper.BaseFeatureMapper;
import com.wanshifu.master.order.push.repository.BaseFeatureRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * 查询数据库工具类
 *
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class MySqlQuery {


	@Resource
	private DataSource dataSource;

	@Resource
	private BaseFeatureRepository baseFeatureRepository;

	/**
	 * 根据特定格式的入参返回查询结果
	 *
	 * @param param 查询入参
	 * @return 查询结果
	 */
	public JSONArray query(Collection<String> dimensionColumns, Collection<String> indexColumns,String tableName,String conditions) {
		JSONObject resultObject = new JSONObject();
		try {
			List<String> fieldColumnList = new ArrayList<>();
			fieldColumnList.addAll(dimensionColumns);
			fieldColumnList.addAll(indexColumns);
			// 查询结果处理
			return queryData(fieldColumnList, tableName,conditions);
		} catch (Exception e) {
			log.error("数据查询失败", e);
		}
		return null;
	}


	private JSONArray parseResultSet(List<String> fieldColumnList,ResultSet resultSet){
		JSONArray jsonArray = new JSONArray();
		try{
			while(resultSet.next()){
				JSONObject jsonObject = new JSONObject();
				fieldColumnList.forEach(fieldColumn -> {
					try{
						Object value = resultSet.getObject(fieldColumn);
						if(value instanceof Timestamp){
							jsonObject.put(fieldColumn,DateUtils.formatDateTime(new Date(((Timestamp) value).getTime())));
						}else{
							jsonObject.put(fieldColumn,resultSet.getObject(fieldColumn));
						}

					}catch(Exception e){
						e.printStackTrace();
					}
				});
				jsonArray.add(jsonObject);
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return jsonArray;
	}


	private JSONArray parseResultSet(List<String> fieldColumnList,List<Map<String,Object>> resultMapList){
		JSONArray jsonArray = new JSONArray();
		try{
			resultMapList.forEach(resultMap -> {
				JSONObject jsonObject = new JSONObject();
				fieldColumnList.forEach(fieldColumn -> {
					try{
						Object value = resultMap.get(fieldColumn);
						if(value instanceof Timestamp){
							jsonObject.put(fieldColumn,DateUtils.formatDateTime(new Date(((Timestamp) value).getTime())));
						}else{
							jsonObject.put(fieldColumn,value);
						}

					}catch(Exception e){
						e.printStackTrace();
					}
				});
				jsonArray.add(jsonObject);
			});
		}catch(Exception e){
			e.printStackTrace();
		}
		return jsonArray;
	}


	/**
	 * 根据传入参数和存储配置查询结果
	 *
	 * @param queryFieldList 需要查询的字段集合
	 * @param conditions     查询条件
	 * @return 查询结果
	 * @throws Exception sql查询异常
	 */
	private JSONArray queryDataBackup(List<String> queryFieldList,String tableName, String conditions) throws Exception {

		Connection connection = null;
		Statement statement = null;
		ResultSet resultSet = null;
		try {

			// sql语句拼接
			connection = dataSource.getConnection();
			statement = connection.createStatement();

			StringBuilder sqlBuilder = new StringBuilder("select ");
			for (String fieldName : queryFieldList) {
				sqlBuilder.append(fieldName);
				sqlBuilder.append(",");
				// 结果参数记录
			}
			sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
			sqlBuilder.append(" from ");
			sqlBuilder.append(tableName);
			if (!StringUtils.isEmpty(conditions)) {
				sqlBuilder.append(" where ");
				sqlBuilder.append(conditions);
			}
			log.info("sqlSBuilder" + sqlBuilder.toString());
			resultSet = statement.executeQuery(sqlBuilder.toString());
			log.info("resultJSONArray" + resultSet.toString());
			JSONArray jsonArray = parseResultSet(queryFieldList, resultSet);
			return jsonArray;
		}catch(Exception e) {
			log.error("queryData:",e);
		}finally {
			if(resultSet != null){
				resultSet.close();
			}

			if(statement != null){
				statement.close();
			}

			if(connection != null){
				connection.close();
			}
		}
		return null;

	}


	/**
	 * 根据传入参数和存储配置查询结果
	 *
	 * @param queryFieldList 需要查询的字段集合
	 * @param conditions     查询条件
	 * @return 查询结果
	 * @throws Exception sql查询异常
	 */
	private JSONArray queryData(List<String> queryFieldList,String tableName, String conditions) {

		StringBuilder sqlBuilder = new StringBuilder("select ");
		for (String fieldName : queryFieldList) {
			sqlBuilder.append(fieldName);
			sqlBuilder.append(",");
			// 结果参数记录
		}
		sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
		sqlBuilder.append(" from ");
		sqlBuilder.append(tableName);
		if (!StringUtils.isEmpty(conditions)) {
			sqlBuilder.append(" where ");
			sqlBuilder.append(conditions);
		}
		log.info("sqlSBuilder" + sqlBuilder.toString());
		List<Map<String,Object>> resultMapList = baseFeatureRepository.selectFeature(sqlBuilder.toString());
		log.info("resultJSONArray" + JSON.toJSONString(resultMapList));
		JSONArray jsonArray = parseResultSet(queryFieldList, resultMapList);
		return jsonArray;


	}





}
