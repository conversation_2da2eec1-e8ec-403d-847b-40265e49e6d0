package com.wanshifu.master.order.push.domain.rqt.sortingStrategy;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 描述 :  精排策略列表Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id
     */
    private Long businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 状态 1:启用 0:禁用
     */
    @ValueIn("0,1")
    private Integer strategyStatus;

    /**
     * 类目id
     */
    private String categoryIds;

    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;


    private String orderFlag;
}