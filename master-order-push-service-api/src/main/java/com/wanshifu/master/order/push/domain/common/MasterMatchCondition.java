package com.wanshifu.master.order.push.domain.common;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 师傅匹配条件实体类
 * <AUTHOR>
 */
@Slf4j
@Data
public class MasterMatchCondition {

    public MasterMatchCondition() {
        this.isBlackListStatusNormal=true;
        this.isAccountNormal=true;
        this.isSettleStatusNormal=true;
        this.isRuleExamStatusNormal=true;
        this.masterType="personal";
        this.isAccordingDistancePush=false;
    }

    /**
     * 黑名单
     */
    private boolean isBlackListStatusNormal;

    /**
     * 账号状态
     */

    private boolean isAccountNormal;
    /**
     * 入驻状态
     */
    private boolean isSettleStatusNormal;

    /**
     * 规则考试
     */
    private boolean isRuleExamStatusNormal;

    /**
     * 至少的活跃时间
     */
    private long activeTimeAtLeast;


    /**
     * 需求的师傅类型
     */
    private String masterType;


    /**
     * 需求的类目(冗余数据)
     */
    private Long categoryId;


    /**
     * 需求的技能类型(冗余数据)
     */
    private Integer serveType;


    /**
     * 需求的技能
     */
    private String technologysInDemand;

    /**
     * 需求的技能
     */
    private Set<String> technologysInDemandSet;

    /**
     * 二级
     */
    private Long secondDivisionId;

    /**
     * 三级
     */
    private Long thirdDivisionId;
    /**
     * 四级
     */
    private Long fourthDivisionId;


    /**
     * 严选
     */
    private boolean isNeesRigorousSelectionMaster=false;


    /**
     * 师傅品牌 ikea:宜家师傅
     */
    private String masterBrand;


    /**
     * 师傅来源类型，toc：C端师傅,tob: B端师傅
     */
    private String masterSourceType;
    /**
     * 地址匹配级别
     */
    private int divisionMatchLevel;


    private String pushMode;


    /**
     * 业务线
     */
    private Integer businessLineId;



    private Long masterOrderId;

    /**
     * 是否根据师傅和订单距离推送
     */
    private boolean isAccordingDistancePush ;

    /**
     * 匹配场景
     */
    private String matchSceneCode;


    public Long getMasterOrderId() {
        return masterOrderId;
    }


    public void setMasterOrderId(Long masterOrderId) {
        this.masterOrderId = masterOrderId;
    }

    /**
     * 2/3服务
     */
    private String serveIds;


    private String orderVersion;

    /**
     * 订单经纬度
     */
    private String orderLngLat;


    /**
     * 目的地经纬度
     */
    private String pickupAddressLngLat;

    public String getOrderVersion() {
        return orderVersion;
    }

    public void setOrderVersion(String orderVersion) {
        this.orderVersion = orderVersion;
    }

    /**
     * 招募ID
     */
    private String recruitIds;

    /**
     *
     */
    private JSONObject extraData=new JSONObject();

    public void setRecruitIds(String recruitIds) {
        this.recruitIds = recruitIds;
    }

    public String getOrderLngLat() {
        return orderLngLat;
    }


    public String getPickupAddressLngLat() {
        return pickupAddressLngLat;
    }


    public void setPickupAddressLngLat(String pickupAddressLngLat) {
        this.pickupAddressLngLat = pickupAddressLngLat;
    }

    public void setOrderLngLat(String orderLngLat) {
        this.orderLngLat = orderLngLat;
    }

    public void setTechnologysInDemandSet(Set<String> technologysInDemandSet) {
        this.technologysInDemandSet = technologysInDemandSet;
    }

    public void setThirdDivisionId(Long thirdDivisionId) {
        this.thirdDivisionId = thirdDivisionId;
    }

    public void setFourthDivisionId(Long fourthDivisionId) {
        this.fourthDivisionId = fourthDivisionId;
    }

    /**
     * 预处理师傅列表
     */
    private Set<String> tmpPreMasterSet;

    /**
     * 代理商订单临时列表
     */
    private Set<String> tmpMasterSet;

    /**
     * 专属订单临时列表
     */
    private Set<String> tmpExclusiveMasterSet;

    /**
     * 品牌师傅临时列表
     */
    private Set<String> tmpBrandMasterSet;

    /**
     * 测量安装单优先列表
     */
    private Set<String> tmpMeasurePriMasterSet;

    private Set<String> tmpMeasureMasterSet;

    /**
     * 订单包列表
     */
    private Set<String> tmpGroupMasterSet;

    /**
     * 附近距离
     */
    private BigDecimal nearbyDistance;


    /**
     * 排除分时师傅
     */
    private Integer isExclusivePartTimeMaster;



    /**
     * 解析招募列表(之前为list列表)
     */
    private void parseRecruitList(JSONObject extraInfo){
        try {
            final String integer = extraInfo.getString(FieldConstant.RECRUIT_ID);
            setRecruitIds(integer);
        } catch (Exception e) {
            log.warn("parseRecruitList failed!{},{}",extraInfo,e);
        }
    }



    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }

    /**
     * 是否需要品牌师傅
     * @return
     */
    public boolean needBrandMaster() {
        if ("ikea".equals(this.masterBrand)) {
            return true;
        }
        return false;
    }

    public String getServeIds() {
        return serveIds;
    }

    public void setServeIds(String serveIds) {
        this.serveIds = serveIds;
    }

    /**
     * 获取冻结恢复时间
     * @return
     */
    public long freezingRecoverTime() {
        return DateFormatterUtil.getNowTimeStamp();
    }


    public boolean isBlackListStatusNormal() {
        return isBlackListStatusNormal;
    }

    public boolean isAccountNormal() {
        return isAccountNormal;
    }

    public boolean isSettleStatusNormal() {
        return isSettleStatusNormal;
    }

    public boolean isRuleExamStatusNormal() {
        return isRuleExamStatusNormal;
    }

    public long getActiveTimeAtLeast() {
        return activeTimeAtLeast;
    }

    public String getMasterType() {
        return masterType;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public Integer getServeType() {
        return serveType;
    }

    public String getTechnologysInDemand() {
        return technologysInDemand;
    }

    public Set<String> getTechnologysInDemandSet() {
        return technologysInDemandSet;
    }

    public Long getThirdDivisionId() {
        return thirdDivisionId;
    }

    public Long getFourthDivisionId() {
        return fourthDivisionId;
    }

    public boolean isRigorousSelectionMaster() {
        return isNeesRigorousSelectionMaster;
    }

    public String getMasterBrand() {
        return masterBrand;
    }

    public int getDivisionMatchLevel() {
        return divisionMatchLevel;
    }

    public void setDivisionMatchLevel(Integer divisionMatchLevel) {
        if (divisionMatchLevel!=null) {
            this.divisionMatchLevel = divisionMatchLevel;
        }
    }


    public String getPushMode() {
        return pushMode;
    }

    public void setPushMode(String pushMode) {
        if (pushMode!=null) {
            this.pushMode = pushMode;
        }
    }

    public Integer getBusinessLineId() {
        return businessLineId;
    }

    public boolean isAccordingDistancePush() {
        return isAccordingDistancePush;
    }

    public void setAccordingDistancePush(boolean accordingDistancePush) {
        isAccordingDistancePush = accordingDistancePush;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MasterMatchCondition.class.getSimpleName() + "[", "]")
                .add("isBlackListStatusNormal=" + isBlackListStatusNormal)
                .add("isAccountNormal=" + isAccountNormal)
                .add("isSettleStatusNormal=" + isSettleStatusNormal)
                .add("isRuleExamStatusNormal=" + isRuleExamStatusNormal)
                .add("activeTimeAtLeast=" + activeTimeAtLeast)
                .add("masterType='" + masterType + "'")
                .add("categoryId='" + categoryId + "'")
                .add("serveType='" + serveType + "'")
                .add("technologysInDemand='" + technologysInDemand + "'")
                .add("technologysInDemandSet=" + technologysInDemandSet)
                .add("thirdDivisionId='" + thirdDivisionId + "'")
                .add("fourthDivisionId='" + fourthDivisionId + "'")
                .add("isNeesRigorousSelectionMaster=" + isNeesRigorousSelectionMaster)
                .add("masterBrand='" + masterBrand + "'")
                .add("divisionMatchLevel=" + divisionMatchLevel)
                .add("pushMode='" + pushMode + "'")
                .add("bussinessId=" + businessLineId)
                .add("masterOrderId=" + masterOrderId)
                .add("serveIds='" + serveIds + "'")
                .add("recruitIds='" + recruitIds + "'")
                .add("extraData=" + extraData)
                .add("tmpPreMasterSet=" + tmpPreMasterSet)
                .add("tmpMasterSet=" + tmpMasterSet)
                .add("tmpExclusiveMasterSet=" + tmpExclusiveMasterSet)
                .add("tmpMeasurePriMasterSet=" + tmpMeasurePriMasterSet)
                .add("tmpMeasureMasterSet=" + tmpMeasureMasterSet)
                .add("tmpGroupMasterSet=" + tmpGroupMasterSet)
                .toString();
    }


    public MasterMatchCondition(OrderDetailData orderDetailData){
        this.businessLineId = orderDetailData.getBusinessLineId();
        this.serveIds = orderDetailData.getLv3ServeIds();
        this.thirdDivisionId = orderDetailData.getThirdDivisionId();
        this.orderPushEliminateMasterIds = orderDetailData.parseOrderPushEliminateMasterIds();
        this.orderVersion = orderDetailData.getOrderVersion();
        this.isSettleStatusNormal=true;
        this.matchSceneCode = orderDetailData.getPushExtraData().getMatchSceneCode();


        this.setPushMode(pushMode);
        Long orderThirdDivisionId=orderDetailData.getThirdDivisionId();
        Long orderFourthDivisionId=orderDetailData.getFourthDivisionId();
        this.setBusinessLineId(orderDetailData.getBusinessLineId());
        this.setMasterOrderId(orderDetailData.getMasterOrderId());
        this.setCategoryId(orderDetailData.getOrderCategoryId());
        this.setServeType(orderDetailData.getOrderServeType());
        this.setTechnologysInDemand(orderDetailData.getOrderTechniques());
        this.setThirdDivisionId(orderThirdDivisionId);
        this.setFourthDivisionId(orderFourthDivisionId);
        this.setMasterBrand(orderDetailData.getOrderFrom());
        this.setTechnologysInDemandSet(orderDetailData.getOrderTechniqueSet());
        this.setServeIds(orderDetailData.getLv3ServeIds());
        this.setNeesRigorousSelectionMaster(StringUtils.equals(orderDetailData.getOrderLabel(), "rigorous_selection") ? true : false);
        this.setDivisionMatchLevel(judgeDivisionMatchLevel(orderThirdDivisionId, orderFourthDivisionId));
        this.setSecondDivisionId(orderDetailData.getSecondDivisionId());
        this.setOrderLngLat(orderDetailData.getOrderLngLat());
        this.setPickupAddressLngLat(orderDetailData.getPickupAddressLngLat());


        /**
         * 2021-11-17 市场部需求特殊处理:定制家具/门,按照三级地址推送 BIGDATA-2408
         */
//		 排除专属
        regionModeForMarketingDepartment(this,orderDetailData.getOrderCategoryId(),this.getPushMode());


    }


    /**
     * 判断区域匹配模式
     * @return
     */
    public Integer judgeDivisionMatchLevel(Long orderThirdDivisionId,Long orderFourthDivisionId) {
        Integer divisionMatchLevel = 3;
        if (orderFourthDivisionId != null && orderFourthDivisionId > 0) {
            divisionMatchLevel=4;
        }else if (orderThirdDivisionId != null && orderThirdDivisionId > 0) {
            divisionMatchLevel = 3;
        }else {
            divisionMatchLevel = 0;
        }
        return divisionMatchLevel;
    }


    /**
     * 2021-11-17 市场部需求特殊处理:定制家具/门,按照三级地址推送
     * 6:门 7:全屋定制
     */
    private void regionModeForMarketingDepartment(MasterMatchCondition masterMatchCondition,Long categoryId,String pushMode) {
        if (("6".equals(categoryId)||"7".equals(categoryId)) && !PushMode.PRE_EXCLUSIVE.code.equals(pushMode)) {
            masterMatchCondition.setDivisionMatchLevel(3);
        }
    }


    /**
     * 推单排除师傅id-普通推单
     */
    private List<String> orderPushEliminateMasterIds;





}
