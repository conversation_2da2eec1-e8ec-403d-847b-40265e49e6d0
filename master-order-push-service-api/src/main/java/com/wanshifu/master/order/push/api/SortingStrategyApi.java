package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.SortingStrategy;
import com.wanshifu.master.order.push.domain.po.SortingStrategySnapshot;
import com.wanshifu.master.order.push.domain.resp.sortingStrategy.DetailRqt;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.SnapshotRqt;
import com.wanshifu.master.order.push.domain.rqt.sortingStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "sortingStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface SortingStrategyApi {


    /**
     * 启用/禁用精排策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);
    /**
     * 精排策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<SortingStrategy> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 删除精排策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);


    /**
     * 创建精排策略
     * @param rqt
     * @return
     */
    @PostMapping("/create/v2")
    int createV2(@RequestBody @Valid CreateV2Rqt rqt);

    /**
     * 修改精排策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify/v2")
    int updateV2(@RequestBody @Valid UpdateV2Rqt rqt);

    /**
     * 精排策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail/v2")
    SortingStrategy detailV2(@RequestBody @Valid DetailRqt rqt);

    @PostMapping(value="/selectBySnapshotIdList")
    List<SortingStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt);


}
