package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 推送策略规则实体类
 * <AUTHOR>
 */
@Data
public class PushStrategyRule {
    private StrategyIdList strategyList;
    private String pushRuleType;
    private List<OrderPushRule> pushRule;
    private Integer pushRuleId;
    private OpenConditionExpression openConditionQlExpression;

    @Data
    public class OpenConditionExpression{
        private String qlExpression;
        private String qlExpressionParams;
    }

    /**
     * 开启条件
     */
    private OpenCondition openCondition;

    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        private String condition;

        /**
         * 开启条件项
         */
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem {

        /**
         * 条件项名称 canPushNumber:可推送人数
         */
        private String itemName;

        /**
         * 符号 <
         */
        private String term;

        /**
         * 规则项值
         */
        private Integer itemValue;
    }

}