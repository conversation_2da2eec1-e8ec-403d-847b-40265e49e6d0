package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderPushRule {

    /**
     * 下单模式
     */
    private Integer appointType;

    /**
     * 最佳报价数
     */
    private Integer bestOfferNum;

    /**
     * 推送间隔
     */
    private Integer delayMinutesBetweenRounds;


    /**
     * 首轮推送人数
     */
    private Integer firstPushMasterNumPerRound;

    /**
     * 首轮推送师傅人群
     */
    private String firstPushMasterFlag;

    /**
     * 首轮推送师傅人群占比
     */
    private Integer firstPushMasterPercent;


    /**
     * 非首轮推送人数
     */
    private Integer delayPushMasterNumPerRound;

    /**
     * 非首轮推送师傅人群
     */
    private String delayPushMasterFlag;

    /**
     * 非首轮推送师傅人群占比
     */
    private Integer delayPushMasterPercent;

}

