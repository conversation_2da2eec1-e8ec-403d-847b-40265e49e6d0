package com.wanshifu.master.order.push.util;

import java.util.*;

/**
 * <AUTHOR>
 */
public class LocalCollectionsUtil {

    public static <T> List<List<T>> groupByBatch(Collection<T> collections,int batchSize){
        final List<List<T>> result = new ArrayList<>();
        if (collections==null) {
            return result;
        }
        int i=0;
        ArrayList<T> batch = new ArrayList<>();
        for (T row : collections) {
            batch.add(row);
            i++;
            if (i==batchSize) {
                result.add(batch);
                batch = new ArrayList<>();
                i=0;
            }
        }
        if (i>0) {
            result.add(batch);
        }
        return result;
    }

    public static  List<List<Long>> groupByBatchLong(Collection<String> collections,int batchSize){
        final List<List<Long>> result = new ArrayList<>();
        if (collections==null) {
            return result;
        }
        int i=0;
        ArrayList<Long> batch = new ArrayList<>();
        for (String row : collections) {
            batch.add(Long.valueOf(row));
            i++;
            if (i==batchSize) {
                result.add(batch);
                batch = new ArrayList<>();
                i=0;
            }
        }
        if (i>0) {
            result.add(batch);
        }
        return result;
    }


    public static  List<List<String>> groupByBatchString(Collection<String> collections,int batchSize){
        final List<List<String>> result = new ArrayList<>();
        if (collections==null) {
            return result;
        }
        int i=0;
        ArrayList<String> batch = new ArrayList<>();
        for (String row : collections) {
            batch.add(row);
            i++;
            if (i==batchSize) {
                result.add(batch);
                batch = new ArrayList<>();
                i=0;
            }
        }
        if (i>0) {
            result.add(batch);
        }
        return result;
    }

    public static <T> void addListValueToMap(Map<String, List<T>> dataCollection, String key, T value){
        if (dataCollection==null) {
            return;
        }
        List<T> valueList = dataCollection.get(key);
        if (valueList==null) {
            valueList=new ArrayList<T>();
            valueList.add(value);
            dataCollection.put(key,valueList);
        }else {
            valueList.add(value);
        }
    }


    public static <T,K> void addListValueToGenericMap(Map<K, List<T>> dataCollection, K key, T value){
        if (dataCollection==null) {
            return;
        }
        List<T> valueList = dataCollection.get(key);
        if (valueList==null) {
            valueList=new ArrayList<T>();
            valueList.add(value);
            dataCollection.put(key,valueList);
        }else {
            valueList.add(value);
        }
    }


    public static <T,K> void addSetValueToGenericMap(Map<K, Set<T>> dataCollection, K key, T value){
        if (dataCollection==null) {
            return;
        }
        Set<T> valueList = dataCollection.get(key);
        if (valueList==null) {
            valueList=new HashSet<>();
            valueList.add(value);
            dataCollection.put(key,valueList);
        }else {
            valueList.add(value);
        }
    }

    public static <T,K> void addHashSetValueToGenericMap(Map<K, HashSet<T>> dataCollection, K key, T value){
        if (dataCollection==null) {
            return;
        }
        HashSet<T> valueList = dataCollection.get(key);
        if (valueList==null) {
            valueList=new HashSet<>();
            valueList.add(value);
            dataCollection.put(key,valueList);
        }else {
            valueList.add(value);
        }
    }


    public static <K, A, B> void putMapToMap(
            Map<K, Map<A, B>> dataCollection,
            K key,
            Map<A, B> otherMap) {
        if (dataCollection == null) {
            return;
        }
        Map<A, B> valueList = dataCollection.get(key);
        if (valueList == null) {
            valueList = new HashMap<A, B>(50);
            valueList.putAll(otherMap);
            dataCollection.put(key, valueList);
        } else {
            valueList.putAll(otherMap);
        }
    }

    public static <K, A, B> void putHashMapToHashMap(
            HashMap<K, HashMap<A, B>> dataCollection,
            K key,
            HashMap<A, B> otherMap) {
        if (dataCollection == null) {
            return;
        }
        HashMap<A, B> valueList = dataCollection.get(key);
        if (valueList == null) {
            valueList = new HashMap<A, B>(50);
            valueList.putAll(otherMap);
            dataCollection.put(key, valueList);
        } else {
            valueList.putAll(otherMap);
        }
    }


    /**
     * String to array
     * @return
     */
    public static Set<String> stringArrayToSet(String[] stringArray) {
        Set<String> result=new HashSet<>();
        for (String string : stringArray) {
            result.add(string);
        }
        return result;
    }

}
