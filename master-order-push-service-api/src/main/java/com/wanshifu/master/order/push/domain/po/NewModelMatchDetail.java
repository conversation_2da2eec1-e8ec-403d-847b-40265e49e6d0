package com.wanshifu.master.order.push.domain.po;


import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Data
@ToString
@Table(name = "new_model_match_detail")
public class NewModelMatchDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "order_no")
    private String orderNo;

    @Column(name = "order_create_time")
    private Date orderCreateTime;

    @Column(name = "master_id")
    private Long masterId;

    @Column(name = "is_main_master")
    private Integer isMainMaster;

    @Column(name = "is_filter_out")
    private Integer isFilterOut = 0;

    @Column(name = "filter_remark")
    private String filterRemark = "";


    @Column(name = "is_distribute")
    private Integer isDistribute = 0;

    @Column(name = "distribute_remark")
    private String distributeRemark = "";

    @Column(name = "distribute_result")
    private Integer distributeResult = 0;
    
    @Column(name = "distribute_fail_remark")
    private String distributeFailRemark = "";
    
    @Column(name = "abandon_time")
    private Date abandonTime;

    @Column(name = "order_version")
    private String orderVersion;

    @Column(name = "operate_time")
    private Date operateTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

}
