package com.wanshifu.master.order.push.domain.api.response;

import lombok.Data;

@Data
public class AllTimeOrderMstStatResp {

    /**
     * 师傅id
     */
    private String masterId;

    /**
     * 当周师傅实时指派量（全量）
     */
    private Integer mstGrabTotalCntCurrWeek;

    /**
     * 当周师傅实时派单量（扣除取消） - 当周抢单量
     */
    private Integer mstGrabNotCancelCntCurrWeek;

    /**
     * 当周师傅订单付款量 -当周派单量
     */
    private Integer mstOrderPayCntCurrWeek;

    /**
     * 当周师傅订单未付款取消量
     */
    private Integer mstOrderCancelCntCurrWeek;

    /**
     * 当天师傅实时指派量（全量）
     */
    private Integer mstGrabTotalCntCurrDay;

    /**
     * 当天师傅实时派单量（扣除取消） - 当天抢单量
     */
    private Integer mstGrabNotCancelCntCurrDay;

    /**
     * 当天师傅订单付款量 - 当天派单量
     */
    private Integer mstOrderPayCntCurrDay;

    /**
     * 当天师傅订单未付款取消量
     */
    private Integer mstOrderCancelCntCurrDay;

}
