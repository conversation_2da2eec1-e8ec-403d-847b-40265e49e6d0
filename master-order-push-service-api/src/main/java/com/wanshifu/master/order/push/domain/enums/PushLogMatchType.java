package com.wanshifu.master.order.push.domain.enums;

/**
 * <AUTHOR>
 * @date 2025/4/29 9:35
 */
public enum PushLogMatchType {


    /**
     * 总包直接指派
     */
    ENTERPRISE_APPOINT("enterprise_appoint", "总包直接指派"),

    /**
     * 平台协议派单
     */
    USER_AGREEMENT("user_agreement","平台协议派单"),


    /**
     * 合作经营派单
     */
    COOPERATION_BUSINESS("cooperation_business","合作经营派单"),


    /**
     * 样板城市派单
     */
    NEW_MODEL_CITY("new_model_city","样板城市派单"),


    FULL_TIME_MASTER("full_time_master", "全时师傅派单");



    public final String code;

    public final String desc;

    PushLogMatchType(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc(){
        return desc;
    }

    @Override
    public String toString(){
        return code;
    }
}
