package com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy;


import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;


/**
 * 描述 :  创建路由Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class CreateRqt {


    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;


    /**
     * 策略名称
     */
    @NotBlank
    private String strategyName;

    /**
     * 策略描述
     */
    @NotBlank
    private String strategyDesc;


    /**
     * 订单标识,all: 不限，site: 企业，enterprise: 总包，family: 家庭
     */
    @NotBlank
    @ValueIn("all,site,enterprise,family")
    private String orderFrom;

    /**
     * 订单标识
     */
    @NotBlank
    @ValueIn("none,new_master_support,new_model,enterprise_appoint")
    private String orderTag;

    /**
     * 类目id集合
     */
    @NotBlank
    private String categoryIds;

    /**
     * 城市id集合
     */
    @NotBlank
    private String cityIds;

    /**
     * 匹配路由id
     */
    @NotNull
    private Integer matchRoutingId;

    private Long createAccountId;

}