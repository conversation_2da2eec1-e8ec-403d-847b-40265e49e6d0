package com.wanshifu.master.order.push.domain.rqt.orderselectstrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.dto.RuleItem;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 创建筛选策略请求实体类
 * @date 2025/3/4 11:31
 */
@Data
public class CreateOrderSelectStrategyRqt {

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Long businessLineId;

    @NotNull
    private String orderFrom;

    /**
     * 策略名称
     */
    @NotBlank
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 师傅资源
     */
    @NotBlank
    @ValueIn("auto_receive,agreement,agent,new_master,new_model,cooperation_business_master,full_time_master,after_verify_new_master")
    private String masterResources;

    /**
     * 评分规则列表
     */
    @Valid
    private SelectStrategy selectStrategy;




    private Long createAccountId;

    @Data
    public static class SelectStrategy{

        @NotNull
        @Valid
        private AppointGroup appointGroup;


        /**
         * 规则列表
         */
        @NotEmpty(message = "至少包含一条规则")
        @Valid
        private List<RuleItem> ruleList;

    }

    @Data
    public static class AppointGroup{

        @NotNull
        private Integer isAppointGroup;


        @Valid
        private List<FilterRuleItem> itemList;

    }


    //    @Data
//    public static class SelectRule{
//
//        /**
//         * 或且关系
//         */
//        @NotBlank
//        @ValueIn("and,or")
//        private String condition;
//
//        @NotEmpty
//        @Valid
//        private List<RuleItem> itemList;
//
//    }
//
//
    @Data
    public static class FilterRuleItem{

        /**
         * master_group: 师傅人群，master_quota: 师傅指标
         */
        @NotBlank
        private String itemType;

        /**
         * 规则项名称
         */
        @NotBlank
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
         */
        @NotBlank
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        /**
         * 规则项值
         */
        @NotBlank
        private String itemValue;
    }

}
