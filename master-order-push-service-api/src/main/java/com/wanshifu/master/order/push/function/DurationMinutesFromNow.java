package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 计算某个日期距今分钟
 * <AUTHOR>
 */
@Slf4j
public class DurationMinutesFromNow extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception{
        if (list.length != 2){
            throw new Exception("操作数异常");
        }
        Long result;
        Long defaultValue = 99999L;
        try {
            String dateString= (String)list[0];
            defaultValue = Long.valueOf((Integer)list[1]);
            if (StringUtils.isBlank(dateString)) {
                result=defaultValue.longValue();
            }else{
                result= DateFormatterUtil.durationMinutesFromNow(dateString);
                if (result==null) {
                    result=defaultValue.longValue();
                }
            }
        } catch (Exception e) {
            log.error("DurationDaysFromNow error",e);
            result=defaultValue.longValue();
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        final DurationMinutesFromNow durationMinutesFromNow = new DurationMinutesFromNow();
        Object[] asd=new Object[]{"2023-12-20 11:20:00",3333};
        System.out.println(durationMinutesFromNow.executeInner(asd));
    }
}
