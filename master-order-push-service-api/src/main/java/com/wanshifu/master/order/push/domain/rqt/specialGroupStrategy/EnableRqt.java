package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 启用/禁用特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Data
@ApiModel(description = "启用/禁用特殊人群策略请求类")
public class EnableRqt {

    @ApiModelProperty(value = "策略id", required = true)
    @NotNull(message = "策略id不能为空")
    private Long strategyId;

    @ApiModelProperty(value = "策略状态 1:启用 0:禁用", required = true)
    @NotNull(message = "策略状态不能为空")
    private Integer strategyStatus;

    @ApiModelProperty(value = "更新人id")
    private Long updateAccountId;
}
