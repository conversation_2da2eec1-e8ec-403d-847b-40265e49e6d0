package com.wanshifu.master.order.push.domain.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 17:34
 */
@Data
public class OrderScoringItemListResp {

    private String itemCode;
    private String itemName;
    private String itemDesc;
    private String valueType;

    public List<EnumValue> valueList;

    @Data
    public static class EnumValue {
        private String name;
        private String value;


    }

    public OrderScoringItemListResp(String itemCode, String itemName, String itemDesc, String valueType) {
        this.itemCode = itemCode;
        this.itemName = itemName;
        this.itemDesc = itemDesc;
        this.valueType = valueType;
    }

    public OrderScoringItemListResp(){

    }

}
