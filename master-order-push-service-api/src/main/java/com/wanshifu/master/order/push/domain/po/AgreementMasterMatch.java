package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 协议师傅匹配记录
 */
@Data
@Table(name = "order_agreement_master_match")
public class AgreementMasterMatch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 协议师傅ID
     */
    @Column(name = "agreement_master_id")
    private String agreementMasterId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 商家id
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 订单下单时间
     */
    @Column(name = "order_create_time")
    private Date orderCreateTime;

    /**
     * 指派模式
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 招募id
     */
    @Column(name = "recruit_id")
    private Long recruitId;

    /**
     * 是否匹配成功
     */
    @Column(name = "is_match_succ")
    private Integer isMatchSucc;


    /**
     * 匹配地址级别，3：按三级地址匹配，4：按四级地址匹配
     */
    @Column(name = "match_division_level")
    private Integer matchDivisionLevel;

    /**
     * 匹配失败原因
     */
    @Column(name = "match_fail_reason")
    private String matchFailReason;

    /**
     * 是否计价成功
     */
    @Column(name = "is_calculate_price_succ")
    private Integer isCalculatePriceSucc;

    /**
     * 计价失败原因
     */
    @Column(name = "calculate_price_fail_reason")
    private String calculatePriceFailReason;

    /**
     * 是否被过滤
     */
    @Column(name = "is_filter")
    private Integer isFilter;

    /**
     * 过滤原因
     */
    @Column(name = "filter_reason")
    private String filterReason;

    /**
     * 是否调度
     */
    @Column(name = "is_distribute")
    private Integer isDistribute;

    /**
     * 调度规则
     */
    @Column(name = "distribute_rule")
    private String distributeRule;

    /**
     * 是否自动接单成功
     */
    @Column(name = "is_auto_receive_succ")
    private Integer isAutoReceiveSucc;

    /**
     * 自动接单失败原因
     */
    @Column(name = "auto_receive_fail_reason")
    private String autoReceiveFailReason;

    /**
     * 版本号
     */
    @Column(name = "order_version")
    private String orderVersion;

    /**
     * 师傅合作价
     */
    @Column(name = "cooperation_price")
    private BigDecimal cooperationPrice;

    /**
     * 招募标签名
     */
    @Column(name = "recruit_tag_name")
    private String recruitTagName;

    /**
     * 招募场景id
     */
    @Column(name = "recruit_scene_id")
    private Integer recruitSceneId;

    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    @Column(name = "master_source_type")
    private String masterSourceType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
