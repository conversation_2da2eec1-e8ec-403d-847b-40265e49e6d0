package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 描述 :  代理商直接指派消息.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-10-24 10:01
 */
@Data
public class AgentDirectAppointRqt {

    private Long orderId;

    BigDecimal orderLongitude ;
    BigDecimal orderLatitude ;

    private String serveIds;

    private Long thirdDivisionId;

    /**
     * 代理商推单师傅
     */
    private String agentMasterList;

}