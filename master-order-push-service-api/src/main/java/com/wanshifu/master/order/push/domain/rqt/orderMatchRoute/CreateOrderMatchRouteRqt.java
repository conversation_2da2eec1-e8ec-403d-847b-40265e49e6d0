package com.wanshifu.master.order.push.domain.rqt.orderMatchRoute;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateOrderMatchRouteRqt {

    @NotNull
    @NotBlank
    private String routeName;

    private String routeDesc;

    @NotNull
    private String orderPushFlag;

    @NotNull
    private String orderPriorityMatchRule;

    private Long createAccountId;


    /**
     * 订单备用路由规则
     */
    @Valid
    private  List<MatchRule> orderStandbyMatchRule;


    @Data
    public static class MatchRule{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<MatchRuleItem> itemList;


        private String matchRule;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class MatchRuleItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("order_receive,order_appoint")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

    }
}
