package com.wanshifu.master.order.push.domain.common;


import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 推单评分器列表
 * <AUTHOR>
 */
@Data
public class PushScorerObject {

    private OpenCondition openCondition;

    private List<PushScorer> scorerList;

    public PushScorerObject(){
    }

    public PushScorerObject(OpenCondition openCondition,List<PushScorer> pushScorerList){
        this.openCondition = openCondition;
        scorerList = pushScorerList;
    }

    public Set<String> getOrderFeatureSet(){
        Set<String> orderFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(scorerList)){
            scorerList.forEach(pushScorer -> {
                if(CollectionUtils.isNotEmpty(pushScorer.getOrderFeatureList())){
                    orderFeatureSet.addAll(pushScorer.getOrderFeatureList());
                }
            });
        }

        if(openCondition != null && CollectionUtils.isNotEmpty(openCondition.getOrderFeatureList())){
            orderFeatureSet.addAll(openCondition.getOrderFeatureList());
        }
        return orderFeatureSet;
    }

    public Set<String> getMasterFeatureSet(){
        Set<String> masterFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(scorerList)){
            scorerList.forEach(pushScorer -> {
                if(CollectionUtils.isNotEmpty(pushScorer.getMasterFeatureList())){
                    masterFeatureSet.addAll(pushScorer.getMasterFeatureList());
                }
            });
        }
        return masterFeatureSet;
    }

}
