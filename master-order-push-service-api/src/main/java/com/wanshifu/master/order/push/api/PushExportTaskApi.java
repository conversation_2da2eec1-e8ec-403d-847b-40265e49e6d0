package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushExportTask;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.CreatePushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.ListPushExportTaskRqt;
import com.wanshifu.master.order.push.domain.rqt.pushexporttask.UpdatePushExportTaskRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/5/27 9:53
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "pushExportTask", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface PushExportTaskApi {

    /**
     * 创建异步导出任务
     * @param createPushExportTaskRqt
     * @return
     */
    @PostMapping("create")
    Long createPushExportTask(@Valid @RequestBody CreatePushExportTaskRqt createPushExportTaskRqt);

    /**
     * 更新异步导出任务状态
     * @param updatePushExportTaskRqt
     */
    @PostMapping("update")
    void updatePushExportTask(@Valid @RequestBody UpdatePushExportTaskRqt updatePushExportTaskRqt);

    /**
     * 查询异步导出任务列表
     * @param listPushExportTaskRqt
     * @return
     */
    @PostMapping("list")
    SimplePageInfo<PushExportTask> listPushExportTask(@Valid @RequestBody ListPushExportTaskRqt listPushExportTaskRqt);

}
