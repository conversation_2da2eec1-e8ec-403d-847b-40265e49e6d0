package com.wanshifu.master.order.push.domain.rqt.pushexporttask;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 11:41
 */
@Data
public class UpdatePushExportTaskRqt {

    /**
     * 主键id
     */
    @NotNull
    private Long id;

    /**
     * 导出任务状态，process:处理中，fail:失败，success:成功
     */
    private String status;

    /**
     * 导出文件地址
     */
    private String url;

    /**
     * 失败原因
     */
    private String failReason;
}
