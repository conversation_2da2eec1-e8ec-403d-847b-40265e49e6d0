package com.wanshifu.master.order.push.domain.rqt.pushexporttask;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27 9:59
 */
@Data
public class CreatePushExportTaskRqt {

    /**
     * 导出数据类型名称
     */
    @NotEmpty
    private String exportDataTypeName;

    /**
     * 导出数据名称
     */
    @NotEmpty
    private String exportDataName;

    /**
     * 文件名
     */
    @NotEmpty
    private String fileName;

    /**
     * 导出任务状态，process:处理中，fail:失败，success:成功
     */
    @NotEmpty
    private String status;
}
