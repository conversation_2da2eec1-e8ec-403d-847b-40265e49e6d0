package com.wanshifu.master.order.push.operator;

import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 描述 :  自定义包含操作符 Notcontain 仅支持二元操作,第一个为参数
 * [1,2]     notContain  1         ->  false
 * ['A','B'] notContain  'C'   ->  true
 * .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-22 20:39
 */
@Slf4j
public class OperatorNotContain extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception {
        Object obj = list[0];
        Object obj1 = list[1];
        if (obj == null) {
            throw new Exception("操作数为空,异常");
        } else if (!obj.getClass().isArray() && !(obj instanceof List)) {
            throw new Exception("操作数类型异常");
        } else if (list.length != 2) {
            throw new Exception("操作数数量异常");
        }
        try {
            return !((List<Object>) obj).contains(obj1);
        } catch (Exception e) {
            log.error("OperatorNotContain is error");
            return false;
        }

    }
}