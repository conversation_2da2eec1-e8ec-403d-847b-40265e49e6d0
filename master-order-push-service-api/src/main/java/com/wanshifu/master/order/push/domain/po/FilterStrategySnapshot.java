package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 召回策略快照表
 *  <AUTHOR>
 */
@Data
@ToString
@Table(name = "filter_strategy_snapshot")
public class FilterStrategySnapshot {

    /**
     * 快照id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "snapshot_id")
    private Long snapshotId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 召回规则配置(JSON格式)
     */
    @Column(name = "filter_rule")
    private String filterRule;

    /**
     * 召回规则表达式
     */
    @Column(name = "rule_expression")
    private String ruleExpression;

    /**
     * 策略状态，1：启用，0：禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 业务线
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 订单标识,normal:普通订单 ikea:宜家订单
     */
    @Column(name = "order_flag")
    private String orderFlag;
}