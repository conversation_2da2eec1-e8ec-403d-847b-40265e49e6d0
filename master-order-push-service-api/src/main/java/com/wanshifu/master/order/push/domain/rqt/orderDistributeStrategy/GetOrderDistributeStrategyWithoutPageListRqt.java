package com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 查询订单分片策略列表请求实体类
 * <AUTHOR>
 */
@Data
public class GetOrderDistributeStrategyWithoutPageListRqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 类目id
     */
    private String categoryIds;

    /**
     * 城市id
     */
    private Long cityId;


    private List<Long> categoryIdList;

    /**
     * 策略状态，1：已启用，0：已禁用
     */
    private Integer strategyStatus;


    public static final class GetOrderDistributeStrategyWithoutPageListRqtBuilder {
        private Integer businessLineId;
        private String strategyName;
        private Date createStartTime;
        private Date createEndTime;
        private String categoryIds;
        private Long cityId;
        private List<Long> categoryIdList;
        private Integer strategyStatus;

        private GetOrderDistributeStrategyWithoutPageListRqtBuilder() {
        }

        public static GetOrderDistributeStrategyWithoutPageListRqtBuilder aGetOrderDistributeStrategyWithoutPageListRqt() {
            return new GetOrderDistributeStrategyWithoutPageListRqtBuilder();
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withBusinessLineId(Integer businessLineId) {
            this.businessLineId = businessLineId;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withStrategyName(String strategyName) {
            this.strategyName = strategyName;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withCreateStartTime(Date createStartTime) {
            this.createStartTime = createStartTime;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withCreateEndTime(Date createEndTime) {
            this.createEndTime = createEndTime;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withCategoryIds(String categoryIds) {
            this.categoryIds = categoryIds;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withCityId(Long cityId) {
            this.cityId = cityId;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withCategoryIdList(List<Long> categoryIdList) {
            this.categoryIdList = categoryIdList;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqtBuilder withStrategyStatus(Integer strategyStatus) {
            this.strategyStatus = strategyStatus;
            return this;
        }

        public GetOrderDistributeStrategyWithoutPageListRqt build() {
            GetOrderDistributeStrategyWithoutPageListRqt getOrderDistributeStrategyWithoutPageListRqt = new GetOrderDistributeStrategyWithoutPageListRqt();
            getOrderDistributeStrategyWithoutPageListRqt.setBusinessLineId(businessLineId);
            getOrderDistributeStrategyWithoutPageListRqt.setStrategyName(strategyName);
            getOrderDistributeStrategyWithoutPageListRqt.setCreateStartTime(createStartTime);
            getOrderDistributeStrategyWithoutPageListRqt.setCreateEndTime(createEndTime);
            getOrderDistributeStrategyWithoutPageListRqt.setCategoryIds(categoryIds);
            getOrderDistributeStrategyWithoutPageListRqt.setCityId(cityId);
            getOrderDistributeStrategyWithoutPageListRqt.setCategoryIdList(categoryIdList);
            getOrderDistributeStrategyWithoutPageListRqt.setStrategyStatus(strategyStatus);
            return getOrderDistributeStrategyWithoutPageListRqt;
        }
    }
}
