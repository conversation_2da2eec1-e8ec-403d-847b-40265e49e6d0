package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.StrategyCombination;
import com.wanshifu.master.order.push.domain.resp.strategyCombination.DetailResp;
import com.wanshifu.master.order.push.domain.rqt.strategyCombination.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "strategyCombination", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface StrategyCombinationApi {


    /**
     * 创建组合策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改组合策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    int update(@RequestBody @Valid UpdateRqt rqt);
    /**
     * 组合策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/combinationDetail")
    DetailResp combinationDetail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 启用/禁用策略组合
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 组合策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<StrategyCombination> list(@RequestBody @Valid ListRqt rqt);


    /**
     * 删除组合策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);


    @PostMapping("/detail")
    StrategyCombination detail(@RequestBody @Valid DetailRqt rqt);
}
