package com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply;

import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.domain.vo.StrategyTuple;
import com.wanshifu.master.order.push.util.QlExpressStatic;
import com.wanshifu.master.order.push.util.QlExpressUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LongTailStrategyMatcher {

    private Long longTailStrategyGroupId;

    private List<LongTailStrategyGroupCondition> longTailStrategyGroupCondition;

    @Data
    public static class LongTailStrategyGroupCondition{

        StrategyTuple open;

        LongTailStrategyTuple trigger;

    }

    public List<LongTailStrategyTuple> match(DefaultContext<String, Object> context){
        final ArrayList<LongTailStrategyTuple> result = new ArrayList<>();
        for (LongTailStrategyGroupCondition strategyGroupCondition : longTailStrategyGroupCondition) {
            if (strategyGroupCondition.getOpen().executeQLExpression(context)) {
                if (strategyGroupCondition.getTrigger()==null) {
                    continue;
                }
                if (StringUtils.isEmpty(strategyGroupCondition.getTrigger().getPreCondition().getTriggerExpression())
                        || strategyGroupCondition.getTrigger().getPreCondition().executeQLExpression(context)) {
                    result.add(strategyGroupCondition.getTrigger());
                }
            }
        }
        return result;
    }

}
