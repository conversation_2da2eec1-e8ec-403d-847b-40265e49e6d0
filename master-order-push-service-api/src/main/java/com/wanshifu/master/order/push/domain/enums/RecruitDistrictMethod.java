package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 招募区域方式
 * <AUTHOR>
 */
public enum RecruitDistrictMethod {

    /**
     * 街道包
     */
    DISTRICT_PACKAGE("district_package","街道包"),

    /**
     * 电子围栏
     */
    GEO_FENCE("geo_fence","电子围栏");

    private final String code;
    private final String name;

    RecruitDistrictMethod(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, RecruitDistrictMethod> valueMapping = new HashMap<>((int) (RecruitDistrictMethod.values().length / 0.75));

    static {
        for (RecruitDistrictMethod instance : RecruitDistrictMethod.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static RecruitDistrictMethod asValue(String value) {
        return valueMapping.get(value);
    }


}
