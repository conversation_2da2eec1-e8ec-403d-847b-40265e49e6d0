package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 描述 :  总包自动邀约推单.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-13 10:55
 */
@Data
public class InviteOrderPushRqt {
    /**
     * 订单全局id
     */
    @NotNull
    private Long globalOrderTraceId;

    @NotNull
    private Integer businessLineId;

    /**
     * 邀约推单师傅id
     */
    @NotNull
    @NotEmpty
    private Set<Long> masterIds;
}