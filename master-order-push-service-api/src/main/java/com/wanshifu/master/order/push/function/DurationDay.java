package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;

public class DurationDay extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception{
        if (list.length != 3){
            throw new Exception("操作数异常");
        }
        Long result;
        try {
            String startDateString= (String)list[0];
            String endsDateString= (String)list[1];
            String dateString= (String)list[2];

            if (StringUtils.isBlank(dateString) || (StringUtils.isBlank(startDateString) && StringUtils.isBlank(endsDateString))) {
                return 0;
            }else{

                Date dateTime = DateUtils.parseDate(dateString);
                if(startDateString != null && endsDateString != null){
                    Date startDateTime = DateUtils.parseDate(startDateString);
                    Date endDateTime = DateUtils.parseDate(endsDateString);

                    return dateTime.compareTo(startDateTime) >= 0 && dateTime.compareTo((endDateTime)) <= 0 ? 1 : 0;

                }else if(StringUtils.isNotBlank(startDateString )){
                    Date startDateTime = DateUtils.parseDate(startDateString);
                    return dateTime.compareTo(startDateTime) >= 0? 1 : 0;
                }else if(StringUtils.isNotBlank(endsDateString)){
                    Date endDateTime = DateUtils.parseDate(endsDateString);
                    return dateTime.compareTo(endDateTime) <= 0 ? 1 : 0;
                }


            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }
}
