package com.wanshifu.master.order.push.domain.enums;


import com.google.common.collect.Lists;

import java.util.*;

/**
 * 服务类型(1:送货到楼下,2:送货到家,3:送货到家并安装,4:安装,5:维修,6:返货,7:保养,8:清洗,9:测量,10:测量并安装,11:拆机,12:拆机并返货,13:拆除,14:打蜡,15:工程单,16:调试,17:测量并安装(新),211:收尾)
 */
@Deprecated
public enum ServeType {
    /**
     * 送货到楼下
     */
    GOODS_TO_DOWNSTAIRS(1, "送货到楼下"),
    /**
     * 送货到家
     */
    GOODS_TO_HOME(2, "送货到家"),
    /**
     * 送货到家并安装
     */
    GOODS_TO_HOME_INSTALL(3, "送货到家并安装"),
    /**
     * 安装
     */
    INSTALL(4, "安装"),
    /**
     * 维修
     */
    MAINTAIN(5, "维修"),
    /**
     * 返货
     */
    RETURN_GOODS(6, "返货"),
    /**
     * 保养
     */
    UPKEEP(7, "保养"),
    /**
     * 清洗
     */
    CLEAN_OUT(8, "清洗"),

    /**
     * 测量
     */
    MEASURE(9, "测量"),

    /**
     * 测量并安装（暂时业务等同于测量）处理方式一样
     */
    MEASURE_AND_INSTALL(10, "测量并安装"),

    /**
     * 拆机
     */
    DISMANTLE(11, "拆机"),

    /**
     * 拆机并返货
     */
    DISMANTLE_RETURN_GOODS(12, "拆机并返货"),

    /**
     * 拆除
     */
    DEMOUNT(13, "拆除"),

    /**
     * 打蜡
     */
    WAXING(14, "打蜡"),

    /**
     * 工程单
     */
    EPC_GENRE_ORDER(15, "工程单"),

    /**
     * 调试
     */
    DEBUG(16, "调试"),

    /**
     * 测量并安装(新)
     */
    NEW_MEASURE_AND_INSTALL(17, "测量并安装(新)"),

    /**
     * 测量-初测
     */
    PRELIMINARY_MEASURE(19, "测量-初测"),

    /**
     * 测量-复测
     */
    REPETITION_MEASURE(20, "测量-复测"),

    /**
     * 套装安装
     */
    SUIT_INSTALL(18, "套装安装"),

    /**
     * 收尾
     */
    EPILOGUE(211, "收尾"),

    /**
     * 维修检测
     */
    MAINTAIN_DETECTION(41138175, "维修检测"),


    /**
     * 净化
     */
    PURIFY(205, "净化"),
    ;

    public final Integer type;

    public final String name;

    private static final Map<Integer, ServeType> mappingMap = new HashMap<>((int) (ServeType.values().length / 0.75));

    static {
        for (ServeType instance : values()) {
            mappingMap.put(instance.type, instance);
        }

    }

    public static ServeType fromType(Integer code) {
        return mappingMap.get(code);
    }

    //配送类型订单(有物流)
    public static Boolean isDeliveryType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] deliveryTypeArr = {GOODS_TO_DOWNSTAIRS.type, GOODS_TO_HOME.type, GOODS_TO_HOME_INSTALL.type, RETURN_GOODS.type, DISMANTLE_RETURN_GOODS.type};

        return Arrays.asList(deliveryTypeArr).contains(code);
    }

    //安装类型订单
    public static Boolean isInstallType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] installTypeArr = {GOODS_TO_HOME_INSTALL.type, INSTALL.type, NEW_MEASURE_AND_INSTALL.type, SUIT_INSTALL.type};

        return Arrays.asList(installTypeArr).contains(code);
    }

    //安装+ 套装安装
    public static final List<Integer> onlyInstallTypeList = Arrays.asList(INSTALL.type, SUIT_INSTALL.type);

    /**
     * 是否安装订单
     *
     * @param serveType
     * @return
     */
    public static boolean isInstallOrder(Integer serveType) {
        return onlyInstallTypeList.contains(serveType);
    }

    //送货到楼下+ 送货到家+ "送货到家并安装 + 返货 + 工程单 + 调试
    public static final List<Integer> logisticsOrder = Arrays.asList(GOODS_TO_DOWNSTAIRS.type, GOODS_TO_HOME.type, GOODS_TO_HOME_INSTALL.type,
            RETURN_GOODS.type, EPC_GENRE_ORDER.type, DEBUG.type);

    public static boolean isLogisticsOrder(Integer code) {
        return logisticsOrder.contains(code);
    }

    public static boolean isReturnLogisticsOrder(Integer code) {
        return RETURN_GOODS.type.equals(code);
    }



    //返货类型订单
    public static Boolean isReturnType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] returnTypeArr = {RETURN_GOODS.type, DISMANTLE_RETURN_GOODS.type};

        return Arrays.asList(returnTypeArr).contains(code);
    }

    //测量类型订单
    public static Boolean isMeasureType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] measureTypeArr = {MEASURE.type, MEASURE_AND_INSTALL.type, NEW_MEASURE_AND_INSTALL.type, PRELIMINARY_MEASURE.type, REPETITION_MEASURE.type};

        return Arrays.asList(measureTypeArr).contains(code);
    }

    ServeType(Integer code, String name) {
        this.type = code;
        this.name = name;
    }

    //接单易4.8卫浴测量后续订单（送货到家，安装，送货到家并安装）
    public static Boolean isMeasureFollowType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] installTypeArr = {GOODS_TO_HOME_INSTALL.type, GOODS_TO_HOME.type, INSTALL.type};

        return Arrays.asList(installTypeArr).contains(code);
    }

    //配送类型非返货订单(有物流)
    public static Boolean isDeliveryTypeNonReturn(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] deliveryTypeArr = {GOODS_TO_DOWNSTAIRS.type, GOODS_TO_HOME.type, GOODS_TO_HOME_INSTALL.type};

        return Arrays.asList(deliveryTypeArr).contains(code);
    }

    //拆机类型订单
    public static Boolean isDismantleType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        Integer[] dismantleArr = {DISMANTLE.type, DISMANTLE_RETURN_GOODS.type};

        return Arrays.asList(dismantleArr).contains(code);
    }

    //拆除类型订单
    public static Boolean isDemountType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        List<Integer> demountTypeList = Lists.newArrayList(DEMOUNT.type);
        return demountTypeList.contains(code);
    }

    //打蜡类型订单
    public static Boolean isWaxingType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        List<Integer> waxingTypeList = Lists.newArrayList(WAXING.type);
        return waxingTypeList.contains(code);
    }

    //调试类型订单
    public static Boolean isDebugType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        List<Integer> debugTypeList = Lists.newArrayList(DEBUG.type);
        return debugTypeList.contains(code);
    }

    //工程单类型订单
    public static Boolean isEpcGenreType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        List<Integer> epcGenreTypeList = Lists.newArrayList(EPC_GENRE_ORDER.type);
        return epcGenreTypeList.contains(code);
    }

    //收尾类型订单
    public static Boolean isEpilogue(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        List<Integer> epilogueTypeList = Lists.newArrayList(EPILOGUE.type);
        return epilogueTypeList.contains(code);
    }

    @Override
    public String toString() {
        return this.name;
    }
}