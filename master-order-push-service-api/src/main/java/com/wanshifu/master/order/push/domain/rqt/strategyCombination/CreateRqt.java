package com.wanshifu.master.order.push.domain.rqt.strategyCombination;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.strategyCombination.AlternateStrategyVo;
import com.wanshifu.master.order.push.domain.vo.strategyCombination.PriorityStrategyVo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  创建策略组合Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 组合名称
     */
    @NotEmpty
    private String combinationName;

    /**
     * 组合描述
     */
    private String combinationDesc;

    /**
     * 适用类目，多个用逗号拼接 all:所有类目
     */
    @NotEmpty
    private String categoryIds;

    /**
     * 城市，多个适用逗号拼接  all:全国所有城市
     */
    @NotEmpty
    private String cityIds;

    /**
     * 优先推荐路由
     */
    @NotNull
    @Valid
    private PriorityStrategyVo priorityStrategy;

    /**
     * 备用推荐路由
     */
    @Valid
    private AlternateStrategyVo alternateStrategy;

    private Long createAccountId;


    @ValueIn("normal,ikea")
    private String orderFlag;

}