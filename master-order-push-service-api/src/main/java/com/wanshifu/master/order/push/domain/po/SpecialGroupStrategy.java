package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "special_group_strategy")
public class SpecialGroupStrategy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Long strategyId;

    @Column(name = "strategy_name")
    private String strategyName;

    @Column(name = "strategy_desc")
    private String strategyDesc;

    // 二维数组形式，为了还原前端结构
    @Column(name = "serve_id_array")
    private String serveIdArray;

    // 1、2、3级服务混存，逗号分隔，任一命中即可
    @Column(name = "serve_ids")
    private String serveIds;

    @Column(name = "region_level")
    private String regionLevel;

    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 调度人群，多个人群之间关系为“或”
     */
    @Column(name = "push_groups")
    private String pushGroups;


    /**
     * 下单模式，2:报价招标，4:一口价，5:预付款
     */
    @Column(name = "serve_models")
    private String serveModels;

    /**
     * 延迟推送下一优先级的时间（分钟）
     */
    @Column(name = "delay_minutes")
    private Integer delayMinutes;

    /**
     * 过滤人群，多个人群之间关系为“或”
     */
    @Column(name = "filter_groups")
    private String  filterGroups;


    @Column(name = "strategy_status")
    private Integer strategyStatus;


    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;


    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


}
