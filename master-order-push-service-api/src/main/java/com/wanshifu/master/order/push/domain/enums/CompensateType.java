package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 补偿调度类型
 */
@AllArgsConstructor
@Getter
public enum CompensateType {

    /**
     * 无人接单
     */
    NONE_RECEIVE("none_receive","无人接单"),

    /**
     * 未指派
     */
    NONE_APPOINT("none_appoint","未指派");


    private final String code;

    private final String desc;


    private static final Map<String, CompensateType> valueMapping = new HashMap<>((int) (CompensateType.values().length / 0.75));


    static {
        for (CompensateType instance : CompensateType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static CompensateType asCode(String code) {
        return valueMapping.get(code);
    }
}
