package com.wanshifu.master.order.push.domain.rqt.repushPolicy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改重推机制Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 机制id
     */
    @NotNull
    private Long policyId;

    private Long updateAccountId;
}