package com.wanshifu.master.order.push.domain.common;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.service.QLExpressHandler;
import com.wanshifu.master.order.push.util.SpringContextUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * 推送过滤器
 * <AUTHOR>
 */
@Data
@Slf4j
public class PushFilter {

    private ExpressRunner expressRunner;

    private String conditionExpression;

    private String filterExpression;

    private String filterName;

    private List<String> orderFeatureList;

    private List<String> masterFeatureList;

    public PushFilter(){

    }


    public PushFilter(ExpressRunner expressRunner,String conditionExpression,String filterExpression,String filterName,String orderFeature,String masterFeature){
        this.expressRunner = expressRunner;
        this.conditionExpression = conditionExpression;
        this.filterExpression = filterExpression;
        this.filterName = filterName;
        if(StringUtils.isNotBlank(orderFeature)){
            this.orderFeatureList = Arrays.asList(orderFeature.split(","));
        }
        if(StringUtils.isNotBlank(masterFeature)){
            this.masterFeatureList = Arrays.asList(masterFeature.split(","));
        }
    }

    public Boolean execute(DefaultContext<String,Object> filterContext) {

        expressRunner = SpringContextUtil.getBean(QLExpressHandler.class).getExpressRunner();

        try{
            return Boolean.valueOf(expressRunner.execute(filterExpression,filterContext,null,true,false).toString());
        }catch(Exception e){
            log.error("execute pushFilter error",e);
            return false;
        }
    }

}
