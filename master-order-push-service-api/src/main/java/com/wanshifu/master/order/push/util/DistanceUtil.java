package com.wanshifu.master.order.push.util;
;

public class DistanceUtil {


    /**
     * km 地球半径 平均值，千米
     */
    static double EARTH_RADIUS = 6371.0;


    /**
     * km 地球半径 平均值，米
     */
    static double EARTH_RADIUS_METRE = 6371000;



    /**
     * latitude:维度 longitude:经度  （第一个点经纬度,第二个点经纬度） 用haversine公式计算球面两点间的距离。
     *
     * @return 距离:千米
     */
    public static Long distance(double lat1, double lon1, double lat2,
                                 double lon2) {
        // 经纬度转换成弧度
        lat1 = convertDegreesToRadians(lat1);
        lon1 = convertDegreesToRadians(lon1);
        lat2 = convertDegreesToRadians(lat2);
        lon2 = convertDegreesToRadians(lon2);

        // 差值
        double vLon = Math.abs(lon1 - lon2);
        double vLat = Math.abs(lat1 - lat2);

        // h is the great circle distance in radians, great
        // circle就是一个球体上的切面，它的圆心即是球心的一个周长最大的圆。
        double h = haverSin(vLat) + Math.cos(lat1) * Math.cos(lat2) * haverSin(vLon);

        Double distance = 2 * EARTH_RADIUS * Math.asin(Math.sqrt(h));

        return distance.longValue();
    }


    /**
     * 将角度换算为弧度。
     *
     * @param degrees
     * @return
     */
    private static double convertDegreesToRadians(double degrees) {
        return degrees * Math.PI / 180;
    }

    // private static double ConvertRadiansToDegrees(double radian) {
    // return radian * 180.0 / Math.PI;
    // }

    private static double haverSin(double theta) {
        double v = Math.sin(theta / 2);
        return v * v;
    }



    /**
     * latitude:维度 longitude:经度  （第一个点经纬度,第二个点经纬度） 用haversine公式计算球面两点间的距离。
     *
     * @return 距离: 米
     */
    public static Long distanceOfMetre(double lat1, double lon1, double lat2,
                                double lon2) {
        // 经纬度转换成弧度
        lat1 = convertDegreesToRadians(lat1);
        lon1 = convertDegreesToRadians(lon1);
        lat2 = convertDegreesToRadians(lat2);
        lon2 = convertDegreesToRadians(lon2);

        // 差值
        double vLon = Math.abs(lon1 - lon2);
        double vLat = Math.abs(lat1 - lat2);

        // h is the great circle distance in radians, great
        // circle就是一个球体上的切面，它的圆心即是球心的一个周长最大的圆。
        double h = haverSin(vLat) + Math.cos(lat1) * Math.cos(lat2) * haverSin(vLon);

        Double distance = 2 * EARTH_RADIUS_METRE * Math.asin(Math.sqrt(h));

        return distance.longValue();
    }


}
