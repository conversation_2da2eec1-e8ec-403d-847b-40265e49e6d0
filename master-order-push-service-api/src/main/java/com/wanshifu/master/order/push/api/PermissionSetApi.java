package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.Permission;
import com.wanshifu.master.order.push.domain.po.PermissionSet;
import com.wanshifu.master.order.push.domain.resp.permisssionSet.GetPermissionSetDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.permissionSet.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "permissionSet", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
@Deprecated
public interface PermissionSetApi {


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "add")
    Integer add(@Valid @RequestBody AddPermissionSetRqt rqt);


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    Integer update(@Valid @RequestBody UpdatePermissionSetRqt rqt);



    @PostMapping(value = "list")
    SimplePageInfo<PermissionSet> list(@Valid @RequestBody GetPermissionSetListRqt rqt);


    @PostMapping(value = "detail")
    PermissionSet detail(@Valid @RequestBody GetPermissionSetDetailRqt rqt);


    @PostMapping(value = "menuList")
    List<Permission> menuList(@Valid @RequestBody GetMenuListRqt rqt);


    @PostMapping(value = "permissionList")
    List<Permission> permissionList(@Valid @RequestBody GetPermissionListRqt rqt);


    @PostMapping(value = "allPermissionList")
    List<Permission> allPermissionList();


    @PostMapping(value = "delete")
    Integer delete(@Valid @RequestBody DeletePermissionSetRqt rqt);


    }
