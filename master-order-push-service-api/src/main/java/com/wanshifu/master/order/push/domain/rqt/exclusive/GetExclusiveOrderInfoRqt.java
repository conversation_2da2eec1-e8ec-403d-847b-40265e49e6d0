package com.wanshifu.master.order.push.domain.rqt.exclusive;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 获取专属活动标签信息x
 *
 * @since 2023-08-10 16:00:00
 */
@Data
public class GetExclusiveOrderInfoRqt {

    /**
     * 推送模式
     */
    @NotNull
    @NotBlank
    private String pushMode;

    /**
     * 推送模式-细分类型(专属,1:pre_exclusive_single(专属单个师傅),2:pre_exclusive_single_transfer_grab_offer_price(专属转抢单))
     */
    private String pushModeType;


    private Long globalOrderTraceId;

    /**
     * 师傅ID
     */
    @NotNull
    private Long masterId;

    /**
     * 专属活动ID
     */
    private Long recruitId;

    /**
     * 是否有价格
     */
    private Boolean hasPrice = false;
}
