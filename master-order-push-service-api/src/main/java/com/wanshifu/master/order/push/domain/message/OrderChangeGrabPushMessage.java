package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单改派重新推单
 * <AUTHOR>
 */
@Data
public class OrderChangeGrabPushMessage {

    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 原师傅id
     */
    private Long oldMasterId;


    /** 要推单的新师傅ids*/
    private List<Long> newMasterIds;


    /** 改派时间 */
    private Date changeTime;

}
