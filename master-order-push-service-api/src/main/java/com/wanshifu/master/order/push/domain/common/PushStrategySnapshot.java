package com.wanshifu.master.order.push.domain.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 描述 :  推送策略快照.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-03-20 10:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushStrategySnapshot {
    /**
     * isToRepush=true时,为重推机制快照id
     * isToRepush=false时，为组合策略快照id
     */
    private Long strategySnapshotId;

    /**
     * 是否为重推   true:是, false:否
     */
    private boolean isRepush;

    private Long baseSelectStrategySnapshotId;
    private Long filterStrategySnapshotId;
    private Long sortingStrategySnapshotId;

    /**
     * 是否为优先推送策略   true:是, false:否
     */
    private boolean isPriorityStrategyPush;

}