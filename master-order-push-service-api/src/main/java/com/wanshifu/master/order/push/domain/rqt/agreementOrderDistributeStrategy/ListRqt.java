package com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 服务类目id
     */
    private String categoryIds;

    /**
     * 城市id
     */
    private String cityIds;


    /**
     * 策略状态，1：启用，0:禁用
     */
    private Integer strategyStatus;


    /**
     * 创建开始时间
     */
    private Date createStartTime;


    /**
     * 创建结束时间
     */
    private Date createEndTime;

}
