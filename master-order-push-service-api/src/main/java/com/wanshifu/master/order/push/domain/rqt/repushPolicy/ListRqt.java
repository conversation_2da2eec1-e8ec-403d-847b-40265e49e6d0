
package com.wanshifu.master.order.push.domain.rqt.repushPolicy;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 描述 :  重推机制列表列表Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id: 1:成品业务线，2：家庭业务线(原APP)，999：家庭业务线（新APP）
     */
    private Long businessLineId;

    /**
     * 策略名称
     */
    private String policyName;

    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;


    /**
     * 城市id
     */
    private String cityIds;

    /**
     * 类目id
     */
    private String categoryIds;

    private String orderFlag;

    /**
     * 组合状态 1:启用 0:禁用
     */
    @ValueIn("0,1")
    private Integer strategyStatus;
}