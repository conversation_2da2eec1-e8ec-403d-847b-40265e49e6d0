package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.LongTailStrategy;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "longTailStrategy",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface LongTailStrategyApi {

    /**
     * 长尾策略列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<LongTailStrategy> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 创建长尾策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);


    /**
     * 修改长尾策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    int update(@RequestBody @Valid UpdateRqt rqt);


    /**
     * 启用/禁用策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/updateStatus")
    int updateStatus(@RequestBody @Valid EnableRqt rqt);


    /**
     * 删除策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);

    /**
     * 策略组详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    LongTailStrategy detail(@RequestBody @Valid DetailRqt rqt);


}
