package com.wanshifu.master.order.push.domain.constant;


/**
 * 表名-字段属性配置
 * 
 * <AUTHOR>
 *
 */
public class FieldConstant {


	public static final String EXCLUSIVE = "exclusive";
	public static final String DIRECT_APPOINTMENT = "direct_appointment";
	public static final String PREFERRED = "preferred";
	public static final String CONTRACT = "contract";
	public static final String BRAND = "brand";
	public static final String AGENT = "agent";

	public static final String RANK_DETAIL = "rank_detail";


	public static final String MAX = "max";
	public static final String MIN = "min";

	public static final String ORDER = "order";



	public static final String NONE = "none";
	public static final String NEW = "new";

	public static final String SMC_MASTER_FEATURE = "t_master";



	public static final String CREATE_TIME="create_time";

	public static final String IS_DELETE = "is_delete";

	public static final String REFUND_TIME = "refund_time";


	public static final String OPEN_CONDITION_RULE_EXPRESSION = "openConditionRuleExpression";
	public static final String OPEN_CONDITION_RULE_PARAMS = "openConditionRuleParams";
	public static final String RULE_NAME = "ruleName";
	public static final String SCORE_RULE_EXPRESSION = "scoreRuleExpression";
	public static final String SCORE_RULE_PARAMS = "scoreRuleParams";
	public static final String SCORE_RULE_LIST = "scoreRuleList";

	public static final String ORDER_SCORING_STRATEGY_ID = "orderScoringStrategyId";
	public static final String ORDER_SELECT_STRATEGY_ID = "orderSelectStrategyId";


	public static final String DISPATCH_TYPE = "dispatchType";



	public static final String DIRECT_APPOINT_SCORING_STRATEGY_ID = "directAppointScoringStrategyId";
	public static final String DIRECT_APPOINT_SELECT_STRATEGY_ID = "directAppointSelectStrategyId";

	public static final String ORDER_DISTRIBUTE_RULE = "distributeRule";
    public static final String COOPERATION_BUSINESS_SERVE_DIVISION_AT_LAST = "cooperationBusinessServeDivisionAtLast";


	public static final String SELECT_RULE_EXPRESSION = "selectRuleExpression";
	public static final String APPOINT_GROUP_EXPRESSION = "appointGroupExpression";
	public static final String SELECT_RULE_PARAMS = "selectRuleParams";
	public static final String APPOINT_GROUP_PARAMS = "appointGroupParams";


	public static final String FILTER_RULE_EXPRESSION = "filterRuleExpression";
	public static final String FILTER_RULE_PARAMS = "filterRuleParams";
	public static final String FILTER_RULE_NAME = "ruleName";








	/**
	 * 订单标识
	 */
	public static final String HAS_PRICE = "has_price";
	public static final String OCS_ORDER_OFFER_NUM = "max_offer_number";
	//招募ID
	public static final String RECRUIT_ID = "recruit_id";
	/**
	 * 定时单
	 */
	public static final String TIMER_FLAG = "timer_flag";


	public static final String PACKAGE_ID = "package_id";


	/**
	 * 数据一致性校验表表名与主键名
	 */
	public static final String MQ_MSG_IDEMPOTENT = "mq_msg_idempotent";
	public static final String MSG_KEY = "msg_key";
	public static final String ORDER_VERSION = "order_version";
	public static final String DEFAULT_UNSPECIFIED = "unspecified";
	
	/**
	\ * OTS特征表字段与表名配置
	 */
	public static final String SMC_KEY_MAP = "smc_key_map";
	// 订单特征表
	public static final String SMC_GLOBAL_ORDER_FEATURE = "t_global_order";
	public static final String ORDER_DETAIL = "order_detail";
	public static final String SMC_ORDER_MSG_RECORD = "smc_order_msg_record";
	
	// 用户特征表
	public static final String SMC_USER_FEATURE = "t_user";
	// 师傅特征表
	public static final String T_MASTER = "t_master";

	// 师傅日期维度表
	public static final String SMC_MASTER_DATE_FEATURE = "t_master_daily";

	//推送模式
	public static final String PUSH_MODE = "push_mode";
	public static final String OPERATE_FLAG = "operate_flag";
	public static final String PACKAGE_MODE = "package_mode";
	public static final String PACKAGE = "package";
	public static final String CONFIRM_ORDER_PACKAGE = "confirm_order_package";


	//推送等级
	public static final String MASTER_PUSH_ADDRESS = "master_push_address";
	public static final String TEAM_MASTER_ORDER_PUSH = "team_master_order_push";

	public static final String CANCEL_APPOINT = "cancel_appoint";
	public static final String CORE_DIVISION_ID = "core_division_id";

	//指派类型
	public static final String APPOINT_TYPE = "appoint_type";
	public static final String OFFER_NUM = "offer_num";
	public static final String IS_APPOINT = "is_appoint";

	// 期望完成时间
	public static final String EXPECT_COMPLETE_TIME = "expect_complete_time";
	public static final String EXPECT_DOORIN_START_TIME = "expect_doorin_start_time";
	public static final String EXPECT_TIME = "expect_time";


    public static final String EXPECT_DOOR_IN_START_DATE = "expect_door_in_start_date";
	public static final String EXPECT_DOOR_IN_END_DATE = "expect_door_in_end_date";

	public static final String SERVE_STATUS = "serve_status";
	public static final String SERVING = "serving";

	public static final String RESERVE_START_TIME="reserve_start_time";
	public static final String RESERVE_END_TIME="reserve_end_time";

	
	// 期望完成时间
	public static final String TODAY_TIME = "today_";
	

	// 预约订单量
	public static final String BOOKING_ORDER_CNT = "booking_order_cnt";


	public static final String SERVE_ID = "serve_id";


	/**
	 * 通用字段名
	 */
	//	实时处理订单用户
	public static final String REALTIME_PROCESSING_ORDER_USER = "realtime-processing-order-user";
	//	实时处理师傅
	public static final String REALTIME_PROCESSING_MASTER = "realtime-processing-master";
	public static final String FEATURE_NAME = "feature_name";
	public static final String FEATURE_DEPENDENCY = "feature_dependency";
	public static final String DEPENDENCY_CHECK = "dependency_check";
	public static final String FEATURE_SYSTEMATICS = "feature_systematics";
	public static final String CALCULATOR_EXPRESSION = "calculator_expression";
	public static final String DEFAULT_VALUE = "default_value";
	public static final String FEATURE_DIMENSION = "feature_dimension";
	public static final String FIELD_TYPE = "field_type";
	public static final String FEATURE_TYPE = "feature_type";
	public static final String ORDER_ID = "order_id";
	public static final String APPOINT_METHOD = "appoint_method";
	public static final String MASTER_ID = "master_id";
	public static final String AGENT_ID = "agent_id";

	public static final String MASTER_ID_LIST = "master_id_list";
	public static final String ACCOUNT_ID = "account_id";
	public static final String ACCOUNT_TYPE = "account_type";
	public static final String ORDERTEC_SNAPSHOP = "ordertec_snapshop";
	public static final String DIVISION_MATCH_LEVEL = "division_match_level";
	public static final String COMMON_FEATURE = "common_feature";
	public static final String IS_DELIVERY_ORDER = "is_delivery_order";
	public static final String LEVEL_1_SP_TYPE = "level_1_sp_type";
	public static final String MASTER_IDS = "master_ids";
	public static final String MASTER_LIST = "master_list";
	public static final String USER_ID = "user_id";
	public static final String DATE_TIME = "date_time";
	public static final String PUSH_TIME = "push_time";
	public static final String DT = "dt";
	public static final String MASTER = "master";
	public static final String CITY = "city";
	public static final String CITY_ID = "city_id";
	public static final String ENTERPRISE = "enterprise";
	public static final String USER = "user";
	public static final String NORMAL = "normal";
	public static final String STOP = "stop";
	public static final String IS_STOP_PUSH = "is_stop_push";
	public static final String IKEA = "ikea";
	public static final String IS_IKEA_ORDER = "is_ikea_order";
	public static final String BUSINESS_LINE_ID = "business_line_id";
	public static final String BUSINESS_LINE_TYPE = "business_line_type";
	public static final String IS_RIGOROUS_SELECTIONORDER = "is_rigorous_selection_order";
	public static final String ORDER_LNG_LAT = "order_lng_lat";

	public static final String ORDER_LOGISTICS_LNG_LAT = "order_logistics_lng_lat";
	public static final String BATCH_ID = "batch_id";
	public static final String WAIT_APPOINT_ORDER_NUM = "waitAppointOrderNum";

	// 区域ID
	public static final String DISTRICT_ID = "district_id";
	public static final String REGION_ID = "region_id";
    public static final String SECOND_DIVISION_ID = "second_division_id";
    public static final String THIRD_DIVISION_ID = "third_division_id";
	public static final String FOURTH_DIVISION_ID = "fourth_division_id";
	// 订单来源
	public static final String ORDER_FROM = "order_from";

	public static final String TIME_LINESS_TAG = "time_liness_tag";
    //增值服务
    public static final String VALUE_ADDED_SERVICE = "value_added_service";
	public static final String CUSTOMER_PHONE = "customer_phone";
	// 订单来源
	public static final String ORDER_LABEL = "order_label";
	// 类目ID
	public static final String CATEGORY_ID = "category_id";
	// 订单服务类型ID
	public static final String SERVE_TYPE_ID = "serve_type_id";
	
	public static final String LV1_SERVE_ID = "lv1_serve_id";
	public static final String LV2_SERVE_IDS = "lv2_serve_ids";
	public static final String LV3_SERVE_IDS = "lv3_serve_ids";


	// 订单服务类型ID
	//订单服务类型
	public static final String ORDER_SERVE_TYPE = "order_serve_type";
	public static final String SINGLE_ORDER_TECHNOLOGYS_IN_DEMAND = "single_order_technologys_in_demand";
	
	// 师傅订单ID
	public static final String MASTER_ORDER_ID = "master_order_id";
	// 用户订单ID
	public static final String USER_ORDER_ID = "user_order_id";
	public static final String ENTERPRISE_ORDER_ID = "enterprise_order_id";
	public static final String GLOBAL_ORDER_ID = "global_order_id";


	// 配置主键
	public static final String ENTERPRISE_ID = "enterprise_id";
	public static final String ORDER_GOODS_ID = "order_goods_id";
	
	public static final String GOODS_PARENT_ID = "goods_parent_id";
	public static final String GOODS_CHILD_ID = "goods_child_id";
	public static final String ALL_GOODS_PARENT_ID = "all_goods_parent_id";
	public static final String GOODS_NUM = "goods_num";
	public static final String GOODS_LEVEL_2_IDS="goods_level_2_ids";


	public static final String DEMOLISH_TYPE = "demolish_type";
	public static final String NOUSE = "nouse";
	public static final String USEALL = "useall";
	public static final String USEPART = "usepart";
	public static final String IS_SEND_BACK_OLD = "is_send_back_old";
	public static final String EMERGENCY_ORDER_FLAG = "emergency_order_flag";
	public static final String ORDER_SERVE_VERSION = "order_serve_version";
	public static final String ORDER_ATTACHED_GOODS = "order_attached_goods";
	public static final String ORDER_ANCILLARY_GOODS = "order_ancillary_goods";
	public static final String DEFINITE_SERVE_FEE = "definite_serve_fee";
	

	
	/**
	 * 常量配置
	 */

	public static final String IS_BLACK_LIST_STATUS_NORMAL = "is_black_list_status_normal";

	public static final String IS_ACCOUNT_NORMAL = "is_account_normal";

	public static final String FREEZING_TIME = "freezing_time";

	public static final String IS_SETTLE_STATUS_NORMAL = "is_settle_status_normal";

	public static final String LAST_ACTIVE_TIME = "last_active_time";

	public static final String REST_STATE = "rest_state";

	public static final String IS_RULE_EXAM_STATUS_NORMAL = "is_rule_exam_status_normal";

	public static final String SERVE_DIVISION_IDS = "serve_division_ids";

	public static final String SERVE_FOURTH_DIVISION_IDS = "serve_fourth_division_ids";

	public static final String IS_PUSH_RESTRICT_NORMAL = "is_push_restrict_normal";

	public static final String  MASTER_FORBIDDEN_BUSINESS_IDS= "master_forbidden_business_ids";


	public static final String push = "is_nearby_push";

	public static final String IS_NEARBY_PUSH = "is_nearby_push";

	public static final String PUSH_FLAG = "push_flag";


	//是否按照距离推单
	public static final String IS_ACCORDING_DISTANCE_PUSH="is_according_distance_push";

	public static final String ATTRIBUTE_VALUE_MIN = "attribute_value_min";
	public static final String ATTRIBUTE_VALUE_MAX = "attribute_value_max";

	public static final String PACKAGE_CONFIG_IDS = "package_config_ids";


	public static final String MEASURE_MASTER_PRIVILEGE = "measure_master_privilege";

	public static final String PACKAGE_ORDER = "package_order";
	public static final String PACKAGE_CONFIG_ID = "package_config_id";
	public static final String PACKAGE_ATTRIBUTE_ID = "package_attribute_id";
	public static final String GOODS_ATTRIBUTE = "goods_attribute";

	public static final String PLATFORM_AUTO_MODE="platform_auto_mode";



	public static final String ATTRIBUTE_KEY = "attribute_key";


	public static final String ORDER_PACKAGE_ATTRIBUTE_RANGE = "order_package_attribute_range";

	public static final String ORDER_BIND_TECHNIQUES = "order_bind_techniques";

	public static final String HAND_OFF_TAG = "handoff_tag";

	public static final String MATCH_SCENE_CODE = "match_scene_code";


	public static final String PUSH_MODE_TYPE = "push_mode_type";

	public static final String PUSH_MODE_TYPE_BIND_MASTER = "push_mode_type_bind_master";

	public static final String PUSH_SCENARIO_TYPE = "push_scenario_type";

    public static final String DATE = "date";

    public static final String YESTERDAY = "yesterday_date";



}
