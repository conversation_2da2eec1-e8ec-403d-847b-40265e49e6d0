package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

/**
 * 开启条件表达式Dto
 * <AUTHOR> cheng<PERSON>@wshifu.com
 * @date : 2025-03-11 16:30
 */
@Data
public class OpenConditionExpressionDto {

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;




    public OpenConditionExpressionDto(){

    }


    public OpenConditionExpressionDto(String openConditionRuleExpression, String openConditionRuleParams){
        this.openConditionRuleExpression = openConditionRuleExpression;

        this.openConditionRuleParams = openConditionRuleParams;


    }

}