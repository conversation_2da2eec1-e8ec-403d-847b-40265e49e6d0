package com.wanshifu.master.order.push.domain.common;
/**
 * 推送上下文:待推送列表
 * <AUTHOR>
 *
 */


import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.enums.MasterType;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.po.PushProgress;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PushMasterList {

	private List<PushMaster> masterList = new ArrayList<>();

	public List<PushMaster> getMasterList(){
		return masterList;
	}

	/**
	 * 强推师傅列表
	 */
	private ArrayList<PushMaster> directPushMasterList;

	/**
	 * 强推师傅列表
	 */
	private ArrayList<PushMaster> priorityPushMasterList;

	/**
	 * 新师傅列表
	 */
	private List<PushMaster> newMasterList;

	/**
	 * 老师傅列表
	 */
	private List<PushMaster> oldMasterList;

	/**
	 * 订单推送进度
	 */
	private PushProgress pushProgress;

	public PushProgress getPushProgress() {
		return pushProgress;
	}


	public PushMasterList(){

	}

	public PushMasterList(PushProgress pushProgress, List<PushMaster> newPushMasterList, List<PushMaster> oldPushMasterList) {
		this.pushProgress = pushProgress;
		this.newMasterList = newPushMasterList;
		this.oldMasterList = oldPushMasterList;
	}

	public PushMasterList(PushProgress pushProgress, List<PushMaster> masterList) {
		this.pushProgress = pushProgress;
		this.masterList = masterList;
	}
		/**
         * 构造待推送列表(未处理)
         *
         * @param listToBeHandle
         */
	public PushMasterList(List<PushMaster> listToBeHandle) {
		directPushMasterList = new ArrayList<>();
		priorityPushMasterList = new ArrayList<>();
		newMasterList = new ArrayList<>();
		oldMasterList = new ArrayList<>();

		// 遍历待处理列表
		listToBeHandle.forEach(pushMaster -> {

			if (pushMaster.isForcePushMaster()) {
				// 强推师傅
				directPushMasterList.add(pushMaster);
			}if (pushMaster.isPriorityPush()) {
				// 强推师傅
				priorityPushMasterList.add(pushMaster);
			} else if (pushMaster.isNewMaster()) {
				// 新手师傅
				newMasterList.add(pushMaster);
				masterList.add(pushMaster);
			} else if (!pushMaster.isNewMaster()) {
				// 老师傅
				oldMasterList.add(pushMaster);
				masterList.add(pushMaster);
			}
		});

		// 构建当前推送进度
		buildPushProgressFirstRound();
	}



	/**
	 * 获取列表当前推送进度(首轮)
	 */
	private void buildPushProgressFirstRound() {
		/**
		 * 首轮
		 */
		this.pushProgress = new PushProgress();
				// 新师傅列表offset
		pushProgress.setNewMasterOffset(0);
				// 老师傅列表offset
		pushProgress.setOldMasterOffset(0);
				// 师傅列表总长度
		pushProgress.setListLength(directPushMasterList.size()+priorityPushMasterList.size() + newMasterList.size() + oldMasterList.size());
				// 已推师傅数
		pushProgress.setPushedMasterNum(0);
				// 已过滤师傅数
		pushProgress.setFilteredMasterNum(0);

		pushProgress.setPushedRound(0);
	}




	public List<PushMaster> getFirstPushMasterList(PushConfig pushConfig){
		List<PushMaster> pushMasterList = new ArrayList<>();
		if(!MasterType.ALL.code.equals(pushConfig.getFirstPushMasterType())){
			pushMasterList.addAll(this.getNewMastersByNum(pushConfig.getPushNewMasterNumPerRound()));
			pushMasterList.addAll(this.getOldMastersByNum(pushConfig.getPushOldMasterNumPerRound()));
			if(MasterType.ALL.code.equals(pushConfig.getDelayPushMasterType())){
				//重新排序
				List<PushMaster> totalPushMasterList = new ArrayList<>();

                totalPushMasterList.addAll(pushMasterList);


                List<PushMaster> tempPushMasterList = new ArrayList<>();
                if(newMasterList.size() > pushConfig.getPushNewMasterNumPerRound()){
                    tempPushMasterList.addAll(newMasterList.subList(pushConfig.getPushNewMasterNumPerRound(),newMasterList.size()));
                }
                if(oldMasterList.size() > pushConfig.getPushOldMasterNumPerRound()){
                    tempPushMasterList.addAll(oldMasterList.subList(pushConfig.getPushOldMasterNumPerRound(),oldMasterList.size()));
                }
                if(CollectionUtils.isNotEmpty(tempPushMasterList)){
                    Collections.sort(tempPushMasterList);
                    totalPushMasterList.addAll(tempPushMasterList);
                }


                masterList = totalPushMasterList;
				pushProgress.setMasterOffset(pushMasterList.size());
			}
		}else{
			pushMasterList.addAll(masterList.subList(0, masterList.size() >= pushConfig.getFirstPushOldMasterNum() ? pushConfig.getFirstPushOldMasterNum() : masterList.size()));
			pushProgress.setMasterOffset(pushMasterList.size());
			if(!MasterType.ALL.code.equals(pushConfig.getDelayPushMasterType())){
				Long newMasterOffset = pushMasterList.stream().filter(pushMaster -> pushMaster.isNewMaster()).count();
				Long oldMasterOffset = pushMasterList.stream().filter(pushMaster -> !pushMaster.isNewMaster()).count();

				pushProgress.setNewMasterOffset(newMasterOffset.intValue());
				pushProgress.setOldMasterOffset(oldMasterOffset.intValue());

			}
		}

		pushMasterList.addAll(this.getDirectPushMasterList());
		pushMasterList.addAll(this.getPriorityPushMasterList());

		return pushMasterList;
	}



	/**
	 * 获取指定数量的新师傅
	 */
	public List<PushMaster> getNewMastersByNum(Integer newMasterNum) {
		// 获取指定数量新师傅
		int endOffset = pushProgress.getNewMasterOffset() + newMasterNum;
		if (newMasterList.size()<endOffset) {
			//师傅不足单轮推送截取,则返回原列表
			pushProgress.setNewMasterOffset(newMasterList.size());
			return newMasterList;
		}
		List<PushMaster> subNewList = newMasterList.subList(pushProgress.getNewMasterOffset(), endOffset);
		// 测试offset 首尾开闭区间→经测试 sublist为左闭右开
		// 更新offset
		pushProgress.setNewMasterOffset(endOffset);
		return subNewList;
	}

	/**
	 * 获取指定数量的老师傅
	 */
	public List<PushMaster> getOldMastersByNum(Integer oldMasterNum) {
		// 获取指定数量老师傅
		int endOffset = pushProgress.getOldMasterOffset() + oldMasterNum;
		if (oldMasterList.size()<endOffset) {
			//师傅不足单轮推送截取,则返回原列表
			pushProgress.setOldMasterOffset(oldMasterList.size());
			return oldMasterList;
		}
		List<PushMaster> subOldList = oldMasterList.subList(pushProgress.getOldMasterOffset(), endOffset);
		// 更新offset
		pushProgress.setOldMasterOffset(endOffset);
		return subOldList;
	}

	/**
	 * 获取强推师傅列表
	 * 
	 * @return
	 */
	public ArrayList<PushMaster> getDirectPushMasterList() {
		return directPushMasterList;
	}


	/**
	 * 获取强推师傅列表
	 *
	 * @return
	 */
	public ArrayList<PushMaster> getPriorityPushMasterList() {
		return priorityPushMasterList;
	}

	/**
	 * 获取新师傅列表
	 * 
	 * @return
	 */
	public List<PushMaster> getNewMasterList() {
		return newMasterList;
	}

	/**
	 * 获取老师傅列表
	 * 
	 * @return
	 */
	public List<PushMaster> getOldMasterList() {
		return oldMasterList;
	}

	@Override
	public String toString() {
		return "ToBePushedMasterList [directPushMasterList=" + directPushMasterList + ", newMasterList=" + newMasterList
				+ ", oldMasterList=" + oldMasterList + ", pushProgress=" + pushProgress + "]";
	}


}
