package com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-05 14:56
 */
@Data
public class CreateRqt {

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    @Min(0L)
    private Integer businessLineId;
    /**
     * 策略名称
     */
    @NotBlank
    private String strategyName;
    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 城市id all:全部
     */
    @NotBlank
    private String cityIds;

    /**
     * 归属类目id
     */
    @NotBlank
    private String categoryIds;



    /**
     * 规则列表
     */
    @Valid
    private List<MatchRoutingRule> compensateDistributeStrategyList;


    @Valid
    private List<CompensateDistributeStrategy> compensateDistributeList;


    /**
     * 操作人id
     */
    private Long createAccountId;





    @Data
    public static class MatchRoutingRule{

        /**
         *规则项
         */
        private RoutingItem item;


        private Integer matchRoutingId;
    }


    @Data
    public static class RoutingItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("none_receive,none_appoint")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

    }


    @Data
    public static class CompensateDistributeStrategy{

        private Integer compensateDistributeId;

        private Long orderRoutingStrategyId;

    }
}