package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/27 17:57
 */
@Data
@ToString
@Table(name = "order_score_item_value")
public class OrderScoreItemValue {

    /**
     * 匹配项枚举值id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "value_id")
    private Long valueId;

    /**
     * 匹配项id
     */
    @Column(name = "score_item_id")
    private Long scoreItemId;

    /**
     * 匹配项枚举值编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 匹配项枚举值名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
