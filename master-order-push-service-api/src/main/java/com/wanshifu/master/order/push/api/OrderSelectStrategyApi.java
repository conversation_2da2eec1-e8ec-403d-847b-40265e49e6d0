package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.master.order.push.domain.resp.MasterQuotaResp;
import com.wanshifu.master.order.push.domain.rqt.MasterQuotaListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/3/4 15:22
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "orderSelectStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface OrderSelectStrategyApi {

    @PostMapping("create")
    Integer create(@Valid @RequestBody CreateOrderSelectStrategyRqt rqt);

    @PostMapping("update")
    Integer update(@Valid @RequestBody UpdateOrderSelectStrategyRqt rqt);

    @PostMapping("enable")
    Integer enable(@Valid @RequestBody EnableOrderSelectStrategyRqt rqt);

    @PostMapping("delete")
    Integer delete(@Valid @RequestBody DeleteOrderSelectStrategyRqt rqt);

    @PostMapping("detail")
    OrderSelectStrategy detail(@Valid @RequestBody OrderSelectStrategyDetailRqt rqt);

    @PostMapping("list")
    SimplePageInfo<OrderSelectStrategy> list(@Valid @RequestBody GetOrderSelectStrategyListRqt rqt);

    @PostMapping("/quotaList")
    SimplePageInfo<MasterQuotaResp> quotaList(@Valid @RequestBody MasterQuotaListRqt rqt);


}
