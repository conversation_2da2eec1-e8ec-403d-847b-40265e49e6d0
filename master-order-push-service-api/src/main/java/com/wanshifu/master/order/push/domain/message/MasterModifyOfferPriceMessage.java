package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 师傅修改报价
 */
@Data
public class MasterModifyOfferPriceMessage {


    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 报价金额
     */
    private BigDecimal offerPrice;

    /**
     * 师傅地区
     */
    private String masterRegion;

    /**
     * 下单用户id
     */
    private String accountId;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 报价数量
     */
    private int offerNumber;

    /**
     * 报价备注
     */
    private String offerPriceNote;

    /**
     * 师傅订单id
     */
    private Long masterOrderId;

    /**
     * 师傅id
     */
    private Long masterId;


    /**
     * 订单来源
     */
    private String orderFrom;


    private String masterName;



}
