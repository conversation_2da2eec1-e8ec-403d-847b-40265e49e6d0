package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "agreement_master_push")
public class AgreementMasterPush {


    /**
     * 注解id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private String id;


    /**
     * 注解id
     */
    @Column(name = "agreement_master_id")
    private String agreementMasterId;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 招募id
     */
    @Column(name = "recruit_id")
    private Long recruitId;

    /**
     * 日期
     */
    @Column(name = "dt")
    private String dt;

    /**
     * 已推单数量
     */
    @Column(name = "push_count_daily")
    private Long pushCountDaily;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
