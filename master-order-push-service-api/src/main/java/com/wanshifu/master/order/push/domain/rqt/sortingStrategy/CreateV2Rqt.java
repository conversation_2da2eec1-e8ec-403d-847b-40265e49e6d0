package com.wanshifu.master.order.push.domain.rqt.sortingStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.sortingStrategy.SortRule;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  创建精排策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-03 10:27
 */
@Data
public class CreateV2Rqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    @NotEmpty
    private String strategyName;

    /**
     * 类目id，多个以逗号拼接,全部类目:all
     */
    @NotEmpty
    private String categoryIds;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 精排规则
     */
    @NotEmpty(message = "至少包含一条规则")
    @Valid
    private List<SortRule> ruleList;


    private Long createAccountId;

    @ValueIn("normal,ikea")
    private String orderFlag;

}