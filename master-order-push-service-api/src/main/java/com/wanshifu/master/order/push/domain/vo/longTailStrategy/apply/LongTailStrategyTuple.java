package com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply;

import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.domain.common.FilterRuleExpression;
import com.wanshifu.master.order.push.domain.dto.AppointGroupExpressionDto;
import com.wanshifu.master.order.push.domain.dto.FilterStrategyRuleExpressionDto;
import com.wanshifu.master.order.push.domain.dto.RangeSelect;
import com.wanshifu.master.order.push.domain.dto.StatusSelect;
import com.wanshifu.master.order.push.domain.vo.StrategyTuple;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.PushRuleVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LongTailStrategyTuple{

    /**
     * 长尾单推送时间
     */
    Long intervalTime;


    /**
     * 长尾单开启条件
     */
    StrategyTuple preCondition;

    /**
     * 触发规则
     */
    StrategyTuple triggerCondition;

    /**
     * 范围初筛
     */
    private RangeSelect rangeSelect;

    /**
     * 状态初筛
     */
    private StatusSelect statusSelect;

    /**
     * 推送类型
     */
    private String pushType;

    /**
     * 推送人群
     */
    private AppointGroupExpressionDto appointGroup;


    /**
     * 推送规则
     **/
    private PushRuleVo pushRule;

    /**
     * 召回规则
     */
    private List<FilterRuleExpression> filterRuleExpressionList;

    public boolean trigger(DefaultContext<String, Object> context){
        return triggerCondition.executeQLExpression(context);
    }

}
