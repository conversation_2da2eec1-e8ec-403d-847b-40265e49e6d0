package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 推单限制方式类型
 */
@AllArgsConstructor
@Getter
public enum PushLimitRuleType {

    STOP_PUSHING_IMMEDIATELY("stop_pushing_immediately", "立即停止推送"),

    DECREASE_PUSH_BY_PERCENT("decrease_push_by_percent", "按比例递减推送"),

    FIXED_PERCENT("fixed_percent", "固定百分比");


    private final String code;

    private final String desc;



    private static final Map<String, PushLimitRuleType> valueMapping = new HashMap<>((int) (PushLimitRuleType.values().length / 0.75));


    static {
        for (PushLimitRuleType instance : PushLimitRuleType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static PushLimitRuleType asCode(String code) {
        return valueMapping.get(code);
    }

}
