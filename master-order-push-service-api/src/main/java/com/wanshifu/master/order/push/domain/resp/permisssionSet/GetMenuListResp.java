package com.wanshifu.master.order.push.domain.resp.permisssionSet;

import lombok.Data;

import java.util.List;

@Data
public class GetMenuListResp {


    private Integer menuId;

    private String menuName;

    private Integer menuLevel;

    private Boolean isLeaf;

    private List<Button> buttonList;

    private List<Tab> tabList;

    private List<GetMenuListResp> subMenu;

    @Data
    public static final class Button{

        private Integer buttonId;

        private String buttonName;

        public Button(){

        }

        public Button(Integer buttonId,String buttonName){
            this.buttonId = buttonId;
            this.buttonName = buttonName;
        }
    }


    @Data
    public static final class Tab{

        private Integer tabId;

        private String tabName;

        public Tab(){

        }

        public Tab(Integer tabId,String tabName){
            this.tabId = tabId;
            this.tabName = tabName;
        }
    }


}
