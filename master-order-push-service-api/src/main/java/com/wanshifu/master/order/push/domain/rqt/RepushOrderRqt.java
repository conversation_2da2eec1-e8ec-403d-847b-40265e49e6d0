package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;

/**
 * 重新推单消息实体
 * <AUTHOR>
 */
@Data
public class RepushOrderRqt extends OrderPushRqt{

    /**
     * 重推机制快照id
     */
    private Long repushPolicySnapshotId;

    /**
     * 最优报价数
     */
    private Integer bestOfferNum;

    /**
     * 推单版本号
     */
    private String orderVersion;

    /**
     * 时间标记
     */
    private String timeMark;

    private Integer delayTimeMinute;

    private String handoffTag;

    private String masterSourceType;

}
