package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * 推送进度表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "push_progress")
public class PushProgress {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "push_progress_id")
    private Long pushProgressId;

    @Column(name = "order_id")
    private Long orderId;

    /**
     * 推送新师傅数
     */
    @Column(name = "new_master_offset")
    private Integer newMasterOffset;

    /**
     * 推送老师傅数量
     */
    @Column(name = "old_master_offset")
    private Integer oldMasterOffset;

    /**
     * 初筛后师傅数量
     */
    @Column(name = "base_select_master_num")
    private Integer baseSelectMasterNum;

    /**
     * 待推送师傅总数
     */
    @Column(name = "list_length")
    private Integer listLength;

    /**
     * 已过滤师傅数
     */
    @Column(name = "filtered_master_num")
    private Integer filteredMasterNum;


    /**
     * 推送师傅数量（除强推师傅）
     */
    @Column(name = "master_offset")
    private Integer masterOffset;

    /**
     * 已推送师傅数
     */
    @Column(name = "pushed_master_num")
    private Integer pushedMasterNum;

    /**
     * 推送轮数
     */
    @Column(name = "pushed_round")
    private Integer pushedRound;

    /**
     * 停止推送原因
     */
    @Column(name = "current_stop_reason")
    private String currentStopReason;

    /**
     * 推送状态
     */
    @Column(name = "push_status")
    private String pushStatus;

    /**
     * 订单版本号
     */
    @Column(name = "order_version")
    private String orderVersion;

    /**
     * 强推师傅数量
     */
    @Column(name = "direct_push_num")
    private Integer directPushNum;

    /**
     * 首次推送时间
     */
    @Column(name = "first_push_time")
    private Date firstPushTime;

    /**
     * 推送类型, normal_push: 普通推送，nearby_push: 附近推送
     */
    @Column(name = "push_type")
    private String pushType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
