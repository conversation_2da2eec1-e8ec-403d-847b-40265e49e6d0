package com.wanshifu.master.order.push.domain.rqt.filterStrategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改召回策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 策略id
     */
    @NotNull
    private Long strategyId;

    private Long updateAccountId;
}