package com.wanshifu.master.order.push.domain.rqt.longTailStrategy;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  长尾策略详情Rqt.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class DetailRqt {

    /**
     * 策略id
     */
    @NotNull
    private Long longTailStrategyId;

    public DetailRqt(){

    }


    public DetailRqt(Long longTailStrategyId){
        this.longTailStrategyId = longTailStrategyId;
    }
}