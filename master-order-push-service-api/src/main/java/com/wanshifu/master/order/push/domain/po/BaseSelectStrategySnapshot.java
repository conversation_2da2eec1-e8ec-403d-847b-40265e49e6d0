package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 初筛策略快照表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "base_select_strategy_snapshot")
public class BaseSelectStrategySnapshot {

    /**
     * 快照id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "snapshot_id")
    private Long snapshotId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 开启条件
     */
    @Column(name = "open_condition")
    private String openCondition;

    /**
     * 范围初筛
     */
    @Column(name = "range_select")
    private String rangeSelect;

    /**
     * 技能初筛
     */
    @Column(name = "technique_select")
    private String techniqueSelect;

    /**
     * 状态初筛
     */
    @Column(name = "status_select")
    private String statusSelect;


    /**
     * 服务数据初筛
     */
    @Column(name = "serve_data_select")
    private String serveDataSelect;

    /**
     * 策略状态,1: 启用，0：禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 订单标识,normal:普通订单 ikea:宜家订单
     */
    @Column(name = "order_flag")
    private String orderFlag;
}