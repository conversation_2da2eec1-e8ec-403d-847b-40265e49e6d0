package com.wanshifu.master.order.push.domain.enums;

/**
 * 范围初筛类型
 * <AUTHOR>
 */
public enum RangeSelectType {

    /**
     * 服务地址与师傅常住地的距离
     */
    ORDER_MASTER_DISTANCE("order_master_distance"),

    /**
     * 服务地址所处师傅服务区域范围(三级地址)
     */
    ORDER_MASTER_SERVE_DIVISION("order_master_serve_division"),

    /**
     * 服务地址所处师傅服务区域范围(四级地址)
     */
    ORDER_MASTER_SERVE_DIVISION_LEVEL4 ("order_master_serve_division_level4"),

    /**
     * 服务地址与师傅常住地的距离所匹配的师傅服务范围
     */
    ORDER_MASTER_DISTANCE_AND_ORDER_MASTER_SERVE_DIVISION("order_master_distance_and_order_master_serve_division"),

    /**
     * 服务地址所处师傅服务区域范围内或者师傅常住地的距离
     */
    ORDER_MASTER_DISTANCE_OR_ORDER_MASTER_SERVE_DIVISION("order_master_distance_or_order_master_serve_division"),

    /**
     * 服务地址在师傅所在城市(师傅服务区县外)
     */
    ORDER_MASTER_ORT_DISTRICT("order_master_out_district")
    ;


    public final String code;

    RangeSelectType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }

}
