package com.wanshifu.master.order.push.domain.rqt.strategyCombination;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 描述 :  策略组合列表Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id
     */
    private Long businessLineId;

    /**
     * 策略名称
     */
    private String combinationName;;

    /**
     * 城市id
     */
    private String cityIds;

    /**
     * 类目id
     */
    private String categoryIds;


    /**
     * 组合状态 1:启用 0:禁用
     */
    @ValueIn("0,1")
    private Integer combinationStatus;

    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;

    private String orderFlag;
}