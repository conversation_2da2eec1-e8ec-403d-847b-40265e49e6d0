package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.NormalOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "normalOrderDistributeStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface NormalOrderDistributeStrategyApi {

    @PostMapping("/list")
    SimplePageInfo<NormalOrderDistributeStrategy> list(@RequestBody @Valid ListRqt rqt);
    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    Integer create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    Integer update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    NormalOrderDistributeStrategy detail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    Integer enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    Integer delete(@RequestBody @Valid DeleteRqt rqt);


}
