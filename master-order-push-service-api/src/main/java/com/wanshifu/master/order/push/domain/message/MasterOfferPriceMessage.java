package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class MasterOfferPriceMessage {


    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 报价金额
     */
    private BigDecimal offerPrice;

    /**
     * 师傅地区
     */
    private String masterRegion;

    /**
     * 下单用户id
     */
    private String accountId;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 报价数量
     */
    private int offerNumber;

    /**
     * 报价备注
     */
    private String offerPriceNote;

    /**
     * 师傅订单id
     */
    private Long masterOrderId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 报价类型,0:师傅报价（默认）,1:系统报价, 2:专属报价, 3:订单包报价(系统报价),4:合约师傅报价（系统自动报价）
     */
    private Integer offerPriceType;


    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 是否报价已满
     */
    private Boolean isLastOfferPrice;

    private String orderTag;

    /**
     * 是否是师傅店铺订单
     */
    private Boolean isMasterShop;

    private String masterName;

    /**
     * 订单推送时间
     */
    private String orderPushTime;


    /** 是否专属订单 1是 0否 */
    /**
     * 专属师傅二期代码调整如下：
     * 101系统报价   102抢单报价单(招募价格)  103成品报价(师傅自主报价) 104家庭专属师傅订单
     * 101,102需要自动支付
     */
    private Integer exclusiveFlag;

    /**
     * 是否是订单包
     */
    private Boolean isOrderPackage;

    private OrderPackageInfo orderPackageInfo;

    /**
     * 报价来源
     */
    private String offerSource;

    /**
     * 省下级地址id
     * (当前业务传市、区、街道级别的地址都行，order-push-service负责计算省下级地址id)
     */
    private Long provinceNextId;

    /**
     * 订单标签，多个英文逗号隔开
     */
    private String orderTagTypes;

    private Boolean isLastPush;


    /**
     * 报价人数已满推单场景,cooperative_business: 合作经营订单扩充名额 ,all_time_order : 全时师傅订单扩充名额, extra_contest_offer_number: 额外竞报扩充名额
     */
    private List<String> fullOfferPushScenes;

    @Data
    public static class OrderPackageInfo{

        private Long orderPackageId;

        private String orderPackageNo;

        private String orderPackageTitle;
    }
}
