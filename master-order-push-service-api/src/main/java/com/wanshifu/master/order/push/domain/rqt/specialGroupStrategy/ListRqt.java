package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import com.wanshifu.framework.core.page.Pager;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 特殊人群策略列表查询请求类
 *
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@ApiModel(description = "特殊人群策略列表查询请求类")
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "服务id集合")
    private String serveIds;

    @ApiModelProperty(value = "创建开始时间")
    private Date createStartTime;

    @ApiModelProperty(value = "创建结束时间")
    private Date createEndTime;

    @ApiModelProperty(value = "策略状态")
    private Integer strategyStatus;
}
