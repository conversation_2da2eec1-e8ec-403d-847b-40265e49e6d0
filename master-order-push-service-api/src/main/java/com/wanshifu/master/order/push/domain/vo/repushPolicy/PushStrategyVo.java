package com.wanshifu.master.order.push.domain.vo.repushPolicy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  重推机制策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 15:34
 */
@Data
public class PushStrategyVo {

    /**
     * 开启条件
     */
    @NotNull
    @Valid
    private OpenCondition openCondition;


    /**
     * 重推机制，new_strategy_combination:按新路由重新推送
     */
    @NotEmpty
    @ValueIn("new_strategy_combination")
    private String repushPolicy;

    /**
     * 是否删除已推送师傅
     */
    @NotNull
    private Integer isFilterPushedMaster;

    /**
     * 策略组合
     */
    @NotNull
    @Valid
    private StrategyCombination strategyCombination;

    @NotBlank
    @ValueIn("new,old")
    private String pushRuleType;

    /**
     * 推送规则
     */
    @Valid
    private List<PushRule> pushRule;


    private Integer pushRuleId;


    @Data
    public static class OpenCondition {

        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *开启条件规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;

        /**
         * n分钟报价人数 < m
         */
        @NotNull
        private OfferIntervalTime offerIntervalTime;

        private OpenConditionQlExpression openConditionQlExpression;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }

    @Data
    public static class OpenConditionQlExpression{
        private String qlExpression;
        private String qlExpressionParams;

        public OpenConditionQlExpression(String qlExpression, String qlExpressionParams) {
            this.qlExpression = qlExpression;
            this.qlExpressionParams = qlExpressionParams;
        }
    }


    @Data
    public static class OfferIntervalTime {
        @NotNull
        private Integer interval;
        @NotEmpty
        @ValueIn("<")
        private String term;
        @NotNull
        private Integer itemValue;
    }


    @Data
    public static class StrategyCombination {

        /**
         * 初筛策略id
         */
        @NotNull
        private Long baseSelectStrategyId;
        /**
         * 召回策略id
         */
        @NotNull
        private Long filterStrategyId;
        /**
         * 精排策略id
         */
        @NotNull
        private Long sortingStrategyId;
    }

    @Data
    private static class PushRule {

        /**
         * 指派模式 2:报价招标 4:一口价 5:预付款
         */
        @ValueIn("2,4,5")
        @NotNull
        private Integer appointType;

        /**
         * 最佳报价数
         */
        @NotNull
        private Integer bestOfferNum;

        /**
         * 推送时间间隔
         */
        @NotNull
        private Integer delayMinutesBetweenRounds;

        /**
         * 首轮推送人数
         */
        @NotNull
        private Integer firstPushMasterNumPerRound;

        /**
         * 非首轮推送人数
         */
        @NotNull
        private Integer delayPushMasterNumPerRound;

        /**
         * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String firstPushMasterFlag;

        /**
         * 首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal firstPushMasterPercent;

        /**
         * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String delayPushMasterFlag;

        /**
         * 非首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private BigDecimal delayPushMasterPercent;
    }
}