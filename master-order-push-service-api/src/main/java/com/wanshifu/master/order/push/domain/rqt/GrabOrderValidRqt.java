package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:31
 */
@Data
public class GrabOrderValidRqt {

    @NotNull
    private Date orderModifyTime;

    @NotNull
    private Long orderId;

    @NotNull
    private Long masterId;

    /**
     * 抢单类型,0:抢单(默认),1:合约师傅自动抢单(兜底计划任务:OrderAutoGrabRule) ,2:订单包自动抢单 3:师傅开启自动接单 4:代理商自动抢单
     */
    private Integer grabType = 0;

    /**
     * 0:师傅主动报价  3:师傅开启自动接单
     */
    private Integer offerPriceType = 0;
}
