package com.wanshifu.master.order.push.api;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderDynamicRoundsPush;
import com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush.ListRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "orderDynamicRoundsPush",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface OrderDynamicRoundsPushApi {

    /**
     * 长尾策略组列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<OrderDynamicRoundsPush> list(@RequestBody @Valid ListRqt rqt);
}
