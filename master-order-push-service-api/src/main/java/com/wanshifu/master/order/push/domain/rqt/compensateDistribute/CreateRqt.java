package com.wanshifu.master.order.push.domain.rqt.compensateDistribute;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;


@Data
public class CreateRqt {

    /**
     * 业务线
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    @NotBlank
    private String categoryIds;

    @NotBlank
    private String appointTypes;

    @NotBlank
    private String strategyName;

    private String strategyDesc;

    @NotBlank
    @ValueIn("exclusive,normal,contract,new_model,excellent,direct_appoint,brand,order_package,agent,agreement,enterprise_appoint")
    private String orderPushFlag;

    @NotNull
    private Integer hasPrice;

    @NotNull
    private Integer hasCooperationUser;

    @NotBlank
    @ValueIn("none_appoint,none_receive,none_master")
    private String compensateType;

    @NotNull
    private Integer intervalTime;

    @NotNull
    private Integer triggerNum;

    private Long createAccountId;

}
