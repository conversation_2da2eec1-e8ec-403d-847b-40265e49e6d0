package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class EnterpriseAppointMasterResultMessage {

    private Long enterpriseId;

    private Long orderId;

    private Long globalOrderTraceId;

    /**
     * 1: 四级地址匹配
     * 2：无四级地址匹配(就近指派的逻辑)
     *
     * 0：未匹配
     */
    private Integer type;

    /**
     * 总包需要按优先级指派的协议师傅信息(包含过滤的师傅)
     *
     */
    private List<MatchMaster> matchAgreementMasterList;



    /**
     * 总包需要按优先级指派的全时师傅信息(包含过滤的师傅)
     *
     */
    private List<MatchMaster> matchFullTimeMasterList;


    @Data
    public static class MatchMaster {

        /**
         * 师傅id
         */
        private Long masterId;

        /**
         * 招募id
         */
        private Long recruitId;

        /**
         * 排序字段，值越小，优先级越高
         */
        private Integer sort;

        /**
         * 招募标签名
         */
        private String tagName;

        /**
         * 计价方式：master: 按师傅计价（街道），unite: 统一计价（全国）,,city:按城市定价,regional:按区域定价
         */
        private String priceType;

        /**
         * 是否可用
         * 1：可用，总包依据该字段按sort排序做指派
         * 0：不可用，被过滤
         */
        private Integer isCanHired;

        /**
         * 过滤原因
         * isCanHired字段为0时，展示过滤原因
         */
        private String reason;

        /**
         * 走就近指派才有的字段
         */
        private Long street;

        /**
         * 走就近指派才有的字段
         */
        private Long distance;


        private BigDecimal price;

    }


    private EnterpriseAppointMessage.AutoHireMasterWebRqt autoHireMasterWebRqt;

    @Data
    public static class AutoHireMasterWebRqt{

        private boolean cancelMaster;

        private Integer changeHireType;

        private boolean definiteOffer;

        private boolean enquiryOrder;

        private boolean openAssign;

        private boolean skipRepeatOrderCheck;

        private List<Long> stopMasterIds;
    }



}
