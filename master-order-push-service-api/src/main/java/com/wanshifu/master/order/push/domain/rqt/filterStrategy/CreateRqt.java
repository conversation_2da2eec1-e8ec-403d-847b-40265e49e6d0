package com.wanshifu.master.order.push.domain.rqt.filterStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.filterStrategy.RuleItem;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  创建召回策略.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-03 10:27
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    @NotEmpty
    private String strategyName;

    /**
     * 类目id，多个以逗号拼接 all:全部类目
     */
    @NotEmpty
    private String categoryIds;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     *  normal:普通订单 ikea:宜家订单
     */
    @ValueIn("normal,ikea")
    private String orderFlag;

    /**
     * 规则列表
     */
    @NotEmpty(message = "至少包含一条规则")
    @Valid
    private List<RuleItem> ruleList;

    private Long createAccountId;
}