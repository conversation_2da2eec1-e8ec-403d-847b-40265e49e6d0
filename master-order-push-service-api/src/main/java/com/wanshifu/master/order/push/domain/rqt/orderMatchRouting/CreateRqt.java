package com.wanshifu.master.order.push.domain.rqt.orderMatchRouting;


import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;


/**
 * 描述 :  创建路由Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class CreateRqt {


    /**
     * 路由名称
     */
    @NotBlank
    private String routingName;

    /**
     * 路由描述
     */
    @NotNull
    private String routingDesc;


    /**
     * 订单标识,all: 不限，site: 企业，enterprise: 总包，family: 家庭
     */
    @NotBlank
    @ValueIn("all,site,enterprise,family")
    private String orderFrom;

    /**
     * 订单标识,none: 无特定标识，new_master_support: 新师傅扶持，new_model: 样板城市
     */
    @NotBlank
    @ValueIn("none,new_master_support,new_model,enterprise_appoint")
    private String orderTag;

    /**
     * 路由类型
     */
    @NotBlank
    private String routingType;

    /**
     * 匹配路由
     */
    @NotBlank
    private String matchRouting;

    /**
     * 师傅资源1
     */
    @NotBlank
    private String lv1MasterType;

    /**
     * 师傅资源2
     */
    private String lv2MasterType;

    /**
     * 师傅资源3
     */
    private String lv3MasterType;

    /**
     * 创建人账号
     */
    private Long createAccountId;

}