package com.wanshifu.master.order.push.domain.enums;

/**
 * @Author: chengbin
 * @Date: 2025/5/15 10:34
 * @Description: 消费者MQ的Tag方法（包含外部与内部）
 */
public enum MqTagEnum {

    MATCH_RESULT("match_result","推单结果通知"),

    //家庭样板城市项目后台调度手动指派师傅功能新加
    RECOMMEND_MATCH_RESULT("recommend_match_result","推单的推荐师傅通知"),

    OFFER_ORDER_PUSH_CLEAR("offer_order_push_clear", "Offer订单已雇佣-清理订单推单记录"),

    EXCLUSIVE_ORDER_TRANSFER("exclusive_order_transfer", "专属订单转单"),

    MASTER_OFFER_PRICE("master_offerPrice", "师傅报价"),

    MASTER_SERVE_FINISH("master_serveFinish", "师傅服务完工"),

    ORDER_CLOSE("order_close", "订单关单"),


    FILTER_MASTER_FOR_NO_OFFER("filter_master_for_no_offer","筛选落库合适的师傅在无人报价时可进行邀请报价"),



    ORDER_PUSH_UPDATE_OFFER("order_push_update_offer", "首次报价更新报价状态"),


    INTERFERE_ORDER_PUSH("interfere_order_push","平台干预推单列表"),



    ;
    public final String value;

    public final String describe;

    MqTagEnum(String value, String describe) {
        this.value = value;
        this.describe = describe ;
    }

    public String getValue() {
        return value;
    }

    public String getDescribe(){return describe;}

}
