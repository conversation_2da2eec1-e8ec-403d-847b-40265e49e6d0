package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.RepushPolicy;
import com.wanshifu.master.order.push.domain.rqt.repushPolicy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "repushPolicy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface RepushPolicyApi {

    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 重推策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    RepushPolicy detail(@RequestBody @Valid DetailRqt rqt);


    /**
     * 启用/禁用重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 重推机制列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<RepushPolicy> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 删除重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);
}
