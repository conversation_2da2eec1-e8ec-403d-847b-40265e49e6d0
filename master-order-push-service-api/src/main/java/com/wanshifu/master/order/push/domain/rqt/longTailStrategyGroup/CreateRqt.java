package com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup;

import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailGroupRule;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  创建长尾策略组
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotEmpty
    private String longTailStrategyGroupName;

    /**
     * 策略名称
     */
    private String longTailStrategyGroupDesc;

    /**
     * 类目id，多个以逗号拼接 all:全部类目
     */
    @NotEmpty
    private String categoryIds;


    @NotEmpty
    private String openCityMode;

    private String pushType;


    @NotEmpty
    private String cityIds;


    /**
     * 规则
     */
    @NotNull
    private List<LongTailGroupRule> longTailGroupRuleList;

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    private Long createAccountId;
}