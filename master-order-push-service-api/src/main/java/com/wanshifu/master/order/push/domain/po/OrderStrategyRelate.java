package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 14:51
 */
@Data
@ToString
@Table(name = "order_strategy_relate")
public class OrderStrategyRelate {


    /**
     * PK
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    /**
     * 关联策略id
     */
    @Column(name = "relate_id")
    private Integer relateId;

    /**
     * 关联类型[repush_policy:重推机制,strategy_combination_priority:策略组合-优先推荐路由,strategy_combination_alternate:策略组合-备用推荐路由]
     */
    @Column(name = "relate_type")
    private String relateType;

    /**
     * 初筛策略id
     */
    @Column(name = "order_select_strategy_id")
    private Integer orderSelectStrategyId;

    /**
     * 召回策略id
     */
    @Column(name = "order_scoring_strategy_id")
    private Integer orderScoringStrategyId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
