//package com.wanshifu.master.order.push.service;
//
//import com.alicloud.openservices.tablestore.model.*;
//import com.alicloud.openservices.tablestore.model.search.*;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.GeoBoundingBoxQuery;
//import com.alicloud.openservices.tablestore.model.search.query.GeoDistanceQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.alicloud.openservices.tablestore.model.search.query.QueryOperator;
//import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.framework.utils.StringUtils;
//import com.wanshifu.master.order.push.domain.common.BaseSelect;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.common.PushFilter;
//import com.wanshifu.master.order.push.domain.common.PushFilterList;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import com.wanshifu.master.order.push.domain.dto.MasterBase;
//import com.wanshifu.master.order.push.domain.dto.RangeSelect;
//import com.wanshifu.master.order.push.domain.dto.StatusSelect;
//import com.wanshifu.master.order.push.domain.dto.TechniqueSelect;
//import com.wanshifu.master.order.push.domain.enums.*;
//import com.wanshifu.master.order.push.util.DateFormatterUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.common.filter.impl.Op;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.time.ZoneOffset;
//import java.util.*;
//
///**
// * tablestore师傅初筛
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class OTSMasterSelector {
//
//	/**
//	 * 2020-06-16 修改初筛查询条件
//	 */
//	private static final String T_MASTER_BASE = "t_master_base";
//
////	@Resource
////	private TableStoreClient tableStoreClient;
//
//
//	@Value("${base.select.limit}")
//	private Integer baseSelectLimit;
//
//
//	@Value("${nearby.push.limit.num}")
//	private Integer nearbyPushLimitNum;
//
//
//	@Value("${base.select.index:t_master_base_index}")
//	private String baseSelectIndex;
//
//
//
//	/**
//	 * 获取订单指派师傅
//	 * @param globalOrderId
//	 * @return
//	 */
//	public String getAppointedMasterByGlobalOrderId(String globalOrderId) {
//		final Row rowWithOnePK = tableStoreClient.getRowWithOnePK("t_global_order", "global_order_id", globalOrderId);
//		return tableStoreClient.getValue(rowWithOnePK,"hire_master_id");
//	}
//
//
//	private void setBaseNotQuery(List<Query> mustNotQueryList,MasterMatchCondition masterMatchCondition) {
//		//服务限制
//		mustNotQueryList.add(tableStoreClient.stringMatchQueryWithMinim(
//				"master_forbidden_serve_ids",
//				masterMatchCondition.getServeIds(),
//				QueryOperator.OR,1));
//	}
//
//	private void setNotQueryBusinessLine(List<Query> mustNotQueryList,Integer businessLineId){
//		//业务线推单限制
//		mustNotQueryList.add(tableStoreClient.stringMatchQueryWithMinim(
//				FieldConstant.MASTER_FORBIDDEN_BUSINESS_IDS,
//				String.valueOf(businessLineId),
//				QueryOperator.OR,1));
//	}
//
//	/**
//	 * 师傅所属品牌
//	 * @param mustQuery
//	 */
//	private void setBrandQuery(List<Query> mustQuery,String brand) {
//		mustQuery.add(tableStoreClient.stringTermQuery("master_brand", brand));
//	}
//
//	/**
//	 * 功能限制-禁止推单||禁止登录
//	 * @param mustQuery
//	 */
//	private void setPushRestrictQuery(List<Query> mustQuery){
//		mustQuery.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_PUSH_RESTRICT_NORMAL, true));
//	}
//
//	public Set<String> searchMasterByBaseSelectStrategy(MasterMatchCondition masterMatchCondition, BaseSelect baseSelect) {
//
//
//		RangeSelect rangeSelect = baseSelect.getRangeSelect();
//		StatusSelect statusSelect = baseSelect.getStatusSelect();
//		TechniqueSelect techniqueSelect = baseSelect.getTechniqueSelect();
//
//		masterMatchCondition.setDivisionMatchLevel(3);
//
//		return searchMasterByBaseSelectStrategyIndex(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect);
//
//
//	}
//
//
//	public Set<String> searchMasterByBaseSelectStrategy(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect, StatusSelect statusSelect) {
//		return searchMasterByBaseSelectStrategyIndex(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect);
//
//
//	}
//
//
//	/**
//	 * 限制枚举
//	 */
//	private static enum RestrictAction {
//		PUSH{
//			@Override
//			public String getActionString(){
//				return "1";
//			};
//		},LOGIN{
//			@Override
//			public String getActionString(){
//				return "2";
//			};
//		},EXCLUSIVE{
//			@Override
//			public String getActionString(){
//				return "14";
//			};
//		},NO_QUOTATION{
//			@Override
//			public String getActionString(){
//				return "3";
//			};
//		},NO_APPOINT{
//			@Override
//			public String getActionString(){
//				return "4";
//			};
//		};
//		public String getActionString(){
//			return null;
//		};
//	};
//
//
//	/**
//	 * 专属订单全额退款限制推送
//	 * @param orderGoodsLv1Id
//	 * @param masterIdsSet
//	 */
//	public void exclusiveRefundBlackList(String orderGoodsLv1Id,Set<String> masterIdsSet){
//		if (masterIdsSet != null) {
//			try {
//				final Set<String> restrictMasters = searchRestrictMasterByIndex(
//						RestrictAction.EXCLUSIVE.getActionString(),
//						orderGoodsLv1Id, masterIdsSet);
//				if (restrictMasters.size()!=0) {
//					masterIdsSet.removeAll(restrictMasters);
//				}
//			} catch (Exception e) {
//				log.warn("exclusiveRefundBlackList error:{},{}",e,masterIdsSet);
//			}
//		}
//	}
//
//
//	/**
//	 * 过滤功能限制师傅
//	 * @param RestrictAction 1：禁推单，2：禁登录，3：禁专属
//	 * @param orderGoodsLv1Id
//	 * @param inputMasterIdsSet
//	 * @return
//	 */
//
//	private static final String MASTER = "t_master";
//	private static final String MASTER_INDEX = "master_index_v1";
//
//
//	private Set<String> searchRestrictMasterByIndex(String RestrictAction,String orderGoodsLv1Id,Set<String> inputMasterIdsSet) {
//		HashSet<String> masterIds=new HashSet<>();
//		if (RestrictAction==null) {
//			return masterIds;
//		}
//		SearchQuery searchQuery = new SearchQuery();
//		BoolQuery boolQuery = new BoolQuery();
//		List<Query> mustQueryList=new ArrayList<Query>();
//		mustQueryList.add(tableStoreClient.stringTermsQuery(FieldConstant.MASTER_ID, inputMasterIdsSet));
//		setRestrictMasterQuery(mustQueryList,RestrictAction,orderGoodsLv1Id);
//		boolQuery.setMustQueries(mustQueryList);
//		searchQuery.setQuery(boolQuery);
//		SearchRequest searchRequest = new SearchRequest(MASTER, MASTER_INDEX, searchQuery);
//		SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//		// 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//		List<Row> rows = resp.getRows();
//		while(resp.getNextToken() != null){
//			//把token设置到下一次请求中
//			searchRequest.getSearchQuery().setToken(resp.getNextToken());
//			resp = tableStoreClient.getSyncClient().search(searchRequest);
//			if (!resp.isAllSuccess()){
//				throw new RuntimeException("not all success");
//			}
//			rows.addAll(resp.getRows());
//		}
//		for (Row row : rows) {
//			if (row!=null) {
//				masterIds.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//			}
//		}
//		return masterIds;
//	}
//
//
//	/**
//	 * 被限制的师傅查询条件
//	 */
//	private void setRestrictMasterQuery(List<Query> mustQueryList,String RestrictAction,String orderGoodsLv1Id){
//		mustQueryList.add(tableStoreClient.stringTermQuery("restrict_action", RestrictAction));
//		mustQueryList.add(tableStoreClient.stringTermQuery("limit_lv1_goods_ids", orderGoodsLv1Id));
//	}
//	private void setRestrictMasterQuery(List<Query> mustQueryList,String ... restrictActions){
//		mustQueryList.add(tableStoreClient.stringTermsQuery("restrict_action", restrictActions));
//	}
//
//
//	/**
//         * 根据初筛策略筛选师傅
//         * @param
//         * @return
//         */
//		private Set<String> searchMasterByBaseSelectStrategyIndex(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect, StatusSelect statusSelect) {
//			SearchQuery searchQuery = new SearchQuery();
//			BoolQuery boolQuery = new BoolQuery();
//			List<Query> mustQueryList = new ArrayList<>();
//			List<Query> mustNotQueryList=new ArrayList<>();
//
//
//			//范围初筛
//			if (RangeSelectType.ORDER_MASTER_DISTANCE.code.equals(rangeSelect.getRangeType())) {
//				if (StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())) {
//					//匹配订单与师傅所在地距离
//					Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(it -> {
//						mustQueryList.add(it);
//						//是否根据距离推送
//						masterMatchCondition.setAccordingDistancePush(true);
//					});
//				} else {
//					mustQueryList.add(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
//				}
//
//			} else if (RangeSelectType.ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
//				//匹配订单与师傅服务区域（三级地址）
//				mustQueryList.add(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
//			} else if (RangeSelectType.ORDER_MASTER_DISTANCE_AND_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
//				//匹配订单与师傅所在地距离 and 匹配订单与师傅服务区域
//				Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(mustQueryList::add);
//				mustQueryList.add(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
//			} else if (RangeSelectType.ORDER_MASTER_DISTANCE_OR_ORDER_MASTER_SERVE_DIVISION.code.equals(rangeSelect.getRangeType())) {
//				//匹配订单与师傅所在地距离 or 匹配订单与师傅服务区域（三级地址）
//				List<Query> queryList = new ArrayList<>();
//				queryList.add(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
//				Optional.ofNullable(this.getMatchOrderMasterDistanceQuery(masterMatchCondition, rangeSelect.getRangeRule().getDistance())).ifPresent(queryList::add);
//				BoolQuery boolQueryMatch = new BoolQuery();
//				boolQueryMatch.setShouldQueries(queryList);
//				boolQueryMatch.setMinimumShouldMatch(1);
//				mustQueryList.add(boolQueryMatch);
//				//是否根据距离推送
//				masterMatchCondition.setAccordingDistancePush(true);
//			} else if (RangeSelectType.ORDER_MASTER_SERVE_DIVISION_LEVEL4.code.equals(rangeSelect.getRangeType())) {
//				//匹配订单与师傅服务区域（四级地址） 订单无四级地址时,按照三级地址匹配
//				if (masterMatchCondition.getFourthDivisionId() != null && masterMatchCondition.getFourthDivisionId() > 0L) {
//					masterMatchCondition.setDivisionMatchLevel(4);
//				}
//				mustQueryList.add(this.getMatchOrderMasterServeDivisionQuery(masterMatchCondition));
//			}
//
//			//技能初筛
//			if (TechniqueSelectType.FILTER_OUT_NO_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
//				BoolQuery shouldQuery = tableStoreClient
//						.stringShouldMatchQuery(
//								"master_technique_ids",
//								masterMatchCondition.getTechnologysInDemandSet(),
//								1);
//				mustQueryList.add(shouldQuery);
//			} else if (TechniqueSelectType.FILTER_OUT_NO_MATCH_IKEA.code.equals(techniqueSelect.getTechniqueType())) {
//				//过滤非宜家师傅时，技能是隐藏条件，必须符合
//				BoolQuery shouldQuery = tableStoreClient
//						.stringShouldMatchQuery(
//								"master_technique_ids",
//								masterMatchCondition.getTechnologysInDemandSet(),
//								1);
//				mustQueryList.add(shouldQuery);
//				this.setBrandQuery(mustQueryList, masterMatchCondition.getMasterBrand());
//			}else if (TechniqueSelectType.FILTER_OUT_MATCH_TECHNIQUE.code.equals(techniqueSelect.getTechniqueType())) {
//				BoolQuery shouldQuery = tableStoreClient
//						.stringShouldMatchQuery(
//								"master_technique_ids",
//								masterMatchCondition.getTechnologysInDemandSet(),
//								1);
//				mustNotQueryList.add(shouldQuery);
//			}
//
//		//状态初筛
//		Optional.ofNullable(statusSelect).ifPresent(it->it.getItemList().forEach(statusSelectItem -> {
//			if(StatusSelectType.MASTER_STATUS.code.equals(statusSelectItem.getItemName()) && StringUtils.isNotBlank(statusSelectItem.getItemValue())){
//
//				String[] statusList = statusSelectItem.getItemValue().split(",");
//
//				Arrays.stream(statusList).distinct().forEach(e -> {
//					if(MasterStatusType.BLACK_LIST.code.equals(e)){
//						mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_BLACK_LIST_STATUS_NORMAL, "in".equals(statusSelectItem.getTerm())));
//					}else if(MasterStatusType.ACCOUNT_STATUS_ABNORMAL.code.equals(e)){
//						mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_ACCOUNT_NORMAL, "in".equals(statusSelectItem.getTerm())));
//					}else if(MasterStatusType.ACCOUNT_FREEZE.code.equals(e)){
//						if("in".equals(statusSelectItem.getTerm())){
//							long freezingRecoverTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
//							mustQueryList.add(tableStoreClient.longRangeQuery(FieldConstant.FREEZING_TIME, 0L,freezingRecoverTime));
//						}
//					}else if(MasterStatusType.NOT_SETTLED.code.equals(e)){
//						mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_SETTLE_STATUS_NORMAL, "in".equals(statusSelectItem.getTerm())));
//					}else if(MasterStatusType.NOT_ACTIVE_GT_30.code.equals(e)){
//						if("in".equals(statusSelectItem.getTerm())){
//							mustQueryList.add(tableStoreClient.longCompareQuery(FieldConstant.LAST_ACTIVE_TIME,RangeQueryType.GREATER_THAN ,DateFormatterUtil.getAssignDayTimeStamp(-30)));
//						}
//					}else if(MasterStatusType.NOT_WORKING.code.equals(e)){
//						mustQueryList.add(tableStoreClient.longTermQuery(FieldConstant.REST_STATE, "in".equals(statusSelectItem.getTerm()) ? 1L : 0L));
//					}else if(MasterStatusType.EXAM_NOT_PASS.code.equals(e)){
//						mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_RULE_EXAM_STATUS_NORMAL, "in".equals(statusSelectItem.getTerm())));
//					}
//				});
//			}
//		}));
//
////		if (masterMatchCondition.needBrandMaster()) {
////			setBrandQuery(mustQueryList, masterMatchCondition.getMasterBrand());
////		}
//		//功能限制-禁止推单||禁止登录
//		setPushRestrictQuery(mustQueryList);
//
//
//		if(StringUtils.isNotBlank(masterMatchCondition.getServeIds())){
//			setBaseNotQuery(mustNotQueryList,masterMatchCondition);
//		}
//
//		//禁止推单的业务线
//		 this.setNotQueryBusinessLine(mustNotQueryList,masterMatchCondition.getBusinessLineId());
//
//
//		boolQuery.setMustQueries(mustQueryList);
//		boolQuery.setMustNotQueries(mustNotQueryList);
//		searchQuery.setQuery(boolQuery);
//
//		Long startTime = System.currentTimeMillis();
//		searchQuery.setLimit(baseSelectLimit);
//		SearchRequest searchRequest = new SearchRequest(T_MASTER_BASE, baseSelectIndex, searchQuery);
//		SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//		Long endTime = System.currentTimeMillis();
//
//		log.info("订单:{} ,searchMasterByBaseSelectStrategyIndex take times:{}",masterMatchCondition.getMasterOrderId(),(endTime - startTime));
//
//
//		// 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//		List<Row> rows = resp.getRows();
//		while(resp.getNextToken() != null){
//			//把token设置到下一次请求中
//			searchRequest.getSearchQuery().setToken(resp.getNextToken());
//			resp = tableStoreClient.getSyncClient().search(searchRequest);
//			if (!resp.isAllSuccess()){
//				throw new RuntimeException("not all success");
//			}
//			rows.addAll(resp.getRows());
//		}
//		HashSet<String> masterIds=new HashSet<>();
//		for (Row row : rows) {
//			if (row!=null) {
//				masterIds.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//			}
//		}
//
//		return masterIds;
//	}
//
//
//	private Query getMatchOrderMasterServeDivisionQuery(MasterMatchCondition masterMatchCondition) {
//		Query query;
//		//区域
//		switch (masterMatchCondition.getDivisionMatchLevel()) {
//			case 3:
//				query = tableStoreClient.stringTermQuery(FieldConstant.SERVE_DIVISION_IDS, String.valueOf(masterMatchCondition.getThirdDivisionId()));
//				break;
//			case 4:
//				query = tableStoreClient.stringTermQuery(FieldConstant.SERVE_FOURTH_DIVISION_IDS, String.valueOf(masterMatchCondition.getFourthDivisionId()));
//				break;
//			default:
//				//never 没有区域的订单不会执行到查询
//				query = tableStoreClient.stringTermQuery(FieldConstant.SERVE_DIVISION_IDS, String.valueOf(masterMatchCondition.getThirdDivisionId()));
//				break;
//		}
//		return query;
//	}
//
//	private Query getMatchOrderMasterDistanceQuery(MasterMatchCondition masterMatchCondition, Double distance) {
//		if (StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())) {
//			String[] lngLatArray = masterMatchCondition.getOrderLngLat().split(",");
//			if (lngLatArray.length == 2) {
//				BoolQuery boolQuery = new BoolQuery();
//				List<Query> queryList = new ArrayList<>();
//				//按照距离取有经纬度的师傅
//				GeoDistanceQuery geoDistanceQuery = new GeoDistanceQuery();
//				geoDistanceQuery.setFieldName("lat_lng");
//				//设置中心点
//				geoDistanceQuery.setCenterPoint(lngLatArray[1] + "," + lngLatArray[0]);
//				//设置条件为到中心点的距离不超过10000米
//				geoDistanceQuery.setDistanceInMeter(distance * 1000);
//
//				//取没有经纬度的师傅 &&三级服务地址相匹配的师傅
//				BoolQuery subBoolQuery = new BoolQuery();
//				List<Query> subQueryList = new ArrayList<>();
//
//				//三级服务地址相匹配的师傅
//				TermQuery serveDivisionLv3Query = tableStoreClient.stringTermQuery(FieldConstant.SERVE_DIVISION_IDS, String.valueOf(masterMatchCondition.getThirdDivisionId()));
//				//无经纬度的师傅
//				GeoBoundingBoxQuery noLngLatQuery = new GeoBoundingBoxQuery();
//				noLngLatQuery.setFieldName("lat_lng");
//				noLngLatQuery.setTopLeft("0.1,0.0");
//				noLngLatQuery.setBottomRight("0.0,0.1");
//
//				subQueryList.add(serveDivisionLv3Query);
//				subQueryList.add(noLngLatQuery);
//				subBoolQuery.setMustQueries(subQueryList);
//
//				queryList.add(geoDistanceQuery);
//				queryList.add(subBoolQuery);
//
//				boolQuery.setShouldQueries(queryList);
//				boolQuery.setMinimumShouldMatch(1);
//
//				return boolQuery;
//			}
//		}
//		return null;
//	}
//
//
//	private void matchNearbyMaster(MasterMatchCondition masterMatchCondition,List<Query> mustQueryList,Double distance){
//		if(StringUtils.isNotBlank(masterMatchCondition.getOrderLngLat())){
//			String[] lngLatArray = masterMatchCondition.getOrderLngLat().split(",");
//			if(lngLatArray.length == 2){
//				//按照筛选取有经纬度的师傅
//				GeoDistanceQuery geoDistanceQuery = new GeoDistanceQuery();
//				geoDistanceQuery.setFieldName("lat_lng");
//				//设置中心点
//				geoDistanceQuery.setCenterPoint(lngLatArray[1] + "," + lngLatArray[0]);
//				//设置条件为到中心点的距离不超过10000米
//				geoDistanceQuery.setDistanceInMeter(distance * 1000);
//				mustQueryList.add(geoDistanceQuery);
//			}
//
//		}
//
//	}
//
//
//	public Set<String> searchNearbyMaster(MasterMatchCondition masterMatchCondition,String accountType,Long accountId,Long userId) {
//		SearchQuery searchQuery = new SearchQuery();
//		BoolQuery boolQuery = new BoolQuery();
//		List<Query> mustQueryList = new ArrayList<>();
//
//		matchNearbyMaster(masterMatchCondition,mustQueryList,masterMatchCondition.getNearbyDistance().doubleValue());
//
//
////		mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_BLACK_LIST_STATUS_NORMAL, true));
//
//		mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_ACCOUNT_NORMAL, true));
//
//		long freezingRecoverTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
//
//		mustQueryList.add(tableStoreClient.longRangeQuery(FieldConstant.FREEZING_TIME, 0L,freezingRecoverTime));
//
//
//		mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_SETTLE_STATUS_NORMAL, true));
//
//		mustQueryList.add(tableStoreClient.longCompareQuery(FieldConstant.LAST_ACTIVE_TIME,RangeQueryType.GREATER_THAN ,DateFormatterUtil.getAssignDayTimeStamp(-7)));
//
//		mustQueryList.add(tableStoreClient.longTermQuery(FieldConstant.REST_STATE, 1L));
//
//		mustQueryList.add(tableStoreClient.booleanTermQuery(FieldConstant.IS_RULE_EXAM_STATUS_NORMAL, true));
//
//		mustQueryList.add(tableStoreClient.stringTermQuery(FieldConstant.SERVE_DIVISION_IDS, String.valueOf(masterMatchCondition.getThirdDivisionId())));
//
//
//		//功能限制-禁止推单||禁止登录
//		setPushRestrictQuery(mustQueryList);
//
//
//		List<Query> mustNotQueryList=new ArrayList<>();
//		if(StringUtils.isNotBlank(masterMatchCondition.getServeIds())){
//			setBaseNotQuery(mustNotQueryList,masterMatchCondition);
//		}
//
//		//禁止推单的业务线
//		this.setNotQueryBusinessLine(mustNotQueryList,masterMatchCondition.getBusinessLineId());
//
//		boolQuery.setMustQueries(mustQueryList);
//		boolQuery.setMustNotQueries(mustNotQueryList);
//		searchQuery.setQuery(boolQuery);
//
//		Long startTime = System.currentTimeMillis();
//		searchQuery.setLimit(baseSelectLimit);
//		SearchRequest searchRequest = new SearchRequest(T_MASTER_BASE, baseSelectIndex, searchQuery);
//		SearchResponse resp = tableStoreClient.getSyncClient().search(searchRequest);
//		Long endTime = System.currentTimeMillis();
//
//		log.info("订单:{} ,searchMasterByBaseSelectStrategyIndex take times:{}",masterMatchCondition.getMasterOrderId(),(endTime - startTime));
//
//
//		// 可检查NextToken是否为空，若不为空，可通过NextToken继续读取。
//		List<Row> rows = resp.getRows();
//		while(resp.getNextToken() != null){
//			//把token设置到下一次请求中
//			searchRequest.getSearchQuery().setToken(resp.getNextToken());
//			resp = tableStoreClient.getSyncClient().search(searchRequest);
//			if (!resp.isAllSuccess()){
//				throw new RuntimeException("not all success");
//			}
//			rows.addAll(resp.getRows());
//			if(rows.size() > nearbyPushLimitNum){
//				break;
//			}
//		}
//		HashSet<String> masterIds=new HashSet<>();
//		for (Row row : rows) {
//			if (row!=null) {
//				masterIds.add(row.getPrimaryKey().getPrimaryKeyColumn("master_id").getValue().asString());
//			}
//		}
//
//		if(FieldConstant.ENTERPRISE.equals(accountType)){
//
//			Set<String> blacklistMasterSet = enterpriseBlackListFilter(String.valueOf(accountId),masterIds);
//			if(CollectionUtils.isNotEmpty(blacklistMasterSet)){
//				masterIds.removeAll(blacklistMasterSet);
//			}
//
//			if(userId != null){
//				Set<String> userBlacklistMasterSet = userBlackListFilter(String.valueOf(userId),masterIds);
//				if(CollectionUtils.isNotEmpty(userBlacklistMasterSet)){
//					masterIds.removeAll(userBlacklistMasterSet);
//				}
//			}
//		}
//
//		return masterIds;
//	}
//
//
//	private final static String userBlackListTable="t_master_user";
//	private final static String userBlackListTimeCol="user_last_blacklist_time";
//	public Set<String> userBlackListFilter(String userId,Set<String> masterIdsSet){
//		HashSet<String> result = new HashSet<>();
//		try {
//			final List<BatchGetRowResponse.RowResult> userRows = tableStoreClient.batchQueryBySetWithTwoPKWithCols(
//					userBlackListTable,
//					masterIdsSet,
//					FieldConstant.MASTER_ID,
//					FieldConstant.USER_ID,
//					userId,
//					userBlackListTimeCol);
//			for (BatchGetRowResponse.RowResult userRow : userRows) {
//				final Row row = userRow.getRow();
//				if (row!=null) {
//					final String value = tableStoreClient.getValue(row, userBlackListTimeCol);
//					if (isBlackListEffective(value)) {
//						result.add(row.getPrimaryKey().getPrimaryKeyColumn(FieldConstant.MASTER_ID)
//								.getValue().asString());
//					}
//				}
//			}
//		} catch (Exception e) {
//			log.error("userBlackListFilter failed:{}",e);
//		}
//		return result;
//	}
//
//
//	private final static String enterpriseBlackListTable="t_master_enterprise";
//	private final static String enterpriseBlackListTimeCol="enterprise_last_blacklist_time";
//	public Set<String> enterpriseBlackListFilter(String enterpriseId,Set<String> masterIdsSet){
//		HashSet<String> result = new HashSet<>();
//		try {
//			final List<BatchGetRowResponse.RowResult> enterpriseRows = tableStoreClient.batchQueryBySetWithTwoPKWithCols(
//					enterpriseBlackListTable,
//					masterIdsSet,
//					FieldConstant.MASTER_ID,
//					FieldConstant.ENTERPRISE_ID,
//					enterpriseId,
//					enterpriseBlackListTimeCol);
//			for (BatchGetRowResponse.RowResult enterpriseRow : enterpriseRows) {
//				final Row row = enterpriseRow.getRow();
//				if (row!=null) {
//					final String value = tableStoreClient.getValue(row, enterpriseBlackListTimeCol);
//					if (isBlackListEffective(value)) {
//						result.add(row.getPrimaryKey().getPrimaryKeyColumn(FieldConstant.MASTER_ID)
//								.getValue().asString());
//					}
//				}
//			}
//		} catch (Exception e) {
//			log.warn("enterpriseBlackListFilter failed:{}",e);
//		}
//		return result;
//	}
//
//
//	private boolean isBlackListEffective(String time){
//		if (StringUtils.isBlank(time) || "unspecified".equals(time)) {
//			return false;
//		}
//		return true;
//	};
//
//
//	private String ORDER_PUSH = "order_push";
//
//	public void filterPushedMaster(String masterOrderId, Set<String> masterIdsSet, String pushMode) {
//
//		try{
//			List<Row> pushedList=tableStoreClient.getRangeRowByDoublePK(ORDER_PUSH,FieldConstant.MASTER_ORDER_ID,
//					FieldConstant.PUSH_TIME,masterOrderId,masterOrderId,null,null);
//			StringJoiner sj = new StringJoiner(",");
//			for (Row row : pushedList) {
//				if (row!=null) {
//					String masterList=tableStoreClient.getValue(row, FieldConstant.MASTER_ID_LIST);
//					sj.add(masterList);
//				}
//			}
//			String masterListString = sj.toString();
//			List<String> pushedMasterList = Arrays.asList(masterListString.split(","));
//			masterIdsSet.removeAll(pushedMasterList);
//		} catch (Exception e) {
//		    log.error("过滤已推送师傅失败",e);
//		}
//	}
//
//
//	private final static String LAT_LNG = "lat_lng";
//	private final static String MASTER_TECHNIQUE_IDS = "master_technique_ids";
//	public Map<String, MasterBase> searchMasterList( Set<String> masterIdsSet){
//		Map<String,MasterBase> result = new HashMap<>();
//		try {
//			final List<BatchGetRowResponse.RowResult> rowResultList = tableStoreClient.batchQueryByList(
//					T_MASTER_BASE,
//					masterIdsSet,
//					FieldConstant.MASTER_ID,
//					LAT_LNG,
//					MASTER_TECHNIQUE_IDS);
//			for (BatchGetRowResponse.RowResult rowResult : rowResultList) {
//				final Row row = rowResult.getRow();
//				if (row!=null) {
//					final String masterLngLat = tableStoreClient.getValue(row, LAT_LNG);
//					final String masterTechniqueIds = tableStoreClient.getValue(row, MASTER_TECHNIQUE_IDS);
//					final String masterId = row.getPrimaryKey()
//							.getPrimaryKeyColumn(FieldConstant.MASTER_ID).getValue().asString();
//					MasterBase masterBase = new MasterBase();
//					masterBase.setMasterId(masterId);
//					masterBase.setMasterLngLat(masterLngLat);
//					masterBase.setMasterTechniqueIds(masterTechniqueIds);
//					result.put(masterId,masterBase);
//				}
//			}
//		} catch (Exception e) {
//			log.error("userBlackListFilter failed:{}",e);
//		}
//		return result;
//	}
//
//
//
//
//	public static void main(String[] args){
//		String test = "not_settled,exam_not_pass,not_active_gt_30,not_working,account_freeze,account_status_abnormal,black_list";
//		Arrays.stream(test.split(",")).forEach(e -> System.out.println(MasterStatusType.ACCOUNT_FREEZE.code.equals(e)));
//	}
//
//
//}
