package com.wanshifu.master.order.push.domain.annotation;

import java.lang.annotation.*;

/**
 * Title
 * Author x<PERSON><PERSON><PERSON>ian<PERSON>@wshifu.com
 * Time 2019/4/11
 */

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface Document {

    String indexAlias();

    String type();

    /**
     * 版本号为空，默认使用indexAlias作为indexName
     */
    String version() default "";

    int shards() default 5;

    int replicas() default 1;

    /**
     * true:在程序启动后自动创建索引和映射
     * false:禁止自动创建
     */
    boolean createIndex() default false;

    /**
     * true:直接使用属性名作为es字段名
     * false:将属性名转换成下划线形式作为es字段名
     */
    boolean useDefaultName() default true;
}
