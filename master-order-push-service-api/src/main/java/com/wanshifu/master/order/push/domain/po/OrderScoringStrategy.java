package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * 订单调度策略PO类
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "order_scoring_strategy")
public class OrderScoringStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Long businessLineId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;


    @Column(name = "master_resources")
    private String masterResources;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 适用类目id
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 调度策略
     */
    @Column(name = "scoring_strategy")
    private String scoringStrategy;


    /**
     * 调度策略表达式
     */
    @Column(name = "scoring_strategy_expression")
    private String scoringStrategyExpression;

    /**
     * 策略状态
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
