package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 协议师傅标签
 * <AUTHOR>
 */
public enum AgreementMasterLabel {

    /**
     * 新合约
     */
    NEW_CONTRACT("new_contract","新合约"),

    /**
     * 品牌
     */
    BRAND("brand","品牌"),


    /**
     * 样板师傅
     */
    EXCLUSIVE_SAMPLE_PLATE("exclusive_sample_plate","样板师傅"),


    /**
     * 其他
     */
    OTHER("other","其他");

    private final String code;
    private final String name;

    AgreementMasterLabel(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, AgreementMasterLabel> valueMapping = new HashMap<>((int) (AgreementMasterLabel.values().length / 0.75));

    static {
        for (AgreementMasterLabel instance : AgreementMasterLabel.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static AgreementMasterLabel asValue(String value) {
        return valueMapping.get(value);
    }


}
