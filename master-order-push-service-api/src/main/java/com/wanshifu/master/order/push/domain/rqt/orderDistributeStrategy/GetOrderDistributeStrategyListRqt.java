package com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 查询订单分片策略列表请求实体类
 * <AUTHOR>
 */
@Data
public class GetOrderDistributeStrategyListRqt {


    /**
     * 调度类型：auto_receive: 自动接单
     */
    private String distributeType;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 类目id
     */
    private String categoryIds;

    /**
     * 城市id
     */
    private Long cityId;


    private List<Long> categoryIdList;

    private Integer pageNum = 1;

    private Integer pageSize = 20;

    /**
     * 策略状态，1：已启用，0：已禁用
     */
    private Integer strategyStatus;


    public static final class GetOrderDistributeStrategyListRqtBuilder {
        private Integer businessLineId;
        private String strategyName;
        private Date createStartTime;
        private Date createEndTime;
        private String categoryIds;
        private Long cityId;
        private List<Long> categoryIdList;
        private Integer pageNum = 1;
        private Integer pageSize = 20;
        private Integer strategyStatus;
        private String distributeType;

        private GetOrderDistributeStrategyListRqtBuilder() {
        }

        public static GetOrderDistributeStrategyListRqtBuilder aGetOrderDistributeStrategyListRqt() {
            return new GetOrderDistributeStrategyListRqtBuilder();
        }

        public GetOrderDistributeStrategyListRqtBuilder withBusinessLineId(Integer businessLineId) {
            this.businessLineId = businessLineId;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withStrategyName(String strategyName) {
            this.strategyName = strategyName;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withCreateStartTime(Date createStartTime) {
            this.createStartTime = createStartTime;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withCreateEndTime(Date createEndTime) {
            this.createEndTime = createEndTime;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withCategoryIds(String categoryIds) {
            this.categoryIds = categoryIds;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withCityId(Long cityId) {
            this.cityId = cityId;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withCategoryIdList(List<Long> categoryIdList) {
            this.categoryIdList = categoryIdList;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withPageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withDistributeType(String distributeType) {
            this.distributeType = distributeType;
            return this;
        }

        public GetOrderDistributeStrategyListRqtBuilder withStrategyStatus(Integer strategyStatus) {
            this.strategyStatus = strategyStatus;
            return this;
        }

        public GetOrderDistributeStrategyListRqt build() {
            GetOrderDistributeStrategyListRqt getOrderDistributeStrategyListRqt = new GetOrderDistributeStrategyListRqt();
            getOrderDistributeStrategyListRqt.setBusinessLineId(businessLineId);
            getOrderDistributeStrategyListRqt.setStrategyName(strategyName);
            getOrderDistributeStrategyListRqt.setCreateStartTime(createStartTime);
            getOrderDistributeStrategyListRqt.setCreateEndTime(createEndTime);
            getOrderDistributeStrategyListRqt.setCategoryIds(categoryIds);
            getOrderDistributeStrategyListRqt.setCityId(cityId);
            getOrderDistributeStrategyListRqt.setCategoryIdList(categoryIdList);
            getOrderDistributeStrategyListRqt.setPageNum(pageNum);
            getOrderDistributeStrategyListRqt.setPageSize(pageSize);
            getOrderDistributeStrategyListRqt.setStrategyStatus(strategyStatus);
            getOrderDistributeStrategyListRqt.setDistributeType(distributeType);
            return getOrderDistributeStrategyListRqt;
        }
    }
}
