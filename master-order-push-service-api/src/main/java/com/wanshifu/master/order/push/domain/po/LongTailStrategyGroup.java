package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 长尾策略表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "long_tail_strategy_group")
public class LongTailStrategyGroup {

    /**
     * 策略组id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "long_tail_strategy_group_id")
    private Long longTailStrategyGroupId;

    /**
     * 策略组名称
     */
    @Column(name = "long_tail_strategy_group_name")
    private String longTailStrategyGroupName;

    /**
     * 策略组描述
     */
    @Column(name = "long_tail_strategy_group_desc")
    private String longTailStrategyGroupDesc;


    /**
     * 推送类型：nearby_more, bonus_order
     */
    @Column(name = "push_type")
    private String pushType;


    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 城市模式
     */
    @Column(name = "open_city_mode")
    private String openCityMode;

    /**
     * 城市ID，多个以逗号拼接
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 策略配置JSON
     */
    @Column(name = "strategy_json")
    private String strategyJson;

    /**
     * 策略表达式
     */
    @Column(name = "strategy_expression")
    private String strategyExpression;

    /**
     * 更新人
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建人
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 策略状态
     */
    @Column(name = "is_active")
    private Integer isActive;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}