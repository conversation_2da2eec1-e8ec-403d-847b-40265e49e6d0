package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 推单端口规则
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "push_port_rule")
public class PushPortRule {

    /**
     * 规则id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_id")
    private Integer ruleId;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;

    /**
     * 规则描述
     */
    @Column(name = "rule_desc")
    private String ruleDesc;

    /**
     * 应用城市集合
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 一级服务id集合
     */
    @Column(name = "lv_1_serve_ids")
    private String lv1ServeIds;


    /**
     * 订单标签
     */
    @Column(name = "order_tag")
    private String orderTag;

    /**
     * 指派模式
     */
    @Column(name = "appoint_type")
    private String appointType;


    /**
     * 端口间隔时间
     */
    @Column(name = "interval_time")
    private Integer intervalTime;


    /**
     * 端口报价人数
     */
    @Column(name = "offer_num")
    private Integer offerNum;


    /**
     * 规则状态
     */
    @Column(name = "rule_status")
    private Integer ruleStatus;


    /**
     * 删除状态，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;



    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


    /**
     * 推送规则
     */
    @Column(name = "push_rule")
    private String pushRule;

}