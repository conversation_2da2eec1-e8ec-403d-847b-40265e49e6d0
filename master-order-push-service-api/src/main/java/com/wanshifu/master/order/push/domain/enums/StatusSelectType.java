package com.wanshifu.master.order.push.domain.enums;

/**
 * 状态初筛类型
 * <AUTHOR>
 */
public enum StatusSelectType {

    /**
     * 师傅状态
     */
    MASTER_STATUS("master_status");

    public final String code;

    StatusSelectType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }

}
