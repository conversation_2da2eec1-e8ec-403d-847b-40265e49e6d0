package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "compensate_distribute")
public class CompensateDistribute {

    /**
     * 调度id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "distribute_id")
    private Integer distributeId;


    @Column(name = "strategy_name")
    private String strategyName;


    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 类目id集合
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 指派类型
     */
    @Column(name = "appoint_types")
    private String appointTypes;

    /**
     * 订单标签(订单推送标签)
     */
    @Column(name = "order_push_flag")
    private String orderPushFlag;

    /**
     * 是否有价格
     */
    @Column(name = "has_price")
    private Integer hasPrice;

    /**
     * 是否有合作用户
     */
    @Column(name = "has_cooperation_user")
    private Integer hasCooperationUser;

    /**
     * 补偿类型
     */
    @Column(name = "compensate_type")
    private String compensateType;

    /**
     * 间隔时间
     */
    @Column(name = "interval_time")
    private Integer intervalTime;

    /**
     * 触发人数
     */
    @Column(name = "trigger_num")
    private Integer triggerNum;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 修改人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

}
