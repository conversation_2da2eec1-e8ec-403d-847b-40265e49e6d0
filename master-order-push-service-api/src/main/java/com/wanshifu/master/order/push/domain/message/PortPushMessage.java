package com.wanshifu.master.order.push.domain.message;

import com.wanshifu.master.order.push.domain.dto.PortPushRuleDTO;
import com.wanshifu.master.order.push.domain.po.PushPortRule;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import lombok.Data;


/**
 * 端口推送mq消息实体类
 */
@Data
public class PortPushMessage {

    /**
     * 订单基础数据
     */
    private OrderDetailData orderDetailData;


    /**
     * 推送端口规则
     */
    private PushPortRule pushPortRule;

    /**
     * 推送端口规则
     */
    private PortPushRuleDTO portPushRuleDTO;
}
