//package com.wanshifu.master.order.push.service;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//import com.wshifu.bigdata.tablestore.sql.TableStore;
//import com.wshifu.bigdata.tablestore.sql.TableStoreSQLClient;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//
///**
// * 查询数据库工具类
// *
// * <AUTHOR>
// *
// */
//@Component
//@Slf4j
//public class TablestoreQuery {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TablestoreQuery.class);
//
//
//    @Value("${ots.accessKeyId}")
//    private String otsAccessKeyId;
//
//    @Value("${ots.accessKeySecret}")
//    private String otsAccessKeySecret;
//
//
//    @Value("${ots.instanceName}")
//    private String otsInstanceName;
//
//    @Value("${ots.endpoint}")
//    private String otsEndpoint;
//
//
//
//    @PostConstruct
//    public void initQueryClient(){
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("ak",otsAccessKeySecret);
//        jsonObject.put("aid",otsAccessKeyId);
//        jsonObject.put("database_name",otsInstanceName);
//        jsonObject.put("database_type","4");
//        jsonObject.put("endpoint",otsEndpoint);
//        jsonObject.put("assign_endpoint","");
//        TableStore.syncClient(jsonObject);
//
//    }
//
//
//    /**
//     * 根据特定格式的入参返回查询结果
//     *
//     * @param param 查询入参
//     * @return 查询结果
//     */
//    public JSONObject query(String param,String instanceName,String tableName) {
//
//        JSONObject resultObject = new JSONObject();
//
//        try {
//            JSONArray paramJSONArray = JSONArray.parseArray(param);
//            for (Object paramObj : paramJSONArray) {
//                JSONArray paramResultJSONArray = new JSONArray();
//                JSONObject paramJSON = JSONObject.parseObject(paramObj.toString());
//                Integer datasetId = paramJSON.getInteger("dataset_id");
//                String conditions = paramJSON.getString("conditions");
//                String pageSetting = paramJSON.getString("page_setting");
//                JSONArray dimensionColumnArray = JSONArray.parseArray(paramJSON.getString("dimension_columns"));
//                JSONArray indexColumnArray = JSONArray.parseArray(paramJSON.getString("index_columns"));
//
//
//                List<String> fieldolumnList = new ArrayList<>();
//                dimensionColumnArray.stream().forEach(dimensionColumnObj -> fieldolumnList.add(dimensionColumnObj.toString()));
//
//                indexColumnArray.stream().forEach(indexColumnObj -> fieldolumnList.add(indexColumnObj.toString()));
//
//
//                // 查询结果处理
//                JSONArray tmpResultJSONArray = new JSONArray();
//                tmpResultJSONArray = queryData(fieldolumnList,instanceName, tableName, conditions);
//
//                JSONObject paramResultJSON = new JSONObject();
//
//                // 获取入参对应的配置
//                int illegalResult = 0;
//
//                int tmpResultSize = tmpResultJSONArray.size();
//                if (tmpResultSize <= 0) {
//                    continue;
//                    // 指标字段都在同一张表中/指标字段不在同一张表中并且每个字段的结果记录只有一条
//                } else if (tmpResultSize == 1) {
//                    paramResultJSON.putAll(tmpResultJSONArray.getJSONObject(0));
//                } else {
//                    // 指标字段都在同一张表中/指标字段不在同一张表中并且可能有结果记录有多条（不兼容）
//                    for (Object resultObj : tmpResultJSONArray) {
//                        JSONObject tmpResultJSON = JSONObject.parseObject(resultObj.toString());
//                        paramResultJSONArray.add(tmpResultJSON);
//                    }
//                    illegalResult++;
//                }
//
//
//                // 返回结果处理
//                if (illegalResult > 1 || (paramResultJSONArray.size() > 0 && paramResultJSON.size() > 0)) {
//                    paramResultJSONArray = new JSONArray();
//                    LOGGER.warn("出现查询结果存在一对多或者多对多，param：[{}]", paramObj.toString());
//                } else if (paramResultJSON.size() > 0 && paramResultJSONArray.size() == 0) {
//                    // 指标字段不在同一张表中/并且每个字段的结果记录只有一条
//                    paramResultJSONArray.add(paramResultJSON);
//                }
//                resultObject.put(String.valueOf(datasetId), paramResultJSONArray);
//            }
//        } catch (Exception e) {
//            log.error("数据查询失败", e);
//        }
//        return resultObject;
//    }
//
//
//
//    /**
//     * 根据传入参数和存储配置查询结果
//     *
//     * @param queryFieldList 需要查询的字段集合
//     * @param conditions     查询条件
//     * @return 查询结果
//     * @throws Exception sql查询异常
//     */
//    private JSONArray queryData(List<String> queryFieldList, String instanceName,String tableName,
//                                String conditions) throws Exception {
//        // sql语句拼接
//        StringBuilder sqlBuilder = new StringBuilder("select ");
//        for (String fieldName : queryFieldList) {
//            sqlBuilder.append(fieldName);
//            sqlBuilder.append(",");
//            // 结果参数记录
//        }
//        sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
//        sqlBuilder.append(" from ");
//        sqlBuilder.append(tableName);
//        if (!StringUtils.isEmpty(conditions)) {
//            sqlBuilder.append(" where ");
//            sqlBuilder.append(conditions);
//        }
//        TableStoreSQLClient client = TableStoreSQLClient.getClient();
//        log.info("sqlSBuilder"+sqlBuilder.toString());
//        JSONArray resultJSONArray = client.excuteSQL(instanceName, sqlBuilder.toString());
//        log.info("resultJSONArray"+resultJSONArray.toString());
//        return resultJSONArray == null ? new JSONArray() : resultJSONArray;
//    }
//
////	public static void main(String[] args){
////
////		JSONObject originalJsonObject = new JSONObject();
////		originalJsonObject.put("ak","LjwGDSJYPIgwFYxDqQLzNZCfONBYNi");
////		originalJsonObject.put("aid","LTAIReop9geyS6Tp");
////		originalJsonObject.put("database_name","master-push-test");
////		originalJsonObject.put("database_type","4");
////		originalJsonObject.put("endpoint","https://master-push-test.cn-shenzhen.ots.aliyuncs.com");
////		originalJsonObject.put("assign_endpoint","");
////		TableStore.syncClient(originalJsonObject);
////
////		String sql = "select master_id,serve_lv3_id,serve_lv3_last_180d_complete_cnt from mst_serve_lv3_stat where master_id = 42  and serve_lv3_id  = 400342";
//////		sql = "select user_id,is_price_sensitive from usr_index_stat where user_id = 4739977434";
////		sql = "select master_id,serve_level_3_id,last_180d_not_like_cnt from mst_serve_lv3_stat where serve_lv3_id = '4003131' and master_id in (3740434537,4740729459,3825547694,5078068759,5073594402,59152,133791346,5279318014,5279318017,5279318138,5169563603,4957789473,5152913198,5279318011,5279318131,5279318134,4957972030,4958129004,5078068723,3779169557,5078086151,5167587925,6097110277,5021608656,5160954546,5097035436,5073596929,5279318026,5279318147,5279318028,5279318149,4957939353,119800124,3742488444,61163691303,5279318141,4961600943,5233889784,5279318022,3742511040,4958330703,5189874664,4957497710,5279318145,3748064727,4961951116,61159742494,4958440647,22450467,3742581659,59055,59178,3735760864,5279318157,15639,5279318038,5279318159,3745403741,4739453389,12002,61190803841,14300158,5248777046,5279318030,5279318151,3803581788,5038906513,5279318032,5279318153,3742510087,5279318034,5279318155,4302515463,4957353395,39573589,4733948197,3736548541,3739298466,3745265335,23900368,5097031055,5151147634,61164518561,3733919183,6098412267,4731305953,5279318048,15252378,3803589472,5279318168,3736168442,5028932930,3844998821,5279318040,5279318161,5040015405,61170665832,5279318042,5279318163,5279318165,3808784295,5279318045,5078088564,4851716732,5279318170,4957502001,4740788891,3818987477,61170662670,3733901899,5248621889,5151146438,3804098035,5279318179,4186496760,5078056748,5177032702,5177093729,3742477650,3856659816,43266789,5279318174,5073598083,5279318052,5279318055,5279318057,5279318177,3743497458,43730,5024516909,4735892304,5131600912,4957769467,5028933879,5073598636,5073594398,3823159060,5177104191,61159785576,17871578,27394789,5030756299,3739435353,4847823840,3863614110,4957492860,5073598860,14931347,113431278,1600873479,3733921700,5078071542,3735769873,4962728758,1591451279,5152908223,3735870474,5033633606,4736250761,5221231776,5078059635,33371346,3746226092,1592831579,5167470843,3870298705,61165493026,24360149,3918859843,4733954831,4957901231,18360368,3740125171,4733948276,4958147749,5021414860,5073598735,6011417472,4737237891,4747537438,5246634710,3736662578,4961938697,3742764279,5097029694,5150175724,3908497818,3745250672,3736167038,6099227528,5257718321,6099409583,61164540011,4961906405,5218857431,16023567,61159803675,5073597993,5134041806,61011312954,61161512614,43704,61015537259,937710234,113152379,3746006203,5127655429,61127258538,4197710020,5156533316,3869124970,61115454930,3734148558,61159679481,1541210489,4957948225,3742977629,5097031110,59200,3733950080,4747539632,5073599800,5276097501,4847561583,5097030015,5167468317,61165637751,59203,5030749965,59205,4957772433,4958141020,3734137016,3736682686,4958437876,3914378695,61170669322,3736200731,3783673692,3911532476,5100521147,28532359,5176947310,3749366680,3736241201,61163691493,4740802616,5126748993,61159742529,4848098805,4363515218,61163691256,5023713777,3904398192,4961972867,3733909891,5165843369,25650589,3736766308,4738203572,4961599854,58011,4851198277,59229,5279318201,58018,27731258,4848528682,59104,5279318205,6090020893,61163692132,1572354579,4961724075,5152917121,4957759359,3774732065,3863516292,4740037174,4740404898,4958151061,5078072813,61160587163,5279318209,3733922973,5169564740,3770447513,58020,5279318213,3733918481,28302568,61163691271,5177348288,5279318216,58025,3917614510,4957995971,3735825333,4957775956,5169537381,3807571084,4733952850) ";
////		TableStoreSQLClient client = TableStoreSQLClient.getClient();
////		JSONArray resultJSONArray = client.excuteSQL("master-push-test", sql);
////		log.info("resultJSONArray"+resultJSONArray.toString());
////	}
//
//
//
//
//}
