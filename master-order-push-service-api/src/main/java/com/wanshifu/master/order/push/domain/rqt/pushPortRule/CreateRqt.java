package com.wanshifu.master.order.push.domain.rqt.pushPortRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.dto.PortPushRuleDTO;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.Column;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 描述 :  推单端口规则
 * <AUTHOR> <EMAIL>
 * @date : 2025-02-12 15:21
 */
@Data
public class CreateRqt {

    /**
     * 规则名称
     */
    @NotEmpty
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;


    /**
     * 应用城市id集合
     */
    private String cityIds;

    /**
     * 一级服务id集合
     */
    private String lv1ServeIds;


    /**
     * 指派模式
     */
    private String appointType;


    /**
     * 订单标签
     */
    private String orderTag;


    /**
     * 端口间隔时间
     */
    private Integer intervalTime;


    /**
     * 端口报价人数
     */
    private Integer offerNum;

    /**
     * 创建人id
     */
    private Long createAccountId;



    @NotEmpty
    @Valid
    private List<PortPushRuleDTO> ruleList;
    





}