package com.wanshifu.master.order.push.domain.rqt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.service.PolarDBService;
import com.wanshifu.master.order.push.util.SpringContextUtil;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单详细信息
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderDetailData {

    /**
     * 师傅订单ID
     */
    private Long masterOrderId;

    /**
     * 全局订单ID
     */
    private Long globalOrderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 二级地址ID
     */
    private Long secondDivisionId;

    /**
     * 三级地址ID
     */
    private Long thirdDivisionId;

    /**
     * 四级地址ID
     */
    private Long fourthDivisionId;

    /**
     * 商品父级ID
     */
    private String parentGoodsIds;

    /**
     * 商品ID集合
     */
    private Set<String> parentGoodsIdsSet = new HashSet<String>();

    /**
     * 子级商品ID
     */
    private String childGoodsIds;

    /**
     * 子级商品ID集合
     */
    private Set<String> childGoodsIdsSet;

    /**
     * 商品数量
     */
    private int goodsNum = 0;

    /**
     * 一级服务ID
     */
    private String lv1ServeIds;


    /**
     * 二级服务ID
     */
    private String lv2ServeIds;

    /**
     * 三级服务ID集合
     */
    private List<Long> lv2ServeIdList;

    /**
     * 三级服务ID
     */
    private String lv3ServeIds;

    /**
     * 三级服务ID集合
     */
    private List<Long> lv3ServeIdList;

    /**
     * 订单所需师傅技能
     */
    private String orderTechniques;

    /**
     *     订单所需师傅技能集合
     */
    private Set<String> orderTechniqueSet = new HashSet<>();

    /**
     * 订单类目id
     */
    private Long orderCategoryId;
    // ID和之前不一样了 1:送货到楼下,2:送货到家,3:送货到家并安装,4:安装,5:维修,6:返货,7:保养,8:清洗,9:测量
    // 配送订单-可接单师傅数等级-对订单类别熟悉程度

    /**
     * 订单服务类型
     */
    private Integer orderServeType;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 订单标签
     */
    private String orderLabel;

    /**
     * 订单期望完成时间
     */
    private String expectCompleteTime;

    /**
     * 订单推单版本号
     */
    private String orderVersion;

    /**
     * 订单期望开始上门时间
     */
    private String expectDoorInStartDate;

    /**
     * 订单期望开始上门时间（字符串）
     */
    private String expectDoorInStartTimeString;

    /**
     * 订单期望结束上门时间
     */
    private String expectDoorInEndTimeString;

    /**
     * 关联原测量订单全局订单id
     */
    private String relaMeasureGlobalOrderId;

    /**
     * 优先原测量师傅
     */
    private Integer hasPriorityPushMeasureMaster;


    /**
     * 加急单标识：
     */
    private Integer emergencyOrderFlag;


    /**
     * 准时单标识 0、普通订单 1、准时单
     */
    private Integer onTimeOrderFlag;

    /**
     * 时间标记
     */
    private Integer timerFlag;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 指派类型
     */
    private Integer appointType;

    /**
     * 楼层数
     */
    private String floorNum;

    /**
     * 是否有电梯
     */
    private String hasLift;

    /**
     * 订单经纬度
     */
    private String orderLngLat;


    /**
     * 订单经纬度
     */
    private String pickupAddressLngLat;

    /**
     * 客户手机号码
     */
    private String customerPhone;


    private Integer isExclusiveTeamMaster;


    private Integer isSendBackOld;

    private String sendBackOldPayMode;


    private String demolishType;

    /**
     * 是否需师傅带配件,1:是,0:否
     */
    private Integer isParts;

    private String attributeKeys;
    private JSONObject attributeValue;
    private Set<String> attributeSet;

    private BigDecimal definiteServeFee;

    /**
     * 一口价订单是否已支付
     */
    private Integer orderPayStatus;


    private Set<String> expectPackageSet=new HashSet<>();;


    private List<String> orderAttachedGoods;

    private JSONArray orderAncillaryGoods;

    private String tagName;

    private String rePushSource;

    private Integer cancelAppoint;

    private String serviceIds;

    private String customerAddress;

    private Long enterpriseOrderId;

    private Date orderCreateTime;


    private List<String> orderTags;


    public void setAttributes(Set<String> goodsAttributes) {
        this.attributeSet = goodsAttributes;
        this.attributeKeys = StringUtils.trimToNull(StringUtils.join(goodsAttributes, SymbolConstant.COMMA));
    }


    /**
     * 推单扩展数据
     */
    private PushExtraData pushExtraData = new PushExtraData();


    public Set<String> getExpectPackageSet() {
        return expectPackageSet;
    }

    public void setExpectPackageSet(JSONArray expectPackageSet) {
        if (expectPackageSet==null) {
            return;
        }
        for (int i = 0; i < expectPackageSet.size(); i++) {
            String packageId=expectPackageSet.getString(i);
            this.expectPackageSet.add(packageId);
        }
    }



    /**
     * 通过消息体解析订单商品信息
     *
     * @return
     */
    public boolean setOrderGoodsInfo(List<OrderGoodsInfo> orderGoodsInfoList) {
        if (CollectionUtils.isEmpty(orderGoodsInfoList)) {
            return false;
        }
        Set<String> goodsCategorys = new HashSet<>();
        Set<String> goodsChilds = new HashSet<>();
        int goodsNum = 0;
        for (OrderGoodsInfo orderGoodsInfo : orderGoodsInfoList) {
            goodsCategorys.add(orderGoodsInfo.getGoodsCategory());
            goodsChilds.add(String.valueOf(orderGoodsInfo.getGoodsCategory()));
            int currentGoodsNum = 0;
            try {
                currentGoodsNum = orderGoodsInfo.getNumber();
            } catch (Exception e) {
            }
            goodsNum = goodsNum + currentGoodsNum;
        }
        if (!CollectionUtils.isEmpty(goodsCategorys)) {
            setParentGoods(goodsCategorys);
        }
        if (CollectionUtils.isNotEmpty(goodsChilds)) {
            setChildGoods(goodsChilds);
        }
        setGoodsNum(goodsNum);
        return true;
    }

    public OrderDetailData(){

    }


    public void setParentGoods(Set<String> parentGoods) {
        this.parentGoodsIdsSet = parentGoods;
        this.parentGoodsIds = StringUtils.trimToNull(StringUtils.join(parentGoods, SymbolConstant.COMMA));
    }

    public void setChildGoods(Set<String> childGoods) {
        this.childGoodsIdsSet = childGoods;
        this.childGoodsIds = StringUtils.trimToNull(StringUtils.join(childGoods, SymbolConstant.COMMA));
    }


    public void setLv2ServeIds(String lv2ServeIds) {
        if(StringUtils.isNotBlank(lv2ServeIds)){
            this.lv2ServeIds = lv2ServeIds;
            this.lv2ServeIdList = Stream.of(lv2ServeIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

    }

    public void setLv3ServeIds(String lv3ServeIds) {
        if(StringUtils.isNotBlank(lv3ServeIds)){
            this.lv3ServeIds = lv3ServeIds;
            this.lv3ServeIdList = Stream.of(lv3ServeIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

    }

    private boolean checkOrderData(OrderPushRqt orderPushRqt){
        return !(StringUtils.isBlank(orderPushRqt.getBindingTechnologyIds()) || orderPushRqt.getSecondDivisionId() == null ||
                orderPushRqt.getThirdDivisionId() == null);
    }

    public OrderDetailData(OrderPushRqt orderPushRqt, String orderVersion) {
        boolean initByPoralDB = false;
        if (!checkOrderData(orderPushRqt)) {
            //推单数据不完整，查询离线库PolarDB初始化推单数据
            initByPoralDB = true;
        } else {
            try {
                this.masterOrderId = orderPushRqt.getMasterOrderId();
                this.globalOrderId = orderPushRqt.getGlobalOrderId();
                this.businessLineId = orderPushRqt.getBusinessLineId();
                this.orderVersion = orderVersion;
                this.accountType = orderPushRqt.getAccountType();
                this.accountId = orderPushRqt.getAccountId();
                this.thirdDivisionId  = orderPushRqt.getThirdDivisionId();
                this.fourthDivisionId = orderPushRqt.getFourthDivisionId();
                this.setOrderGoodsInfo(JSONArray.parseArray(orderPushRqt.getOrderGoods(), OrderGoodsInfo.class));
                this.lv1ServeIds = orderPushRqt.getLv1ServeIds();
                this.setLv2ServeIds(orderPushRqt.getLv2ServeIds());
                this.setLv3ServeIds(orderPushRqt.getLv3ServeIds());
                this.orderTechniques = orderPushRqt.getBindingTechnologyIds();
                Collections.addAll(this.orderTechniqueSet, orderPushRqt.getBindingTechnologyIds().split("\\|"));
                this.orderCategoryId = orderPushRqt.getCategoryId();
                this.orderServeType = orderPushRqt.getServeType();
                this.orderFrom = orderPushRqt.getOrderFrom();
                this.setOrderLabel(orderPushRqt.getOrderLabel());
                this.expectCompleteTime =orderPushRqt.getExpectCompleteTime();
                if (StringUtils.isNotBlank(orderPushRqt.getOrderLngLat())) {
                    OrderLngLat orderLngLat = JSON.parseObject(orderPushRqt.getOrderLngLat(), OrderLngLat.class);
                    this.orderLngLat = orderLngLat.getBuyerAddressLongitude() + "," +orderLngLat.getBuyerAddressLatitude();
                }
                this.pushExtraData.setOrderPushExcludeMasterIds(orderPushRqt.getOrderPushExcludeMasterIds());
                this.pushExtraData.setOrderPushEliminateMasterIds(orderPushRqt.getOrderPushEliminateMasterIds());
                this.pushExtraData.setTeamMasterOrderPush(orderPushRqt.getTeamMasterOrderPush());
                String expectDoorInStartDateSource = org.apache.commons.lang3.StringUtils.trimToNull(orderPushRqt.getExpectDoorInStartDate());
                if (org.apache.commons.lang.StringUtils.isNotBlank(expectDoorInStartDateSource)) {
                    this.expectDoorInStartTimeString = expectDoorInStartDateSource;
                    String expectDoorInStartDate = DateFormatterUtil.miToDayv2(expectDoorInStartDateSource);
                    this.expectDoorInStartDate = expectDoorInStartDate;
                }
                if (org.apache.commons.lang.StringUtils.isNotBlank(orderPushRqt.getExpectDoorInEndDate())) {
                    this.expectDoorInEndTimeString = orderPushRqt.getExpectDoorInEndDate();
                }
                this.relaMeasureGlobalOrderId = orderPushRqt.getMeasureGlobalOrderTraceId();
                this.hasPriorityPushMeasureMaster = orderPushRqt.getPriorityPushMeasureMaster();
                this.timerFlag = orderPushRqt.getTimerFlag();
                this.emergencyOrderFlag = orderPushRqt.getEmergencyOrderFlag();
                this.onTimeOrderFlag = orderPushRqt.getOnTimeOrderFlag();
                this.userId = orderPushRqt.getUserId();
                this.appointType = orderPushRqt.getAppointType();

                this.pushExtraData.setRecruitId(orderPushRqt.getRecruitId());
                this.pushExtraData.setHasPrice(orderPushRqt.getHasPrice());
                this.secondDivisionId = orderPushRqt.getSecondDivisionId();
                this.customerPhone = orderPushRqt.getCustomerPhone();
                this.isExclusiveTeamMaster = orderPushRqt.getIsExclusiveTeamMaster();

                parseTecByOrderBase();

            } catch (Exception e) {
                log.error("初始化订单数据失败，orderPushRqt：{}",JSON.toJSONString(orderPushRqt));
                initByPoralDB = true;
            }
        }


        if (initByPoralDB) {
            log.info("订单数据不完整，查询离线库订单数据");
            constructByPolar(orderPushRqt.getMasterOrderId(), orderPushRqt.getBusinessLineId());
        }
    }

    private void constructByPolar(Long masterOrderId, Integer businessLineId) {
        this.masterOrderId = masterOrderId;
        this.businessLineId = businessLineId;
        // order base 技能 地区不能为空
        PolarDBService polarDBService = SpringContextUtil.getBean(PolarDBService.class);
        boolean orderBaseResult = polarDBService.setOrderBaseInfo(masterOrderId, businessLineId, this);
        if (!orderBaseResult) {
            return;
        }

        parseTecByOrderBase();
        if ((!"ikea".equals(getOrderFrom()))&&orderTechniqueSet.size()<=0) {
            return;
        }
        //....................................................


        // order goods
        polarDBService.setOrderGoodsInfo(masterOrderId, businessLineId, this);
        // expect time -- extra
        polarDBService.setOrderExpectCompleteTime(masterOrderId, businessLineId, this);
        //apoint type
        polarDBService.setOrderAppointType(masterOrderId, businessLineId, this);
        //lv2 serveIds

    };


    private void parseTecByOrderBase() {
        if (this.orderTechniques != null) {
//			if (getBussinessId()==3) {
//				tmpParseTec();
//			}else {
            Collections.addAll(this.orderTechniqueSet,orderTechniques.split("\\|"));
//			}
        }
    }


    public String getOrderPushExcludeMasterIds(){
        return pushExtraData.getOrderPushExcludeMasterIds();
    }

    public List<String> parseOrderPushEliminateMasterIds() {
        List<Long> orderPushEliminateMasterIds = pushExtraData.getOrderPushEliminateMasterIds();
        if (CollectionUtils.isNotEmpty(orderPushEliminateMasterIds)) {
            return orderPushEliminateMasterIds.stream().map(String::valueOf).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public Long getRecruitId(){
        return pushExtraData.getRecruitId();
    }

    public Boolean getHasPrice(){
        return pushExtraData.getHasPrice();
    }

    public Integer getTeamMasterOrderPush(){
        return pushExtraData.getTeamMasterOrderPush();
    }


}
