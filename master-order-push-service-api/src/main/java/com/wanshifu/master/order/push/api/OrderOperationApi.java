package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.domain.common.DefaultDecoder;
import com.wanshifu.master.order.push.domain.rqt.NotifyOrderComeInRqt;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "master-inner-api",
        url = "${wanshifu.master-inner-api.url}",
        path = "/order/operate",
        configuration = {DefaultEncoder.class, DefaultDecoder.class, DefaultErrorDecode.class})
public interface OrderOperationApi {

    @PostMapping("notifyOrderComeIn")
    String notifyOrderComeIn(@RequestBody @Validated NotifyOrderComeInRqt rqt);


}