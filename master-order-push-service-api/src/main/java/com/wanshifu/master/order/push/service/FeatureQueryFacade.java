package com.wanshifu.master.order.push.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.BaseFeatureConfigDimensionList;
import com.wanshifu.master.order.push.domain.common.PushCommonObject;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.domain.po.ComplexFeature;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.BaseFeatureRepository;
import com.wanshifu.master.order.push.repository.ComplexFeatureRepository;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.JSONUtil;
import com.wanshifu.master.order.push.util.VariableCastUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 特征数据Facade
 * <AUTHOR>
 */
@Component
@Slf4j
public class FeatureQueryFacade {

	@Resource
	private BaseFeatureRepository baseFeatureRepository;

	@Resource
	private ComplexFeatureRepository complexFeatureRepository;

	@Resource
	private FeatureQueryServiceImpl featureQueryServiceImpl;

	@Resource
	private QLExpressHandler qlExpressHandler;


	/**
	 * 查询订单特征
	 * @param pushCommonObject
	 * @param orderFeatureSet
	 * @return 推单特征
	 */
	public PushFeature getOrderFeatures(PushCommonObject pushCommonObject,Set<String> orderFeatureSet) {

		DefaultContext<String, Object> dimensionData = buildDimensionData(pushCommonObject.getOrderDetailData());
		Set<String> masterIdSet = pushCommonObject.getMasterSet();
		PushFeature feature = new PushFeature(pushCommonObject.getOrderDetailData().getGlobalOrderId(),masterIdSet);
		feature.addDimensionFeature(dimensionData);

		try{

			List<ComplexFeature> orderComplexFeatureList = complexFeatureRepository.selectByFeatureFor("order",orderFeatureSet);
			if(CollectionUtils.isNotEmpty(orderComplexFeatureList)){
				orderComplexFeatureList.forEach(complexFeature -> orderFeatureSet.addAll(Arrays.asList(complexFeature.getFeatureDependency().split(","))));
			}

			// 订单特征t
			List<BaseFeature> orderBaseFeatureList = baseFeatureRepository.selectByFeatureType("order",orderFeatureSet);
			Map<String,List<BaseFeature>> baseFeatureMap = orderBaseFeatureList.stream().collect(Collectors.groupingBy(baseFeature -> baseFeature.getDbType() + ";" + baseFeature.getTableName()));

			for(Map.Entry<String,List<BaseFeature>> entry: baseFeatureMap.entrySet()){
				Map<String, BaseFeatureConfigDimensionList>  orderBaseFeatureMap =  initBaseFeatureDimension(entry.getValue());
				queryOrderBaseFeature(feature,orderBaseFeatureMap,dimensionData);
			}

			// 计算订单特征
			calculateOrderComplexFeature(feature.getOrderFeature(),orderComplexFeatureList);
		}catch(Exception e){
			log.error(String.format("query orderFeature error,globalOrderId:%d,orderFeatureSet:%s",pushCommonObject.getGlobalOrderId(),orderFeatureSet),e);
		}

		return feature;
	}


	/**
	 * 查询师傅特征
	 *
	 * @param pushFeature
	 * @param masterIdSet
	 * @param masterFeatureSet
	 * @return
	 */
	public void getMasterFeatures(PushFeature pushFeature,Set<String> masterIdSet,Set<String> masterFeatureSet) {
		try{
			List<ComplexFeature> masterComplexFeatureList = complexFeatureRepository.selectByFeatureFor("master",masterFeatureSet);
			if(CollectionUtils.isNotEmpty(masterComplexFeatureList)){
				masterComplexFeatureList.forEach(complexFeature -> masterFeatureSet.addAll(Arrays.asList(complexFeature.getFeatureDependency().split(","))));
			}
			// 特征
			List<BaseFeature> masterBaseFeatureList = baseFeatureRepository.selectByFeatureType("master",masterFeatureSet);
			Map<String,List<BaseFeature>> baseFeatureMap = masterBaseFeatureList.stream().collect(Collectors.groupingBy(baseFeature -> baseFeature.getDbType() + ";" + baseFeature.getTableName()));

			for(Map.Entry<String,List<BaseFeature>> entry: baseFeatureMap.entrySet()){
				Map<String, BaseFeatureConfigDimensionList>  masterBaseFeatureMap =  initBaseFeatureDimension(entry.getValue());
				queryMasterBaseFeature(pushFeature,masterBaseFeatureMap,pushFeature.getDimensionFeature(),masterIdSet);
			}
			//计算师傅复杂特征
			calculateMasterComplexFeature(pushFeature,masterComplexFeatureList);
		}catch(Exception e){
			log.error(String.format("query master features error,masterIdSet:%s,masterFeatureSet:%s",masterIdSet,masterFeatureSet),e);
		}

	}


	public PushFeature buildPushFeature(OrderDetailData orderDetailData,Set<String> masterSet){
		DefaultContext<String, Object> dimensionData = buildDimensionData(orderDetailData);
		PushFeature feature = new PushFeature(orderDetailData.getGlobalOrderId(),masterSet);
		feature.addDimensionFeature(dimensionData);
		return feature;
	}



	/**
	 * 查询基础订单特征
	 *
	 * @param pushFeature
	 * @param featureQueryMap
	 * @param dimensionData
	 */
	private void queryOrderBaseFeature(PushFeature pushFeature, Map<String, BaseFeatureConfigDimensionList> featureQueryMap,
									   Map<String, Object> dimensionData) {
		for (Map.Entry<String, BaseFeatureConfigDimensionList> row : featureQueryMap.entrySet()) {
			// 当前维度
			String dimensionColumn = row.getKey();
			List<String> dimensionColumnList = Arrays.asList(dimensionColumn.split(","));
			BaseFeatureConfigDimensionList featureConfigRowList = row.getValue();
			// 查询
			JSONObject result = featureQueryServiceImpl.queryOrderFeature(dimensionColumnList,
					new HashSet<String>(featureConfigRowList.getFeatureFieldList()), dimensionData,featureConfigRowList.get(0).getDbType(),featureConfigRowList.get(0).getTableName());
			// 添加
			addOrderFeature(featureConfigRowList, result, pushFeature);
		}
	}


	private void addOrderFeature(BaseFeatureConfigDimensionList featureConfigRowList, JSONObject featureField,
								 PushFeature feature) {
		for (BaseFeature baseFeature : featureConfigRowList) {
			String featureCode = baseFeature.getFeatureCode();
			String defaultValue = baseFeature.getDefaultValue();
			// 类型转换和默认值
			Object featureValue = getFeatureValue(featureField,  baseFeature.getFeatureField(), baseFeature.getFieldType(),
					defaultValue);
			if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
				continue;
			}
			feature.addOrderFeature(featureCode, convertFeatureValueToList(baseFeature,featureValue));
		}
	}

	private Object convertFeatureValueToList(BaseFeature baseFeature,Object featureValue){
        if(StringUtils.isNotBlank(baseFeature.getListType())){
            String featureValueStr = (String)featureValue;
            if(StringUtils.isNotBlank(featureValueStr)){
                return Stream.of(featureValueStr.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            }else{
                return new ArrayList<>();
            }
        }
        return featureValue;
    }


	private Object getFeatureValue(JSONObject featureField, String featureName, String fieldType, String defaultValue) {
		Object result = null;
		try {
			if ("Long".equals(fieldType)) {
				result = JSONUtil.getLong(featureField, featureName, defaultValue);
			} else if ("Double".equals(fieldType)) {
				result = JSONUtil.getDouble(featureField, featureName, defaultValue);
			} else {
				result = JSONUtil.getString(featureField, featureName, defaultValue);
			}
		} catch (Exception e) {
			result = VariableCastUtil.variableCast(defaultValue, fieldType);
		}
		return result;
	}


	private Map<String,Object> getMultiDimensionFeatureValue(List<JSONObject> featureFieldList, String featureName, String fieldType,String defaultValue,String calculateType) {

		Map<String,Object> resultMap = new HashMap<>();
		List<Object> list = new ArrayList<>();
		Object calculateValue = null;

		if(CollectionUtils.isEmpty(featureFieldList)){
			Object result = getFeatureValue(null, featureName, fieldType, defaultValue);
			list.add(result);
			calculateValue = result;
		}else{

			for(JSONObject jsonObject : featureFieldList) {
				Object result = getFeatureValue(jsonObject, featureName, fieldType, defaultValue);
				list.add(result);

				if(calculateValue == null){
					calculateValue = result;
				}else{
					if("min".equals(calculateType)){
						if ("Long".equals(fieldType)) {
							if(((Long)calculateValue) > ((Long)result)){
								calculateValue = result;
							}

						}else if ("Double".equals(fieldType)) {
							if(((Double)calculateValue) > ((Double)result)){
								calculateValue = result;
							}

						}
					}else if("max".equals(calculateType)){
						if ("Long".equals(fieldType)) {
							if(((Long)calculateValue) < ((Long)result)){
								calculateValue = result;
							}

						}else if ("Double".equals(fieldType)) {
							if(((Double)calculateValue) < ((Double)result)){
								calculateValue = result;
							}

						}
					}

				}
			}

		}

		resultMap.put("valueList",list);
		resultMap.put("calculateValue",calculateValue);

		return resultMap;
	}


	/**
	 * 计算订单特征
	 *
	 * @param orderFeature
	 */
	private void calculateOrderComplexFeature(Map<String, Object> orderFeature,List<ComplexFeature> orderComplexFeatureList) {
		Map<String, ComplexFeature> complexFeatureMap = new HashMap<>();
		orderComplexFeatureList.forEach(complexFeature -> {
			complexFeature.setFeatureDependencySet(complexFeature.getFeatureDependency());
			complexFeature.setDependencyCheck(complexFeature.getDependencyCheck());
			complexFeatureMap.put(complexFeature.getFeatureCode(),complexFeature);
		});
		DefaultContext<String, Object> calcuMaterial = new DefaultContext<>();
		calcuMaterial.putAll(orderFeature);
		for (Map.Entry<String, ComplexFeature> row : complexFeatureMap.entrySet()) {
			String featureCode = row.getKey();
			ComplexFeature complexFeature = row.getValue();
			if (complexFeature.needDependencyCheck()) {
				if (!complexFeature.featureCheck(calcuMaterial.keySet())) {
					continue;
				}
			}
			String defaultValue = complexFeature.getDefaultValue();
			String aviatorExpression = complexFeature.getCalculateExpression();
			Object featureValue = calculateFeatureValue(aviatorExpression, calcuMaterial, defaultValue,
					complexFeature.getFieldType());
			if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
				continue;
			}
			orderFeature.put(featureCode, featureValue);
		}
	}


	/**
	 * 计算师傅特征
	 *
	 * @param feature
	 */
	private void calculateMasterComplexFeature(PushFeature feature,List<ComplexFeature> masterComplexFeatureList) {
		DefaultContext<String, DefaultContext<String, Object>> masterFeature = feature.getMasterFeature();
		Map<String, ComplexFeature> complexFeatureMap = new HashMap<>();
        Set<String> additionFeatureList = new HashSet<>();
        masterComplexFeatureList.forEach(complexFeature -> {
            if (complexFeature.getFeatureDependency()!=null) {
                for (String featureName : complexFeature.getFeatureDependency().split(",")) {
                    if (featureName.startsWith("{")) {
                        additionFeatureList.add(StrUtil.removeAll(featureName, '{','}'));
                    }
                }
            }
            complexFeature.setFeatureDependencySet(complexFeature.getFeatureDependency());
            complexFeature.setDependencyCheck(complexFeature.getDependencyCheck());
			complexFeatureMap.put(complexFeature.getFeatureCode(),complexFeature);
		});

		for (DefaultContext.Entry<String, DefaultContext<String, Object>> row : masterFeature.entrySet()) {
			// 循环师傅
			HashMap<String, Object> currentMasterFeature = row.getValue();
			DefaultContext<String, Object> calcuMaterial = new DefaultContext<>();
			calcuMaterial.putAll(currentMasterFeature);
            Map<String, Object> additionFeatureMap = feature.getOrderFeatureByList(additionFeatureList);
            calcuMaterial.putAll(additionFeatureMap);
			for (Map.Entry<String, ComplexFeature> rowTwo : complexFeatureMap.entrySet()) {
				// 循环特征
				String featureName = rowTwo.getKey();
				ComplexFeature complexFeature = rowTwo.getValue();
				if (complexFeature.needDependencyCheck()) {
					if (!complexFeature.featureCheck(calcuMaterial.keySet())) {
						continue;
					}
				}
				String aviatorExpression = complexFeature.getCalculateExpression();
				Object featureValue = calculateFeatureValue(aviatorExpression, calcuMaterial,
						complexFeature.getDefaultValue(), complexFeature.getFieldType());
				if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
					continue;
				}
				currentMasterFeature.put(featureName, featureValue);
			}
		}
	}

	/**
	 * 计算特征值：aviator
	 *
	 * @return
	 */
	private Object calculateFeatureValue(String expression, DefaultContext<String, Object> calcuMaterial,
										 String defaultValue, String fieldType) {
		Object featureValue = null;
		try {
			featureValue = qlExpressHandler.getExpressRunner().execute(expression, calcuMaterial,null,true,false);
			featureValue = VariableCastUtil.variableCast(featureValue, fieldType);
		} catch (Exception e) {
			log.error("calculateFeatureValue error",e);
			featureValue = VariableCastUtil.variableCast(defaultValue, fieldType);
		}
		return featureValue;
	}

	/**
	 * 添加师傅特征
	 *
	 * @param featureConfigRowList
	 * @param feature
	 */
	private void addMasterFeature(BaseFeatureConfigDimensionList featureConfigRowList,
								  Map<String, List<JSONObject>> masterFeaturesArray, PushFeature feature) {
		// 师傅
		for (String masterId : feature.getMasterFeature().keySet()) {
			DefaultContext<String, Object> currentMasterFeature = new DefaultContext<>();
			List<JSONObject> masterFeatureRowList = masterFeaturesArray.get(masterId);
			// 特征
			for (BaseFeature baseFeature : featureConfigRowList) {
				String featureCode = baseFeature.getFeatureCode();

				String defaultValue = baseFeature.getDefaultValue();
				Object featureValue = null;


				Object calculateValue =null;

				if(StringUtils.isNotBlank(baseFeature.getMultiValueDimension())){
					// 类型转换和默认值
					Map<String,Object> valueMap = getMultiDimensionFeatureValue(masterFeatureRowList, baseFeature.getFeatureField(), baseFeature.getFieldType(),defaultValue,baseFeature.getCalculateType());
					featureValue = valueMap.get("valueList");
					calculateValue = valueMap.get("calculateValue");

				}else{
					// 类型转换和默认值
						featureValue = getFeatureValue(CollectionUtils.isNotEmpty(masterFeatureRowList) && masterFeatureRowList.get(0) != null ? masterFeatureRowList.get(0) : null, baseFeature.getFeatureField(), baseFeature.getFieldType(),
								defaultValue);
					}

                currentMasterFeature.put(featureCode, convertFeatureValueToList(baseFeature,featureValue));
				if(calculateValue != null){
					currentMasterFeature.put(featureCode + ":calculateValue", calculateValue);
				}
			}
			//冗余师傅ID 2022-03-08
			currentMasterFeature.put(FieldConstant.MASTER_ID,masterId);
			feature.addMasterFeature(masterId, currentMasterFeature);
		}
	}



	/**
	 * 查询基础师傅特征
	 *
	 * @param feature
	 * @param featureQueryMap
	 * @param dimensionData
	 */
	private void queryMasterBaseFeature(PushFeature pushFeature, Map<String, BaseFeatureConfigDimensionList> featureQueryMap,
										Map<String, Object> dimensionData, Set<String> masterIdSet) {
		for (Map.Entry<String, BaseFeatureConfigDimensionList> row : featureQueryMap.entrySet()) {
			// 当前维度
			String dimensionColumn = row.getKey();
			BaseFeatureConfigDimensionList featureConfigRowList = row.getValue();
			List<String> dimensionColumnList = null;
			if (featureConfigRowList.isDynamicDimension()) {
				// 动态维度
				featureConfigRowList.genrateDimensionFeature(dimensionData);
				dimensionColumnList = featureConfigRowList.dynamicDimensionList();
			} else {
				//TODO 验证是否有序
				dimensionColumnList = Arrays.asList(dimensionColumn.split(","));
			}
			// 查询
			JSONArray result = featureQueryServiceImpl.queryMasterFeature(dimensionColumnList,
					featureConfigRowList.getFeatureFieldList() ,dimensionData, masterIdSet,featureConfigRowList.get(0).getDbType(),featureConfigRowList.get(0).getTableName(),featureConfigRowList.getMultiValueDimension());

			Map<String, List<JSONObject>> featureFieldMap = FeatureQueryServiceImpl.getMasterFeatureMap(result);
			// 添加
			addMasterFeature(featureConfigRowList, featureFieldMap, pushFeature);

			if(StringUtils.isNotBlank(featureConfigRowList.getMultiValueDimension())){
				pushFeature.addMultiValueFeature(featureConfigRowList.getFeatureCodeList());
			}
		}
	}


	/**
	 * 基础特征维度划分
	 */
	private Map<String, BaseFeatureConfigDimensionList> initBaseFeatureDimension(List<BaseFeature> baseFeatureList) {

		HashMap<String, BaseFeatureConfigDimensionList> baseFeatureMap = new HashMap<>();

		for (BaseFeature baseFeature : baseFeatureList) {
			String featureDimension = baseFeature.getFeatureDimension();
			if (baseFeatureMap.containsKey(featureDimension)) {
				baseFeatureMap.get(featureDimension).add(baseFeature);
			} else {
				BaseFeatureConfigDimensionList featureConfigRowList = new BaseFeatureConfigDimensionList();
				if (featureDimension.contains("{")) {
					featureConfigRowList.setDynamicDimensionTrue(featureDimension);
				}
				featureConfigRowList.setMultiValueDimension(baseFeature.getMultiValueDimension());
				featureConfigRowList.add(baseFeature);
				baseFeatureMap.put(featureDimension, featureConfigRowList);
			}
		}
		return baseFeatureMap;
	}

	/**
	 * 初始化特征维度
	 * @param orderDetailData
	 * @return
	 */
	private DefaultContext<String, Object> buildDimensionData(OrderDetailData orderDetailData) {

		DefaultContext<String, Object> deminsionResult = new DefaultContext<String, Object>();
		try {
			String accountType = orderDetailData.getAccountType();
			// 由于是必要特征,所以作为维度特征保证可靠性
			deminsionResult.put(FieldConstant.ACCOUNT_TYPE, accountType);
			deminsionResult.put(FieldConstant.APPOINT_TYPE, orderDetailData.getAppointType());
			deminsionResult.put(FieldConstant.ORDERTEC_SNAPSHOP, orderDetailData.getOrderTechniques());

			//业务线ID
			deminsionResult.put(FieldConstant.BUSINESS_LINE_ID, orderDetailData.getBusinessLineId());
			deminsionResult.put(FieldConstant.BUSINESS_LINE_TYPE, orderDetailData.getBusinessLineId() != null && orderDetailData.getBusinessLineId() == 1 ? MasterSourceType.TOB.code : MasterSourceType.TOC.code);

			deminsionResult.put(FieldConstant.EXPECT_COMPLETE_TIME, orderDetailData.getExpectCompleteTime());
			deminsionResult.put(FieldConstant.EXPECT_DOORIN_START_TIME, orderDetailData.getExpectDoorInStartDate());


			deminsionResult.put(FieldConstant.TODAY_TIME, DateFormatterUtil.getNow1());

			deminsionResult.put(FieldConstant.MASTER_ORDER_ID, orderDetailData.getMasterOrderId());
			//sign
			deminsionResult.put(FieldConstant.CATEGORY_ID, orderDetailData.getOrderCategoryId());
			Long globalOrderTraceId = orderDetailData.getGlobalOrderId();
			deminsionResult.put(FieldConstant.GLOBAL_ORDER_ID, globalOrderTraceId);
			deminsionResult.put(FieldConstant.TIMER_FLAG, orderDetailData.getTimerFlag());

			deminsionResult.put(FieldConstant.SECOND_DIVISION_ID, orderDetailData.getSecondDivisionId());
			deminsionResult.put(FieldConstant.THIRD_DIVISION_ID, orderDetailData.getThirdDivisionId());
			deminsionResult.put(FieldConstant.FOURTH_DIVISION_ID, orderDetailData.getFourthDivisionId());

			deminsionResult.put(FieldConstant.ORDER_SERVE_TYPE, orderDetailData.getOrderServeType());
			deminsionResult.put(FieldConstant.LV1_SERVE_ID, orderDetailData.getLv1ServeIds());
			deminsionResult.put(FieldConstant.LV2_SERVE_IDS, orderDetailData.getLv2ServeIdList());
			deminsionResult.put(FieldConstant.LV3_SERVE_IDS, orderDetailData.getLv3ServeIdList());
			deminsionResult.put(FieldConstant.ORDER_LNG_LAT,orderDetailData.getOrderLngLat());
			deminsionResult.put(FieldConstant.ORDER_FROM,getOrderFrom(orderDetailData));
			deminsionResult.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderDetailData));
			deminsionResult.put(FieldConstant.CUSTOMER_PHONE,orderDetailData.getCustomerPhone());
			deminsionResult.put(FieldConstant.ORDER_LABEL,orderDetailData.getOrderTags());


			List<Long> goodsLevel2Ids = Arrays.stream(Optional.ofNullable(orderDetailData.getParentGoodsIds())
							.orElse("0").split(",")).map(Long::parseLong)
					.collect(Collectors.toList());
			deminsionResult.put(FieldConstant.GOODS_LEVEL_2_IDS, goodsLevel2Ids);

			if (orderDetailData.getOrderTechniqueSet().size()==1) {
				String tecString=orderDetailData.getOrderTechniqueSet().iterator().next();
				String[] stringArray=tecString.split(",");
//				Arrays.sort(stringArray);//字符串排序
				Arrays.sort(stringArray,new Comparator<String>() {
					@Override
					public int compare(String o1, String o2) {
						return Double.valueOf(o1)-Double.valueOf(o2)>0?1:-1;
					}
				});
				deminsionResult.put(FieldConstant.SINGLE_ORDER_TECHNOLOGYS_IN_DEMAND
						,String.join(",", stringArray));
			}
			int goodsSize=orderDetailData.getParentGoodsIdsSet().size();
			deminsionResult.put(FieldConstant.GOODS_NUM, orderDetailData.getGoodsNum());

			if (orderDetailData.getChildGoodsIdsSet()!=null&&goodsSize == 1) {
				deminsionResult.put(FieldConstant.GOODS_PARENT_ID,
						NumberUtils.toLong(orderDetailData.getParentGoodsIds(), 0));
				deminsionResult.put(FieldConstant.GOODS_CHILD_ID,
						NumberUtils.toLong(orderDetailData.getChildGoodsIds(), 0));
			}

			deminsionResult.put(FieldConstant.ALL_GOODS_PARENT_ID, orderDetailData.getParentGoodsIds());

			if (FieldConstant.ENTERPRISE.equals(accountType)) {
				deminsionResult.put(FieldConstant.USER_ID, orderDetailData.getUserId());
				deminsionResult.put(FieldConstant.ENTERPRISE_ID, orderDetailData.getAccountId());
			} else {
				deminsionResult.put(FieldConstant.USER_ID, orderDetailData.getAccountId());
				deminsionResult.put(FieldConstant.ENTERPRISE_ID, 0L);
			}


			deminsionResult.put(FieldConstant.TEAM_MASTER_ORDER_PUSH,teamMasterPushOrder(orderDetailData));

			deminsionResult.put(FieldConstant.CANCEL_APPOINT,orderDetailData.getCancelAppoint());
		} catch (Exception e) {
			log.error(String.format("buildDimensionData error,orderDetailData:%s", JSON.toJSONString(orderDetailData)),e);
		}
		log.info("{},deminsion:{}",orderDetailData.getMasterOrderId(),deminsionResult);
		return deminsionResult;
	}

	private int teamMasterPushOrder(OrderDetailData orderDetailData) {
		try{
			Integer isExclusiveTeamMaster = orderDetailData.getIsExclusiveTeamMaster();
			if(isExclusiveTeamMaster != null && isExclusiveTeamMaster == 1){
				return 0;
			}
			String orderFrom = orderDetailData.getOrderFrom();
			List<String> baseServe = Arrays.asList("1003106,2003106,3003106,4003106,1003133,2003133,3003133,4003133,1133144,2133144,3133144,4133144,1193144,2193144,3193144,4193144,1203144,2203144,3203144,4203144,1143144,2143144,3143144,4143144,1153144,2153144,3153144,4153144".split(","));
			String[] serveArray = Optional.ofNullable(orderDetailData.getLv3ServeIds()).orElse("").split(",");
			List<String> baseServeTypes = Arrays.asList("1,2,3,4,15".split(","));

			Long categoryId = orderDetailData.getOrderCategoryId();
			Integer serveType = orderDetailData.getOrderServeType();

			BigDecimal goodsNum = new BigDecimal(orderDetailData.getGoodsNum());

			boolean conditionOne = "site".equals(orderFrom) && Stream.of(serveArray).anyMatch(baseServe::contains);
			boolean conditionTwo = "site".equals(orderFrom)
					&& Objects.equals(categoryId, 1L)
					&& baseServeTypes.contains(String.valueOf(serveType))
					&& goodsNum.compareTo(BigDecimal.TEN) > 0;

			return (conditionOne || conditionTwo) ? 1 : 0;
		}catch(Exception e){
			log.error(String.format("计算推单师傅推单失败,orderDetailData:%s",JSON.toJSONString(orderDetailData)),e);
		}

		return 0;
	}

	private String getOrderFrom(OrderDetailData orderDetailData){
        String orderFrom = orderDetailData.getOrderFrom();
        if("user".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "thirdpart".equals(orderFrom)){
                return "site";
            }else{
                return "family";
            }
        }else if("enterprise".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "applet".equals(orderFrom)){
                return "enterprise_inside";
            }else{
                return "enterprise_outside";
            }
        }

        return "";


    }


    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){
		List<String> timeLinessTagList = new ArrayList<>();

		if(StringUtils.isBlank(orderDetailData.getExpectDoorInStartDate())){
			return timeLinessTagList;
		}

		if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
			timeLinessTagList.add("regular_time_order");
		}

		if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
			timeLinessTagList.add("emergency_order");
		}

		if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
			timeLinessTagList.add("on_time_order");
		}

		if(StringUtils.isNotBlank(orderDetailData.getExpectDoorInStartDate())){
			timeLinessTagList.add("expect_time_order");
		}

		return timeLinessTagList;
	}

}
