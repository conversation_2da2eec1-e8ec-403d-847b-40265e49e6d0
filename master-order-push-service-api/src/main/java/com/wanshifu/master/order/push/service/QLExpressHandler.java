package com.wanshifu.master.order.push.service;

import com.ql.util.express.ExpressRunner;
import com.wanshifu.master.order.push.function.*;
import com.wanshifu.master.order.push.operator.OperatorContain;
import com.wanshifu.master.order.push.operator.OperatorContainsAny;
import com.wanshifu.master.order.push.operator.OperatorNotContain;
import com.wanshifu.master.order.push.operator.OperatorNotContainsAny;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * QL表达式处理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class QLExpressHandler {

    private ExpressRunner expressRunner;


    @PostConstruct
    public void initQueryClient() {
        expressRunner = new ExpressRunner();
        expressRunner.addFunction("distance_from_two_point", new DistanceFromTwoPoint());
        expressRunner.addFunction("duration_days_from_now",new DurationDaysFromNow());
        expressRunner.addFunction("duration_minutes_from_now",new DurationMinutesFromNow());
        expressRunner.addFunction("date_expire",new DateExpire());
        expressRunner.addFunction("duration_day",new DurationDay());
        expressRunner.addFunction("duration_time",new DurationTime());



        expressRunner.addFunction("string_sequence_contains",new StringSequenceContains());
        expressRunner.addFunction("anyMatch", new AnyMatch());
        expressRunner.addFunction("allMatch", new AllMatch());

        try {
            expressRunner.addOperator("contain", new OperatorContain());
            expressRunner.addOperator("notContain", new OperatorNotContain());
            expressRunner.addOperator("containsAny", new OperatorContainsAny());
            expressRunner.addOperator("notContainsAny", new OperatorNotContainsAny());
        }catch (Exception e){
            log.error("expressRunner.addOperator is error!",e);
        }
    }

    public ExpressRunner getExpressRunner(){
        return expressRunner;
    }

}
