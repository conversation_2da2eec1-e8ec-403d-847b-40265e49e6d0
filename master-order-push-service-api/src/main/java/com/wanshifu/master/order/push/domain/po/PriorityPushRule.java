package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "priority_push_rule")
public class PriorityPushRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_id")
    private Integer ruleId;

    @Column(name = "business_line_id")
    private Integer businessLineId;

    @Column(name = "rule_name")
    private String ruleName;

//    @Column(name = "order_from")
//    private String orderFrom;
//
//    @Column(name = "appoint_type")
//    private String appointType;

    @Column(name = "rule_desc")
    private String ruleDesc;

    @Column(name = "category_ids")
    private String categoryIds;

    @Column(name = "city_ids")
    private String cityIds;

    @Column(name = "push_groups")
    private String pushGroups;


    @Column(name = "push_groups_expression")
    private String pushGroupsExpression;


    @Column(name = "push_rule")
    private String pushRule;


    @Column(name = "push_rule_expression")
    private String pushRuleExpression;


    @Column(name = "rule_status")
    private Integer ruleStatus;


    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;


    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


}
