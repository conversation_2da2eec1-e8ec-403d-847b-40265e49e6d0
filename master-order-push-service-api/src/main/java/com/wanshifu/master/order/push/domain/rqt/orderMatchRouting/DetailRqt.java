package com.wanshifu.master.order.push.domain.rqt.orderMatchRouting;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  初筛策略详情Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailRqt{

    /**
     * 策略id
     */
    @NotNull
    private Integer routingId;

    public DetailRqt(Integer routingId){
        this.routingId = routingId;
    }


    public DetailRqt(){}


}