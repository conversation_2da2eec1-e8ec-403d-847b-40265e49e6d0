package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.resp.OrderScoringItemListResp;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 18:00
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "orderScoringStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface OrderScoringStrategyApi {


    @PostMapping("create")
    Integer create(@Valid @RequestBody CreateOrderScoringStrategyRqt rqt);

    @PostMapping("update")
    Integer update(@Valid @RequestBody UpdateOrderScoringStrategyRqt rqt);

    @PostMapping("enable")
    Integer enable(@Valid @RequestBody EnableOrderScoringStrategyRqt rqt);

    @PostMapping("delete")
    Integer delete(@Valid @RequestBody DeleteOrderScoringStrategyRqt rqt);

    @PostMapping("detail")
    OrderScoringStrategy detail(@Valid @RequestBody OrderScoringStrategyDetailRqt rqt);

    @PostMapping(value = "selectAvailableStrategyByIdList")
    List<OrderScoringStrategy> selectAvailableStrategyByIdList(OrderScoringStrategyByIdListRqt rqt);

    @PostMapping("list")
    SimplePageInfo<OrderScoringStrategy> list(@Valid @RequestBody GetOrderScoringStrategyListRqt rqt);

    @GetMapping("/scoringItemList")
    List<OrderScoringItemListResp> scoringItemList(@RequestParam(value = "itemName", required = false) String itemName);

}
