package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.util.List;

@Data
public class EnterpriseAppointMessage {

    private Long globalOrderTraceId;

    private String orderNo;

    private Long fromAccountId;

    private Long orderId;

    private Long enterpriseId;

    private List<Long> exclusiveMasterList;


    private AutoHireMasterWebRqt autoHireMasterWebRqt;

    @Data
    public static class AutoHireMasterWebRqt{

        private boolean cancelMaster;

        private Integer changeHireType;

        private boolean definiteOffer;

        private boolean enquiryOrder;

        private boolean openAssign;

        private boolean skipRepeatOrderCheck;

        private List<Long> stopMasterIds;
    }

}
