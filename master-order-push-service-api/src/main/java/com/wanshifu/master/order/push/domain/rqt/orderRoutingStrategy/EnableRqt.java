package com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  启用/禁用策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class EnableRqt {

    /**
     * 策略id
     */
    @NotNull
    private Integer strategyId;

    /**
     * 状态 1:启用 0:禁用
     */
    @NotNull
    @ValueIn("1,0")
    private Integer strategyStatus;


    /**
     * 更新人账号id
     */
    private Long updateAccountId;
}