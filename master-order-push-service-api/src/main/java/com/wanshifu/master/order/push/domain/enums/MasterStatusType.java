package com.wanshifu.master.order.push.domain.enums;

/**
 * 师傅状态类型
 * <AUTHOR>
 */
public enum MasterStatusType {

    /**
     * 黑名单
     */
    BLACK_LIST("black_list"),

    /**
     * 账号异常
     */
    ACCOUNT_STATUS_ABNORMAL("account_status_abnormal"),

    /**
     * 账号冻结
     */
    ACCOUNT_FREEZE("account_freeze"),

    /**
     * 未成功入驻
     */
    NOT_SETTLED("not_settled"),

    /**
     * 最近30天未活跃
     */
    NOT_ACTIVE_GT_30("not_active_gt_30"),

    /**
     * 休息
     */
    NOT_WORKING("not_working"),

    /**
     * 考试未通过
     */
    EXAM_NOT_PASS("exam_not_pass");


    public final String code;

    MasterStatusType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }

}
