package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "orderDistributeStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface OrderDistributeStrategyApi {


    @PostMapping("create")
    Integer create(@Valid @RequestBody CreateOrderDistributeStrategyRqt rqt);

    @PostMapping("update")
    Integer update(@Valid @RequestBody UpdateOrderDistributeStrategyRqt rqt);

    @PostMapping("enable")
    Integer enable(@Valid @RequestBody EnableOrderDistributeStrategyRqt rqt);

    @PostMapping("delete")
    Integer delete(@Valid @RequestBody DeleteOrderDistributeStrategyRqt rqt);

    @PostMapping("detail")
    GetOrderDistributeStrategyDetailResp detail(@Valid @RequestBody OrderDistributeStrategyDetailRqt rqt);

    @PostMapping("list")
    SimplePageInfo<OrderDistributeStrategy> list(@Valid @RequestBody GetOrderDistributeStrategyListRqt rqt);
}
