package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.CompensateDistribute;
import com.wanshifu.master.order.push.domain.rqt.compensateDistribute.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "compensateDistribute", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface CompensateDistributeApi {

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    int update(@RequestBody @Valid UpdateRqt rqt);
    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    CompensateDistribute detail(@RequestBody @Valid DetailRqt rqt);


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<CompensateDistribute> list(@RequestBody @Valid ListRqt rqt);


    @PostMapping("/delete")
    Integer delete(@RequestBody @Valid DeleteRqt rqt);

    @PostMapping("/match")
    List<CompensateDistribute> match(@RequestBody @Valid MatchRqt rqt);

}
