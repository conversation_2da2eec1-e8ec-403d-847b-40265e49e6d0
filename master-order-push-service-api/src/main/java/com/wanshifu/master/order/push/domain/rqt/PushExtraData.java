package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 推单扩展数据
 * <AUTHOR>
 */
@Data
public class PushExtraData {


    /**
     * 推单排除师傅id-专属退款
     */
    private String orderPushExcludeMasterIds;

    /**
     * 推单排除师傅id-普通推单
     */
    private List<Long> orderPushEliminateMasterIds;

    /**
     * 招募id(原专属师傅)
     */
    private Long recruitId;

    /**
     * 是否有价格（原专属师傅）
     */
    private Boolean hasPrice;

    private Integer teamMasterOrderPush;

    private String operateFlag;

    private String pushMode;

    private String handoffTag;


    private Long directAppointMasterId;

    /**
     * 匹配场景
     */
    private String matchSceneCode;

    /**
     * 推送师傅来源类型，tob: B端师傅,toc: C端师傅
     */
    private String masterSourceType;

    /**
     * 重推延迟时间
     */
    private Integer afreshDelayTime;


    /**
     * 排除的推单模式
     */
    private List<String> exclusivePushModeList;


    /**
     * 用户直接指派师傅（作业帮）
     */
    private List<MasterAutoOfferPriceInfo> masterAutoOfferPriceInfoList;

    /**
     * 用户雇佣信息自动报价信息
     */
    @Data
    public static class MasterAutoOfferPriceInfo {
        /**
         * 师傅id
         */
        private Long masterId;

        /**
         * 报价
         */
        private BigDecimal targetOfferPrice;
    }
}
