package com.wanshifu.master.order.push.domain.rqt.strategyCombination;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改组合策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 组合策略id
     */
    @NotNull
    private Long combinationId;

    private Long updateAccountId;
}