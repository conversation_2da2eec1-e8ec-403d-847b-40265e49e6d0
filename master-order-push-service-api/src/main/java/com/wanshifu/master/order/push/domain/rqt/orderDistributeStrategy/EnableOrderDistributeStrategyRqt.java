package com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class EnableOrderDistributeStrategyRqt {


    /**
     * 策略id
     */
    @NotNull
   private Integer strategyId;

    @NotNull
    @ValueIn("1,0")
   private Integer strategyStatus;

    private Long updateAccountId;

}
