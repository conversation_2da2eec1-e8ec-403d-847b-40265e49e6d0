package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 协议师傅匹配记录
 */
@Data
@Table(name = "order_technique_verify_master_match")
public class OrderTechniqueVerifyMasterMatch {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 订单下单时间
     */
    @Column(name = "order_create_time")
    private Date orderCreateTime;

    /**
     * 指派模式
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 是否匹配成功
     */
    @Column(name = "is_match_succ")
    private Integer isMatchSucc;


    /**
     * 匹配地址级别，3：按三级地址匹配，4：按四级地址匹配
     */
    @Column(name = "match_division_level")
    private Integer matchDivisionLevel;

    /**
     * 匹配失败原因
     */
    @Column(name = "match_fail_reason")
    private String matchFailReason;

    /**
     * 是否被过滤
     */
    @Column(name = "is_filter")
    private Integer isFilter;

    /**
     * 过滤原因
     */
    @Column(name = "filter_reason")
    private String filterReason;

    /**
     * 是否调度
     */
    @Column(name = "is_distribute")
    private Integer isDistribute;

    /**
     * 调度规则
     */
    @Column(name = "distribute_rule")
    private String distributeRule;

    /**
     * 是否自动接单成功
     */
    @Column(name = "is_auto_receive_succ")
    private Integer isAutoReceiveSucc;

    /**
     * 自动接单失败原因
     */
    @Column(name = "auto_receive_fail_reason")
    private String autoReceiveFailReason;

    /**
     * 版本号
     */
    @Column(name = "order_version")
    private String orderVersion;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
