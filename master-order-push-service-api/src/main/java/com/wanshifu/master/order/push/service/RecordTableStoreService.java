//package com.wanshifu.master.order.push.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.alicloud.openservices.tablestore.AsyncClient;
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.TableStoreCallback;
//import com.alicloud.openservices.tablestore.model.*;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 表格存储Service
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class RecordTableStoreService {
//
//	@Resource
//	private TableStoreClient tableStoreClient;
//
//
//	private static final String CONTENT="content";
//	private static final String REGION_MODE="region_mode";
//	private static final String RECORD_TYPE="record_type";
//
//	public static final String SMC_ORDER_PUSH="smc_order_push";
//	private static final String FEATURE_TABLE="t_global_order";
//
//	public static final String PUSH_COUNT_TAG="push_count_tag";
//
////
////	public void saveBaseSelectRecord(Record record, String orderVersion) {
////		SyncClient synClient = tableStoreClient.getSyncClient();
////		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(FieldConstant.GLOBAL_ORDER_ID, record.getGlobalOrderId(), RECORD_TYPE,
////				record.getRecordType().toString()+"-"+orderVersion);
////		RowUpdateChange rowUpdateChange=new RowUpdateChange(RECORD_TABLE, primaryKey);
////		rowUpdateChange.put(REGION_MODE, ColumnValue.fromLong(record.getRegionMode()));
////		rowUpdateChange.put(CONTENT, ColumnValue.fromString(record.getContent()));
////		synClient.updateRow(new UpdateRowRequest(rowUpdateChange));
////	}
////
////
////	public void saveBaseSelectRecord(Record record, String orderVersion, String pushMode) {
////		SyncClient synClient = tableStoreClient.getSyncClient();
////		String reType=new StringJoiner("-")
////				.add(pushMode.toUpperCase())
////				.add(record.getRecordType().toString())
////				.add(orderVersion).toString();
////		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(FieldConstant.GLOBAL_ORDER_ID, record.getGlobalOrderId(), RECORD_TYPE,
////				reType);
////		RowUpdateChange rowUpdateChange=new RowUpdateChange(RECORD_TABLE, primaryKey);
////		rowUpdateChange.put(REGION_MODE, ColumnValue.fromLong(record.getRegionMode()));
////		rowUpdateChange.put(CONTENT, ColumnValue.fromString(record.getContent()));
////		synClient.updateRow(new UpdateRowRequest(rowUpdateChange));
////	}
//
//
//	public void saveOrderFeature(String globalOrderId,String fieldName,String fieldValue) {
//		SyncClient synClient = tableStoreClient.getSyncClient();
//		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithOneColumn(FieldConstant.GLOBAL_ORDER_ID, globalOrderId);
//		RowUpdateChange rowUpdateChange=new RowUpdateChange(FEATURE_TABLE, primaryKey);
//		rowUpdateChange.put(fieldName, ColumnValue.fromString(fieldValue));
//		synClient.updateRow(new UpdateRowRequest(rowUpdateChange));
//	}
//
//
//	public void saveOrderDetailRecord(String masterOrderId,String orderVersion,JSONObject orderDetail) {
//		SyncClient synClient = tableStoreClient.getSyncClient();
//		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(FieldConstant.MASTER_ORDER_ID,
//				masterOrderId, FieldConstant.ORDER_VERSION,
//				orderVersion);
//		RowUpdateChange rowUpdateChange=new RowUpdateChange(FieldConstant.SMC_ORDER_MSG_RECORD, primaryKey);
//		rowUpdateChange.put(FieldConstant.ORDER_DETAIL, ColumnValue.fromString(orderDetail.toJSONString()));
//		synClient.updateRow(new UpdateRowRequest(rowUpdateChange));
//	}
//
////	public void saveOrderPushRecordLine(String masterOrderId, String pushTime, List<PushMaster> finalList) {
////		final AsyncClient asyncClientDataCenter = tableStoreClient.getAsyncClientDataCenter();
////		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithThreeColumnsString(
////				FieldConstant.MASTER_ORDER_ID,masterOrderId,
////				FieldConstant.MASTER_ID,"",
////				FieldConstant.PUSH_TIME,""
////		);
////		RowUpdateChange rowUpdateChange=new RowUpdateChange(FieldConstant.SMC_ORDER_MSG_RECORD, primaryKey);
////
////
////		asyncClientDataCenter.updateRow(new UpdateRowRequest(rowUpdateChange), new TableStoreCallback<UpdateRowRequest, UpdateRowResponse>() {
////			@Override
////			public void onCompleted(UpdateRowRequest updateRowRequest, UpdateRowResponse updateRowResponse) {
////
////			}
////
////			@Override
////			public void onFailed(UpdateRowRequest updateRowRequest, Exception e) {
////
////			}
////		});
////	}
//
//	public void saveOrderPushRecordLine(String masterOrderId, String pushTime, String pushMaster) {
//		final AsyncClient asyncClientDataCenter = tableStoreClient.getAsyncClient();
//		PrimaryKey primaryKey = tableStoreClient.generatePrimaryKeyWithTwoColumns(
//				FieldConstant.MASTER_ORDER_ID,masterOrderId,
//				FieldConstant.PUSH_TIME,pushTime
//		);
//		RowUpdateChange rowUpdateChange=new RowUpdateChange(SMC_ORDER_PUSH, primaryKey);
//		rowUpdateChange.put(FieldConstant.MASTER_ID_LIST, ColumnValue.fromString(pushMaster));
//		asyncClientDataCenter.updateRow(new UpdateRowRequest(rowUpdateChange), new TableStoreCallback<UpdateRowRequest, UpdateRowResponse>() {
//			@Override
//			public void onCompleted(UpdateRowRequest updateRowRequest, UpdateRowResponse updateRowResponse) {
//			}
//			@Override
//			public void onFailed(UpdateRowRequest updateRowRequest, Exception e) {
//
//			}
//		});
//	}
//
////	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {
////		final SyncClient syncClient = tableStoreClient.getSyncClient();
//////		//构造主键。
////		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
////		primaryKeyBuilder.addPrimaryKeyColumn(FieldConstant.PACKAGE_CONFIG_ID, PrimaryKeyValue.fromString(packageConfigId));
////		primaryKeyBuilder.addPrimaryKeyColumn(FieldConstant.MASTER_ID, PrimaryKeyValue.fromString(masterId));
////		PrimaryKey primaryKey = primaryKeyBuilder.build();
////
////		//读取一行数据。
////		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(OTSMasterSelector.SMC_PACKAGE_ORDER_MASTER_BASE, primaryKey);
////		criteria.setMaxVersions(1);
////		GetRowResponse getRowResponse = syncClient.getRow(new GetRowRequest(criteria));
////		Row row = getRowResponse.getRow();
////		final String pushCountTag = tableStoreClient.getValue(row, PUSH_COUNT_TAG);
////		RowUpdateChange rowUpdateChange = new RowUpdateChange(OTSMasterSelector.SMC_PACKAGE_ORDER_MASTER_BASE, primaryKey);
////		if (pushCountTag == null) {
////			rowUpdateChange.put(PUSH_COUNT_TAG, ColumnValue.fromString(miToDay));
////			rowUpdateChange.put(OTSMasterSelector.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY, ColumnValue.fromLong(1));
////		} else if (miToDay.compareTo(pushCountTag)>0) {
////			rowUpdateChange.put(PUSH_COUNT_TAG, ColumnValue.fromString(miToDay));
////			rowUpdateChange.put(OTSMasterSelector.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY, ColumnValue.fromLong(1));
////		} else {
////			rowUpdateChange.increment(new Column(OTSMasterSelector.MASTER_PACKAGE_ORDER_PUSH_NUMBER_DAILY,
////					ColumnValue.fromLong(1)));
////		}
////		try {
////			syncClient.updateRow(new UpdateRowRequest(rowUpdateChange));
////		}
//////		catch (TableStoreException ex) {
//////			final String errorCode = ex.getErrorCode();
//////			if (FieldConstant.OTS_CONDITION_CHECK_FAIL.equals(errorCode)) {
//////				RowUpdateChange afterChange = new RowUpdateChange(OTSMasterSelector.SMC_PACKAGE_ORDER_MASTER_BASE, primaryKey);
//////				afterChange.put(PUSH_COUNT_TAG, ColumnValue.fromString(miToDay));
//////				afterChange.put(OTSMasterSelector.MASTER_PACKAGE_ORDER_PUSH_NUMBER, ColumnValue.fromLong(1));
//////				try {
//////					syncClient.updateRow(new UpdateRowRequest(afterChange));
//////				}catch (Exception e){
//////					logger.warn("[2] packageOrderPushCount exception:[{}]",e);
//////				}
//////			}
//////		}
////		catch (Exception e){
////			log.warn("[2] packageOrderPushCount exception:[{}]",e);
////		}
////	}
//
//}