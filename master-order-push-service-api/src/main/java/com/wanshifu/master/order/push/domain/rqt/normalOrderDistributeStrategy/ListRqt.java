package com.wanshifu.master.order.push.domain.rqt.normalOrderDistributeStrategy;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 服务类目id
     */
    private Integer categoryIds;

    /**
     * 城市id
     */
    private Integer cityIds;


    /**
     * 策略状态，1：启用，0:禁用
     */
    private Integer strategyStatus;


    /**
     * 创建开始时间
     */
    private Date createStartTime;


    /**
     * 创建结束时间
     */
    private Date createEndTime;

}
