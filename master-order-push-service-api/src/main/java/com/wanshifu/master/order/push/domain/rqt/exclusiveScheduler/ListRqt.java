package com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 描述 :  列表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id
     */
    private Integer businessLineId;

    private String categoryIds;
}