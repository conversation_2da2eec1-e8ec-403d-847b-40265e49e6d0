package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;
/**
 * 推单分流规则
 */
@Data
public class PushShuntRule {

    /**
     * 规则id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_id")
    private Integer ruleId;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;


    /**
     * 规则描述
     */
    @Column(name = "rule_desc")
    private String ruleDesc;

    /**
     * 城市id集合
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 人群类型，crowd_label: 标签，crowd_group: 人群组
     */
    @Column(name = "crowd_type")
    private String crowdType;

    /**
     * 人群标签
     */
    @Column(name = "crowd_label")
    private String crowdLabel;


    /**
     * 人群组
     */
    @Column(name = "crowd_group")
    private String crowdGroup;


    /**
     * 指定人群表达式
     */
    @Column(name = "crowd_group_expression")
    private String crowdGroupExpression;


    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 限制范围, all: 全部，serve: 按服务限制
     */
    @Column(name = "limit_range")
    private String limitRange;

    /**
     * 限制服务规则
     */
    @Column(name = "limit_serve_rule")
    private String limitServeRule;


    /**
     * 限制服务规则表达式
     */
    @Column(name = "serve_rule_expression")
    private String serveRuleExpression;

    /**
     * 例外规则
     */
    @Column(name = "exclusive_rule")
    private String exclusiveRule;

    /**
     * 分流规则, 固定比例：fixed_percent
     */
    @Column(name = "limit_rule")
    private String limitRule;

    /**
     * 推送百分百（推送概率）
     */
    @Column(name = "push_percent")
    private Integer pushPercent;


    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


    @Column(name = "rule_status")
    private Integer ruleStatus;


    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
