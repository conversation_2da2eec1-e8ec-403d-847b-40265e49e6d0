package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "normal_order_distribute")
public class NormalOrderDistribute {

    /**
     * 指标枚举值id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 指标id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 指标枚举值编码
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 指标枚举值名称
     */
    @Column(name = "agreement_master_list")
    private String agreementMasterList;


    /**
     * 指标枚举值名称
     */
    @Column(name = "after_filter_master_list")
    private String afterFilterMasterList;

    /**
     * 指标枚举值名称
     */
    @Column(name = "success_master_list")
    private String successMasterList;

    /**
     * 指标枚举值名称
     */
    @Column(name = "fail_master_list")
    private String failMasterList;


    @Column(name = "distribute_type")
    private String distributeType;


    /**
     * 指标枚举值名称
     */
    @Column(name = "remark")
    private String remark;



    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


}
