package com.wanshifu.master.order.push.domain.vo;

import com.ql.util.express.DefaultContext;
import com.wanshifu.master.order.push.util.QlExpressStatic;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class StrategyTuple {
    Set<String> featureSet;
    String triggerExpression;

    public boolean executeQLExpression(DefaultContext<String, Object> context){
        return QlExpressStatic.QlExpressBoolean(triggerExpression,context);
    }

    public static final class StrategyTupleBuilder {
        Set<String> featureSet;
        String triggerExpression;

        private StrategyTupleBuilder() {
        }

        public static StrategyTupleBuilder aStrategyTuple() {
            return new StrategyTupleBuilder();
        }

        public StrategyTupleBuilder withFeatureSet(Set<String> featureSet) {
            this.featureSet = featureSet;
            return this;
        }

        public StrategyTupleBuilder withTriggerExpression(String triggerExpression) {
            this.triggerExpression = triggerExpression;
            return this;
        }

        public StrategyTuple build() {
            StrategyTuple strategyTuple = new StrategyTuple();
            strategyTuple.setFeatureSet(featureSet);
            strategyTuple.setTriggerExpression(triggerExpression);
            return strategyTuple;
        }
    }
}
