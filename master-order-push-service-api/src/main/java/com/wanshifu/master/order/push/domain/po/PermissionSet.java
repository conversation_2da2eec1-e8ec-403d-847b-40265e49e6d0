package com.wanshifu.master.order.push.domain.po;


import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Data
@ToString
@Table(name = "permission_set")
@Deprecated
public class PermissionSet {

    /**
     * 权限集id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "permission_set_id")
    private Integer permissionSetId;

    /**
     * 权限集名称
     */
    @Column(name = "permission_set_name")
    private String permissionSetName;


    /**
     * 权限集描述
     */
    @Column(name = "permission_set_desc")
    private String permissionSetDesc;

    /**
     * 权限集
     */
    @Column(name = "permission_set")
    private String permissionSet;

    /**
     * 角色列表
     */
    @Column(name = "role_list")
    private String roleList;


    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
