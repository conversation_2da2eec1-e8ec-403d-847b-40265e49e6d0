package com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.*;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  创建初筛策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    @NotEmpty
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

//    /**
//     * 开启条件
//     */
//    @Valid
//    @NotNull
//    @Deprecated
//    private OpenConditionVo openCondition;
    /**
     *  normal:普通订单 ikea:宜家订单
     */
    @ValueIn("normal,ikea")
    private String orderFlag;

    /**
     * 范围初筛
     */
    @Valid
    @NotNull
    private RangeSelectVo rangeSelect;

    /**
     * 技能初筛
     */
    @Valid
    @NotNull
    private TechniqueSelectVo techniqueSelect;

    /**
     * 状态初筛
     */
    @Valid
    private StatusSelectVo statusSelect;

    /**
     * 服务数据初筛
     */
    @Valid
    private ServeDataSelectVo serveDataSelect;


    private Long createAccountId;
}