package com.wanshifu.master.order.push.service;

import com.wanshifu.master.order.push.domain.common.BaseSelect;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushCommonObject;
import com.wanshifu.master.order.push.domain.dto.BaseSelectMaster;
import com.wanshifu.master.order.push.domain.dto.RangeSelect;
import com.wanshifu.master.order.push.domain.dto.StatusSelect;
import com.wanshifu.master.order.push.domain.dto.TechniqueSelect;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 初筛师傅
 * <AUTHOR>
 */
@Component
@Slf4j
public class BaseSelector {


    @Resource
    private MasterSelectorForEs masterSelectorForEs;

    @Resource
    private MasterSelector masterSelector;

    /**
     * 不推单城市列表
     */
    @Value("${masterSelector.filter.switch:on}")
    private String masterSelectFilterSwitch;


    /**
     * 专属好单推送全时师傅城市列表
     */
    @Value("${exclusive.goodsOrder.pushFullTimeMater.cityList:0}")
    private String pushFullTimeMasterCityList;



    @Resource
    private PushCommon pushCommon;


//    public PushCommonObject baseSelect(OrderDetailData orderDetailData, String orderVersion,String pushMode,Integer divisionMatchLevel){
//
//        MasterMatchCondition masterMatchCondition = buildMasterMatchCondition(orderDetailData,pushMode,orderVersion);
//        masterMatchCondition.setDivisionMatchLevel(divisionMatchLevel);
//        Set<String> masterSet = null;
//        try{
//            baseSelect(masterMatchCondition,orderDetailData);
//            masterSet = masterMatchCondition.getMasterSet();
//        }catch(Exception e){
//            log.error(String.format("非策略初筛师傅失败，orderDetailData:%s", JSON.toJSONString(orderDetailData)),e);
//        }
//
//
//        //家庭业务过滤已推过一次的代理商-临时-------
//        if (StringUtils.isNotBlank(masterMatchCondition.getPushMode()) && !PushMode.NORMAL.getCode().equals(masterMatchCondition.getPushMode()) && orderVersion != null) {
//            //记录初筛列表
//            recordFacade.baseSelectRecord(String.valueOf(orderDetailData.getGlobalOrderId()), masterSet,
//                    masterMatchCondition.getDivisionMatchLevel(),orderVersion,masterMatchCondition.getPushMode());
//            //记录推送模式特征
//            recordFacade.orderFeatureRecord(String.valueOf(orderDetailData.getGlobalOrderId()), FieldConstant.PUSH_MODE, masterMatchCondition.getPushMode());
//        }
//
//        PushCommonObject pushCommonObject = new PushCommonObject();
//        pushCommonObject.setPushMode(masterMatchCondition.getPushMode());
//        pushCommonObject.setMasterSet(masterSet);
//        if(PushMode.NORMAL.getCode().equals(masterMatchCondition.getPushMode())){
//            pushCommonObject.setDivisionMatchLevel(divisionMatchLevel);
//        }else{
//            pushCommonObject.setDivisionMatchLevel(masterMatchCondition.getDivisionMatchLevel());
//        }
//        pushCommonObject.setOrderDetailData(orderDetailData);
//        pushCommonObject.setMasterMatchCondition(masterMatchCondition);
//        pushCommonObject.setOrderVersion(orderVersion);
//        return pushCommonObject;
//    }

    public List<BaseSelectMaster> baseSelectByStrategy(PushCommonObject pushCommonObject,String orderVersion) {

        String pushMode = pushCommonObject.getPushMode();
        OrderDetailData orderDetailData = pushCommonObject.getOrderDetailData();
        if(pushCommonObject.getMasterMatchCondition() == null){
            MasterMatchCondition masterMatchCondition = buildMasterMatchCondition(orderDetailData,pushMode,orderVersion);
            pushCommonObject.setMasterMatchCondition(masterMatchCondition);
        }
        MasterMatchCondition masterMatchCondition = pushCommonObject.getMasterMatchCondition();

        masterMatchCondition.setDivisionMatchLevel(3);
        masterMatchCondition.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
        if(orderDetailData.getOrderTags().contains("exclusive_good_order") && checkPushFullTimeMasterCity(orderDetailData)){
            masterMatchCondition.setIsExclusivePartTimeMaster(1);
        }

        List<BaseSelectMaster> baseSelectMasterList;
        if("on".equals(masterSelectFilterSwitch)){
            baseSelectMasterList = masterSelector.searchMasterByBaseSelectStrategy(masterMatchCondition, pushCommonObject.getBaseSelect());
        }else{
            baseSelectMasterList = masterSelectorForEs.searchMasterByBaseSelectStrategy(masterMatchCondition, pushCommonObject.getBaseSelect());
        }


        Set<String> masterSet = baseSelectMasterList.stream().map(BaseSelectMaster::getMasterId).collect(Collectors.toSet());

        //初筛后过滤需要剔除的师傅
        masterSet.removeAll(orderDetailData.parseOrderPushEliminateMasterIds());

//        if (PushMode.NORMAL.code.equals(pushMode) && orderDetailData.getHasPriorityPushMeasureMaster() != 0 && orderDetailData.getRelaMeasureGlobalOrderId() != null) {
//            //测量安装单特殊处理
////			priority_push_measure_master : 0
////			1：优先推送给原来师傅
////			2：按原来的逻辑推送（过滤原师傅）
////			0：按原来的逻辑推送（不过滤师傅）
//
//            String measureMasterId = otsMasterSelector.getAppointedMasterByGlobalOrderId(orderDetailData.getRelaMeasureGlobalOrderId());
//            if (measureMasterId != null && orderDetailData.getHasPriorityPushMeasureMaster()==1) {
//                if (CollectionUtils.isNotEmpty(masterSet) && masterSet.contains(measureMasterId)) {
//                    //优先推送给原来师傅
//                    Set<String> priMasterSet=new HashSet<>();
//                    priMasterSet.add(measureMasterId);
//                    pushCommonObject.setPushMode(FieldConstant.MEASURE_MASTER_PRIVILEGE);
//                    pushCommonObject.setPriorityPushMasterSet(priMasterSet);
//                }
//            }else if (measureMasterId != null && orderDetailData.getHasPriorityPushMeasureMaster() == 2) {
////					按原来的逻辑推送（过滤原师傅）
//                if (masterSet.contains(measureMasterId)) {
//                    masterSet.remove(measureMasterId);
//                }
//            }
//
//        }

//        //家庭业务过滤已推过一次的代理商-临时-------
//        if (orderDetailData.getBusinessLineId() == 2 && FieldConstant.NORMAL.equals(masterMatchCondition.getPushMode())) {
//            String lastPushMode= otsMasterSelector.getPushedMode(String.valueOf(orderDetailData.getGlobalOrderId()));
//            if (lastPushMode!=null&&PushMode.AGENT.code.equals(lastPushMode)) {
//                otsMasterSelector.tmpPushedMasterFilter(String.valueOf(orderDetailData.getMasterOrderId()), masterSet,PushMode.AGENT.code);
//            }
//        }
        //家庭业务过滤已推过一次的代理商-临时-------
//        if (orderVersion!=null) {
//            //记录初筛列表
//            recordFacade.baseSelectRecord(String.valueOf(orderDetailData.getGlobalOrderId()), masterSet,
//                    masterMatchCondition.getDivisionMatchLevel(),orderVersion,masterMatchCondition.getPushMode());
//            //记录推送模式特征
//            recordFacade.orderFeatureRecord(String.valueOf(orderDetailData.getGlobalOrderId()), FieldConstant.PUSH_MODE, masterMatchCondition.getPushMode());
//        }

        pushCommonObject.setDivisionMatchLevel(masterMatchCondition.getDivisionMatchLevel());


        pushCommonObject.setMasterSet(masterSet);
        return baseSelectMasterList;

    }


    public List<BaseSelectMaster> delayPushBaseSelect(OrderDetailData orderDetailData, BaseSelect baseSelect,String pushMode,Integer divisionMatchLevel){
        Long timeStamp = System.currentTimeMillis();
        String orderVersion = String.valueOf(timeStamp);
        MasterMatchCondition masterMatchCondition = buildMasterMatchCondition(orderDetailData,pushMode,orderVersion);
        masterMatchCondition.setDivisionMatchLevel(3);
        List<BaseSelectMaster> baseSelectMasterList = masterSelectorForEs.searchMasterByBaseSelectStrategy(masterMatchCondition,baseSelect);
       return baseSelectMasterList;
    }



    public MasterMatchCondition buildMasterMatchCondition(OrderDetailData orderDetailData, String pushMode, String orderVersion){
        MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
        masterMatchCondition.setPushMode(pushMode);
        masterMatchCondition.setOrderVersion(orderVersion);
        Long orderThirdDivisionId=orderDetailData.getThirdDivisionId();
        Long orderFourthDivisionId=orderDetailData.getFourthDivisionId();
        masterMatchCondition.setBusinessLineId(orderDetailData.getBusinessLineId());
        masterMatchCondition.setMasterOrderId(orderDetailData.getMasterOrderId());
        masterMatchCondition.setCategoryId(orderDetailData.getOrderCategoryId());
        masterMatchCondition.setServeType(orderDetailData.getOrderServeType());
        masterMatchCondition.setTechnologysInDemand(orderDetailData.getOrderTechniques());
        masterMatchCondition.setThirdDivisionId(orderThirdDivisionId);
        masterMatchCondition.setFourthDivisionId(orderFourthDivisionId);
        masterMatchCondition.setMasterBrand(orderDetailData.getOrderFrom());
        masterMatchCondition.setTechnologysInDemandSet(orderDetailData.getOrderTechniqueSet());
        masterMatchCondition.setServeIds(orderDetailData.getLv3ServeIds());
        masterMatchCondition.setNeesRigorousSelectionMaster(StringUtils.equals(orderDetailData.getOrderLabel(), "rigorous_selection") ? true : false);
        masterMatchCondition.setDivisionMatchLevel(judgeDivisionMatchLevel(orderThirdDivisionId, orderFourthDivisionId));
        masterMatchCondition.setSecondDivisionId(orderDetailData.getSecondDivisionId());
        masterMatchCondition.setOrderLngLat(orderDetailData.getOrderLngLat());
        masterMatchCondition.setPickupAddressLngLat(orderDetailData.getPickupAddressLngLat());


        /**
         * 2021-11-17 市场部需求特殊处理:定制家具/门,按照三级地址推送 BIGDATA-2408
         */
//		 排除专属
        regionModeForMarketingDepartment(masterMatchCondition,orderDetailData.getOrderCategoryId(),masterMatchCondition.getPushMode());

        /**
         * 2022-04-25
         * 获取专属师傅招募信息
         */
        masterMatchCondition.setRecruitIds(String.valueOf(orderDetailData.getRecruitId()));
        masterMatchCondition.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());



        return masterMatchCondition;
    }

    /**
     * 2021-11-17 市场部需求特殊处理:定制家具/门,按照三级地址推送
     * 6:门 7:全屋定制
     */
    private void regionModeForMarketingDepartment(MasterMatchCondition masterMatchCondition,Long categoryId,String pushMode) {
        if (("6".equals(categoryId)||"7".equals(categoryId)) && !PushMode.PRE_EXCLUSIVE.code.equals(pushMode)) {
            masterMatchCondition.setDivisionMatchLevel(3);
        }
    }



    /**
     * 判断区域匹配模式
     * @return
     */
    public Integer judgeDivisionMatchLevel(Long orderThirdDivisionId,Long orderFourthDivisionId) {
        Integer divisionMatchLevel = 3;
        if (orderFourthDivisionId != null && orderFourthDivisionId > 0) {
            divisionMatchLevel=4;
        }else if (orderThirdDivisionId != null && orderThirdDivisionId > 0) {
            divisionMatchLevel = 3;
        }else {
            divisionMatchLevel = 0;
        }
        return divisionMatchLevel;
    }


//    public Set<String> nearbyPushBaseSelect(MasterMatchCondition masterMatchCondition,String accountType,Long accountId,Long userId){
//        return otsMasterSelector.searchNearbyMaster(masterMatchCondition,accountType,accountId,userId);
//    }


    public List<BaseSelectMaster> searchMasterByBaseSelectStrategyIndex(MasterMatchCondition masterMatchCondition, RangeSelect rangeSelect, TechniqueSelect techniqueSelect, StatusSelect statusSelect) {
        return masterSelector.searchMasterByBaseSelectStrategy(masterMatchCondition,rangeSelect,techniqueSelect,statusSelect);
    }


    private boolean checkPushFullTimeMasterCity(OrderDetailData orderDetailData){

        try{
            if(StringUtils.isBlank(pushFullTimeMasterCityList)){
                return false;
            }

            if("0".equals(pushFullTimeMasterCityList)){
                return false;
            }


            if("all".equals(pushFullTimeMasterCityList)){
                return true;
            }



            List<Long> cityList = Arrays.stream(pushFullTimeMasterCityList.split(",")).map(Long::parseLong).collect(Collectors.toList());

            return cityList.contains(orderDetailData.getSecondDivisionId());
        }catch(Exception e){
            log.error("checkPushFullTimeMasterCity error",e);
        }

        return false;


    }


}
