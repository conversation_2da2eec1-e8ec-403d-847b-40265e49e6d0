package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.FilterStrategy;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.SnapshotRqt;
import com.wanshifu.master.order.push.domain.rqt.filterStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "filterStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface FilterStrategyApi {


    /**
     * 创建召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);


    /**
     * 修改召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 召回策略详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    FilterStrategy detail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 召回策略列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<FilterStrategy> list(@RequestBody @Valid ListRqt rqt);


    /**
     * 启用/禁用召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除召回策略
     *
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);

    @PostMapping(value="/selectBySnapshotIdList")
    List<FilterStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt);

}
