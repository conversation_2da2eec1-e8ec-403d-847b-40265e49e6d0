package com.wanshifu.master.order.push.domain.rqt.dynamicRoundsPush;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class ListRqt extends Pager {

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Long businessLineId;

    private Date pushTimeStart;

    private Date pushTimeEnd;

    private Long divisionId;

    private String categoryId;

    private String serveTypeId;

    private String appointType;

    private String orderFrom;

    private String orderNo;

    private String lackType;


    private List<Integer> serveTypeList;

    private List<Integer> appointTypeList;

    private List<String> orderFromList;

    private List<Long> categoryList;

    private Integer firstRoundsNumStart;

    private Integer firstRoundsNumEnd;

}
