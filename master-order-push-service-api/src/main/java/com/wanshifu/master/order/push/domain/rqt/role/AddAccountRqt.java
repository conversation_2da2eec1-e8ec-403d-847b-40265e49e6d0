package com.wanshifu.master.order.push.domain.rqt.role;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AddAccountRqt {

    /**
     * 角色id
     */
    @NotNull
    private Integer roleId;

    /**
     * 账号id列表
     */
    @NotEmpty
    private List<Long> accountIdList;


    private Long lastUpdateAccountId;
}
