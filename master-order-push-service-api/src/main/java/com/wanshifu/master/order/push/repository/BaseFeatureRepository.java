package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.mapper.BaseFeatureMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 基础特征Repository
 * <AUTHOR>
 */
@Repository
public class BaseFeatureRepository extends BaseRepository<BaseFeature> {

    @Resource
    private BaseFeatureMapper baseFeatureMapper;



    public List<BaseFeature> selectByFeatureType(String featureFor, Set<String> orderFeatureSet){
        Condition condition = new Condition(BaseFeature.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("featureCode", orderFeatureSet).andEqualTo("featureFor",featureFor);
        return this.selectByCondition(condition);
    }


    public List<Map<String,Object>> selectFeature(String sql){
        return baseFeatureMapper.selectFeature(sql);
    }



}