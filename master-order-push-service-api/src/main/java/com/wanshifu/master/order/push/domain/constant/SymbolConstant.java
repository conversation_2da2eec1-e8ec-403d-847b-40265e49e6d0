package com.wanshifu.master.order.push.domain.constant;

/**字符常量
 * 
 * <AUTHOR>
 *
 */
public interface SymbolConstant {
	/**
	 * 分隔符:下划线
	 */
	String UNDERLINE = "_";
	
	/**
	 * 字符:0
	 */
	String ZERO = "0";
	/**
	 * 字符:1
	 */
	String ONE = "1";

	/**
	 * 分隔符:逗号
	 */
	String COMMA = ",";
	
	/**
	 * 字符串:空串
	 */
	public static final String EMPTY_STRING  = "";
	
	/**
	 * 字符串:波浪线
	 */
	public static final String WAVE_STRING  = "~";
	
	/**
	 * 字符串:冒号
	 */
	public static final String COLON  = ":";
	
	/**
	 * 分隔符:换行/回车
	 */
	public static final String ROUND = "\r\n";
	
	/**
	 * 通配符:向左补零五位
	 */
	public static final String LEFT_ZORE_FIVE= "%05d";
	
	/**
	 * 通配符:匹配五位任意字符
	 */
	public static final String MATCH_FIVE_SYMBOL= "(.{5})";
	
	/**
	 * 正则:将每一组匹配到的字符拼接逗号
	 */
	public static final String SPLICE_COMMA= "$1,";
	
	
}
