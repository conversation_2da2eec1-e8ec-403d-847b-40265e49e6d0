package com.wanshifu.master.order.push.domain.rqt.compensateDistribute;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ListRqt extends Pager {

    /**
     * 业务线
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    private Long categoryId;

    private String orderPushFlag;

    private String compensateType;
}
