package com.wanshifu.master.order.push.domain.vo.longTailStrategy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.util.QlExpressUtil;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class Trigger {

    /**
     * 触发时间
     */
    @NotNull
    private TriggerIntervalTime triggerIntervalTime;

    /**
     * 触发条件
     */
    @NotNull
    @Valid
    private TriggerCondition triggerCondition;

    @Data
    public static class TriggerIntervalTime{
        @NotNull
        private Integer itemValue;
        @NotEmpty
        @ValueIn("<")
        private String term;
        @NotEmpty
        @ValueIn("push_minutes_interval")
        private String itemName;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class TriggerCondition{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *开启条件规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;

    }


    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源,offer_num:接单人数
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,offer_num,appoint_num")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }

    private static final List<String> preFeature=new ArrayList<String>(){
        {add("appoint_type");
        }};
    public String triggerQLExpression(){
        final JSONObject local = new JSONObject();
        final ArrayList<String> triggerParams = new ArrayList<>();
        final ArrayList<String> openParams = new ArrayList<>();

        final Trigger.TriggerCondition triggerCondition = getTriggerCondition();
        final Trigger.TriggerIntervalTime triggerIntervalTime = getTriggerIntervalTime();
//        final String triggerExpress = QlExpressUtil
//                .transitionQlExpress(triggerIntervalTime.getItemName(), triggerIntervalTime.getTerm()
//                        , String.valueOf(triggerIntervalTime.getItemValue()), Integer.class);

        triggerParams.add(triggerIntervalTime.getItemName());
        final ArrayList<String> preCondition = new ArrayList<>();

        final List<String> collect = triggerCondition.getItemList().stream().map(
                row -> {
                    if (preFeature.contains(row.getItemName())) {
                        preCondition.add(QlExpressUtil
                                .transitionQlExpress(row.getItemName(), row.getTerm(), row.getItemValue(), Integer.class));
                    }

                    openParams.add(row.getItemName());
                    return QlExpressUtil
                            .transitionQlExpress(row.getItemName(), row.getTerm(), row.getItemValue(), Integer.class);
                }
        ).collect(Collectors.toList());

        String openExpressions = QlExpressUtil.transitionQlExpressStr(triggerCondition.getCondition(), collect);

        String preExpressions = QlExpressUtil.transitionQlExpressStr(triggerCondition.getCondition(), preCondition);


        local.put(triggerIntervalTime.getItemName(),triggerIntervalTime.getItemValue());

        local.put("preExpressions",preExpressions);
        local.put("openExpression",openExpressions);
        local.put("openParams",openParams);
        return local.toJSONString();
    }

    public void parseTriggerQLExpression(String[] triggerQLExpression,List<String> triggerParams){
        final Trigger.TriggerCondition triggerCondition = getTriggerCondition();
        final Trigger.TriggerIntervalTime triggerIntervalTime = getTriggerIntervalTime();
        final String triggerTimeExpress = QlExpressUtil
                .transitionQlExpress(triggerIntervalTime.getItemName(), triggerIntervalTime.getTerm()
                        , String.valueOf(triggerIntervalTime.getItemValue()), Integer.class);

        triggerParams.add(triggerIntervalTime.getItemName());

        final List<String> collect = triggerCondition.getItemList().stream().map(
                row -> {
                    triggerParams.add(row.getItemName());
                    return QlExpressUtil
                            .transitionQlExpress(row.getItemName(), row.getTerm(), row.getItemValue(), Integer.class);
                }
        ).collect(Collectors.toList());

        String openExpressions = QlExpressUtil.transitionQlExpressStr(triggerCondition.getCondition(), collect);

        String finalExpressions= StrUtil.format("({}) and {}",openExpressions,triggerTimeExpress);

        triggerQLExpression[0]=finalExpressions;
    }
}
