package com.wanshifu.master.order.push.domain.common;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

@Data
public class OpenCondition {

    private String conditionExpression;

    private List<String> orderFeatureList;


    public OpenCondition(){

    }

    public OpenCondition(String conditionExpression,String openConditionRuleParams){
        this.conditionExpression = conditionExpression;
        if(StringUtils.isNotBlank(openConditionRuleParams)){
            this.orderFeatureList = Arrays.asList(openConditionRuleParams.split(","));
        }
    }


}
