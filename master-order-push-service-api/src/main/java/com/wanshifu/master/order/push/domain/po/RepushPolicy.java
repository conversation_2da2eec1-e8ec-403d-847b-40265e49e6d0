package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 重推机制表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "repush_policy")
public class RepushPolicy {

    /**
     * 重推机制id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "policy_id")
    private Long policyId;

    /**
     * 策略快照id
     */
    @Column(name = "snapshot_id")
    private Long snapshotId;

    /**
     * 重推机制名称
     */
    @Column(name = "policy_name")
    private String policyName;

    /**
     * 重推机制描述
     */
    @Column(name = "policy_desc")
    private String policyDesc;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 城市id，多个以逗号拼接
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 重推策略配置(JSON格式)
     */
    @Column(name = "strategy_combination")
    private String strategyCombination;

    /**
     * 状态，1：启用，0：禁用
     */
    @Column(name = "policy_status")
    private Integer policyStatus;

    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


    /**
     * 订单标识,normal:普通订单 ikea:宜家订单
     */
    @Column(name = "order_flag")
    private String orderFlag;


    /**
     * 版本
     */
    @Column(name = "version")
    private String version;
}