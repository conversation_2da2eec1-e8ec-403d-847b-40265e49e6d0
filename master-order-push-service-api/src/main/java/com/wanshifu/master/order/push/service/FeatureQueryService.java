//package com.wanshifu.master.order.push.service;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.wanshifu.framework.utils.CollectionUtils;
//import com.wanshifu.master.order.push.domain.constant.FieldConstant;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//
///**
// * 特征查询Service
// * <AUTHOR>
// */
//@Service
//@Slf4j
//public class FeatureQueryService {
//
//	@Resource
//	private TablestoreQuery tablestoreQuery;
//
//	public static JSONObject getSingleDataSetById(JSONObject result) {
//		if (result.size() == 1) {
//			JSONArray tmpArray = result.getJSONArray("1");
//			if (tmpArray.size() == 1) {
//				return tmpArray.getJSONObject(0);
//			} else {
//				return new JSONObject();
//			}
//		} else {
//			return new JSONObject();
//		}
//	}
//
//	public static Map<String, List<JSONObject>> getMasterFeatureMap(JSONObject result) {
//
//		Map<String, List<JSONObject>> masterFeatureMap=new HashMap<>();
//		if(result.size() != 0) {
//			JSONArray resultArray = result.getJSONArray("1");
//			for (int i = 0; i < resultArray.size(); i++) {
//				//师傅
//				JSONObject masterFeatureRow = resultArray.getJSONObject(i);
//				String masterId = masterFeatureRow.getString(FieldConstant.MASTER_ID);
//				if(!masterFeatureMap.containsKey(masterId)){
//					List<JSONObject> list = new ArrayList<>();
//					list.add(masterFeatureRow);
//					masterFeatureMap.put(masterId, list);
//
//				}else{
//					masterFeatureMap.get(masterId).add(masterFeatureRow);
//				}
//			}
//			return masterFeatureMap;
//		} else {
//			return masterFeatureMap;
//		}
//	}
//
//	/**
//	 * 通过DATA-SDK获取数据 dataset固定为1
//	 *
//	 * @return
//	 */
//	public JSONObject queryOrderFeature(Collection<String> dimensionColumns, List<String> indexColumns,
//			Map<String, Object> dimensionData,String instanceName,String tableName) {
//		JSONObject result = new JSONObject();
//		try {
//			JSONObject dataSetOne = generateQueryParam(dimensionColumns, indexColumns, dimensionData);
//			JSONArray queryParam = new JSONArray();
//			queryParam.add(dataSetOne);
//			result = tablestoreQuery.query(queryParam.toJSONString(),instanceName,tableName);
//		} catch (Exception e) {
//		}
//		return result;
//	}
//
//
//	public JSONObject queryMasterFeature(Collection<String> dimensionColumns, List<String> indexColumns,
//												  Map<String, Object> dimensionData, Set<String> masterIdSet, String instanceName,String tableName,
//	String multiValueDimension) {
//		JSONObject result = new JSONObject();
//		result.put("1", new JSONArray());
//		try {
//			if (CollectionUtils.isEmpty(masterIdSet)) {
//				return result;
//			}
//
//			if(StringUtils.isNotBlank(multiValueDimension)) {
//
//				List multiValueList = (List) dimensionData.get(multiValueDimension);
//				if(CollectionUtils.isEmpty(multiValueList)){
//					log.info(String.format("维度缺失，multiValueDimension: %s,dimensionData: %s",multiValueDimension, JSON.toJSONString(dimensionData)));
//					return result;
//				}
//
//
//				for (Object current : multiValueList) {
//
//					dimensionData.put(multiValueDimension,current);
//					queryMasterFeature(dimensionColumns, indexColumns,
//							dimensionData, masterIdSet, instanceName, tableName,result);
//				}
//
//				dimensionData.put(multiValueDimension,multiValueList);
//
//			}else{
//				queryMasterFeature(dimensionColumns, indexColumns,
//						dimensionData, masterIdSet, instanceName, tableName,result);
//			}
//
//		} catch (Exception e) {
//			log.error("queryMasterFeature error",e);
//		}
//		return result;
//	}
//
//
//	private void queryMasterFeature(Collection<String> dimensionColumns, List<String> indexColumns,
//				 Map<String, Object> dimensionData, Set<String> masterIdSet, String instanceName,String tableName,JSONObject result){
//		HashSet<String> questSet = new HashSet<>();
//
//		for (String string : masterIdSet) {
//			questSet.add(string);
//			if (questSet.size() >= 400) {
//				queryMasterFeatureBatch(dimensionColumns,indexColumns,dimensionData,questSet,instanceName,tableName,result);
//				questSet = new HashSet<>();
//			}
//		}
//		if (CollectionUtils.isNotEmpty(questSet)) {
//			queryMasterFeatureBatch(dimensionColumns,indexColumns,dimensionData,questSet,instanceName,tableName,result);
//		}
//	}
//
//
//	public JSONObject queryMasterFeatureBatch( Collection<String> dimensionColumns, List<String> indexColumns,
//										 Map<String, Object> dimensionData, Set<String> masterIdSet,String instanceName, String tableName,JSONObject result) {
//
//		try {
//			if (CollectionUtils.isEmpty(masterIdSet)) {
//				return result;
//			}
//			HashSet<String> questSet = new HashSet<>();
//			for (String string : masterIdSet) {
//				questSet.add(string);
//				if (questSet.size() >= 400) {
//					JSONObject dataSetOne = generateBatchQueryParam(dimensionColumns, indexColumns, dimensionData,
//							"master_id", questSet);
//					JSONArray queryParam = new JSONArray();
//					queryParam.add(dataSetOne);
//					JSONObject empResult = tablestoreQuery.query(queryParam.toJSONString(),instanceName,tableName);
//					if(empResult.size() > 0){
//						result.getJSONArray("1").addAll(empResult.getJSONArray("1"));
//					}
//					questSet = new HashSet<>();
//				}
//			}
//			if (CollectionUtils.isNotEmpty(questSet)) {
//				JSONObject dataSetOne = generateBatchQueryParam(dimensionColumns, indexColumns, dimensionData,
//						"master_id", questSet);
//				JSONArray queryParam = new JSONArray();
//				queryParam.add(dataSetOne);
//				JSONObject empResult=tablestoreQuery.query(queryParam.toJSONString(),instanceName,tableName);
//				if(empResult.size() > 0) {
//					result.getJSONArray("1").addAll(empResult.getJSONArray("1"));
//				}
//			}
//		} catch (Exception e) {
//			log.error("queryMasterFeatureBatch error",e);
//		}
//		return result;
//	}
//
//	/**
//	 * 生成sdk查询参数
//	 *
//	 * @return
//	 */
//	private JSONObject generateQueryParam(Collection<String> dimensionColumns, Collection<String> indexColumns,
//			Map<String, Object> dimensionData) throws Exception {
//		JSONObject dataset = new JSONObject();
//		dataset.put("dataset_id", 1);
//		dataset.put("conditions", generateConditionsByIndexColumns(dimensionColumns, dimensionData));
//		dataset.put("dimension_columns", dimensionColumns);
//		dataset.put("index_columns", indexColumns);
//		return dataset;
//	}
//
//	/**
//	 * 生成批量查询参数
//	 *
//	 * @return
//	 */
//	private JSONObject generateBatchQueryParam(Collection<String> dimensionColumns, Collection<String> indexColumns,
//			Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue)
//			throws Exception {
//		JSONObject dataset = new JSONObject();
//		dataset.put("dataset_id", 1);
//		dataset.put("conditions", generateBatchConditionsByIndexColumns(dimensionColumns, dimensionData,
//				batchDimensionColumn, batchColumnValue));
//		dataset.put("dimension_columns", dimensionColumns);
//		dataset.put("index_columns", indexColumns);
//		return dataset;
//	}
//
//	/**
//	 * 生成conditon查询条件
//	 *
//	 * @return
//	 */
//	private String generateConditionsByIndexColumns(Collection<String> dimensionColumns,
//			Map<String, Object> dimensionData) {
//		StringBuilder conditonString = new StringBuilder();
//		for (String column : dimensionColumns) {
//			Object dimensionValueObject = dimensionData.get(column);
//			if (dimensionValueObject == null) {
//				if (!FieldConstant.ORDER_GOODS_ID.equals(column)&&!FieldConstant.ENTERPRISE_ID.equals(column)) {
//				}
//				log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
//				throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
//			}
//			String dimensionValue = dimensionValueObject.toString();
//			conditonString.append(column);
//			conditonString.append(" = ");
//			if (dimensionValueObject.getClass() == String.class) {
//				conditonString.append("'");
//				conditonString.append(dimensionValue);
//				conditonString.append("'");
//			} else {
//				conditonString.append(dimensionValue);
//			}
//			conditonString.append(" and ");
//		}
//		if (conditonString.length() != 0) {
//			conditonString.setLength(conditonString.length() - 5);
//		}
//		return conditonString.toString();
//	}
//
//	/**
//	 * 生成batch condition查询条件
//	 *
//	 * @return
//	 */
//	private String generateBatchConditionsByIndexColumns(Collection<String> dimensionColumns,
//			Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue) {
//		StringBuilder conditonString = new StringBuilder();
//		for (String column : dimensionColumns) {
//			if (column.equals(batchDimensionColumn)) {
//				continue;
//			}
//			Object dimensionValueObject = dimensionData.get(column);
//			if (dimensionValueObject == null) {
//				log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
//				throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
//			}
//			String dimensionValue = dimensionValueObject.toString();
//			conditonString.append(column);
//			conditonString.append(" = ");
//			if (dimensionValueObject.getClass() == String.class) {
//				conditonString.append("'");
//				conditonString.append(dimensionValue);
//				conditonString.append("'");
//			} else {
//				conditonString.append(dimensionValue);
//			}
//			conditonString.append(" and ");
//		}
//		String batchValue = StringUtils.join(batchColumnValue, ",");
//		conditonString.append(batchDimensionColumn).append(" in (").append(batchValue).append(")");
//		return conditonString.toString();
//	}
//
//
//}
