package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 推单模式
 * <AUTHOR>
 */
public enum PushMode {

	/**
	 * 普通推单
	 */
	NORMAL("normal"),

	/**
	 * 代理商推单
	 */
	AGENT_MASTER("agent_master"),


    /**
     * 特殊人群推单
     */
    SPECIAL_GROUP("special_group"),


    /**
	 * 订单包推单
	 */
	PACKAGE_ORDER("package_order"),

	/**
	 * 专属推单
	 */
	PRE_EXCLUSIVE("pre_exclusive"),


	/**
	 * 测量后续订单推单
	 */
	MEASURE_MASTER_PRIVILEGE("measure_master_privilege"),

	/**
	 * 品牌师傅推单
	 */
	BRAND_MASTER("brand_master"),

	/**
	 * 品牌师傅推单
	 */
	BRAND_ES("brand_es"),

	/**
	 * 非智能推单
	 */
	NON_INTELLIGENT("non_intelligent"),

	/**
	 * 直接指派
	 */
	DIRECT_APPOINT_MASTER("direct_appoint_master"),


	AGREEMENT_MASTER("agreement_master"),


	RECOMMEND_LIST("recommend_list"),

	/**
	 * 合作经营师傅
	 */
	COOPERATION_BUSINESS_MASTER("cooperation_business_master"),

	/**
	 * 家庭协议师傅
	 */
	FAMILY_AGREEMENT_MASTER("family_agreement_master"),


	/**
	 * 作业帮
	 */
	COLLECT_CONTRACT_MASTER("collect_contract_master"),


	/**
	 * 主力师傅推单
	 */
	NEW_MODEL_SINGLE("new_model_single"),

	/**
	 * 样板推单（专属推荐）
	 */
	NEW_MODEL_MASTER("new_model_master"),


	AFRESH_NEW_MODEL_SINGLE("afresh_new_model_single"),

	/**
	 * 总包直接指派
	 */
	ENTERPRISE_APPOINT("enterprise_appoint"),

	/**
	 * 订单包师傅
	 */
	ORDER_PACKAGE_MASTER("order_package_master"),

	/**
	 * 专属师傅
	 */
	EXCLUSIVE_MASTER("exclusive_master"),


	/**
	 * 匹配扶持师傅
	 */
	SUPPORT_MASTER("support_master"),


	/**
	 金牌维修师傅
	 */
	GOLD_MEDAL_MASTER("gold_medal_master"),


	TECHNIQUE_VERIFY_MASTER_DISPATCH("technique_verify_master_dispatch"),

	TECHNIQUE_VERIFY_MASTER("technique_verify_master"),

    /**
     * 技能验证后师傅派单
     */
    AFTER_TECHNIQUE_VERIFY_MASTER("after_technique_verify_master"),


	/**
	 * 总包直接指派技能验证师傅（新师傅）
	 */
	ENTERPRISE_TECHNIQUE_VERIFY_MASTER("enterprise_technique_verify_master"),


	/**
	 * 全时师傅派单
	 */
	FULL_TIME_MASTER_DISPATCH("full_time_master_dispatch"),

	/**
	 * 全时师傅推单
	 */
	FULL_TIME_MASTER("full_time_master"),


	/**
	 * 样板主力师傅派单（非锁单）
	 */
	NEW_MODEL_DISPATCH("new_model_dispatch");






	public final String code;

	PushMode(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}


	@Override
	public String toString(){
		return code;
	}


	private static final Map<String, PushMode> valueMapping = new HashMap<>((int) (PushMode.values().length / 0.75));

	static {
		for (PushMode instance : PushMode.values()) {
			valueMapping.put(instance.code, instance);
		}
	}


	public static PushMode asCode(String code) {
		return valueMapping.get(code);
	}
}
