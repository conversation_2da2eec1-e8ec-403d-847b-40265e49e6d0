package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
public class PortPushRuleDTO {


    @NotBlank
    @ValueIn("push_interval_time,push_master_num")
    private String condition;

    private Integer intervalTime;

    private Integer offerNum;

    private Integer pushNum;

    @NotNull
    private Integer weight;
    
}
