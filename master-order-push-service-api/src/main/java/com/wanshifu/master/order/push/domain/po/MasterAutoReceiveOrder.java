package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


@Data
@Table(name = "master_auto_receive_order")
public class MasterAutoReceiveOrder {


    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 自动接单师傅评分列表
     */
    @Column(name = "score_master_list")
    private String scoreMasterList;

    /**
     * 参与自动接单师傅列表
     */
    @Column(name = "wait_auto_receive_master_list")
    private String waitAutoReceiveMasterList;

    /**
     * 自动接单师傅成功列表
     */
    @Column(name = "auto_receive_master_list")
    private String autoReceiveMasterList;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
    
}
