package com.wanshifu.master.order.push.domain.enums;

/**
 * 技能初筛类型
 * <AUTHOR>
 */
public enum TechniqueSelectType {

    /**
     * 过滤不符合订单服务映射技能的师傅
     */
    FILTER_OUT_NO_MATCH_TECHNIQUE("filter_out_no_match_technique"),
    /**
     * 过滤不是宜家标签的师傅
     */
    FILTER_OUT_NO_MATCH_IKEA("filter_out_no_match_ikea"),

    /**
     * 过滤技能匹配的师傅（长尾单）
     */
    FILTER_OUT_MATCH_TECHNIQUE("filter_out_match_ikea"),
    ;

    public final String code;

    TechniqueSelectType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }

}
