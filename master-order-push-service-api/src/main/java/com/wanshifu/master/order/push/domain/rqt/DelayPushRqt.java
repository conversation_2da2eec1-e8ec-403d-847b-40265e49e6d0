package com.wanshifu.master.order.push.domain.rqt;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.domain.common.BaseSelect;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import lombok.Data;

/**
 * 分轮推送消息体对象
 * <AUTHOR>
 */
@Data
public class DelayPushRqt {

    /**
     * 用户订单id
     */
    private Long userOrderId;

    /**
     * 师傅订单id
     */
    private Long masterOrderId;

    /**
     * 总包订单id
     */
    private Long enterpriseOrderId;

    /**
     * 全局订单id
     */
    private Long globalOrderId;

    /**
     * 推单版本号
     */
    private String orderVersion;

    /**
     * 推单模式
     */
    private String pushMode;

    /**
     * 推单配置
     */
    private PushConfig pushConfig;

    /**
     * 订单基础数据
     */
    private OrderDetailData orderDetailData;

    /**
     * 初筛策略id
     */
    private BaseSelect baseSelect;

    /**
     * 推单特征
     */
    JSONObject commonFeature;
}
