package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.NewMasterOrderDistributeStrategy;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.rqt.newMasterOrderDistributeStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "newMasterOrderDistributeStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface NewMasterOrderDistributeStrategyApi {


    @PostMapping("create")
    Integer create(@Valid @RequestBody CreateRqt rqt);

    @PostMapping("update")
    Integer update(@Valid @RequestBody UpdateRqt rqt);

    @PostMapping("enable")
    Integer enable(@Valid @RequestBody EnableRqt rqt);

    @PostMapping("delete")
    Integer delete(@Valid @RequestBody DeleteRqt rqt);

    @PostMapping("detail")
    DetailResp detail(@Valid @RequestBody DetailRqt rqt);

    @PostMapping("list")
    SimplePageInfo<NewMasterOrderDistributeStrategy> list(@Valid @RequestBody ListRqt rqt);
}
