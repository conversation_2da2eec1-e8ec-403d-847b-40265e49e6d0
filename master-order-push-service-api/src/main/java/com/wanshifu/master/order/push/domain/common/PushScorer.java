package com.wanshifu.master.order.push.domain.common;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 推单评分器
 * <AUTHOR>
 */
@Data
@Slf4j
public class PushScorer {


    private ExpressRunner expressRunner;

    private String conditionExpression;

    private String scoreExpression;

    private String scorerName;

    private List<String> orderFeatureList;

    private List<String> masterFeatureList;


    public PushScorer(){

    }


    public PushScorer(ExpressRunner expressRunner,String conditionExpression,String scoreExpression, String scorerName,String orderFeature,String masterFeature){
        this.expressRunner = expressRunner;
        this.conditionExpression = conditionExpression;
        this.scoreExpression = scoreExpression;
        this.scorerName = scorerName;
        if(StringUtils.isNotBlank(orderFeature)){
            this.orderFeatureList = Arrays.asList(orderFeature.split(","));
        }
        if(StringUtils.isNotBlank(masterFeature)){
            this.masterFeatureList = Arrays.asList(masterFeature.split(","));
        }
    }

    public BigDecimal execute(DefaultContext<String,Object> scoreContext){
        try{
            Object score = expressRunner.execute(scoreExpression,scoreContext,null,true,false);
            if(score instanceof Integer){
                return BigDecimal.valueOf((Integer)score);
            }else if(score instanceof Double){
                return BigDecimal.valueOf((Double)score);
            }
        }catch(Exception e){
            log.error("pushScorer.execute is error!scoreContext=【{}】,scoreExpression=【{}】",scoreContext,scoreExpression);
        }
        return BigDecimal.ZERO;
    }

}
