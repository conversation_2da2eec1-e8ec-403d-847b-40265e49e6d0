package com.wanshifu.master.order.push.domain.enums;


/**
 * 计算类型枚举
 * <AUTHOR>
 */
public enum CalculateType {


    /**
     * 求和
     */
    SUM("sum"),

    /**
     * 求最小值
     */
    MIN("min"),

    /**
     * 求最大值
     */
    MAX("max");


    public final String code;

    CalculateType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }

}
