package com.wanshifu.master.order.push.domain.rqt.orderMatchRoute;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

@Data
public class CreateOrderMatchRouteTimeRqt {

    @NotNull
    private Integer businessLineId;

    @NotBlank
    private String categoryIds;

    @NotBlank
    private String appointTypes;

    @NotBlank
    @NotNull
    @ValueIn("appointTime,receiveTime")
    private String settingType;

    @NotNull
    private Integer settingTime;

    @NotNull
    private Integer settingNum;

    private Long createAccountId;

}
