package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * 推单列表
 * <AUTHOR>
 * @date 2023-07-12 10:31:01
 */
@Data
@ToString
@Table(name = "push_order_list")
public class PushOrderList {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;


    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;


    /**
     * 全局订单id
     */
    @Column(name = "global_order_id")
    private Long globalOrderId;


    /**
     * 全局订单id
     */
    @Column(name = "account_type")
    private String accountType;

    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 城市id
     */
    @Column(name = "second_division_id")
    private Long secondDivisionId;

    /**
     * 区县id
     */
    @Column(name = "third_division_id")
    private Long thirdDivisionId;

    /**
     * 街道id
     */
    @Column(name = "fourth_division_id")
    private Long fourthDivisionId;

    /**
     * 子级商品id
     */
    @Column(name = "child_goods_category_ids")
    private String childGoodsCategoryIds;


    /**
     * 下单时间
     */
    @Column(name = "order_create_time")
    private Date orderCreateTime;

    /**
     * 推单时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 服务类型id
     */
    @Column(name = "serve_type_id")
    private Integer serveTypeId;


    /**
     * 订单来源
     */
    @Column(name = "order_from")
    private String orderFrom;


    /**
     * 类目id
     */
    @Column(name = "category_id")
    private Integer categoryId;


    /**
     * 订单来源类型
     */
    @Column(name = "order_from_type")
    private String orderFromType;

    /**
     * 指派模式
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 推单策略路由
     */
    @Column(name = "push_strategy")
    private String pushStrategy;

    /**
     * 初筛师傅数
     */
    @Column(name = "base_select_master_num")
    private Integer baseSelectMasterNum;

    /**
     * 召回后师傅数
     */
    @Column(name = "master_num_after_filter")
    private Integer masterNumAfterFilter;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务
     */
    @Column(name = "business_line_id")
    private Long businessLineId;

    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
