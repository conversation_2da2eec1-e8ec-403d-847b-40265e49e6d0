package com.wanshifu.master.order.push.domain.rqt.pushexporttask;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/28 21:43
 */
@Data
public class ListPushExportTaskRqt extends Pager {

    /**
     * 任务提交时间起
     */
    @NotNull
    private Date createTimeStart;

    /**
     * 任务提交时间止
     */
    @NotNull
    private Date createTimeEnd;
}
