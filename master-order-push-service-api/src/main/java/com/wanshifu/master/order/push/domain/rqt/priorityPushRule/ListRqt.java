package com.wanshifu.master.order.push.domain.rqt.priorityPushRule;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.Date;

@Data
public class ListRqt extends Pager {

    private String ruleName;

    private Integer businessLineId;

//    private String orderFrom;
//
//    private Integer appointType;

    private Long cityId;

    private String categoryIds;

    private Date createStartTime;

    private Date createEndTime;

}
