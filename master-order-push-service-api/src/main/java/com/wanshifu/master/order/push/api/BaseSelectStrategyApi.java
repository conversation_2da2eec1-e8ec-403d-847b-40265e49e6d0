package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategy;
import com.wanshifu.master.order.push.domain.po.BaseSelectStrategySnapshot;
import com.wanshifu.master.order.push.domain.po.FilterStrategySnapshot;
import com.wanshifu.master.order.push.domain.rqt.baseSelectStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "baseSelectStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface BaseSelectStrategyApi {

    /**
     * 初筛策略列表
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<BaseSelectStrategy> list(@RequestBody @Valid ListRqt rqt);
    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/modify")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    BaseSelectStrategy detail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 启用/禁用初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    int enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);


    @PostMapping(value="/selectBySnapshotIdList")
    List<BaseSelectStrategySnapshot> selectBySnapshotIdList(@RequestBody @Valid SnapshotRqt rqt);
}
