package com.wanshifu.master.order.push.domain.rqt.repushPolicy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.repushPolicy.PushStrategyVo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  创建重推机制.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-14 15:21
 */
@Data
public class CreateRqt {


    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 机制名称
     */
    @NotEmpty
    private String policyName;

    /**
     * 机制描述
     */
    private String policyDesc;

    /**
     * 适用类目，多个用逗号拼接 all:所有类目
     */
    @NotEmpty
    private String categoryIds;

    /**
     * 城市id，多个用逗号拼接 all:全国城市
     */
    @NotEmpty
    private String cityIds;


    /**
     * 重推机制策略
     */
    @NotNull
    @Valid
    private List<PushStrategyVo> pushStrategy;

    private Long createAccountId;


    @ValueIn("normal,ikea")
    private String orderFlag;

}