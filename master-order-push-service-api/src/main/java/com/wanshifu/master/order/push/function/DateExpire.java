package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;

import java.util.Date;


/**
 * <AUTHOR>
 */
public class DateExpire  extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list.length != 1) {
            throw new Exception("操作数异常");
        }

        if(list[0] == null){
            return true;
        }else{
            Date pushExpireDate = new Date((Long) list[0]);
            return new Date().compareTo(pushExpireDate) >= 0;
        }

    }

}
