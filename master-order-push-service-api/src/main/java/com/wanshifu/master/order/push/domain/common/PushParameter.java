package com.wanshifu.master.order.push.domain.common;

import javax.annotation.Generated;
/**
 * 推送参数
 * <AUTHOR>
 *
 */
public class PushParameter {
	private Long userOrderId;
	private Long masterOrderId;
	private Long globalOrderId;
	private Long enterpriseOrderId;
	/**
	 * 推单开关
	 */
	private boolean distibutorSwitch;
	
	public Long getUserOrderId() {
		return userOrderId;
	}
	
	public Long getMasterOrderId() {
		return masterOrderId;
	}
	public Long getGlobalOrderId() {
		return globalOrderId;
	}
	public Long getEnterpriseOrderId() {
		return enterpriseOrderId;
	}
	
	public boolean isDistibutorSwitch() {
		return distibutorSwitch;
	}
	
	@Generated("SparkTools")
	private PushParameter(Builder builder) {
		this.userOrderId = builder.userOrderId;
		this.masterOrderId = builder.masterOrderId;
		this.globalOrderId = builder.globalOrderId;
		this.enterpriseOrderId = builder.enterpriseOrderId;
		this.distibutorSwitch = builder.distibutorSwitch;
	}
	/**
	 * Creates builder to build {@link PushParameter}.
	 * @return created builder
	 */
	@Generated("SparkTools")
	public static Builder builder() {
		return new Builder();
	}
	/**
	 * Builder to build {@link PushParameter}.
	 */
	@Generated("SparkTools")
	public static final class Builder {
		private Long userOrderId;
		private Long masterOrderId;
		private Long globalOrderId;
		private Long enterpriseOrderId;
		private boolean distibutorSwitch;
		private Builder() {
		}
		
		public Builder setUserOrderId(Long userOrderId) {
			this.userOrderId = userOrderId;
			return this;
		}

		public Builder setMasterOrderId(Long masterOrderId) {
			this.masterOrderId = masterOrderId;
			return this;
		}

		public Builder setGlobalOrderId(Long globalOrderId) {
			this.globalOrderId = globalOrderId;
			return this;
		}

		public Builder setEnterpriseOrderId(Long enterpriseOrderId) {
			this.enterpriseOrderId = enterpriseOrderId;
			return this;
		}

		public Builder setDistibutorSwitch(boolean distibutorSwitch) {
			this.distibutorSwitch = distibutorSwitch;
			return this;
		}

		public PushParameter build() {
			return new PushParameter(this);
		}
	}
	@Override
	public String toString() {
		return "PushParameter [userOrderId=" + userOrderId + ", masterOrderId=" + masterOrderId + ", globalOrderId="
				+ globalOrderId + ", enterpriseOrderId=" + enterpriseOrderId + ", orderCategoryId=" 
				+ ", orderServeTypeId="  + ", distibutorSwitch=" + distibutorSwitch
				+  "]";
	}
	

	
	
	
}
