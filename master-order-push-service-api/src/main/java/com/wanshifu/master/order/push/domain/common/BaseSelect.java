package com.wanshifu.master.order.push.domain.common;

import com.wanshifu.master.order.push.domain.dto.RangeSelect;
import com.wanshifu.master.order.push.domain.dto.ServeDataSelect;
import com.wanshifu.master.order.push.domain.dto.StatusSelect;
import com.wanshifu.master.order.push.domain.dto.TechniqueSelect;
import lombok.Data;


/**
 * 初筛策略规则实体类
 * <AUTHOR>
 */
@Data
public class BaseSelect {
    private String strategyName;
    private Long snapshotId;
    private RangeSelect rangeSelect;
    private TechniqueSelect techniqueSelect;
    private StatusSelect statusSelect;
    private ServeDataSelect serveDataSelect;

    public BaseSelect(){

    }

    public BaseSelect(Long snapshotId,String strategyName,RangeSelect rangeSelect,
                      TechniqueSelect techniqueSelect,StatusSelect statusSelect,ServeDataSelect serveDataSelect){
        this.snapshotId=snapshotId;
        this.strategyName = strategyName;
        this.rangeSelect = rangeSelect;
        this.techniqueSelect = techniqueSelect;
        this.statusSelect = statusSelect;
        this.serveDataSelect = serveDataSelect;
    }
}
