package com.wanshifu.master.order.push.domain.resp.baseSelectStrategy;

import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.OpenConditionVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.RangeSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.StatusSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.TechniqueSelectVo;
import lombok.Data;

/**
 * 描述 :  初筛策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 策略状态
     */
    private Integer strategyStatus;

    /**
     * 开启条件
     */
    private OpenConditionVo openCondition;

    /**
     * 范围初筛
     */
    private RangeSelectVo rangeSelect;

    /**
     * 技能初筛
     */
    private TechniqueSelectVo techniqueSelect;

    /**
     * 状态初筛
     */
    private StatusSelectVo statusSelect;
}