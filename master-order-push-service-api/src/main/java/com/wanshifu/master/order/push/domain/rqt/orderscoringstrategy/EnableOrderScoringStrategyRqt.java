package com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 17:16
 */
@Data
public class EnableOrderScoringStrategyRqt {

    @NotNull
    private Integer strategyId;

    @NotNull
    @ValueIn("1,0")
    private Integer strategyStatus;

    private Long updateAccountId;

}
