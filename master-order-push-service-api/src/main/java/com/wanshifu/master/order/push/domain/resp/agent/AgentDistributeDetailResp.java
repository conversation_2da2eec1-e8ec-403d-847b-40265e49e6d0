package com.wanshifu.master.order.push.domain.resp.agent;

import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.rqt.agent.AddAgentDistributeStrategyRqt;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:56
 */
@Data
public class AgentDistributeDetailResp {

    private Integer strategyId;

    private Long agentId;

    private String agentName;

    private String masterSourceType;

    private String strategyName;

    private String strategyDesc;

    private Long cityDivisionId;

    @Deprecated
    private Long scoringStrategyId;

    @Deprecated
    private String scoringStrategyName;

    private List<AddAgentDistributeStrategyRqt.NonEffectiveTime> nonEffectiveTimeList;

    private String serveIds;

    private String serveIdArray;

    @Deprecated
    private String thirdDivisionIds;

    private String fourthDivisionIds;

    private AddAgentDistributeStrategyRqt.PushStrategy pushStrategy;

    private String distributeRule;

    private String distributePriority;

    private Integer strategyStatus;

    @Deprecated
    private AgentInfo agentInfo;
}
