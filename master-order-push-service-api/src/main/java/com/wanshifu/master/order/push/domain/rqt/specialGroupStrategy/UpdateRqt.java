package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 更新特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "更新特殊人群策略请求类")
@Data
public class UpdateRqt extends CreateRqt {

    @ApiModelProperty(value = "策略id", required = true)
    @NotNull(message = "策略id不能为空")
    private Long strategyId;

    @ApiModelProperty(value = "更新人id")
    private Long updateAccountId;
}
