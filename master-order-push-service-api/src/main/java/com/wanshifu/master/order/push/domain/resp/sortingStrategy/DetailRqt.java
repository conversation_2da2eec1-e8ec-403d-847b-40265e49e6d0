package com.wanshifu.master.order.push.domain.resp.sortingStrategy;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  精排策略详情Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailRqt {

    /**
     * 策略id
     */
    @NotNull
    private Long strategyId;

    public DetailRqt(){

    }


    public DetailRqt(Long strategyId){
        this.strategyId = strategyId;
    }
}