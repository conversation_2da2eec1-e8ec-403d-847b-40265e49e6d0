package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Data
public class OrderMatchRoute {

    /**
     * 路由id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "route_id")
    private Integer routeId;

    /**
     * 路由名称
     */
    @Column(name = "route_name")
    private String routeName;

    /**
     * 路由描述
     */
    @Column(name = "route_desc")
    private String routeDesc;

    /**
     * 订单标签
     */
    @Column(name = "order_push_flag")
    private String orderPushFlag;

    /**
     * 优先路由规则
     */
    @Column(name = "order_priority_match_rule")
    private String orderPriorityMatchRule;

    /**
     * 备用路由规则
     */
    @Column(name = "order_standby_match_rule")
    private String orderStandbyMatchRule;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
