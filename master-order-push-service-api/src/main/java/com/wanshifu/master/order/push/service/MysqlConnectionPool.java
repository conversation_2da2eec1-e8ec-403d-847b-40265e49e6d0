package com.wanshifu.master.order.push.service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


import com.alibaba.druid.pool.DruidDataSource;

/**
 * mysql数据库连接池
 * <AUTHOR>
 */
public class MysqlConnectionPool {

//	private static final Logger LOGGER = LoggerFactory.getLogger(MysqlConnectionPool.class);

	private static DruidDataSource dataSource;

	/**
	 * 获取MYSQL的JDBC连接
	 *
	 * @return
	 * @throws Exception
	 */
//	public static Connection getConnection() throws Exception {
//		if (dataSource != null) {
//			return dataSource.getConnection();
//		}
//		synchronized (MysqlConnectionPool.class) {
//			if (dataSource != null) {
//				return dataSource.getConnection();
//			}
//			dataSource = new DruidDataSource();
//			dataSource.setDriverClassName(ConfigConstant.DRIVER);
//			if (PhraseConstant.PROD.equals(MyEnvironment.env()) || PhraseConstant.GRAY.equals(MyEnvironment.env())) {
//				dataSource.setUsername(ConfigConstant.PROD_ENV_USERNAME);
//				dataSource.setPassword(ConfigConstant.PROD_ENV_PASSWORD);
//				dataSource.setUrl(ConfigConstant.PROD_ENV_JDBC_URL);
//			} else if (PhraseConstant.TEST.equals(MyEnvironment.env())) {
//				dataSource.setUsername(ConfigConstant.TEST_ENV_USERNAME);
//				dataSource.setPassword(ConfigConstant.TEST_ENV_PASSWORD);
//				dataSource.setUrl(ConfigConstant.TEST_ENV_JDBC_URL);
//			} else {
//				dataSource.setUsername(ConfigConstant.LOCAL_ENV_USERNAME);
//				dataSource.setPassword(ConfigConstant.LOCAL_ENV_PASSWORD);
//				dataSource.setUrl(ConfigConstant.LOCAL_ENV_JDBC_URL);
//			}
//			dataSource.setInitialSize(5); // 初始化连接
//			dataSource.setMinIdle(1); // 最小空闲连接
//			dataSource.setMaxActive(500); // 最大连接数量
//			dataSource.setMaxWait(6000);// 连接等待超时的时间
//			dataSource.setPoolPreparedStatements(false);
//			return dataSource.getConnection();
//		}
//	}

	/**
	 * 关闭MYSQL连接
	 *
	 * @param connection
	 * @param preparedStatement
	 * @param resultSet
	 */
	public static void closeCon(Connection connection, PreparedStatement preparedStatement, ResultSet resultSet) {
		try {
			if (resultSet != null) {
				resultSet.close();
			}
			if (preparedStatement != null) {
				preparedStatement.close();
			}
			if (connection != null) {
				connection.close();
			}
		} catch (SQLException e) {
//			LOGGER.error("MysqlConnectionPool close resultSet error:[{}]", e.getMessage());
		}
	}
}
