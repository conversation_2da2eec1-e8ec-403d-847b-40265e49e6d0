package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.Date;

@Data
public class OrderLock {

    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 城市id
     */
    @Column(name = "city_division_id")
    private Long cityDivisionId;

    /**
     * 锁单数量
     */
    @Column(name = "order_grab_lock_cnt")
    private Long orderGrabLockCnt;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
