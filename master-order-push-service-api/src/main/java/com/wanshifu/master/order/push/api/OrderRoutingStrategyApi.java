package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderRoutingStrategy;
import com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "orderRoutingStrategy",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface OrderRoutingStrategyApi {

    /**
     * 长尾策略组列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<OrderRoutingStrategy> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 创建长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    Integer create(@RequestBody @Valid CreateRqt rqt);


    /**
     * 修改长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    Integer update(@RequestBody @Valid UpdateRqt rqt);



    /**
     * 策略组详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    OrderRoutingStrategy detail(@RequestBody @Valid DetailRqt rqt);

    @PostMapping("/enable")
    Integer enable(@RequestBody @Valid EnableRqt rqt);


    @PostMapping("/delete")
    Integer delete(@RequestBody @Valid DeleteRqt rqt);



    }
