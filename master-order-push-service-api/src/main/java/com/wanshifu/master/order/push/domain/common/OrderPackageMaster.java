package com.wanshifu.master.order.push.domain.common;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public class OrderPackageMaster {
    String masterId;
    Integer remainPushOrderNumber;
    String coreDivisionId;
    String bindPackageId;
    String bindPackageConfigId;
    Long bindPackageCreateTime;

    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    public Integer getRemainPushOrderNumber() {
        return remainPushOrderNumber;
    }

    public void setRemainPushOrderNumber(Integer remainPushOrderNumber) {
        this.remainPushOrderNumber = remainPushOrderNumber;
    }

    public String getCoreDivisionId() {
        return coreDivisionId;
    }

    public void setCoreDivisionId(String coreDivisionId) {
        this.coreDivisionId = coreDivisionId;
    }

    public String getBindPackageId() {
        return bindPackageId;
    }

    public void setBindPackageId(String bindPackageId) {
        this.bindPackageId = bindPackageId;
    }

    public String getBindPackageConfigId() {
        return bindPackageConfigId;
    }

    public void setBindPackageConfigId(String bindPackageConfigId) {
        this.bindPackageConfigId = bindPackageConfigId;
    }

    public Long getBindPackageCreateTime() {
        return bindPackageCreateTime;
    }

    public void setBindPackageCreateTime(Long bindPackageCreateTime) {
        this.bindPackageCreateTime = bindPackageCreateTime;
    }

    public static final class GroupMasterBuilder {
        String masterId;
        Integer remainPushOrderNumber;
        String coreDivisionId;
        String bindPackageId;
        String bindPackageConfigId;
        Long bindPackageCreateTime;

        private GroupMasterBuilder() {
        }

        public static GroupMasterBuilder aGroupMaster() {
            return new GroupMasterBuilder();
        }

        public GroupMasterBuilder withMasterId(String masterId) {
            this.masterId = masterId;
            return this;
        }

        public GroupMasterBuilder withRemainPushOrderNumber(Integer remainPushOrderNumber) {
            this.remainPushOrderNumber = remainPushOrderNumber;
            return this;
        }

        public GroupMasterBuilder withCoreDivisionId(String coreDivisionId) {
            this.coreDivisionId = coreDivisionId;
            return this;
        }

        public GroupMasterBuilder withBindPackageId(String bindPackageId) {
            this.bindPackageId = bindPackageId;
            return this;
        }

        public GroupMasterBuilder withBindPackageConfigId(String bindPackageConfigId) {
            this.bindPackageConfigId = bindPackageConfigId;
            return this;
        }

        public GroupMasterBuilder withBindPackageCreateTime(Long bindPackageCreateTime) {
            this.bindPackageCreateTime = bindPackageCreateTime;
            return this;
        }

        public OrderPackageMaster build() {
            OrderPackageMaster groupMaster = new OrderPackageMaster();
            groupMaster.setMasterId(masterId);
            groupMaster.setRemainPushOrderNumber(remainPushOrderNumber);
            groupMaster.setCoreDivisionId(coreDivisionId);
            groupMaster.setBindPackageId(bindPackageId);
            groupMaster.setBindPackageConfigId(bindPackageConfigId);
            groupMaster.setBindPackageCreateTime(bindPackageCreateTime);
            return groupMaster;
        }
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderPackageMaster.class.getSimpleName() + "[", "]")
                .add("masterId='" + masterId + "'")
                .add("remainPushOrderNumber=" + remainPushOrderNumber)
                .add("coreDivisionId='" + coreDivisionId + "'")
                .add("bindPackageId='" + bindPackageId + "'")
                .add("bindPackageConfigId='" + bindPackageConfigId + "'")
                .add("bindPackageCreateTime=" + bindPackageCreateTime)
                .toString();
    }
}
