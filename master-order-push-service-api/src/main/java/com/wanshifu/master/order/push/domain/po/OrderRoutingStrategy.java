package com.wanshifu.master.order.push.domain.po;


import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * 描述 :  创建路由Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
@Table(name = "order_routing_strategy")
public class OrderRoutingStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;


    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 订单来源
     */
    @Column(name = "order_from")
    private String orderFrom;

    /**
     * 订单标识
     */
    @Column(name = "order_tag")
    private String orderTag;

    /**
     * 类目id
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 城市id
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 匹配路由id
     */
    @Column(name = "match_routing_id")
    private Integer matchRoutingId;

    /**
     * 策略状态
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}