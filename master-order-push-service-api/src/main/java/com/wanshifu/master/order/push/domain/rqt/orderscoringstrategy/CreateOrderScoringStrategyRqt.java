package com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 15:12
 */
@Data
public class CreateOrderScoringStrategyRqt {

    /**
     * 业务线id
     *
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Long businessLineId;

    /**
     * 策略名称
     */
    @NotBlank
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 类目id
     */
    @NotBlank
    private String categoryIds;

    @NotBlank
    @ValueIn("auto_receive,agreement,agent,new_master,cooperation_business_master,full_time_master,after_verify_new_master")
    private String masterResources;

    /**
     * 评分规则列表
     */
    @NotEmpty
    @Valid
    private List<SortRule> ruleList;


    private Long createAccountId;

    @Data
    public static class SortRule{
        /**
         * 开启条件
         */
        @NotNull
        @Valid
        private OpenCondition openCondition;

        /**
         * 匹配项集合
         */
        @NotEmpty(message = "至少包含一条匹配项")
        @Valid
        private List<RuleItem> itemList;


        @Data
        public static class OpenCondition{
            /**
             * 或且关系
             */
            @NotEmpty
            @ValueIn("and,or")
            private String condition;

            /**
             *规则项
             */
            @NotEmpty
            @Valid
            private List<OpenConditionItem> itemList;
        }

        /**
         * 开启条件item
         */
        @Data
        public static class OpenConditionItem{

            /**
             *
             *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
             */
            @NotEmpty
            @ValueIn("serve,appoint_type,order_from,time_liness_tag,appoint_user")
            private String itemName;

            /**
             * 符号 in:包含  not_in:不包含
             */
            @NotEmpty
            @ValueIn("in,not_in")
            private String term;

            /**
             * 规则项值
             */
            private String itemValue;

            /**
             * [1],[1,2],[1,2,3] 数组长度表示 服务级别
             */
            private List<List<Long>> serveIdList;

        }
    }


    @Data
    public static class RuleItem {

        /**
         * 匹配项名称
         */
        @NotEmpty
        private String itemTitle;

        @NotEmpty
        @ValueIn("master_group,master_quota")
        private String itemType;

        /**
         * 匹配项
         */
        @NotEmpty
        private String itemName;

        /**
         * 匹配项权重 (0,1]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "1")
        @NotNull
        private BigDecimal weight;

        /**
         * 赋值方式：range_value: 区间，enum_value: 枚举
         */
        @NotEmpty
        @ValueIn("range_value,enum_value")
        private String assignMode;

        /**
         * 评分项列表
         */
        @NotEmpty
        @Valid
        private List<ScoreItem> scoreList;

        @Data
        public static class ScoreItem {

            /**
             * 起始值 assignMode="range_value" 时使用
             */
            private BigDecimal startValue;

            /**
             * 终止值 assignMode="range_value" 时使用
             */
            private BigDecimal endValue;

            /**
             * 枚举值 assignMode="enum_value" 时使用
             */
            private String value;

            /**
             * 赋分
             */
            @NotNull
            //@DecimalMin(inclusive = false, value = "0", message = "分值必须大于0")
            @DecimalMax(value = "100", message = "分值必须小于等于100")
            private BigDecimal score;
        }
    }


}
