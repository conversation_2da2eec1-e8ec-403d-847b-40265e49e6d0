package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.utils.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PushCommon {

    @Value("${es.city.list}")
    private String esCityList;


    @Value("${hbase.city.list}")
    private String hbaseCityList;


    @Value("${mustOrderFlag.city.list}")
    private String mustOrderFlagCityList;

    @Deprecated
    public boolean searchEs(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(esCityList)){
                return false;
            }

            if ("all".equals(esCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(esCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }

        return false;

    }


    @Deprecated
    public boolean searchHbase(Long cityDivisionId){
        try{
            if(StringUtils.isBlank(hbaseCityList)){
                return false;
            }

            if ("all".equals(hbaseCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(hbaseCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }

        return false;
    }


    @Deprecated
    public boolean checkMustOrderFlagSwitch(Long cityDivisionId){

        try{
            if(StringUtils.isBlank(mustOrderFlagCityList)){
                return false;
            }

            if ("all".equals(mustOrderFlagCityList)) {
                return true;
            }

            Set<Long> cityIdSet = Arrays.stream(mustOrderFlagCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(cityIdSet)) {
                if (cityIdSet.contains(cityDivisionId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        }catch(Exception e){
            e.printStackTrace();
        }

        return false;

    }
}
