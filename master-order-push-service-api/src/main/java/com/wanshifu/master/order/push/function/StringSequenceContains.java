package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class StringSequenceContains  extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list.length != 2) {
            throw new Exception("操作数异常");
        }

        try {
            String strings = (String) list[0];
            String string = (String) list[1];
            if (strings == null || string == null) {
                return false;
            } else {
                return Arrays.asList(strings.split(",")).contains(string);
            }
        } catch (Exception e) {
            return false;
        }
    }
}
