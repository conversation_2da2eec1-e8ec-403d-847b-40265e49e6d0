package com.wanshifu.master.order.push.domain.rqt.pushLimitRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述 :  创建推送限制规则
 * <AUTHOR> <EMAIL>
 * @date : 2023-03-08 15:21
 */
@Data
public class CreateRqt {



    /**
     * 规则名称
     */
    @NotBlank
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;


    /**
     * 城市id集合
     */
    @NotBlank
    private String cityIds;

    /**
     * 人群类型，crowd_label: 标签，crowd_group: 人群组
     */
    @NotBlank
    private String crowdType;

    /**
     * 人群标签
     */
    private String crowdLabel;


    /**
     * 人群组
     */
    private CrowdGroup crowdGroup;

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 限制范围, all: 全部，serve: 按服务限制
     */
    @NotBlank
    private String limitRange;

    /**
     * 限制服务规则
     */
    private LimitServeRule limitServeRule;

    /**
     * 例外规则
     */
    private ExclusiveRule exclusiveRule;

    /**
     * 限制推送规则，stop_pushing_immediately: 立即停止推送，decrease_push_by_percent: 按比例递减推送,fixed_
     */
    @NotBlank
    private String limitRule;

    /**
     * 递减周期
     */
    private Integer decreaseDays;

    /**
     * 递减百分比
     */
    private Integer decreasePercent;

    /**
     * 固定递减百分比
     */
    private Integer fixedDecreasePercent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;



    @Data
    public static class LimitServeRule{

        private String condition;

        private List<Item> itemList;
    }


    @Data
    public static class ExclusiveRule{

        private String condition;

        private List<Item> itemList;
      }


    @Data
    public static class CrowdGroup{

        private String condition;

        private List<RuleItem> itemList;
    }




    @Data
    public static class Item{

        @NotBlank
        private String itemName;

        @NotBlank
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

        private String itemExtraValue;
    }

    private Long createAccountId;


    @Data
    public static class RuleItem{

        /**
         * master_group: 师傅人群，master_quota: 师傅指标
         */
        @NotEmpty
        private String itemType;

        /**
         * 规则项名称
         */
        @NotEmpty
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
         */
        @NotEmpty
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        /**
         * 规则项值
         */
        @NotEmpty
        private String itemValue;
    }

}