package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

@Data

public class OrderMasterPush {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "push_id")
    private Long pushId;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private String masterId;

    /**
     * 版本号
     */
    @Column(name = "order_version")
    private String orderVersion;

    /**
     * 推单评分
     */
    @Column(name = "score")
    private BigDecimal score;

    /**
     * 轮次
     */
    @Column(name = "rounds")
    private String rounds;

    /**
     * 是否备选
     */
    @Column(name = "is_alternative")
    private Integer isAlternative;

    /**
     * 师傅类型
     */
    @Column(name = "master_type")
    private String masterType;

    /**
     * 是否优先推送
     */
    @Column(name = "is_priority_push")
    private Integer isPriorityPush;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
