package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.domain.po.ComplexFeature;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Set;

/**
 * 复杂特征Repository
 * <AUTHOR>
 */
@Repository
public class ComplexFeatureRepository extends BaseRepository<ComplexFeature> {

    public List<ComplexFeature> selectByFeatureFor(String featureFor, Set<String> featureList){
        Condition condition = new Condition(ComplexFeature.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andIn("featureCode", featureList).andEqualTo("featureFor",featureFor);
        return this.selectByCondition(condition);
    }


}