package com.wanshifu.master.order.push.domain.rqt.orderRoutingStrategy;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 描述 :  初筛策略列表Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    @NotNull
    private Integer businessLineId;

    /**
     * 路由名称
     */
    private String strategyName;

    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;

    private String categoryIds;

    private Integer strategyStatus;

    private Long cityId;


}