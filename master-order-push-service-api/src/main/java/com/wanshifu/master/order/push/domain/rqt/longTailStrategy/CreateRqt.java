package com.wanshifu.master.order.push.domain.rqt.longTailStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.RangeSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.StatusSelectVo;
import com.wanshifu.master.order.push.domain.vo.filterStrategy.RuleItem;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.LongTailRule;
import com.wanshifu.master.order.push.domain.vo.longTailStrategy.Trigger;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述 :  创建长尾策略
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotEmpty
    private String longTailStrategyName;

    /**
     * 策略名称
     */
    private String longTailStrategyDesc;

    /**
     * 规则
     */
    @NotNull
    private LongTailRule longTailRule;


    /**
     * 推送类型：
     * 附近更多：nearby_more,
     * 红包订单：bonus_order,
     * 区县外订单：out_district
     */
    @NotNull
    private String pushType;


    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    private Long createAccountId;
}