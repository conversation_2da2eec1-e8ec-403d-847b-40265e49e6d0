package com.wanshifu.master.order.push.function;

import com.google.common.collect.Lists;
import com.wanshifu.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-03-16 16:12
 */
@Slf4j
public class Match {

    /**
     * 目前支持的操作符
     */
    private final static List<String> TERMS = Lists.newArrayList("<", "=", ">", "<=", ">=");

    public static boolean executeInner(Object[] list,String matchType) throws Exception {
        if (list.length != 3) {
            throw new Exception("操作数异常");
        }
        String term = String.valueOf(list[1]);
        if (!TERMS.contains(term)) {
            throw new Exception("该函数不支持该运算符");
        }
        try {
            Object obj = list[0];
            BigDecimal value = new BigDecimal(list[2].toString());
            if (term == null) {
                return false;
            }
            if (!obj.getClass().isArray() && !(obj instanceof List)) {
                return false;
            }
            return Match.eval((List<?>) obj, term, value,matchType);

        } catch (Exception e) {
            log.error("math execute error",e);
        }
        return false;
    }

    private static boolean eval(List<?> values, String term, BigDecimal value,String matchType) {

        if(StringUtils.equals(matchType,"allMatch")){
            return values.stream().allMatch(it -> Match.evalItem(it,term,value));
        }else {
            return values.stream().anyMatch(it -> Match.evalItem(it,term,value));
        }
    }

    private static boolean evalItem(Object valueItem, String term,BigDecimal value){
        int compareTo = new BigDecimal(valueItem.toString()).compareTo(value);

        switch (term) {
            case "<":
                return compareTo < 0;
            case "=":
                return compareTo == 0;
            case ">":
                return compareTo > 0;
            case "<=":
                return compareTo <= 0;
            case ">=":
                return compareTo >= 0;
            default:
                throw new IllegalStateException("Unexpected value: " + term);
        }
    }
}