package com.wanshifu.master.order.push.api;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushOrderList;
import com.wanshifu.master.order.push.domain.resp.GetFilterDataResp;
import com.wanshifu.master.order.push.domain.rqt.GetFilterDataRqt;
import com.wanshifu.master.order.push.domain.rqt.NoPushedMasterOrderListRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "orderPushMonitor", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface OrderPushMonitorApi {

    /**
     * 未推单明细列表
     * @param rqt
     * @return
     */
    @PostMapping("/noPushedOrderList")
    SimplePageInfo<PushOrderList> noPushedMasterOrderList(@RequestBody @Valid NoPushedMasterOrderListRqt rqt);





    @PostMapping("/filterData")
    List<GetFilterDataResp> filterData(@Valid @RequestBody GetFilterDataRqt rqt);
}
