package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import com.wanshifu.master.order.push.domain.enums.CalculateType;
import com.wanshifu.master.order.push.util.DistanceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 计算两点之间距离
 * <AUTHOR>
 */
@Slf4j
public class DistanceFromTwoPoint extends Operator {


    @Override
    public Object executeInner(Object[] list) throws Exception{
        if (list.length != 3){
            throw new Exception("操作数异常");
        }
        //订单经纬度（先lng，后lat，逗号分隔）
        Long reuslt;
        Number defaultValue=999999999L;

        try{
            String leftPoint= (String)list[0];
            String rightPoint = (String)list[1];
            if (leftPoint == null || rightPoint==null) {
                reuslt = defaultValue.longValue();
            }else {
                String[] leftArr = leftPoint.split(",");
                String[] rightArr = rightPoint.split(",");
                if (leftArr.length != 2 || rightArr.length != 2) {
                    reuslt=defaultValue.longValue();
                }else {
//						lat lng
                    if("null,null".equals(rightPoint) || "null,null".equals(leftPoint)){
                        reuslt=defaultValue.longValue();
                    }else{
                        reuslt= DistanceUtil.distance(Double.parseDouble(leftArr[1]), Double.parseDouble(leftArr[0]),
                                Double.parseDouble(rightArr[1]), Double.parseDouble(rightArr[0]));
                    }

                }
            }
        }catch(Exception e){
            log.error(String.format("计算两点距离失败，param1:%s, param2:%s,param3:%s",list[0],list[1],list[2]), e);
            reuslt=defaultValue.longValue();
        }
        return reuslt;
    }


//    /**
//     * km 地球半径 平均值，千米
//     */
//    static double EARTH_RADIUS = 6371.0;
//
//
//
//    /**
//     * latitude:维度 longitude:经度  （第一个点经纬度,第二个点经纬度） 用haversine公式计算球面两点间的距离。
//     *
//     * @return 距离:千米
//     */
//    private static Long distance(double lat1, double lon1, double lat2,
//                                 double lon2) {
//        // 经纬度转换成弧度
//        lat1 = convertDegreesToRadians(lat1);
//        lon1 = convertDegreesToRadians(lon1);
//        lat2 = convertDegreesToRadians(lat2);
//        lon2 = convertDegreesToRadians(lon2);
//
//        // 差值
//        double vLon = Math.abs(lon1 - lon2);
//        double vLat = Math.abs(lat1 - lat2);
//
//        // h is the great circle distance in radians, great
//        // circle就是一个球体上的切面，它的圆心即是球心的一个周长最大的圆。
//        double h = haverSin(vLat) + Math.cos(lat1) * Math.cos(lat2) * haverSin(vLon);
//
//        Double distance = 2 * EARTH_RADIUS * Math.asin(Math.sqrt(h));
//
//        return distance.longValue();
//    }
//
//
//    /**
//     * 将角度换算为弧度。
//     *
//     * @param degrees
//     * @return
//     */
//    private static double convertDegreesToRadians(double degrees) {
//        return degrees * Math.PI / 180;
//    }
//
//    // private static double ConvertRadiansToDegrees(double radian) {
//    // return radian * 180.0 / Math.PI;
//    // }
//
//    private static double haverSin(double theta) {
//        double v = Math.sin(theta / 2);
//        return v * v;
//    }
//
    public Object executeInner(Object op1,
                               Object op2) throws Exception {
        Object result = null;
        int compareResult = Operator.compareData(op1,op2);
        if (CalculateType.MIN.code.equals(this.name)) {
            if (compareResult < 0) {
                result = op1;
            }else {
                result = op2;
            }
        } else if (CalculateType.MAX.code.equals(this.name)) {
            if (compareResult < 0) {
                result = op2;
            }else {
                result = op1;
            }
        }
        return result;
    }



}
