package com.wanshifu.master.order.push.util;

import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.wanshifu.master.order.push.service.QLExpressHandler;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Slf4j
public class QlExpressStatic {

    private static ExpressRunner expressRunner
            = SpringContextUtil.getBean(QLExpressHandler.class).getExpressRunner();

    public static boolean QlExpressBoolean(String expression, DefaultContext<String, Object> context){
        boolean result=false;
        try{
            result=Boolean.valueOf(expressRunner.execute(expression,context,
                    null,true,false).toString());
        }catch(Exception e){
//            log.warn("execute QlExpressBoolean error:[{}],[{}]",expression,context);
        }
        return result;
    }

    public static BigDecimal QlExpressNumber(String expression, DefaultContext<String, Object> context){
        BigDecimal result=BigDecimal.ZERO;
        try{
            Object score = expressRunner.execute(expression,context,
                    null,true,false);
            if(score instanceof Integer){
                result= BigDecimal.valueOf((Integer)score);
            }else if(score instanceof Double){
                result= BigDecimal.valueOf((Double)score);
            }
        }catch(Exception e){
//            log.error("QlExpressNumber.execute is error!Context=【{}】,Expression=【{}】",
//                    context,expression);
        }
        return result;
    }

    public static String QlExpressString(String expression, DefaultContext<String, Object> context,String defaultValue){
        String result=defaultValue;
        try{
            Object o = expressRunner.execute(expression,context,
                    null,true,false);
            result=o.toString();
        }catch(Exception e){
//            log.error("QlExpressString.execute is error!Context=【{}】,Expression=【{}】",
//                    context,expression);
        }
        return result;
    }
}
