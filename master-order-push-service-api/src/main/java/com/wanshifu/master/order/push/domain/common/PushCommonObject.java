package com.wanshifu.master.order.push.domain.common;

import com.wanshifu.master.order.push.domain.dto.PushRuleConfig;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.pushRule.CreateRqt;
import lombok.Data;

import java.util.Set;

/**
 * 订单推送公共实体类
 * <AUTHOR>
 */
@Data
public class PushCommonObject {

    private String orderVersion;
    private Long timestamp;
    private Set<String> masterSet;
    private OrderDetailData orderDetailData;
    private MasterMatchCondition masterMatchCondition;
    private Integer divisionMatchLevel;
    private Set<String> priorityPushMasterSet;
    private PushConfig pushConfig;
    private BaseSelect baseSelect;
    private Long repushStrategySnapshotId;
    private String latestOrderVersion;
    private PushRuleConfig pushRuleConfig;

    /**
     * proprietary
     * normal
     * agent
     */
    private	String pushMode="normal";

    public boolean isDirectPush() {
        return "agent".equals(pushMode);
    }


    public void setPushMode(String pushMode) {
        this.pushMode = pushMode;
    }

    public PushCommonObject( ) {

    }

    public Long masterOrderId() {
        return this.orderDetailData.getMasterOrderId();
    }

    public Long getGlobalOrderId() {
        return this.orderDetailData.getGlobalOrderId();
    }





}
