package com.wanshifu.master.order.push.domain.po;

import com.wanshifu.master.order.push.domain.enums.PushRound;
import lombok.Data;
import lombok.ToString;

import javax.annotation.Generated;
import javax.persistence.*;
import java.util.Date;

/**
 * 特殊推单配置
 */
@Data
@ToString
@Table(name = "push_config")
public class PushConfig {




    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "push_config_id")
    private Integer pushConfigId;


    /**
     * 推送配置开启条件QL表达式
     */
    @Column(name = "push_config_expression")
    private String pushConfigExpression;



    /**
     * 最优报价数
     */
    @Column(name = "push_config_expression")
    private Integer bestOfferNum;

    /**
     * 每轮推送时间间隔
     */
    @Column(name = "delay_minutes_between_rounds")
    private Integer delayMinutesBetweenRounds;

    /**
     * 首轮推送老师傅数
     */
    @Column(name = "first_push_old_master_num")
    private Integer firstPushOldMasterNum;

    /**
     * 首轮推送新师傅数
     */
    @Column(name = "first_push_new_master_num")
    private Integer firstPushNewMasterNum;

    /**
     * 延迟老师傅推送数
     */
    @Column(name = "delay_push_old_master_num_per_round")
    private Integer delayPushOldMasterNumPerRound;

    /**
     * 延迟新师傅推送数
     */
    @Column(name = "delay_push_new_master_num_per_round")
    private Integer delayPushNewMasterNumPerRound;

    private String firstPushMasterType;


    private String delayPushMasterType;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    @Transient
    private String pushMark;



    public PushConfig clone(){
        return PushConfig.builder()
                .setBestOfferNum(getBestOfferNum())
                .setDelayMinutesBetweenRounds(getDelayMinutesBetweenRounds())
                .setFirstPushOldMasterNum(this.firstPushOldMasterNum)
                .setFirstPushNewMasterNum(this.firstPushNewMasterNum)
                .setDelayPushOldMasterNumPerRound(this.delayPushOldMasterNumPerRound)
                .setDelayPushNewMasterNumPerRound(this.delayPushNewMasterNumPerRound).build();
    }

    public PushConfig() {

        /**
         * 最优报价数
         */
        this.bestOfferNum = 50;

        /**
         * 每轮推送时间间隔
         */
        this.delayMinutesBetweenRounds = 100;

        /**
         * 首轮推送老师傅数
         */
        this.firstPushOldMasterNum = 50;

        /**
         * 首轮推送新师傅数
         */
        this.firstPushNewMasterNum = 50;

        /**
         * 延迟老师傅推送数
         */
        this.delayPushOldMasterNumPerRound = 70;

        /**
         * 延迟新师傅推送数
         */
        this.delayPushNewMasterNumPerRound = 60;

    }

    /**
     * 本轮推送老师傅数
     *
     * @return
     */
    public Integer getPushOldMasterNumPerRound() {
        if (PushRound.DELAY_PUSH.getCode().equals(pushMark)) {
            return delayPushOldMasterNumPerRound;
        }else {
            return firstPushOldMasterNum;
        }
    }

    /**
     * 本轮推送新师傅数
     *
     * @return
     */
    public Integer getPushNewMasterNumPerRound() {
        if (PushRound.DELAY_PUSH.getCode().equals(pushMark)) {
            return delayPushNewMasterNumPerRound;
        }else {
            return firstPushNewMasterNum;
        }
    }



    @Generated("SparkTools")
    private PushConfig(Builder builder) {
        this.bestOfferNum = builder.bestOfferNum;
        this.delayMinutesBetweenRounds = builder.delayMinutesBetweenRounds;
        this.firstPushOldMasterNum = builder.firstPushOldMasterNum;
        this.firstPushNewMasterNum = builder.firstPushNewMasterNum;
        this.delayPushOldMasterNumPerRound = builder.delayPushOldMasterNumPerRound;
        this.delayPushNewMasterNumPerRound = builder.delayPushNewMasterNumPerRound;
        this.firstPushMasterType = builder.firstPushMasterType;
        this.delayPushMasterType = builder.delayPushMasterType;

    }

    /**
     * Creates builder to build {@link PushConfig}.
     *
     * @return created builder
     */
    @Generated("SparkTools")
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder to build {@link PushConfig}.
     */
    @Generated("SparkTools")
    public static final class Builder {
        private Integer bestOfferNum;
        private Integer delayMinutesBetweenRounds;
        private Integer firstPushOldMasterNum;
        private Integer firstPushNewMasterNum;
        private Integer delayPushOldMasterNumPerRound;
        private Integer delayPushNewMasterNumPerRound;
        private String firstPushMasterType;
        private String delayPushMasterType;

        private Builder() {
        }

        public Builder setBestOfferNum(Integer bestOfferNum) {
            if (bestOfferNum<=0) {
                this.bestOfferNum = 50;
            }else {

                this.bestOfferNum = bestOfferNum;
            }
            return this;
        }

        public Builder setDelayMinutesBetweenRounds(Integer delayMinutesBetweenRounds) {
            if (delayMinutesBetweenRounds<=0) {
                this.delayMinutesBetweenRounds = 100;
            }else {
                this.delayMinutesBetweenRounds = delayMinutesBetweenRounds;
            }
            return this;
        }

        public Builder setFirstPushOldMasterNum(Integer firstPushOldMasterNum) {
            this.firstPushOldMasterNum = firstPushOldMasterNum;
            return this;
        }

        public Builder setFirstPushNewMasterNum(Integer firstPushNewMasterNum) {
            this.firstPushNewMasterNum = firstPushNewMasterNum;
            return this;
        }

        public Builder setDelayPushOldMasterNumPerRound(Integer delayPushOldMasterNumPerRound) {
            this.delayPushOldMasterNumPerRound = delayPushOldMasterNumPerRound;
            return this;
        }

        public Builder setDelayPushNewMasterNumPerRound(Integer delayPushNewMasterNumPerRound) {
            this.delayPushNewMasterNumPerRound = delayPushNewMasterNumPerRound;
            return this;
        }

        public Builder setFirstPushMasterType(String firstPushMasterType) {
            this.firstPushMasterType = firstPushMasterType;
            return this;
        }

        public Builder setDelayPushMasterType(String delayPushMasterType) {
            this.delayPushMasterType = delayPushMasterType;
            return this;
        }

        public PushConfig build() {
            return new PushConfig(this);
        }
    }



}
