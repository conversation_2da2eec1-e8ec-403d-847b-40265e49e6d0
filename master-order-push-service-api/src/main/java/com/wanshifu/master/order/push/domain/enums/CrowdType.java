package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 推单限制方式类型
 */
@AllArgsConstructor
@Getter
public enum CrowdType {

    CROWD_LABEL("crowd_label", "人群标签"),

    CROWD_GROUP("crowd_group", "人群组");


    private final String code;

    private final String desc;



    private static final Map<String, CrowdType> valueMapping = new HashMap<>((int) (CrowdType.values().length / 0.75));


    static {
        for (CrowdType instance : CrowdType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static CrowdType asCode(String code) {
        return valueMapping.get(code);
    }

}
