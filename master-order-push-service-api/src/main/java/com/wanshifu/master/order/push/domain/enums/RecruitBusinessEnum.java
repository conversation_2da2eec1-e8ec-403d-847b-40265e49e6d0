package com.wanshifu.master.order.push.domain.enums;

import java.util.*;

public enum RecruitBusinessEnum {
    /**
     * 成品业务
     */
    FINISHED_PRODUCT("finished_product"),

    /**
     * 家庭业务
     */
    FAMILY("family"),

    /**
     * 总包业务
     */
    ENTERPRISE("enterprise"),

    /**
     * 成品业务-家庭业务
     */
    FINISHED_PRODUCT_FAMILY("finished_product_family"),

    /**
     * 成品业务-总包业务
     */
    FINISHED_PRODUCT_ENTERPRISE("finished_product_enterprise"),

    /**
     * 家庭业务-总包业务
     */
    FAMILY_ENTERPRISE("family_enterprise"),

    /**
     * 成品业务-家庭业务-总包业务
     */
    FINISHED_PRODUCT_FAMILY_ENTERPRISE("finished_product_family_enterprise");

    public final String code;

    RecruitBusinessEnum(String code) {
        this.code = code;
    }

    public static final Map<String, List<String>> businessMap = new HashMap<>();

    public static final Map<String, List<String>> baseBusinessMap = new HashMap<>();

    public static final Map<String, List<String>> businessCnMap = new HashMap<>();

    public static final List<String> baseBusinessList = new ArrayList<>();

    static {
        businessMap.put(FINISHED_PRODUCT.code, Arrays.asList(FINISHED_PRODUCT.code, FINISHED_PRODUCT_FAMILY.code, FINISHED_PRODUCT_ENTERPRISE.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(FAMILY.code, Arrays.asList(FAMILY.code, FINISHED_PRODUCT_FAMILY.code, FAMILY_ENTERPRISE.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(ENTERPRISE.code, Arrays.asList(ENTERPRISE.code, FINISHED_PRODUCT_ENTERPRISE.code, FAMILY_ENTERPRISE.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(FINISHED_PRODUCT_FAMILY.code, Arrays.asList(FINISHED_PRODUCT_FAMILY.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(FINISHED_PRODUCT_ENTERPRISE.code, Arrays.asList(FINISHED_PRODUCT_ENTERPRISE.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(FAMILY_ENTERPRISE.code, Arrays.asList(FAMILY_ENTERPRISE.code, FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));
        businessMap.put(FINISHED_PRODUCT_FAMILY_ENTERPRISE.code, Collections.singletonList(FINISHED_PRODUCT_FAMILY_ENTERPRISE.code));

        baseBusinessMap.put(FINISHED_PRODUCT.code, Collections.singletonList(FINISHED_PRODUCT.code));
        baseBusinessMap.put(FAMILY.code, Collections.singletonList(FAMILY.code));
        baseBusinessMap.put(ENTERPRISE.code, Collections.singletonList(ENTERPRISE.code));
        baseBusinessMap.put(FINISHED_PRODUCT_FAMILY.code, Arrays.asList(FINISHED_PRODUCT.code, FAMILY.code));
        baseBusinessMap.put(FINISHED_PRODUCT_ENTERPRISE.code, Arrays.asList(FINISHED_PRODUCT.code, ENTERPRISE.code));
        baseBusinessMap.put(FAMILY_ENTERPRISE.code, Arrays.asList(FAMILY.code, ENTERPRISE.code));
        baseBusinessMap.put(FINISHED_PRODUCT_FAMILY_ENTERPRISE.code, Arrays.asList(FINISHED_PRODUCT.code, FAMILY.code, ENTERPRISE.code));

        businessCnMap.put(FINISHED_PRODUCT.code, Collections.singletonList("成品业务"));
        businessCnMap.put(FAMILY.code, Collections.singletonList("家庭业务"));
        businessCnMap.put(ENTERPRISE.code, Collections.singletonList("总包业务"));
        businessCnMap.put(FINISHED_PRODUCT_FAMILY.code, Arrays.asList("成品业务", "家庭业务"));
        businessCnMap.put(FINISHED_PRODUCT_ENTERPRISE.code, Arrays.asList("成品业务", "总包业务"));
        businessCnMap.put(FAMILY_ENTERPRISE.code, Arrays.asList("家庭业务", "总包业务"));
        businessCnMap.put(FINISHED_PRODUCT_FAMILY_ENTERPRISE.code, Arrays.asList("成品业务", "家庭业务", "总包业务"));

        baseBusinessList.add(FINISHED_PRODUCT.code);
        baseBusinessList.add(ENTERPRISE.code);
        baseBusinessList.add(FAMILY.code);
    }


    @Override
    public String toString() {
        return super.toString();
    }

    public static String getRecruitBusiness(List<String> recruitBusinessList) {
        String recruitBusiness = "";

        if (recruitBusinessList.size() == 1) {
            recruitBusiness = recruitBusinessList.get(0);
        } else if (recruitBusinessList.size() == 2) {
            String business1 = recruitBusinessList.get(0);
            String business2 = recruitBusinessList.get(1);

            boolean containsEnterprise = RecruitBusinessEnum.ENTERPRISE.code.equals(business1) || RecruitBusinessEnum.ENTERPRISE.code.equals(business2);

            if (RecruitBusinessEnum.FINISHED_PRODUCT.code.equals(business1) || RecruitBusinessEnum.FINISHED_PRODUCT.code.equals(business2)) {
                return containsEnterprise ? RecruitBusinessEnum.FINISHED_PRODUCT_ENTERPRISE.code : RecruitBusinessEnum.FINISHED_PRODUCT_FAMILY.code;
            } else {
                return RecruitBusinessEnum.FAMILY_ENTERPRISE.code;
            }

        } else if (recruitBusinessList.size() == 3) {
            recruitBusiness = RecruitBusinessEnum.FINISHED_PRODUCT_FAMILY_ENTERPRISE.code;
        }

        return recruitBusiness;
    }
}