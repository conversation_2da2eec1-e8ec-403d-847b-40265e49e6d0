package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单调度类型
 * <AUTHOR>
 */
public enum DistributeType {

    /**
     * 自动接单
     */
    AUTO_RECEIVE("auto_receive","自动接单"),

    /**
     * 新师傅扶持
     */
    NEW_MASTER_SUPPORT("new_master_support","新师傅扶持"),


    /**
     * 总包直接指派
     */
    ENTERPRISE_APPOINT("enterprise_appoint","总包直接指派"),


    /**
     * 样板订单
     */
    NEW_MODEL("new_model","样板订单"),


    /**
     * 总包直接指派新师傅
     */
    ENTERPRISE_APPOINT_NEW_MASTER("enterprise_appoint_new_master","总包直接指派新师傅"),



    /**
     * 全时师傅派单
     */
    FULL_TIME_MASTER("full_time_master","全时师傅派单"),


    /**
     * 总包直接指派全时师傅
     */
    ENTERPRISE_APPOINT_FULL_TIME_MASTER("enterprise_appoint_full_time_master","总包直接指派全时师傅");



    private final String code;
    private final String name;

    DistributeType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, DistributeType> valueMapping = new HashMap<>((int) (DistributeType.values().length / 0.75));

    static {
        for (DistributeType instance : DistributeType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static DistributeType asValue(String value) {
        return valueMapping.get(value);
    }


}
