//package com.wanshifu.master.order.push.service;
//
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.ResultSet;
//import java.sql.Statement;
//import java.util.ArrayList;
//import java.util.List;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//
///**
// * 查询数据库工具类
// *
// * <AUTHOR>
// *
// */
//@Component
//@Slf4j
//public class HBaseQuery {
//
//	private static final Logger LOGGER = LoggerFactory.getLogger(HBaseQuery.class);
//
//
////	@Value("${hbase.url}")
//	private String hbaseUrl = "*************************************************";
//
//
//	@Resource
//	private DataSource dataSource;
//
//	private static Connection connection;
//
//
//
//	@PostConstruct
//	public void initPhoenixDriver(){
//
////		try {
////			Class.forName("org.apache.phoenix.jdbc.PhoenixDriver");
////			connection = getHbaseConnection();
////		} catch (ClassNotFoundException e) {
////			System.out.println("error " + e.getMessage());
////		}
//
//
//
//
//	}
//
//
//	public Connection getHbaseConnection(){
//		try{
//			String ipList = "***********:2181";
//			String znodeParent = "/hbase-emr-5g4yaki0";
//			String url = "jdbc:phoenix:" + ipList + ":" +znodeParent;
//			return DriverManager.getConnection(url);
//		}catch(Exception e){
//			e.printStackTrace();
//		}
//		return null;
//
//	}
//
//
//	private Connection getConnection(String instanceName){
//		try{
//			if("mysql".equals(instanceName)){
//				return dataSource.getConnection();
//			}else if("hbase".equals(instanceName)){
//				return connection;
//			}
//		}catch(Exception e){
//			e.printStackTrace();
//		}
//		return null;
//
//	}
//
//
//	/**
//	 * 根据特定格式的入参返回查询结果
//	 *
//	 * @param param 查询入参
//	 * @return 查询结果
//	 */
//	public JSONObject query(String param,String tableName,String instanceName) {
//
//		JSONObject resultObject = new JSONObject();
//
//		try {
//			JSONArray paramJSONArray = JSONArray.parseArray(param);
//			for (Object paramObj : paramJSONArray) {
//				JSONArray paramResultJSONArray = new JSONArray();
//				JSONObject paramJSON = JSONObject.parseObject(paramObj.toString());
//				Integer datasetId = paramJSON.getInteger("dataset_id");
//				String conditions = paramJSON.getString("conditions");
//				String pageSetting = paramJSON.getString("page_setting");
//				JSONArray dimensionColumnArray = JSONArray.parseArray(paramJSON.getString("dimension_columns"));
//				JSONArray indexColumnArray = JSONArray.parseArray(paramJSON.getString("index_columns"));
//
//
//				List<String> fieldolumnList = new ArrayList<>();
//				dimensionColumnArray.stream().forEach(dimensionColumnObj -> fieldolumnList.add(dimensionColumnObj.toString()));
//
//				indexColumnArray.stream().forEach(indexColumnObj -> fieldolumnList.add(indexColumnObj.toString()));
//
//
//				// 查询结果处理
//				JSONArray tmpResultJSONArray = queryData(fieldolumnList, tableName, instanceName,conditions);
//
//				JSONObject paramResultJSON = new JSONObject();
//
//				// 获取入参对应的配置
//				int illegalResult = 0;
//
//				int tmpResultSize = tmpResultJSONArray.size();
//				if (tmpResultSize <= 0) {
//					continue;
//					// 指标字段都在同一张表中/指标字段不在同一张表中并且每个字段的结果记录只有一条
//				} else if (tmpResultSize == 1) {
//					paramResultJSON.putAll(tmpResultJSONArray.getJSONObject(0));
//				} else {
//					// 指标字段都在同一张表中/指标字段不在同一张表中并且可能有结果记录有多条（不兼容）
//					for (Object resultObj : tmpResultJSONArray) {
//						JSONObject tmpResultJSON = JSONObject.parseObject(resultObj.toString());
//						paramResultJSONArray.add(tmpResultJSON);
//					}
//					illegalResult++;
//				}
//
//
//				// 返回结果处理
//				if (illegalResult > 1 || (paramResultJSONArray.size() > 0 && paramResultJSON.size() > 0)) {
//					paramResultJSONArray = new JSONArray();
//					LOGGER.warn("出现查询结果存在一对多或者多对多，param：[{}]", paramObj.toString());
//				} else if (paramResultJSON.size() > 0 && paramResultJSONArray.size() == 0) {
//					// 指标字段不在同一张表中/并且每个字段的结果记录只有一条
//					paramResultJSONArray.add(paramResultJSON);
//				}
//				resultObject.put(String.valueOf(datasetId), paramResultJSONArray);
//			}
//		} catch (Exception e) {
//			log.error("数据查询失败", e);
//		}
//		return resultObject;
//	}
//
//
//	private JSONArray parseResultSet(List<String> fieldColumnList,ResultSet resultSet){
//		JSONArray jsonArray = new JSONArray();
//		try{
//			while(resultSet.next()){
//				JSONObject jsonObject = new JSONObject();
//				fieldColumnList.forEach(fieldColumn -> {
//					try{
//						jsonObject.put(fieldColumn,resultSet.getObject(fieldColumn));
//					}catch(Exception e){
//						e.printStackTrace();
//					}
//				});
//				jsonArray.add(jsonObject);
//			}
//		}catch(Exception e){
//			e.printStackTrace();
//		}
//		return jsonArray;
//	}
//
//
//
//	/**
//	 * 根据传入参数和存储配置查询结果
//	 *
//	 * @param queryFieldList 需要查询的字段集合
//	 * @param conditions     查询条件
//	 * @return 查询结果
//	 * @throws Exception sql查询异常
//	 */
//	private JSONArray queryData(List<String> queryFieldList,String tableName,String instanceName,
//			String conditions) throws Exception {
//		// sql语句拼接
//		Statement statement = connection.createStatement();
//
//		StringBuilder sqlBuilder = new StringBuilder("select ");
//		for (String fieldName : queryFieldList) {
//			sqlBuilder.append("\"").append(fieldName);
//			sqlBuilder.append("\",");
//			// 结果参数记录
//		}
//		sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
//		sqlBuilder.append(" from \"");
//		sqlBuilder.append(tableName).append("\"");
//		if (!StringUtils.isEmpty(conditions)) {
//			sqlBuilder.append(" where ");
//			sqlBuilder.append(conditions);
//		}
//		log.info("sqlSBuilder"+sqlBuilder.toString());
//		String sql = "select \"master_id\",\"customer_phone\",\"sum_dispute_order_cnt\" from \"mst_and_customer_stat\" where \"master_id\" = 58025 and \"customer_phone\"= '13798437650'";
//		ResultSet resultSet = statement.executeQuery(sqlBuilder.toString());
//		log.info("resultJSONArray"+resultSet.toString());
//		JSONArray jsonArray = parseResultSet(queryFieldList,resultSet);
//		statement.close();
////		connection.close();
//		return jsonArray;
//	}
//
//
//
//
//
//}
