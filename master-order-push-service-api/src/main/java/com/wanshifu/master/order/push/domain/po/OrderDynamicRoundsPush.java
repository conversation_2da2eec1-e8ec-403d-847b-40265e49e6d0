package com.wanshifu.master.order.push.domain.po;


import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Data
@ToString
@Table(name = "order_dynamic_rounds_push")
public class OrderDynamicRoundsPush {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 订单id
     */
    @Column(name = "global_order_id")
    private Long globalOrderId;

    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 类目id
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 城市id
     */
    @Column(name = "second_division_id")
    private Long secondDivisionId;

    /**
     * 区县id
     */
    @Column(name = "third_division_id")
    private Long thirdDivisionId;

    /**
     * 街道id
     */
    @Column(name = "fourth_division_id")
    private Long fourthDivisionId;

    /**
     * 订单来源类型
     */
    @Column(name = "order_from_type")
    private String orderFromType;

    /**
     * 推送时间
     */
    @Column(name = "push_time")
    private Date pushTime;

    /**
     * 服务类型id
     */
    @Column(name = "serve_type_id")
    private Integer serveTypeId;

    /**
     * 指派模式
     */
    @Column(name = "appoint_type")
    private Integer appointType;

    /**
     * 运力不足类型
     */
    @Column(name = "lack_type")
    private String lackType;

    /**
     * 推送师傅明细列表
     */
    @Column(name = "push_master_detail_list")
    private String pushMasterDetailList;

    /**
     * 推送师傅数
     */
    @Column(name = "push_master_num")
    private Integer pushMasterNum;

    /**
     * 一级服务id
     */
    @Column(name = "serve_level_1_ids")
    private String serveLevel1Ids;

    /**
     * 客户地址
     */
    @Column(name = "customer_address")
    private String customerAddress;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
