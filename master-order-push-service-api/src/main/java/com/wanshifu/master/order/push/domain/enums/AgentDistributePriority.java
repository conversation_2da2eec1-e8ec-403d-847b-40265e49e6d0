package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 合作商订单调度策略
 * <AUTHOR>
 * @date 2025/2/26 20:01
 */
public enum AgentDistributePriority {

    /**
     * 随机
     */
    RANDOM("random"),

    /**
     * 智能
     */
    SMART("smart");



    public final String code;

    AgentDistributePriority(String code) {
        this.code = code;
    }

    private static final Map<String, AgentDistributePriority> valueMapping = new HashMap<>((int) (AgentDistributePriority.values().length / 0.75));

    static {
        for (AgentDistributePriority instance : AgentDistributePriority.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static AgentDistributePriority asValue(Integer value) {
        return valueMapping.get(value);
    }

}
