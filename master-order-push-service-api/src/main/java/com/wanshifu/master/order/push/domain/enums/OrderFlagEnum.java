package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述 :  订单标识枚举.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 16:44
 */
@AllArgsConstructor
@Getter
public enum OrderFlagEnum {

    NORMAL("normal", "普通订单"),
    EXCLUSIVE("exclusive", "专属订单"),
    COOPERATE("cooperate", "合作商订单"),
    IKEA("ikea", "宜家订单");

    private final String code;

    private final String desc;
}