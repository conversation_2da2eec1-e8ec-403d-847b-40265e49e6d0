package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Data
public class OrderMatchRouteTime {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "route_time_id")
    private Integer routeTimeId;

    private Integer businessLineId;

    @Column(name = "category_ids")
    private String categoryIds;

    @Column(name = "appoint_types")
    private String appointTypes;

    @Column(name = "setting_type")
    private String settingType;

    @Column(name = "setting_time")
    private Integer settingTime;

    @Column(name = "setting_num")
    private Integer settingNum;

    @Column(name = "create_account_id")
    private Long createAccountId;

    @Column(name = "update_account_id")
    private Long updateAccountId;

    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
