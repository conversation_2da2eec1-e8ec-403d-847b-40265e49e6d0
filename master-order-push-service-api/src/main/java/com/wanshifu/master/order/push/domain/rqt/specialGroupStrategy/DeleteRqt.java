package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 删除特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@ApiModel(description = "删除特殊人群策略请求类")
@Data
public class DeleteRqt {

    @ApiModelProperty(value = "策略id", required = true)
    @NotNull(message = "策略id不能为空")
    private Long strategyId;

    @ApiModelProperty(value = "更新人id")
    private Long updateAccountId;
}
