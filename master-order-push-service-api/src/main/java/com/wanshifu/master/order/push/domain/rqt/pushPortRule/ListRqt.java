
package com.wanshifu.master.order.push.domain.rqt.pushPortRule;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 推单端口规则列表
 *
 * <AUTHOR> ch<PERSON><PERSON>@wshifu.com
 * @date : 2025-02-12 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 订单标签
     */
    private String orderTag;

    /**
     * 指派模式
     */
    private Integer appointType;

    /**
     * 服务id
     */
    private Long lv1ServeId;

}