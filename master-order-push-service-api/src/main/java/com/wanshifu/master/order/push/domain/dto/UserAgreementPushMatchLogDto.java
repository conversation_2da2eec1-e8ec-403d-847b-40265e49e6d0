package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/13 15:20
 */
@Data
public class UserAgreementPushMatchLogDto {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 派单模式
     */
    private String matchType;

    /**
     * 订单城市
     */
    private String orderCityName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商家id
     */
    private Long userId;

    /**
     * 订单下单时间
     */
    private Date orderCreateTime;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 服务名称
     */
    private String serveTypeName;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 协议师傅招募id
     */
    private Long recruitId;

    /**
     * 师傅姓名
     */
    private String masterName;

    /**
     * 是否匹配成功，1：匹配成功，0：匹配失败
     */
    private Integer isMatchSuccess;

    /**
     * 匹配失败原因
     */
    private String matchFailReason;


    /**
     * 是否计算价格成功，1：计价成功，0：计价失败
     */
    private Integer isCalculatePriceSuccess;


    /**
     * 计价失败原因
     */
    private String calculatePriceFailReason;


    /**
     * 是否过滤，1：过滤，0：未过滤
     */
    private Integer isFilter;

    /**
     * 过滤原因
     */
    private String filterReason;

    /**
     * 是否调度(是否分配成功)
     * 1：已调度
     * 0或null：未调度
     */
    private Integer isDistribute;

    /**
     * 调度规则
     */
    private String distributeRule;

    /**
     * 是否自动抢单成功，1：成功，0：失败
     */
    private Integer isAutoGrabSuccess;

    /**
     * 自动抢单失败原因
     */
    private String autoGrabFailReason;

    /**
     * 版本号(标志同一次推单)
     */
    private String orderVersion;

    /**
     * 创建时间(匹配时间)
     */
    private Date createTime;
}
