package com.wanshifu.master.order.push.domain.rqt.repushPolicy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 : 启用/禁用重推策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class EnableRqt {

    /**
     * 机制id
     */
    @NotNull
    private Long policyId;

    /**
     * 状态 1:启用 0:禁用
     */
    @NotNull
    @ValueIn("1,0")
    private Integer policyStatus;


    /**
     * 更新人账号id
     */
    private Long updateAccountId;
}