package com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-09-06 09:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 策略id
     */
    @NotNull
    private Integer strategyId;

    private Long updateAccountId;
}