package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import com.wanshifu.framework.utils.DateUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class DurationTime extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception{
        if (list.length != 2){
            throw new Exception("操作数异常");
        }
        Long result;
        try {
            String startDateString= (String)list[0];
            String endsDateString= (String)list[1];

            if ( (StringUtils.isBlank(startDateString) && StringUtils.isBlank(endsDateString))) {
                return 0;
            }else{

                Date dateTime = new Date();
                if(startDateString != null && endsDateString != null){
                    Date startDateTime = DateUtils.parseDate(startDateString);
                    Date endDateTime = DateUtils.parseDate(endsDateString);

                    return dateTime.compareTo(startDateTime) >= 0 && dateTime.compareTo((endDateTime)) <= 0 ? 1 : 0;

                }else if(StringUtils.isNotBlank(startDateString )){
                    Date startDateTime = DateUtils.parseDate(startDateString);
                    return dateTime.compareTo(startDateTime) >= 0? 1 : 0;
                }else if(StringUtils.isNotBlank(endsDateString)){
                    Date endDateTime = DateUtils.parseDate(endsDateString);
                    return dateTime.compareTo(endDateTime) <= 0 ? 1 : 0;
                }


            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }
}
