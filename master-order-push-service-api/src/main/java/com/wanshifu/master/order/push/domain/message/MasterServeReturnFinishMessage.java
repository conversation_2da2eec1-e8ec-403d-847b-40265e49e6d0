package com.wanshifu.master.order.push.domain.message;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class MasterServeReturnFinishMessage {


    /**
     * 全局订单id
     */
    private Long globalOrderTraceId;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 订单号
     */
    private String orderNo;

    private Boolean isUploadParts;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 账号类型
     */
    private String masterSourceType;

    /**
     * 订单id
     */
    private Long masterOrderId;


    private BigDecimal logisticsPrice;

    /**
     * 账号id
     */
    private Long accountId;

    private String logisticsCompany;

    private String logisticsNo;

    /**
     * 订单来源
     */
    private String orderFrom;

    private String logisticsPhone;


}
