package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import com.wanshifu.master.order.push.domain.resp.simulatePush.GetSimulatePushResp;
import com.wanshifu.master.order.push.domain.rqt.simulatePush.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;


@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "simulatePush", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface SimulatePushApi {


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/getStrategyCombinationSimulate")
    List<StrategyCombinationSimulate> getStrategyCombinationSimulate(@RequestBody @Valid GetStrategyCombinationSimulateRqt rqt);



    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/getCombinationLastSimulate")
    StrategyCombinationSimulate getCombinationLastSimulate(@RequestBody @Valid GetCombinationLastSimulateRqt rqt);


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/addCombinationSimulate")
    Long addCombinationSimulate(@RequestBody @Valid AddCombinationSimulateRqt rqt);

    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/updateOrderNum")
    Integer updateOrderNum(@RequestBody @Valid UpdateOrderNumRqt rqt);


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/updateSimulateFinish")
    Integer updateSimulateFinish(@RequestBody @Valid UpdateSimulateFinishRqt rqt);



    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/getSimulatePush")
    GetSimulatePushResp getSimulatePush(@RequestBody @Valid GetSimulatePushRqt rqt);


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/addSimulatedOrderNum")
    Integer addSimulatedOrderNum(@RequestBody @Valid AddSimulateOrderNumRqt rqt);


    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/addSimulatePush")
    Long addSimulatePush(@RequestBody @Valid AddSimulatePushRqt rqt);



    /**
     * 创建重推机制
     * @param rqt
     * @return
     */
    @PostMapping("/batchAddSimulatePushDetail")
    Integer batchAddSimulatePushDetail(@RequestBody @Valid BatchAddSimulatePushDetailRqt rqt);


}
