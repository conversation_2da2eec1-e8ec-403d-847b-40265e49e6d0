package com.wanshifu.master.order.push.domain.vo.longTailStrategy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.dto.QlExpressDto;
import com.wanshifu.master.order.push.util.QlExpressUtil;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Data
public class LongTailGroupRule {


    /**
     * 开启条件
     */
    @NotNull
    @Valid
    private OpenCondition openCondition;


    /**
     * 长尾策略id
     */
    @NotNull
    private Long longTailStrategyId;

    /**
     * 开启条件item
     */
    @Data
    public static class OpenCondition {
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *开启条件规则项
         */
        @NotEmpty
        @Valid
        private List<Trigger.OpenConditionItem> itemList;

    }


    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }

    public JSONObject triggerQLExpressionJSON(){
        final JSONObject local = new JSONObject();
        final ArrayList<String> triggerParams = new ArrayList<>();
        final OpenCondition openCondition = getOpenCondition();

        final List<String> collect = openCondition.getItemList().stream().map(
                row -> {
                    String term = row.getTerm();
                    final String itemName = row.getItemName();
                    //时效标签 和 用户人群 操作符符号转换 特殊处理
                    if(SpecialWork.specialTerms.contains(itemName)){
                        term=StringUtils.equals("in", term) ? "containsAny" : "notContainsAny";
                    }

                    //服务特殊处理
                    if (itemName.equals("serve")) {

                        return serveSpecialExpression(row,triggerParams);
                    }

                    triggerParams.add(itemName);
                    return QlExpressUtil
                            .transitionQlExpress(itemName, term, row.getItemValue(), Integer.class);
                }
        ).collect(Collectors.toList());

        String openExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), collect);

        local.put("triggerExpression",openExpressions);
        local.put("triggerParams",triggerParams);
        local.put("longTailStrategyId",this.longTailStrategyId);
        return local;
    }


    private String serveSpecialExpression(Trigger.OpenConditionItem item,ArrayList<String> triggerParams){
        //服务 的开启条件特殊处理
        List<Trigger.OpenConditionItem> serveItems = new ArrayList<Trigger.OpenConditionItem>();
        serveItems.add(item);

        List<String> serveExpression = serveItems.stream().map(it -> {
            List<QlExpressDto> qlExpressDtoList = Lists.newArrayList();
            String itemCondition = StringUtils.equals(it.getTerm(), "in") ? "containsAny" : "notContainsAny";
            String condition = StringUtils.equals(it.getTerm(), "in") ? "or" : "and";

            List<List<Long>> serveIdList = it.getServeIdList();

            List<Long> serveLevel1Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 1)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(0))).collect(Collectors.toList());

            List<Long> serveLevel2Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 2)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(1))).collect(Collectors.toList());

            List<Long> serveLevel3Ids = serveIdList.stream().filter(serveIdListItem -> serveIdListItem.size() == 3)
                    .flatMap(serveIdListItem -> Stream.of(serveIdListItem.get(2))).collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(serveLevel1Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv1_serve_id", it.getTerm(), StringUtils.join(serveLevel1Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel2Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv2_serve_ids", itemCondition, StringUtils.join(serveLevel2Ids, ","),Long.class));
            }
            if (CollectionUtils.isNotEmpty(serveLevel3Ids)) {
                qlExpressDtoList.add(new QlExpressDto("lv3_serve_ids", itemCondition, StringUtils.join(serveLevel3Ids, ","),Long.class));
            }
            return StrUtil.format("({})", QlExpressUtil.transitionQlExpress(condition, qlExpressDtoList));
        }).collect(Collectors.toList());
        String serveExpressions = QlExpressUtil.transitionQlExpressStr(openCondition.getCondition(), serveExpression);


        if (CollectionUtils.isNotEmpty(serveItems)) {
            if (serveItems.stream().anyMatch(
                    it ->
                            CollectionUtils.isNotEmpty(it.getServeIdList())
                                    && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 1)
            )) {
                triggerParams.add("lv1_serve_id");
            }

            if (serveItems.stream().anyMatch(
                    it ->
                            CollectionUtils.isNotEmpty(it.getServeIdList())
                                    && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 2))) {
                triggerParams.add("lv2_serve_ids");
            }

            if (serveItems.stream().anyMatch(
                    it ->
                            CollectionUtils.isNotEmpty(it.getServeIdList())
                                    && it.getServeIdList().stream().filter(Objects::nonNull).anyMatch(var -> var.size() == 3))) {
                triggerParams.add("lv3_serve_ids");
            }
        }

        return serveExpressions;
    }


}
