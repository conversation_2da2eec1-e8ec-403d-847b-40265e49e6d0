package com.wanshifu.master.order.push.domain.enums;

/**
 * <AUTHOR>
 * 师傅类型枚举
 */
public enum MasterType {

    /**
     * 老师傅
     */
    MASTER_OLD("master_old"),

    /**
     * 新师傅
     */
    MASETR_NEW("master_new"),

    /**
     * 全部师傅
     */
    ALL("all");

    public String code;

    MasterType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }


}
