package com.wanshifu.master.order.push.domain.resp.exclusive;

import lombok.Data;

import java.util.Date;

/**
 *
 *
 * @since 2023-08-10 16:00:00
 */
@Data
public class GetExclusiveOrderInfoResp {

    /**
     * 专属订单标记:101:系统报价,102:抢单报价单,103成品报价,104家庭,105总包,106系统报价转抢单报价单
     */
    private Integer exclusiveFlag;
    /**
     * 标签名,exclusive:专属,direct_appointment:直约,preferred:优选,contract:合约(专属),brand:品牌
     */
    private String recruitTagName;

}
