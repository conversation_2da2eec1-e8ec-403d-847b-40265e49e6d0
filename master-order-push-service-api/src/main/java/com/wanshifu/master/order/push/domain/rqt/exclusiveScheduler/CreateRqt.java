package com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.RangeSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.StatusSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.TechniqueSelectVo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 描述 :  创建专属调度
 *
 * <AUTHOR>
 */
@Data
public class CreateRqt {

    /**
     * 业务线id
     */
    @NotNull
    private Integer businessLineId;

    private Integer categoryId;

    private String categoryIds;

    private Integer scheduleTime=0;


    private Long createAccountId;
}