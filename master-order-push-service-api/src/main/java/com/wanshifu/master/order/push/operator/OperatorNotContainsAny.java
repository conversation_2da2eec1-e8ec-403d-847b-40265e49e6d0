package com.wanshifu.master.order.push.operator;

import com.ql.util.express.Operator;
import com.wanshifu.framework.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 描述 : 二元操作符 notContainsAny  两个集合是否不存在交集
 * .
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-23 13:39
 */
@Slf4j
public class OperatorNotContainsAny extends Operator {
    @Override
    public Object executeInner(Object[] list) throws Exception {
        Object list1 = list[0];
        Object list2 = list[1];
        if (list1 == null || list2 == null) {
            throw new Exception("操作数为空,异常");
        } else if (!list1.getClass().isArray() && !(list1 instanceof List) && !list2.getClass().isArray() && !(list2 instanceof List)) {
            throw new Exception("操作数类型异常");
        } else if (list.length != 2) {
            throw new Exception("操作数数量异常");
        }
        try {
            return !CollectionUtils.containsAny((List<Object>) list1, Arrays.asList((Object[]) list2));
        } catch (Exception e) {
            log.error("operatorNotContainsAny is error");
            return false;
        }
    }
}