package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.util.Date;

/**
 * description: 将预约消息通知给用户端
 * author: <EMAIL>
 * createDate: 2019/8/718:17
 * version: 1.0
 **/
@Data
public class MasterReserveCustomerMessage {
    /**订单ID*/
    private Long globalOrderTraceId;

    /**预约结果*/
    private String reserveResult;

    /**预约开始时间*/
    private Date reserveBuyerStartTime;

    /**预约结束时间*/
    private Date reserveBuyerEndTime;

    /**失败理由*/
    private String failOrChangeReason;

    /**预约异常原因*/
    private String reserveExceptionReason;

    /**预约失败类型*/
    private String failOrChangReasonType;

    /**师傅ID*/
    private Long masterId;

    /**师傅名称*/
    private String masterName;

    /**师傅电话*/
    private String masterPhone;

    /***/
    private Integer reserveBuyerNum;

    /**用户类型*/
    private String accountType;

    /**用户ID*/
    private Long accountId;

    /**订单来源*/
    private String orderFrom;

    /**师傅订单ID*/
    private Long masterOrderId;

    /**订单编号*/
    private String orderNo;

    /**操作时间*/
    private Date operateTime;

}
