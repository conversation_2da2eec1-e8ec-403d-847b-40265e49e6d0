package com.wanshifu.master.order.push.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;


/**
 * 日期处理工具
 * 
 * <AUTHOR>
 * @since 2019-05-06
 */
@Slf4j
public class DateFormatterUtil {

	private static DateTimeFormatter yyyymmddhhmiss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static DateTimeFormatter yyyymmdd = DateTimeFormatter.ofPattern("yyyyMMdd");
	private static DateTimeFormatter yyyymmdd_ = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	private static final DateTimeFormatter FORMATTER_19 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final DateTimeFormatter FORMATTER_10 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	private static final DateTimeFormatter YYMM = DateTimeFormatter.ofPattern("yyyyMM");


	public static String assignDayString(String timeDay,int days) {
		LocalDateTime localDateTime = string2LocalDateTime(timeDay);
		return localDateTime.plusDays(days).format(yyyymmdd);
	}

	public static String formatDateString(String time,String format) {
		LocalDateTime localDateTime = string2LocalDateTime(time);
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
		return localDateTime.format(dateTimeFormatter);
	}
	/**
	 * 获取传入时间与当前时间的差值
	 * 
	 * @param timestamp
	 * @return
	 */
	public static Long getDuringFromNow(String timeString) {
		LocalDateTime localDateTime = LocalDateTime.parse(timeString, FORMATTER_19);
		LocalDateTime localDateTimeNow = LocalDateTime.now();
		return Duration.between(localDateTimeNow, localDateTime).toMinutes();
	}

	/**
	 * 传入时间距离当前时间的天数
	 * 
	 * @param timeString
	 *            时间字符串（ 支持多种时间格式，yyyy-MM-dd、yyyy-MM-dd HH:mm:ss、yyyy-MM-dd
	 *            HH:mm:ss.sss、秒时间戳）
	 * @return 距今天数
	 */
	public static Long durationDaysFromNow(String timeString) {
		LocalDateTime localDateTimeToCompare = string2LocalDateTime(timeString);
		if (localDateTimeToCompare == null) {
			return null;
		}
		LocalDateTime localDateTimeNow = LocalDateTime.now();
		return Duration.between(localDateTimeToCompare, localDateTimeNow).toDays();
	}

	public static Long durationMinutesFromNow(String timeString) {
		LocalDateTime localDateTimeToCompare = string2LocalDateTime(timeString);
		if (localDateTimeToCompare == null) {
			return null;
		}
		LocalDateTime localDateTimeNow = LocalDateTime.now();
		return Duration.between(localDateTimeToCompare, localDateTimeNow).toMinutes();
	}

	/**
	 * 字符串转为LocalDateTime对象
	 * 
	 * @param timeString
	 *            时间字符串（ 支持多种时间格式，yyyy-MM-dd、yyyy-MM-dd HH:mm:ss、yyyy-MM-dd
	 *            HH:mm:ss.sss、秒时间戳）
	 * @return LocalDateTime对象（如果是异常的时间格式或者不支持的时间格式，返回NULL）
	 */
	private static LocalDateTime string2LocalDateTime(String timeString) {
		timeString = StringUtils.trimToNull(timeString);
		if (timeString == null) {
			return null;
		}
		try {
			// 尝试yyyyMMdd转化
			if(timeString.length()==8) {
				LocalDate ld = LocalDate.parse(timeString, DateTimeFormatter.BASIC_ISO_DATE);
				return LocalDateTime.of(ld, LocalDateTime.now().toLocalTime());
			}
			if (timeString.length() == 10) {
				if (StringUtils.isNumeric(timeString)) {
					// 纯数字，尝试秒时间戳转换
					Instant instant = Instant.ofEpochMilli(Long.parseLong(timeString) * 1000);
					ZoneId zone = ZoneId.systemDefault();
					return LocalDateTime.ofInstant(instant, zone);
				} else {
					// 非纯数字，尝试yyyy-MM-dd格式转换
					LocalDate ld = LocalDate.parse(timeString, FORMATTER_10);
					return LocalDateTime.of(ld, LocalDateTime.now().toLocalTime());
				}
			} else if (timeString.length() == 19) {
				// 尝试yyyy-MM-dd HH:mm:ss格式转换
				return LocalDateTime.parse(timeString, FORMATTER_19);
			} else if (timeString.length() > 19) {
				// 截取前19位，尝试yyyy-MM-dd HH:mm:ss格式转换
				return LocalDateTime.parse(StringUtils.substring(timeString, 0, 19), FORMATTER_19);
			}
		} catch (Exception e) {
			return null;
		}
		return null;
	}

	/**
	 * 判断当前时间是否属于夜间（23:00-08:00）
	 * 
	 * @return
	 */
	private static final SimpleDateFormat DFT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final String NIGHT_START = "2300";
	private static final String NIGHT_END = "0700";

	private static final String NIGHT_START_V2 = "2200";
	private static final String NIGHT_CRITICAL = "2359";
	private static final String NIGHT_ZERO = "0000";

	private static final String NIGHT_END_V2 = "0800";

	public static boolean isDuringNightTime(String startTime,String endTime) {
		Calendar calendar = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat("HHmm");
		String hhMm = df.format(calendar.getTime());
		return hhMm.compareTo(startTime) > 0 && endTime.compareTo(hhMm) > 0;
//		return hhMm.compareTo(NIGHT_START) > 0 || NIGHT_END.compareTo(hhMm) > 0;
	}

	public static boolean isDuringNightTimeV2(String startTime,String endTime) {
	 	Calendar calendar = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat("HHmm");
		String hhMm = df.format(calendar.getTime());
		return hhMm.compareTo(startTime) > 0 && endTime.compareTo(hhMm) > 0;

//		return hhMm.compareTo(NIGHT_START_V2) > 0 || NIGHT_END_V2.compareTo(hhMm) > 0;
	}

	public static Long getDayTimingV2(){
		Calendar calendar = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat("HHmm");
		String hhMm = df.format(calendar.getTime());
		if (hhMm.compareTo(NIGHT_START_V2)>= 0&&NIGHT_CRITICAL.compareTo(hhMm) >= 0) {
			//22:00-23：59
			calendar.add(Calendar.DATE,1);
		}else if (hhMm.compareTo(NIGHT_ZERO)>= 0&&NIGHT_END_V2.compareTo(hhMm) > 0) {
			//00-08：00
		}
		calendar.set(Calendar.HOUR_OF_DAY,8);
		calendar.set(Calendar.MINUTE,0);
		return calendar.getTimeInMillis();
	}


//	public static boolean isDuringNightTimeV2(String start,String end) {
//		Calendar calendar = Calendar.getInstance();
//		final SimpleDateFormat df = new SimpleDateFormat("HHmm");
//		String hhMm = df.format(calendar.getTime());
//		return hhMm.compareTo(start) > 0 || end.compareTo(hhMm) > 0;
//	}


	/**
	 * 获取当前时间
	 * 
	 * @param time
	 * @return yyyyMMdd
	 */
	public static String getNow() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return localDateTime.format(yyyymmdd);
	}
	/**
	 * 获取当前时间
	 * 
	 * @param time
	 * @return yyyy-MM-dd
	 */
	public static String getNow1() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return localDateTime.format(FORMATTER_10);
	}

	public static String getNowYymm() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return localDateTime.format(YYMM);
	}

	/**
	 * 获取当前时间
	 * 
	 * @param time
	 * @return yyyyMMdd
	 */
	public static String getNowSp() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return localDateTime.format(FORMATTER_19);
	}
	
	/**
	 * 获取当前时间(Int)
	 * 
	 * @param time
	 * @return yyyyMMdd
	 */
	public static Integer getNowInt() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return Integer.valueOf(localDateTime.format(yyyymmdd));
	}


	/**
	 * 获取当前时间(Int)
	 *
	 * @param time
	 * @return yyyyMMdd
	 */
	public static Integer getNowMonth() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return Integer.valueOf(localDateTime.format(YYMM));
	}
	
	/**
	 * 获取当前时间
	 * 
	 * @param time
	 * @return yyyymmddhhmiss
	 */
	public static String getNowMi() {
		LocalDateTime localDateTime = LocalDateTime.now();
		return localDateTime.format(yyyymmddhhmiss);
	}
	/**
	 * 获取当前时间
	 * 
	 * @param time
	 */
	public static Long getNowTimeStamp() {
		return LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
	}
	public static Long getNowTimeStampMil() {
		return LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"))*1000;
	}
	/**
	 * 时间格式转换
	 * 
	 * @param time
	 * @return yyyymmddhhmiss
	 */
	public static String miToDay(String timeMi) {
		LocalDateTime localDateTime = string2LocalDateTime(timeMi);
		return localDateTime.format(yyyymmdd);
	}


	/**
	 * 时间格式转换
	 *
	 * @param timeMi
	 * @return yyyymmddhhmiss
	 */
	public static String miToMonth(String timeMi) {
		LocalDateTime localDateTime = string2LocalDateTime(timeMi);
		return localDateTime.format(YYMM);
	}


	public static String miToDayv2(String timeMi) {
		LocalDateTime localDateTime = string2LocalDateTime(timeMi);
		return localDateTime.format(FORMATTER_10);
	}
	
	/**
	 * 计算当天的前/后第N天
	 * 
	 * @param dt
	 *            当前日期
	 * @param cnt
	 *            0-当天，1-后一天，-1-前一天，以此类推......
	 * @return
	 */
	public static String getAssignDay(int cnt) {
		LocalDateTime localDateTime = LocalDateTime.now();
		localDateTime = localDateTime.plusDays(cnt);
		String result = localDateTime.format(yyyymmdd);
		return result;
	}
	
	/**
	 * 计算当天的前/后第N天
	 * 
	 * @param dt
	 *            当前日期
	 * @param cnt
	 *            0-当天，1-后一天，-1-前一天，以此类推......
	 * @return
	 */
	public static String getAssignDayMi(int cnt) {
		LocalDateTime localDateTime = LocalDateTime.now();
		localDateTime = localDateTime.plusDays(cnt);
		String result = localDateTime.format(yyyymmddhhmiss);
		return result;
	}
	/**
	 * 计算当天的前/后第N天
	 * 
	 * @param dt
	 *            当前日期
	 * @param cnt
	 *            0-当天，1-后一天，-1-前一天，以此类推......
	 * @return
	 */
	public static Long getAssignDayTimeStamp(int cnt) {
		LocalDateTime localDateTime = LocalDateTime.now();
		localDateTime = localDateTime.plusDays(cnt);
		return localDateTime.toEpochSecond(ZoneOffset.of("+8"));
	}
	public static Long getAssignDayTimeStampMils(int cnt) {
		LocalDateTime localDateTime = LocalDateTime.now();
		localDateTime = localDateTime.plusDays(cnt);
		return localDateTime.toEpochSecond(ZoneOffset.of("+8"))*1000;
	}
	public static Long getAssignMinutesTimeStampMils(int cnt) {
		LocalDateTime localDateTime = LocalDateTime.now();
		localDateTime = localDateTime.plusMinutes(cnt);
		return localDateTime.toEpochSecond(ZoneOffset.of("+8"))*1000;
	}
	
	/**
	 * localData to yyyyddmm
	 * @param localDate
	 * @return
	 */
	public static String localDateToString(LocalDate localDate) {
		return LocalDateTime.of(localDate, LocalDateTime.now().toLocalTime()).format(yyyymmdd);
	}
	
	/**
	 * timeToTimestamp
	 * @param localDate
	 * @return
	 */
	public static Long timeToTimestamp(String time) {
		return string2LocalDateTime(time).toInstant(ZoneOffset.of("+8")).toEpochMilli();
	}

	/**
	 * timeToTimestamp
	 * @param localDate
	 * @return
	 */
	public static String timeStampToTime(Long timestamp) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(yyyymmddhhmiss);
	}
	public static String timeStampToTimed(Long timestamp) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(yyyymmdd);
	}


	public static String timeStampToTimedMonth(Long timestamp) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(YYMM);
	}

	/**
	 * sql.date to util.date
	 * @param timestamp
	 * @return
	 */
	public static String sqlDateToString(java.sql.Date date) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(date.getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(FORMATTER_10);
	}
	public static String sqlDateToFullString(java.sql.Date date) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(date.getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(FORMATTER_10);
	}

	public static Long getAssignTimeWithUnit(int cnt, TimeUnit timeUnit) {
		LocalDateTime localDateTime = LocalDateTime.now();
		switch(timeUnit){
			case DAYS:
				localDateTime = localDateTime.plusDays(cnt);
		        break;
			case MINUTES:
				localDateTime = localDateTime.plusMinutes(cnt);
				break;
			case SECONDS:
				localDateTime = localDateTime.plusSeconds(cnt);
				break;
		    default:
		        break;
		}
		return localDateTime.toEpochSecond(ZoneOffset.of("+8"));
	}

	public static String timeStampToTimeDay(Long timestamp) {
		LocalDateTime localDateTime = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
		return localDateTime.format(yyyymmdd_);
	}


	public static  boolean isBetweenPeriodTime(String date1, String date2,Calendar date){
		try{
			SimpleDateFormat df = new SimpleDateFormat("HH:mm");
			Calendar begin = Calendar.getInstance();
			Calendar end = Calendar.getInstance();
			date.setTime(df.parse(df.format(new Date())));
			Date startTimeDate = df.parse(date1);
			Date endTimeDate = df.parse(date2);
			begin.setTime(startTimeDate);
			end.setTime(endTimeDate);
			if (endTimeDate.getHours() < startTimeDate.getHours()) {
				if (date.after(begin) || date.before(end)) {
					return true;
				}else {
					return false;
				}
			}else if(endTimeDate.getHours() == startTimeDate.getHours()){
				if (endTimeDate.getMinutes() < startTimeDate.getMinutes()) {
					if (date.after(begin) || date.before(end)) {
						return true;
					}else {
						return false;
					}
				}
			}

			//这里是时间段的起止都在同一天的情况，只需要判断当前时间是否在这个时间段内即可
			if (date.after(begin) && date.before(end)) {
				return true;
			}else {
				return false;
			}
		}catch(Exception e){
			log.error("计算当前时间是否在指定时间段失败",e);
		}

		return true;

	}


	public static  boolean isBetweenPeriodTime(String date1, String date2) {
		try {
			SimpleDateFormat df = new SimpleDateFormat("HH:mm");
			Calendar date = Calendar.getInstance();
			Calendar begin = Calendar.getInstance();
			Calendar end = Calendar.getInstance();
			date.setTime(df.parse(df.format(new Date())));
			Date startTimeDate = df.parse(date1);
			Date endTimeDate = df.parse(date2);
			begin.setTime(startTimeDate);
			end.setTime(endTimeDate);
			if (endTimeDate.getHours() < startTimeDate.getHours()) {
				if (date.after(begin) || date.before(end)) {
					return true;
				} else {
					return false;
				}
			} else if (endTimeDate.getHours() == startTimeDate.getHours()) {
				if (endTimeDate.getMinutes() < startTimeDate.getMinutes()) {
					if (date.after(begin) || date.before(end)) {
						return true;
					} else {
						return false;
					}
				}
			}

			//这里是时间段的起止都在同一天的情况，只需要判断当前时间是否在这个时间段内即可
			if (date.after(begin) && date.before(end)) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("计算当前时间是否在指定时间段失败", e);
		}

		return true;

	}


	public static boolean isTimeRangeOverlap(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
		List<Range> range1 = expandRange(start1, end1);
		List<Range> range2 = expandRange(start2, end2);

		// 检查所有组合是否有重叠
		for (Range r1 : range1) {
			for (Range r2 : range2) {
				if (r1.overlaps(r2)) {
					return true;
				}
			}
		}
		return false;
	}


	public static boolean isTimeRangeOverlap(String startTime1, String endTime1, String startTime2, String endTime2) {

		LocalTime start1 = LocalTime.parse(startTime1);
		LocalTime end1 = LocalTime.parse(endTime1);
		LocalTime start2 = LocalTime.parse(startTime2);
		LocalTime end2 = LocalTime.parse(endTime2);

		return isTimeRangeOverlap(start1,end1,start2,end2);
	}

	// 将时间段展开为最多两段
	private static List<Range> expandRange(LocalTime start, LocalTime end) {
		if (start.equals(end)) {
			// 视为全天
			return Arrays.asList(new Range(LocalTime.MIN, LocalTime.MAX));
		}
		if (start.isBefore(end)) {
			return Arrays.asList(new Range(start, end));
		} else {
			// 跨过午夜，分两段
			return Arrays.asList(
					new Range(start, LocalTime.MAX),
					new Range(LocalTime.MIN, end)
			);
		}
	}

	// 时间段类
	private static class Range {
		LocalTime start;
		LocalTime end;

		public Range(LocalTime start, LocalTime end) {
			this.start = start;
			this.end = end;
		}

		public boolean overlaps(Range other) {
			return !this.end.isBefore(other.start) && !other.end.isBefore(this.start);
		}
	}


}
