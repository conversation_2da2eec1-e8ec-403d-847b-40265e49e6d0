package com.wanshifu.master.order.push.domain.common;


import lombok.Data;

/**
 * 推送上下文订单状态
 * 
 * <AUTHOR>
 *
 */
@Data
public class OrderStatus {
//	private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatus.class);
	/**
	 * 订单ID
	 */
	private Long globalOrderId;
	private Long userOrderId;
	private Long enterpriseOrderId;
	
	public Long getUserOrderId() {
		return userOrderId;
	}
	public Long getEnterpriseOrderId() {
		return enterpriseOrderId;
	}
	public Long getGlobalOrderId() {
		return globalOrderId;
	}

	/**
	 * 订单状态
	 */
	private OrderStatusValue orderStatus = OrderStatusValue.NORMAL;

	/**
	 * 当前报价数
	 */
	private Integer offerNum = 0;

	/**
	 * 订单版本
	 */
	private String orderVersion;

	/**
	 * 构建订单状态（关单,雇佣,报价达标,修改）
	 * 
	 * @param orderId
	 */
	public OrderStatus(Long orderId) {
		this.globalOrderId = orderId;
	}

	public OrderStatus(Long globalOrderId, String orderVersion) {
		this.globalOrderId = globalOrderId;
		this.orderVersion = orderVersion;
	}



	/**
	 * 获取订单状态
	 * 
	 * @return
	 */
	public OrderStatusValue getOrderStatus() {
		return orderStatus;
	}

	public String getOrderVersion() {
		return orderVersion;
	}

	@Override
	public String toString() {
		return "OrderStatus [orderId=" + globalOrderId + ", orderStatus=" + orderStatus + ", orderVersion=" + orderVersion + "]";
	}

}
