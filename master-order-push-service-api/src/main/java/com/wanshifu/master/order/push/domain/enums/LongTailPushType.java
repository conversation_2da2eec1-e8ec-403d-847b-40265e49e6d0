package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 师傅类型枚举
 */
public enum LongTailPushType {

    /**
     * 红包订单
     */
    NEARBY_MORE("nearby_more","附近更多"),

    /**
     * 红包订单
     */
    BONUS_ORDER("bonus_order","红包订单"),

    /**
     * 区县外订单
     */
    OUT_DISTRICT("out_district","区县外订单");



    private final String code;
    private final String name;

    LongTailPushType(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, LongTailPushType> valueMapping = new HashMap<>((int) (LongTailPushType.values().length / 0.75));

    static {
        for (LongTailPushType instance : LongTailPushType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static LongTailPushType asValue(String value) {
        return valueMapping.get(value);
    }


}
