package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.domain.resp.orderPackage.PackageOrderOfferPriceResp;
import com.wanshifu.master.order.push.domain.rqt.orderPackage.PackageOrderMasterOfferPriceRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "master-inner-api", url = "${wanshifu.master-inner-api.url}", path = "orderPackageMaster", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface PackageOrderApi {


    /**
     * 获取订单包价格
     * @return
     */
    @PostMapping("getOfferPrice")
    PackageOrderOfferPriceResp getOfferPrice(@Validated @RequestBody PackageOrderMasterOfferPriceRqt rqt);


}
