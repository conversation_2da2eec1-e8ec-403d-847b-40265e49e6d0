package com.wanshifu.master.order.push.domain.rqt;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 推单消息实体
 * <AUTHOR>
 */
@Data
public class OrderPushRqt {


    /**
     * 账号类型
     */
    @JSONField(name="account_type")
    private String accountType;

    /**
     * 服务类型
     */
    @JSONField(name="serve_type")
    private Integer serveType;

    /**
     * 订单三级地址
     */
    @JSONField(name="third_division_id")
    private Long thirdDivisionId;

    /**
     * 最大报价人数
     */
    @JSONField(name="max_offer_number")
    private Integer maxOfferNumber;

    /**
     * 全局订单id
     */
    @JSONField(name="global_order_trace_id")
    private Long globalOrderId;

    /**
     * 类目id
     */
    @JSONField(name="category_id")
    private Long categoryId;

    /**
     * 订单服务绑定的技能
     */
    @JSONField(name="binding_technology_ids")
    private String bindingTechnologyIds;

    /**
     * 订单商品
     */
    @JSONField(name="order_goods")
    private String orderGoods;

    /**
     * 订单四级地址（街道/乡镇）
     */
    @JSONField(name="fourth_division_id")
    private Long fourthDivisionId;

    /**
     * 订单二级地址（城市）
     */
    @JSONField(name="second_division_id")
    private Long secondDivisionId;

    /**
     * 期待上门开始时间
     */
    @JSONField(name="expect_door_in_start_date")
    private String expectDoorInStartDate;

    /**
     * 团队师傅推单
     */
    @JSONField(name="team_master_order_push")
    private Integer teamMasterOrderPush;

    /**
     * 师傅订单id
     */
    @JSONField(name="master_order_id")
    private Long masterOrderId;

    /**
     * 一级服务id
     */
    @JSONField(name="serve_level_1_ids")
    private String lv1ServeIds;


    /**
     * 二级服务id
     */
    @JSONField(name="serve_level_2_ids")
    private String lv2ServeIds;


    /**
     * 三级服务id
     */
    @JSONField(name="serve_ids")
    private String lv3ServeIds;


    /**
     * 业务线id
     */
    @JSONField(name="business_line_id")
    private Integer businessLineId;

    /**
     * 订单客户地址经纬度
     */
    @JSONField(name="order_lng_lat")
    private String orderLngLat;

    /**
     * 楼层数
     */
    @JSONField(name="floor_num")
    private Integer floorNum;


    /**
     * 加急单标识：
     */
    @JSONField(name="emergency_order_flag")
    private Integer emergencyOrderFlag;


    /**
     * 准时单标识 0、普通订单 1、准时单
     */
    @JSONField(name="on_time_order_flag")
    private Integer onTimeOrderFlag;


    /**
     * 定时单标记
     */
    @JSONField(name="timer_flag")
    private Integer timerFlag;

    /**
     * 指派模式
     */
    @JSONField(name="appoint_type")
    private Integer appointType;

    /**
     * 期望上门结束时间
     */
    @JSONField(name="expect_door_in_end_date")
    private String expectDoorInEndDate;

    /**
     * 订单标签
     */
    @JSONField(name="order_label")
    private String orderLabel;

    /**
     * 是否有电梯
     */
    @JSONField(name="has_lift")
    private Integer hasLift;

    /**
     * 账号id
     */
    @JSONField(name="account_id")
    private Long accountId;

    /**
     * 订单来源
     */
    @JSONField(name="order_from")
    private String orderFrom;


    /**
     * 推单模式
     */
    @JSONField(name="push_mode")
    private String pushMode;

    /**
     * 订单推单地址等级
     */
    @JSONField(name="master_push_address")
    private Integer masterPushAddress;

    /**
     * 期望完成时间
     */
    @JSONField(name="expect_complete_time")
    private String expectCompleteTime;

    /**
     * 推单排除师傅id-专属退款
     */
    @JSONField(name="order_push_exclude_master_ids")
    private String orderPushExcludeMasterIds;


    /**
     * 推单排除师傅id-普通推单
     */
    @JSONField(name="order_push_eliminate_master_ids")
    private List<Long> orderPushEliminateMasterIds;

    /**
     * 测量订单全局订单id
     */
    @JSONField(name="measure_global_order_trace_id")
    private String measureGlobalOrderTraceId;

    @JSONField(name="priority_push_measure_master")
    private Integer priorityPushMeasureMaster;

    /**
     * 用户id
     */
    @JSONField(name="user_id")
    private Long userId;

    /**
     * 专属招募id
     */
    @JSONField(name="recruit_id")
    private Long recruitId;


    @JSONField(name="has_price")
    private Boolean hasPrice;

    @JSONField(name="operate_flag")
    private String operateFlag;


    @JSONField(name="customer_phone")
    private String customerPhone;


    @JSONField(name="is_exclusive_team_master")
   private Integer isExclusiveTeamMaster;

}
