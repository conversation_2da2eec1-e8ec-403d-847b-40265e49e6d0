package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.rqt.pushmatchlog.PushMatchLogRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/4/25 18:21
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "pushMatchLog", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface PushMatchLogApi {

    /**
     * 推送匹配日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("list")
    @Deprecated
    SimplePageInfo<PushMatchLogDto> pushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);

    /**
     * 平台协议派单日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("userAgreementList")
    SimplePageInfo<UserAgreementPushMatchLogDto> userAgreementPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);

    /**
     * 总包直接指派日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("enterpriseAppointList")
    SimplePageInfo<EnterpriseAppointPushMatchLogDto> enterpriseAppointPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);

    /**
     * 合作经营派单日志列表
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("cooperationBusinessList")
    SimplePageInfo<CooperationBusinessPushMatchLogDto> cooperationBusinessPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);

    /**
     * 样板城市派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("newModelCityList")
    SimplePageInfo<NewModelCityPushMatchLogDto> newModelCityPushMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);


    /**
     * 全时师傅派单日志列表
     *
     * @param pushMatchLogRqt
     * @return
     */
    @PostMapping("fullTimeMatchLogList")
    SimplePageInfo<OrderFullTimeMasterMatchLogDto> fullTimeMatchLogList(@Valid @RequestBody PushMatchLogRqt pushMatchLogRqt);

    }
