package com.wanshifu.master.order.push.domain.rqt;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class RefundSyncRqt {
    private Long userId;
    private Long masterId;
    private Integer serveType;
    private Long orderId;
    private Date refundTime;

    public RefundSyncRqt(Long userId, Long masterId, Integer serveType, Long orderId, Date refundTime) {
        this.userId = userId;
        this.masterId = masterId;
        this.serveType = serveType;
        this.orderId = orderId;
        this.refundTime = refundTime;
    }

    public RefundSyncRqt(){

    }
}
