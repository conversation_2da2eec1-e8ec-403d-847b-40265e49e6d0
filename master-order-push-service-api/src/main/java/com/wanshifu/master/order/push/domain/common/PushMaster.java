package com.wanshifu.master.order.push.domain.common;

import lombok.Data;

import javax.annotation.Generated;
import java.math.BigDecimal;


/**
 * 推送师傅(用于推送控制)
 * 
 * <AUTHOR>
 *
 */
@Data
public class PushMaster implements Comparable<PushMaster> {

	/**
	 * 师傅ID
	 */
	private String masterId;

	/**
	 * 师傅类型→是否为新师傅
	 */
	private boolean isNewMaster;

	/**
	 * 是否是强推师傅
	 */
	private boolean isForcePushMaster;


	/**
	 * 是否是强推师傅
	 */
	private boolean isPriorityPush;


	private String baseSelectType;

	/**
	 * 是否跨城：1: 跨城市推单，0：非跨城城市推单
	 */
	private Integer isCrossCityPush;

	private Long pushDistance;


	private String rounds;

	private Integer mustOrderFlag;

	/**
	 * 师傅是否被限制推单
	 */
	private Integer isPushRestrict;

	private String idCardNumber;

	private Integer additionalQuoteOrderNum;

	/**
	 * 师傅来源类型，tob: B端师傅，toc: C端师傅
	 */
	private String masterSourceType;

	/**
	 * 全时/分时师傅
	 */
	private Integer masterTimeType;

	/**
	 * 分时师傅分流标记，0: 不分流，1：分流
	 */
	private Integer shuntFlag = 0;

	public Integer getShuntFlag() {
		return shuntFlag;
	}

	public void setShuntFlag(Integer shuntFlag) {
		this.shuntFlag = shuntFlag;
	}


	public boolean isForcePushMaster() {
		return isForcePushMaster;
	}



	/**
	 * 师傅推送评分
	 */
	private BigDecimal score = BigDecimal.ZERO;

	public void setNewMaster() {
		this.isNewMaster = true;
	}



	public void setForcePushMaster() {
		this.isForcePushMaster = true;
	}

	public void setScore(BigDecimal score) {
		if (score == null) {
			this.score= BigDecimal.ZERO;
			return;
		}
		this.score = score;
	}

	/**
	 * 获取师傅ID
	 * 
	 * @return
	 */
	public String getMasterId() {
		return masterId;
	}

	/**
	 * 获取师傅类型
	 * 
	 * @return
	 */
	public boolean isNewMaster() {
		return isNewMaster;
	}



	/**
	 * 获取评分
	 * 
	 * @return
	 */
	public BigDecimal getScore() {
		return score;
	}



	@Override
	public int compareTo(PushMaster o) {
		return o.score.compareTo(this.score);
	}

	@Override
	public String toString() {
		return masterId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((masterId == null) ? 0 : masterId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		String other = obj.toString();
		if (masterId == null) {
			if (other != null)
				return false;
		} else if (!masterId.equals(other))
			return false;
		return true;
	}

	@Generated("SparkTools")
	private PushMaster(Builder builder) {
		this.masterId = builder.masterId;
		this.isNewMaster = builder.isNewMaster;
		this.isForcePushMaster = builder.isForcePushMaster;
		this.score = builder.score;
	}

	public PushMaster() {

	}
	/**
	 * Creates builder to build {@link PushMaster}.
	 * @return created builder
	 */
	@Generated("SparkTools")
	public static Builder builder() {
		return new Builder();
	}

	/**
	 * Builder to build {@link PushMaster}.
	 */
	@Generated("SparkTools")
	public static final class Builder {
		private String masterId;
		private boolean isNewMaster;
		private boolean isForcePushMaster;
		private BigDecimal score= BigDecimal.ZERO;

		private Builder() {
		}

		public Builder setMasterId(String masterId) {
			this.masterId = masterId;
			return this;
		}

		public Builder setIsNewMaster(boolean isNewMaster) {
			this.isNewMaster = isNewMaster;
			return this;
		}

		public Builder setIsForcePushMaster(boolean isForcePushMaster) {
			this.isForcePushMaster = isForcePushMaster;
			return this;
		}

		public Builder setScore(BigDecimal score) {
			this.score = score;
			return this;
		}

		public PushMaster build() {
			return new PushMaster(this);
		}
	}


	public void addScore(BigDecimal scoreChange) {
		this.score = this.score.add(scoreChange);
	}


}
