package com.wanshifu.master.order.push.domain.vo.baseSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述 :  服务数据初筛.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>@wshifu.com
 * @date : 2024-06-12 11:01
 */
@Data
public class ServeDataSelectVo {


    /**
     * 服务数据类型：
     * last_1y_serve_complete： 师傅有近一年该服务的完工数据
     * last_1y_serve_appoint： 师傅有近一年该服务的接单数据
     */
    @NotEmpty
    @ValueIn("last_1y_serve_complete,last_1y_serve_appoint")
    private String serveDataType;


}