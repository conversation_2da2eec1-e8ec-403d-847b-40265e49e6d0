package com.wanshifu.master.order.push.domain.po;

import javax.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 协议订单调度策略
 */
@Data
@ToString
@Table(name = "agreement_order_distribute_strategy")
public class AgreementOrderDistributeStrategy {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;

    /**
     * 策略组合名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略组合描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 城市id，多个以逗号拼接,all表示全国
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     *  调度策略列表
     */
    @Column(name = "distribute_strategy_list")
    private String distributeStrategyList;

    /**
     *  调度策略列表
     */
    @Column(name = "distribute_strategy_expression_list")
    private String distributeStrategyExpressionList;


    /**
     *  调度策略列表
     */
    @Column(name = "compensate_distribute_list")
    private String compensateDistributeList;


    /**
     * 开启条件表达式JSON
     */
    @Column(name = "compensate_distribute_strategy_list")
    private String compensateDistributeStrategyList;

    /**
     * 策略状态，1：已启用，0：已禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;


    /**
     * 更新人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

}