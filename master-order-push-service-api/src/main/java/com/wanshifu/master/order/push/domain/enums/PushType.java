package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 推单模式
 * <AUTHOR>
 */
public enum PushType {

	/**
	 * 普通推单
	 */
	NORMAL("normal"),

	/**
	 * 代理商推单
	 */
	AGENT("agent"),


	/**
	 * 订单包推单
	 */
	ORDER_PACKAGE("order_package"),

	/**
	 * 专属推单
	 */
	EXCLUSIVE("exclusive"),

	/**
	 * 品牌师傅推单
	 */
	BRAND("brand"),


	AGREEMENT("agreement"),

	/**
	 * 直接指派
	 */
	DIRECT_APPOINT("direct_appoint"),

	/**
	 * 非智能推单
	 */
	NON_INTELLIGENT("non_intelligent"),


	/**
	 * 匹配扶持师傅
	 */
	SUPPORT_MASTER("support_master"),


	/**
	 * 样板城市
	 */
	NEW_MODEL("new_model"),


	/**
	 * 样板城市
	 */
	ENTERPRISE_APPOINT("enterprise_appoint"),


	/**
	 * 指派合约师傅（作业帮）
	 */
	COLLECT_CONTRACT_MASTER("collect_contract_master"),

    /**
     * 合作经营师傅
     */
    COOPERATION_BUSINESS_MASTER("cooperation_business_master"),



	/**
	 * 家庭协议师傅
	 */
	FAMILY_AGREEMENT_MASTER("family_agreement_master"),


	/**
	 * 金牌维修师傅
	 */
	GOLD_MEDAL_MASTER("gold_medal_master");



	public final String code;

	PushType(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}


	@Override
	public String toString(){
		return code;
	}

	private static final Map<String, PushType> valueMapping = new HashMap<>((int) (PushType.values().length / 0.75));

	static {
		for (PushType instance : PushType.values()) {
			valueMapping.put(instance.code, instance);
		}
	}

	public static PushType asCode(String code) {
		return valueMapping.get(code);
	}


}
