package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.CreateOrderMatchRouteRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.OrderMatchRouteListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.UpdateOrderMatchRouteRqt;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "orderMatchRoute",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface OrderMatchRouteApi {

    /**
     * 长尾策略组列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<OrderMatchRoute> list(@RequestBody @Valid OrderMatchRouteListRqt rqt);

    /**
     * 创建长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    Integer create(@RequestBody @Valid CreateOrderMatchRouteRqt rqt);


    /**
     * 修改长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    Integer update(@RequestBody @Valid UpdateOrderMatchRouteRqt rqt);



    /**
     * 策略组详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    OrderMatchRoute detail(@RequestBody @Valid OrderMatchRouteDetailRqt rqt);


}
