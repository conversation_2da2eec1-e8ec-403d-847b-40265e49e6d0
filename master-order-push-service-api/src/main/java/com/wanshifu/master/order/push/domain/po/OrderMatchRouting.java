package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.Date;

@Data
public class OrderMatchRouting {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "routing_id")
    private Integer routingId;

    @Column(name = "routing_name")
    private String routingName;

    @Column(name = "routing_desc")
    private String routingDesc;

    @Column(name = "order_from")
    private String orderFrom;

    @Column(name = "order_tag")
    private String orderTag;

    @Column(name = "routing_type")
    private String routingType;

    @Column(name = "match_routing")
    private String matchRouting;


    @Column(name = "lv1_master_type")
    private String lv1MasterType;

    @Column(name = "lv2_master_type")
    private String lv2MasterType;

    @Column(name = "lv3_master_type")
    private String lv3MasterType;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
