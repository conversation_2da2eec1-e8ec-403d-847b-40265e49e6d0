package com.wanshifu.master.order.push.domain.message;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MasterHangOrderMessage {

    private List<HangOrderServeInfo> orderMasterInfoList;

    private String hangReason;

    private String hangDesc;

    private String hangEvidence;

    private Date operateTime;

    private String historyMark;


    @Data
    public static class HangOrderServeInfo {

        /**
         * 订单服务主键ID
         */
        private Long orderServeId;

        /**
         * 师傅ID
         */
        private Long masterId;

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 全局订单ID
         */
        private Long globalOrderTraceId;


    }

}
