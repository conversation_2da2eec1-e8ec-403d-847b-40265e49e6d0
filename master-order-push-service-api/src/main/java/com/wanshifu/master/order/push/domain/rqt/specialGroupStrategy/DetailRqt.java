package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 特殊人群策略详情请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@ApiModel(description = "特殊人群策略详情请求类")
@Data
public class DetailRqt {

    @ApiModelProperty(value = "策略id", required = true)
    @NotNull(message = "策略id不能为空")
    private Long strategyId;
}
