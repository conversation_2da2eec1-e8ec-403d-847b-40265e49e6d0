package com.wanshifu.master.order.push.util;


import com.wanshifu.master.order.push.domain.constant.FieldConstant;

/**
 * 
 * <AUTHOR>
 *
 */
public class VariableCastUtil {

	public static Object variableCast(Object variable,String goalType){
		String dataSource= variable.toString();
		Object result;
		if (FieldConstant.DEFAULT_UNSPECIFIED.equals(dataSource)) {
			return FieldConstant.DEFAULT_UNSPECIFIED;
		}
		try {
			switch (goalType) {
			case "String":
					result=dataSource;
				break;
			case "Long":
					result=Long.valueOf(dataSource);
				break;
			case "Double":
					result=Double.valueOf(dataSource);
				break;

				case "Integer":
					result=Integer.valueOf(dataSource);
					break;

			default:
				return "default";
			}
		} catch (Exception e) {
			result=dataSource;
		}
		return result;
	}

}
