package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.domain.common.DefaultDecoder;
import com.wanshifu.master.order.push.domain.resp.exclusive.GetExclusiveOrderInfoResp;
import com.wanshifu.master.order.push.domain.rqt.exclusive.GetExclusiveOrderInfoRqt;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "master-inner-api",
        url = "${wanshifu.master-inner-api.url}",
        path = "/order/serveInfo",
        configuration = {DefaultEncoder.class, DefaultDecoder.class, DefaultErrorDecode.class})
public interface OrderMessageApi {

    /**
     * 获取专属活动标签信息---推单入库前的匹配专属标签
     *
     * @return
     */
    @PostMapping("getExclusiveOrderInfo")
    GetExclusiveOrderInfoResp getExclusiveOrderInfo(@Validated @RequestBody GetExclusiveOrderInfoRqt rqt);
}