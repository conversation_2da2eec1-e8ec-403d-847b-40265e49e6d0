package com.wanshifu.master.order.push.api;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.PushLimitRule;
import com.wanshifu.master.order.push.domain.rqt.pushLimitRule.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 补偿调度
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "pushLimitRule", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface PushLimitRuleApi {

    /**
     * 创建初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 修改初筛策略
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    PushLimitRule detail(@RequestBody @Valid DetailRqt rqt);


    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<PushLimitRule> list(@RequestBody @Valid ListRqt rqt);



    /**
     * 初筛策略详情
     * @param rqt
     * @return
     */
    @PostMapping("/enable")
    Integer enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除补偿调度策略
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    Integer delete(@RequestBody @Valid DeleteRqt rqt);




}
