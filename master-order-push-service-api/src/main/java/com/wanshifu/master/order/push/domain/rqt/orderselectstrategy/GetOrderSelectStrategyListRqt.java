package com.wanshifu.master.order.push.domain.rqt.orderselectstrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 查询筛选策略列表请求实体类
 * @date 2025/3/4 11:27
 */
@Data
public class GetOrderSelectStrategyListRqt {

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Integer businessLineId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 类目id
     */
    private String categoryIds;

    private Integer pageNum = 1;

    private Integer pageSize = 20;

    /**
     * 策略状态
     */
    private Integer strategyStatus;


    /**
     * 师傅资源
     */
    @ValueIn("auto_receive,agreement,agent,new_master,new_model,cooperation_business_master,full_time_master,after_verify_new_master")
    private String masterResources;

}
