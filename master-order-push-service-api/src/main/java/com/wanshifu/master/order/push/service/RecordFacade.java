//package com.wanshifu.master.order.push.service;
//
//import com.alibaba.fastjson.JSONObject;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 表格存储Facade
// * <AUTHOR>
// */
//@Component
//public class RecordFacade {
//
//	@Resource
//	private RecordTableStoreService recordTableStoreService;
//
////	/**
////	 * 记录初筛
////	 */
////	public void baseSelectRecord(String globalOrderId,Set<String> masterSet,int regionMode,String orderVersion) {
////		String masterSetString= StringUtils.join(masterSet, ",");
////		Record baseSelectRecord=new Record(globalOrderId, RecordType.BASE_SELECT_RECORD,regionMode, masterSetString);
////		recordTableStoreService.saveBaseSelectRecord(baseSelectRecord,orderVersion);
////	}
//
//	/**
//	 * 记录初筛
//	 * @param globalOrderId
//	 * @param masterSet
//	 * @param regionMode
//	 * @param orderVersion
//	 */
////	public void baseSelectRecord(String globalOrderId,Set<String> masterSet,int regionMode,String orderVersion,String pushMode) {
////		String masterSetString=StringUtils.join(masterSet, ",");
////		Record baseSelectRecord=new Record(globalOrderId, RecordType.BASE_SELECT_RECORD,regionMode, masterSetString);
////		if (pushMode!=null&& PushMode.AGENT.code.equals(pushMode)) {
////			recordTableStoreService.saveBaseSelectRecord(baseSelectRecord,orderVersion,pushMode);
////		}else if (pushMode!=null&& PushMode.PRE_EXCLUSIVE.code.equals(pushMode)) {
////			recordTableStoreService.saveBaseSelectRecord(baseSelectRecord,orderVersion,pushMode);
////		}
////		else {
////			recordTableStoreService.saveBaseSelectRecord(baseSelectRecord,orderVersion);
////		}
////	}
//
//	/**
//	 * 记录订单特征
//	 * @param globalOrderId
//	 * @param fieldName
//	 * @param fieldValue
//	 */
//	public void orderFeatureRecord(String globalOrderId,String fieldName,String fieldValue) {
//		recordTableStoreService.saveOrderFeature(globalOrderId, fieldName, fieldValue);
//	}
//
//	/**
//	 * 订单信息消息记录
//	 * @param masterOrderId
//	 * @param orderVersion
//	 * @param orderDetail
//	 */
//	public void orderDetailRecord(String masterOrderId,String orderVersion,JSONObject orderDetail) {
//		recordTableStoreService.saveOrderDetailRecord(masterOrderId, orderVersion, orderDetail);
//	}
//
//	/**
//	 * 推送记录
//	 * @param masterOrderId
//	 * @param pushTime
//	 * @param pushMaster
//	 */
//	public void saveOrderPushRecordLine(String masterOrderId, String pushTime, String pushMaster) {
//		recordTableStoreService.saveOrderPushRecordLine(masterOrderId, pushTime, pushMaster);
//	}
//
////	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {
////		recordTableStoreService.packageOrderPushCount(miToDay,packageConfigId,masterOrderId, masterId);
////	}
//
//
//	public void packageOrderPushCount(String miToDay,String packageConfigId,String masterOrderId, String masterId) {
//		recordTableStoreService.packageOrderPushCount(miToDay,packageConfigId,masterOrderId, masterId);
//	}
//}