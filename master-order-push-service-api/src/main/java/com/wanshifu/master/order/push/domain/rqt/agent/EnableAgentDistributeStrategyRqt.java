package com.wanshifu.master.order.push.domain.rqt.agent;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:55
 */
@Data
public class EnableAgentDistributeStrategyRqt {

    @NotNull
    private Integer strategyId;

    @NotNull
    @ValueIn("1,0")
    private Integer strategyStatus;

    private Long updateAccountId;

}
