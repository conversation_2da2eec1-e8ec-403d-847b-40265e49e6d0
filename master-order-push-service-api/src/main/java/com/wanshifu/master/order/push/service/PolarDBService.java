package com.wanshifu.master.order.push.service;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import com.wanshifu.master.order.push.domain.constant.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static io.protostuff.CollectionSchema.MessageFactories.ArrayList;

/**
 * 离线库Service
 * <AUTHOR>
 */
@Service
@Slf4j
public class PolarDBService {

	public static final String DRIVER = "com.mysql.jdbc.Driver";


	private DruidDataSource userOrderServiceDataSource;

	@Value("${poraldb.username}")
	private String username;

	@Value("${poraldb.password}")
	private String password;

	@Value("${poraldb.userOrderService.url}")
	private String userOrderServiceUrl;

	@Value("${poraldb.masterOrderService.url}")
	private String masterOrderServiceUrl;

	@Value("${poraldb.masterInformationService.url}")
	private String masterInformationServiceUrl;

	@Value("${poraldb.orderConfigService.url}")
	private String orderConfigServiceUrl;

	private Connection userOrderServiceConnection() throws Exception {
		if (userOrderServiceDataSource != null) {
			return userOrderServiceDataSource.getConnection();
		}
		synchronized (PolarDBService.class) {
			if (userOrderServiceDataSource != null) {
				return userOrderServiceDataSource.getConnection();
			}
			userOrderServiceDataSource = new DruidDataSource();
			userOrderServiceDataSource.setDriverClassName(DRIVER);
			userOrderServiceDataSource.setUsername(username);
			userOrderServiceDataSource.setPassword(password);
			userOrderServiceDataSource.setUrl(userOrderServiceUrl);
			userOrderServiceDataSource.setInitialSize(5);
			userOrderServiceDataSource.setMinIdle(1);
			userOrderServiceDataSource.setMaxActive(500);
			userOrderServiceDataSource.setMaxWait(6000);
			userOrderServiceDataSource.setPoolPreparedStatements(false);
			return userOrderServiceDataSource.getConnection();
		}
	}

	private DruidDataSource masterOrderServiceDataSource;

	private Connection masterOrderServiceConnection() throws Exception {
		if (masterOrderServiceDataSource != null) {
			return masterOrderServiceDataSource.getConnection();
		}
		synchronized (PolarDBService.class) {
			if (masterOrderServiceDataSource != null) {
				return masterOrderServiceDataSource.getConnection();
			}
			masterOrderServiceDataSource = new DruidDataSource();
			masterOrderServiceDataSource.setDriverClassName(DRIVER);
			masterOrderServiceDataSource.setUsername(username);
			masterOrderServiceDataSource.setPassword(password);
			masterOrderServiceDataSource.setUrl(masterOrderServiceUrl);
			masterOrderServiceDataSource.setInitialSize(5);
			masterOrderServiceDataSource.setMinIdle(1);
			masterOrderServiceDataSource.setMaxActive(500);
			masterOrderServiceDataSource.setMaxWait(6000);
			masterOrderServiceDataSource.setPoolPreparedStatements(false);
			return masterOrderServiceDataSource.getConnection();
		}
	}


	private DruidDataSource masterInformationServiceDataSource;

	private Connection masterInformationServiceConnect() throws Exception {
		if (masterInformationServiceDataSource != null) {
			return masterInformationServiceDataSource.getConnection();
		}
		synchronized (PolarDBService.class) {
			if (masterInformationServiceDataSource != null) {
				return masterInformationServiceDataSource.getConnection();
			}
			masterInformationServiceDataSource = new DruidDataSource();
			masterInformationServiceDataSource.setDriverClassName(DRIVER);
			masterInformationServiceDataSource.setUsername(username);
			masterInformationServiceDataSource.setPassword(password);
			masterInformationServiceDataSource.setUrl(masterInformationServiceUrl);
			// 初始化连接
			masterInformationServiceDataSource.setInitialSize(5);
			// 最小空闲连接
			masterInformationServiceDataSource.setMinIdle(1);
			// 最大连接数量
			masterInformationServiceDataSource.setMaxActive(500);
			// 连接等待超时的时间
			masterInformationServiceDataSource.setMaxWait(6000);
			masterInformationServiceDataSource.setPoolPreparedStatements(false);
			return masterInformationServiceDataSource.getConnection();
		}
	}


	private DruidDataSource orderConfigServiceDataSource;

	private Connection orderConfigServiceConnection() throws Exception {
		if (orderConfigServiceDataSource != null) {
			return orderConfigServiceDataSource.getConnection();
		}
		synchronized (PolarDBService.class) {
			if (orderConfigServiceDataSource != null) {
				return orderConfigServiceDataSource.getConnection();
			}
			orderConfigServiceDataSource = new DruidDataSource();
			orderConfigServiceDataSource.setDriverClassName(DRIVER);
			orderConfigServiceDataSource.setUsername(username);
			orderConfigServiceDataSource.setPassword(password);
			orderConfigServiceDataSource.setUrl(orderConfigServiceUrl);
			orderConfigServiceDataSource.setInitialSize(5);
			orderConfigServiceDataSource.setMinIdle(1);
			orderConfigServiceDataSource.setMaxActive(500);
			orderConfigServiceDataSource.setMaxWait(6000);
			orderConfigServiceDataSource.setPoolPreparedStatements(false);
			return orderConfigServiceDataSource.getConnection();
		}
	}





	private Map<String, List<String>> lowerTecMap;
	private HashMap<String, List<String>> getLowerTec() {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		HashMap<String, List<String>> localLowerTecMap = new HashMap<>();
		try {
			connection = orderConfigServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT parent_id,technique_id FROM `technique` where is_delete=0;");
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				String techniqueId=resultSet.getString("technique_id");
				String parentId=resultSet.getString("parent_id");
				List<String> tecRow=localLowerTecMap.get(parentId);
				if (tecRow==null) {
					tecRow=new ArrayList<>();
					tecRow.add(techniqueId);
					localLowerTecMap.put(parentId, tecRow);
				}else {
					tecRow.add(techniqueId);
				}
			}
		} catch (Exception e) {
//			logger.warn("getLowerTec 查询失败");
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return localLowerTecMap;
	}


	private Map<String, String> uperServeMap;
	private HashMap<String, String> getUperServe() {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		HashMap<String, String> localUperServeMap = new HashMap<>();
		try {
			connection = orderConfigServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT serve_id,parent_id FROM `serve` where is_delete=0 and `level`!=1;");
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				String serveId=resultSet.getString("serve_id");
				String parentId=resultSet.getString("parent_id");
				localUperServeMap.put(serveId, parentId);
			}
		} catch (Exception e) {
//			logger.warn("getUperServe 查询失败");
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return localUperServeMap;
	}

	public Set<String> getLvTwoTecByTop(String serveId){
		if (serveId==null) {
			return new HashSet<>();
		}
		if (!lvTwoTecByTopMap.containsKey(serveId)) {
			synchronized (this) {
				if (lvTwoTecByTopMap.containsKey(serveId)) {
					return lvTwoTecByTopMap.get(serveId);
				}
				Set<String> lvTowSet=getLvTwoTecByTopDB(serveId);
				lvTwoTecByTopMap.put(serveId, lvTowSet);
				return lvTowSet;
			}
		}
		return lvTwoTecByTopMap.get(serveId);
	}
	private Map<String, Set<String>> lvTwoTecByTopMap=new HashMap<>();
	private Set<String> getLvTwoTecByTopDB(String serveId) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		Set<String> lvTowSet=new HashSet<>();
		try {
			connection = orderConfigServiceConnection();
			preparedStatement = connection
					.prepareStatement("select technique_id from technique WHERE parent_id in (SELECT technique_ids FROM `serve_technique_rela` WHERE serve_ids = ?) and `level` = 2 and is_delete = 0;");
			preparedStatement.setString(1, serveId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				String techniqueId=resultSet.getString("technique_id");
				lvTowSet.add(techniqueId);
			}
		} catch (Exception e) {
//			logger.warn("getLvTwoTecByTop 查询失败");
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return lvTowSet;
	}

	public Set<String> getLvTwoTecByLvOneTec(String tecId){
		if (tecId==null) {
			return new HashSet<>();
		}
		if (!lvTwoTecByLvOneTecMap.containsKey(tecId)) {
			synchronized (this) {
				if (lvTwoTecByLvOneTecMap.containsKey(tecId)) {
					return lvTwoTecByLvOneTecMap.get(tecId);
				}
				Set<String> lvTowSet=getLvTwoTecByLvOneTecDB(tecId);
				lvTwoTecByLvOneTecMap.put(tecId, lvTowSet);
				return lvTowSet;
			}
		}
		return lvTwoTecByLvOneTecMap.get(tecId);
	}
	private Map<String, Set<String>> lvTwoTecByLvOneTecMap=new HashMap<>();
	private Set<String> getLvTwoTecByLvOneTecDB(String tecId) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		Set<String> lvTowSet=new HashSet<>();
		try {
			connection = orderConfigServiceConnection();
			preparedStatement = connection
					.prepareStatement("select technique_id from technique WHERE parent_id =? and `level` = 2 and is_delete = 0;");
			preparedStatement.setString(1, tecId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				String techniqueId=resultSet.getString("technique_id");
				lvTowSet.add(techniqueId);
			}
		} catch (Exception e) {
//			logger.warn("getLvTwoTecByLvOneTecDB 查询失败");
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return lvTowSet;
	}

//	public boolean lvOneServeMappingContains(String serveId) {
//		if (lvOneServeMappingMap == null) {
//			synchronized (this) {
//				if (lvOneServeMappingMap != null) {
//					return lvOneServeMappingMap.containsKey(serveId);
//				}
//				lvOneServeMappingMap = getserveMappingDB();
//			}
//		}
//		return lvOneServeMappingMap.containsKey(serveId);
//	}

//	public Set<String> getLvOneServeMapping(String serveId) {
//		if (serveId == null) {
//			return new HashSet<>();
//		}
//		if (lvOneServeMappingMap == null) {
//			synchronized (this) {
//				if (lvOneServeMappingMap != null) {
//					if (lvOneServeMappingMap.containsKey(serveId)) {
//						return lvOneServeMappingMap.get(serveId);
//					}
//				}
//				lvOneServeMappingMap = getserveMappingDB();
//			}
//		}
//		if (lvOneServeMappingMap.containsKey(serveId)) {
//			return lvOneServeMappingMap.get(serveId);
//		}
//		return new HashSet<>();
//	}


//	public boolean lvTwoServeMappingContains(String serveIds) {
//		if (lvTwoServeMappingMap == null) {
//			synchronized (this) {
//				if (lvTwoServeMappingMap != null) {
//						return lvTwoServeMappingMap.containsKey(serveIds);
//				}
//				lvTwoServeMappingMap = getserveMappingDB();
//			}
//		}
//		return lvTwoServeMappingMap.containsKey(serveIds);
//	}

//	public Set<String> getLvTwoServeMapping(String serveId) {
//		if (serveId == null) {
//			return new HashSet<>();
//		}
//		if (lvTwoServeMappingMap == null) {
//			synchronized (this) {
//				if (lvTwoServeMappingMap != null) {
//					if (lvTwoServeMappingMap.containsKey(serveId)) {
//						return lvTwoServeMappingMap.get(serveId);
//					}
//				}
//				lvTwoServeMappingMap = getserveMappingDB();
//			}
//		}
//		if (lvTwoServeMappingMap.containsKey(serveId)) {
//			return lvTwoServeMappingMap.get(serveId);
//		}
//		return new HashSet<>();
//	}
	private Map<String, Set<String>> lvOneServeMappingMap;
	private Map<String, Set<String>> lvTwoServeMappingMap;
//	private HashMap<String, Set<String>> getserveMappingDB() {
//		Connection connection = null;
//		PreparedStatement preparedStatement = null;
//		ResultSet resultSet = null;
//		HashMap<String, Set<String>> lvOneServeMapping = new HashMap<>();
////		HashMap<String, Set<String>> lvTwoServeMapping = new HashMap<>();
//		try {
//			connection = MysqlConnectionPool.getConnection();
//			preparedStatement = connection
//					.prepareStatement("SELECT serve_id,serve_level,mapping_level,mapping_technique_ids FROM `serve_mapping` where is_delete=0;");
//			resultSet = preparedStatement.executeQuery();
//			while (resultSet.next()) {
//				Integer mappingLevel = resultSet.getInt("mapping_level");
//				Integer serveLevel = resultSet.getInt("serve_level");
//				String serveId = resultSet.getString("serve_id");
//				String mappingTechniqueIds = resultSet.getString("mapping_technique_ids");
////				if (1==serveLevel) {
//					if (1 == mappingLevel) {
//						for (String element : mappingTechniqueIds.split(",")) {
//							putSetMap(lvOneServeMapping, serveId, getLvTwoTecByLvOneTec(element));
//						}
//					} else if (2 == mappingLevel) {
//						for (String element : mappingTechniqueIds.split(",")) {
//							putSetMap(lvOneServeMapping, serveId, element);
//						}
//					}
////				}else if (2==serveLevel) {
////					if (1 == mappingLevel) {
////						for (String element : mappingTechniqueIds.split(",")) {
////							putSetMap(lvTwoServeMapping, serveId, getLvTwoTecByLvOneTec(element));
////						}
////					} else if (2 == mappingLevel) {
////						for (String element : mappingTechniqueIds.split(",")) {
////							putSetMap(lvTwoServeMapping, serveId, element);
////						}
////					}
////				}
//			}
//			lvOneServeMappingMap=lvOneServeMapping;
////			lvTwoServeMappingMap=lvTwoServeMapping;
//		} catch (Exception e) {
////			logger.warn("getserveMappingDB 查询失败,{}",ExceptionInfo.getExceptionAllinformation(e));
//		} finally {
//			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
//		}
//		return lvOneServeMapping;
//	}

	private void putSetMap(HashMap<String, Set<String>> setMap,String key,String value) {
		if (setMap==null) {
			return;
		}
		if (setMap.containsKey(key)) {
			setMap.get(key).add(value);
		}else {
			HashSet<String> currentSet=new HashSet<>();
			currentSet.add(value);
			setMap.put(key, currentSet);
		}
	}

	private void putSetMap(HashMap<String, Set<String>> setMap,String key,Set<String> value) {
		if (setMap==null) {
			return;
		}
		if (setMap.containsKey(key)) {
			setMap.get(key).addAll(value);
		}else {
			HashSet<String> currentSet=new HashSet<>();
			currentSet.addAll(value);
			setMap.put(key, currentSet);
		}
	}

//	public String[] getServeMappingMap(String[] serveIds){
//		List<String> result=new ArrayList<>();
//		if (serveIds==null||serveIds.length==0) {
//			return new String[0];
//		}
//		if (serveMappingMap==null) {
//			synchronized (this) {
//				if (serveMappingMap!=null) {
//					for (String id : serveIds) {
//						if (serveMappingMap.containsKey(id)) {
//							result.add(serveMappingMap.get(id));
//						}
//					}
//					return TecGroupUtil.stringListToArray(result);
//				}
//				serveMappingMap=getUperServe();
//			}
//		}
//		for (String id : serveIds) {
//			if (serveMappingMap.containsKey(id)) {
//				result.add(serveMappingMap.get(id));
//			}
//		}
//		return TecGroupUtil.stringListToArray(result);
//	}
//	private Map<String, String> serveMappingMap;
//	private HashMap<String, String> getServeMapping() {
//		Connection connection = null;
//		PreparedStatement preparedStatement = null;
//		ResultSet resultSet = null;
//		HashMap<String, String> localUperServeMap = new HashMap<>();
//		try {
//			connection = orderConfigServiceConnection();
//			preparedStatement = connection
//					.prepareStatement("SELECT serve_id,parent_id FROM `serve` where is_delete=0 and `level`!=1;");
//			resultSet = preparedStatement.executeQuery();
//			while (resultSet.next()) {
//				String serveId=resultSet.getString("serve_id");
//				String parentId=resultSet.getString("parent_id");
//				localUperServeMap.put(serveId, parentId);
//			}
//		} catch (Exception e) {
//			logger.warn("getUperServe 查询失败");
//		} finally {
//			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
//		}
//		return localUperServeMap;
//	}

	/**
	 * 获取商品信息
	 *
	 * @param masterOrderId
	 * @return
	 */
	public boolean setOrderGoodsInfo(Long masterOrderId,Integer bussinessId ,OrderDetailData orderDetailData) {
		boolean reulst=false;
		if (bussinessId==3) {
			//创新业务
			reulst=setOrderGoodsBussinessId3(masterOrderId, orderDetailData);
		}else {
			//成品,家庭
			reulst=setOrderGoodsBussinessId12(masterOrderId, orderDetailData);
		}
		return reulst;
	}
	private boolean setOrderGoodsBussinessId12(Long masterOrderId, OrderDetailData orderBase) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		Set<String> goodsCategorys = new HashSet<>();
		Set<String> goodsChilds = new HashSet<>();
		boolean reulst=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT goods_category,category_child,number FROM `order_goods` where order_id=?");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			int goodsNum=0;
			while (resultSet.next()) {
				goodsCategorys.add(resultSet.getString("goods_category"));
				goodsChilds.add(resultSet.getString("category_child"));
				try {
					int currentGoodsNum=Integer.valueOf(resultSet.getString("number"));
					goodsNum=goodsNum+currentGoodsNum;
				} catch (Exception e) {
				}
			}
			if (goodsCategorys.size()!=0) {
				orderBase.setParentGoods(goodsCategorys);
			}
			if (goodsChilds.size()!=0) {
				orderBase.setChildGoods(goodsChilds);
			}
			orderBase.setGoodsNum(goodsNum);
			reulst=true;
		} catch (Exception e) {
//			logger.warn("master_order_id:{} BussinessId12-商品 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return reulst;
	}
	private boolean setOrderGoodsBussinessId3(Long masterOrderId, OrderDetailData orderBase) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		Set<String> goodsCategorys = new HashSet<>();
//		Set<String> goodsChilds = new HashSet<>();
		boolean reulst=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT goods_category,number FROM `info_order_goods` where order_id=?");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			int goodsNum=0;
			while (resultSet.next()) {
				goodsCategorys.add(resultSet.getString("goods_category"));
//				goodsChilds.add(resultSet.getString("category_child"));--创新业务无此字段
				try {
					int currentGoodsNum=resultSet.getInt("number");
					goodsNum=goodsNum+currentGoodsNum;
				} catch (Exception e) {
				}
			}
			if (goodsCategorys.size()!=0) {
				orderBase.setParentGoods(goodsCategorys);
			}
//			if (goodsChilds.size()!=0) {
//				orderBase.setChildGoods(goodsChilds);
//			}
			orderBase.setGoodsNum(goodsNum);
			reulst=true;
		} catch (Exception e) {
//			logger.warn("master_order_id:{} BussinessId3-商品 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return reulst;
	}
	@Deprecated
	public Map<String, Set<String>> getOrderGoodsByMasterOrderId(String masterOrderId) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		Map<String, Set<String>> goodsInfo = new HashMap<>();
		Set<String> goodsCategorys = new HashSet<>();
		Set<String> goodsChilds = new HashSet<>();
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT goods_category,category_child FROM `order_goods` where order_id=?");
			preparedStatement.setString(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				goodsCategorys.add(resultSet.getString("goods_category"));
				goodsChilds.add(resultSet.getString("category_child"));
			}
		} catch (Exception e) {
//			logger.warn("master_order_id 商品 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		goodsInfo.put("goodsCategorys", goodsCategorys);
		goodsInfo.put("goodsChilds", goodsChilds);
		return goodsInfo;
	}

	/**
	 * 获取城市,指派 信息
	 *
	 * @param masterOrderId
	 * @return
	 */
	public String getOrderCityByMasterOrderId(String masterOrderId) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		String cityId = null;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT grab_id,city_id FROM `order_grab` where order_id=? and is_delete = 0");
			preparedStatement.setString(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			int grabId = -1;
			while (resultSet.next()) {
				Integer currentGrabId = resultSet.getInt("grab_id");
				if (currentGrabId > grabId) {
					cityId = resultSet.getString("city_id");
					grabId = currentGrabId;
				}
				resultSet.getInt("city_id");
			}
		} catch (Exception e) {
//			logger.warn("master_order_id 城市查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return cityId;
	}

	public boolean setOrderAppointType(Long masterOrderId,Integer businessLineId ,OrderDetailData orderBase) {
		boolean reulst=false;
		reulst=setOrderAppointTypeByDB(masterOrderId, orderBase);
		return reulst;
	}

	/**
	 * 获取期望完成时间
	 *
	 * @param masterOrderId
	 * @return
	 */
	public boolean setOrderExpectCompleteTime(Long masterOrderId,Integer businessLineId ,OrderDetailData orderDetailData) {
		boolean reulst=false;
		if (businessLineId==3) {
			//创新业务
//			reulst=setOrderGoodsBussinessId3(masterOrderId, orderBase);
		}else {
			//成品,家庭
			reulst=setOrderExpectCompleteTimeBussinessId12(masterOrderId, orderDetailData);
		}
		return reulst;
	}
	private boolean setOrderExpectCompleteTimeBussinessId12(Long masterOrderId, OrderDetailData orderDetailData) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		boolean result=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT expect_complete_time,expect_door_in_start_date,expect_door_in_end_date,timer_flag,emergency_order_flag,on_time_order_flag,buyer_phone FROM `order_extra_data` where order_id=?");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				Date date = resultSet.getDate("expect_complete_time");
				Date expectDoorInStartDate=resultSet.getDate("expect_door_in_start_date");
				Date expectDoorInEndDate=resultSet.getDate("expect_door_in_end_date");
				final int timerFlag = resultSet.getInt(FieldConstant.TIMER_FLAG);
				if (date != null) {
					String expectCompleteTime = DateFormatterUtil.sqlDateToString(date);
					orderDetailData.setExpectCompleteTime(expectCompleteTime);
				}
				if (expectDoorInStartDate != null) {
					String expectDoorInStartDateString = DateFormatterUtil.sqlDateToString(expectDoorInStartDate);
					orderDetailData.setExpectDoorInStartDate(expectDoorInStartDateString);
					orderDetailData.setExpectDoorInStartTimeString(DateFormatterUtil.sqlDateToFullString(expectDoorInStartDate));
				}
				if (expectDoorInEndDate != null) {
					orderDetailData.setExpectDoorInEndTimeString(DateFormatterUtil.sqlDateToFullString(expectDoorInEndDate));
				}
				orderDetailData.setTimerFlag(timerFlag);
				orderDetailData.setEmergencyOrderFlag(resultSet.getInt("emergency_order_flag"));
				orderDetailData.setOnTimeOrderFlag(resultSet.getInt("on_time_order_flag"));
				orderDetailData.setCustomerPhone(resultSet.getString("buyer_phone"));

				result=true;
			}
		} catch (Exception e) {
//			logger.warn("master_order_id:{} BussinessId12-订单期望完成时间 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return result;
	}

	private boolean setOrderAppointTypeByDB(Long masterOrderId, OrderDetailData orderDetailData) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		boolean result=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT appoint_type,second_division_id FROM `order_grab` where order_id=? and is_delete=0;");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				int appointTypeInt = resultSet.getInt("appoint_type");
				long secondDivisionId = resultSet.getLong("second_division_id");

				orderDetailData.setAppointType(appointTypeInt);
				orderDetailData.setSecondDivisionId(secondDivisionId);
					result=true;
			}
		} catch (Exception e) {
//			logger.warn("master_order_id:{} setOrderAppointTypeByDB-订单指派类型 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return result;
	}

	@Deprecated
	public String getOrderExpectCompleteTime(String masterOrderId) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		String expectCompleteTime = null;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement("SELECT expect_complete_time FROM `order_extra_data` where order_id=?");
			preparedStatement.setString(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				Date date = resultSet.getDate("expect_complete_time");
				if (date == null) {
					return null;
				} else {
					expectCompleteTime = DateFormatterUtil.sqlDateToString(resultSet.getDate("expect_complete_time"));
				}
			}
		} catch (Exception e) {
//			logger.warn("master_order_id 订单期望完成时间 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return expectCompleteTime;
	}


	/**
	 * 获取订单基本信息
	 *
	 * @param masterOrderId
	 * @return
	 */
	public boolean setOrderBaseInfo(Long masterOrderId,Integer businessLineId ,OrderDetailData orderDetailData) {
		boolean reulst=false;
		if (businessLineId==3) {
			//创新业务
			reulst=setMasterOrderBaseBussinessId3(masterOrderId, orderDetailData);
			setUserOrderBaseBussinessId3(orderDetailData.getGlobalOrderId(),orderDetailData);
		}else {
			//成品,家庭
			reulst=setMasterOrderBaseBussinessId12(masterOrderId, orderDetailData);
			setUserOrderBaseBussinessId12(orderDetailData.getGlobalOrderId(),orderDetailData);
		}
		return reulst;
	}
	private boolean setMasterOrderBaseBussinessId12(Long masterOrderId, OrderDetailData orderBase) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		Connection orderConfigConnection = null;
		PreparedStatement orderConfigPreparedStatement = null;
		ResultSet resultSet = null;
		ResultSet orderConfigResultSet = null;

		boolean reulst=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection.prepareStatement(
					"SELECT order_id,account_id,account_type,global_order_trace_id,category_id,serve_type,serve_ids,binding_technology_ids,third_division_id,fourth_division_id,order_from,order_label,account_type,serve_level_1_ids FROM `order_base` where order_id=?");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			out:if (resultSet.next()) {
				String bindingTechnologyIds = StringUtils.trimToNull(resultSet.getString("binding_technology_ids"));
				String thirdDivisionId = StringUtils.trimToNull(resultSet.getString("third_division_id"));
				String orderFrom=resultSet.getString("order_from");
				if (bindingTechnologyIds==null||thirdDivisionId==null) {
					break out;
				}
				orderBase.setGlobalOrderId(resultSet.getLong("global_order_trace_id"));
				orderBase.setOrderServeType(Integer.valueOf(resultSet.getString("serve_type")));

				//TODO 一级和二级服务id
				orderBase.setLv3ServeIds(resultSet.getString("serve_ids"));
				if(StringUtils.isNotBlank(orderBase.getLv3ServeIds())){
					orderConfigConnection = orderConfigServiceConnection();
					StringBuilder sqlBuilder = new StringBuilder();
					sqlBuilder.append("select GROUP_CONCAT(level_2_id) as level_2_ids from serve where serve_id in (")
							.append(orderBase.getLv3ServeIds())
							.append(") and level = 3 and is_delete = 0 GROUP BY level");
					orderConfigPreparedStatement = orderConfigConnection.prepareStatement(
							sqlBuilder.toString());
					orderConfigResultSet = orderConfigPreparedStatement.executeQuery();
					if (orderConfigResultSet.next()) {
						orderBase.setLv2ServeIds(orderConfigResultSet.getString("level_2_ids"));
						if(StringUtils.isNotBlank(orderBase.getLv2ServeIds())){
							orderBase.setLv2ServeIdList(Stream.of(orderBase.getLv2ServeIds().split(","))
									.map(Long::parseLong)
									.collect(Collectors.toList()));
						}
					}

				}
				orderBase.setOrderTechniques(bindingTechnologyIds);
				orderBase.setOrderCategoryId(Long.valueOf(resultSet.getString("category_id")));
				orderBase.setAccountType(resultSet.getString("account_type"));
				orderBase.setAccountId(resultSet.getLong("account_id"));
				orderBase.setThirdDivisionId(Long.valueOf(thirdDivisionId));
				orderBase.setFourthDivisionId(Long.valueOf(resultSet.getString("fourth_division_id")));
				orderBase.setOrderFrom(orderFrom);
				orderBase.setLv1ServeIds(resultSet.getString("serve_level_1_ids"));
				orderBase.setOrderLabel(resultSet.getString("order_label"));
				reulst=true;
			}
		} catch (Exception e) {
			log.error("setMasterOrderBaseBussinessId12 error",e);
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
			MysqlConnectionPool.closeCon(orderConfigConnection, orderConfigPreparedStatement, orderConfigResultSet);

		}
		return reulst;
	}



	private boolean setMasterOrderBaseBussinessId3(Long masterOrderId, OrderDetailData orderDetailData) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		boolean reulst=false;
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection.prepareStatement(
					"SELECT order_id,account_id,account_type,global_order_trace_id,category_id,serve_ids,binding_technology_ids,third_division_id,fourth_division_id,account_type,serve_level_1_ids FROM `info_order_base` where order_id=?");
			preparedStatement.setLong(1, masterOrderId);
			resultSet = preparedStatement.executeQuery();
			out:if (resultSet.next()) {
				String bindingTechnologyIds = StringUtils.trimToNull(resultSet.getString("binding_technology_ids"));
				String thirdDivisionId = StringUtils.trimToNull(resultSet.getString("third_division_id"));
				if (bindingTechnologyIds==null||thirdDivisionId==null) {
					break out;
				}
				orderDetailData.setGlobalOrderId(resultSet.getLong("global_order_trace_id"));
//				orderBase.setOrderServeType(resultSet.getString("serve_type"));//--创新业务无此字段
				orderDetailData.setLv3ServeIds(resultSet.getString("serve_ids"));
				orderDetailData.setLv1ServeIds(resultSet.getString("serve_level_1_ids"));
				orderDetailData.setOrderTechniques(bindingTechnologyIds);
				orderDetailData.setOrderCategoryId(Long.valueOf(resultSet.getString("category_id")));
				orderDetailData.setAccountType(resultSet.getString("account_type"));
				orderDetailData.setAccountId(resultSet.getLong("account_id"));
				orderDetailData.setThirdDivisionId(Long.valueOf(thirdDivisionId));
				orderDetailData.setFourthDivisionId(Long.valueOf(resultSet.getString("fourth_division_id")));
//				orderBase.setOrderFrom(resultSet.getString("order_from"));//--创新业务无此字段
//				orderBase.setOrderLabel(resultSet.getString("order_label"));//--创新业务无此字段
				reulst=true;
			}
		} catch (Exception e) {
//			logger.info("master_order_id:{} BussinessId12-订单基本信息查询异常:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return reulst;
	}

	private boolean setUserOrderBaseBussinessId12(Long globalOrderId, OrderDetailData orderDetailData) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		boolean reulst=false;
		try {
			connection = userOrderServiceConnection();
			preparedStatement = connection.prepareStatement(
					"SELECT user_id FROM `order_base` where global_order_trace_id=?");
			preparedStatement.setLong(1, globalOrderId);
			resultSet = preparedStatement.executeQuery();
			out:if (resultSet.next()) {
				final Long userId = resultSet.getLong("user_id");
				orderDetailData.setUserId(userId);
				reulst=true;
			}
		} catch (Exception e) {
//			logger.info("master_order_id:{} BussinessId12-订单基本信息查询异常:{}", globalOrderId, ExceptionInfo.getExceptionAllinformation(e));
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return reulst;
	}
	private boolean setUserOrderBaseBussinessId3(Long globalOrderId, OrderDetailData orderDetailData) {
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		boolean reulst=false;
		try {
			connection = userOrderServiceConnection();
			preparedStatement = connection.prepareStatement(
					"SELECT user_id FROM `info_order_base` where global_order_trace_id=?");
			preparedStatement.setLong(1, globalOrderId);
			resultSet = preparedStatement.executeQuery();
			out:if (resultSet.next()) {
				final Long userId = resultSet.getLong("user_id");
				orderDetailData.setUserId(userId);
				reulst=true;
			}
		} catch (Exception e) {
			log.error("setUserOrderBaseBussinessId3 error",e);
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return reulst;
	}


	public Map<Long,String> getOrderNo(Set<Long> orderIdList){

		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;

		String orderIds = net.logstash.logback.encoder.org.apache.commons.lang.StringUtils.join(orderIdList,",");
		String sql = "SELECT order_id,order_no FROM `order_base` where order_id in (" + orderIds + ")";

		Map<Long,String> orderNoMap = new HashMap<>();
		try {
			connection = masterOrderServiceConnection();
			preparedStatement = connection
					.prepareStatement(sql);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				Long orderId = resultSet.getLong("order_id");
				String orderNo = resultSet.getString("order_no");
				orderNoMap.put(orderId,orderNo);
			}
		} catch (Exception e) {
			log.error("getOrderNo error",e);
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return orderNoMap;
	}

	public Map<Long,Map<String,Object>> getMasterList(Set<Long> masterIdSet){

		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;

		String masterIds = net.logstash.logback.encoder.org.apache.commons.lang.StringUtils.join(masterIdSet,",");
		String sql = "SELECT master_id,team_name,phone FROM `master_info` where master_id in (" + masterIds + ")";

		Map<Long,Map<String,Object>> masterMap = new HashMap<>();
		try {
			connection = masterInformationServiceConnect();
			preparedStatement = connection.prepareStatement(sql);
			resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				Long masterId = resultSet.getLong("master_id");
				Map<String,Object> map = new HashMap<>();
				map.put("masterName",resultSet.getString("team_name"));
				map.put("masterPhone",resultSet.getString("phone"));
				masterMap.put(masterId,map);

			}
		} catch (Exception e) {
			e.printStackTrace();
//			logger.warn("master_order_id:{} BussinessId12-商品 查询失败:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return masterMap;
	}


	public List<Long> getOrderListByCategoryIdAndSecondDivisionId(Long businessLineId,String categoryIds, java.util.Date orderCreateTimeStart, java.util.Date orderCreateTimeEnd, String secondDivisionIds){

		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet resultSet = null;
		List<Long> orderIdList = new ArrayList<>();
		try {
			connection = masterOrderServiceConnection();
			StringBuilder sqlBuilder = new StringBuilder();
			sqlBuilder.append("SELECT ob.order_id FROM `order_base` ob inner join order_grab og on ob.order_id = og.order_id WHERE ob.business_line_id = ? AND ob.category_id in (")
					.append(categoryIds)
					.append(") AND ob.order_create_time between  ? AND ? AND og.second_division_id in (")
					.append(secondDivisionIds)
					.append(") and og.appoint_type in (2)");
			preparedStatement = connection.prepareStatement(sqlBuilder.toString());
			preparedStatement.setLong(1, businessLineId);
			preparedStatement.setDate(2, new java.sql.Date(orderCreateTimeStart.getTime()));
			preparedStatement.setDate(3, new java.sql.Date(orderCreateTimeEnd.getTime()));
			resultSet = preparedStatement.executeQuery();
			out:if (resultSet.next()) {
				orderIdList.add(resultSet.getLong("order_id"));
			}
		} catch (Exception e) {
//			logger.info("master_order_id:{} BussinessId12-订单基本信息查询异常:{}", masterOrderId, e.getMessage());
		} finally {
			MysqlConnectionPool.closeCon(connection, preparedStatement, resultSet);
		}
		return orderIdList;

	}





}
