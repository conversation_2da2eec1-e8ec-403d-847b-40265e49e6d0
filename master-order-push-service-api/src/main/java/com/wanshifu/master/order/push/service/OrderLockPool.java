package com.wanshifu.master.order.push.service;

import java.util.Map;
import java.util.concurrent.TimeUnit;


import org.springframework.stereotype.Component;


/**
 * 订单锁池
 * 
 * <AUTHOR>
 * @since 2019-05-31
 */
@Component
public class OrderLockPool {
//	private static final Logger LOGGER = LoggerFactory.getLogger(OrderLockPool.class);
//	private static final Logger LOGGER = LogManager.getLogger(OrderLockPool.class);
	/**
	 * 单例模式
	 */
	private static class OrderLockPoolHolder {
		private static OrderLockPool instance = new OrderLockPool();
	}

	private OrderLockPool() {
	}

	public static OrderLockPool getInstance() {
		return OrderLockPoolHolder.instance;
	}

	// 锁存在的生命周期5000Millis
//	Map<Long, Byte> lockers = new PassiveExpiringMap<Long, Byte>(5, TimeUnit.SECONDS);
//	Map<String, Byte> stringLockers = new PassiveExpiringMap<String, Byte>(5, TimeUnit.SECONDS);

	Map<Long, Byte> lockers = null;
	Map<String, Byte> stringLockers = null;

	/**
	 * 获取订单锁
	 * 
	 * @param orderId
	 * @return
	 */
	public Byte getLocker(Long orderId) {
		if (lockers.containsKey(orderId)) {
			return lockers.get(orderId);
		} else {
			Byte lock = new Byte("1");
			synchronized (this) {
				if (!lockers.containsKey(orderId)) {
					lockers.put(orderId, lock);
				}
				return lockers.get(orderId);
			}
		}
	}
	public Byte getLocker(String orderId) {
		if (stringLockers.containsKey(orderId)) {
			return stringLockers.get(orderId);
		} else {
			Byte lock = new Byte("1");
			synchronized (this) {
				if (!stringLockers.containsKey(orderId)) {
					stringLockers.put(orderId, lock);
				}
				return stringLockers.get(orderId);
			}
		}
	}


}
