package com.wanshifu.master.order.push.domain.dto;

import com.alibaba.fastjson.JSONObject;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public class PackageBase {
    private String PackageConfigId;
    private Long maxPushNumberDaily;
    private Long createTime;
    private String matchedAttributeId;

    private String matchedAttributeSizeMin;
    private String matchedAttributeSizeMax;
    private JSONObject matchedAttributeValue;


    public String getMatchedAttributeSizeMin() {
        return matchedAttributeSizeMin;
    }

    public void setMatchedAttributeSizeMin(String matchedAttributeSizeMin) {
        this.matchedAttributeSizeMin = matchedAttributeSizeMin;
    }

    public String getMatchedAttributeSizeMax() {
        return matchedAttributeSizeMax;
    }

    public void setMatchedAttributeSizeMax(String matchedAttributeSizeMax) {
        this.matchedAttributeSizeMax = matchedAttributeSizeMax;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", PackageBase.class.getSimpleName() + "[", "]")
                .add("PackageConfigId='" + PackageConfigId + "'")
                .add("maxPushNumberDaily=" + maxPushNumberDaily)
                .add("createTime=" + createTime)
                .add("matchedAttributeId='" + matchedAttributeId + "'")
                .add("matchedAttributeValue=" + matchedAttributeValue)
                .toString();
    }

    public JSONObject getMatchedAttributeValue() {
        return matchedAttributeValue;
    }

    public void setMatchedAttributeValue(JSONObject matchedAttributeValue) {
        this.matchedAttributeValue = matchedAttributeValue;
    }

    public String getMatchedAttributeId() {
        return matchedAttributeId;
    }

    public void setMatchedAttributeId(String matchedAttributeId) {
        this.matchedAttributeId = matchedAttributeId;
    }

    public String getPackageConfigId() {
        return PackageConfigId;
    }

    public void setPackageConfigId(String packageConfigId) {
        PackageConfigId = packageConfigId;
    }

    public Long getMaxPushNumberDaily() {
        return maxPushNumberDaily;
    }

    public void setMaxPushNumberDaily(Long maxPushNumberDaily) {
        this.maxPushNumberDaily = maxPushNumberDaily;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public static final class PackageBaseBuilder {
        private String PackageConfigId;
        private Long maxPushNumberDaily;
        private Long createTime;
        private String matchedAttributeId;
        private String matchedAttributeSizeMin;
        private String matchedAttributeSizeMax;
        private JSONObject matchedAttributeValue;

        private PackageBaseBuilder() {
        }

        public static PackageBaseBuilder aPackageBase() {
            return new PackageBaseBuilder();
        }

        public PackageBaseBuilder withPackageConfigId(String PackageConfigId) {
            this.PackageConfigId = PackageConfigId;
            return this;
        }

        public PackageBaseBuilder withMaxPushNumberDaily(Long maxPushNumberDaily) {
            this.maxPushNumberDaily = maxPushNumberDaily;
            return this;
        }

        public PackageBaseBuilder withCreateTime(Long createTime) {
            this.createTime = createTime;
            return this;
        }

        public PackageBaseBuilder withMatchedAttributeId(String matchedAttributeId) {
            this.matchedAttributeId = matchedAttributeId;
            return this;
        }

        public PackageBaseBuilder withMatchedAttributeSizeMin(String matchedAttributeSizeMin) {
            this.matchedAttributeSizeMin = matchedAttributeSizeMin;
            return this;
        }

        public PackageBaseBuilder withMatchedAttributeSizeMax(String matchedAttributeSizeMax) {
            this.matchedAttributeSizeMax = matchedAttributeSizeMax;
            return this;
        }

        public PackageBaseBuilder withMatchedAttributeValue(JSONObject matchedAttributeValue) {
            this.matchedAttributeValue = matchedAttributeValue;
            return this;
        }

        public PackageBase build() {
            PackageBase packageBase = new PackageBase();
            packageBase.setPackageConfigId(PackageConfigId);
            packageBase.setMaxPushNumberDaily(maxPushNumberDaily);
            packageBase.setCreateTime(createTime);
            packageBase.setMatchedAttributeId(matchedAttributeId);
            packageBase.setMatchedAttributeSizeMin(matchedAttributeSizeMin);
            packageBase.setMatchedAttributeSizeMax(matchedAttributeSizeMax);
            packageBase.setMatchedAttributeValue(matchedAttributeValue);
            return packageBase;
        }
    }
}
