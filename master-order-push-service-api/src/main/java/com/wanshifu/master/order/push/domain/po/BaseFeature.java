package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "base_feature")
public class BaseFeature {

    /**
     * 注解id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "feature_id")
    private Long featureId;

    /**
     * 特征编码
     */
    @Column(name = "feature_code")
    private String featureCode;

    /**
     * 特征类型: normal:
     */
    @Column(name = "feature_type")
    private String featureType;

    /**
     * 特征名称
     */
    @Column(name = "feature_field")
    private String featureField;

    /**
     * 特征维度
     */
    @Column(name = "feature_dimension")
    private String featureDimension;


    /**
     * 特征维度
     */
    @Column(name = "feature_for")
    private String featureFor;

    /**
     * 
     */
    @Column(name = "feature_name")
    private String featureName;

    /**
     * 默认值
     */
    @Column(name = "default_value")
    private String defaultValue;

    /**
     * 字段类型
     */
    @Column(name = "field_type")
    private String fieldType;


    /**
     * 实例名称
     */
    @Column(name = "instance_name")
    private String instanceName;

    /**
     * 数据库类型
     */
    @Column(name = "db_type")
    private String dbType;

    /**
     * tablestore表名
     */
    @Column(name = "table_name")
    private String tableName;

    /**
     * 特征计算类型,sum: 求和，min: 最小值，max: 最大值
     */
    @Column(name = "calculate_type")
    private String calculateType;

    /**
     * 指标值List元素类型
     */
    @Column(name = "list_type")
    private String listType;

    /**
     * 订单特征维度多值字段
     */
    @Column(name = "multi_value_dimension")
    private String multiValueDimension;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}