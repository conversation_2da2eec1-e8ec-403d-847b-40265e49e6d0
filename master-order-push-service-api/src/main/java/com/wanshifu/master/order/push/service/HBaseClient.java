package com.wanshifu.master.order.push.service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.repository.BaseFeatureRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * 查询数据库工具类
 * 
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class HBaseClient {


	@Value("${hbase.zookeeper.quorum}")
	private String hbaseZkQuorum;

	@Value("${hbase.client.username}")
	private String hbaseClientUsername;

	@Value("${hbase.client.password}")
	private String hbaseClientPassword;


	/**
	 * 根据特定格式的入参返回查询结果
	 *
	 * @param param 查询入参
	 * @return 查询结果
	 */
	public JSONArray query(List<Get> getList, List<String> fieldColumnList, String tableName) {
		try {
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result[] results = table.get(getList);
			return parseResult(fieldColumnList, results);
		} catch (Exception e) {
			log.error(String.format("hbase query error,tableName:%s,fieldColumnList:%s",tableName,fieldColumnList.toString()));
		}
		return null;
	}


	/**
	 * 根据特定格式的入参返回查询结果
	 *
	 * @param rowKeyList 查询入参
	 * @return 查询结果
	 */
	public JSONArray batchQuery(List<String> rowKeyList, List<String> fieldColumnList, String tableName) {
		try {

			List<Get> getList = new ArrayList<>();
			rowKeyList.forEach(rowKey -> getList.add(new Get(Bytes.toBytes(rowKey.toString()))));
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result[] results = table.get(getList);
			return parseResult(fieldColumnList, results);
		} catch (Exception e) {
			log.error(String.format("hbase query error,tableName:%s,fieldColumnList:%s",tableName,fieldColumnList.toString()));
		}
		return null;
	}


	private JSONArray parseResult(List<String> fieldColumnList, Result[] results) {
		JSONArray jsonArray = new JSONArray();
		try {
			for (Result result : results) {
				JSONObject jsonObject = new JSONObject();
				fieldColumnList.forEach(fieldColumn -> {
					try {
						jsonObject.put(fieldColumn, getFieldValue(fieldColumn, result));
					} catch (Exception e) {
						e.printStackTrace();
//						log.warn(String.format("hbase query parseResult error,fieldColumnList:%s",fieldColumnList.toString()));
					}
				});
				jsonArray.add(jsonObject);
			}
		} catch (Exception e) {
			log.error(String.format("hbase query parseResult error,fieldColumnList:%s",fieldColumnList.toString()));
		}
		log.info("resultJSONArray"+jsonArray.toString());
		return jsonArray;
	}

//	public Object getFieldValue(String fieldColumn,Result result,Map<String,BaseFeature> baseFeatureMap){
//		if("master_id".equals(fieldColumn)){
//			return Bytes.toString(result.getValue(Bytes.toBytes("0"),Bytes.toBytes(fieldColumn)));
//		}
//		String fieldType = baseFeatureMap.get(fieldColumn).getFieldType();
//		byte[] dataBytes = result.getValue(Bytes.toBytes("0"),Bytes.toBytes(fieldColumn));
//		if(dataBytes == null || dataBytes.length == 0){
//			return null;
//		}
//		if("Integer".equals(fieldType)){
//			return Bytes.toString(dataBytes);
//		}else if("Long".equals(fieldType)){
//			return Bytes.toString(dataBytes);
//		}else if("Double".equals(fieldType)){
//			return Bytes.toString(dataBytes);
//		}else if("BigDecimal".equals(fieldType)){
//			return Bytes.toString(dataBytes);
//		}else if("String".equals(fieldType)){
//			return Bytes.toString(dataBytes);
//		}
//		return null;
//	}


	public Object getFieldValue(String fieldColumn, Result result) {

		byte[] dataBytes = result.getValue(Bytes.toBytes("0"), Bytes.toBytes(fieldColumn));
		if (dataBytes == null || dataBytes.length == 0) {
			return null;
		}

		return Bytes.toString(dataBytes);
	}


	public JSONObject querySingle(Set<String> queryFieldList, Get get, String tableName) {
		try {
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result result = table.get(get);
			return parseResult(queryFieldList, result);
		} catch (Exception e) {

		}
		return null;
	}


	private JSONObject parseResult(Set<String> fieldColumnList, Result result) {
		JSONObject jsonObject = new JSONObject();
		fieldColumnList.forEach(fieldColumn -> {
			try {
				jsonObject.put(fieldColumn, getFieldValue(fieldColumn, result));
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
		return jsonObject;
	}

	   // 使用volatile修饰禁止重排序
       private volatile static Connection connection;

	@PostConstruct
	public void initHbaseConnection() {

		try {

			log.info("init hbase Connection");

			// 新建一个Configuration
			Configuration conf = HBaseConfiguration.create();

			conf.set("hbase.zookeeper.quorum", hbaseZkQuorum);
			// xml_template.comment.hbaseue.username_password.default
			conf.set("hbase.client.username", hbaseClientUsername);
			conf.set("hbase.client.password", hbaseClientPassword);


			// 如果您直接依赖了阿里云hbase客户端，则无需配置connection.impl参数，如果您依赖了alihbase-connector，则需要配置此参数
			//conf.set("hbase.client.connection.impl", AliHBaseUEClusterConnection.class.getName());
			connection = ConnectionFactory.createConnection(conf);

		} catch (Exception e) {
			log.error("连接hbase失败", e);
		}

	}

	@PreDestroy
	public void closeHaseConnection(){
		log.error("close hbase connection");  //为了观察是否关闭hbase connection打印成error
		try{
			if(connection != null && (!connection.isClosed())){
				connection.close();
			}
		}catch(Exception e){
			log.error("closeHaseConnection error",e);
		}
	}


	public Connection getHbaseConnection(){

		if(connection == null || connection.isClosed() || connection.isAborted()){
			
			synchronized (HBaseClient.class){

				if(connection == null || connection.isClosed() || connection.isAborted()){
					log.error("hbaseConnection is closed");
					initHbaseConnection();
				}
				
			}

		}
		return connection;
	}


	public String querySingle(String tableName,String rowKey,String column){
		try{
			Get get = new Get(Bytes.toBytes(rowKey));
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result result = table.get(get);
			byte[] dataBytes = result.getValue(Bytes.toBytes("0"), Bytes.toBytes(column));
			if (dataBytes == null || dataBytes.length == 0) {
				return null;
			}
			return Bytes.toString(dataBytes);
		}catch(Exception e){
			e.printStackTrace();
		}
		return null;

	}


	/**
	 * 查询多字段
	 * @param tableName
	 * @param rowKey
	 * @param columnList
	 * @return
	 */
	public Map<String,String> query(String tableName,String rowKey,List<String> columnList){
		try{
			Get get = new Get(Bytes.toBytes(rowKey));
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result result = table.get(get);
			Map<String,String> resultMap = new HashMap<>();
			columnList.forEach(column -> {
				byte[] dataBytes = result.getValue(Bytes.toBytes("0"), Bytes.toBytes(column));
				if (dataBytes != null && dataBytes.length > 0) {
					resultMap.put(column,Bytes.toString(dataBytes));
				}
			});
		}catch(Exception e){
			e.printStackTrace();
		}
		return null;

	}


	/**
	 * 批量查询多字段
	 * @param tableName
	 * @param rowKeySet
	 * @param columnSet
	 * @return
	 */
	public Map<String,Map<String,String>> batchQuery(String tableName,String key,List<String> rowKeySet,List<String> columnSet){
		Map<String,Map<String,String>> resultMap = new HashMap<>();
		try{
			List<Get> getList = new ArrayList<>();
			rowKeySet.forEach(rowKey -> getList.add(new Get(Bytes.toBytes(rowKey))));
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Result[] results = table.get(getList);
			Arrays.stream(results).forEach(result -> {
				byte[] keyDataBytes = result.getValue(Bytes.toBytes("0"), Bytes.toBytes(key));
				if (keyDataBytes != null && keyDataBytes.length > 0) {
					String keyValue = Bytes.toString(keyDataBytes);
					Map<String,String> map = new HashMap<>();
					columnSet.forEach(column -> {
						byte[] dataBytes = result.getValue(Bytes.toBytes("0"), Bytes.toBytes(column));
						if (dataBytes != null && dataBytes.length > 0) {
							map.put(column,Bytes.toString(dataBytes));
						}
					});
					resultMap.put(keyValue,map);
				}

			});




		}catch(Exception e){
			e.printStackTrace();
		}
		return resultMap;

	}


	/**
	 * 插入数据（批量）
	 * @param tableName 表名
	 * @param rowKey rowKey
	 * @param columnFamily 列族
	 * @param columns 列
	 * @param values 值
	 * @return true/false
	 */
	public boolean putData(String tableName, String rowKey, String columnFamily,
						   List<String> columns, List<String> values) {
		try {
			Table table = getHbaseConnection().getTable(TableName.valueOf(tableName));
			Put put = new Put(Bytes.toBytes(rowKey));
			for (int i=0; i<columns.size(); i++) {
				put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(columns.get(i)),
						Bytes.toBytes(values.get(i)));
			}
			table.put(put);
			table.close();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}
	}


	/**
	 * 插入数据（单条）
	 * @param tableName 表名
	 * @param rowKey rowKey
	 * @param columnFamily 列族
	 * @param column 列
	 * @param value 值
	 * @return true/false
	 */
	public boolean putData(String tableName, String rowKey, String columnFamily, String column,
						   String value) {
		return putData(tableName, rowKey, columnFamily, Arrays.asList(column),
				Arrays.asList(value));
	}


	/**
	 * 插入数据（单条）
	 * @param tableName 表名
	 * @param rowKey rowKey
	 * @param columnFamily 列族
	 * @param column 列
	 * @param value 值
	 * @return true/false
	 */
	public boolean updateData(String tableName, String rowKey, String columnFamily, String column,
						   String value) {

		String existValue = this.querySingle(tableName,rowKey,column);
		if(StringUtils.isNotBlank(existValue)){
			value = existValue + "," + value;
		}
		return putData(tableName, rowKey, columnFamily, Arrays.asList(column),
				Arrays.asList(value));
	}


	public Map<String,Map<String,Object>> queryMap(Set<String> queryFieldList, List<Get> getList, String tableName){
		try{
			org.apache.hadoop.hbase.client.Connection connection = getHbaseConnection();
			Table table = connection.getTable(TableName.valueOf(tableName));
			Result[] results = table.get(getList);
			return parseResult(queryFieldList,results);
		}catch(Exception e){
			log.error("query error",e);
		}
		return null;
	}





	public Map<String,Object> querySingleMap(Set<String> queryFieldList, Get get, String tableName){
		try{
			org.apache.hadoop.hbase.client.Connection connection = getHbaseConnection();
			Table table = connection.getTable(TableName.valueOf(tableName));
			Result result = table.get(get);
			return parseResult(queryFieldList,result);
		}catch(Exception e){
			log.error("querySingle error",e);
		}
		return null;
	}


	private Map<String,Object> parseResultMap(Set<String> fieldColumnList,Result result ){
		Map<String,Object> resultMap = new HashMap<>();
		fieldColumnList.forEach(fieldColumn -> {
			try{
				resultMap.put(fieldColumn,getFieldValue(fieldColumn,result));
			}catch(Exception e){
				e.printStackTrace();
			}
		});
		return resultMap;
	}


	private Map<String,Map<String,Object>> parseResult(Set<String> fieldColumnList,Result[] results){
		Map<String,Map<String,Object>> resultMap = new HashMap<>();

		JSONArray jsonArray = new JSONArray();
		try{
			for(Result result : results){
				Map<String,Object> map = new HashMap<>();
				JSONObject jsonObject = new JSONObject();
				fieldColumnList.forEach(fieldColumn -> {
					try{
						map.put(fieldColumn,getFieldValue(fieldColumn,result));
					}catch(Exception e){
						e.printStackTrace();
					}
				});
				resultMap.put((String)map.get("master_id"),map);
				jsonArray.add(jsonObject);
			}
		}catch(Exception e){
			log.error("parseResult",e);
		}
		resultMap.remove(null);
		return resultMap;
	}





}
