package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 师傅来源类型
 */
@AllArgsConstructor
@Getter
public enum MasterSourceType {

    TOB("tob", "B端师傅"),

    TOC("toc", "C端师傅");


    public final String code;

    public final String desc;



    private static final Map<String, MasterSourceType> valueMapping = new HashMap<>((int) (MasterSourceType.values().length / 0.75));


    static {
        for (MasterSourceType instance : MasterSourceType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static MasterSourceType asCode(String code) {
        return valueMapping.get(code);
    }

}
