package com.wanshifu.master.order.push.domain.message;


import lombok.Data;


@Data
public class MasterServeFinishMessage {


//    msg.put("globalOrderTraceId", orderBase.getGlobalOrderTraceId());
//            msg.put("masterId", masterId);
//            msg.put("orderFrom", orderBase.getOrderFrom());
//            msg.put("accountType", orderBase.getAccountType());
//            msg.put("accountId", orderBase.getAccountId());
//            msg.put("masterOrderId", orderBase.getOrderId());
//            msg.put("operateTime", DateUtils.getDate(DateUtils.YToSec));
//            msg.put("orderNo", orderBase.getOrderNo());
//            msg.put("rateAwardFlag", StringUtils.isNotEmpty(completeRqt.getAwardImageAids()));
//            msg.put("awardImageAids", StringUtil.isEmpty(completeRqt.getAwardImageAids()) ? "" : completeRqt.getAwardImageAids());
//            msg.put("isUploadParts", isUploadParts);
//            msg.put("repairType", completeRqt.getRepairType());
//            msg.put("repairReason", completeRqt.getRepairReason());
////            msg.put("snCodeList", snCodeInfoList);
//            msg.put("snCodeListV2", snCodeList);
//            msg.put("operatelaterAward", operatelaterAward);
//            msg.put("operateOrigin", operateOrigin);
//            if (emergencyFeeCalculateResult != null) {
//        msg.put("emergencyFeeStatus", emergencyFeeCalculateResult.getEmergencyFeeStatus());
//        if (EmergencyFeeStatusEnum.AWARD_FAIL.code.equals(emergencyFeeCalculateResult.getEmergencyFeeStatus())) {
//            msg.put("notAwardReason", EmergencyFeeLoseReasonEnum.getReasonCn(emergencyFeeCalculateResult.getNotAwardReason()));
//        }
//    }
//            if (CollUtil.isNotEmpty(orderServeLabelList)&&orderServeLabelList.contains(OrderServeLabelEnum.MASTER_SUPPORT.type)){
//        //扶持订单>完工
//        msg.put("isSupportOrder",true);
//    }

    private Long globalOrderTraceId;

    private Long masterId;


}
