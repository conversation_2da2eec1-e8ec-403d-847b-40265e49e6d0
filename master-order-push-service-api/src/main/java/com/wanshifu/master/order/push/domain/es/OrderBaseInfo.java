package com.wanshifu.master.order.push.domain.es;


import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.annotation.Field;
import com.wanshifu.master.order.push.domain.annotation.FieldType;
import lombok.Data;
import org.springframework.stereotype.Component;

@Data
@Component
@Document(indexAlias = "order_base", type = "order_base_info")
public class OrderBaseInfo {

    /**
     * 订单id
     */
    @Field(type = FieldType.Long)
    private Long orderId;


    /**
     * 类目id
     */
    @Field(type = FieldType.Long)
    private Long categoryId;

    /**
     * 三级地址id
     */
    @Field(type = FieldType.Long)
    private Long thirdDivisionId;

    /**
     * 四级地址id
     */
    @Field(type = FieldType.Long)
    private Long fourthDivisionId;

    /**
     * 订单绑定技能
     */
    @Field(type = FieldType.Text)
    private String orderTechniqueIds;

    /**
     * 订单最下级服务id
     * 逗号分词
     */
    @Field(type = FieldType.Text)
    private String serveIdsAnalyzed;


    /**
     * 业务线id
     */
    @Field(type = FieldType.Long)
    private Long businessLineId;


    /**
     * 订单状态
     */
    @Field(type = FieldType.Text)
    private String orderStatus;

}
