package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.resp.agent.TobGroupAgentInfoResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/2/26 20:22
 */
@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "agentDistributeStrategy", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface AgentDistributeStrategyApi {

    @PostMapping("add")
    Integer add(@Valid @RequestBody AddAgentDistributeStrategyRqt rqt);

    @PostMapping("update")
    Integer update(@Valid @RequestBody UpdateAgentDistributeStrategyRqt rqt);

    @PostMapping("enable")
    Integer enable(@Valid @RequestBody EnableAgentDistributeStrategyRqt rqt);

    @PostMapping("delete")
    Integer delete(@Valid @RequestBody DeleteAgentDistributeStrategyRqt rqt);

    @PostMapping("detail")
    AgentDistributeDetailResp detail(@Valid @RequestBody AgentDistributeStrategyDetailRqt rqt);

    @PostMapping("getAgentList")
    SimplePageInfo<AgentInfo> getAgentList(@Valid @RequestBody GetAgentListRqt rqt);


    @PostMapping(value = "getTobGroupAgentList")
    SimplePageInfo<TobGroupAgentInfoResp> getTobGroupAgentList(@Valid @RequestBody TobGroupAgentInfoRqt rqt);

    @PostMapping("list")
    SimplePageInfo<AgentDistributeStrategy> list(@Valid @RequestBody GetAgentDistributeStrategyListRqt rqt);

    @PostMapping("getAgentById")
    AgentInfo getAgentById(@Valid @RequestBody Long agentId);

    @PostMapping("getTobGroupAgentById")
    TobGroupAgentInfoResp getTobGroupAgentById(@Valid @RequestBody Long masterId);
}
