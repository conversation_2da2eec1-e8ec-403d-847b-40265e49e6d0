package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;

/**
 * 固定的召回规则
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum BaseFilterStrategyRule {

    /**
     * 团队师傅过滤
     */
    TEAM_MASTER_ORDER_FILTER("工程单/大数量商品订单过滤", "team_master_order_push == 1", "team_master_order_push", "member_number < 1 or is_delete_team_owner == 1", "member_number,is_delete_team_owner");

    /**
     * 过滤规则名称
     */
    private String ruleName;

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启表达式参数
     */
    private String openConditionRuleParams;
    /**
     * 过滤规则表达式
     */
    private String filterRuleExpression;

    /**
     * 过滤规则表达式参数
     */
    private String filterRuleParams;


    public String getRuleName() {
        return ruleName;
    }

    public String getOpenConditionRuleExpression() {
        return openConditionRuleExpression;
    }

    public String getOpenConditionRuleParams() {
        return openConditionRuleParams;
    }

    public String getFilterRuleExpression() {
        return filterRuleExpression;
    }

    public String getFilterRuleParams() {
        return filterRuleParams;
    }
}
