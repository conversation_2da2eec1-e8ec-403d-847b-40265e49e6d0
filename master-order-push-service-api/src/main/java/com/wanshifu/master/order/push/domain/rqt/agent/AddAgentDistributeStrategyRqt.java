package com.wanshifu.master.order.push.domain.rqt.agent;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 19:54
 */
@Data
public class AddAgentDistributeStrategyRqt {

    @NotBlank
    private String strategyName;

    private String strategyDesc;

    /**
     * 合作商类型，toc:C端合作商，
     * tob:B端合作商，
     * tobGroup:B端团队师傅
     *
     */
    @NotBlank
    private String masterSourceType;

    @NotNull
    private Long agentId;

    @NotEmpty
    private String agentName;

    @NotBlank
    private String serveIds;

    /**
     * 服务id，用于前端回显
     */
    private String serveIdArray;

    @NotNull
    private Long cityDivisionId;

    @Deprecated
    private String thirdDivisionIds;

    @NotBlank
    private String fourthDivisionIds;

    @NotBlank
    @ValueIn("direct_push,direct_appoint")
    private String distributeRule;

    private String distributePriority;

    private PushStrategy pushStrategy;

    @Deprecated
    private Long scoringStrategyId;

    private List<NonEffectiveTime> nonEffectiveTimeList;

    private String serveNames;

    private Long createAccountId;

    @Data
    public static class NonEffectiveTime{

        private String nonEffectiveStartTime;

        private String nonEffectiveEndTime;

    }

    /**
     * 调度规则为direct_push定向推送时，该字段必填
     * 定向推送策略
     */
    @Data
    public static class PushStrategy {

        /**
         * 无人接单重推时间配置
         */
        private Integer agentPushNoHiredRePushTime;

        /**
         * 首次查看后无人接单重推时间配置
         */
        private Integer agentFirstViewNoHiredRePushTime;
    }
}
