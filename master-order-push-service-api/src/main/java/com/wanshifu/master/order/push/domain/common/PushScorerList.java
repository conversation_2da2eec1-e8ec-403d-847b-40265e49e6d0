package com.wanshifu.master.order.push.domain.common;


import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 推单评分器列表
 * <AUTHOR>
 */
@Data
public class PushScorerList {

    private Long snapshotId;

    private String strategyName;

    List<PushScorerObject> pushScorerObjectList;

    public PushScorerList(){

        pushScorerObjectList = new ArrayList<>();

    }

    public void addPushScorerObject(PushScorerObject pushScorerObject){
        pushScorerObjectList.add(pushScorerObject);
    }

    public Set<String> getOrderFeatureSet(){
        Set<String> orderFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(pushScorerObjectList)){
            pushScorerObjectList.forEach(pushScorerObject -> orderFeatureSet.addAll(pushScorerObject.getOrderFeatureSet()));
        }
        return orderFeatureSet;
    }

    public Set<String> getMasterFeatureSet(){
        Set<String> masterFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(pushScorerObjectList)){
            pushScorerObjectList.forEach(pushScorerObject -> masterFeatureSet.addAll(pushScorerObject.getMasterFeatureSet()));
        }
        return masterFeatureSet;
    }

}
