package com.wanshifu.master.order.push.domain.common;

import com.wanshifu.framework.utils.CollectionUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 过滤器列表
 * <AUTHOR>
 */
@Data
public class PushFilterList {

    private Long snapshotId;

    private String strategyName;

    private List<PushFilter> filterList;



    public PushFilterList(){
        filterList = new ArrayList<>();
    }

    public void addFilter(PushFilter pushFilter){
        this.filterList.add(pushFilter);
    }


    public Set<String> getOrderFeatureSet(){
        Set<String> orderFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(filterList)){
            filterList.forEach(pushFilter -> {
                if(CollectionUtils.isNotEmpty(pushFilter.getOrderFeatureList())){
                    orderFeatureSet.addAll(pushFilter.getOrderFeatureList());
                }
            });
        }
        return orderFeatureSet;
    }

    public Set<String> getMasterFeatureSet(){
        Set<String> masterFeatureSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(filterList)){
            filterList.forEach(pushFilter -> {
                if(CollectionUtils.isNotEmpty(pushFilter.getMasterFeatureList())){
                    masterFeatureSet.addAll(pushFilter.getMasterFeatureList());
                }
            });
        }
        return masterFeatureSet;
    }

}
