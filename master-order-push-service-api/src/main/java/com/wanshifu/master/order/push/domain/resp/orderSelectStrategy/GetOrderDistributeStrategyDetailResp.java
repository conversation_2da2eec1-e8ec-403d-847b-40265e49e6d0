package com.wanshifu.master.order.push.domain.resp.orderSelectStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

@Data
public class GetOrderDistributeStrategyDetailResp {

    private Integer strategyId;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    private Long businessLineId;

    private String strategyName;

    private String orderFrom;

    private String strategyDesc;

    private String categoryIds;

    private String openCityMode;

    private String distributeType;

    private String cityIds;

    @NotEmpty
    private List<DistributeStrategyVo> distributeStrategyList;

    private List<CompensateDistributeVo> compensateDistributeList;


    @Data
    public static class DistributeStrategyVo{

        private OpenCondition openCondition;

        private Integer orderSelectStrategyId;

        private String orderSelectStrategyName;

        private Integer orderScoringStrategyId;

        private String orderScoringStrategyName;

        private String distributeRule;

        private String dispatchType;


        private Integer directAppointSelectStrategyId;

        private Integer directAppointScoringStrategyId;

        private String directAppointSelectStrategyName;


        private String directAppointScoringStrategyName;

        /**
         * 合作经营调度是否服务区域兜底
         * 1：是
         * 0：否
         */
        private Integer cooperationBusinessServeDivisionAtLast;

    }


    @Data
    public static class CompensateDistributeVo{

        private Integer compensateDistributeId;

        private String compensateDistributeName;

        private Integer orderRoutingStrategyId;

        private String orderRoutingStrategyName;

        private DistributeStrategyListVo distributeStrategyList;


    }

    @Data
    public static class OpenCondition{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,time_liness_tag,appoint_user")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

        /**
         * ["1:家具安装",“2:家具送货到楼下”]
         */
        private List<String> serveInfoList;

    }


    @Data
    public static class DistributeStrategyListVo{

        private Integer orderSelectStrategyId;

        private String orderSelectStrategyName;

        private Integer orderScoringStrategyId;

        private String orderScoringStrategyName;

        private String distributeRule;

    }
}
