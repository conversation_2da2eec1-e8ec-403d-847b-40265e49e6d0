package com.wanshifu.master.order.push.domain.enums;

/**
 * 推送状态
 */
public enum PushStatus {

    /**
     * 首轮推送
     */
    PUSHING("PUSHING"),

    /**
     * 非首轮推送
     */
    STOP("STOP");

    public String code;

    PushStatus(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }


    @Override
    public String toString(){
        return code;
    }
}
