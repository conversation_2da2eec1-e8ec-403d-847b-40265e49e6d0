package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 11:34
 */
@Data
public class RuleItem {


    /**
     * 规则名称
     */
    @NotEmpty
    private String ruleName;

    /**
     * 开启条件
     */
    @NotNull
    @Valid
    private  OpenCondition openCondition;

    /**
     * 召回规则
     */
    @NotNull
    @Valid
    private FilterRule filterRule;

    @Data
    public static class OpenCondition{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,
         *  value_added_service:增值服务
         *  appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,time_liness_tag,value_added_service,appoint_user,cancel_appoint,user_group")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }

    @Data
    public static class FilterRule{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<FilterRuleItem> itemList;
    }

    /**
     * 召回规则item
     */
    @Data
    public static class FilterRuleItem{

        /**
         * master_group: 师傅人群，master_quota: 师傅指标
         */
        @NotEmpty
        private String itemType;

        /**
         * 规则项名称
         */
        @NotEmpty
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
         */
        @NotEmpty
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        /**
         * 规则项值
         */
        @NotEmpty
        private String itemValue;
    }

}
