package com.wanshifu.master.order.push.api;

import com.wanshifu.master.order.push.api.decoder.BigDataApiDecoder;
import com.wanshifu.master.order.push.domain.api.response.GetMasterJiedanInfoResp;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "bigdata-data-service",
        url = "${wanshifu.master-bigdata-open-api.url}",
        configuration = {DefaultEncoder.class, BigDataApiDecoder.class, BigDataApiDecoder.ApiErrorDecoder.class})
public interface MasterBigDataOpenApi {

    /***
     *查询师傅近一个月服务过的商品
     * @param masterIds
     * @param serveLv2Id
     * @return 师傅接单次数 & 入驻时长
     * 大数据重构每个新接口要传appCode
     */
    @GetMapping("${bigDataBaseUrl}" + "getMstServeLv2Index?appCode=d2986c35ae8e44f7966d29b8ac467879")
    List<GetMasterJiedanInfoResp> getMstServeLv2Index(@RequestParam("masterIds") String masterIds, @RequestParam("serveLv2Id") String serveLv2Id);


}