package com.wanshifu.master.order.push.util;

import com.alibaba.fastjson.JSONObject;

import java.math.BigDecimal;

public class JSONUtil {
	public static Long getLong(JSONObject json,String key,String defaultValue) {
		Long reuslt=null;
		try {
			reuslt=json.getLong(key);
			if (reuslt==null) {
				reuslt=Long.parseLong(defaultValue);
			}
		} catch (Exception e) {
			reuslt=Long.parseLong(defaultValue);
		}
		return reuslt;
	}
	
	public static Double getDouble(JSONObject json,String key,String defaultValue) {
		Double reuslt=null;
		try {
			reuslt=json.getDouble(key);
			if (reuslt==null) {
				reuslt=Double.parseDouble(defaultValue);
			}
		} catch (Exception e) {
			reuslt=Double.parseDouble(defaultValue);
		}
		return reuslt;
	}
	
	public static String getString(JSONObject json,String key,String defaultValue) {
		String reuslt=null;
		try {
			reuslt=json.getString(key);
			if (reuslt==null) {
				reuslt=defaultValue;
			}
		} catch (Exception e) {
			reuslt=defaultValue;
		}
		return reuslt;
	}

	public static BigDecimal getBigDecimal(JSONObject json, String key, BigDecimal defaultValue) {
		BigDecimal reuslt=null;
		try {
			reuslt=json.getBigDecimal(key);
			if (reuslt==null) {
				reuslt=defaultValue;
			}
		} catch (Exception e) {
			reuslt=defaultValue;
		}
		return reuslt;
	}
}
