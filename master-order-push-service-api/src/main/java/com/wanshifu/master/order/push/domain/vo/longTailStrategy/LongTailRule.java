package com.wanshifu.master.order.push.domain.vo.longTailStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.FilterRuleItemVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.PushRuleVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.RangeSelectVo;
import com.wanshifu.master.order.push.domain.vo.baseSelectStrategy.StatusSelectVo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LongTailRule {

    /**
     * 触发规则
     */
    @NotEmpty
    private Trigger trigger;

    /**
     * 范围初筛
     */
    @Valid
    @NotNull
    private RangeSelectVo rangeSelect;

    /**
     * 状态初筛
     */
    @Valid
    private StatusSelectVo statusSelect;

    /**
     * 过滤规则
     */
    @NotEmpty(message = "至少包含一条过滤规则")
    @Valid
    private List<FilterRuleItemVo> filterRuleItem;

    /**
     * 推送规则
     */
    @Valid
    @NotNull
    private PushRuleVo pushRule;



    @Valid
    @NotNull
    private AppointGroup appointGroup;


    @Data
    public static class AppointGroup{

        @NotNull
        private Integer isAppointGroup;

        @NotNull
        private String condition;


        @Valid
        private List<RuleItem> itemList;

    }


    @Data
    public static class RuleItem{

        /**
         * master_group: 师傅人群，master_quota: 师傅指标
         */
        @NotEmpty
        private String itemType;

        /**
         * 规则项名称
         */
        @NotEmpty
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含 >,<,=,<=,>=
         */
        @NotEmpty
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        /**
         * 规则项值
         */
        @NotEmpty
        private String itemValue;
    }
}
