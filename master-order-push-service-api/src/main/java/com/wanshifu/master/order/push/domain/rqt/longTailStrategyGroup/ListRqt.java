package com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup;

import com.wanshifu.framework.core.page.Pager;
import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 描述 :  策略组列表Rqt.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {

    /**
     * 业务线id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    private Long businessLineId;

    /**
     * 策略名称
     */
    private String longTailStrategyGroupName;


    private String categoryId;


    private String cityId;


    /**
     * 状态 1:启用 0:禁用
     */
    @ValueIn("0,1")
    private Integer isActive;

    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;

}