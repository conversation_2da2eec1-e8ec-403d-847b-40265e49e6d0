package com.wanshifu.master.order.push.function;

import com.ql.util.express.Operator;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 计算未来某个日期距今天数
 * <AUTHOR>
 */
@Slf4j
public class DurationDaysFromNow  extends Operator {

    @Override
    public Object executeInner(Object[] list) throws Exception{
        if (list.length != 2){
            throw new Exception("操作数异常");
        }
        Long result;
        Long defaultValue = 99999L;
        try {
            String dateString= (String)list[0];
            defaultValue = Long.valueOf((Integer)list[1]);
            if (StringUtils.isBlank(dateString)) {
                result=defaultValue.longValue();
            }else{
                result= DateFormatterUtil.durationDaysFromNow(dateString);
                if (result==null) {
                    result=defaultValue.longValue();
                }
            }
        } catch (Exception e) {
            log.error("DurationDaysFromNow error",e);
            result=defaultValue.longValue();
        }
        return result;
    }
}
