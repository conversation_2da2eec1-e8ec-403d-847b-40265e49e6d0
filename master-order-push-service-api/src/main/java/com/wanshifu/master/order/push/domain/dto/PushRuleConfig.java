package com.wanshifu.master.order.push.domain.dto;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PushRuleConfig {

    private Integer appointType;

    private String pushRuleType;

    /**
     * 大数据ab实验编号
     */
    private Integer testId;

    /**
     * 是否ab实验，1：是，0：否
     */
    private Integer testFlag;

    /**
     * 大数据实验组别id
     */
    private Integer testGroupId;

    /**
     * 大数据实验组别名称
     */
    private String testGroupName;

    /**
     * 最佳报价数
     */
    private Integer bestOfferNum;

    private Integer bestOfferIntervalTime;


    private FixedRoundsRule fixedRoundsRule;

    private List<DynamicRoundsRule> dynamicRoundsRuleList;


    @Data
    public static class FixedRoundsRule{

        /**
         * 最佳报价数
         */
        @NotNull
        private Integer bestOfferNum;

        /**
         * 推送时间间隔
         */
        @NotNull
        private Integer delayMinutesBetweenRounds;

        /**
         * 首轮推送人数
         */
        @NotNull
        private Integer firstPushMasterNumPerRound;

        /**
         * 非首轮推送人数
         */
        @NotNull
        private Integer delayPushMasterNumPerRound;

        /**
         * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String firstPushMasterFlag;

        /**
         * 首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private Integer firstPushMasterPercent;

        /**
         * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
         */
        @ValueIn("master_new,master_old,all")
        @NotEmpty
        private String delayPushMasterFlag;

        /**
         * 非首轮推送师傅人数占比 (0,100]
         */
        @DecimalMin(inclusive = false, value = "0")
        @DecimalMax(value = "100")
        private Integer delayPushMasterPercent;    }


    @Data
    public static class TriggerParam{

        @NotBlank
        @ValueIn("and,or")
        private String condition;

        @NotEmpty
        @Valid
        private List<Item> itemList;

    }


    @Data
    public static class Item{

        @NotBlank
        private String itemName;

        @NotBlank
        @ValueIn("in,not_in,>,<,=,<=,>=")
        private String term;

        private String itemValue;
    }


    @Data
    public static class DynamicRoundsRule{

        private String rounds;

        private BigDecimal pushScoreStartValue;

        private BigDecimal pushScoreEndValue;

        private BigDecimal offerRate;

        private Integer masterNum;

        private Integer batchNum;

        private Integer deliveryPercent;

        private List<WheelRoundRule> wheelRoundRuleList;

    }


    @Data
    public static class WheelRoundRule{

        private TriggerParam triggerParam;

        private String pushTarget;

        private String pushRate;

        private Integer batchTime;

        private Integer batchNum;



    }

}
