package com.wanshifu.master.order.push.domain.rqt.orderMatchRouting;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改初筛策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 策略id
     */
    @NotNull
    private Integer routingId;

    private Long updateAccountId;
}