package com.wanshifu.master.order.push.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 特征查询Service
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeatureQueryServiceImpl {

	@Resource
	private HBaseClient hBaseClient;

	@Resource
	private MySqlQuery mySqlQuery;


	public static Map<String, List<JSONObject>> getMasterFeatureMap(JSONArray result) {

		Map<String, List<JSONObject>> masterFeatureMap=new HashMap<>();
		if(result.size() != 0) {
			for (int i = 0; i < result.size(); i++) {
				//师傅
				JSONObject masterFeatureRow = result.getJSONObject(i);
				String masterId = masterFeatureRow.getString(FieldConstant.MASTER_ID);
				if(!masterFeatureMap.containsKey(masterId)){
					List<JSONObject> list = new ArrayList<>();
					list.add(masterFeatureRow);
					masterFeatureMap.put(masterId, list);

				}else{
					masterFeatureMap.get(masterId).add(masterFeatureRow);
				}
			}
			return masterFeatureMap;
		} else {
			return masterFeatureMap;
		}
	}

	public JSONObject queryOrderFeature(Collection<String> dimensionColumns, Set<String> indexColumns, Map<String, Object> dimensionData,String dbType,String tableName) {
		if("hbase".equals(dbType)){
			Get get = generateHbaseQueryGet(dimensionColumns, dimensionData);
			return hBaseClient.querySingle(indexColumns,get,tableName);
		}else if("mysql".equals(dbType)){
			String conditions = generateConditionsByIndexColumns(dimensionColumns, dimensionData);
			JSONArray jsonArray = mySqlQuery.query(dimensionColumns,indexColumns,tableName,conditions);
			if(Objects.nonNull(jsonArray) && jsonArray.size() > 0){
				return (JSONObject)jsonArray.get(0);
			}
		}
		return null;
	}


	/**
	 * 生成batch condition查询条件
	 *
	 * @return
	 */
	private Get generateHbaseQueryGet(Collection<String> dimensionColumns, Map<String, Object> dimensionData) {
		StringBuilder key = new StringBuilder();
		for (String column : dimensionColumns) {
			Object dimensionValueObject = dimensionData.get(column);
			if (dimensionValueObject == null) {
				log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
				throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
			}
			String dimensionValue = dimensionValueObject.toString();
			key.append(dimensionValue);
		}
		return new Get(Bytes.toBytes(key.toString()));
	}

	
	public JSONArray queryMasterFeature(List<String> dimensionColumns, List<String> indexColumns,
												  Map<String, Object> dimensionData, Set<String> masterIdSet, String dbType,String tableName,
	String multiValueDimension) {
		JSONArray result = new JSONArray();
		try {
			if (CollectionUtils.isEmpty(masterIdSet)) {
				return result;
			}

			if(StringUtils.isNotBlank(multiValueDimension)) {

				List multiValueList = (List) dimensionData.get(multiValueDimension);
				if(CollectionUtils.isEmpty(multiValueList)){
					log.info(String.format("维度缺失，multiValueDimension: %s,dimensionData: %s",multiValueDimension, JSON.toJSONString(dimensionData)));
					return result;
				}


				for (Object current : multiValueList) {

					dimensionData.put(multiValueDimension,current);
					queryMasterFeature(dimensionColumns, indexColumns,
							dimensionData, masterIdSet, dbType, tableName,result);
				}

				dimensionData.put(multiValueDimension,multiValueList);

			}else{
				queryMasterFeature(dimensionColumns, indexColumns,
						dimensionData, masterIdSet, dbType, tableName,result);
			}

		} catch (Exception e) {
			log.error("queryMasterFeature error",e);
		}
		return result;
	}


	private void queryMasterFeature(List<String> dimensionColumns, List<String> indexColumns,
				 Map<String, Object> dimensionData, Set<String> masterIdSet, String dbType,String tableName,JSONArray result){
		HashSet<String> questSet = new HashSet<>();

		for (String string : masterIdSet) {
			questSet.add(string);
			if (questSet.size() >= 400) {
				queryMasterFeatureBatch(dimensionColumns,indexColumns,dimensionData,questSet,dbType,tableName,result);
				questSet = new HashSet<>();
			}
		}
		if (CollectionUtils.isNotEmpty(questSet)) {
			queryMasterFeatureBatch(dimensionColumns,indexColumns,dimensionData,questSet,dbType,tableName,result);
		}
	}


	public JSONArray queryMasterFeatureBatch( List<String> dimensionColumns, List<String> indexColumns,
										 Map<String, Object> dimensionData, Set<String> masterIdSet,String dbType, String tableName,JSONArray result) {

		try {
			if (CollectionUtils.isEmpty(masterIdSet)) {
				return result;
			}
			HashSet<String> questSet = new HashSet<>();
			for (String string : masterIdSet) {
				questSet.add(string);
				if (questSet.size() >= 400) {
					JSONArray empResult = query(dimensionColumns,indexColumns,dimensionData,dbType,tableName,questSet);
					if(empResult != null && empResult.size() > 0) {
						result.addAll(empResult);
					}
					questSet = new HashSet<>();
				}
			}
			if (CollectionUtils.isNotEmpty(questSet)) {
				JSONArray empResult = query(dimensionColumns,indexColumns,dimensionData,dbType,tableName,questSet);
				if(empResult != null && empResult.size() > 0) {
					result.addAll(empResult);
				}
			}
		} catch (Exception e) {
			log.error("queryMasterFeatureBatch error",e);
		}
		return result;
	}


	private JSONArray query(List<String> dimensionColumns, List<String> indexColumns,
							Map<String, Object> dimensionData,String dbType, String tableName,HashSet<String> questSet){
		if("hbase".equals(dbType)){
			List<Get> getList = generateHbaseQueryGets(dimensionColumns, dimensionData,
					"master_id", questSet);
			indexColumns.add("master_id");
			return hBaseClient.query(getList,indexColumns,tableName);
		}else if("mysql".equals(dbType)){
			String conditions = generateBatchQueryConditions(dimensionColumns, indexColumns, dimensionData,
					"master_id", questSet);
			return mySqlQuery.query(dimensionColumns,indexColumns,tableName,conditions);
		}
		return null;
	}




	/**
	 * 生成批量查询参数
	 * 
	 * @return
	 */
	private String generateBatchQueryConditions(Collection<String> dimensionColumns, Collection<String> indexColumns,
			Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue)
			 {
		return generateBatchConditionsByIndexColumns(dimensionColumns, dimensionData,
				batchDimensionColumn, batchColumnValue);
	}

	/**
	 * 生成conditon查询条件
	 * 
	 * @return
	 */
	private String generateConditionsByIndexColumns(Collection<String> dimensionColumns,
			Map<String, Object> dimensionData) {
		StringBuilder conditonString = new StringBuilder();
		for (String column : dimensionColumns) {
			Object dimensionValueObject = dimensionData.get(column);
			if (dimensionValueObject == null) {
				if (!FieldConstant.ORDER_GOODS_ID.equals(column)&&!FieldConstant.ENTERPRISE_ID.equals(column)) {
				}
				log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
				throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
			}
			String dimensionValue = dimensionValueObject.toString();
			conditonString.append(column);
			conditonString.append(" = ");
			if (dimensionValueObject.getClass() == String.class) {
				conditonString.append("'");
				conditonString.append(dimensionValue);
				conditonString.append("'");
			} else {
				conditonString.append(dimensionValue);
			}
			conditonString.append(" and ");
		}
		if (conditonString.length() != 0) {
			conditonString.setLength(conditonString.length() - 5);
		}
		return conditonString.toString();
	}

	/**
	 * 生成batch condition查询条件
	 * 
	 * @return
	 */
	private String generateBatchConditionsByIndexColumns(Collection<String> dimensionColumns,
			Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue) {
		StringBuilder conditonString = new StringBuilder();
		for (String column : dimensionColumns) {
			if (column.equals(batchDimensionColumn)) {
				continue;
			}
			Object dimensionValueObject = dimensionData.get(column);
			if (dimensionValueObject == null) {
				log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
				throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
			}
			String dimensionValue = dimensionValueObject.toString();
			conditonString.append(column);
			conditonString.append(" = ");
			conditonString.append("'");
			conditonString.append(dimensionValue);
			conditonString.append("'");
			conditonString.append(" and ");
		}
		String batchValue = StringUtils.join(batchColumnValue, "','");
		batchValue = "'" + batchValue + "'";
		conditonString.append(batchDimensionColumn).append(" in (").append(batchValue).append(")");
		return conditonString.toString();
	}



	/**
	 * 生成batch condition查询条件
	 *
	 * @return
	 */
	private List<Get> generateHbaseQueryGets(List<String> dimensionColumns,
															Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue) {
		List<Get> getList = new ArrayList<>();
		for(String masterId : batchColumnValue){
			StringBuilder key = new StringBuilder(masterId);
			for (String column : dimensionColumns) {
				if (column.equals(batchDimensionColumn)) {
					continue;
				}
				Object dimensionValueObject = dimensionData.get(column);
				if (dimensionValueObject == null) {
					log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
					throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
				}
				String dimensionValue = dimensionValueObject.toString();
				key.append("_").append(dimensionValue);
			}
			getList.add(new Get(Bytes.toBytes(key.toString())));
		}
		return getList;
	}


}
