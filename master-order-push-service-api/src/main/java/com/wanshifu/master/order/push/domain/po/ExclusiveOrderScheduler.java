package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "exclusive_order_scheduler_base")
public class ExclusiveOrderScheduler {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "config_id")
    private Long configId;

    /**
     * 策略快照id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 策略名称
     */
    @Column(name = "category_id")
    private Integer categoryId;

    /**
     * 策略名称
     */
    @Column(name = "schedule_time")
    private Integer scheduleTime;


    /**
     * 更新人
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建人
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
