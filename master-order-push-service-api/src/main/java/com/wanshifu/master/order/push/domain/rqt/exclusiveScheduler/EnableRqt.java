package com.wanshifu.master.order.push.domain.rqt.exclusiveScheduler;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  禁用启用
 *
 * <AUTHOR>
 */
@Data
public class EnableRqt {

    /**
     * 配置id
     */
    @NotNull
    private Long configId;

    /**
     * 状态 1:启用 0:禁用
     */
    @NotNull
    @ValueIn("1,0")
    private Integer strategyStatus;


    private Long updateAccountId;
}