package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述 : 策略关联类型.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-02 16:44
 */
@AllArgsConstructor
@Getter
public enum StrategyRelateTypeEnum {

    REPUSH_POLICY("repush_policy", "重推机制"),
    STRATEGY_COMBINATION_PRIORITY("strategy_combination_priority", "策略组合-优先推荐路由"),
    STRATEGY_COMBINATION_ALTERNATE("strategy_combination_alternate", "策略组合-备用推荐路由"),
    ;
    private final String code;
    private final String name;
}