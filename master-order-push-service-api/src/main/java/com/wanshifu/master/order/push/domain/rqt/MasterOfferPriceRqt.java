package com.wanshifu.master.order.push.domain.rqt;

import com.wanshifu.framework.core.validation.annotation.AmountPattern;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/28 15:31
 */
@Data
public class MasterOfferPriceRqt {


    /**
     * 订单ID
     */
    @NotNull
    @Min(1L)
    private Long orderId;

    /**
     * 订单修改时间
     */
    @NotNull
    private String orderModifyTime;

    @AmountPattern(regex = "^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$")
    @Max(10000000)
    private BigDecimal offerPrice;

    /**
     * 报价备注
     */
    String offerNote;

    /**
     * 是否属于用户分享的订单  1：是  0：否  #V4.9
     */
    @NotNull(message = "参数是否是分享订单不能为空")
    private Integer belongUserShare;

    @NotNull(message = "是否包含配件不能为空")
    private Boolean includeFitting;

    @NotNull(message = "是否提供台面服务参数不能为空")
    private Integer isFurnishMesaInstallation;

    /**
     * 专属订单报价方式  1：系统自动报价  2：专属师傅报价  #V7.3
     */
    private Integer exclusiveOrderOfferType;

    /**
     * 师傅Id v7.3
     */
    private Long masterId;

    /**
     * 订单包id V7.7.2
     */
    private Long orderPackageId;

    /**
     * 订单包推单标签 0:预定订单包  1:用户公开报价转订单包
     */
    private Integer orderPackageOrderLabel;

    private Integer isContract;


    /**
     * 0:师傅主动报价  3:师傅开启自动接单
     */
    private Integer offerPriceType = 0;
}
