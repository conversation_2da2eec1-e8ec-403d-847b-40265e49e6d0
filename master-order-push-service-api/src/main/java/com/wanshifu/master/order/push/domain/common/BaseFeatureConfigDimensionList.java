package com.wanshifu.master.order.push.domain.common;
import java.util.*;


import cn.hutool.core.util.StrUtil;
import com.wanshifu.master.order.push.domain.po.BaseFeature;

/**
 * 基础特征配置维度实体类
 * <AUTHOR>
 */
public class BaseFeatureConfigDimensionList extends ArrayList<BaseFeature> {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private ArrayList<String> featureFieldList=new ArrayList<>();
	private ArrayList<String> featureCodeList=new ArrayList<>();
	private boolean isDynamicDimension = false;
	private String multiValueDimension;
	private List<String> dynamicDimensionList;
	private Map<String, String> dynamicDimensionKeyValue; 

	@Override
	public boolean add(BaseFeature baseFeature) {
		try {
			featureFieldList.add(baseFeature.getFeatureField());
			featureCodeList.add(baseFeature.getFeatureCode());
		} catch (Exception e2) {
		}
		return super.add(baseFeature);
	}

	public ArrayList<String> getFeatureFieldList() {
		return featureFieldList;
	}

	public ArrayList<String> getFeatureCodeList() {
		return featureCodeList;
	}

	public boolean isDynamicDimension() {
		return isDynamicDimension;
	}

	public void setDynamicDimensionTrue(String featureDimension) {
		dynamicDimensionKeyValue=new HashMap<>();
		dynamicDimensionList=new ArrayList<>();
		if (featureDimension!=null) {
			Arrays.stream(featureDimension.split(",")).sequential().forEach(row -> {
				if (row.contains("{")) {
					String[] dimensionData=StrUtil.removeAll(row, '{','}').split("=");
					setDimensionData(dimensionData);
					addDynamicDimensionToList(dimensionData[0]);
				}else {
					addDynamicDimensionToList(row);
				}
			});
		}
		this.isDynamicDimension = true;
	}
	
	public List<String> dynamicDimensionList() {
		return dynamicDimensionList;
	}

	public void setDimensionData(String[] dimensionData) {
		dynamicDimensionKeyValue.put(dimensionData[0], dimensionData[1]);
	}
	
	private void addDynamicDimensionToList(String dimension) {
		dynamicDimensionList.add(dimension);
	}
	/**
	 * 生成动态维度特征
	 * @param dimensionData
	 */
	public void genrateDimensionFeature(Map<String, Object> dimensionData) {
		if (dynamicDimensionKeyValue!=null) {
			try {
				dynamicDimensionKeyValue.entrySet().stream().forEach(row -> dimensionData.put(row.getKey(), dimensionData.get(row.getValue())));
			} catch (Exception e) {
			}
		}
	}

	@Override
	public String toString() {
		return "BaseFeatureConfigList [featureNameList=" + featureFieldList + "]";
	}

	public String getMultiValueDimension() {
		return multiValueDimension;
	}

	public void setMultiValueDimension(String multiValueDimension) {
		this.multiValueDimension = multiValueDimension;
	}
}
