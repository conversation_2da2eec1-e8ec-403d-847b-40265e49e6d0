package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 重推策略规则实体类
 * <AUTHOR>
 */
@Data
public class RepushPolicyRule {
    private StrategyIdList strategyCombination;
    private String pushRuleType;
    private List<OrderPushRule> pushRule;
    private Integer pushRuleId;
    private Integer isFilterPushedMaster;
    private OpenCondition openCondition;

    @Data
    public static class OpenCondition{

        private OfferIntervalTime offerIntervalTime;

        private OpenConditionQlExpression openConditionQlExpression;

    }

    @Data
    public static class OfferIntervalTime{
        private Integer interval;
        private Integer itemValue;
        private String term;
    }

    @Data
    public static class OpenConditionQlExpression{

        private String qlExpression;

        private String qlExpressionParams;

    }



}
