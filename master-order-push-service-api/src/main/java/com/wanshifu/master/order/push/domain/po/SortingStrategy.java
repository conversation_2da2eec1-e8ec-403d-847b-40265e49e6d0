package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * 精排策略表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "sorting_strategy")
public class SortingStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Long strategyId;

    /**
     * 策略快照id
     */
    @Column(name = "snapshot_id")
    private Long snapshotId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 类目id，多个以逗号拼接
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 评分规则配置(JSON格式)
     */
    @Column(name = "sorting_rule")
    private String sortingRule;

    /**
     * 评分规则表达式
     */
    @Column(name = "rule_expression")
    private String ruleExpression;

    /**
     * 策略状态，1：启用，0：禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 策略版本号
     */
    @Column(name = "strategy_version")
    private Integer strategyVersion;


    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 订单标识,normal:普通订单 ikea:宜家订单
     */
    @Column(name = "order_flag")
    private String orderFlag;
}