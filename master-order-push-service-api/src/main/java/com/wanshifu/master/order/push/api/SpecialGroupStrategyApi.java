package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 特殊人群策略API接口
 *
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@FeignClient(value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "specialGroupStrategy",
        configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface SpecialGroupStrategyApi {

    /**
     * 创建特殊人群策略
     *
     * @param rqt 创建请求
     * @return 策略id
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);

    /**
     * 更新特殊人群策略
     *
     * @param rqt 更新请求
     * @return 更新结果
     */
    @PostMapping("/update")
    int update(@RequestBody @Valid UpdateRqt rqt);

    /**
     * 获取特殊人群策略详情
     *
     * @param rqt 详情请求
     * @return 策略详情
     */
    @PostMapping("/detail")
    SpecialGroupStrategy detail(@RequestBody @Valid DetailRqt rqt);

    /**
     * 分页查询特殊人群策略列表
     *
     * @param rqt 列表查询请求
     * @return 分页结果
     */
    @PostMapping("/list")
    SimplePageInfo<SpecialGroupStrategy> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 启用/禁用特殊人群策略
     *
     * @param rqt 启用/禁用请求
     * @return 操作结果
     */
    @PostMapping("/enable")
    Integer enable(@RequestBody @Valid EnableRqt rqt);

    /**
     * 删除特殊人群策略
     *
     * @param rqt 删除请求
     * @return 删除结果
     */
    @PostMapping("/delete")
    Integer delete(@RequestBody @Valid DeleteRqt rqt);
}
