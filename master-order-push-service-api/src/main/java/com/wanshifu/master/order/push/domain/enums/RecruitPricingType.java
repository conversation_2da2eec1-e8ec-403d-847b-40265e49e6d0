package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 招募定价方式
 * <AUTHOR>
 */
public enum RecruitPricingType {

    /**
     * 街道包
     */
    NO_COOPERATION_PRICE("noCooperationPrice","无合作价"),

    /**
     * 电子围栏
     */
    UNITE("unite","统一定价"),


    /**
     * 电子围栏
     */
    CITY("city","按城市定价"),


    /**
     * 电子围栏
     */
    REGIONAL("regional","按区域定价");



    private final String code;
    private final String name;

    RecruitPricingType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    @Override
    public String toString(){
        return code;
    }


    private static final Map<String, RecruitPricingType> valueMapping = new HashMap<>((int) (RecruitPricingType.values().length / 0.75));

    static {
        for (RecruitPricingType instance : RecruitPricingType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static RecruitPricingType asValue(String value) {
        return valueMapping.get(value);
    }


}
