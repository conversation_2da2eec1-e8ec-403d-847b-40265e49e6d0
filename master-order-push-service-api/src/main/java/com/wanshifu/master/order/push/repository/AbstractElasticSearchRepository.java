package com.wanshifu.master.order.push.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.EsResponseSingle;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Title
 * <p>
 * 参数定义：
 * T ：es配置bean
 * <p>
 * Author <EMAIL>
 * Time 2019/4/12
 */
@Slf4j
public abstract class AbstractElasticSearchRepository<T> implements ElasticsearchRepository {

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    @Value("${baseSelect.search.after.es.time:3000}")
    private int baseSelectSearchAfterEsTime;

    /**
     * 单条记录查询
     */
    protected EsResponseSingle<T> find(QueryBuilder queryBuilder) {
        EsResponseSingle<T> esResponse = new EsResponseSingle<>();
        List<T> list = deserializationResponse(getSearchResponse(queryBuilder));
        if (list == null || list.size() < 1) {
            return null;
        }
        esResponse.setData(CollectionUtils.getFirstSafety(list));
        return esResponse;
    }

    /**
     * 多条记录查询
     */
    public EsResponse<T> search(QueryBuilder queryBuilder) {
        EsResponse<T> esResponse = new EsResponse<>();
        esResponse.setDataList(deserializationResponse(getSearchResponse(queryBuilder)));
        return esResponse;
    }

    public EsResponse<T> searchAfter(QueryBuilder queryBuilder,SortBuilder sortBuilder,Integer pageSize,Integer limitCount) {

        EsResponse<T> esResponse = new EsResponse<>();
        List<T> dataList = new ArrayList<>();

        try{
            Class<T> clazz = getElasticEntityClass();
            Document annotation = clazz.getAnnotation(Document.class);
            String indexAlias = annotation.indexAlias();
            String type = annotation.type();
            SearchRequest searchRequest = new SearchRequest(indexAlias);
            searchRequest.types(type);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 设置查询条件
            searchSourceBuilder.query(queryBuilder);

            // 设置排序
            searchSourceBuilder.sort(sortBuilder);

            // 设置每页的大小
            searchSourceBuilder.size(pageSize);
            searchRequest.source(searchSourceBuilder);


            SearchResponse searchResponse;
            Object[] sortValues;
            while(true) {
                searchResponse = restHighLevelClient.search(searchRequest);

                if(searchResponse.getHits().getHits().length == 0){
                    break;
                }
//                // 获取结果
//                for (SearchHit hit : searchResponse.getHits().getHits()) {
//                    System.out.println(hit.getSourceAsString());
//                }

                dataList.addAll(deserializationResponse(searchResponse));

                if(dataList.size() >= limitCount){
                    break;
                }

                // 如果有下一页，记录上一页的最后一个文档的排序值
                sortValues  = searchResponse.getHits().getHits()[searchResponse.getHits().getHits().length - 1].getSortValues();

                // 下一次查询时使用
                searchSourceBuilder.searchAfter(sortValues);
            }
        }catch(IOException e){
            throw new RuntimeException("ElasticSearch throws an IOException", e);
        }
        esResponse.setDataList(dataList);
        return esResponse;
    }

    public EsResponse<T> searchAfter(MasterMatchCondition masterMatchCondition, QueryBuilder queryBuilder, SortBuilder sortBuilder, Integer pageSize, Integer limitCount) {

        EsResponse<T> esResponse = new EsResponse<>();
        List<T> dataList = new ArrayList<>();

        try {
            Class<T> clazz = getElasticEntityClass();
            Document annotation = clazz.getAnnotation(Document.class);
            String indexAlias = annotation.indexAlias();
            String type = annotation.type();
            SearchRequest searchRequest = new SearchRequest(indexAlias);
            searchRequest.types(type);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 设置查询条件
            searchSourceBuilder.query(queryBuilder);

            // 设置排序
            searchSourceBuilder.sort(sortBuilder);

            // 设置每页的大小
            searchSourceBuilder.size(pageSize);
            searchRequest.source(searchSourceBuilder);


            SearchResponse searchResponse;
            Object[] sortValues;
            int currentPage = 0;
            while (true) {
                currentPage++;
                long beginTime = System.currentTimeMillis();

                searchResponse = restHighLevelClient.search(searchRequest);

                long endTime = System.currentTimeMillis();
                long searchTime = endTime - beginTime;
                log.info("master_base baseSelect searchAfter everySearchTime: {} ms,订单id:{}", searchTime, masterMatchCondition.getMasterOrderId());
                if (searchTime > baseSelectSearchAfterEsTime) {
                    log.info("master_base baseSelect searchAfter searchTime greater than,actual take: {} ms,订单id:{},技能数：{}，页码：{}，boolQueryBuilder：{}",
                            searchTime, masterMatchCondition.getMasterOrderId(),
                            CollectionUtil.isEmpty(masterMatchCondition.getTechnologysInDemandSet()) ? "" : masterMatchCondition.getTechnologysInDemandSet().size(),
                            currentPage,
                            searchSourceBuilder.toString());

                }
                if (searchResponse.getHits().getHits().length == 0) {
                    break;
                }

                dataList.addAll(deserializationResponse(searchResponse));

                if (dataList.size() >= limitCount) {
                    break;
                }

                // 如果有下一页，记录上一页的最后一个文档的排序值
                sortValues = searchResponse.getHits().getHits()[searchResponse.getHits().getHits().length - 1].getSortValues();

                // 下一次查询时使用
                searchSourceBuilder.searchAfter(sortValues);
            }
        } catch (IOException e) {
            throw new RuntimeException("ElasticSearch throws an IOException", e);
        }
        esResponse.setDataList(dataList);
        return esResponse;
    }


    public SearchResponse search(QueryBuilder queryBuilder, AggregationBuilder aggregationBuilder) {
        try{
            return this.search(queryBuilder, aggregationBuilder,restHighLevelClient);
        }catch(Exception e){
            e.printStackTrace();
        }
        return null;
  }

    private SearchResponse getSearchResponse(QueryBuilder queryBuilder) {
        SearchResponse response;
        try {
            response = this.search(queryBuilder, restHighLevelClient);
        } catch (IOException e) {
            throw new RuntimeException("ElasticSearch throws an IOException", e);
        }
        return response;
    }

    /**
     * 分页查询
     * warning 根据别名分页查询时，旧索引和新索引数据会重叠(不使用别名可忽略此问题)
     */
    public EsResponse<T> search(QueryBuilder queryBuilder, Pageable pageable , List<SortBuilder> sortBuilderList) {
        EsResponse<T> esResponse = new EsResponse<>();
        if (pageable == null) {
            pageable = new Pageable(0, 10);
        }
        pageable.setPageSize(pageable.getPageSize() == 0 ? 10 : pageable.getPageSize());
        SearchResponse response;
        try {
            response = this.search(queryBuilder, restHighLevelClient, pageable,sortBuilderList);
        } catch (IOException e) {
            throw new RuntimeException("ElasticSearch throws an IOException", e);
        }
        esResponse.setDataList(deserializationResponse(response));
        esResponse.setTotal(response.getHits().totalHits);
        return esResponse;
    }

    private List<T> deserializationResponse(SearchResponse response) {
        List<T> results = new ArrayList<>();
        for (SearchHit hit : response.getHits()) {
            if (hit != null) {
                String source = hit.getSourceAsString();
                T result = mapEntity(genericHitSource(source), getEntityClass());
                results.add(result);
            }
        }
        return results;
    }

    private String genericHitSource(String source) {
        JSONObject item = JSONObject.parseObject(source);
        JSONObject object = new JSONObject();
        for (Map.Entry<String, Object> entry : item.entrySet()) {
            object.put(StringUtils.underLine2Camel(entry.getKey()), entry.getValue());
        }
        return object.toJSONString();
    }

    @SuppressWarnings("unchecked")
    private Class<T> getEntityClass() {
        ParameterizedType parameterizedType = resolveReturnedClassFromGenericType(getClass());
        return (Class<T>) parameterizedType.getActualTypeArguments()[0];
    }

    private ParameterizedType resolveReturnedClassFromGenericType(Class<?> clazz) {
        Object genericSuperclass = clazz.getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            return (ParameterizedType) genericSuperclass;
        }
        return resolveReturnedClassFromGenericType(clazz.getSuperclass());
    }

    private <E> E mapEntity(String source, Class<E> clazz) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        return JSONObject.parseObject(source, clazz);
    }

    @Override
    public Class getElasticEntityClass() {
        return getEntityClass();
    }

    @Override
    public void closeClient(RestHighLevelClient client) {
        if (client != null) {
            try {
                client.close();
            } catch (IOException e) {
                log.error("关闭ElasticSearch RestHighLevelClient失败");
            }
        }
    }
}
