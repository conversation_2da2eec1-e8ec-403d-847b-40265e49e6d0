package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 18:23
 */
@Data
@ToString
@Table(name = "agent_distribute_strategy")
public class AgentDistributeStrategy {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;

    /**
     * 代理商id
     * B端团队师傅代理商存的则是团队长师傅id
     */
    @Column(name = "agent_id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @Column(name = "agent_name")
    private String agentName;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;

    /**
     * 城市id
     */
    @Column(name = "city_division_id")
    private Long cityDivisionId;

    /**
     * 服务id集合
     */
    @Column(name = "serve_ids")
    private String serveIds;

    @Column(name = "serve_id_array")
    private String serveIdArray;

    /**
     * 服务名称集合
     */
    @Column(name = "serve_names")
    private String serveNames;

    /**
     * 合作区域
     */
    @Column(name = "third_division_ids")
    private String thirdDivisionIds;

    /**
     * 合作街道
     */
    @Column(name = "fourth_division_ids")
    private String fourthDivisionIds;

    /**
     * 定向推送的推送策略
     */
    @Column(name = "push_strategy")
    private String pushStrategy;

    /**
     * 调度规则
     */
    @Column(name = "distribute_rule")
    private String distributeRule;

    /**
     * 调度策略
     */
    @Column(name = "distribute_priority")
    private String distributePriority;

    /**
     * 评分模型策略id
     */
    @Column(name = "scoring_strategy_id")
    private Long scoringStrategyId;

    /**
     * 策略状态，1：已启用，0：已禁用
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;


    /**
     * 合作商类型，toc:C端合作商，
     * tob:B端合作商，
     * tobGroup:B端团队师傅
     *
     */
    @Column(name = "master_source_type")
    private String masterSourceType;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     *创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新时间
     */
    @Column(name = "non_effective_time")
    private String nonEffectiveTime;
}
