//package com.wanshifu.master.order.push.service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//import com.alicloud.openservices.tablestore.AsyncClient;
//
//
//import com.alicloud.openservices.tablestore.ClientConfiguration;
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.TableStoreCallback;
//import com.alicloud.openservices.tablestore.model.*;
//import com.alicloud.openservices.tablestore.model.BatchGetRowResponse.RowResult;
//import com.alicloud.openservices.tablestore.model.filter.Filter;
//import com.alicloud.openservices.tablestore.model.search.SearchQuery;
//import com.alicloud.openservices.tablestore.model.search.SearchRequest;
//import com.alicloud.openservices.tablestore.model.search.SearchResponse;
//import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
//import com.alicloud.openservices.tablestore.model.search.query.MatchQuery;
//import com.alicloud.openservices.tablestore.model.search.query.Query;
//import com.alicloud.openservices.tablestore.model.search.query.QueryOperator;
//import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
//import com.alicloud.openservices.tablestore.model.search.query.TermQuery;
//import com.alicloud.openservices.tablestore.model.search.query.TermsQuery;
//import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
//import com.wanshifu.master.order.push.domain.constant.*;
//import com.wanshifu.master.order.push.domain.enums.RangeQueryType;
//import com.wanshifu.master.order.push.domain.po.DeleteRow;
//import com.wanshifu.master.order.push.domain.po.IncrementRow;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//
///**
// * TableStore的通用类
// * 
// * <AUTHOR>
// */
//@Slf4j
//@Component
//public class TableStoreClient {
//
//
//	private AsyncClient asyncClient;
//
//	private SyncClient syncClient;
//
//	@Value("${ots.accessKeyId}")
//	private String otsAccessKeyId;
//
//	@Value("${ots.accessKeySecret}")
//	private String otsAccessKeySecret;
//
//
//	@Value("${ots.instanceName}")
//	private String otsInstanceName;
//
//	@Value("${ots.endpoint}")
//	private String otsEndpoint;
//
//
//	/**
//	 * 初始化TableStore客户端
//	 *
//	 */
//	@PostConstruct
//	public void initTableStoreClient() {
//		initSyncClient();
//		initAsyncClient();
//	}
//
//
//	private void initSyncClient() {
//		try {
//			ClientConfiguration clientConfiguration = new ClientConfiguration();
//			clientConfiguration.setConnectionTimeoutInMillisecond(5000);
//			clientConfiguration.setSocketTimeoutInMillisecond(5000);
//			clientConfiguration.setRetryStrategy(new AlwaysRetryStrategy(6, 1000));
//			// DATA_CENTER客户端
//			if (syncClient == null) {
//				syncClient = new SyncClient(otsEndpoint, otsAccessKeyId, otsAccessKeySecret, otsInstanceName, clientConfiguration);
//			}
//		} catch (Exception e) {
//
//		}
//	}
//
//
//	private void initAsyncClient() {
//		try {
//			ClientConfiguration clientConfiguration = new ClientConfiguration();
//			clientConfiguration.setConnectionTimeoutInMillisecond(5000);
//			clientConfiguration.setSocketTimeoutInMillisecond(5000);
//			clientConfiguration.setRetryStrategy(new AlwaysRetryStrategy(6, 1000));
//			// DATA_CENTER客户端
//			if (asyncClient == null) {
//				asyncClient = new AsyncClient(otsEndpoint, otsAccessKeyId, otsAccessKeySecret, otsInstanceName, clientConfiguration);
//			}
//		} catch (Exception e) {
//
//		}
//	}
//
//
//
//
//
//	public SyncClient getSyncClient() {
//		return syncClient;
//	}
//
//
//	public AsyncClient getAsyncClient() {
//		return asyncClient;
//	}
//
//
//
//
//	public boolean idempotentValidate(String msgKey) {
////		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
////		primaryKeyBuilder.addPrimaryKeyColumn(FieldConstant.MSG_KEY, PrimaryKeyValue.fromString(msgKey));
////		PrimaryKey primaryKey = primaryKeyBuilder.build();
////		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(FieldConstant.MQ_MSG_IDEMPOTENT, primaryKey);
////		criteria.setMaxVersions(1);
////		GetRowResponse getRowResponse = syncClient.getRow(new GetRowRequest(criteria));
////		Row row = getRowResponse.getRow();
////		if (row != null) {
////			return false;
////		}
//		return true;
//	}
//
//	public List<RowResult> batchQueryByList(String tableName, List<String> queryList, String pkName1, String pkName2, String pkValue2,String columnToGet)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					multiRowQueryCriteria.addColumnsToGet(columnToGet);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				multiRowQueryCriteria.addColumnsToGet(columnToGet);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//	public List<RowResult> batchQueryBySet(String tableName, Set<String> queryList, String pkName1, String pkName2, String pkValue2,String columnToGet)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					multiRowQueryCriteria.addColumnsToGet(columnToGet);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				multiRowQueryCriteria.addColumnsToGet(columnToGet);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//
//	/**
//	 * 批量查询（针对单主键，且主键为字符串类型的表）
//	 * 
//	 * @param tableName
//	 *            表名
//	 * @param pkValueList
//	 *            主键值列表
//	 * @param pkColumnName
//	 *            主键字段名
//	 * @param attrColumnNameList
//	 *            属性列列名列表
//	 * @return
//	 */
//	public List<RowResult> batchQueryByList(String tableName, List<String> pkValueList, String pkColumnName, List<String> attrColumnNameList) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : pkValueList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkColumnName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					for (String field : attrColumnNameList) {
//						multiRowQueryCriteria.addColumnsToGet(field);
//					}
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				for (String field : attrColumnNameList) {
//					multiRowQueryCriteria.addColumnsToGet(field);
//				}
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	public List<RowResult> batchQueryByList(String tableName, List<String> pkValueList, String pkColumnName, String attrColumnName)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : pkValueList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkColumnName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					multiRowQueryCriteria.addColumnsToGet(attrColumnName);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				multiRowQueryCriteria.addColumnsToGet(attrColumnName);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//		throw e;
//		}
//		return result;
//	}
//
//	public List<RowResult> batchQueryByCollection(String tableName, Collection<String> pkValueList, String pkColumnName, String attrColumnName)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : pkValueList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkColumnName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					multiRowQueryCriteria.addColumnsToGet(attrColumnName);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				multiRowQueryCriteria.addColumnsToGet(attrColumnName);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//	public List<RowResult> batchQueryByList(String tableName, Set<String> pkValueList, String pkColumnName,
//												   String ... attrColumnNameList)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : pkValueList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkColumnName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					// 添加获取的字段
//					multiRowQueryCriteria.addColumnsToGet(attrColumnNameList);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				// 添加获取的字段
//				multiRowQueryCriteria.addColumnsToGet(attrColumnNameList);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 通过Set批量查询
//	 * 
//	 * @param queryList
//	 *            需要查询的列
//	 */
//	public  List<RowResult> batchQueryBySet(String tableName, Set<String> queryList, String pkName) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//
//	public List<RowResult> batchQueryBySetWithFilter(String tableName,Filter filter,
//													 String fixPrimaryKey,String fixPrimaryValue,
//													 Set<String> queryList, String pkName) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithTwoColumns(fixPrimaryKey,fixPrimaryValue,pkName,pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					multiRowQueryCriteria.setFilter(filter);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				multiRowQueryCriteria.setFilter(filter);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	public List<RowResult> batchQueryBySetWithCol(String tableName,String col, Set<String> queryList, String pkName) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithOneColumn(pkName, pkValue));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					multiRowQueryCriteria.addColumnsToGet(col);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				multiRowQueryCriteria.addColumnsToGet(col);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 通过Set批量查询(PK1為列表)
//	 * 
//	 * @param tableName
//	 * @param queryList
//	 * @param pkName2
//	 * @param pkValue2
//	 * @param pkName1
//	 * @return
//	 */
//	public List<RowResult> batchQueryBySet(String tableName, Set<String> queryList, String pkName1, String pkName2, String pkValue2)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 通过List批量查询(固定第二个主键的值)
//	 * 
//	 * @param tableName
//	 *            表名
//	 * @param queryList
//	 *            主键一值集合
//	 * @param pkName1
//	 *            主键一名
//	 * @param pkName2
//	 *            主键二名
//	 * @param pkValue2
//	 *            主键二值
//	 * @return
//	 * @throws Exception
//	 */
//	public List<RowResult> batchQueryByList(String tableName, List<String> queryList, String pkName1, String pkName2, String pkValue2)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(this.generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 通过List批量查询(固定第二个主键的值)
//	 * 
//	 * @param tableName
//	 *            表名
//	 * @param pkValue1List
//	 *            第一主键值集合
//	 * @param pkName1
//	 *            第一主键名
//	 * @param pkName2
//	 *            第二主键名
//	 * @param pkValue2
//	 *            主键二值
//	 * @param pkName3
//	 *            第三主键名
//	 * @param pkValue3List
//	 *            第三主键值集合
//	 * @return
//	 */
//	public List<RowResult> batchQueryByList(String tableName, List<String> pkValue1List, String pkName1, String pkName2, String pkValue2,
//			String pkName3, List<String> pkValue3List) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			int size = pkValue1List.size();
//			for (int j = 0; j < size; j++) {
//				i++;
//				multiRowQueryCriteria.addRow(
//						getPrimaryKeyWithThreePK(tableName, pkName1, pkValue1List.get(j), pkName2, pkValue2, pkName3, pkValue3List.get(j)));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = this.getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//
//	public void saveMsg(String msgKey) {
////		RowPutChange rowPutChange = new RowPutChange(FieldConstant.MQ_MSG_IDEMPOTENT, generatePrimaryKeyWithOneColumn(FieldConstant.MSG_KEY, msgKey));
////		syncClient.putRow(new PutRowRequest(rowPutChange));
//	}
//
//	/**
//	 * 构造只有一个主键列的主键对象
//	 * 
//	 * @param pkName1
//	 *            主键列名
//	 * @param pkValue1
//	 *            主键值（字符串类型）
//	 * @return
//	 */
//	public PrimaryKey generatePrimaryKeyWithOneColumn(String pkName1, String pkValue1) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		return primaryKey;
//	}
//
//	/**
//	 * 构造有两个主键列的主键对象
//	 * 
//	 * @param pkName1
//	 *            主键1列名
//	 * @param pkValue1
//	 *            主键1值（字符串类型）
//	 * @param pkName2
//	 *            主键2列名
//	 * @param pkValue2
//	 *            主键2值（数值类型）
//	 * @return
//	 */
//	public PrimaryKey generatePrimaryKeyWithTwoColumns(String pkName1, String pkValue1, String pkName2, String pkValue2) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		return primaryKey;
//	}
//
//	/**
//	 * 构造有三个主键列的主键对象
//	 * 
//	 * @param pkName1
//	 *            主键1列名
//	 * @param pkValue1
//	 *            主键1值（字符串类型）
//	 * @param pkName2
//	 *            主键2列名
//	 * @param pkValue2
//	 *            主键3值（字符串类型） * @param pkName3 主键3列名
//	 * @param pkValue3
//	 *            主键3值（字符串类型）
//	 * @return
//	 */
//	public PrimaryKey generatePrimaryKeyWithThreeColumnsString(String pkName1, String pkValue1, String pkName2, String pkValue2, String pkName3,
//			String pkValue3) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName3, PrimaryKeyValue.fromString(pkValue3));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		return primaryKey;
//	}
//	public static PrimaryKey generatePrimaryKeyWithThreeColumnsInteger(String pkName1, String pkValue1, String pkName2, String pkValue2, String pkName3,
//			Long pkValue3) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName3, PrimaryKeyValue.fromLong(pkValue3));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		return primaryKey;
//	}
//
//	/**
//	 * 查询行是否存在（单主键表）
//	 * 
//	 * @param otsTableName
//	 *            表名
//	 * @param pkName
//	 *            主键列名
//	 * @param pkValue
//	 *            主键值（仅支持单主键，且类型为String）
//	 * @return true,行存在;false,行不存在
//	 */
//	public boolean checkRowExistsWithOnePK(String otsTableName, String pkName, String pkValue) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName, PrimaryKeyValue.fromString(pkValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		criteria.addColumnsToGet(pkName);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		if (row != null) {
//			return true;
//		}
//		return false;
//	}
//
//	/**
//	 * 单行Get操作（单主键表）
//	 * 
//	 * @param otsTableName
//	 *            表名
//	 * @param pkName
//	 *            主键列名
//	 * @param pkValue
//	 *            主键值（仅支持单主键，且类型为String）
//	 * @return 行
//	 */
//	public Row getRowWithOnePK(String otsTableName, String pkName, String pkValue) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName, PrimaryKeyValue.fromString(pkValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		return row;
//	}
//
//	public Row getRowWithOnePKWithColumns(String otsTableName, String pkName, String pkValue,String...Columns) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName, PrimaryKeyValue.fromString(pkValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		criteria.addColumnsToGet(Columns);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		return row;
//	}
//
//	public Row getRowWithOnePKWithColumns(String otsTableName, String pkName, Long pkValue,String...Columns) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName, PrimaryKeyValue.fromLong(pkValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		criteria.addColumnsToGet(Columns);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		return row;
//	}
//
//	/**
//	 * 单行Get操作（双主键表）
//	 * 
//	 * @param otsTableName
//	 * @param pkName1
//	 * @param pkValue1
//	 * @param pkName2
//	 * @param pkValue2
//	 * @return
//	 */
//	public Row getRowWithTwoPK(String otsTableName, String pkName1, String pkValue1, String pkName2, String pkValue2) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		return row;
//	}
//
//	/**
//	 * 两个主键范围查询
//	 * 
//	 * @param otsTableName
//	 *            查询的表名
//	 * @param pk1Name
//	 *            第一个主键名
//	 * @param pk2Name
//	 *            第二个主键名
//	 * @param startPk1Value
//	 *            第一个主键开始值
//	 * @param endPk1Value
//	 *            第一个主键结束值
//	 * @param startPk2Value
//	 *            第二个主键开始值
//	 * @param endPk2Value
//	 *            第二个主键结束值
//	 * @return
//	 */
//	public List<Row> getRangeRowByDoublePK(String otsTableName, String pk1Name, String pk2Name, String startPk1Value, String endPk1Value,
//			String startPk2Value, String endPk2Value) {
//		RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(otsTableName);
//		// 设置起始主键
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pk1Name, startPk1Value == null ? PrimaryKeyValue.INF_MIN : PrimaryKeyValue.fromString(startPk1Value));
//		primaryKeyBuilder.addPrimaryKeyColumn(pk2Name, startPk2Value == null ? PrimaryKeyValue.INF_MIN : PrimaryKeyValue.fromString(startPk2Value));
//		rangeRowQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyBuilder.build());
//
//		// 设置结束主键
//		primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pk1Name, endPk1Value == null ? PrimaryKeyValue.INF_MAX : PrimaryKeyValue.fromString(endPk1Value));
//		primaryKeyBuilder.addPrimaryKeyColumn(pk2Name, endPk2Value == null ? PrimaryKeyValue.INF_MAX : PrimaryKeyValue.fromString(endPk2Value));
//		rangeRowQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyBuilder.build());
//
//		// 设置读取最新版本
//		rangeRowQueryCriteria.setMaxVersions(1);
//
//		// 默认读取所有的属性列
//		GetRangeResponse getRangeResponse = getSyncClient().getRange(new GetRangeRequest(rangeRowQueryCriteria));
//		List<Row> rows = getRangeResponse.getRows();
//		return rows;
//	}
//
//	/**
//	 * 单行Get操作（三主键表）
//	 * 
//	 * @param otsTableName
//	 *            表名
//	 *            主键列名
//	 *            主键值（仅支持单主键，且类型为String）
//	 * @return 行
//	 */
//	public Row getRowWithThreePK(String otsTableName, String pkName1, String pkValue1, String pkName2, String pkValue2, String pkName3,
//			String pkValue3) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName3, PrimaryKeyValue.fromString(pkValue3));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(otsTableName, primaryKey);
//		criteria.setMaxVersions(1);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		return row;
//	}
//
//	/**
//	 *生成三主键表主键
//	 * 
//	 * @param otsTableName
//	 *            表名
//	 *            主键列名
//	 *            主键值（仅支持单主键，且类型为String）
//	 * @return 行
//	 */
//	public PrimaryKey getPrimaryKeyWithThreePK(String otsTableName, String pkName1, String pkValue1, String pkName2, String pkValue2,
//			String pkName3, String pkValue3) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName1, PrimaryKeyValue.fromString(pkValue1));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName2, PrimaryKeyValue.fromString(pkValue2));
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName3, PrimaryKeyValue.fromString(pkValue3));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		return primaryKey;
//	}
//
//	/**
//	 * 通过Set批量查询(双主键)
//	 * 
//	 * @param queryList
//	 *            需要查询的列
//	 */
//	public List<RowResult> batchQueryBySetWithTwoPK(String tableName, Set<String> queryList, String pkName1, String pkName2, String pkValue2)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	public List<RowResult> batchQueryBySetWithTwoPKWithCols(String tableName, Set<String> queryList, String pkName1, String pkName2,
//																   String pkValue2,String... columns)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			multiRowQueryCriteria.addColumnsToGet(columns);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(generatePrimaryKeyWithTwoColumns(pkName1, pkValue, pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//					multiRowQueryCriteria.addColumnsToGet(columns);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	public <T> List<RowResult> batchQueryListTwoPK(String tableName, List<T> queryList, String pkName1, String pkName2, String pkValue2)
//			throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (T pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(generatePrimaryKeyWithTwoColumns(pkName1, pkValue.toString(), pkName2, pkValue2));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 通过Set批量查询(三主键)
//	 * 
//	 * @param queryList
//	 *            需要查询的列
//	 */
//	public List<RowResult> batchQueryBySetWithThreePK(String tableName, Set<String> queryList, String pkName1, String pkName2, String pkValue2,
//			String pkName3, String pkValue3) throws Exception {
//		List<RowResult> result = new ArrayList<>();
//		try {
//			int i = 0;
//			MultiRowQueryCriteria multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//			BatchGetRowRequest batchGetRowRequest = new BatchGetRowRequest();
//			for (String pkValue : queryList) {
//				i++;
//				multiRowQueryCriteria.addRow(generatePrimaryKeyWithThreeColumnsString(pkName1, pkValue, pkName2, pkValue2, pkName3, pkValue3));
//				if (i >= 99) {
//					// 添加条件
//					multiRowQueryCriteria.setMaxVersions(1);
//					batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//					BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//					result.addAll(batchGetRowResponse.getSucceedRows());
//					i = 0;
//					multiRowQueryCriteria = new MultiRowQueryCriteria(tableName);
//				}
//			}
//			if (i > 0) {
//				// 添加条件
//				multiRowQueryCriteria.setMaxVersions(1);
//				batchGetRowRequest.addMultiRowQueryCriteria(multiRowQueryCriteria);
//				BatchGetRowResponse batchGetRowResponse = getSyncClient().batchGetRow(batchGetRowRequest);
//				result.addAll(batchGetRowResponse.getSucceedRows());
//			}
//		} catch (Exception e) {
//			throw e;
//		}
//		return result;
//	}
//
//	/**
//	 * 双主键表范围查询(单主键固定)
//	 * 
//	 * @param tableName
//	 * @return
//	 */
//	public List<Row> getRangeWithTwoPK(String tableName, String fixPKName, String fixPKValue, String flexiblePKName) {
//		RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(tableName);
//		rangeRowQueryCriteria
//				.setInclusiveStartPrimaryKey(generatePrimaryKeyWithTwoColumns(fixPKName, fixPKValue, flexiblePKName, SymbolConstant.ZERO));
//		rangeRowQueryCriteria
//				.setExclusiveEndPrimaryKey(generatePrimaryKeyWithTwoColumns(fixPKName, fixPKValue, flexiblePKName, SymbolConstant.WAVE_STRING));
//		rangeRowQueryCriteria.setMaxVersions(1);
//		List<Row> resultList = new ArrayList<Row>();
//		while (true) {
//			GetRangeResponse getRangeResponse = getSyncClient().getRange(new GetRangeRequest(rangeRowQueryCriteria));
//			resultList.addAll(getRangeResponse.getRows());
//			if (getRangeResponse.getNextStartPrimaryKey() != null) {
//				rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
//			} else {
//				break;
//			}
//		}
//		return resultList;
//	}
//	
//	/**
//	 * 范围查询
//	 * 
//	 * @param tableName
//	 * @param startPk
//	 * @param endPk
//	 * @return
//	 */
//	public List<Row> getRange(String tableName, PrimaryKey  startPk, PrimaryKey endPk) {
//		RangeRowQueryCriteria rangeRowQueryCriteria = new RangeRowQueryCriteria(tableName);
//		rangeRowQueryCriteria
//				.setInclusiveStartPrimaryKey(startPk);
//		rangeRowQueryCriteria
//				.setExclusiveEndPrimaryKey(endPk);
//		rangeRowQueryCriteria.setMaxVersions(1);
//		List<Row> resultList = new ArrayList<Row>();
//		while (true) {
//			GetRangeResponse getRangeResponse = getSyncClient().getRange(new GetRangeRequest(rangeRowQueryCriteria));
//			resultList.addAll(getRangeResponse.getRows());
//			if (getRangeResponse.getNextStartPrimaryKey() != null) {
//				rangeRowQueryCriteria.setInclusiveStartPrimaryKey(getRangeResponse.getNextStartPrimaryKey());
//			} else {
//				break;
//			}
//		}
//		return resultList;
//	}
//
//	/**
//	 * 通用方法，获取行中的某个属性列的值，以字符串形式返回。不存在时返回null
//	 * 
//	 * @param row
//	 *            行
//	 * @param columnName
//	 *            属性列名
//	 * @return 值，字符串类型。不存在时返回null
//	 */
//	public String getValue(Row row, String columnName) {
//		if (row == null) {
//			return null;
//		} else if (!row.contains(columnName)) {
//			return null;
//		}
//		ColumnType columnType = row.getLatestColumn(columnName).getValue().getType();
//		if (ColumnType.BOOLEAN.equals(columnType)) {
//			return String.valueOf(row.getLatestColumn(columnName).getValue().asBoolean());
//		} else if (ColumnType.STRING.equals(columnType)) {
//			return row.getLatestColumn(columnName).getValue().asString();
//		} else if (ColumnType.DOUBLE.equals(columnType)) {
//			return String.valueOf(row.getLatestColumn(columnName).getValue().asDouble());
//		} else if (ColumnType.INTEGER.equals(columnType)) {
//			return String.valueOf(row.getLatestColumn(columnName).getValue().asLong());
//		} else if (ColumnType.BINARY.equals(columnType)) {
//			return String.valueOf(row.getLatestColumn(columnName).getValue().asBinary());
//		}
//		return null;
//	}
//
//	public Long getLongValue(Row row, String columnName) {
//		if (row == null) {
//			return null;
//		} else if (!row.contains(columnName)) {
//			return null;
//		}
//		return row.getLatestColumn(columnName).getValue().asLong();
//	}
//	
//	/**
//	 * 通用方法，获取行中的某个属性列的值，以Long。不存在或异常时返回null
//	 * 
//	 * @param row
//	 *            行
//	 * @param columnName
//	 *            属性列名
//	 * @return 值，Long类型。不存在时返回null
//	 */
//	public Integer getIntegerValue(Row row, String columnName) {
//		if (row == null) {
//			return null;
//		} else if (!row.contains(columnName)) {
//			return null;
//		}
//		try {
//			Long result=row.getLatestColumn(columnName).getValue().asLong();
//			if (result>Integer.MAX_VALUE) {
//				throw new Exception();
//			}else {
//				return result.intValue();
//			}
//		} catch (Exception e) {
//			log.warn("获取属性列为integer异常,col:[{}]",columnName);
//			return null;
//		}
//	}
//
//	/**
//	 * 通用方法，获取行中的某个属性列的值，以字符串形式返回。不存在时返回默认值
//	 * 
//	 * @param row
//	 *            行
//	 * @param columnName
//	 *            属性列名
//	 * @param defaultValue
//	 *            默认值（不存在时返回）
//	 * @return 值，字符串类型。不存在时返回默认值
//	 */
//	public String getValue(Row row, String columnName, String defaultValue) {
//		String result = getValue(row, columnName);
//		if (result == null) {
//			return defaultValue;
//		}
//		return result;
//
//	}
//
//	/**
//	 * 更新单行
//	 * 
//	 * @param tableName
//	 *            表名
//	 * @param primaryKey
//	 *            主键对象
//	 * @param columnList
//	 *            column列表
//	 */
//	public void updateRow(String tableName, PrimaryKey primaryKey, List<Column> columnList) {
//		RowUpdateChange rowUpdateChange = new RowUpdateChange(tableName, primaryKey);
//		rowUpdateChange.put(columnList);
//		getSyncClient().updateRow(new UpdateRowRequest(rowUpdateChange));
//	}
//	
//	/**
//	 * 删除某一列
//	 * @param otsTableName
//	 * @param pkName
//	 * @param pkValue
//	 * @param columns
//	 */
//	public void deleteColumnsByOnePK(SyncClient c,String otsTableName, String pkName, String pkValue,String...columns) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(pkName, PrimaryKeyValue.fromString(pkValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		RowUpdateChange rowUpdateChange = new RowUpdateChange(otsTableName, primaryKey);
//		for (String col : columns) {
//			rowUpdateChange.deleteColumns(col);
//		}
//		c.updateRow(new UpdateRowRequest(rowUpdateChange));
//	}
//
//	/**
//	 * 批量更新行
//	 * 
//	 * @param tableName
//	 * @param rowList
//	 */
//	public void batchUpdateRow(String tableName, List<Row> rowList) {
//		BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//		int i = 0;
//		for (Row row : rowList) {
//			i++;
//			RowUpdateChange rowUpdateChange = new RowUpdateChange(tableName);
//			rowUpdateChange.setPrimaryKey(row.getPrimaryKey());
//			rowUpdateChange.put(Arrays.asList(row.getColumns()));
//			batchWriteRowRequest.addRowChange(rowUpdateChange);
//			if (i >= 199) {
//				getSyncClient().batchWriteRow(batchWriteRowRequest);
//				i = 0;
//				batchWriteRowRequest = new BatchWriteRowRequest();
//			}
//		}
//		if (i > 0) {
//			getSyncClient().batchWriteRow(batchWriteRowRequest);
//		}
//	}
//	
//	
//	//---------------------------------------------------------------------------------------------------------------
//	/**
//	 * 索引API
//	 */
//	public RangeQuery longRangeQuery(String fieldName,Long start,Long end) {
//		RangeQuery rangeQuery = new RangeQuery();
//		rangeQuery.setFieldName(fieldName);
//		rangeQuery.greaterThanOrEqual(ColumnValue.fromLong(start));
//		rangeQuery.lessThan(ColumnValue.fromLong(end));
//		return rangeQuery;
//	}
//	public static RangeQuery longRangeQueryWithEqual(String fieldName,Long start,Long end) {
//		RangeQuery rangeQuery = new RangeQuery();
//		rangeQuery.setFieldName(fieldName);
//		rangeQuery.greaterThanOrEqual(ColumnValue.fromLong(start));
//		rangeQuery.lessThanOrEqual(ColumnValue.fromLong(end));
//		return rangeQuery;
//	}
//
//
//	public RangeQuery longCompareQuery(String fieldName, RangeQueryType rangeQueryType, Long value) {
//		RangeQuery rangeQuery = new RangeQuery();
//		rangeQuery.setFieldName(fieldName);
//		switch (rangeQueryType) {
//		case GREATER_THAN:
//			rangeQuery.greaterThan(ColumnValue.fromLong(value));
//			break;
//		case GREATER_THAN_OREQUAL:
//			rangeQuery.greaterThanOrEqual(ColumnValue.fromLong(value));
//			break;
//		case LESS_THAN:
//			rangeQuery.lessThan(ColumnValue.fromLong(value));
//			break;
//		case LESS_THAN_OREQUAL:
//			rangeQuery.lessThanOrEqual(ColumnValue.fromLong(value));
//			break;
//		default:
//			throw new RuntimeException("不支持的操作!->" + rangeQueryType);
//		}
//		return rangeQuery;
//	}
//	
//	/**
//	 * 索引API
//	 */
//	public BoolQuery stringShouldMatchQuery(String fieldName,Set<String> shouldConditions,Integer minimumShouldMatch) {
//		BoolQuery shuouldQuery = new BoolQuery();
//		List<Query> shouldQueryCondition=shouldConditions.stream()
//				.map(shouldCondition->stringMatchQuery(fieldName,shouldCondition,QueryOperator.AND))
//				.collect(Collectors.toList());
//		shuouldQuery.setShouldQueries(shouldQueryCondition);
//		shuouldQuery.setMinimumShouldMatch(minimumShouldMatch);
//		return shuouldQuery;
//	}
//
//	public BoolQuery stringShouldMatchQuery(String fieldName, List<String> shouldConditions, Integer minimumShouldMatch) {
//		BoolQuery shuouldQuery = new BoolQuery();
//		List<Query> shouldQueryCondition=shouldConditions.stream()
//				.map(shouldCondition->stringMatchQuery(fieldName,shouldCondition,QueryOperator.AND))
//				.collect(Collectors.toList());
//		shuouldQuery.setShouldQueries(shouldQueryCondition);
//		shuouldQuery.setMinimumShouldMatch(minimumShouldMatch);
//		return shuouldQuery;
//	}
//
//	
//	public MatchQuery stringMatchQuery(String fieldName,String value,QueryOperator queryOperator) {
//		//设置查询类型为MatchQuery。
//	    MatchQuery matchQuery = new MatchQuery();
//		//设置要匹配的列。
//	    matchQuery.setFieldName(fieldName);
//	    matchQuery.setOperator(queryOperator);
//		//设置要匹配的值。
//	    matchQuery.setText(value);
//		return matchQuery;
//	}
//
//	public MatchQuery stringMatchQueryWithMinim(String fieldName,String value,QueryOperator queryOperator,Integer minimumShouldMatch) {
//		//设置查询类型为MatchQuery。
//		MatchQuery matchQuery = new MatchQuery();
//		//设置要匹配的列。
//		matchQuery.setFieldName(fieldName);
//		matchQuery.setOperator(queryOperator);
//		//设置要匹配的值。
//		matchQuery.setText(value);
//		matchQuery.setMinimumShouldMatch(minimumShouldMatch);
//		return matchQuery;
//	}
//
//	public TermsQuery stringTermsQuery(String fieldName,List<String> terms) {
//		//设置查询类型为TermsQuery。
//		TermsQuery termsQuery = new TermsQuery();
//		//设置要匹配的字段。
//		termsQuery.setFieldName(fieldName);
//		terms.stream().forEach(x->termsQuery.addTerm(ColumnValue.fromString(x)));
//		return termsQuery;
//	}
//	public TermsQuery stringTermsQuery(String fieldName,Set<String> terms) {
//		//设置查询类型为TermsQuery。
//	    TermsQuery termsQuery = new TermsQuery();
//		//设置要匹配的字段。
//	    termsQuery.setFieldName(fieldName);
//	    terms.stream().forEach(x->termsQuery.addTerm(ColumnValue.fromString(x)));
//		return termsQuery;
//	}
//	public TermsQuery stringTermsQuery(String fieldName,String ... terms) {
//		//设置查询类型为TermsQuery。
//		TermsQuery termsQuery = new TermsQuery();
//		//设置要匹配的字段。
//		termsQuery.setFieldName(fieldName);
//		for (String term : terms) {
//			termsQuery.addTerm(ColumnValue.fromString(term));
//		}
//		return termsQuery;
//	}
//
//	public TermsQuery stringTermsQueryList(String fieldName,List<String> terms) {
//		//设置查询类型为TermsQuery。
//		TermsQuery termsQuery = new TermsQuery();
//		//设置要匹配的字段。
//		termsQuery.setFieldName(fieldName);
//		for (String term : terms) {
//			termsQuery.addTerm(ColumnValue.fromString(term));
//		}
//		return termsQuery;
//	}
//
//
//	public TermQuery stringTermQuery(String fieldName,String value) {
//		// 设置查询类型为MatchQuery
//		TermQuery termQuery = new TermQuery();
//		// 设置要匹配的字段
//		termQuery.setFieldName(fieldName);
//		// 设置要匹配的值
//		termQuery.setTerm(ColumnValue.fromString(value));
//		return termQuery;
//	}
//	public TermQuery longTermQuery(String fieldName,long value) {
//		TermQuery termQuery = new TermQuery();
//		// 设置要匹配的字段
//		termQuery.setFieldName(fieldName);
//		// 设置要匹配的
//		termQuery.setTerm(ColumnValue.fromLong(value));
//		return termQuery;
//	}
//	public TermQuery booleanTermQuery(String fieldName,boolean value) {
//		// 设置查询类型为MatchQuery
//		TermQuery termQuery = new TermQuery();
//		// 设置要匹配的字段
//		termQuery.setFieldName(fieldName);
//		// 设置要匹配的值
//		termQuery.setTerm(ColumnValue.fromBoolean(value));
//		return termQuery;
//	}
//	
//	/**
//	 * 查询表中Col_Keyword这一列的值能够匹配"hangzhou"的数据，返回匹配到的总行数和一些匹配成功的行。
//	 * 
//	 */
//	public MatchQuery stringMatchQuery(String fieldName,String value) {
//		// 设置查询类型为MatchQuery
//		MatchQuery matchQuery = new MatchQuery();
//		// 设置要匹配的字段
//		matchQuery.setFieldName(fieldName);
//		// 设置要匹配的值
//		matchQuery.setText(value);
//		return matchQuery;
//	}
//
//
//
//	public boolean rowExists(String tableName, String primaryKeyString,String primaryKeyValue) {
//		PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//		primaryKeyBuilder.addPrimaryKeyColumn(primaryKeyString, PrimaryKeyValue.fromString(primaryKeyValue));
//		PrimaryKey primaryKey = primaryKeyBuilder.build();
//		SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(tableName, primaryKey);
//		criteria.setMaxVersions(1);
//		GetRowResponse getRowResponse = getSyncClient().getRow(new GetRowRequest(criteria));
//		Row row = getRowResponse.getRow();
//		if (row != null) {
//			return true;
//		}
//		return false;
//	}
//
//	public void batchDeleteRowAsync(String tableName, List<DeleteRow> rowList) {
//		BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//		int i = 0;
//		for (DeleteRow row : rowList) {
//			i++;
//			RowUpdateChange rowUpdateChange = new RowUpdateChange(tableName);
//			rowUpdateChange.setPrimaryKey(row.getPrimaryKey());
//			rowUpdateChange.deleteColumns(row.getColumns());
//			batchWriteRowRequest.addRowChange(rowUpdateChange);
//			if (i >= 199) {
//				getAsyncClient().batchWriteRow(batchWriteRowRequest,
//						new TableStoreCallback<BatchWriteRowRequest, BatchWriteRowResponse>() {
//							@Override
//							public void onCompleted(BatchWriteRowRequest batchWriteRowRequest, BatchWriteRowResponse batchWriteRowResponse) {
//
//							}
//							@Override
//							public void onFailed(BatchWriteRowRequest batchWriteRowRequest, Exception e) {
//
//							}
//						});
//				i = 0;
//				batchWriteRowRequest = new BatchWriteRowRequest();
//			}
//		}
//		if (i > 0) {
//			getAsyncClient().batchWriteRow(batchWriteRowRequest, new TableStoreCallback<BatchWriteRowRequest, BatchWriteRowResponse>() {
//				@Override
//				public void onCompleted(BatchWriteRowRequest batchWriteRowRequest, BatchWriteRowResponse batchWriteRowResponse) {
//
//				}
//				@Override
//				public void onFailed(BatchWriteRowRequest batchWriteRowRequest, Exception e) {
//
//				}
//			});
//		}
//	}
//
//
//	public void batchIncrementRowAsync(String tableName, List<IncrementRow> rowList) {
//		BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//		int i = 0;
//		for (IncrementRow row : rowList) {
//			i++;
//			RowUpdateChange rowUpdateChange = new RowUpdateChange(tableName);
//			rowUpdateChange.setPrimaryKey(row.getPrimaryKey());
//			rowUpdateChange.increment(row.getColumn());
//			batchWriteRowRequest.addRowChange(rowUpdateChange);
//			if (i >= 199) {
//				getAsyncClient().batchWriteRow(batchWriteRowRequest,
//						new TableStoreCallback<BatchWriteRowRequest, BatchWriteRowResponse>() {
//							@Override
//							public void onCompleted(BatchWriteRowRequest batchWriteRowRequest, BatchWriteRowResponse batchWriteRowResponse) {
//
//							}
//							@Override
//							public void onFailed(BatchWriteRowRequest batchWriteRowRequest, Exception e) {
//
//							}
//						});
//				i = 0;
//				batchWriteRowRequest = new BatchWriteRowRequest();
//			}
//		}
//		if (i > 0) {
//			getAsyncClient().batchWriteRow(batchWriteRowRequest, new TableStoreCallback<BatchWriteRowRequest, BatchWriteRowResponse>() {
//				@Override
//				public void onCompleted(BatchWriteRowRequest batchWriteRowRequest, BatchWriteRowResponse batchWriteRowResponse) {
//
//				}
//				@Override
//				public void onFailed(BatchWriteRowRequest batchWriteRowRequest, Exception e) {
//
//				}
//			});
//		}
//	}
//
//
//	public void doubleRangeBetweenQuery(List<Query> mustQueryList,
//											   Double aimValue,String startField,String endField) {
//		RangeQuery rangeQueryStart = new RangeQuery();
//		rangeQueryStart.setFieldName(startField);
//		rangeQueryStart.lessThanOrEqual(ColumnValue.fromDouble(aimValue));
//		mustQueryList.add(rangeQueryStart);
//
//
//		RangeQuery rangeQueryEnd = new RangeQuery();
//		rangeQueryEnd.setFieldName(endField);
//		rangeQueryEnd.greaterThan(ColumnValue.fromDouble(aimValue));
//		mustQueryList.add(rangeQueryEnd);
//	}
//
//
//}