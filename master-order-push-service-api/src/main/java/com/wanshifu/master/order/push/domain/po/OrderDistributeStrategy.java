package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * 订单调度策略PO类
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "order_distribute_strategy")
public class OrderDistributeStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Long businessLineId;

    /**
     * 策略名称
     */
    @Column(name = "strategy_name")
    private String strategyName;

    /**
     * 策略描述
     */
    @Column(name = "strategy_desc")
    private String strategyDesc;


    /**
     * 订单来源
     */
    @Column(name = "order_from")
    private String orderFrom;


    /**
     * 调度类型
     */
    @Column(name = "distribute_type")
    private String distributeType;

    /**
     * 适用类目id
     */
    @Column(name = "category_ids")
    private String categoryIds;

    /**
     * 开放城市模式
     */
    @Column(name = "open_city_mode")
    private String openCityMode;

    /**
     * 开放城市
     */
    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 调度策略
     */
    @Column(name = "distribute_strategy")
    private String distributeStrategy;


    /**
     * 调度策略表达式
     */
    @Column(name = "distribute_strategy_expression")
    private String distributeStrategyExpression;


    /**
     * 调度策略
     */
    @Column(name = "compensate_distribute_strategy")
    private String compensateDistributeStrategy;

    /**
     * 策略状态
     */
    @Column(name = "strategy_status")
    private Integer strategyStatus;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建人账号id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 修改人账号id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
