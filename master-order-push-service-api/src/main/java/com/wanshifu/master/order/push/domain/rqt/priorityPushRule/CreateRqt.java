package com.wanshifu.master.order.push.domain.rqt.priorityPushRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.master.order.push.domain.vo.priorityPushRule.PushGroupsRuleItem;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

@Data
public class CreateRqt {

    private String ruleName;

    private Integer businessLineId;

//    private String orderFrom;

//    private String appointType;

    private String ruleDesc;

    private String categoryIds;

    private String cityIds;

    private List<PushGroupsRuleItem> ruleList;

    private Long createAccountId;


}
