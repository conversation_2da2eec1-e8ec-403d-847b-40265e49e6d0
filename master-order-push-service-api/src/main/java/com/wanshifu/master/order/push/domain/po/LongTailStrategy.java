package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 长尾策略表
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "long_tail_strategy_base")
public class LongTailStrategy {

    /**
     * 策略id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "long_tail_strategy_id")
    private Long longTailStrategyId;


    /**
     * 推送类型：
     * 附近更多：nearby_more,
     * 红包订单：bonus_order,
     * 区县外订单：out_district
     */
    @Column(name = "push_type")
    private String pushType;

    /**
     * 策略名称
     */
    @Column(name = "long_tail_strategy_name")
    private String longTailStrategyName;

    /**
     * 策略描述
     */
    @Column(name = "long_tail_strategy_desc")
    private String longTailStrategyDesc;


    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 范围初筛
     */
    @Column(name = "range_select")
    private String rangeSelect;

    /**
     * 状态初筛
     */
    @Column(name = "status_select")
    private String statusSelect;

    /**
     * 过滤规则
     */
    @Column(name = "filter_rule")
    private String filterRule;

    /**
     * 过滤规则表达式
     */
    @Column(name = "filter_rule_expression")
    private String filterRuleExpression;

    /**
     * 推送规则
     */
    @Column(name = "push_rule")
    private String pushRule;

    /**
     * 规则配置JSON
     */
    @Column(name = "strategy_json")
    private String strategyJson;

    /**
     * 规则表达式
     */
    @Column(name = "strategy_expression")
    private String strategyExpression;


    /**
     * 指定人群表达式
     */
    @Column(name = "appoint_group_expression")
    private String appointGroupExpression;

    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;

    /**
     * 可用状态
     */
    @Column(name = "is_active")
    private Integer isActive;

    /**
     * 是否删除，1：删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;



}