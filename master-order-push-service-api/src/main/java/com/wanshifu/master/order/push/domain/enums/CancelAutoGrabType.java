package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum CancelAutoGrabType {

    /**
     * 师傅手动取消
     */
    MASTER_MANUAL_CANCEL("master_manual_cancel","师傅手动放弃"),

    /**
     * 系统自动取消
     */
    SYSTEM_AUTO_CANCEL("system_auto_cancel","系统自动取消"),


    /**
     * 未指派
     */
    MASTER_MANUAL_CONFIRM_ERROR("master_manual_confirm_error","师傅手动确认失败"),


    /**
     * 人工调度
     */
    BACKEND_APPOINT("backend_appoint","人工调度");






    private final String code;

    private final String desc;


    private static final Map<String, CancelAutoGrabType> valueMapping = new HashMap<>((int) (CancelAutoGrabType.values().length / 0.75));


    static {
        for (CancelAutoGrabType instance : CancelAutoGrabType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static CancelAutoGrabType asCode(String code) {
        return valueMapping.get(code);
    }
}
