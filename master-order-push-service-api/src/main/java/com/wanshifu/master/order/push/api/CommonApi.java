package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.MasterQuota;
import com.wanshifu.master.order.push.domain.po.MasterQuotaValue;
import com.wanshifu.master.order.push.domain.po.ScoreItem;
import com.wanshifu.master.order.push.domain.po.ScoreItemValue;
import com.wanshifu.master.order.push.domain.rqt.common.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;


@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "common", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface CommonApi {


    /**
     * 查询师傅指标
     *
     * @return
     */
    @PostMapping("/masterQuota")
    SimplePageInfo<MasterQuota> masterQuota(@RequestBody @Valid MasterQuotaRqt rqt);

    /**
     * 查询匹配项-推单
     *
     * @return
     */
    @PostMapping("/masterItemList")
    List<ScoreItem> masterItemList(@RequestBody @Valid MasterItemRqt rqt);


    @PostMapping("/getMasterQuotaValue")
    List<MasterQuotaValue> getMasterQuotaValue(@RequestBody @Valid GetMasterQuotaValueRqt rqt);


    @PostMapping("/getScoreItemValue")
    List<ScoreItemValue> getScoreItemValue(@RequestBody @Valid GetScoreItemValueRqt rqt);


    @PostMapping("/getScoreItemByCodes")
    List<ScoreItem> getScoreItemByCodes(@RequestBody @Valid GetScoreItemByCodesRqt rqt);

    @PostMapping("/getMasterQuotaByCodes")
    List<MasterQuota> getMasterQuotaByCodes(@RequestBody @Valid GetMasterQuotaByCodesRqt rqt);


}
