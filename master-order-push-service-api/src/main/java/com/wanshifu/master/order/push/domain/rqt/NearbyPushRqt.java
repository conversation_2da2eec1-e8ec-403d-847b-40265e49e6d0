package com.wanshifu.master.order.push.domain.rqt;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 附近推单
 * <AUTHOR>
 */
@Data
public class NearbyPushRqt{

    /**
     * 当前推送版本号
     */
    String orderVersion;

    /**
     * 最优报价数（附近推送规则配置）
     */
    Integer bestOfferNum;


    /**
     * 最大报价人数
     */
    private Integer maxOfferNum;

    /**
     * 附近距离
     */
    private BigDecimal nearbyDistance;

    /**
     * 订单数据
     */
    private OrderDetailData orderDetailData;

}
