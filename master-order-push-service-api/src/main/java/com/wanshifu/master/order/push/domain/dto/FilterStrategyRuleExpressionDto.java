package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 描述 :  召回策略表达式dto.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-08 16:30
 */
@Data
@AllArgsConstructor
public class FilterStrategyRuleExpressionDto {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;


    /**
     * 召回规则表达式
     */
    private String filterRuleExpression;

    /**
     * 召回规则表达式参数
     */
    private String filterRuleParams;

}