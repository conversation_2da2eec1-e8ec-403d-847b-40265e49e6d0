package com.wanshifu.master.order.push.domain.resp.permisssionSet;

import lombok.Data;

import java.util.List;

@Data
public class GetPermissionListResp {

    private String name;

    private String path;

    private List<Button> buttonList;

    private List<Tab> tabList;

    private List<GetPermissionListResp> routes;

    @Data
    public static final class Button{

        private String code;

        private String name;

        public Button(){

        }

        public Button(String code,String name){
            this.code = code;
            this.name = name;
        }
    }


    @Data
    public static final class Tab{

        private String code;

        private String name;

        public Tab(){

        }

        public Tab(String code,String name){
            this.code = code;
            this.name = name;
        }
    }





}
