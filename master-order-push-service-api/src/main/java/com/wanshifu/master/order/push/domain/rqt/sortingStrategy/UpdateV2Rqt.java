package com.wanshifu.master.order.push.domain.rqt.sortingStrategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改精排策略Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateV2Rqt extends CreateV2Rqt {

    /**
     * 策略id
     */
    @NotNull
    private Long strategyId;

    private Long updateAccountId;
}