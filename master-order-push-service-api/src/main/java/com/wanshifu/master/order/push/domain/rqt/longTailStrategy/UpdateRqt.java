package com.wanshifu.master.order.push.domain.rqt.longTailStrategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  修改策略列表Rqt.
 *
 * <AUTHOR> -L
 * @date : 2023-10-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateRqt extends CreateRqt {

    /**
     * 策略id
     */
    @NotNull
    private Long longTailStrategyId;

    private Long updateAccountId;
}