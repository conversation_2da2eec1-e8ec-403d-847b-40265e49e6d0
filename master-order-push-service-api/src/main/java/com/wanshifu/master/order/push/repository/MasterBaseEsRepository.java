package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.repository.AbstractElasticSearchRepository;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Repository
public class MasterBaseEsRepository extends AbstractElasticSearchRepository<MasterBaseSearch> {

    @Value("${selectMaster.pageSize:200}")
    private Integer selectMasterPageSize;

    public MasterBaseSearch searchByMasterId(Long masterId){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("masterId",masterId));
        EsResponse<MasterBaseSearch> esResponse = this.search(boolQueryBuilder);
        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList().get(0);
        }
        return null;
    }


    public List<MasterBaseSearch> searchByIdCardNumberAndMasterSourceType(String idCardNumber,String masterSourceType){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("idCardNumber",idCardNumber));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType",masterSourceType));
        EsResponse<MasterBaseSearch> esResponse = this.search(boolQueryBuilder);
        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList();
        }
        return null;
    }


    public List<MasterBaseSearch> searchCooperationBusinessEndMaster(Set<Long> masterSet){

        int pageNum = 1;
        int pageSize = selectMasterPageSize;
        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();

        List<List<Long>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterSet,selectMasterPageSize);
        for(List<Long> masterList : masterBatchList){
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", masterList));
            boolQueryBuilder.must(QueryBuilders.termQuery("isRestrictPushOrder", 1));
            EsResponse<MasterBaseSearch> esResponse = this.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
            }
        }


        return masterBaseSearchList;
    }


}
