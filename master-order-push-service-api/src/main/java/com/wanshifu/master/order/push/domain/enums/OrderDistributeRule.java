package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单分配规则
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OrderDistributeRule {

    SCORING_ORDER("scoring_order", "基于评分由高到低分配"),

    SCORING_ORDER_TOP10_RANDOM("scoring_order_top10_random", "基于评分TOP10随机分配"),

    SCORING_ORDER_TOP50_RANDOM("scoring_order_top50_random", "基于评分TOP50随机分配"),

    SCORING_ORDER_TOP50_LOW_PRICE_PRIORITY("scoring_order_top50_low_price_priority","基于评分TOP50价低者优先分配"),

    LOW_PRICE_PRIORITY("low_price_priority","价低者优先分配"),

    RANDOM("random","随机分配"),

    INTELLIGENT("intelligent","智能分配");


    private final String code;

    private final String desc;



    private static final Map<String, OrderDistributeRule> valueMapping = new HashMap<>((int) (OrderDistributeRule.values().length / 0.75));



    static {
        for (OrderDistributeRule instance : OrderDistributeRule.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static OrderDistributeRule asCode(String code) {
        return valueMapping.get(code);
    }
}
