package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/1 16:21
 */
@Data
@Table(name = "after_technique_verify_master_match_log")
public class AfterTechniqueVerifyMasterMatchLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 订单id
     */
    @Column(name = "order_id")
    private Long orderId;

    /**
     * 订单编号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 订单创建时间
     */
    @Column(name = "order_create_time")
    private Date orderCreateTime;

    /**
     * 师傅id
     */
    @Column(name = "master_id")
    private Long masterId;

    /**
     * 是否匹配成功，1：匹配成功，0：匹配失败
     */
    @Column(name = "is_match_success")
    private Integer isMatchSuccess;

    /**
     * 匹配失败原因
     */
    @Column(name = "match_fail_reason")
    private String matchFailReason;

    /**
     * 是否过滤，1：过滤，0：未过滤
     */
    @Column(name = "is_filter")
    private Integer isFilter;

    /**
     * 过滤原因
     */
    @Column(name = "filter_reason")
    private String filterReason;

    /**
     * 是否调度
     */
    @Column(name = "is_distribute")
    private Integer isDistribute;

    /**
     * 调度规则
     */
    @Column(name = "distribute_rule")
    private String distributeRule;

    /**
     * 是否自动抢单成功，1：成功，0：失败
     */
    @Column(name = "is_auto_grab_success")
    private Integer isAutoGrabSuccess;

    /**
     * 自动抢单失败原因
     */
    @Column(name = "auto_grab_fail_reason")
    private String autoGrabFailReason;

    /**
     * 版本号：标识同一次推单
     */
    @Column(name = "order_version")
    private String orderVersion;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
