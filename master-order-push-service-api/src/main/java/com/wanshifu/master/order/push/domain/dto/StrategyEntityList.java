package com.wanshifu.master.order.push.domain.dto;


import com.wanshifu.master.order.push.domain.common.BaseSelect;
import com.wanshifu.master.order.push.domain.common.PushFilterList;
import com.wanshifu.master.order.push.domain.common.PushScorerList;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.rqt.pushRule.CreateRqt;
import lombok.Data;

/**
 * 策略实体类
 * <AUTHOR>
 */
@Data
public class StrategyEntityList {

    private BaseSelect baseSelect;

    private PushFilterList pushFilterList;

    private PushScorerList pushScorerList;

    private PushConfig pushConfig;

    private PushRuleConfig pushRuleConfig;

    public StrategyEntityList(BaseSelect baseSelect,PushFilterList pushFilterList,PushScorerList pushScorerList,PushConfig pushConfig){
        this.baseSelect = baseSelect;
        this.pushFilterList = pushFilterList;
        this.pushScorerList = pushScorerList;
        this.pushConfig = pushConfig;
    }

    public StrategyEntityList(){

    }

    public StrategyEntityList(BaseSelect baseSelect, PushFilterList pushFilterList, PushScorerList pushScorerList, PushRuleConfig pushRuleConfig){
        this.baseSelect = baseSelect;
        this.pushFilterList = pushFilterList;
        this.pushScorerList = pushScorerList;
        this.pushRuleConfig = pushRuleConfig;
    }
}
