package com.wanshifu.master.order.push.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 描述 :  召回策略表达式dto.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-08 16:30
 */
@Data
public class PriorityPushRuleExpressionDto {

    /**
     * 开启条件表达式
     */
    private String openConditionRuleExpression;

    /**
     * 开启条件表达式参数
     */
    private String openConditionRuleParams;


    /**
     * 召回规则表达式
     */
    private String pushRuleExpression;

    /**
     * 召回规则表达式参数
     */
    private String pushRuleParams;


    public PriorityPushRuleExpressionDto(){

    }


    public PriorityPushRuleExpressionDto(String openConditionRuleExpression,String openConditionRuleParams,String pushRuleExpression,String pushRuleParams){
        this.openConditionRuleExpression = openConditionRuleExpression;

        this.openConditionRuleParams = openConditionRuleParams;

        this.pushRuleExpression = pushRuleExpression;

        this.pushRuleParams = pushRuleParams;

    }

}