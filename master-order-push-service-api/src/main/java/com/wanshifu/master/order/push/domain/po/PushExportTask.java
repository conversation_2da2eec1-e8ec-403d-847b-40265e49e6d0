package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 推单异步导出任务
 * @date 2025/5/26 17:38
 */
@Data
@Table(name = "push_export_task")
public class PushExportTask {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 导出数据类型名称
     */
    @Column(name = "export_data_type_name")
    private String exportDataTypeName;

    /**
     * 导出数据名称
     */
    @Column(name = "export_data_name")
    private String exportDataName;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件地址
     */
    @Column(name = "url")
    private String url;

    /**
     * 导出任务状态，process:处理中，fail:失败，success:成功
     */
    @Column(name = "status")
    private String status;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
