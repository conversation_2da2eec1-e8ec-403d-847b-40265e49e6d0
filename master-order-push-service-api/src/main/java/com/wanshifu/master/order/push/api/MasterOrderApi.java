package com.wanshifu.master.order.push.api;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.domain.feign.DefaultDecoder;
import com.wanshifu.master.order.push.domain.rqt.GrabOrderValidRqt;
import com.wanshifu.master.order.push.domain.rqt.MasterOfferPriceRqt;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025/2/28 15:28
 */
@FeignClient(value = "master-inner-api",
        url = "${wanshifu.master-inner-api.url}",
        path = "/order/operate",
        configuration = {DefaultEncoder.class, DefaultDecoder.class, DefaultErrorDecode.class})
public interface MasterOrderApi {


    @PostMapping("autoOfferPrice")
    JSONObject autoOfferPrice(@RequestBody @Validated MasterOfferPriceRqt masterOfferPriceRqt);


    @PostMapping("autoGrabOrder")
    JSONObject autoGrabOrder(@RequestBody @Validated GrabOrderValidRqt grabOrderValidRqt);
}
