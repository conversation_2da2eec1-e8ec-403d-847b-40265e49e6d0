package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.LongTailStrategyGroup;
import com.wanshifu.master.order.push.domain.rqt.longTailStrategyGroup.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "longTailStrategyGroup",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface LongTailStrategyGroupApi {

    /**
     * 长尾策略组列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<LongTailStrategyGroup> list(@RequestBody @Valid ListRqt rqt);

    /**
     * 创建长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    int create(@RequestBody @Valid CreateRqt rqt);


    /**
     * 修改长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    int update(@RequestBody @Valid UpdateRqt rqt);


    /**
     * 启用/禁用策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/updateStatus")
    int updateStatus(@RequestBody @Valid EnableRqt rqt);


    /**
     * 删除策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/delete")
    int delete(@RequestBody @Valid DeleteRqt rqt);

    /**
     * 策略组详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    LongTailStrategyGroup detail(@RequestBody @Valid DetailRqt rqt);


}
