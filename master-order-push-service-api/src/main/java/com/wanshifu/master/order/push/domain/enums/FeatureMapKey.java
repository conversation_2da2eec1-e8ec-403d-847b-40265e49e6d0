package com.wanshifu.master.order.push.domain.enums;

import com.wanshifu.master.order.push.domain.constant.FieldConstant;

/**
 * <AUTHOR>
 */

public enum FeatureMapKey {
    MASTER{
        @Override
        public String getKey(){
            return FieldConstant.MASTER_ID;
        }
    },AGENT{
        @Override
        public String getKey(){
            return FieldConstant.AGENT_ID;
        }
    };
    public String getKey(){
        return FieldConstant.MASTER_ID;
    }
}
