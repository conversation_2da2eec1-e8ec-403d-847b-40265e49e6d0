package com.wanshifu.master.order.push.domain.rqt;

import com.wanshifu.master.order.push.domain.vo.longTailStrategy.apply.LongTailStrategyTuple;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 长尾单推送
 * <AUTHOR>
 */
@Data
public class LongTailOrderPushRqt {

    /**
     * 最大报价人数
     */
    private Integer maxOfferNum;


    /**
     * 当前推送版本号
     */
    String orderVersion;

    /**
     * 长尾单规则
     */
    private LongTailStrategyTuple longTailStrategyTuple;

    /**
     * 订单数据
     */
    private OrderDetailData orderDetailData;

}
