package com.wanshifu.master.order.push.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  兜底定向推送代理商信息
 * @date 2025/8/7 11:05
 */
@Data
public class AgentAfterDirectAppointPushInfo {

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商类型
     * toc:C端合作商，
     * tobGroup:B端团队师傅
     */
    private String agentType;


    /**
     * 代理商推送类型为push时才有数据，定向推送推单后无人接单重推时间配置
     */
    private Integer agentPushNoHiredRePushTime;

    /**
     * 代理商推送类型为push时才有数据，定向推送推单首次查看后无人接单重推时间配置
     */
    private Integer agentFirstViewNoHiredRePushTime;
}
