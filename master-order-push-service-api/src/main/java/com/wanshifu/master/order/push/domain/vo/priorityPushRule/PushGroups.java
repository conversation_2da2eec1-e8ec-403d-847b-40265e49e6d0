package com.wanshifu.master.order.push.domain.vo.priorityPushRule;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import java.util.List;

@Data
public class PushGroups{

        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;


        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<RuleItem> itemList;


    @Data
    public class RuleItem{

        private String itemType;
        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         *  2023-11-23 + cancel_appoint:取消指派
         */
        @NotEmpty
        @ValueIn("master_group")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;
    }


    }


