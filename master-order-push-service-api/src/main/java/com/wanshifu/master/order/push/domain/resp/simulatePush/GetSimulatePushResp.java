package com.wanshifu.master.order.push.domain.resp.simulatePush;

import com.wanshifu.master.order.push.api.SimulatePushApi;
import com.wanshifu.master.order.push.domain.po.SimulatePush;
import com.wanshifu.master.order.push.domain.po.StrategyCombinationSimulate;
import lombok.Data;

@Data
public class GetSimulatePushResp {

    private SimulatePush simulatePush;

    private StrategyCombinationSimulate strategyCombinationSimulate;

}
