package com.wanshifu.master.order.push.api;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.OrderMatchRoute;
import com.wanshifu.master.order.push.domain.po.OrderMatchRouteTime;
import com.wanshifu.master.order.push.domain.rqt.orderMatchRoute.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "master-order-push-service",
        url = "${wanshifu.master-order-push-service.url}",
        path = "orderMatchRouteTime",
        configuration = {
                com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class,
                com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class}
)
public interface OrderMatchRouteTimeApi {

    /**
     * 长尾策略组列表
     *
     * @param rqt
     * @return
     */
    @PostMapping("/list")
    SimplePageInfo<OrderMatchRouteTime> list(@RequestBody @Valid OrderMatchRouteTimeListRqt rqt);

    /**
     * 创建长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/create")
    Integer create(@RequestBody @Valid CreateOrderMatchRouteTimeRqt rqt);


    /**
     * 修改长尾策略组
     *
     * @param rqt
     * @return
     */
    @PostMapping("/update")
    Integer update(@RequestBody @Valid UpdateOrderMatchRouteTimeRqt rqt);



    /**
     * 策略组详情
     *
     * @param rqt
     * @return
     */
    @PostMapping("/detail")
    OrderMatchRouteTime detail(@RequestBody @Valid OrderMatchRouteTimeDetailRqt rqt);


}
