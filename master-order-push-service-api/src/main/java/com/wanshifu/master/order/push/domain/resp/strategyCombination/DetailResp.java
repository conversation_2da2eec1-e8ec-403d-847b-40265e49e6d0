package com.wanshifu.master.order.push.domain.resp.strategyCombination;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述 :  组合策略详情Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailResp {

    private String orderFlag;

    /**
     * 业务线id
     */
    private Integer businessLineId;

    /**
     * 类目id，多个以逗号拼接
     */
    private String categoryIds;


    /**
     * 城市id，多个以逗号拼接
     */
    private String cityIds;

    /**
     * 省份id，多个以逗号拼接，all:表示全国
     */
    private String provinceIds;

    /**
     * 组合名称
     */
    private String combinationName;

    /**
     * 组合描述
     */
    private String combinationDesc;

    /**
     * 组合状态
     */
    private Integer combinationStatus;


    /**
     * 优先推荐路由
     */
    private PriorityStrategyVo priorityStrategy;

    /**
     * 备用推荐路由
     */
    private AlternateStrategyVo alternateStrategy;

    @Data
    public static class PriorityStrategyVo {


        /**
         * 选择的初筛、召回、精排策略id
         */
        public PriorityStrategyVo.StrategyList strategyList;


        private String pushRuleType;

        /**
         * 推送规则
         */
        private List<PriorityStrategyVo.PushRuleItem> pushRule;

        private Integer pushRuleId;

        private String pushRuleName;


        /**
         * 组合策略
         */
        @Data
        public static class StrategyList {

            /**
             * 初筛策略
             */
            private Long baseSelectStrategyId;
            private String baseSelectStrategyName;

            /**
             * 召回策略
             */
            private Long filterStrategyId;
            private String filterStrategyName;

            /**
             * 精排策略id
             */
            private Long sortingStrategyId;
            private String  sortingStrategyName;
        }


        /**
         * 推送规则
         */
        @Data
        private static class PushRuleItem {

            /**
             * 指派模式 2:报价招标 4:一口价 5:预付款
             */
            private Integer appointType;

            /**
             * 最佳报价数
             */
            private Integer bestOfferNum;

            /**
             * 推送时间间隔
             */
            private Integer delayMinutesBetweenRounds;

            /**
             * 首轮推送人数
             */
            private Integer firstPushMasterNumPerRound;

            /**
             * 非首轮推送人数
             */
            private Integer delayPushMasterNumPerRound;

            /**
             * 首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
             */
            private String firstPushMasterFlag;

            /**
             * 首轮推送师傅人数占比 (0,100]
             */
            private BigDecimal firstPushMasterPercent;

            /**
             * 非首轮推送师傅人群，master_new: 新师傅，master_old： 老师傅,all: 全部师傅
             */
            private String delayPushMasterFlag;

            /**
             * 非首轮推送师傅人数占比 (0,100]
             */
            private BigDecimal delayPushMasterPercent;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class AlternateStrategyVo extends PriorityStrategyVo {

        /**
         * 开启条件
         */
        private AlternateStrategyVo.OpenCondition openCondition;

        @Data
        public static class OpenCondition {
            /**
             * 或且关系
             */
            private String condition;

            /**
             * 开启条件项
             */
            private List<AlternateStrategyVo.OpenConditionItem> itemList;
        }

        /**
         * 开启条件item
         */
        @Data
        public static class OpenConditionItem {

            /**
             * 条件项名称 canPushNumber:可推送人数
             */
            private String itemName;

            /**
             * 符号 <,>,=,>=,<=
             */
            private String term;

            /**
             * 规则项值
             */
            private Integer itemValue;
        }
    }
}