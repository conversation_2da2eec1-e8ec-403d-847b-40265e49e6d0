package com.wanshifu.master.order.push.domain.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 20:01
 */
public enum AgentDistributeRule {

    /**
     * 定向推送
     */
    DIRECT_PUSH("direct_push"),

    /**
     * 直接雇佣
     */
    DIRECT_APPOINT("direct_appoint");



    public final String code;

    AgentDistributeRule(String code) {
        this.code = code;
    }

    private static final Map<String, AgentDistributeRule> valueMapping = new HashMap<>((int) (AgentDistributeRule.values().length / 0.75));

    static {
        for (AgentDistributeRule instance : AgentDistributeRule.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static AgentDistributeRule asValue(Integer value) {
        return valueMapping.get(value);
    }
}
