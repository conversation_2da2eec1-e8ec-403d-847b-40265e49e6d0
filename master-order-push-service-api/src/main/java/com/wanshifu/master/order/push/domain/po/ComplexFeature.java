package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 复杂特征表
 * <AUTHOR>
 */
@Data
@Table(name = "complex_feature")
public class ComplexFeature {

    /**
     * 注解id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "feature_id")
    private Long featureId;

    /**
     * 特征编码
     */
    @Column(name = "feature_code")
    private String featureCode;

    /**
     *
     */
    @Column(name = "feature_name")
    private String featureName;

    /**
     * 特征维度
     */
    @Column(name = "feature_for")
    private String featureFor;


    /**
     * 特征计算表达式
     */
    @Column(name = "calculate_expression")
    private String calculateExpression;


    /**
     * 依赖的特征集合
     */
    @Column(name = "feature_dependency")
    private String featureDependency;


    /**
     * 是否检测依赖指标
     */
    @Column(name = "dependency_check")
    private String dependencyCheck;

    /**
     * 字段类型
     */
    @Column(name = "field_type")
    private String fieldType;

    /**
     * 默认值
     */
    @Column(name = "default_value")
    private String defaultValue;


    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    @Transient
    private boolean needDependencyCheck;

    @Transient
    private Set<String> featureDependencySet;


    @Transient
    private Set<String> orderFeatureRela;


    public void setOrderFeatureRelaSet() {
        this.orderFeatureRela=new HashSet<>();
        if (featureDependency!=null) {
            Arrays.stream(featureDependency.split(",")).forEach(featureName -> {
                if (featureName.startsWith("{")) {
                    featureName=featureName.replace("{", "");
                    featureName=featureName.replace("}", "");
                    this.orderFeatureRela.add(featureName);
                }
            });
        }
    }


    public void setFeatureDependencySet(String featureDependency) {
        this.featureDependencySet=new HashSet<>();
        if (featureDependency!=null) {
            Arrays.stream(featureDependency.split(",")).forEach(featureName -> {
                if (featureName.startsWith("{")) {
                    featureName=featureName.replace("{", "");
                    featureName=featureName.replace("}", "");
                    this.featureDependencySet.add(featureName);
                }else {
                    this.featureDependencySet.add(featureName);
                }
            });
        }
    }

    public boolean needDependencyCheck() {
        return needDependencyCheck;
    }

    public void setNeedDependencyCheck(String dependencyCheck) {
        if ("on".equals(dependencyCheck)) {
            this.needDependencyCheck = true;
        }else {
            this.needDependencyCheck = false;
        }
    }

    /**
     * 特征检查
     * ture:通过 false：不通过
     * @return
     */
    public boolean featureCheck(Set<String> featureSet) {
        boolean reuslt=false;
        try {
            reuslt=featureSet.containsAll(featureDependencySet);
        } catch (Exception e) {
        }
        return reuslt;
    }


}
