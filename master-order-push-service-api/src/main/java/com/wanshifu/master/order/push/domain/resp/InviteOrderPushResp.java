package com.wanshifu.master.order.push.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 描述 :  总包自动邀约推单.
 *
 * <AUTHOR> xin<PERSON><PERSON>@wshifu.com
 * @date : 2023-09-13 10:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteOrderPushResp {
    /**
     * 订单全局id
     */
    private Long globalOrderTraceId;

    /**
     * 邀约推单师傅id
     */
    private Set<Long> masterIds;
}