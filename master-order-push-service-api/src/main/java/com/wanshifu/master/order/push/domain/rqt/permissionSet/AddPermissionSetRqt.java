package com.wanshifu.master.order.push.domain.rqt.permissionSet;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

@Data
public class AddPermissionSetRqt {

    @NotBlank
    private String permissionSetName;

    private String permissionSetDesc;

    private List<PermissionMenu> menuList;

    private Long createAccountId;

    private List<Integer> roleList;

    @Data
    public static final class PermissionMenu{

        private Integer menuId;

        private List<Integer> buttonList;

        private List<Integer> tabList;


    }
}
