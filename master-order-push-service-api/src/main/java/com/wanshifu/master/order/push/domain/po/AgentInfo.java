package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26 18:22
 */
@Data
@ToString
@Table(name = "t_agent_info")
public class AgentInfo {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "agent_id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @Column(name = "agent_name")
    private String agentName;

    /**
     * 所在地城市id
     */
    @Column(name = "city_division_id")
    private Long cityDivisionId;

    /**
     * 所在地三级区域id(逗号隔开)
     */
    @Column(name = "division_id")
    private String divisionId;

    /**
     * 强制推单服务(逗号隔开)
     */
    @Column(name = "serve_ids")
    private String serveIds;

    /**
         * 师傅来源类型
     */
    @Column(name = "master_source_type")
    private String masterSourceType;

    /**
     * 状态{0:正常,1:禁用},默认0
     */
    @Column(name = "use_status")
    private Integer useStatus;

    /**
     * 操作时间
     */
    @Column(name = "operate_time")
    private Date operateTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
