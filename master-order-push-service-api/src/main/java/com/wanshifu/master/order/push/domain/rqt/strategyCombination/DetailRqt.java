package com.wanshifu.master.order.push.domain.rqt.strategyCombination;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 描述 :  组合策略详情Rqt.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class DetailRqt {

    /**
     * 组合策略id
     */
    @NotNull
    private Long combinationId;

    public DetailRqt(){

    }

    public DetailRqt(Long combinationId){
        this.combinationId = combinationId;
    }
}