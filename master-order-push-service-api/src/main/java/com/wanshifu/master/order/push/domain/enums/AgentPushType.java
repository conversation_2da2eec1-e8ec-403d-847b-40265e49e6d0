package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 代理商推送类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AgentPushType {
    /**
     * 直接指派优先
     */
    DIRECT_APPOINT_PRIORITY("directAppointPriority"),
    /**
     * 只有直接指派
     */
    DIRECT_APPOINT("directAppoint"),
    /**
     * 只有定向推送
     */
    PUSH("push");

    private final String type;
}
