
package com.wanshifu.master.order.push.domain.rqt.pushLimitRule;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 描述 :  重推机制列表列表Rqt.
 *
 * <AUTHOR> xinze<PERSON>@wshifu.com
 * @date : 2023-02-01 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRqt extends Pager {


    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 城市id
     */
    private Long cityId;


    /**
     * 创建起始时间
     */
    private Date createStartTime;

    /**
     * 创建起始时间
     */
    private Date createEndTime;

}