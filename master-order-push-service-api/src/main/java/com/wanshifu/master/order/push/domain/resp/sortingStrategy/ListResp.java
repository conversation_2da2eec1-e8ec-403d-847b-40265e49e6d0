package com.wanshifu.master.order.push.domain.resp.sortingStrategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.master.order.push.domain.enums.EnableStatusEnum;
import com.wanshifu.master.order.push.domain.enums.TranslateEnum;
import lombok.Data;

import java.util.Date;

/**
 * 描述 :  精排策略列表Resp.
 *
 * <AUTHOR> <EMAIL>
 * @date : 2023-02-01 15:36
 */
@Data
public class ListResp {

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 匹配项数
     */
    private Integer itemNum;

    /**
     * 策略状态
     */
    @TranslateEnum(enumClass= EnableStatusEnum.class,fieldName = "strategyStatusStr")
    private Integer strategyStatus;

    /**
     * 策略状态中文
     */
    private String strategyStatusStr;

    /**
     * 最后修改人
     */
    private String lastUpdateAccountName ="admin";

    /**
     * 创建时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *修改时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}