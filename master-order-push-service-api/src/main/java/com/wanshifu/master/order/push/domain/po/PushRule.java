package com.wanshifu.master.order.push.domain.po;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;


/**
 * 订单推送规则
 * <AUTHOR>
 */
@Data
@ToString
@Table(name = "push_rule")
public class PushRule {

    /**
     * 规则id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "rule_id")
    private Integer ruleId;

    /**
     * 业务线id
     */
    @Column(name = "business_line_id")
    private Integer businessLineId;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;

    /**
     * 规则描述
     */
    @Column(name = "rule_desc")
    private String ruleDesc;

    /**
     * 规则列表
     */
    @Column(name = "push_rule_list")
    private String pushRuleList;

    /**
     * 是否删除，1：已删除，0：未删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;



    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;

}