package com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建订单调度策略请求实体
 * <AUTHOR>
 */
@Data
public class CreateOrderDistributeStrategyRqt {

    /**
     * 业务线Id
     * 1:企业，2：家庭，3：创新业务，999：家庭新师傅app
     */
    @NotNull
    private Long businessLineId;

    /**
     * 策略名称
     */
    @NotBlank
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 订单标识,all: 不限，site: 企业，enterprise: 总包，family: 家庭
     */
    @NotBlank
    @ValueIn("all,site,enterprise,family")
    private String orderFrom;

    /**
     * 调度类型
     */
    @NotBlank
    @ValueIn("auto_receive,new_master_support,new_model,enterprise_appoint,cooperation_business_master,enterprise_appoint_new_master,full_time_master,enterprise_appoint_full_time_master")
    private String distributeType;


    @NotBlank
    private String categoryIds;

    /**
     * 开放城市模式，all: 全国，city: 按城市开放
     */

    private String openCityMode;

    /**
     * 开放城市id集合
     */
    private String cityIds;

    /**
     * 调度策略列表
     */
    @NotEmpty
    @Valid
    private List<DistributeStrategy> distributeStrategyList;


    @Valid
    private List<CompensateDistributeStrategy> compensateDistributeList;


    private Long createAccountId;

    private Long updateAccountId;

    @Data
    public static class DistributeStrategy{

        /**
         * 开启条件
         */
        @NotNull
        private OpenCondition openCondition;


        /**
         * 推送类型: 推送类型，order_dispatch: 直接指派+锁单，order_lock：仅锁单
         */
        private String dispatchType;

        /**
         * 初筛策略id
         */
        @NotNull
        private Integer orderSelectStrategyId;

        /**
         * 评分策略id
         */
        private Integer orderScoringStrategyId;


        /**
         * 直接指派筛选策略id
         */
        private Integer directAppointSelectStrategyId;



        /**
         * 直接指派评分策略id
         */
        private Integer directAppointScoringStrategyId;


        /**
         * 调度规则
         */
        @NotBlank
        private String distributeRule;

        /**
         * 合作经营调度是否服务区域兜底
         * 1：是
         * 0：否
         */
        private Integer cooperationBusinessServeDivisionAtLast;

    }

    @Data
    public static class OpenCondition{
        /**
         * 或且关系
         */
        @NotEmpty
        @ValueIn("and,or")
        private String condition;

        /**
         *规则项
         */
        @NotEmpty
        @Valid
        private List<OpenConditionItem> itemList;
    }

    /**
     * 开启条件item
     */
    @Data
    public static class OpenConditionItem{

        /**
         *
         *  规则项名称,serve: 服务，appoint_type: 下单模式，order_from: 下单来源，time_liness_tag:时效标签,appoint_user:下单用户
         */
        @NotEmpty
        @ValueIn("serve,appoint_type,order_from,time_liness_tag,appoint_user")
        private String itemName;

        /**
         * 符号 in:包含  not_in:不包含
         */
        @NotEmpty
        @ValueIn("in,not_in")
        private String term;

        /**
         * 规则项值
         */
        private String itemValue;

        /**
         * [1],[1,2],[1,2,3] 数组长度表示 服务级别
         */
        private List<List<Long>> serveIdList;

    }


    @Data
    public static class CompensateDistributeStrategy{

        private Long compensateDistributeId;

        private Long orderRoutingStrategyId;

        private DistributeStrategyList distributeStrategyList;


    }


    @Data
    public static class DistributeStrategyList{

        private Long orderSelectStrategyId;

        private Long orderScoringStrategyId;

        private String distributeRule;
    }





}
