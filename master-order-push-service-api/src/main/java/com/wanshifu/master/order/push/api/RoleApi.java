package com.wanshifu.master.order.push.api;


import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.Role;
import com.wanshifu.master.order.push.domain.resp.role.RoleDetailResp;
import com.wanshifu.master.order.push.domain.resp.role.RoleListResp;
import com.wanshifu.master.order.push.domain.rqt.role.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "master-order-push-service", url = "${wanshifu.master-order-push-service.url}", path = "role", configuration = {com.wanshifu.spring.cloud.fegin.component.DefaultEncoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultDecoder.class, com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode.class})
public interface RoleApi {


    /**
     * 新增策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "add")
    Integer add(@Valid @RequestBody AddRoleRqt rqt);


    /**
     * 更新策略
     * @param rqt
     * @return
     */
    @PostMapping(value = "update")
    Integer update(@Valid @RequestBody UpdateRoleRqt rqt);



    @PostMapping(value = "list")
    SimplePageInfo<RoleListResp> list(@Valid @RequestBody GetRoleListRqt rqt);


    @PostMapping(value = "detail")
    RoleDetailResp detail(@Valid @RequestBody GetRoleDetailRqt rqt);


    @PostMapping(value = "delete")
    Integer delete(@Valid @RequestBody DeleteRoleRqt rqt);

    @PostMapping(value = "addAccount")
    Integer addAccount(@Valid @RequestBody AddAccountRqt rqt);


    @PostMapping(value="batchRoleList")
    List<Role> batchRoleList(@Valid @RequestBody BatchRoleListRqt rqt);

}
